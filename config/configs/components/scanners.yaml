type: "list"
item:
  type: "node"
  children:
    target:
      default_recommended: false
      type: "node"
      children:
        x:
          default_recommended: false
          type: "int"
          default: 0
          complexity: "user"
        "y":
          default_recommended: false
          type: "int"
          default: 0
          complexity: "user"
    enabled:
      type: "bool"
      default: true
      complexity: "user"
    focus:
      default_recommended: false
      type: "int"
      default: 100
    laser:
      type: "node"
      children:
        trust_thermistors:
          type: "bool"
          default: true
          complexity: "expert"
          hint: "Disable using thermistor to set error state, if the sensor seems faulty."
        trust_power:
          type: "bool"
          default: true
          complexity: "expert"
          hint: "Disable using power pin to set error state, if the sensor seems faulty."
        min_delta_temp:
          type: "float"
          default: 0.25
          complexity: "user"
          hint: "Delta temp deg C (mirror temp - control temp) below which we assume no laser fire"
        min_duty_cycle:
          type: "float"
          default: 0.25
          complexity: "user"
          hint: "min percentage of time laser was on, at which we can actually see temp delta"
        power:
          type: "float"
          default: -1.0 # <= 0 means unset so default to 100% power
          units: "watts"
          hint: "Measured power output of this laser used to calculate shoot time. (<= 0 means unset so default to 100% power/nominal power)"
          complexity: "user"
          default_recommended: false
    arc_detector:
      type: "node"
      children:
        enabled:
          type: "bool"
          default: false
          hint: "Enable scanner-based laser arc detection and alarming"
        count_period:
          type: "uint"
          units: "seconds"
          default: 3600
          hint: "Time interval over which arc events are integrated"
        count_threshold:
          type: "uint"
          default: 30
          hint: "Number of arc events to trigger alarm (within the detection period)"
        current_min:
          complexity: "expert"
          type: "float"
          units: "A"
          default: 0.020
          hint: "Lower current limit"
        current_max:
          complexity: "expert"
          type: "float"
          units: "A"
          default: 0.042
          hint: "Upper current limit"
        sample_delay:
          complexity: "expert"
          type: "uint"
          units: "ms"
          default: 10
          hint: "Time delay between triggering laser fire and first current sample"
        sample_rate:
          complexity: "expert"
          type: "uint"
          units: "ms"
          default: 15
          hint: "Time interval between subsequent current samples taken by the arc detector"
    fh_ambient_temp:
      type: "node"
      children:
        enabled:
          type: "bool"
          default: false
          hint: "Report the ambient temperature to FH drives"
        use_fixed_temp:
          type: "bool"
          default: false
          hint: "Write a fixed temperature to the drives rather than the actual sensed (if thermistors are bad)"
        fixed_temp:
          complexity: "expert"
          type: "float"
          units: "C"
          default: 21
          hint: "Fixed ambient temperature to report instead of measured from thermistor (if use_fixed_temp is set)"
