#pragma once

#include <c10/cuda/CUDAStream.h>
#include <glm/glm.hpp>
#include <torch/torch.h>

#include "lib/common/model/cpp/atomic_model.h"

namespace F = torch::nn::functional;

namespace cv::encoder {

using namespace lib::common;

class FeatureExtractor {
public:
  FeatureExtractor(model::AtomicModel model);
  torch::Tensor infer(torch::Tensor input);
  const ModelMetadataProto &get_internal_metadata();
  std::tuple<int, int> get_input_size();

private:
  torch::Tensor normalize(torch::Tensor image);
  model::AtomicModel model_;
  torch::Tensor means_;
  torch::Tensor stds_;
  int batch_size_;
  int gpu_id_;
};

} // namespace cv::encoder