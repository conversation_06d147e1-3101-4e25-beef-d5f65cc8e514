#include "weed_tracking/cpp/tracking/weed_tracker.h"
#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <weed_tracking_libs/plant_captcha/cpp/plant_captcha.hpp>
#include "generated/recorder/proto/recorder.pb.h"

#include <chrono>

namespace carbon::weed_tracking {
inline WeedTrackerOutput
get_latest_weeds_common(std::shared_ptr<Tracker> tracker, std::vector<uint32_t> &newly_tracked_ids,
                        int64_t latest_timestamp_ms,
                        std::map<uint32_t, std::pair<float, float>> &deepweed_flow_offsets_xy) {
  std::map<uint32_t, int> id_to_number_missed_instances;
  std::map<uint32_t, float> id_to_first_detection_vertical_offset;

  for (auto id : newly_tracked_ids) {
    if (tracker->has_id(id)) {
      auto trajectory = tracker->get_trajectory(id);
      id_to_number_missed_instances[id] = trajectory->get_number_of_undetected_initial_instances();
      id_to_first_detection_vertical_offset[id] = trajectory->get_first_location().get_y();
    }
  }

  auto output =
      WeedTrackerOutput(latest_timestamp_ms, tracker->get_trajectories(), newly_tracked_ids, deepweed_flow_offsets_xy,
                        id_to_number_missed_instances, id_to_first_detection_vertical_offset);

  newly_tracked_ids.clear();
  return output;
}

WeedTrackerOutput::WeedTrackerOutput(int64_t timestamp_ms,
                                     std::vector<std::shared_ptr<trajectory::Trajectory>> item_locations,
                                     std::vector<uint32_t> newly_tracked_ids,
                                     std::map<uint32_t, std::pair<float, float>> deepweed_flow_offsets_xy,
                                     std::map<uint32_t, int> number_missed_first_instances,
                                     std::map<uint32_t, float> vertical_offset_first_instances)
    : timestamp_ms_(timestamp_ms), item_locations_(item_locations), newly_tracked_ids_(newly_tracked_ids),
      deepweed_flow_offsets_xy_(deepweed_flow_offsets_xy),
      number_missed_first_instances_(number_missed_first_instances),
      vertical_offset_first_instances_(vertical_offset_first_instances) {}

int64_t WeedTrackerOutput::get_timestamp_ms() { return timestamp_ms_; }

std::vector<std::shared_ptr<trajectory::Trajectory>> WeedTrackerOutput::get_trajectories() { return item_locations_; }

std::vector<uint32_t> WeedTrackerOutput::get_newly_tracked_ids() { return newly_tracked_ids_; }

std::map<uint32_t, std::pair<float, float>> WeedTrackerOutput::get_deepweed_flow_offsets_xy() {
  return deepweed_flow_offsets_xy_;
}

std::map<uint32_t, int> WeedTrackerOutput::get_number_missed_first_instances() {
  return number_missed_first_instances_;
}

std::map<uint32_t, float> WeedTrackerOutput::get_vertical_offset_first_instances() {
  return vertical_offset_first_instances_;
}

WeedTracker::Config::Config(std::shared_ptr<config::ConfigTree> aimbot_tree,
                            std::shared_ptr<config::ConfigTree> plant_captcha_tree)
    : plant_dedup_radius_px(aimbot_tree->get_node("plant_dedup_radius")->get_value<float>()),
      crop_safety_radius_mm(aimbot_tree->get_node("crop_safety_radius_mm")->get_value<float>()),
      min_plant_captcha_weed_threshold(plant_captcha_tree->get_node("min_ingest_weed_threshold")->get_value<float>()),
      min_plant_captcha_crop_threshold(plant_captcha_tree->get_node("min_ingest_crop_threshold")->get_value<float>()),
      min_plant_score(aimbot_tree->get_node("min_plant_score")->get_value<float>()) {}

WeedTracker::WeedTracker(std::string pcam_id, uint32_t numeric_id,
                         std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam, int64_t prune_grace_ms,
                         uint32_t width, uint32_t height, float max_pos_mm_y, uint32_t max_number_of_weeds)
    : scoped_tree_(config::get_global_config_subscriber()->get_config_node("aimbot", "weed_tracking")),
      plant_captcha_scoped_tree_(config::get_global_config_subscriber()->get_config_node("common", "plant_captcha")),
      conf_(std::make_unique<Config>(scoped_tree_.tree(), plant_captcha_scoped_tree_.tree())), pcam_id_(pcam_id),
      numeric_id_(numeric_id), geo_cam_(geo_cam),
      tracker_(std::make_shared<Tracker>(geo_cam, numeric_id, max_number_of_weeds, height, width)),
      robot_distance_accumulator_(std::make_shared<trajectory::FlowAccumulator>()), rotary_velocity_(0),
      deepweed_timestamp_ms_(0), latest_of_timestamp_ms_(0), latest_rotary_timestamp_ms_(0),
      prune_grace_ms_(prune_grace_ms), started_getting_ticks_(false), width_(width), height_(height),
      max_pos_mm_y_(max_pos_mm_y), latency_(100), processing_time_(100), roi_accessor_() {}

std::tuple<size_t, size_t, size_t, std::map<size_t, trajectory::Trajectory::ID>, bool>
WeedTracker::add_deepweed(cv::deepweed::DeepweedOutput &deepweed_output, std::vector<::recorder::TrackedItem> &recorded_tracked_items, std::map<size_t, std::pair<float, float>> &model_undistorted_coordinates, ::recorder::CandidateShift &best_candidate_match,
    std::vector<::recorder::CandidateShift> &all_candidate_matches, std::unordered_set<size_t> &centroids_used_in_dedup) {
  auto start_time = std::chrono::system_clock::now();
  // Get centroids from deepweed predictions
  if (scoped_tree_.reload_required() || plant_captcha_scoped_tree_.reload_required()) {
    conf_ = std::make_unique<Config>(scoped_tree_.tree(), plant_captcha_scoped_tree_.tree());
  }
  std::vector<trajectory::TrackedItemCentroid> centroids;
  auto embedding_categories = std::make_shared<std::vector<std::string>>(deepweed_output.embedding_categories);
  for (uint32_t i = 0; i < deepweed_output.detections.size(); i++) {
    auto detection = deepweed_output.detections[i];
    auto centroid = trajectory::TrackedItemCentroid(
        pcam_id_, numeric_id_, detection.x, detection.y, 0, 0, 0, detection.size, detection.score, detection.weed_score,
        detection.crop_score, detection.plant_score, deepweed_output.timestamp_ms, true,
        detection.get_mask_intersections(deepweed_output.mask_channel_classes),
        detection.get_detection_classes(deepweed_output.weed_detection_classes), detection.reduced_scaled_embedding,
        detection.hit_class, deepweed_output.predict_in_distance_buffer, geo_cam_.get(), i,
        detection.embedding_category_distances, embedding_categories, detection.detection_id);
    centroid.set_available_for_snapshotting(deepweed_output.available_for_snapshotting);
    if (centroid.get_plant_score() > conf_->min_plant_score) {
      // We trust this centroid object
      if (roi_accessor_.in_bounds(numeric_id_, centroid)) {
        centroids.push_back(centroid);
        centroids_used_in_dedup.insert(centroid.index());
      }
    } else if (plant_captcha::PlantCaptcha::is_running() &&
               ((centroid.get_hit_class() == cv::runtime::proto::HitClass::WEED &&
                 centroid.get_score() >= conf_->min_plant_captcha_weed_threshold) ||
                (centroid.get_hit_class() == cv::runtime::proto::HitClass::CROP &&
                 centroid.get_score() >= conf_->min_plant_captcha_crop_threshold))) {
      if (roi_accessor_.in_bounds(numeric_id_, centroid)) {
        centroid.set_is_plant_captcha_only();
        centroids.push_back(centroid);
        centroids_used_in_dedup.insert(centroid.index());
      }
    }
  }

  deepweed_flow_offsets_xy_.clear();

  size_t number_deduplicated_plants = 0;
  size_t number_unmatched_plants_predictions = 0;
  size_t number_undeduplicated_plants = 0;
  std::map<size_t, trajectory::Trajectory::ID> model_to_track;
  bool plant_matcher_valid = false;

  if (centroids.size() > 0 && latest_rotary_timestamp_ms_ > 0) {
    ReconciliationResults reconciliation_results = reconcile_tracked_objects_with_model_predictions_deepweed_tracking(
        tracker_.get(), geo_cam_, centroids, deepweed_output.timestamp_ms, latest_rotary_timestamp_ms_,
        robot_distance_accumulator_, conf_->plant_dedup_radius_px, recorded_tracked_items, model_undistorted_coordinates, best_candidate_match, all_candidate_matches);

    auto new_ids = reconciliation_results.new_ids;
    auto offsets_xy = reconciliation_results.deepweed_flow_offsets_xy;

    number_deduplicated_plants = reconciliation_results.number_deduplicated_plants;
    number_unmatched_plants_predictions = reconciliation_results.number_unmatched_plants_predictions;
    number_undeduplicated_plants = reconciliation_results.number_undeduplicated_plants;

    model_to_track = reconciliation_results.model_to_track;

    plant_matcher_valid = reconciliation_results.plant_matcher_valid;

    robot_distance_accumulator_->prune_up_to_timestamp_ms(deepweed_output.timestamp_ms - prune_grace_ms_);
    tracker_->prune_deepweed_timestamps_up_to_timestamp_ms(deepweed_output.timestamp_ms - prune_grace_ms_);
    for (auto new_id : new_ids) {
      newly_tracked_ids_.push_back(new_id);
    }
    deepweed_flow_offsets_xy_ = offsets_xy;

    tracker_->push_settled_trajectories_height_to_height_estimator();
  }
  untrack();
  deepweed_timestamp_ms_ = deepweed_output.timestamp_ms;
  latency_.add(std::chrono::duration_cast<std::chrono::milliseconds>(start_time.time_since_epoch()).count() -
               deepweed_timestamp_ms_);
  processing_time_.add(
      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - start_time).count());

  return std::make_tuple(number_deduplicated_plants, number_unmatched_plants_predictions, number_undeduplicated_plants,
                         model_to_track, plant_matcher_valid);
}

int64_t WeedTracker::add_rotary_ticks(int64_t timestamp_ms, float mm_travelled) {
  int64_t current_re_timestamp_ms = timestamp_ms;
  robot_distance_accumulator_->update(0, mm_travelled, current_re_timestamp_ms);

  update_tracked_objects_by_mm(tracker_.get(), geo_cam_, 0, mm_travelled, max_pos_mm_y_, current_re_timestamp_ms,
                               std::pair<uint32_t, uint32_t>(height_, width_),
                               std::bind(&WeedTracker::mark_untrack, this, std::placeholders::_1));

  latest_rotary_timestamp_ms_ = current_re_timestamp_ms;
  started_getting_ticks_ = true;

  return latest_rotary_timestamp_ms_;
}

std::vector<double> WeedTracker::get_averages_for_all_columns(std::optional<float> size_mm) {
  return tracker_->get_averages_for_all_columns(size_mm);
}

WeedTrackerOutput WeedTracker::get_latest_weeds() {
  return get_latest_weeds_common(tracker_, newly_tracked_ids_, latest_rotary_timestamp_ms_, deepweed_flow_offsets_xy_);
}

std::shared_ptr<trajectory::FlowAccumulator> WeedTracker::get_robot_distance_accumulator() {
  return robot_distance_accumulator_;
}

std::shared_ptr<Tracker> WeedTracker::get_tracker() { return this->tracker_; }

float WeedTracker::get_pix_per_ticks() { return current_pix_per_ticks_; }
void WeedTracker::mark_untrack(uint32_t id) {
  if (!to_be_untracked_.push(id)) {
    spdlog::warn("Couldn't push {} to to_be_untracked_", id);
  }
}
void WeedTracker::untrack() {
  uint32_t weed_id;
  while (to_be_untracked_.pop(weed_id)) {
    tracker_->untrack(weed_id);
  }
}

} // namespace carbon::weed_tracking