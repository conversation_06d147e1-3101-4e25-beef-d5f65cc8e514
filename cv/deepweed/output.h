#pragma once

#include "cv/runtime/proto/cv_runtime.pb.h"
#include <cstdint>
#include <glm/glm.hpp>
#include <torch/torch.h>

namespace cv::deepweed {

constexpr int kDeepweedMaxMaskWidth = 250;
constexpr int kDeepweedMaxMaskHeight = 250;
constexpr int kDeepweedMaxMaskChannels = 3;

class DeepweedDetection {
public:
  DeepweedDetection() {}
  DeepweedDetection(float x_, float y_, float size_, float score_, float weed_score_, float crop_score_,
                    float plant_score_, runtime::proto::HitClass hit_class_, int32_t hit_y_, int32_t hit_x_,
                    int32_t hit_ds_, uint32_t detection_id_)
      : x(x_), y(y_), size(size_), score(score_), weed_score(weed_score_), crop_score(crop_score_),
        plant_score(plant_score_), detection_id(detection_id_), hit_class(hit_class_), hit_y(hit_y_), hit_x(hit_x_),
        hit_ds(hit_ds_) {}

  std::vector<std::pair<std::string, float>>
  get_detection_classes(const std::vector<std::string> &weed_detection_classes) const {
    std::vector<std::pair<std::string, float>> detection_class_pairs;
    fill_detection_classes(detection_class_pairs, weed_detection_classes);
    return detection_class_pairs;
  }

  void fill_detection_classes(std::vector<std::pair<std::string, float>> &detection_class_pairs,
                              const std::vector<std::string> &weed_detection_classes) const {
    for (size_t i = 0; i < weed_detection_classes.size(); i++) {
      detection_class_pairs.push_back({weed_detection_classes[i], weed_detection_class_scores[i]});
    }
  }

  std::vector<std::string> get_mask_intersections(const std::vector<std::string> &mask_channel_classes) const {
    std::vector<std::string> mask_intersections_string;
    for (size_t i = 0; i < mask_intersections.size(); i++) {
      mask_intersections_string.push_back(mask_channel_classes[mask_intersections[i]]);
    }
    return mask_intersections_string;
  }

  void fill_weed_detection_classes(std::vector<std::pair<std::string, float>> &weed_detection_class_pairs,
                                   const std::vector<std::string> &weed_detection_classes) const {
    for (size_t i = 0; i < weed_detection_classes.size(); i++) {
      weed_detection_class_pairs.push_back({weed_detection_classes[i], weed_detection_class_scores[i]});
    }
  }

  float x, y, size, score, weed_score, crop_score, plant_score;
  uint32_t detection_id;
  runtime::proto::HitClass hit_class;
  int32_t hit_y, hit_x, hit_ds, tile_index;

  std::vector<float> weed_detection_class_scores;
  std::vector<float> embedding_category_distances;

  std::vector<float> reduced_scaled_embedding;
  torch::Tensor embedding;

  std::vector<int> mask_intersections;
};

struct DeepweedOutput {
  std::vector<DeepweedDetection> detections;
  std::vector<std::string> weed_detection_classes;
  std::vector<std::string> embedding_categories;
  uint32_t mask_width, mask_height, mask_channels;
  std::array<uint8_t, kDeepweedMaxMaskWidth * kDeepweedMaxMaskHeight * kDeepweedMaxMaskChannels> mask;
  std::vector<std::string> mask_channel_classes;
  int64_t timestamp_ms;
  bool predict_in_distance_buffer;
  bool available_for_snapshotting = false;
};

struct DeepweedRawOutput {
public:
  DeepweedRawOutput() {}
  DeepweedRawOutput(std::vector<torch::Tensor> output) {
    auto output_ptr = output.begin();
    int strides_len = 0;
    if (output.size() == 6) {
      version = 0;
      mask = output[0];
      strides_len = output[1][0].item().to<int>();
      output_ptr += 2;
    } else {
      version = output[0][0].item().to<int>();
      mask = output[1];
      strides_len = output[2][0].item().to<int>();
      output_ptr += 3;
    }
    point_hits = std::vector<torch::Tensor>(output_ptr, output_ptr + strides_len);
    output_ptr += strides_len;
    if (version >= 1) {
      point_categories = std::vector<torch::Tensor>(output_ptr, output_ptr + strides_len);
      output_ptr += strides_len;
    }
    point_offsets = std::vector<torch::Tensor>(output_ptr, output_ptr + strides_len);
    output_ptr += strides_len;
    point_sizes = std::vector<torch::Tensor>(output_ptr, output_ptr + strides_len);
    output_ptr += strides_len;
    output_ptr += 1; // skip sigmoid

    if (output_ptr < output.end()) {
      point_reduced_scaled_embeddings = std::vector<torch::Tensor>(output_ptr, output_ptr + strides_len);
      output_ptr += strides_len;
    }

    if (output_ptr < output.end()) {
      point_embeddings = std::vector<torch::Tensor>(output_ptr, output_ptr + strides_len);
      output_ptr += strides_len;
    }
  }

  c10::DeviceIndex device() { return mask.device().index(); }

  int batch_size() {
    // We ensure that dummy mask has correct batch size
    return (int)mask.size(0);
  }

  torch::Tensor mask;
  int version;
  std::vector<torch::Tensor> point_hits;
  std::vector<torch::Tensor> point_categories;
  std::vector<torch::Tensor> point_offsets;
  std::vector<torch::Tensor> point_sizes;
  std::vector<torch::Tensor> point_embeddings;
  std::vector<torch::Tensor> point_reduced_scaled_embeddings;
};

} // namespace cv::deepweed