add_subdirectory(runtime/cpp)
add_subdirectory(connected_components)
add_subdirectory(comparison)
add_subdirectory(deck)
add_subdirectory(deepweed)
add_subdirectory(furrows)
add_subdirectory(encoder)
add_subdirectory(p2p)
add_subdirectory(embeddings)

add_custom_target(make_cv_connected_components ALL make -C ${CMAKE_CURRENT_LIST_DIR}/connected_components all) 

add_custom_target(clean_cv_connected_components make -C ${CMAKE_CURRENT_LIST_DIR}/connected_components clean) 
add_dependencies(make_clean clean_cv_connected_components)