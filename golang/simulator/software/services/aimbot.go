package services

import (
	"context"
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/lib/aimbot_client"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"google.golang.org/grpc"
)

type AimbotService struct {
	SimulatedServiceImpl
	aimbot.UnimplementedAimbotServiceServer

	state         *aimbot.AimbotState
	trackingState *aimbot.TrackingState
	scannerStates []*aimbot.ScannerState

	proxyClient *aimbot_client.AimbotClient
}

func NewAimbotService(id uint32, grpcServer *grpc.Server) *AimbotService {
	a := &AimbotService{
		SimulatedServiceImpl: SimulatedServiceImpl{id},
		state: &aimbot.AimbotState{
			Algorithm: "rotary_and_p2p",
			Armed:     true,
			Running:   true,
			TargetingState: &aimbot.TargetingState{
				WeedingEnabled:  true,
				ThinningEnabled: false,
			},
		},
		trackingState: &aimbot.TrackingState{
			States: make([]*aimbot.TrackerState, 0),
			SchedulerState: &aimbot.SchedulerState{
				OverCapacity: false,
			},
		},
		scannerStates: make([]*aimbot.ScannerState, 0),
		proxyClient:   aimbot_client.NewAimbotClient(fmt.Sprintf("%v:%v", Row1Addr, AimbotPort)),
	}

	numLasers := 10
	if environment.IsReaper() {
		numLasers = 2
	}
	for i := 1; i <= numLasers; i++ {
		a.trackingState.States = append(a.trackingState.States, &aimbot.TrackerState{
			Id: uint32(i),
		})
		a.scannerStates = append(a.scannerStates, &aimbot.ScannerState{
			ScannerDescriptor: &aimbot.ScannerDescriptor{
				Id: uint32(i),
			},
			LaserState: &aimbot.LaserState{
				Enabled: true,
				Power:   true,
			},
			CrosshairState: &aimbot.CrosshairState{},
		})
	}
	aimbot.RegisterAimbotServiceServer(grpcServer, a)
	return a
}

func (a *AimbotService) GetBooted(context.Context, *aimbot.Empty) (*aimbot.BootedReply, error) {
	return &aimbot.BootedReply{
		Booted: true,
	}, nil
}
func (a *AimbotService) SetTargetingState(cxt context.Context, state *aimbot.TargetingState) (*aimbot.Empty, error) {
	a.state.Running = state.WeedingEnabled || state.ThinningEnabled
	a.state.TargetingState = state
	return &aimbot.Empty{}, nil
}

func (a *AimbotService) ArmLasers(context.Context, *aimbot.Empty) (*aimbot.Empty, error) {
	a.state.Armed = true
	return &aimbot.Empty{}, nil
}
func (a *AimbotService) DisarmLasers(context.Context, *aimbot.Empty) (*aimbot.Empty, error) {
	a.state.Armed = false
	return &aimbot.Empty{}, nil
}
func (a *AimbotService) GetAimbotState(context.Context, *aimbot.Empty) (*aimbot.AimbotState, error) {
	return a.state, nil
}
func (a *AimbotService) LensGetAll(context.Context, *aimbot.Empty) (*aimbot.LensGetAllReply, error) {
	reply := &aimbot.LensGetAllReply{
		LensStatus: make([]*aimbot.LensGetReply, 0),
	}
	for i := 1; i <= 10; i++ {
		reply.LensStatus = append(reply.LensStatus, &aimbot.LensGetReply{
			Value:                  10,
			MinValue:               0,
			MaxValue:               100,
			ManualAutofocusPercent: 50,
			ManualAutofocusing:     true,
			ScannerId:              uint32(i),
		})
	}
	return reply, nil
}
func (a *AimbotService) GetTrackingState(context.Context, *aimbot.Empty) (*aimbot.TrackingState, error) {
	return a.trackingState, nil
}

func (a *AimbotService) GetDimensions(context.Context, *aimbot.Empty) (*aimbot.GetDimensionsResponse, error) {
	return a.proxyClient.GetDimensions()
}

func (a *AimbotService) GetScannerStatus(context.Context, *aimbot.Empty) (*aimbot.ScannerStatusReply, error) {
	return &aimbot.ScannerStatusReply{
		States: a.scannerStates,
	}, nil
}

func (a *AimbotService) GetTargetVelocity(context.Context, *aimbot.TargetVelocityRequest) (*aimbot.TargetVelocityReply, error) {
	return a.proxyClient.GetTargetVelocity()
}

func (a *AimbotService) LaserEnable(ctx context.Context, req *aimbot.LaserEnableRequest) (*aimbot.LaserEnableReply, error) {
	if req.ScannerId == 0 {
		for _, s := range a.scannerStates {
			s.LaserState.Enabled = req.Enabled
		}
	} else {
		a.scannerStates[req.ScannerId-1].LaserState.Enabled = req.Enabled
	}
	return &aimbot.LaserEnableReply{Status: true}, nil
}

func (a *AimbotService) ReloadAlmanacConf(ctx context.Context, req *aimbot.ReloadAlmanacConfRequest) (*aimbot.Empty, error) {
	return &aimbot.Empty{}, nil
}
func (a *AimbotService) ReloadDiscriminatorConf(ctx context.Context, req *aimbot.ReloadDiscriminatorConfRequest) (*aimbot.Empty, error) {
	return &aimbot.Empty{}, nil
}
func (a *AimbotService) ReloadModelinatorConf(ctx context.Context, req *aimbot.ReloadModelinatorConfRequest) (*aimbot.Empty, error) {
	return &aimbot.Empty{}, nil
}
func (a *AimbotService) ReloadTVEProfile(ctx context.Context, req *aimbot.ReloadTVEProfileRequest) (*aimbot.Empty, error) {
	return &aimbot.Empty{}, nil
}

func (a *AimbotService) ReloadThinningConf(ctx context.Context, req *aimbot.ReloadThinningConfRequest) (*aimbot.Empty, error) {
	return &aimbot.Empty{}, nil
}
