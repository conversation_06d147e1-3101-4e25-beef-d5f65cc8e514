// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: recorder/proto/recorder.proto

package recorder

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeepweedDetection_HitClass int32

const (
	DeepweedDetection_WEED DeepweedDetection_HitClass = 0
	DeepweedDetection_CROP DeepweedDetection_HitClass = 1
)

// Enum value maps for DeepweedDetection_HitClass.
var (
	DeepweedDetection_HitClass_name = map[int32]string{
		0: "WEED",
		1: "CROP",
	}
	DeepweedDetection_HitClass_value = map[string]int32{
		"WEED": 0,
		"CROP": 1,
	}
)

func (x DeepweedDetection_HitClass) Enum() *DeepweedDetection_HitClass {
	p := new(DeepweedDetection_HitClass)
	*p = x
	return p
}

func (x DeepweedDetection_HitClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeepweedDetection_HitClass) Descriptor() protoreflect.EnumDescriptor {
	return file_recorder_proto_recorder_proto_enumTypes[0].Descriptor()
}

func (DeepweedDetection_HitClass) Type() protoreflect.EnumType {
	return &file_recorder_proto_recorder_proto_enumTypes[0]
}

func (x DeepweedDetection_HitClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeepweedDetection_HitClass.Descriptor instead.
func (DeepweedDetection_HitClass) EnumDescriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{1, 0}
}

type DetectionClass struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Class string  `protobuf:"bytes,1,opt,name=class,proto3" json:"class,omitempty"`
	Score float32 `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *DetectionClass) Reset() {
	*x = DetectionClass{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectionClass) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionClass) ProtoMessage() {}

func (x *DetectionClass) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionClass.ProtoReflect.Descriptor instead.
func (*DetectionClass) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{0}
}

func (x *DetectionClass) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

func (x *DetectionClass) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type DeepweedDetection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X                          float32                    `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y                          float32                    `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	Size                       float32                    `protobuf:"fixed32,3,opt,name=size,proto3" json:"size,omitempty"`
	Score                      float32                    `protobuf:"fixed32,4,opt,name=score,proto3" json:"score,omitempty"`
	HitClass                   DeepweedDetection_HitClass `protobuf:"varint,5,opt,name=hit_class,json=hitClass,proto3,enum=recorder.DeepweedDetection_HitClass" json:"hit_class,omitempty"`
	DetectionClasses           []*DetectionClass          `protobuf:"bytes,6,rep,name=detection_classes,json=detectionClasses,proto3" json:"detection_classes,omitempty"`
	MaskIntersections          [][]byte                   `protobuf:"bytes,7,rep,name=mask_intersections,json=maskIntersections,proto3" json:"mask_intersections,omitempty"`
	TrajectoryId               uint32                     `protobuf:"varint,8,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
	WeedScore                  float32                    `protobuf:"fixed32,9,opt,name=weed_score,json=weedScore,proto3" json:"weed_score,omitempty"`
	CropScore                  float32                    `protobuf:"fixed32,10,opt,name=crop_score,json=cropScore,proto3" json:"crop_score,omitempty"`
	PlantScore                 float32                    `protobuf:"fixed32,11,opt,name=plant_score,json=plantScore,proto3" json:"plant_score,omitempty"`
	EmbeddingCategoryDistances []float32                  `protobuf:"fixed32,12,rep,packed,name=embedding_category_distances,json=embeddingCategoryDistances,proto3" json:"embedding_category_distances,omitempty"`
	XUndistorted               float32                    `protobuf:"fixed32,13,opt,name=x_undistorted,json=xUndistorted,proto3" json:"x_undistorted,omitempty"`
	YUndistorted               float32                    `protobuf:"fixed32,14,opt,name=y_undistorted,json=yUndistorted,proto3" json:"y_undistorted,omitempty"`
}

func (x *DeepweedDetection) Reset() {
	*x = DeepweedDetection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedDetection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedDetection) ProtoMessage() {}

func (x *DeepweedDetection) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedDetection.ProtoReflect.Descriptor instead.
func (*DeepweedDetection) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{1}
}

func (x *DeepweedDetection) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *DeepweedDetection) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *DeepweedDetection) GetSize() float32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DeepweedDetection) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DeepweedDetection) GetHitClass() DeepweedDetection_HitClass {
	if x != nil {
		return x.HitClass
	}
	return DeepweedDetection_WEED
}

func (x *DeepweedDetection) GetDetectionClasses() []*DetectionClass {
	if x != nil {
		return x.DetectionClasses
	}
	return nil
}

func (x *DeepweedDetection) GetMaskIntersections() [][]byte {
	if x != nil {
		return x.MaskIntersections
	}
	return nil
}

func (x *DeepweedDetection) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

func (x *DeepweedDetection) GetWeedScore() float32 {
	if x != nil {
		return x.WeedScore
	}
	return 0
}

func (x *DeepweedDetection) GetCropScore() float32 {
	if x != nil {
		return x.CropScore
	}
	return 0
}

func (x *DeepweedDetection) GetPlantScore() float32 {
	if x != nil {
		return x.PlantScore
	}
	return 0
}

func (x *DeepweedDetection) GetEmbeddingCategoryDistances() []float32 {
	if x != nil {
		return x.EmbeddingCategoryDistances
	}
	return nil
}

func (x *DeepweedDetection) GetXUndistorted() float32 {
	if x != nil {
		return x.XUndistorted
	}
	return 0
}

func (x *DeepweedDetection) GetYUndistorted() float32 {
	if x != nil {
		return x.YUndistorted
	}
	return 0
}

type TrackedItemCoordinates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XMm float32 `protobuf:"fixed32,1,opt,name=x_mm,json=xMm,proto3" json:"x_mm,omitempty"`
	YMm float32 `protobuf:"fixed32,2,opt,name=y_mm,json=yMm,proto3" json:"y_mm,omitempty"`
	ZMm float32 `protobuf:"fixed32,3,opt,name=z_mm,json=zMm,proto3" json:"z_mm,omitempty"`
	XPx float32 `protobuf:"fixed32,4,opt,name=x_px,json=xPx,proto3" json:"x_px,omitempty"`
	YPx float32 `protobuf:"fixed32,5,opt,name=y_px,json=yPx,proto3" json:"y_px,omitempty"`
}

func (x *TrackedItemCoordinates) Reset() {
	*x = TrackedItemCoordinates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackedItemCoordinates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackedItemCoordinates) ProtoMessage() {}

func (x *TrackedItemCoordinates) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackedItemCoordinates.ProtoReflect.Descriptor instead.
func (*TrackedItemCoordinates) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{2}
}

func (x *TrackedItemCoordinates) GetXMm() float32 {
	if x != nil {
		return x.XMm
	}
	return 0
}

func (x *TrackedItemCoordinates) GetYMm() float32 {
	if x != nil {
		return x.YMm
	}
	return 0
}

func (x *TrackedItemCoordinates) GetZMm() float32 {
	if x != nil {
		return x.ZMm
	}
	return 0
}

func (x *TrackedItemCoordinates) GetXPx() float32 {
	if x != nil {
		return x.XPx
	}
	return 0
}

func (x *TrackedItemCoordinates) GetYPx() float32 {
	if x != nil {
		return x.YPx
	}
	return 0
}

type EmbeddingCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category string  `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Distance float32 `protobuf:"fixed32,2,opt,name=distance,proto3" json:"distance,omitempty"`
}

func (x *EmbeddingCategory) Reset() {
	*x = EmbeddingCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmbeddingCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmbeddingCategory) ProtoMessage() {}

func (x *EmbeddingCategory) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmbeddingCategory.ProtoReflect.Descriptor instead.
func (*EmbeddingCategory) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{3}
}

func (x *EmbeddingCategory) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *EmbeddingCategory) GetDistance() float32 {
	if x != nil {
		return x.Distance
	}
	return 0
}

type TrackedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrajectoryId      uint32                  `protobuf:"varint,1,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
	Deduplicated      bool                    `protobuf:"varint,2,opt,name=deduplicated,proto3" json:"deduplicated,omitempty"`
	CoordinatesBefore *TrackedItemCoordinates `protobuf:"bytes,3,opt,name=coordinates_before,json=coordinatesBefore,proto3" json:"coordinates_before,omitempty"`
	CoordinatesAfter  *TrackedItemCoordinates `protobuf:"bytes,4,opt,name=coordinates_after,json=coordinatesAfter,proto3" json:"coordinates_after,omitempty"`
	InCamera          bool                    `protobuf:"varint,5,opt,name=in_camera,json=inCamera,proto3" json:"in_camera,omitempty"`
}

func (x *TrackedItem) Reset() {
	*x = TrackedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackedItem) ProtoMessage() {}

func (x *TrackedItem) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackedItem.ProtoReflect.Descriptor instead.
func (*TrackedItem) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{4}
}

func (x *TrackedItem) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

func (x *TrackedItem) GetDeduplicated() bool {
	if x != nil {
		return x.Deduplicated
	}
	return false
}

func (x *TrackedItem) GetCoordinatesBefore() *TrackedItemCoordinates {
	if x != nil {
		return x.CoordinatesBefore
	}
	return nil
}

func (x *TrackedItem) GetCoordinatesAfter() *TrackedItemCoordinates {
	if x != nil {
		return x.CoordinatesAfter
	}
	return nil
}

func (x *TrackedItem) GetInCamera() bool {
	if x != nil {
		return x.InCamera
	}
	return false
}

type CandidateShift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XShift       float32 `protobuf:"fixed32,1,opt,name=x_shift,json=xShift,proto3" json:"x_shift,omitempty"`
	YShift       float32 `protobuf:"fixed32,2,opt,name=y_shift,json=yShift,proto3" json:"y_shift,omitempty"`
	CentroidId   uint32  `protobuf:"varint,3,opt,name=centroid_id,json=centroidId,proto3" json:"centroid_id,omitempty"`
	TrajectoryId uint32  `protobuf:"varint,4,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
}

func (x *CandidateShift) Reset() {
	*x = CandidateShift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CandidateShift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CandidateShift) ProtoMessage() {}

func (x *CandidateShift) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CandidateShift.ProtoReflect.Descriptor instead.
func (*CandidateShift) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{5}
}

func (x *CandidateShift) GetXShift() float32 {
	if x != nil {
		return x.XShift
	}
	return 0
}

func (x *CandidateShift) GetYShift() float32 {
	if x != nil {
		return x.YShift
	}
	return 0
}

func (x *CandidateShift) GetCentroidId() uint32 {
	if x != nil {
		return x.CentroidId
	}
	return 0
}

func (x *CandidateShift) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

type DeepweedPredictionFrame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs         int64                `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	Detections          []*DeepweedDetection `protobuf:"bytes,2,rep,name=detections,proto3" json:"detections,omitempty"`
	EmbeddingCategories []string             `protobuf:"bytes,3,rep,name=embedding_categories,json=embeddingCategories,proto3" json:"embedding_categories,omitempty"`
	TrackedItems        []*TrackedItem       `protobuf:"bytes,4,rep,name=tracked_items,json=trackedItems,proto3" json:"tracked_items,omitempty"`
	BestShift           *CandidateShift      `protobuf:"bytes,5,opt,name=best_shift,json=bestShift,proto3" json:"best_shift,omitempty"`
	CandidateShifts     []*CandidateShift    `protobuf:"bytes,6,rep,name=candidate_shifts,json=candidateShifts,proto3" json:"candidate_shifts,omitempty"`
	PlantMatcherValid   bool                 `protobuf:"varint,7,opt,name=plant_matcher_valid,json=plantMatcherValid,proto3" json:"plant_matcher_valid,omitempty"`
}

func (x *DeepweedPredictionFrame) Reset() {
	*x = DeepweedPredictionFrame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedPredictionFrame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedPredictionFrame) ProtoMessage() {}

func (x *DeepweedPredictionFrame) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedPredictionFrame.ProtoReflect.Descriptor instead.
func (*DeepweedPredictionFrame) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{6}
}

func (x *DeepweedPredictionFrame) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *DeepweedPredictionFrame) GetDetections() []*DeepweedDetection {
	if x != nil {
		return x.Detections
	}
	return nil
}

func (x *DeepweedPredictionFrame) GetEmbeddingCategories() []string {
	if x != nil {
		return x.EmbeddingCategories
	}
	return nil
}

func (x *DeepweedPredictionFrame) GetTrackedItems() []*TrackedItem {
	if x != nil {
		return x.TrackedItems
	}
	return nil
}

func (x *DeepweedPredictionFrame) GetBestShift() *CandidateShift {
	if x != nil {
		return x.BestShift
	}
	return nil
}

func (x *DeepweedPredictionFrame) GetCandidateShifts() []*CandidateShift {
	if x != nil {
		return x.CandidateShifts
	}
	return nil
}

func (x *DeepweedPredictionFrame) GetPlantMatcherValid() bool {
	if x != nil {
		return x.PlantMatcherValid
	}
	return false
}

type DeepweedPredictionRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordTimestampMs uint64                   `protobuf:"varint,1,opt,name=record_timestamp_ms,json=recordTimestampMs,proto3" json:"record_timestamp_ms,omitempty"`
	Frame             *DeepweedPredictionFrame `protobuf:"bytes,2,opt,name=frame,proto3" json:"frame,omitempty"`
}

func (x *DeepweedPredictionRecord) Reset() {
	*x = DeepweedPredictionRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedPredictionRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedPredictionRecord) ProtoMessage() {}

func (x *DeepweedPredictionRecord) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedPredictionRecord.ProtoReflect.Descriptor instead.
func (*DeepweedPredictionRecord) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{7}
}

func (x *DeepweedPredictionRecord) GetRecordTimestampMs() uint64 {
	if x != nil {
		return x.RecordTimestampMs
	}
	return 0
}

func (x *DeepweedPredictionRecord) GetFrame() *DeepweedPredictionFrame {
	if x != nil {
		return x.Frame
	}
	return nil
}

type LaneHeightSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedHeight []float64 `protobuf:"fixed64,1,rep,packed,name=weed_height,json=weedHeight,proto3" json:"weed_height,omitempty"`
	CropHeight []float64 `protobuf:"fixed64,2,rep,packed,name=crop_height,json=cropHeight,proto3" json:"crop_height,omitempty"`
}

func (x *LaneHeightSnapshot) Reset() {
	*x = LaneHeightSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaneHeightSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaneHeightSnapshot) ProtoMessage() {}

func (x *LaneHeightSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaneHeightSnapshot.ProtoReflect.Descriptor instead.
func (*LaneHeightSnapshot) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{8}
}

func (x *LaneHeightSnapshot) GetWeedHeight() []float64 {
	if x != nil {
		return x.WeedHeight
	}
	return nil
}

func (x *LaneHeightSnapshot) GetCropHeight() []float64 {
	if x != nil {
		return x.CropHeight
	}
	return nil
}

type LaneHeightRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordTimestampMs uint64              `protobuf:"varint,1,opt,name=record_timestamp_ms,json=recordTimestampMs,proto3" json:"record_timestamp_ms,omitempty"`
	Snapshot          *LaneHeightSnapshot `protobuf:"bytes,2,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
}

func (x *LaneHeightRecord) Reset() {
	*x = LaneHeightRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaneHeightRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaneHeightRecord) ProtoMessage() {}

func (x *LaneHeightRecord) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaneHeightRecord.ProtoReflect.Descriptor instead.
func (*LaneHeightRecord) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{9}
}

func (x *LaneHeightRecord) GetRecordTimestampMs() uint64 {
	if x != nil {
		return x.RecordTimestampMs
	}
	return 0
}

func (x *LaneHeightRecord) GetSnapshot() *LaneHeightSnapshot {
	if x != nil {
		return x.Snapshot
	}
	return nil
}

type RotaryTicksSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampUs uint64 `protobuf:"varint,1,opt,name=timestamp_us,json=timestampUs,proto3" json:"timestamp_us,omitempty"`
	Fl          int32  `protobuf:"varint,2,opt,name=fl,proto3" json:"fl,omitempty"`
	Fr          int32  `protobuf:"varint,3,opt,name=fr,proto3" json:"fr,omitempty"`
	Bl          int32  `protobuf:"varint,4,opt,name=bl,proto3" json:"bl,omitempty"`
	Br          int32  `protobuf:"varint,5,opt,name=br,proto3" json:"br,omitempty"`
	FlEnabled   bool   `protobuf:"varint,6,opt,name=fl_enabled,json=flEnabled,proto3" json:"fl_enabled,omitempty"`
	FrEnabled   bool   `protobuf:"varint,7,opt,name=fr_enabled,json=frEnabled,proto3" json:"fr_enabled,omitempty"`
	BlEnabled   bool   `protobuf:"varint,8,opt,name=bl_enabled,json=blEnabled,proto3" json:"bl_enabled,omitempty"`
	BrEnabled   bool   `protobuf:"varint,9,opt,name=br_enabled,json=brEnabled,proto3" json:"br_enabled,omitempty"`
}

func (x *RotaryTicksSnapshot) Reset() {
	*x = RotaryTicksSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryTicksSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryTicksSnapshot) ProtoMessage() {}

func (x *RotaryTicksSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryTicksSnapshot.ProtoReflect.Descriptor instead.
func (*RotaryTicksSnapshot) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{10}
}

func (x *RotaryTicksSnapshot) GetTimestampUs() uint64 {
	if x != nil {
		return x.TimestampUs
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetFl() int32 {
	if x != nil {
		return x.Fl
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetFr() int32 {
	if x != nil {
		return x.Fr
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetBl() int32 {
	if x != nil {
		return x.Bl
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetBr() int32 {
	if x != nil {
		return x.Br
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetFlEnabled() bool {
	if x != nil {
		return x.FlEnabled
	}
	return false
}

func (x *RotaryTicksSnapshot) GetFrEnabled() bool {
	if x != nil {
		return x.FrEnabled
	}
	return false
}

func (x *RotaryTicksSnapshot) GetBlEnabled() bool {
	if x != nil {
		return x.BlEnabled
	}
	return false
}

func (x *RotaryTicksSnapshot) GetBrEnabled() bool {
	if x != nil {
		return x.BrEnabled
	}
	return false
}

type RotaryTicksRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordTimestampMs uint64               `protobuf:"varint,1,opt,name=record_timestamp_ms,json=recordTimestampMs,proto3" json:"record_timestamp_ms,omitempty"`
	Snapshot          *RotaryTicksSnapshot `protobuf:"bytes,2,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
}

func (x *RotaryTicksRecord) Reset() {
	*x = RotaryTicksRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryTicksRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryTicksRecord) ProtoMessage() {}

func (x *RotaryTicksRecord) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryTicksRecord.ProtoReflect.Descriptor instead.
func (*RotaryTicksRecord) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{11}
}

func (x *RotaryTicksRecord) GetRecordTimestampMs() uint64 {
	if x != nil {
		return x.RecordTimestampMs
	}
	return 0
}

func (x *RotaryTicksRecord) GetSnapshot() *RotaryTicksSnapshot {
	if x != nil {
		return x.Snapshot
	}
	return nil
}

var File_recorder_proto_recorder_proto protoreflect.FileDescriptor

var file_recorder_proto_recorder_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x3c, 0x0a, 0x0e, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xc2, 0x04, 0x0a, 0x11, 0x44, 0x65, 0x65, 0x70,
	0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a,
	0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x48, 0x69, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x08, 0x68, 0x69,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x45, 0x0a, 0x11, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x10, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x2d, 0x0a,
	0x12, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x11, 0x6d, 0x61, 0x73, 0x6b, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x77, 0x65, 0x65, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x63, 0x72, 0x6f, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x40, 0x0a, 0x1c, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x18, 0x0c, 0x20, 0x03, 0x28, 0x02, 0x52, 0x1a, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x78, 0x5f, 0x75, 0x6e, 0x64, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x74, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x78, 0x55, 0x6e, 0x64, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x79, 0x5f, 0x75, 0x6e, 0x64,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c,
	0x79, 0x55, 0x6e, 0x64, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x22, 0x1e, 0x0a, 0x08,
	0x48, 0x69, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x45, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x52, 0x4f, 0x50, 0x10, 0x01, 0x22, 0x77, 0x0a, 0x16,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x12, 0x11, 0x0a, 0x04, 0x78, 0x5f, 0x6d, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x78, 0x4d, 0x6d, 0x12, 0x11, 0x0a, 0x04, 0x79, 0x5f, 0x6d,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x79, 0x4d, 0x6d, 0x12, 0x11, 0x0a, 0x04,
	0x7a, 0x5f, 0x6d, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x7a, 0x4d, 0x6d, 0x12,
	0x11, 0x0a, 0x04, 0x78, 0x5f, 0x70, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x78,
	0x50, 0x78, 0x12, 0x11, 0x0a, 0x04, 0x79, 0x5f, 0x70, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x03, 0x79, 0x50, 0x78, 0x22, 0x4b, 0x0a, 0x11, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x64, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x64,
	0x65, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x4f, 0x0a, 0x12, 0x63,
	0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x52, 0x11, 0x63, 0x6f, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x4d, 0x0a, 0x11,
	0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x52, 0x10, 0x63, 0x6f, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x6e, 0x5f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x6e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x22, 0x88, 0x01, 0x0a, 0x0e, 0x43, 0x61, 0x6e,
	0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x66, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x78,
	0x5f, 0x73, 0x68, 0x69, 0x66, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x78, 0x53,
	0x68, 0x69, 0x66, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x79, 0x5f, 0x73, 0x68, 0x69, 0x66, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x79, 0x53, 0x68, 0x69, 0x66, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x22, 0x96, 0x03, 0x0a, 0x17, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x4d, 0x73, 0x12, 0x3b, 0x0a, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x31, 0x0a, 0x14, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x65,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x12, 0x3a, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x37,
	0x0a, 0x0a, 0x62, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x68, 0x69, 0x66, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x61,
	0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x66, 0x74, 0x52, 0x09, 0x62, 0x65,
	0x73, 0x74, 0x53, 0x68, 0x69, 0x66, 0x74, 0x12, 0x43, 0x0a, 0x10, 0x63, 0x61, 0x6e, 0x64, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x68, 0x69, 0x66, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x6e,
	0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x66, 0x74, 0x52, 0x0f, 0x63, 0x61, 0x6e,
	0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x66, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x22, 0x83, 0x01, 0x0a,
	0x18, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x05, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x22, 0x56, 0x0a, 0x12, 0x4c, 0x61, 0x6e, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0a, 0x77,
	0x65, 0x65, 0x64, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0a,
	0x63, 0x72, 0x6f, 0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x7c, 0x0a, 0x10, 0x4c, 0x61,
	0x6e, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2e,
	0x0a, 0x13, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x38,
	0x0a, 0x08, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x6e, 0x65,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x08,
	0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x22, 0xf4, 0x01, 0x0a, 0x13, 0x52, 0x6f, 0x74,
	0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x55, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x66, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x66, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x62, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x62, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x62, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x62, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22,
	0x7e, 0x0a, 0x11, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x4d, 0x73, 0x12, 0x39, 0x0a, 0x08, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x08, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x42,
	0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_recorder_proto_recorder_proto_rawDescOnce sync.Once
	file_recorder_proto_recorder_proto_rawDescData = file_recorder_proto_recorder_proto_rawDesc
)

func file_recorder_proto_recorder_proto_rawDescGZIP() []byte {
	file_recorder_proto_recorder_proto_rawDescOnce.Do(func() {
		file_recorder_proto_recorder_proto_rawDescData = protoimpl.X.CompressGZIP(file_recorder_proto_recorder_proto_rawDescData)
	})
	return file_recorder_proto_recorder_proto_rawDescData
}

var file_recorder_proto_recorder_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_recorder_proto_recorder_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_recorder_proto_recorder_proto_goTypes = []interface{}{
	(DeepweedDetection_HitClass)(0),  // 0: recorder.DeepweedDetection.HitClass
	(*DetectionClass)(nil),           // 1: recorder.DetectionClass
	(*DeepweedDetection)(nil),        // 2: recorder.DeepweedDetection
	(*TrackedItemCoordinates)(nil),   // 3: recorder.TrackedItemCoordinates
	(*EmbeddingCategory)(nil),        // 4: recorder.EmbeddingCategory
	(*TrackedItem)(nil),              // 5: recorder.TrackedItem
	(*CandidateShift)(nil),           // 6: recorder.CandidateShift
	(*DeepweedPredictionFrame)(nil),  // 7: recorder.DeepweedPredictionFrame
	(*DeepweedPredictionRecord)(nil), // 8: recorder.DeepweedPredictionRecord
	(*LaneHeightSnapshot)(nil),       // 9: recorder.LaneHeightSnapshot
	(*LaneHeightRecord)(nil),         // 10: recorder.LaneHeightRecord
	(*RotaryTicksSnapshot)(nil),      // 11: recorder.RotaryTicksSnapshot
	(*RotaryTicksRecord)(nil),        // 12: recorder.RotaryTicksRecord
}
var file_recorder_proto_recorder_proto_depIdxs = []int32{
	0,  // 0: recorder.DeepweedDetection.hit_class:type_name -> recorder.DeepweedDetection.HitClass
	1,  // 1: recorder.DeepweedDetection.detection_classes:type_name -> recorder.DetectionClass
	3,  // 2: recorder.TrackedItem.coordinates_before:type_name -> recorder.TrackedItemCoordinates
	3,  // 3: recorder.TrackedItem.coordinates_after:type_name -> recorder.TrackedItemCoordinates
	2,  // 4: recorder.DeepweedPredictionFrame.detections:type_name -> recorder.DeepweedDetection
	5,  // 5: recorder.DeepweedPredictionFrame.tracked_items:type_name -> recorder.TrackedItem
	6,  // 6: recorder.DeepweedPredictionFrame.best_shift:type_name -> recorder.CandidateShift
	6,  // 7: recorder.DeepweedPredictionFrame.candidate_shifts:type_name -> recorder.CandidateShift
	7,  // 8: recorder.DeepweedPredictionRecord.frame:type_name -> recorder.DeepweedPredictionFrame
	9,  // 9: recorder.LaneHeightRecord.snapshot:type_name -> recorder.LaneHeightSnapshot
	11, // 10: recorder.RotaryTicksRecord.snapshot:type_name -> recorder.RotaryTicksSnapshot
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_recorder_proto_recorder_proto_init() }
func file_recorder_proto_recorder_proto_init() {
	if File_recorder_proto_recorder_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_recorder_proto_recorder_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectionClass); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedDetection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackedItemCoordinates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmbeddingCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CandidateShift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedPredictionFrame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedPredictionRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaneHeightSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaneHeightRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryTicksSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryTicksRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_recorder_proto_recorder_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_recorder_proto_recorder_proto_goTypes,
		DependencyIndexes: file_recorder_proto_recorder_proto_depIdxs,
		EnumInfos:         file_recorder_proto_recorder_proto_enumTypes,
		MessageInfos:      file_recorder_proto_recorder_proto_msgTypes,
	}.Build()
	File_recorder_proto_recorder_proto = out.File
	file_recorder_proto_recorder_proto_rawDesc = nil
	file_recorder_proto_recorder_proto_goTypes = nil
	file_recorder_proto_recorder_proto_depIdxs = nil
}
