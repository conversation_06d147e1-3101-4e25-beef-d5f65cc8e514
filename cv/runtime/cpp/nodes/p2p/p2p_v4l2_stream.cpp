#include "p2p_v4l2_stream.hpp"
#include "infer.h"

#include "cv/p2p/p2p_utils.h"
#include "cv/runtime/proto/cv_runtime.pb.h"
#include "frontend/proto/weeding_diagnostics.pb.h"

#include <google/protobuf/util/json_util.h>
#include <spdlog/spdlog.h>

constexpr int channels(3);
namespace cv::runtime::nodes::p2p {
P2PV4l2Stream::P2PV4l2Stream(lib::common::camera::Camera camera, Input<cv::p2p::P2POutput> input,
                             Input<cv::p2p::P2POutputForBurstRecord> p2p_output_for_burst_record,
                             FixedCamBufferMap dist_camera_buffers, FixedCamBuffer target_img_buffer,
                             std::shared_ptr<carbon::config::ConfigTree> enabled_cfg)
    : CVNodeImpl(P2PV4l2Stream::build_name(camera.get_info().camera_id)), last_processed_ts_(0), last_predict_ts_(0),
      last_coords_(0, 0), curr_predict_img_(std::nullopt), input_(input),
      input_record_data_(p2p_output_for_burst_record), v4l2_writer_(fmt::format("{}_p2p", camera.get_info().camera_id)),
      buffer_initialized_(false), dist_camera_buffers_(dist_camera_buffers), target_img_buffer_(target_img_buffer),
      enabled_(enabled_cfg), identifier_(0) {}

P2PV4l2Stream::~P2PV4l2Stream() {}

int64_t P2PV4l2Stream::tick() {
  set_state(CVNodeState::kPulling);
  cv::p2p::P2POutput p2p_output = input_.pop();
  cv::p2p::P2POutputForBurstRecord p2p_record = input_record_data_.pop();

  set_state(CVNodeState::kProcessing);
  if (p2p_output.target_timestamp_ms <= last_processed_ts_) {
    return last_processed_ts_;
  }
  if (!enabled_.get_value()) {
    last_processed_ts_ = p2p_output.target_timestamp_ms;
    return last_processed_ts_;
  }
  std::optional<lib::common::camera::CameraImage> target_img(std::nullopt);

  target_img = target_img_buffer_->get_opt(p2p_output.target_timestamp_ms);
  if (!target_img) {
    return last_processed_ts_;
  }
  if (!load_predict_image(p2p_record, p2p_output, glm::vec2(target_img->get_dimensions()), target_img->ppi.value())) {
    return last_processed_ts_;
  }
  // TODO use target_img and class predict img
  torch::Tensor target_image_t =
      target_img->image.is_cuda()
          ? target_img->image.cpu().detach()
          : target_img->image.clone().detach(); // move to cpu if on gpu or clone if on cpu so it is a new cpu tensor

  const int num_cascades = 2;
  try {
    for (int i = 0; i < num_cascades; i++) {
      float half_percentage = (0.2f * std::pow(3.0f, (float)i)) / 2.0f;
      torch::Tensor predict_at_target_ppi_inner =
          curr_predict_img_->image
              .slice(-2, (int64_t)((float)curr_predict_img_->image.size(-2) * (0.5f - half_percentage)),
                     (int64_t)((float)curr_predict_img_->image.size(-2) * (0.5f + half_percentage)))
              .slice(-1, (int64_t)((float)curr_predict_img_->image.size(-1) * (0.5f - half_percentage)),
                     (int64_t)((float)curr_predict_img_->image.size(-1) * (0.5f + half_percentage)));

      // Create opencv mat from predict image and draw cross at center of the image
      predict_at_target_ppi_inner =
          predict_at_target_ppi_inner.squeeze(0).permute({1, 2, 0}).to(torch::kUInt8).contiguous();
      const int data_type = CV_8UC((int)predict_at_target_ppi_inner.size(2));
      cv::Mat predict_mat((int)predict_at_target_ppi_inner.size(0), (int)predict_at_target_ppi_inner.size(1), data_type,
                          predict_at_target_ppi_inner.data_ptr());

      cv::drawMarker(predict_mat,
                     {(int)predict_at_target_ppi_inner.size(1) / 2, (int)predict_at_target_ppi_inner.size(0) / 2},
                     {255, 0, 0}, cv::MARKER_TILTED_CROSS, 15, 2);
      predict_at_target_ppi_inner = predict_at_target_ppi_inner.permute({2, 0, 1}).unsqueeze(0).to(torch::kFloat32);

      // Resize predict image to 20% of target image
      predict_at_target_ppi_inner =
          interpolate(predict_at_target_ppi_inner, glm::vec2(target_img->get_dimensions()) * 0.2f);

      target_image_t = target_image_t.permute({1, 2, 0}).to(torch::kUInt8).contiguous();
      cv::Mat target_mat((int)target_image_t.size(0), (int)target_image_t.size(1), data_type,
                         target_image_t.data_ptr());

      cv::drawMarker(target_mat, {(int)p2p_output.target_coord_x, (int)p2p_output.target_coord_y}, {255, 0, 0},
                     cv::MARKER_TILTED_CROSS, 15, 2);

      target_image_t = target_image_t.permute({2, 0, 1}).to(torch::kFloat32);
      // Copy predict image into top left 20% of target image to create image used for annotating
      target_image_t
          .slice(-1, predict_at_target_ppi_inner.size(-1) * (num_cascades - 1 - i),
                 predict_at_target_ppi_inner.size(-1) * (num_cascades - 1 - i + 1))
          .slice(-2, 0, predict_at_target_ppi_inner.size(-2))
          .copy_(predict_at_target_ppi_inner.squeeze(0));
    }
    auto height = target_img->get_height();
    auto width = target_img->get_width();
    init_buffers_if_needed(width, height);

    // Convert CHW -> HWC.
    auto image = target_image_t.permute({1, 2, 0});

    set_state(CVNodeState::kPushing);

    torch_video_buffer_.copy_(image);
    add_id(width, height);
    auto ret_code = v4l2_writer_.write(video_buffer_, width * height * channels);
    if (ret_code < 0) {
      spdlog::error("V4L2 Write Error: Written: {} Expected: {} WxH: {}x{}", std::strerror(errno),
                    width * height * channels, width, height);
    }
    last_processed_ts_ = p2p_output.target_timestamp_ms;
  } catch (const maka_error &err) {
  }
  return last_processed_ts_;
}
bool P2PV4l2Stream::load_predict_image(const cv::p2p::P2POutputForBurstRecord &record_data,
                                       const cv::p2p::P2POutput &output_data, const glm::vec2 &dims, float target_ppi) {
  if (last_predict_ts_ == output_data.predict_timestamp_ms && record_data.predict_coord == last_coords_ &&
      record_data.predict_buffer_name == last_predict_name_) {
    // Re-use current image
    return true;
  }
  std::optional<lib::common::camera::CameraImage> predict_img(std::nullopt);
  if (dist_camera_buffers_->count(record_data.predict_buffer_name) != 0) {
    auto predict_cam_buffer = dist_camera_buffers_->at(record_data.predict_buffer_name).get();
    predict_img = predict_cam_buffer->get_opt(output_data.predict_timestamp_ms);
  }
  if (!predict_img) {
    return false;
  }
  // Pad and crop predict image to target image size in inches centered around predict coordinates
  glm::ivec2 predict_half_dimensions = glm::round(dims / target_ppi * predict_img->ppi.value() / 2.0f);
  auto [predict_crop, _] = cv::utils::crop_or_pad(predict_img->image.unsqueeze(0).to(torch::kFloat32),
                                                  record_data.predict_coord, predict_half_dimensions * 2);
  predict_img->image = predict_crop.cpu();
  curr_predict_img_ = predict_img;
  last_coords_ = record_data.predict_coord;
  last_predict_ts_ = output_data.predict_timestamp_ms;
  last_predict_name_ = record_data.predict_buffer_name;
  return true;
}

void P2PV4l2Stream::init_buffers_if_needed(int width, int height) {
  if (buffer_initialized_) {
    return;
  }

  video_buffer_ = new __u8[width * height * channels];
  torch_video_buffer_ = torch::from_blob(video_buffer_, {height, width, channels}, torch::kUInt8);
  if (!v4l2_writer_.set_format(width, height)) {
    throw std::runtime_error("Failed to set format v4l2 device");
  }

  buffer_initialized_ = true;
}
void P2PV4l2Stream::add_id(int width, int height) {
  if (identifier_ == 0) {
    return;
  }
  auto id_str = fmt::format("{}", identifier_);

  const auto font = cv::FONT_HERSHEY_COMPLEX_SMALL;
  const double font_scale = 1;
  const int thickness = 1;
  const uint32_t padding = 5;

  int baseline = 0;
  auto text_size = cv::getTextSize(id_str, font, font_scale, thickness, &baseline);

  cv::Mat m(height, width, CV_8UC3, video_buffer_);
  cv::putText(m, id_str, cv::Point(width - text_size.width - padding, text_size.height + padding), font, font_scale,
              CV_RGB(0, 0, 0), thickness + 2);
  cv::putText(m, id_str, cv::Point(width - text_size.width - padding, text_size.height + padding), font, font_scale,
              CV_RGB(255, 219, 0), thickness);
}
} // namespace cv::runtime::nodes::p2p
