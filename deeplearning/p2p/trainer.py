import functools
import logging
import math
import os
import random
import time
from typing import Any, Callable, Dict, List, Optional, Tuple, Union, cast

import cv2
import numpy as np
import numpy.typing as npt
import torch
import torch.nn.functional as F
import wandb
from lightning.pytorch.loggers import <PERSON><PERSON><PERSON><PERSON><PERSON>ger
from lightning.pytorch.strategies import SingleDeviceStrategy
from sqlalchemy.orm import Session
from torch.utils.data import DataLoader, get_worker_info

import deeplearning.server.trt_runtime as rt
from deeplearning.dl_metrics.p2p_metrics import p2p_db
from deeplearning.model_io import ModelMetadata, load_tensorrt_model
from deeplearning.p2p.config import P2PConfig
from deeplearning.p2p.datasets import DATASET_ELEMENT, LABEL_THRESHOLD, P2PDataset, P2PTrainValDatasets, label_coord
from deeplearning.p2p.metadata import ImageMetadata
from deeplearning.p2p.metrics import AverageMetrics, Metrics
from deeplearning.p2p.models.p2p_model_v1 import P2PModelV1
from deeplearning.p2p.models.utils import P2PModel, P2PModelOutput, P2PModelOutputFactory
from deeplearning.p2p.trt_convert import TrtConvert
from deeplearning.p2p.utils import downsample_like
from deeplearning.p2p.version import get_version
from deeplearning.utils.dataset import DEFAULT_SPLITS
from deeplearning.utils.debug_signal import DebugSignal
from deeplearning.utils.fire_utils import safe_split
from deeplearning.utils.images import IMAGENET_MEANS, IMAGENET_STDS, denormalize_image
from deeplearning.utils.trainer import (
    S3_BUCKET,
    Environment,
    Trainer,
    TrainingModule,
    compute_md5sum,
    get_compute_capability,
    get_tensorrt_version,
)
from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import get_wandb_metadata_json_str

EPS = 1e-5


class P2PTrainingModule(TrainingModule):
    def __init__(
        self,
        config: P2PConfig,
        datasets: P2PTrainValDatasets,
        initial_lr: float,
        lr_milestones: Optional[List[int]],
        lr_gamma: float,
        train_batch_size: int,
        val_batch_size: int,
        data_pipeline_processes: int,
        model: Optional[P2PModel] = None,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        test_log_image_p: float = 0.05,
        additional_wandb_config: Optional[Dict[str, Any]] = None,
        convert_int8: bool = False,
    ) -> None:
        super().__init__()
        additional_wandb_config = additional_wandb_config if additional_wandb_config is not None else {}
        self._datasets = datasets
        self._initial_lr = initial_lr
        self._lr_milestones = lr_milestones
        self._lr_gamma = lr_gamma
        self._train_batch_size = train_batch_size
        self._val_batch_size = val_batch_size
        self._data_pipeline_processes = data_pipeline_processes
        self._train_log_image_p = train_log_image_p
        self._val_log_image_p = val_log_image_p
        self._test_log_image_p = test_log_image_p
        self._model = model if model is not None else P2PModelV1()
        self._trt_model: Optional[torch.nn.Module] = None
        self._trt_max_batch_size: int = 1
        self._convert_int8 = convert_int8
        self._debug_signal = DebugSignal()
        self._experiment_url: Optional[str] = None
        self._additional_wandb_config = additional_wandb_config
        self._training_outputs: List[Metrics] = []
        self._validation_outputs: List[Metrics] = []
        self._test_outputs: List[Metrics] = []
        self._config = config
        self._p2p_db_engine: Optional[Session] = None
        self._exp_dir: Optional[str] = None

        logging.info(
            f"Datasets: train={len(self._datasets.get_training())} examples, val={len(self._datasets.get_validation())} examples, test={len(self._datasets.get_test())} examples"
        )

    @property
    def config(self) -> P2PConfig:
        return self._config

    @property
    def trt_file_name(self) -> str:
        tensorrt_version = get_tensorrt_version().replace(".", "")
        compute_capability = get_compute_capability().replace(".", "")
        return f"trt_{'int8' if self._convert_int8 else 'fp16'}_{tensorrt_version}_{compute_capability}.trt"

    @property
    def trt_file_path(self) -> str:
        assert self._exp_dir is not None
        return os.path.join(self._exp_dir, self.trt_file_name)

    def forward(self, perspective: torch.Tensor, image: torch.Tensor) -> P2PModelOutput:
        if self._trt_model is not None:
            return P2PModelOutputFactory.unpack(self._trt_model(perspective, image))
        else:
            return P2PModelOutputFactory.unpack(self._model(perspective, image))

    def hit_loss(self, hit_ds: torch.Tensor, out_hat: P2PModelOutput) -> torch.Tensor:
        hit_weight = hit_ds * hit_ds[0, 0].numel() + 1
        return F.binary_cross_entropy(out_hat.hit_ds, hit_ds, weight=hit_weight)

    def offset_loss(
        self,
        label: torch.Tensor,
        hit_ds: torch.Tensor,
        out_hat: P2PModelOutput,
        hit_threshold: float = EPS,
        score_threshold: float = 0.5,
    ) -> torch.Tensor:
        batch_size = label.shape[0]
        offset_loss = torch.tensor(0.0, device=out_hat.device)
        for n in range(batch_size):
            hit_ds_coords = (hit_ds[n, 0] > LABEL_THRESHOLD).nonzero()
            hit_ds_hat_coords = (out_hat.hit_ds[n, 0] > hit_threshold).nonzero()
            if not len(hit_ds_coords) or not len(hit_ds_hat_coords):
                continue

            y, x = hit_ds_coords[0]
            y_h, x_h = hit_ds_hat_coords[0]
            # TODO(asergeev): would longer offset work, too?
            if abs(y_h - y) > 1 or abs(x_h - x) > 1:
                continue

            coord = label_coord(label, n)
            coord_hat = out_hat.coord(n, hit_threshold=hit_threshold, score_threshold=score_threshold, stage=1)
            assert coord is not None and coord_hat is not None

            # Offsets between true point and upsampled x16 predicted point
            # We use it to construct target prediction
            y_offset = coord[1] - coord_hat[1]
            x_offset = coord[0] - coord_hat[0]

            # Guard
            if abs(y_offset) >= int(out_hat.offset.shape[2] / 2) or abs(x_offset) >= int(out_hat.offset.shape[3] / 2):
                continue

            offset = torch.zeros(out_hat.offset[0:1].shape, device=out_hat.device)
            offset[:, :, int(out_hat.offset.shape[2] / 2) + y_offset, int(out_hat.offset.shape[3] / 2) + x_offset] = 1.0
            offset_ds = downsample_like(offset, out_hat.offset_ds)

            offset_weight = offset_ds * offset_ds[0, 0].numel() + 1
            offset_loss += F.binary_cross_entropy(out_hat.offset_ds[n : n + 1], offset_ds, weight=offset_weight)
        offset_loss /= batch_size
        return offset_loss

    def match_loss(
        self, label: torch.Tensor, out_hat: P2PModelOutput, hit_threshold: float = EPS, score_threshold: float = 0.5
    ) -> torch.Tensor:
        batch_size = label.shape[0]
        match_loss = torch.tensor(0.0, device=out_hat.device)
        for n in range(batch_size):
            coord = label_coord(label, n)
            coord_hat = out_hat.coord(n, hit_threshold=hit_threshold, score_threshold=score_threshold, stage=2)
            if coord_hat is not None:
                if coord is not None:
                    d = math.hypot(coord[0] - coord_hat[0], coord[1] - coord_hat[1])
                    match_n = 1.0 if d <= 4 else 0.5 if d <= 8 else 0.0
                else:
                    match_n = 0.0
                match_loss += F.binary_cross_entropy(out_hat.match[n], torch.tensor(match_n, device=out_hat.device))
        match_loss /= batch_size
        return 2 * match_loss

    def annotate_circle(self, img: torch.Tensor, n: int, c: int, coord: Tuple[int, int]) -> None:
        mask: npt.NDArray[Any] = np.zeros((img.shape[2], img.shape[3]), dtype=np.float32)
        mask = cv2.circle(mask, coord, 5, (1,), thickness=2)
        img[n, c] = torch.max(img[n, c], torch.tensor(mask).to(img.device))

    def annotate_image(
        self,
        perspective: torch.Tensor,
        image: torch.Tensor,
        label: torch.Tensor,
        out_hat: P2PModelOutput,
        hit_threshold: float = EPS,
        score_threshold: float = 0.5,
    ) -> torch.Tensor:
        batch_size = label.shape[0]

        # Annotate perspective
        perspective_ann = torch.stack([denormalize_image(t) for t in perspective])
        for n in range(batch_size):
            perspective_center = (int(perspective_ann.shape[3] / 2), int(perspective_ann.shape[2] / 2))
            self.annotate_circle(perspective_ann, n, 0, perspective_center)

        # Annotate image
        image_ann = torch.stack([denormalize_image(t) for t in image])
        image_ann[:, :, : perspective.shape[2], : perspective.shape[3]] = perspective_ann
        for n in range(batch_size):
            coord = label_coord(label, n)
            if coord is not None:
                self.annotate_circle(image_ann, n, 0, coord)
        for n in range(batch_size):
            coord_hat = out_hat.coord(n, hit_threshold=hit_threshold, score_threshold=score_threshold, stage=1)
            if coord_hat is not None:
                self.annotate_circle(image_ann, n, 2, coord_hat)
        for n in range(batch_size):
            coord_hat = out_hat.coord(n, hit_threshold=hit_threshold, score_threshold=score_threshold)
            if coord_hat is not None:
                self.annotate_circle(image_ann, n, 1, coord_hat)

        return image_ann

    @staticmethod
    def collate_fn(data: List[DATASET_ELEMENT],) -> Any:
        batch = (
            torch.stack([datapoint[0] for datapoint in data]),
            torch.stack([datapoint[1] for datapoint in data]),
            torch.stack([datapoint[2] for datapoint in data]),
            [datapoint[3] for datapoint in data],
            [datapoint[4] for datapoint in data],
        )

        return batch

    def predict(  # noqa: C901
        self, batch: Any, hit_threshold: float = EPS, score_threshold: float = 0.5
    ) -> Tuple[
        Metrics, torch.Tensor, torch.Tensor, torch.Tensor,
    ]:
        perspective, image, label, metadata, reconstruction_metadata = batch

        # run prediction
        out_hat = self.forward(perspective, image)

        # resize label to match hit_ds shape
        hit_ds = downsample_like(label, out_hat.hit_ds)

        # losses
        hit_loss = self.hit_loss(hit_ds, out_hat)
        offset_loss = self.offset_loss(
            label, hit_ds, out_hat, hit_threshold=hit_threshold, score_threshold=score_threshold
        )
        match_loss = self.match_loss(label, out_hat, hit_threshold=hit_threshold, score_threshold=score_threshold)

        # metrics
        metrics = Metrics(
            label,
            out_hat,
            hit_loss,
            offset_loss,
            match_loss,
            hit_threshold=hit_threshold,
            score_threshold=score_threshold,
            metadata=metadata,
            dataset_root="/data/deeplearning/p2p",
            p2p_db_engine=self._p2p_db_engine,
            reconstruction_metadata=reconstruction_metadata,
            ppi=self._datasets.get_training().model_ppi,
        )

        # annotations
        with torch.no_grad():
            debug_image_ann = self.annotate_image(
                perspective, image, label, out_hat, hit_threshold=hit_threshold, score_threshold=score_threshold
            ).cpu()
            debug_perspective_cropped = out_hat.debug_perspective_cropped.detach().cpu()
            debug_image_cropped = out_hat.debug_image_cropped.detach().cpu()

        return (metrics, debug_image_ann, debug_perspective_cropped, debug_image_cropped)

    def on_train_start(self) -> None:
        if self.logger is not None:
            wandb.config.smearing = self._datasets.get_training().smearing
            wandb.config.batch_size = self._train_batch_size
            wandb.watch(self._model)
            wandb.config.update(
                self._additional_wandb_config, allow_val_change=True,
            )
            wandb.config.update(self._config.to_dict(), allow_val_change=True)
            wandb.config.update(
                {
                    "train_dataset_count": self._datasets.get_training().num_files,
                    "validation_dataset_count": self._datasets.get_validation().num_files,
                    "test_dataset_count": self._datasets.get_test().num_files,
                },
                allow_val_change=True,
            )

    def training_step(self, batch: DATASET_ELEMENT, batch_nb: int) -> Dict[str, Any]:
        if self._debug_signal.is_set():
            import pdb

            pdb.set_trace()
            self._debug_signal.clear()

        (metrics, debug_image_ann, debug_perspective_cropped, debug_image_cropped) = self.predict(batch)

        if self.logger is not None:
            if random.random() < self._train_log_image_p:
                self.logger.experiment.log(
                    {
                        "image_ann": [wandb.Image(debug_image_ann)],
                        "perspective_cropped": [wandb.Image(debug_perspective_cropped)],
                        "image_cropped": [wandb.Image(debug_image_cropped)],
                    },
                    step=self.trainer.global_step,
                )

        detached_metrics = metrics.detach().cpu()
        metrics_dict = detached_metrics.to_dict(lr=self._optimizer.param_groups[0]["lr"], epoch=self.current_epoch)
        self._training_outputs.append(detached_metrics)
        self.log_dict(metrics_dict, logger=False, prog_bar=True, on_epoch=False, on_step=True)

        result = {"loss": metrics.loss, "log": metrics_dict}
        return result

    def on_train_epoch_end(self) -> None:
        metrics = AverageMetrics(self._training_outputs)
        metrics_dict = {k: v.item() for k, v in metrics.to_dict(prefix="train_").items()}
        self._training_outputs.clear()

        self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)

    def validation_step(self, batch: DATASET_ELEMENT, batch_nb: int) -> None:
        with torch.no_grad():
            (metrics, debug_image_ann, debug_perspective_cropped, debug_image_cropped) = self.predict(batch)

        if self.logger is not None:
            if random.random() < self._val_log_image_p:
                self.logger.experiment.log(
                    {
                        "val_image_ann": [wandb.Image(debug_image_ann)],
                        "val_perspective_cropped": [wandb.Image(debug_perspective_cropped)],
                        "val_image_cropped": [wandb.Image(debug_image_cropped)],
                    },
                    step=self.trainer.global_step,
                )
        result = metrics.cpu()
        self._validation_outputs.append(result)

    def on_validation_epoch_end(self) -> None:
        metrics = AverageMetrics(self._validation_outputs)
        metrics_dict = {k: v.item() for k, v in metrics.to_dict(prefix="val_").items()}
        logging.info(f"\n\nVALIDATION RESULTS: {metrics_dict}\n")
        self._validation_outputs.clear()

        self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)

    def on_test_start(self) -> None:
        assert self._exp_dir is not None
        db_path = os.path.join(self._exp_dir, f"{self.get_test_prefix()}db/p2p_{p2p_db.__version__}.db")
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self._p2p_db_engine = p2p_db.get_db(os.path.join(self._exp_dir, db_path))

    def test_step(self, batch: DATASET_ELEMENT, batch_nb: int) -> None:
        with torch.no_grad():
            (metrics, debug_image_ann, debug_perspective_cropped, debug_image_cropped) = self.predict(batch)

        if self.logger is not None:
            if random.random() < self._val_log_image_p:
                self.logger.experiment.log(
                    {
                        "test_image_ann": [wandb.Image(debug_image_ann[0])],
                        "test_perspective_cropped": [wandb.Image(debug_perspective_cropped[0])],
                        "test_image_cropped": [wandb.Image(debug_image_cropped[0])],
                    }
                )

        result = metrics.cpu()
        self._test_outputs.append(result)

    def set_exp_dir(self, exp_dir: str) -> None:
        self._exp_dir = exp_dir

    def set_experiment_url(self, experiment_url: str) -> None:
        self._experiment_url = experiment_url

    def on_test_epoch_end(self) -> None:
        metrics = AverageMetrics(self._test_outputs)
        prefix = "test_unoptimized_"
        if self._trt_model is not None:
            prefix = "test_"
        metrics_dict = {k: v.item() for k, v in metrics.to_dict(prefix=prefix).items()}

        if self.logger is not None and isinstance(self.logger, WandbLogger):
            self.logger.experiment.summary.update(metrics_dict)
        self._test_outputs.clear()

        self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)
        self._p2p_db_engine = None

    def configure_optimizers(
        self,
    ) -> Union[
        torch.optim.Optimizer, Tuple[List[torch.optim.Optimizer], List[torch.optim.lr_scheduler.LRScheduler]],
    ]:
        # REQUIRED
        self._optimizer = torch.optim.SGD(self.parameters(), lr=self._initial_lr, momentum=0.9, weight_decay=1e-5)

        if self._lr_milestones is not None:
            scheduler = torch.optim.lr_scheduler.MultiStepLR(
                self._optimizer, milestones=self._lr_milestones, gamma=self._lr_gamma
            )
            return [self._optimizer], [scheduler]

        return self._optimizer

    def get_worker_init_fn(self) -> Callable[[int], None]:
        def worker_init_fn(worker_id: int) -> None:
            worker_info = get_worker_info()
            if worker_info is not None and hasattr(worker_info.dataset, "set_seed"):
                worker_info.dataset.set_seed(worker_id)

        return worker_init_fn

    def load_trt(self, trt_file_path: str) -> None:
        self._trt_model, _ = load_tensorrt_model(trt_file_path)
        cast(rt.TRTModule, self._trt_model).set_cache_context(True)

    def get_test_prefix(self) -> str:
        prefix = "test_"
        if self._trt_model is None:
            prefix = prefix + "unoptimized_"

        return prefix

    def optimize_for_testing(self, best_checkpoint: Optional[str] = None) -> bool:
        if self.config.make_trt_model:
            torch.cuda.empty_cache()
            assert self._exp_dir is not None
            trt_converter = TrtConvert(self._datasets, self.export_metadata(), self.export_model())
            trt_converter.convert(
                max_batch_size=self._trt_max_batch_size,
                save_to=self.trt_file_path,
                int8=self._convert_int8,
                fp16=not self._convert_int8,
            )

            # replace model with optimized model
            self.load_trt(self.trt_file_path)
            return True
        return False

    def train_dataloader(self) -> "DataLoader[DATASET_ELEMENT]":
        return DataLoader(
            self._datasets.get_training(),
            shuffle=True,
            batch_size=self._train_batch_size,
            num_workers=self._data_pipeline_processes,
            collate_fn=self.collate_fn,
            pin_memory=True,
            drop_last=True,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def val_dataloader(self) -> "DataLoader[DATASET_ELEMENT]":
        return DataLoader(
            self._datasets.get_validation(),
            batch_size=self._val_batch_size,
            num_workers=self._data_pipeline_processes,
            collate_fn=self.collate_fn,
            pin_memory=True,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def test_dataloader(self) -> "DataLoader[DATASET_ELEMENT]":
        return DataLoader(
            self._datasets.get_test(),
            batch_size=self._trt_max_batch_size,
            num_workers=self._data_pipeline_processes,
            collate_fn=self.collate_fn,
            pin_memory=True,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def export_model(self) -> P2PModel:
        return self._model

    def export_torch_script(self) -> torch.jit.ScriptModule:
        return cast(torch.jit.ScriptModule, torch.jit.script(self._model))

    @property
    def use_cases(self) -> List[ModelUseCase]:
        return [ModelUseCase.P2P]

    def export_metadata(self) -> ModelMetadata:
        return ModelMetadata(
            input_dtype=torch.float32,
            input_size=(self._datasets.get_training().input_size, self._datasets.get_training().input_size),
            aux_input_sizes=[
                (
                    self._datasets.get_training().perspective_input_size,
                    self._datasets.get_training().perspective_input_size,
                )
            ],
            means=IMAGENET_MEANS[:3],
            stds=IMAGENET_STDS[:3],
            experiment_url=self._experiment_url,
            use_cases=self.use_cases,
            ppi=self._datasets.get_training().model_ppi,
            # TODO(asergeev): figure out low precision support
            supports_half=False,
            model_class=self._model.__class__.__name__,
        )


class P2PTrainer(Trainer):
    def __init__(self) -> None:
        super().__init__()
        self._datasets: Optional[P2PTrainValDatasets] = None

    def uniform_dataset(
        self,
        data_root: Union[str, Tuple[str, ...]],
        splits: Tuple[int, ...] = DEFAULT_SPLITS,
        perspective_crop_size_inches: float = 1,
        image_crop_size_inches: float = 6,
        model_ppi: int = 100,
        smearing: Optional[int] = None,
        num_samples: Optional[int] = None,
    ) -> "Trainer":
        data_root = cast(Tuple[str, ...], safe_split(data_root))
        mk_dataset = functools.partial(
            P2PDataset,
            perspective_crop_size_inches=perspective_crop_size_inches,
            image_crop_size_inches=image_crop_size_inches,
            model_ppi=model_ppi,
            smearing=smearing,
        )

        train_dataset = mk_dataset(data_root, training=True, splits=splits, split_idx=0, num_samples=num_samples)
        val_dataset = mk_dataset(data_root, splits=splits, split_idx=1)
        test_dataset = mk_dataset(data_root, splits=splits, split_idx=2)
        calibration_dataset = mk_dataset(data_root, splits=splits, split_idx=2)
        self._datasets = P2PTrainValDatasets(train_dataset, val_dataset, test_dataset, calibration_dataset)
        return self

    def explicit_split_dataset_roots(
        self,
        train_data_root: Union[str, Tuple[str, ...]],
        validation_data_root: Union[str, Tuple[str, ...]],
        test_data_root: Union[str, Tuple[str, ...]],
        perspective_crop_size_inches: float = 1,
        image_crop_size_inches: float = 6,
        model_ppi: int = 100,
        smearing: Optional[int] = None,
        num_samples: Optional[int] = None,
    ) -> "Trainer":
        train_data_root = cast(Tuple[str, ...], safe_split(train_data_root))
        validation_data_root = cast(Tuple[str, ...], safe_split(validation_data_root))
        test_data_root = cast(Tuple[str, ...], safe_split(test_data_root))
        mk_dataset = functools.partial(
            P2PDataset,
            perspective_crop_size_inches=perspective_crop_size_inches,
            image_crop_size_inches=image_crop_size_inches,
            model_ppi=model_ppi,
            smearing=smearing,
        )

        train_dataset = mk_dataset(train_data_root, training=True, num_samples=num_samples)
        val_dataset = mk_dataset(validation_data_root)
        test_dataset = mk_dataset(test_data_root)
        calibration_dataset = mk_dataset(test_data_root)
        self._datasets = P2PTrainValDatasets(train_dataset, val_dataset, test_dataset, calibration_dataset)
        return self

    def explicit_split_dataset_files(
        self,
        train_files: List[ImageMetadata],
        validation_files: List[ImageMetadata],
        test_files: List[ImageMetadata],
        perspective_crop_size_inches: float = 1,
        image_crop_size_inches: float = 6,
        model_ppi: int = 100,
        smearing: Optional[int] = None,
        num_samples: Optional[int] = None,
    ) -> "Trainer":
        mk_dataset = functools.partial(
            P2PDataset,
            perspective_crop_size_inches=perspective_crop_size_inches,
            image_crop_size_inches=image_crop_size_inches,
            model_ppi=model_ppi,
            smearing=smearing,
        )

        train_dataset = mk_dataset(files=train_files, training=True, num_samples=num_samples)
        calibration_dataset = mk_dataset(files=test_files, num_samples=128)
        val_dataset = mk_dataset(files=validation_files)
        test_dataset = mk_dataset(files=test_files)
        self._datasets = P2PTrainValDatasets(train_dataset, val_dataset, test_dataset, calibration_dataset)
        return self

    def infer(
        self,
        config: P2PConfig,
        trt_path: str,
        evaluation_dir: str,
        fast_run: bool,
        model: Optional[P2PModel] = None,
        initial_lr: float = 0.01,
        lr_milestones: Optional[Union[int, Tuple[int, ...]]] = None,
        lr_gamma: float = 0.5,
        data_pipeline_processes: int = 16,
        train_batch_size: int = 4,
        val_batch_size: int = 4,
    ) -> None:
        assert self._datasets is not None, "Train dataset is not defined, use `- dataset`"

        module = P2PTrainingModule(
            config,
            self._datasets,
            initial_lr=initial_lr,
            lr_milestones=safe_split(lr_milestones),
            lr_gamma=lr_gamma,
            train_batch_size=train_batch_size,
            val_batch_size=val_batch_size,
            data_pipeline_processes=data_pipeline_processes,
            train_log_image_p=0,
            val_log_image_p=0,
            test_log_image_p=0,
            model=model,
        )

        super()._infer(module, trt_path=trt_path, evaluation_dir=evaluation_dir, fast_run=fast_run, strategy="auto")

    def train(
        self,
        config: P2PConfig,
        pipeline_id: str,
        model: P2PModel,
        fast_run: bool = False,
        epochs: int = 30,
        initial_lr: float = 0.01,
        lr_milestones: Optional[Union[int, Tuple[int, ...]]] = None,
        lr_gamma: float = 0.5,
        train_batch_size: int = 4,
        val_batch_size: int = 16,
        data_pipeline_processes: int = 16,
        resume_from: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        log_experiment: bool = True,
        checkpoint_dir: Optional[str] = None,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        convert_int8: bool = False,
        customer: Optional[str] = None,
        robot_name: Optional[str] = None,
        environment: Environment = Environment.DEVELOPMENT,
        deploy: bool = False,
        additional_wandb_config: Optional[Dict[str, Any]] = None,
    ) -> None:
        assert self._datasets is not None, "datasets is not defined"
        additional_wandb_config = additional_wandb_config or {}

        module = P2PTrainingModule(
            config,
            self._datasets,
            initial_lr=initial_lr,
            lr_milestones=safe_split(lr_milestones),
            lr_gamma=lr_gamma,
            train_batch_size=train_batch_size,
            val_batch_size=val_batch_size,
            data_pipeline_processes=data_pipeline_processes,
            train_log_image_p=train_log_image_p,
            val_log_image_p=val_log_image_p,
            additional_wandb_config=additional_wandb_config,
            convert_int8=convert_int8,
            model=model,
        )

        exp_dir = super()._train(
            module,
            get_version(),
            fast_run=fast_run,
            epochs=epochs,
            resume_from=resume_from,
            description=description,
            tags=tags,
            log_experiment=log_experiment,
            ci_run=config.ci_run,
            checkpoint_dir=checkpoint_dir,
            customer=customer,
            robot_name=robot_name,
            environment=environment,
            deploy=deploy,
            strategy=SingleDeviceStrategy(device=0),
        )

        if not config.ci_run and log_experiment and config.make_trt_model and exp_dir is not None:
            version = exp_dir.rsplit("/", maxsplit=1)[-1]
            self._upload_s3(exp_dir)
            wandb_json = get_wandb_metadata_json_str(exp_dir)
            self._veselka_post_model(
                model_id=version,
                url=f"s3://{S3_BUCKET}/models/{version}/{module.trt_file_name}",
                checksum=compute_md5sum(module.trt_file_path),
                trained_at=int(time.time()),
                metadata_json=module.export_metadata().dump(),
                wandb_json=wandb_json,
                is_good_to_deploy=True,
                dl_config=config.to_dict(),
                exp_dir=exp_dir,
                pipeline_id=pipeline_id,
            )


if __name__ == "__main__":
    P2PTrainer.main()
