// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: golang/simulator/hardware/proto/sim_UI/sim_UI.proto

#include "golang/simulator/hardware/proto/sim_UI/sim_UI.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace simulator_UI {
constexpr GetVelocityRequest::GetVelocityRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetVelocityRequestDefaultTypeInternal {
  constexpr GetVelocityRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetVelocityRequestDefaultTypeInternal() {}
  union {
    GetVelocityRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetVelocityRequestDefaultTypeInternal _GetVelocityRequest_default_instance_;
constexpr GetVelocityResponse::GetVelocityResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : velocity_(0){}
struct GetVelocityResponseDefaultTypeInternal {
  constexpr GetVelocityResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetVelocityResponseDefaultTypeInternal() {}
  union {
    GetVelocityResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetVelocityResponseDefaultTypeInternal _GetVelocityResponse_default_instance_;
constexpr SetVelocityRequest::SetVelocityRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : velocity_(0){}
struct SetVelocityRequestDefaultTypeInternal {
  constexpr SetVelocityRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetVelocityRequestDefaultTypeInternal() {}
  union {
    SetVelocityRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetVelocityRequestDefaultTypeInternal _SetVelocityRequest_default_instance_;
constexpr SetVelocityResponse::SetVelocityResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetVelocityResponseDefaultTypeInternal {
  constexpr SetVelocityResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetVelocityResponseDefaultTypeInternal() {}
  union {
    SetVelocityResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetVelocityResponseDefaultTypeInternal _SetVelocityResponse_default_instance_;
constexpr GetPresetsRequest::GetPresetsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetPresetsRequestDefaultTypeInternal {
  constexpr GetPresetsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPresetsRequestDefaultTypeInternal() {}
  union {
    GetPresetsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPresetsRequestDefaultTypeInternal _GetPresetsRequest_default_instance_;
constexpr PresetSetting::PresetSetting(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : path_()
  , value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct PresetSettingDefaultTypeInternal {
  constexpr PresetSettingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PresetSettingDefaultTypeInternal() {}
  union {
    PresetSetting _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PresetSettingDefaultTypeInternal _PresetSetting_default_instance_;
constexpr GetPresetsResponse::GetPresetsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : settings_(){}
struct GetPresetsResponseDefaultTypeInternal {
  constexpr GetPresetsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPresetsResponseDefaultTypeInternal() {}
  union {
    GetPresetsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPresetsResponseDefaultTypeInternal _GetPresetsResponse_default_instance_;
constexpr SetPresetRequest::SetPresetRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : setting_(nullptr){}
struct SetPresetRequestDefaultTypeInternal {
  constexpr SetPresetRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetPresetRequestDefaultTypeInternal() {}
  union {
    SetPresetRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetPresetRequestDefaultTypeInternal _SetPresetRequest_default_instance_;
constexpr SetPresetResponse::SetPresetResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetPresetResponseDefaultTypeInternal {
  constexpr SetPresetResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetPresetResponseDefaultTypeInternal() {}
  union {
    SetPresetResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetPresetResponseDefaultTypeInternal _SetPresetResponse_default_instance_;
constexpr DiagnosticSettings::DiagnosticSettings(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : diagnostics_()
  , current_diagnostic_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , row_(int64_t{0})
  , ticket_(int64_t{0}){}
struct DiagnosticSettingsDefaultTypeInternal {
  constexpr DiagnosticSettingsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DiagnosticSettingsDefaultTypeInternal() {}
  union {
    DiagnosticSettings _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DiagnosticSettingsDefaultTypeInternal _DiagnosticSettings_default_instance_;
constexpr GetDiagnosticSettingsRequest::GetDiagnosticSettingsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ticket_(int64_t{0}){}
struct GetDiagnosticSettingsRequestDefaultTypeInternal {
  constexpr GetDiagnosticSettingsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetDiagnosticSettingsRequestDefaultTypeInternal() {}
  union {
    GetDiagnosticSettingsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetDiagnosticSettingsRequestDefaultTypeInternal _GetDiagnosticSettingsRequest_default_instance_;
constexpr GetDiagnosticSettingsResponse::GetDiagnosticSettingsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : settings_(nullptr){}
struct GetDiagnosticSettingsResponseDefaultTypeInternal {
  constexpr GetDiagnosticSettingsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetDiagnosticSettingsResponseDefaultTypeInternal() {}
  union {
    GetDiagnosticSettingsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetDiagnosticSettingsResponseDefaultTypeInternal _GetDiagnosticSettingsResponse_default_instance_;
constexpr SetDiagnosticSettingsRequest::SetDiagnosticSettingsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : current_diagnostic_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , row_(int64_t{0}){}
struct SetDiagnosticSettingsRequestDefaultTypeInternal {
  constexpr SetDiagnosticSettingsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetDiagnosticSettingsRequestDefaultTypeInternal() {}
  union {
    SetDiagnosticSettingsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetDiagnosticSettingsRequestDefaultTypeInternal _SetDiagnosticSettingsRequest_default_instance_;
constexpr SetDiagnosticSettingsResponse::SetDiagnosticSettingsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetDiagnosticSettingsResponseDefaultTypeInternal {
  constexpr SetDiagnosticSettingsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetDiagnosticSettingsResponseDefaultTypeInternal() {}
  union {
    SetDiagnosticSettingsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetDiagnosticSettingsResponseDefaultTypeInternal _SetDiagnosticSettingsResponse_default_instance_;
}  // namespace simulator_UI
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[14];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto = nullptr;

const uint32_t TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetVelocityRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetVelocityResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetVelocityResponse, velocity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetVelocityRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetVelocityRequest, velocity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetVelocityResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetPresetsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::PresetSetting, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::PresetSetting, path_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::PresetSetting, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetPresetsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetPresetsResponse, settings_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetPresetRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetPresetRequest, setting_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetPresetResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::DiagnosticSettings, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::DiagnosticSettings, current_diagnostic_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::DiagnosticSettings, diagnostics_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::DiagnosticSettings, row_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::DiagnosticSettings, ticket_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetDiagnosticSettingsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetDiagnosticSettingsRequest, ticket_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetDiagnosticSettingsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::GetDiagnosticSettingsResponse, settings_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetDiagnosticSettingsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetDiagnosticSettingsRequest, current_diagnostic_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetDiagnosticSettingsRequest, row_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator_UI::SetDiagnosticSettingsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::simulator_UI::GetVelocityRequest)},
  { 6, -1, -1, sizeof(::carbon::simulator_UI::GetVelocityResponse)},
  { 13, -1, -1, sizeof(::carbon::simulator_UI::SetVelocityRequest)},
  { 20, -1, -1, sizeof(::carbon::simulator_UI::SetVelocityResponse)},
  { 26, -1, -1, sizeof(::carbon::simulator_UI::GetPresetsRequest)},
  { 32, -1, -1, sizeof(::carbon::simulator_UI::PresetSetting)},
  { 40, -1, -1, sizeof(::carbon::simulator_UI::GetPresetsResponse)},
  { 47, -1, -1, sizeof(::carbon::simulator_UI::SetPresetRequest)},
  { 54, -1, -1, sizeof(::carbon::simulator_UI::SetPresetResponse)},
  { 60, -1, -1, sizeof(::carbon::simulator_UI::DiagnosticSettings)},
  { 70, -1, -1, sizeof(::carbon::simulator_UI::GetDiagnosticSettingsRequest)},
  { 77, -1, -1, sizeof(::carbon::simulator_UI::GetDiagnosticSettingsResponse)},
  { 84, -1, -1, sizeof(::carbon::simulator_UI::SetDiagnosticSettingsRequest)},
  { 92, -1, -1, sizeof(::carbon::simulator_UI::SetDiagnosticSettingsResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_GetVelocityRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_GetVelocityResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_SetVelocityRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_SetVelocityResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_GetPresetsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_PresetSetting_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_GetPresetsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_SetPresetRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_SetPresetResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_DiagnosticSettings_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_GetDiagnosticSettingsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_GetDiagnosticSettingsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_SetDiagnosticSettingsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator_UI::_SetDiagnosticSettingsResponse_default_instance_),
};

const char descriptor_table_protodef_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n3golang/simulator/hardware/proto/sim_UI"
  "/sim_UI.proto\022\023carbon.simulator_UI\"\024\n\022Ge"
  "tVelocityRequest\"\'\n\023GetVelocityResponse\022"
  "\020\n\010velocity\030\001 \001(\002\"&\n\022SetVelocityRequest\022"
  "\020\n\010velocity\030\001 \001(\002\"\025\n\023SetVelocityResponse"
  "\"\023\n\021GetPresetsRequest\",\n\rPresetSetting\022\014"
  "\n\004path\030\001 \003(\t\022\r\n\005value\030\002 \001(\t\"J\n\022GetPreset"
  "sResponse\0224\n\010settings\030\001 \003(\0132\".carbon.sim"
  "ulator_UI.PresetSetting\"G\n\020SetPresetRequ"
  "est\0223\n\007setting\030\001 \001(\0132\".carbon.simulator_"
  "UI.PresetSetting\"\023\n\021SetPresetResponse\"b\n"
  "\022DiagnosticSettings\022\032\n\022current_diagnosti"
  "c\030\001 \001(\t\022\023\n\013diagnostics\030\002 \003(\t\022\013\n\003row\030\003 \001("
  "\003\022\016\n\006ticket\030\005 \001(\003\".\n\034GetDiagnosticSettin"
  "gsRequest\022\016\n\006ticket\030\001 \001(\003\"Z\n\035GetDiagnost"
  "icSettingsResponse\0229\n\010settings\030\001 \001(\0132\'.c"
  "arbon.simulator_UI.DiagnosticSettings\"G\n"
  "\034SetDiagnosticSettingsRequest\022\032\n\022current"
  "_diagnostic\030\001 \001(\t\022\013\n\003row\030\002 \001(\003\"\037\n\035SetDia"
  "gnosticSettingsResponse2\260\005\n\022SimulatorUIS"
  "ervice\022j\n\021GetVelocityStream\022\'.carbon.sim"
  "ulator_UI.GetVelocityRequest\032(.carbon.si"
  "mulator_UI.GetVelocityResponse\"\0000\001\022b\n\013Se"
  "tVelocity\022\'.carbon.simulator_UI.SetVeloc"
  "ityRequest\032(.carbon.simulator_UI.SetVelo"
  "cityResponse\"\000\022f\n\017GetPresetStream\022&.carb"
  "on.simulator_UI.GetPresetsRequest\032\'.carb"
  "on.simulator_UI.GetPresetsResponse\"\0000\001\022\\"
  "\n\tSetPreset\022%.carbon.simulator_UI.SetPre"
  "setRequest\032&.carbon.simulator_UI.SetPres"
  "etResponse\"\000\022\200\001\n\025GetDiagnosticSettings\0221"
  ".carbon.simulator_UI.GetDiagnosticSettin"
  "gsRequest\0322.carbon.simulator_UI.GetDiagn"
  "osticSettingsResponse\"\000\022\200\001\n\025SetDiagnosti"
  "cSettings\0221.carbon.simulator_UI.SetDiagn"
  "osticSettingsRequest\0322.carbon.simulator_"
  "UI.SetDiagnosticSettingsResponse\"\000B\016Z\014pr"
  "oto/sim_UIb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto = {
  false, false, 1498, descriptor_table_protodef_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto, "golang/simulator/hardware/proto/sim_UI/sim_UI.proto", 
  &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once, nullptr, 0, 14,
  schemas, file_default_instances, TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto::offsets,
  file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto, file_level_enum_descriptors_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto, file_level_service_descriptors_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter() {
  return &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto(&descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto);
namespace carbon {
namespace simulator_UI {

// ===================================================================

class GetVelocityRequest::_Internal {
 public:
};

GetVelocityRequest::GetVelocityRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.GetVelocityRequest)
}
GetVelocityRequest::GetVelocityRequest(const GetVelocityRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.GetVelocityRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetVelocityRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetVelocityRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetVelocityRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[0]);
}

// ===================================================================

class GetVelocityResponse::_Internal {
 public:
};

GetVelocityResponse::GetVelocityResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.GetVelocityResponse)
}
GetVelocityResponse::GetVelocityResponse(const GetVelocityResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  velocity_ = from.velocity_;
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.GetVelocityResponse)
}

inline void GetVelocityResponse::SharedCtor() {
velocity_ = 0;
}

GetVelocityResponse::~GetVelocityResponse() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.GetVelocityResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetVelocityResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetVelocityResponse::ArenaDtor(void* object) {
  GetVelocityResponse* _this = reinterpret_cast< GetVelocityResponse* >(object);
  (void)_this;
}
void GetVelocityResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetVelocityResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetVelocityResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.GetVelocityResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  velocity_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetVelocityResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float velocity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetVelocityResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.GetVelocityResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float velocity = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = this->_internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_velocity(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.GetVelocityResponse)
  return target;
}

size_t GetVelocityResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.GetVelocityResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float velocity = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = this->_internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetVelocityResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetVelocityResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetVelocityResponse::GetClassData() const { return &_class_data_; }

void GetVelocityResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetVelocityResponse *>(to)->MergeFrom(
      static_cast<const GetVelocityResponse &>(from));
}


void GetVelocityResponse::MergeFrom(const GetVelocityResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.GetVelocityResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = from._internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    _internal_set_velocity(from._internal_velocity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetVelocityResponse::CopyFrom(const GetVelocityResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.GetVelocityResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetVelocityResponse::IsInitialized() const {
  return true;
}

void GetVelocityResponse::InternalSwap(GetVelocityResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(velocity_, other->velocity_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetVelocityResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[1]);
}

// ===================================================================

class SetVelocityRequest::_Internal {
 public:
};

SetVelocityRequest::SetVelocityRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.SetVelocityRequest)
}
SetVelocityRequest::SetVelocityRequest(const SetVelocityRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  velocity_ = from.velocity_;
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.SetVelocityRequest)
}

inline void SetVelocityRequest::SharedCtor() {
velocity_ = 0;
}

SetVelocityRequest::~SetVelocityRequest() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.SetVelocityRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetVelocityRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetVelocityRequest::ArenaDtor(void* object) {
  SetVelocityRequest* _this = reinterpret_cast< SetVelocityRequest* >(object);
  (void)_this;
}
void SetVelocityRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetVelocityRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetVelocityRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.SetVelocityRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  velocity_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetVelocityRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float velocity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetVelocityRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.SetVelocityRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float velocity = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = this->_internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_velocity(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.SetVelocityRequest)
  return target;
}

size_t SetVelocityRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.SetVelocityRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float velocity = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = this->_internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetVelocityRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetVelocityRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetVelocityRequest::GetClassData() const { return &_class_data_; }

void SetVelocityRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetVelocityRequest *>(to)->MergeFrom(
      static_cast<const SetVelocityRequest &>(from));
}


void SetVelocityRequest::MergeFrom(const SetVelocityRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.SetVelocityRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = from._internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    _internal_set_velocity(from._internal_velocity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetVelocityRequest::CopyFrom(const SetVelocityRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.SetVelocityRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetVelocityRequest::IsInitialized() const {
  return true;
}

void SetVelocityRequest::InternalSwap(SetVelocityRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(velocity_, other->velocity_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetVelocityRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[2]);
}

// ===================================================================

class SetVelocityResponse::_Internal {
 public:
};

SetVelocityResponse::SetVelocityResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.SetVelocityResponse)
}
SetVelocityResponse::SetVelocityResponse(const SetVelocityResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.SetVelocityResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetVelocityResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetVelocityResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetVelocityResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[3]);
}

// ===================================================================

class GetPresetsRequest::_Internal {
 public:
};

GetPresetsRequest::GetPresetsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.GetPresetsRequest)
}
GetPresetsRequest::GetPresetsRequest(const GetPresetsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.GetPresetsRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPresetsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPresetsRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetPresetsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[4]);
}

// ===================================================================

class PresetSetting::_Internal {
 public:
};

PresetSetting::PresetSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  path_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.PresetSetting)
}
PresetSetting::PresetSetting(const PresetSetting& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      path_(from.path_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_value().empty()) {
    value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_value(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.PresetSetting)
}

inline void PresetSetting::SharedCtor() {
value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

PresetSetting::~PresetSetting() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.PresetSetting)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PresetSetting::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PresetSetting::ArenaDtor(void* object) {
  PresetSetting* _this = reinterpret_cast< PresetSetting* >(object);
  (void)_this;
}
void PresetSetting::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PresetSetting::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PresetSetting::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.PresetSetting)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  path_.Clear();
  value_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PresetSetting::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_path();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.simulator_UI.PresetSetting.path"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.simulator_UI.PresetSetting.value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PresetSetting::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.PresetSetting)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string path = 1;
  for (int i = 0, n = this->_internal_path_size(); i < n; i++) {
    const auto& s = this->_internal_path(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.simulator_UI.PresetSetting.path");
    target = stream->WriteString(1, s, target);
  }

  // string value = 2;
  if (!this->_internal_value().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_value().data(), static_cast<int>(this->_internal_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.simulator_UI.PresetSetting.value");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.PresetSetting)
  return target;
}

size_t PresetSetting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.PresetSetting)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string path = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(path_.size());
  for (int i = 0, n = path_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      path_.Get(i));
  }

  // string value = 2;
  if (!this->_internal_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PresetSetting::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PresetSetting::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PresetSetting::GetClassData() const { return &_class_data_; }

void PresetSetting::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PresetSetting *>(to)->MergeFrom(
      static_cast<const PresetSetting &>(from));
}


void PresetSetting::MergeFrom(const PresetSetting& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.PresetSetting)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  path_.MergeFrom(from.path_);
  if (!from._internal_value().empty()) {
    _internal_set_value(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PresetSetting::CopyFrom(const PresetSetting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.PresetSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PresetSetting::IsInitialized() const {
  return true;
}

void PresetSetting::InternalSwap(PresetSetting* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  path_.InternalSwap(&other->path_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &value_, lhs_arena,
      &other->value_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata PresetSetting::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[5]);
}

// ===================================================================

class GetPresetsResponse::_Internal {
 public:
};

GetPresetsResponse::GetPresetsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  settings_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.GetPresetsResponse)
}
GetPresetsResponse::GetPresetsResponse(const GetPresetsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      settings_(from.settings_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.GetPresetsResponse)
}

inline void GetPresetsResponse::SharedCtor() {
}

GetPresetsResponse::~GetPresetsResponse() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.GetPresetsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPresetsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetPresetsResponse::ArenaDtor(void* object) {
  GetPresetsResponse* _this = reinterpret_cast< GetPresetsResponse* >(object);
  (void)_this;
}
void GetPresetsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPresetsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPresetsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.GetPresetsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  settings_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPresetsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.simulator_UI.PresetSetting settings = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_settings(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPresetsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.GetPresetsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.simulator_UI.PresetSetting settings = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_settings_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_settings(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.GetPresetsResponse)
  return target;
}

size_t GetPresetsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.GetPresetsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.simulator_UI.PresetSetting settings = 1;
  total_size += 1UL * this->_internal_settings_size();
  for (const auto& msg : this->settings_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPresetsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPresetsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPresetsResponse::GetClassData() const { return &_class_data_; }

void GetPresetsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPresetsResponse *>(to)->MergeFrom(
      static_cast<const GetPresetsResponse &>(from));
}


void GetPresetsResponse::MergeFrom(const GetPresetsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.GetPresetsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  settings_.MergeFrom(from.settings_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPresetsResponse::CopyFrom(const GetPresetsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.GetPresetsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPresetsResponse::IsInitialized() const {
  return true;
}

void GetPresetsResponse::InternalSwap(GetPresetsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  settings_.InternalSwap(&other->settings_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPresetsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[6]);
}

// ===================================================================

class SetPresetRequest::_Internal {
 public:
  static const ::carbon::simulator_UI::PresetSetting& setting(const SetPresetRequest* msg);
};

const ::carbon::simulator_UI::PresetSetting&
SetPresetRequest::_Internal::setting(const SetPresetRequest* msg) {
  return *msg->setting_;
}
SetPresetRequest::SetPresetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.SetPresetRequest)
}
SetPresetRequest::SetPresetRequest(const SetPresetRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_setting()) {
    setting_ = new ::carbon::simulator_UI::PresetSetting(*from.setting_);
  } else {
    setting_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.SetPresetRequest)
}

inline void SetPresetRequest::SharedCtor() {
setting_ = nullptr;
}

SetPresetRequest::~SetPresetRequest() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.SetPresetRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetPresetRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete setting_;
}

void SetPresetRequest::ArenaDtor(void* object) {
  SetPresetRequest* _this = reinterpret_cast< SetPresetRequest* >(object);
  (void)_this;
}
void SetPresetRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetPresetRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetPresetRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.SetPresetRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && setting_ != nullptr) {
    delete setting_;
  }
  setting_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetPresetRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.simulator_UI.PresetSetting setting = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_setting(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetPresetRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.SetPresetRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.simulator_UI.PresetSetting setting = 1;
  if (this->_internal_has_setting()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::setting(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.SetPresetRequest)
  return target;
}

size_t SetPresetRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.SetPresetRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.simulator_UI.PresetSetting setting = 1;
  if (this->_internal_has_setting()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *setting_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetPresetRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetPresetRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetPresetRequest::GetClassData() const { return &_class_data_; }

void SetPresetRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetPresetRequest *>(to)->MergeFrom(
      static_cast<const SetPresetRequest &>(from));
}


void SetPresetRequest::MergeFrom(const SetPresetRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.SetPresetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_setting()) {
    _internal_mutable_setting()->::carbon::simulator_UI::PresetSetting::MergeFrom(from._internal_setting());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetPresetRequest::CopyFrom(const SetPresetRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.SetPresetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetPresetRequest::IsInitialized() const {
  return true;
}

void SetPresetRequest::InternalSwap(SetPresetRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(setting_, other->setting_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetPresetRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[7]);
}

// ===================================================================

class SetPresetResponse::_Internal {
 public:
};

SetPresetResponse::SetPresetResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.SetPresetResponse)
}
SetPresetResponse::SetPresetResponse(const SetPresetResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.SetPresetResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetPresetResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetPresetResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetPresetResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[8]);
}

// ===================================================================

class DiagnosticSettings::_Internal {
 public:
};

DiagnosticSettings::DiagnosticSettings(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  diagnostics_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.DiagnosticSettings)
}
DiagnosticSettings::DiagnosticSettings(const DiagnosticSettings& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      diagnostics_(from.diagnostics_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  current_diagnostic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    current_diagnostic_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_current_diagnostic().empty()) {
    current_diagnostic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_current_diagnostic(), 
      GetArenaForAllocation());
  }
  ::memcpy(&row_, &from.row_,
    static_cast<size_t>(reinterpret_cast<char*>(&ticket_) -
    reinterpret_cast<char*>(&row_)) + sizeof(ticket_));
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.DiagnosticSettings)
}

inline void DiagnosticSettings::SharedCtor() {
current_diagnostic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  current_diagnostic_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&row_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ticket_) -
    reinterpret_cast<char*>(&row_)) + sizeof(ticket_));
}

DiagnosticSettings::~DiagnosticSettings() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.DiagnosticSettings)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DiagnosticSettings::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  current_diagnostic_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DiagnosticSettings::ArenaDtor(void* object) {
  DiagnosticSettings* _this = reinterpret_cast< DiagnosticSettings* >(object);
  (void)_this;
}
void DiagnosticSettings::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DiagnosticSettings::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DiagnosticSettings::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.DiagnosticSettings)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  diagnostics_.Clear();
  current_diagnostic_.ClearToEmpty();
  ::memset(&row_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ticket_) -
      reinterpret_cast<char*>(&row_)) + sizeof(ticket_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DiagnosticSettings::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string current_diagnostic = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_current_diagnostic();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.simulator_UI.DiagnosticSettings.current_diagnostic"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string diagnostics = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_diagnostics();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.simulator_UI.DiagnosticSettings.diagnostics"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int64 row = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          row_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 ticket = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          ticket_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DiagnosticSettings::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.DiagnosticSettings)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string current_diagnostic = 1;
  if (!this->_internal_current_diagnostic().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_current_diagnostic().data(), static_cast<int>(this->_internal_current_diagnostic().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.simulator_UI.DiagnosticSettings.current_diagnostic");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_current_diagnostic(), target);
  }

  // repeated string diagnostics = 2;
  for (int i = 0, n = this->_internal_diagnostics_size(); i < n; i++) {
    const auto& s = this->_internal_diagnostics(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.simulator_UI.DiagnosticSettings.diagnostics");
    target = stream->WriteString(2, s, target);
  }

  // int64 row = 3;
  if (this->_internal_row() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_row(), target);
  }

  // int64 ticket = 5;
  if (this->_internal_ticket() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(5, this->_internal_ticket(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.DiagnosticSettings)
  return target;
}

size_t DiagnosticSettings::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.DiagnosticSettings)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string diagnostics = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(diagnostics_.size());
  for (int i = 0, n = diagnostics_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      diagnostics_.Get(i));
  }

  // string current_diagnostic = 1;
  if (!this->_internal_current_diagnostic().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_current_diagnostic());
  }

  // int64 row = 3;
  if (this->_internal_row() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_row());
  }

  // int64 ticket = 5;
  if (this->_internal_ticket() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_ticket());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DiagnosticSettings::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DiagnosticSettings::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DiagnosticSettings::GetClassData() const { return &_class_data_; }

void DiagnosticSettings::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DiagnosticSettings *>(to)->MergeFrom(
      static_cast<const DiagnosticSettings &>(from));
}


void DiagnosticSettings::MergeFrom(const DiagnosticSettings& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.DiagnosticSettings)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  diagnostics_.MergeFrom(from.diagnostics_);
  if (!from._internal_current_diagnostic().empty()) {
    _internal_set_current_diagnostic(from._internal_current_diagnostic());
  }
  if (from._internal_row() != 0) {
    _internal_set_row(from._internal_row());
  }
  if (from._internal_ticket() != 0) {
    _internal_set_ticket(from._internal_ticket());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DiagnosticSettings::CopyFrom(const DiagnosticSettings& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.DiagnosticSettings)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DiagnosticSettings::IsInitialized() const {
  return true;
}

void DiagnosticSettings::InternalSwap(DiagnosticSettings* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  diagnostics_.InternalSwap(&other->diagnostics_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &current_diagnostic_, lhs_arena,
      &other->current_diagnostic_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DiagnosticSettings, ticket_)
      + sizeof(DiagnosticSettings::ticket_)
      - PROTOBUF_FIELD_OFFSET(DiagnosticSettings, row_)>(
          reinterpret_cast<char*>(&row_),
          reinterpret_cast<char*>(&other->row_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DiagnosticSettings::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[9]);
}

// ===================================================================

class GetDiagnosticSettingsRequest::_Internal {
 public:
};

GetDiagnosticSettingsRequest::GetDiagnosticSettingsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.GetDiagnosticSettingsRequest)
}
GetDiagnosticSettingsRequest::GetDiagnosticSettingsRequest(const GetDiagnosticSettingsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ticket_ = from.ticket_;
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.GetDiagnosticSettingsRequest)
}

inline void GetDiagnosticSettingsRequest::SharedCtor() {
ticket_ = int64_t{0};
}

GetDiagnosticSettingsRequest::~GetDiagnosticSettingsRequest() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.GetDiagnosticSettingsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetDiagnosticSettingsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetDiagnosticSettingsRequest::ArenaDtor(void* object) {
  GetDiagnosticSettingsRequest* _this = reinterpret_cast< GetDiagnosticSettingsRequest* >(object);
  (void)_this;
}
void GetDiagnosticSettingsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetDiagnosticSettingsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetDiagnosticSettingsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.GetDiagnosticSettingsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ticket_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetDiagnosticSettingsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 ticket = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          ticket_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetDiagnosticSettingsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.GetDiagnosticSettingsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 ticket = 1;
  if (this->_internal_ticket() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_ticket(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.GetDiagnosticSettingsRequest)
  return target;
}

size_t GetDiagnosticSettingsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.GetDiagnosticSettingsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 ticket = 1;
  if (this->_internal_ticket() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_ticket());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetDiagnosticSettingsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetDiagnosticSettingsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetDiagnosticSettingsRequest::GetClassData() const { return &_class_data_; }

void GetDiagnosticSettingsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetDiagnosticSettingsRequest *>(to)->MergeFrom(
      static_cast<const GetDiagnosticSettingsRequest &>(from));
}


void GetDiagnosticSettingsRequest::MergeFrom(const GetDiagnosticSettingsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.GetDiagnosticSettingsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_ticket() != 0) {
    _internal_set_ticket(from._internal_ticket());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetDiagnosticSettingsRequest::CopyFrom(const GetDiagnosticSettingsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.GetDiagnosticSettingsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetDiagnosticSettingsRequest::IsInitialized() const {
  return true;
}

void GetDiagnosticSettingsRequest::InternalSwap(GetDiagnosticSettingsRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ticket_, other->ticket_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetDiagnosticSettingsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[10]);
}

// ===================================================================

class GetDiagnosticSettingsResponse::_Internal {
 public:
  static const ::carbon::simulator_UI::DiagnosticSettings& settings(const GetDiagnosticSettingsResponse* msg);
};

const ::carbon::simulator_UI::DiagnosticSettings&
GetDiagnosticSettingsResponse::_Internal::settings(const GetDiagnosticSettingsResponse* msg) {
  return *msg->settings_;
}
GetDiagnosticSettingsResponse::GetDiagnosticSettingsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.GetDiagnosticSettingsResponse)
}
GetDiagnosticSettingsResponse::GetDiagnosticSettingsResponse(const GetDiagnosticSettingsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_settings()) {
    settings_ = new ::carbon::simulator_UI::DiagnosticSettings(*from.settings_);
  } else {
    settings_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.GetDiagnosticSettingsResponse)
}

inline void GetDiagnosticSettingsResponse::SharedCtor() {
settings_ = nullptr;
}

GetDiagnosticSettingsResponse::~GetDiagnosticSettingsResponse() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.GetDiagnosticSettingsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetDiagnosticSettingsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete settings_;
}

void GetDiagnosticSettingsResponse::ArenaDtor(void* object) {
  GetDiagnosticSettingsResponse* _this = reinterpret_cast< GetDiagnosticSettingsResponse* >(object);
  (void)_this;
}
void GetDiagnosticSettingsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetDiagnosticSettingsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetDiagnosticSettingsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.GetDiagnosticSettingsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && settings_ != nullptr) {
    delete settings_;
  }
  settings_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetDiagnosticSettingsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.simulator_UI.DiagnosticSettings settings = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_settings(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetDiagnosticSettingsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.GetDiagnosticSettingsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.simulator_UI.DiagnosticSettings settings = 1;
  if (this->_internal_has_settings()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::settings(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.GetDiagnosticSettingsResponse)
  return target;
}

size_t GetDiagnosticSettingsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.GetDiagnosticSettingsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.simulator_UI.DiagnosticSettings settings = 1;
  if (this->_internal_has_settings()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *settings_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetDiagnosticSettingsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetDiagnosticSettingsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetDiagnosticSettingsResponse::GetClassData() const { return &_class_data_; }

void GetDiagnosticSettingsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetDiagnosticSettingsResponse *>(to)->MergeFrom(
      static_cast<const GetDiagnosticSettingsResponse &>(from));
}


void GetDiagnosticSettingsResponse::MergeFrom(const GetDiagnosticSettingsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.GetDiagnosticSettingsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_settings()) {
    _internal_mutable_settings()->::carbon::simulator_UI::DiagnosticSettings::MergeFrom(from._internal_settings());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetDiagnosticSettingsResponse::CopyFrom(const GetDiagnosticSettingsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.GetDiagnosticSettingsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetDiagnosticSettingsResponse::IsInitialized() const {
  return true;
}

void GetDiagnosticSettingsResponse::InternalSwap(GetDiagnosticSettingsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(settings_, other->settings_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetDiagnosticSettingsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[11]);
}

// ===================================================================

class SetDiagnosticSettingsRequest::_Internal {
 public:
};

SetDiagnosticSettingsRequest::SetDiagnosticSettingsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.SetDiagnosticSettingsRequest)
}
SetDiagnosticSettingsRequest::SetDiagnosticSettingsRequest(const SetDiagnosticSettingsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  current_diagnostic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    current_diagnostic_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_current_diagnostic().empty()) {
    current_diagnostic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_current_diagnostic(), 
      GetArenaForAllocation());
  }
  row_ = from.row_;
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.SetDiagnosticSettingsRequest)
}

inline void SetDiagnosticSettingsRequest::SharedCtor() {
current_diagnostic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  current_diagnostic_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
row_ = int64_t{0};
}

SetDiagnosticSettingsRequest::~SetDiagnosticSettingsRequest() {
  // @@protoc_insertion_point(destructor:carbon.simulator_UI.SetDiagnosticSettingsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetDiagnosticSettingsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  current_diagnostic_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetDiagnosticSettingsRequest::ArenaDtor(void* object) {
  SetDiagnosticSettingsRequest* _this = reinterpret_cast< SetDiagnosticSettingsRequest* >(object);
  (void)_this;
}
void SetDiagnosticSettingsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetDiagnosticSettingsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetDiagnosticSettingsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator_UI.SetDiagnosticSettingsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  current_diagnostic_.ClearToEmpty();
  row_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetDiagnosticSettingsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string current_diagnostic = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_current_diagnostic();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.simulator_UI.SetDiagnosticSettingsRequest.current_diagnostic"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 row = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          row_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetDiagnosticSettingsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator_UI.SetDiagnosticSettingsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string current_diagnostic = 1;
  if (!this->_internal_current_diagnostic().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_current_diagnostic().data(), static_cast<int>(this->_internal_current_diagnostic().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.simulator_UI.SetDiagnosticSettingsRequest.current_diagnostic");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_current_diagnostic(), target);
  }

  // int64 row = 2;
  if (this->_internal_row() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_row(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator_UI.SetDiagnosticSettingsRequest)
  return target;
}

size_t SetDiagnosticSettingsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator_UI.SetDiagnosticSettingsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string current_diagnostic = 1;
  if (!this->_internal_current_diagnostic().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_current_diagnostic());
  }

  // int64 row = 2;
  if (this->_internal_row() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_row());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetDiagnosticSettingsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetDiagnosticSettingsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetDiagnosticSettingsRequest::GetClassData() const { return &_class_data_; }

void SetDiagnosticSettingsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetDiagnosticSettingsRequest *>(to)->MergeFrom(
      static_cast<const SetDiagnosticSettingsRequest &>(from));
}


void SetDiagnosticSettingsRequest::MergeFrom(const SetDiagnosticSettingsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator_UI.SetDiagnosticSettingsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_current_diagnostic().empty()) {
    _internal_set_current_diagnostic(from._internal_current_diagnostic());
  }
  if (from._internal_row() != 0) {
    _internal_set_row(from._internal_row());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetDiagnosticSettingsRequest::CopyFrom(const SetDiagnosticSettingsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator_UI.SetDiagnosticSettingsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetDiagnosticSettingsRequest::IsInitialized() const {
  return true;
}

void SetDiagnosticSettingsRequest::InternalSwap(SetDiagnosticSettingsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &current_diagnostic_, lhs_arena,
      &other->current_diagnostic_, rhs_arena
  );
  swap(row_, other->row_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetDiagnosticSettingsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[12]);
}

// ===================================================================

class SetDiagnosticSettingsResponse::_Internal {
 public:
};

SetDiagnosticSettingsResponse::SetDiagnosticSettingsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.simulator_UI.SetDiagnosticSettingsResponse)
}
SetDiagnosticSettingsResponse::SetDiagnosticSettingsResponse(const SetDiagnosticSettingsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.simulator_UI.SetDiagnosticSettingsResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetDiagnosticSettingsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetDiagnosticSettingsResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetDiagnosticSettingsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto[13]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace simulator_UI
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::GetVelocityRequest* Arena::CreateMaybeMessage< ::carbon::simulator_UI::GetVelocityRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::GetVelocityRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::GetVelocityResponse* Arena::CreateMaybeMessage< ::carbon::simulator_UI::GetVelocityResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::GetVelocityResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::SetVelocityRequest* Arena::CreateMaybeMessage< ::carbon::simulator_UI::SetVelocityRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::SetVelocityRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::SetVelocityResponse* Arena::CreateMaybeMessage< ::carbon::simulator_UI::SetVelocityResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::SetVelocityResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::GetPresetsRequest* Arena::CreateMaybeMessage< ::carbon::simulator_UI::GetPresetsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::GetPresetsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::PresetSetting* Arena::CreateMaybeMessage< ::carbon::simulator_UI::PresetSetting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::PresetSetting >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::GetPresetsResponse* Arena::CreateMaybeMessage< ::carbon::simulator_UI::GetPresetsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::GetPresetsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::SetPresetRequest* Arena::CreateMaybeMessage< ::carbon::simulator_UI::SetPresetRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::SetPresetRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::SetPresetResponse* Arena::CreateMaybeMessage< ::carbon::simulator_UI::SetPresetResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::SetPresetResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::DiagnosticSettings* Arena::CreateMaybeMessage< ::carbon::simulator_UI::DiagnosticSettings >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::DiagnosticSettings >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::GetDiagnosticSettingsRequest* Arena::CreateMaybeMessage< ::carbon::simulator_UI::GetDiagnosticSettingsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::GetDiagnosticSettingsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::GetDiagnosticSettingsResponse* Arena::CreateMaybeMessage< ::carbon::simulator_UI::GetDiagnosticSettingsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::GetDiagnosticSettingsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::SetDiagnosticSettingsRequest* Arena::CreateMaybeMessage< ::carbon::simulator_UI::SetDiagnosticSettingsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::SetDiagnosticSettingsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator_UI::SetDiagnosticSettingsResponse* Arena::CreateMaybeMessage< ::carbon::simulator_UI::SetDiagnosticSettingsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator_UI::SetDiagnosticSettingsResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
