syntax = "proto3";

package carbon.simulator_UI;
option go_package = "proto/sim_UI";

message GetVelocityRequest {}

message GetVelocityResponse {
    float velocity = 1;
}

message SetVelocityRequest {
    float velocity = 1;
}

message SetVelocityResponse {}

message GetPresetsRequest {}

// [parents..., leaf], value
message PresetSetting {
    repeated string path = 1;
    string value = 2;
}

// [Setting, Setting, Setting...]
message GetPresetsResponse {
    repeated PresetSetting settings = 1;
}

message SetPresetRequest {
    PresetSetting setting = 1;
}

message SetPresetResponse {}

message DiagnosticSettings {
    string current_diagnostic = 1;
    repeated string diagnostics = 2;
    int64 row = 3;
    int64 ticket = 5;
}

message GetDiagnosticSettingsRequest {
    int64 ticket = 1;
}

message GetDiagnosticSettingsResponse {
    DiagnosticSettings settings = 1;
}

message SetDiagnosticSettingsRequest {
    string current_diagnostic = 1;
    int64 row = 2;
}

message SetDiagnosticSettingsResponse {}

service SimulatorUIService {
    rpc GetVelocityStream(GetVelocityRequest) returns (stream GetVelocityResponse) {};
    rpc SetVelocity(SetVelocityRequest) returns (SetVelocityResponse) {};
    rpc GetPresetStream(GetPresetsRequest) returns (stream GetPresetsResponse) {};
    rpc SetPreset(SetPresetRequest) returns (SetPresetResponse) {};
    rpc GetDiagnosticSettings(GetDiagnosticSettingsRequest) returns (GetDiagnosticSettingsResponse) {};
    rpc SetDiagnosticSettings(SetDiagnosticSettingsRequest) returns (SetDiagnosticSettingsResponse) {};
}


