from lib.common.tasks.manager import get_event_loop_by_name  # isort:skip

import argparse
import asyncio
import sys
import time
from contextlib import AbstractAsyncContextManager, AsyncExitStack
from typing import Any, List, Optional

from prometheus_client import start_http_server

from config.client.cpp.config_client_python import ConfigTree, get_computer_config_prefix, get_global_config_subscriber
from lib.cloud.auth import AuthConf, TokenStore
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.config.accessor import ConfigAccessor, TypedConfigAccessor
from lib.common.devices.device_configuration import get_devices_for_service, get_skip_list
from lib.common.devices.registry import DeviceRegistry
from lib.common.generation import GENERATION, generation, is_rtc
from lib.common.logging import get_logger, init_log
from lib.common.redis_client import RedisClient, wait_for_redis
from lib.common.role import is_simulator
from lib.common.units.speed import Speed
from lib.rtc.data_provider.data_bus_client import DATA_BUS
from lib.rtc.messaging.message import Message
from lib.rtc.tractor_pose import init_pose
from tractor_ctl.atim_server import ATIM_IMP_SAFETY_BYPASSED, AtimController
from tractor_ctl.autonomy.autonomous_controller import AutonomousController
from tractor_ctl.autonomy.autonomy import SteeringAssistAlgo
from tractor_ctl.autonomy.furrow_follow_estimator import FurrowFollowEstimator
from tractor_ctl.autonomy.gps_steering_estimator import GpsSteeringEstimator
from tractor_ctl.autonomy.steering_assist import SteeringEstimatorAlgo
from tractor_ctl.connection_manager import ConnectionManager
from tractor_ctl.constants import DATA_CHANNEL, SERVICE_NAME
from tractor_ctl.event_bus import ON_PEER_CONNECT, TRACTOR_EVENT_BUS
from tractor_ctl.grpc.server import GrpcServer
from tractor_ctl.heading_handler import HeadingHandler
from tractor_ctl.messages import REQ_RESP_MAP, ControlState, MessageType
from tractor_ctl.path_handler import PathHandler
from tractor_ctl.state.ff_stateful_emitter import FFStatefulEmitter
from tractor_ctl.tractor_board import TractorBoard
from tractor_ctl.tractor_if import TractorIF
from tractor_ctl.tractor_keeper import TractorKeeper
from tractor_ctl.tractor_state_provider import TractorStateProvider
from tractor_ctl.vstop import VStop

LOG = get_logger(SERVICE_NAME)


class TractorController:
    def __init__(self, registry: DeviceRegistry, tree: ConfigTree, stop_event: asyncio.Event) -> None:
        self._registry = registry
        self._tree = tree
        self._stop_event = stop_event
        self._loop = asyncio.get_event_loop()
        self._hb_timeout = self._tree.get_node("hb_timeout").get_int_value()
        self._tasks: List[asyncio.Future[Any]] = []
        self._cm: List[AbstractAsyncContextManager[Any]] = []
        self._state_providers: List[TractorStateProvider] = []
        self._path_handler: Optional[PathHandler] = None
        self._tractor: Optional[TractorIF] = None
        self._atim: Optional[AtimController] = None
        self._ts = TokenStore(AuthConf())
        TRACTOR_EVENT_BUS.add_listener(ON_PEER_CONNECT, self._broadcast)
        TRACTOR_EVENT_BUS.add_listener("BROADCAST_STATE", self._broadcast)

    async def get_states(self) -> Optional[Message]:
        if not self._state_providers:
            return None
        states = ControlState()
        await asyncio.gather(*[provider.fill_states(states) for provider in self._state_providers])
        return Message.build(MessageType.CONTROL_STATE, states)

    async def _broadcast(self) -> None:
        states = await self.get_states()
        if states is not None:
            await DATA_BUS.broadcast_msg(states)

    async def _cancel(self) -> None:
        for t in self._tasks:
            t.cancel()
        for t in self._tasks:
            try:
                await t
            except asyncio.CancelledError:
                pass

    async def init(self) -> None:
        # self._tasks.append(self._loop.create_task(asyncio_watcher(100, 10)))
        await init_pose()
        self._path_handler = await PathHandler.build()

        self._autonomous_ctl = AutonomousController(
            {
                "Implement Safety": FFStatefulEmitter(
                    ATIM_IMP_SAFETY_BYPASSED, ConfigAccessor(bool, self._tree.get_node("features/implement_safety"))
                )
            }
        )
        self._cm.append(self._autonomous_ctl)
        self._cm.append(await ConnectionManager.build(self._tree, self._stop_event, self._autonomous_ctl))
        self._state_providers.append(self._autonomous_ctl)
        tractor_board = await TractorBoard.build(self._tree, self._registry, self._autonomous_ctl)
        self._atim = AtimController(tractor_board, self._tree)
        self._tractor = tractor_board
        self._state_providers.append(tractor_board)
        self._state_providers.append(self._atim)
        self._cm.append(tractor_board)
        vstop = await VStop.build(self._tree.get_node("vstop"), self._ts)
        self._state_providers.append(vstop)
        self._cm.append(vstop)
        if (
            get_global_config_subscriber()
            .get_config_node("common", "feature_flags/boundary_checker_feature")
            .get_bool_value()
        ):
            tk = await TractorKeeper.build(self._tree.get_node("boundary_checker"))
            self._cm.append(tk)
            self._state_providers.append(tk)
        else:
            LOG.warning("Running without boundary checking, this is not safe.")
        autonomous_algos_tree = self._tree.get_node("autonomous_algos")
        min_ctl_speed = TypedConfigAccessor(
            float, autonomous_algos_tree.get_node("min_steering_control_speed"), Speed.from_mph
        )
        await self._autonomous_ctl.ainit(tractor_board, self._atim, self._ts, autonomous_algos_tree, min_ctl_speed)
        gps_estimator = await GpsSteeringEstimator.build(
            autonomous_algos_tree.get_node("gps_path_follow"), self._path_handler, tractor_board
        )
        gps_heading_estimator = await GpsSteeringEstimator.build(
            autonomous_algos_tree.get_node("gps_path_follow"), await HeadingHandler.build(), tractor_board
        )

        ff_estimator = FurrowFollowEstimator(
            autonomous_algos_tree.get_node("furrow_following"), await RedisClient.build(), tractor_board
        )

        self._autonomous_ctl.add_assist(
            SteeringEstimatorAlgo(tractor_board, SteeringAssistAlgo.GPS_PATH, gps_estimator, min_ctl_speed)
        )
        self._autonomous_ctl.add_assist(
            SteeringEstimatorAlgo(tractor_board, SteeringAssistAlgo.CV_FURROW_FOLLOW, ff_estimator, min_ctl_speed)
        )
        self._autonomous_ctl.add_assist(
            SteeringEstimatorAlgo(tractor_board, SteeringAssistAlgo.GPS_HEADING, gps_heading_estimator, min_ctl_speed)
        )

        DATA_BUS.on_connect(self.get_states)

    async def run(self) -> None:
        assert self._atim is not None
        async with AsyncExitStack() as stack:
            for cm in self._cm:
                await stack.enter_async_context(cm)
            stack.push_async_callback(self._cancel)
            await self._atim.run(self._stop_event)

    @property
    def tractor(self) -> TractorIF:
        assert self._tractor is not None
        return self._tractor


async def asyncio_watcher(watch_interval_ms: int, tolerance_ms: int) -> None:
    watch_interval_s = watch_interval_ms / 1000
    tolerance_s = tolerance_ms / 1000
    while True:
        start_time = time.monotonic()
        await asyncio.sleep(watch_interval_s)
        end_time = time.monotonic()
        if end_time - start_time > watch_interval_s + tolerance_s:
            LOG.warning(f"Asyncio Loop may be overloaded, delay: {(end_time - start_time - watch_interval_s) * 1000}ms")


async def main() -> None:
    async def inner() -> None:
        parser = argparse.ArgumentParser(description="WebSocket Tractor Control")
        parser.add_argument("--ws-port", help="Port to start server", type=int, default=8765)
        parser.add_argument("--metrics-port", help="Port to start metrics server", type=int, default=62011)
        args = parser.parse_args()
        if not is_rtc() and not is_simulator():
            LOG.error("Tractor ctl only runs on RTC generation devices, or simulators emulating rtc")
            return
        start_http_server(args.metrics_port)

        registry = DeviceRegistry()
        config_subscriber = get_global_config_subscriber()
        gen = generation()
        if is_rtc():
            config_subscriber.add_config_tree(
                SERVICE_NAME, f"{get_computer_config_prefix()}/{SERVICE_NAME}", f"services/{SERVICE_NAME}.yaml"
            )
            config_subscriber.add_config_tree("common", "common", "services/rtc_common.yaml")
        else:
            LOG.info("Non RTC hardware pretending to be RTC")
            gen = GENERATION.RTC  # hack for simulators
            config_subscriber.add_config_tree(SERVICE_NAME, f"rtc/{SERVICE_NAME}", f"services/{SERVICE_NAME}.yaml")
            config_subscriber.add_config_tree("common", "common", "services/common.yaml")
        config_subscriber.start()
        await asyncio.get_event_loop().run_in_executor(None, lambda: config_subscriber.wait_until_ready())
        service_tree = config_subscriber.get_config_node(SERVICE_NAME, "")
        common_tree = config_subscriber.get_config_node("common", "")
        device_conf = service_tree.get_node("device_overrides")

        await registry.boot_devices_from_iterable(
            get_devices_for_service(SERVICE_NAME, device_conf, common_tree, gen=gen),
            skip_list=get_skip_list(device_conf),
        )

        stop_event = await bot_stop_handler.get_stop_event()
        controller = TractorController(registry, service_tree, stop_event)
        await controller.init()
        grpc_server = GrpcServer(controller.tractor, controller.get_states)
        LOG.info("serving ...")
        await DATA_BUS.start(DATA_CHANNEL, REQ_RESP_MAP)
        await grpc_server.start()
        await controller.run()

    await wait_for_redis()
    try:
        await inner()
    except Exception as ex:
        bot_stop_handler.exit_with_exception(ex)


if __name__ == "__main__":
    init_log(level="INFO", logfile=f"{SERVICE_NAME}.log")
    asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name())
    bot_stop_handler.ready_for_termination_event.wait()
    sys.exit(bot_stop_handler.error_code)
