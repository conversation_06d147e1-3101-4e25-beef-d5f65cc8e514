package simulator_controller

import (
	"sync"
	"sync/atomic"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/cameras"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/generate"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/gps"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/nofx"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/pulczar"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/safety"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/simulation"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/supervisory"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/types"
	"github.com/sirupsen/logrus"
)

type SimulatorState struct {
	lock     sync.RWMutex // lock for Replayer and Field members
	Replayer *simulation.RecordingReplayer
	Field    *generate.Field

	Nofx             *nofx.NofxBoardSim
	Gps              *gps.GPSBoardSim
	SimSpace         *cameras.SimulationSpace
	Pulczars         []*pulczar.PulczarBoardSim
	ConfigTree       *config.ConfigTree
	configSubscriber *config.ConfigSubscriber
	SupervisorySim   *supervisory.SupervisoryBoardSim
	SafetySim        *safety.SafetyBoardSim
	Mode             atomic.Value
}

func CreateSimulators(config *config.ConfigSubscriber, configClient *config.ConfigClient) (*SimulatorState, error) {
	var field *generate.Field
	var mode types.SimulatorMode
	var recordingReplayer *simulation.RecordingReplayer
	var err error

	simConfig := config.GetConfigNode("simulator", "")
	currentDiagnostic := simConfig.GetNode("replay_diagnostic_name").GetStringValue()
	diagnosticRow := int(simConfig.GetNode("replay_diagnostic_row").GetIntValue())
	generateRandom := simConfig.GetNode("generate_random_predictions").GetBoolValue()

	if generateRandom {
		for _, w := range generate.WeedClasses {
			hasNode := simConfig.GetNode("weeds").GetNode("weed_classes").HasNode(w)
			if !hasNode {
				configClient.AddToList("simulator/weeds/weed_classes", w)
			}
		}
		field = generate.NewField(config)
		mode = types.Generate
	} else {
		mode = types.Replay
		recordingReplayer, err = simulation.NewRecordingReplayer(currentDiagnostic, diagnosticRow)
		if err != nil {
			logrus.WithError(err).Error("Error Creating New Recording Replayer")
			return nil, err
		}
	}
	simulationSpace := cameras.NewSimulationSpace(config, field, recordingReplayer)
	nofx := nofx.NewNofxBoardSim(config, recordingReplayer)
	gps := gps.NewGPSBoardSim(config)
	pulczars := make([]*pulczar.PulczarBoardSim, 10)
	for i := 1; i <= 10; i++ {
		pulczars[i-1] = pulczar.NewPulczarBoardSim(i)
	}

	supervisorySim := supervisory.NewSupervisoryBoardSim(config.GetConfigNode("simulator", "supervisory"))
	safety := safety.NewSafetyBoardSim(config.GetConfigNode("simulator", "safety"))

	simState := &SimulatorState{
		Replayer:         recordingReplayer,
		Nofx:             nofx,
		Gps:              gps,
		SimSpace:         simulationSpace,
		Field:            field,
		ConfigTree:       simConfig,
		configSubscriber: config,
		Pulczars:         pulczars,
		SupervisorySim:   supervisorySim,
		SafetySim:        safety,
	}

	simState.Mode.Store(mode)
	simState.Nofx.SetMode(&simState.Mode)
	simState.SimSpace.SetMode(&simState.Mode)

	if simState.Mode.Load() == types.Replay {
		simState.Replayer.SetMode(&simState.Mode)

	}

	simConfig.RegisterCallback(simState.Reload)

	return simState, err
}

func (simState *SimulatorState) StartSimulators(isRestart bool) {
	simState.lock.Lock()
	defer simState.lock.Unlock()
	logrus.Infof("Starting new simulation in mode %v", simState.Mode.Load())

	simState.Nofx.Start()
	simState.Gps.Start()
	if simState.Mode.Load() == types.Generate {
		simState.Field.Start()
	}
	simState.SimSpace.Start()

	if !isRestart {
		for _, pulczar := range simState.Pulczars {
			go pulczar.Start()
		}
		go simState.SupervisorySim.Start()
		go simState.SafetySim.Start()
	}

	if simState.Mode.Load() == types.Replay {
		go simState.Replayer.Play()
	}
}

func (simState *SimulatorState) Stop() {
	logrus.Info("stopping simulators")
	simState.lock.Lock()
	defer simState.lock.Unlock()

	simState.Nofx.Stop()
	simState.Gps.Stop()
	simState.SimSpace.Stop()

	if simState.Mode.Load() == types.Replay {
		if simState.Replayer != nil {
			simState.Replayer.Stop()
		}
		simState.Replayer = nil
	}

	if simState.Mode.Load() == types.Generate {
		simState.Field.Stop()
		simState.Field = nil
	}
	logrus.Info("replayer stopped")
}

func (simState *SimulatorState) reloadReplayer(diagnosticName string, diagnosticRow int) error {
	var err error
	simState.lock.Lock()
	defer simState.lock.Unlock()
	simState.Replayer, err = simulation.NewRecordingReplayer(diagnosticName, diagnosticRow)
	if err != nil {
		logrus.WithError(err).Error("Error Creating New Recording Replayer on reload")
		return err
	}
	simState.Replayer.SetMode(&simState.Mode)
	simState.Nofx.Reload(simState.Replayer)
	simState.SimSpace.Reload(simState.Replayer, simState.Field)
	logrus.Info("Syncing configs")
	err = simState.Replayer.SyncConfigs()
	if err != nil {
		logrus.Errorf("Replay: could not sync configs, err=%v", err)
		return err
	}
	return nil
}

func (simState *SimulatorState) Reload() {
	var newMode types.SimulatorMode
	generateRandom := simState.ConfigTree.GetNode("generate_random_predictions").GetBoolValue()
	diagnosticName := simState.ConfigTree.GetNode("replay_diagnostic_name").GetStringValue()
	diagnosticRow := int(simState.ConfigTree.GetNode("replay_diagnostic_row").GetIntValue())

	if generateRandom {
		newMode = types.Generate
	} else {
		newMode = types.Replay
		if diagnosticName == "" {
			logrus.Error("Diagnostic Name is empty")
			return
		}
	}
	if !simState.needsReload(newMode, diagnosticName, diagnosticRow) {
		logrus.Infof("Reload replay finished - no changes needed")
		return
	}

	// stop only if reload is needed and inputs are valid
	simState.Stop()
	logrus.Infof("Received change in mode, will restart")

	if newMode != simState.Mode.Load() {
		simState.switchMode(newMode, diagnosticName, diagnosticRow)
	} else if newMode == types.Replay {
		if err := simState.reloadReplayer(diagnosticName, diagnosticRow); err != nil {
			logrus.WithError(err).Error("Error Reloading Replayer")
			return
		}
	}

	simState.StartSimulators(true)
	logrus.Infof("Reload replay finished")
}

func (simState *SimulatorState) ReloadWithParams(diagnosticRow int, diagnosticName string) {
	var newMode types.SimulatorMode
	generateRandom := simState.ConfigTree.GetNode("generate_random_predictions").GetBoolValue()
	if generateRandom {
		newMode = types.Generate
	} else {
		newMode = types.Replay
		if diagnosticName == "" {
			logrus.Error("Diagnostic Name is empty")
			return
		}
	}

	if !simState.needsReload(newMode, diagnosticName, diagnosticRow) {
		logrus.Infof("Reload replay finished - no changes needed")
		return
	}

	// stop only if reload is needed and inputs are valid
	simState.Stop()
	logrus.Infof("Received change in mode, will restart")

	if newMode != simState.Mode.Load() {
		simState.switchMode(newMode, diagnosticName, diagnosticRow)
	} else if newMode == types.Replay {
		if err := simState.reloadReplayer(diagnosticName, diagnosticRow); err != nil {
			logrus.WithError(err).Error("Error Reloading Replayer")
			return
		}
	}

	simState.StartSimulators(true)
	logrus.Infof("Reload replay finished")
}

func (simState *SimulatorState) needsReload(newMode types.SimulatorMode, diagnosticName string, diagnosticRow int) bool {
	simState.lock.RLock()
	defer simState.lock.RUnlock()
	currentMode := simState.Mode.Load()

	if newMode != currentMode {
		return true
	}

	if newMode == types.Replay {
		row, currentDiagnostic, _ := simState.Replayer.GetReplayerDiagnosticState().GetDiagnosticState()
		return diagnosticName != currentDiagnostic || diagnosticRow != row
	}

	return false
}

func (simState *SimulatorState) switchMode(newMode types.SimulatorMode, diagnosticName string, diagnosticRow int) {
	simState.Mode.Store(newMode)

	if newMode == types.Generate {
		simState.lock.Lock()
		defer simState.lock.Unlock()
		if simState.Field == nil {
			simState.Field = generate.NewField(simState.configSubscriber)
		}
		simState.Nofx.Reload(simState.Replayer)
		simState.SimSpace.Reload(simState.Replayer, simState.Field)
	} else {
		if err := simState.reloadReplayer(diagnosticName, diagnosticRow); err != nil {
			logrus.WithError(err).Error("Error Reloading Replayer")
			return
		}
	}
}
