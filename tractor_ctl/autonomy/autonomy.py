from dataclasses import dataclass
from enum import IntEnum

from dataclass_wizard import JSONWizard


class SteeringAssistAlgo(IntEnum):
    NONE = 0
    GPS_PATH = 1
    CV_FURROW_FOLLOW = 2
    GPS_HEADING = 3


class AutonomyMode(IntEnum):
    UNKNOWN = 0
    REMOTE_DRIVER = 1
    TASK_AUTONOMY = 2


@dataclass
class AutonomyMsg(JSONWizard):
    mode: AutonomyMode = AutonomyMode.UNKNOWN
    steering_assist_algo: SteeringAssistAlgo = SteeringAssistAlgo.NONE
