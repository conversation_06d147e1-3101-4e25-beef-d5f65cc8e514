from abc import abstractmethod
from collections import defaultdict
from dataclasses import dataclass, field
from typing import Dict, List

from lib.common.logging import get_logger
from lib.common.redis_client import RedisClient
from metrics.pybind.metrics_python import ConclusionType, DailyTimezone

LOG = get_logger(__name__)


@dataclass
class ConclusionInfo:
    killed: int = 0
    missed: int = 0
    skipped: int = 0
    not_shooting: int = 0
    size: float = 0
    count: int = 0

    def avg_size(self) -> float:
        if self.count == 0:
            return 0
        return self.size / self.count


@dataclass
class WeedConclusionInfo(ConclusionInfo):
    targetable_req_laser_time: int = 0
    targetable_count: int = 0
    untargetable_req_laser_time: int = 0
    untargetable_count: int = 0
    type_counts: Dict[str, int] = field(default_factory=dict)

    def avg_targetable_time(self) -> float:
        if self.targetable_count == 0:
            return 0
        return self.targetable_req_laser_time / self.targetable_count

    def avg_untargetable_time(self) -> float:
        if self.untargetable_count == 0:
            return 0
        return self.untargetable_req_laser_time / self.untargetable_count


@dataclass
class CropConclusionInfo(ConclusionInfo):
    kept: int = 0
    valid_crops: int = 0


class ConclusionChecker:
    def __init__(self) -> None:
        self._redis = RedisClient()

    async def _ainit_(self) -> None:
        await self._redis.connect()

    @abstractmethod
    def _key_fetcher(self) -> str:
        ...

    async def check_weed_counters(self) -> WeedConclusionInfo:
        killed = 0
        missed = 0
        skipped = 0
        not_weeding = 0
        size = 0.0
        count = 0
        type_counts: Dict[str, int] = defaultdict(int)
        targetable_time = 0
        untargetable_time = 0
        targetable_count = 0
        untargetable_count = 0

        key = self._key_fetcher()
        data = await self._redis.hgetall(key)
        armed_prefix = "armed/weed/"
        disarmed_prefix = "disarmed/weed/"
        migrated_prefix = "migrated/weed/"
        killed_types = [ConclusionType.kShot]
        missed_types = [
            ConclusionType.kNotShot,
            ConclusionType.kPartiallyShot,
            ConclusionType.kP2PNotFound,
            ConclusionType.kError,
            ConclusionType.kOutOfRange,
            ConclusionType.kP2PMissingContext,
        ]
        skipped_types = [
            ConclusionType.kOutOfBand,
            ConclusionType.kIntersectsWithNonShootable,
            ConclusionType.kNotTargeted,
            ConclusionType.kUnimportant,
        ]
        killed_fields = set([f"{armed_prefix}{ct.value}" for ct in killed_types])
        skipped_fields = set([f"{armed_prefix}{ct.value}" for ct in skipped_types])
        missed_fields = set([f"{armed_prefix}{ct.value}" for ct in missed_types])
        inactive_fields = set([f"{armed_prefix}{ConclusionType.kNotWeeding.value}"])
        killed_fields.add(f"{migrated_prefix}killed")
        missed_fields.add(f"{migrated_prefix}missed")
        skipped_fields.add(f"{migrated_prefix}skipped")
        inactive_fields.add(f"{migrated_prefix}inactive")

        for i in range(ConclusionType.kConclusionTypeNumber.value):
            if i == ConclusionType.kFlicker.value:
                continue
            inactive_fields.add(f"{disarmed_prefix}{i}")

        size += float(data.get("size/weeds", "0.0"))
        count += int(data.get(f"{migrated_prefix}count", "0"))

        for field_name, value in data.items():
            if field_name in killed_fields:
                killed += int(value)
            elif field_name in missed_fields:
                missed += int(value)
            elif field_name in skipped_fields:
                skipped += int(value)
            elif field_name in inactive_fields:
                not_weeding += int(value)

            if field_name.startswith(armed_prefix) or field_name.startswith(disarmed_prefix):
                if not field_name.endswith(f"/{ConclusionType.kFlicker.value}"):
                    count += int(value)
            if field_name.startswith("type/"):
                type_counts[field_name.split("/")[-1]] += int(value)

        targetable_time += int(data.get("laser_time/targetable", "0"))
        targetable_count += int(data.get("laser_time_count/targetable", "0"))

        untargetable_time += int(data.get("laser_time/untargetable", "0"))
        untargetable_count += int(data.get("laser_time_count/untargetable", "0"))

        return WeedConclusionInfo(
            killed,
            missed,
            skipped,
            not_weeding,
            size,
            count,
            targetable_time,
            targetable_count,
            untargetable_time,
            untargetable_count,
            type_counts,
        )

    async def check_crop_counters(self) -> CropConclusionInfo:
        killed = 0
        missed = 0
        skipped = 0
        not_thinning = 0
        size = 0.0
        count = 0
        kept = 0
        valid_crops = 0

        key = self._key_fetcher()
        data = await self._redis.hgetall(key)
        armed_prefix = "armed/crop/"
        disarmed_prefix = "disarmed/crop/"
        migrated_prefix = "migrated/crop/"
        kept_types = [ConclusionType.kNotTargeted]
        killed_types = [ConclusionType.kShot]
        missed_types = [
            ConclusionType.kNotShot,
            ConclusionType.kPartiallyShot,
            ConclusionType.kP2PNotFound,
            ConclusionType.kError,
            ConclusionType.kOutOfRange,
            ConclusionType.kP2PMissingContext,
            ConclusionType.kMarkedForThinning,
        ]
        skipped_types = [
            ConclusionType.kOutOfBand,
            ConclusionType.kIntersectsWithNonShootable,
            ConclusionType.kUnimportant,
        ]
        kept_fields = set([f"{armed_prefix}{ct.value}" for ct in kept_types])
        killed_fields = set([f"{armed_prefix}{ct.value}" for ct in killed_types])
        skipped_fields = set([f"{armed_prefix}{ct.value}" for ct in skipped_types])
        missed_fields = set([f"{armed_prefix}{ct.value}" for ct in missed_types])
        inactive_fields = set([f"{armed_prefix}{ConclusionType.kNotWeeding.value}"])
        kept_fields.add(f"{migrated_prefix}kept")
        killed_fields.add(f"{migrated_prefix}killed")
        missed_fields.add(f"{migrated_prefix}missed")
        skipped_fields.add(f"{migrated_prefix}skipped")
        inactive_fields.add(f"{migrated_prefix}inactive")
        for i in range(ConclusionType.kConclusionTypeNumber.value):
            if i == ConclusionType.kFlicker.value:
                continue
            inactive_fields.add(f"{disarmed_prefix}{i}")

        size += float(data.get("size/crops", "0.0"))
        count += int(data.get(f"{migrated_prefix}count", "0"))

        for field_name, value in data.items():
            if field_name in kept_fields:
                kept += int(value)
            elif field_name in killed_fields:
                killed += int(value)
            elif field_name in missed_fields:
                missed += int(value)
            elif field_name in skipped_fields:
                skipped += int(value)
            elif field_name in inactive_fields:
                not_thinning += int(value)

            if field_name.startswith(armed_prefix) or field_name.startswith(disarmed_prefix):
                if not field_name.endswith(f"/{ConclusionType.kFlicker.value}"):
                    count += int(value)

        valid_crops += int(data.get("laser_time_count/valid_crops", "0"))

        return CropConclusionInfo(killed, missed, skipped, not_thinning, size, count, kept, valid_crops)


def key_formatter(metric_type: str, metric_id: str) -> str:
    return f"/{metric_type}/{metric_id}/weed_tracking/combined/counts"


class DailyConclusionChecker(ConclusionChecker):
    def __init__(self, dtz: DailyTimezone) -> None:
        super(DailyConclusionChecker, self).__init__()
        self._dtz = dtz

    @staticmethod
    async def build(dtz: DailyTimezone) -> "DailyConclusionChecker":
        checker = DailyConclusionChecker(dtz)
        await checker._ainit_()
        return checker

    def _key_fetcher(self) -> str:
        return key_formatter("daily", self._dtz.get_day())

    async def migrate(self) -> None:
        # Only need this when upgrading versions in the middle of the day, can remove this once all machines have been upgraded to a version running this code
        key = self._key_fetcher()
        if await self._redis.client.hexists(key, "migrated/complete"):
            # This day has already been migrated so no need to do this again
            return
        await self._redis.client.hset(key, "migrated/complete", "1")
        row_keys: List[str] = await self._redis.client.keys(f"{self._dtz.get_day()}/weed_tracking/*/counts")
        for row_key in row_keys:
            row_data = await self._redis.hgetall(row_key)
            for field_name, value in row_data.items():
                if field_name.startswith("type/"):
                    field_name = field_name.replace("type/", "type/weeds_type_count_")
                if field_name.startswith("size/"):
                    await self._redis.client.hincrbyfloat(key, field_name, float(value))
                else:
                    await self._redis.client.hincrby(key, field_name, int(value))


class JobsConclusionChecker(ConclusionChecker):
    def __init__(self) -> None:
        super(JobsConclusionChecker, self).__init__()
        self._active_job = "NONE"

    @staticmethod
    async def build() -> "JobsConclusionChecker":
        checker = JobsConclusionChecker()
        await checker._ainit_()
        return checker

    def _key_fetcher(self) -> str:
        return key_formatter("jobs", self._active_job)

    async def job_change(self, job_id: str, saved_data: Dict[str, str]) -> None:
        self._active_job = job_id
        key = self._key_fetcher()
        if await self._redis.client.hexists(key, "migrated/complete"):
            # This job has already been migrated so no need to do this again
            return
        data: Dict[str, str] = {"migrated/complete": "1"}
        migration_map = {
            "killed_weeds": "weed/killed",
            "missed_weeds": "weed/missed",
            "skipped_weeds": "weed/skipped",
            "not_weeding": "weed/inactive",
            "total_weeds": "weed/count",
            "kept_crops": "crop/kept",
            "thinned_crops": "crop/killed",
            "missed_crops": "crop/missed",
            "skipped_crops": "crop/skipped",
            "not_thinning": "crop/inactive",
            "total_crops": "crop/count",
        }
        for k, nk in migration_map.items():
            if k in saved_data:
                data[f"migrated/{nk}"] = saved_data[k]

        weed_count = int(saved_data.get("total_weeds", "0"))
        crop_count = int(saved_data.get("total_crops", "0"))
        weed_size = float(saved_data.get("avg_weed_size_mm", "0.0")) * weed_count
        crop_size = float(saved_data.get("avg_crop_size_mm", "0.0")) * crop_count

        for k, v in saved_data.items():
            if k.startswith("weeds_type_count_"):
                await self._redis.client.hincrby(key, f"type/{k}", int(v))

        await self._redis.client.hincrbyfloat(key, "size/weeds", weed_size)
        await self._redis.client.hincrbyfloat(key, "size/crops", crop_size)

        if "total_targetable_req_laser_time" in saved_data:
            await self._redis.client.hincrby(
                key, "laser_time/targetable", int(saved_data["total_targetable_req_laser_time"])
            )
        if "targetable_count" in saved_data:
            await self._redis.client.hincrby(key, "laser_time_count/targetable", int(saved_data["targetable_count"]))
        if "total_untargetable_req_laser_time" in saved_data:
            await self._redis.client.hincrby(
                key, "laser_time/untargetable", int(saved_data["total_untargetable_req_laser_time"])
            )
        if "untargetable_count" in saved_data:
            await self._redis.client.hincrby(
                key, "laser_time_count/untargetable", int(saved_data["untargetable_count"])
            )
        if "valid_crops" in saved_data:
            await self._redis.client.hincrby(key, "laser_time_count/valid_crops", int(saved_data["valid_crops"]))

        await self._redis.hmset(key, data)
