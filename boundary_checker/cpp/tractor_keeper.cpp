#include "boundary_checker/cpp/tractor_keeper.hpp"

#include <cmath>

#include <boundary_checker/cpp/farm_data.hpp>
#include <boundary_checker/cpp/tractor_state.hpp>
#include <hw_client_gps_distributor/cpp/gps_distributor.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <proj_utils/cpp/point.hpp>

#include <fmt/format.h>
#include <geodesic.h>
#include <spdlog/spdlog.h>

constexpr int64_t UPDATE_DELTA = 50;
constexpr inline double deg2rad(const double &degrees) { return degrees * M_PI / 180.0; }
constexpr inline double mph2mps(const double &mph) { return mph / 2.237; }
inline int64_t timestamp_ms() {
  return std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
      .count();
}
constexpr double RIGHT_ANGLE_RAD = deg2rad(90);
constexpr double MAX_DIST_SQUARED = std::pow(2500.0, 2.0); // 2.5km in meters

namespace carbon::boundary_checker {
TractorKeeper::TractorKeeper(std::shared_ptr<config::ConfigTree> tree, StateChangeCB change_cb, double wheelbase_meters,
                             const std::vector<proj::Point> &relative_tractor)
    : change_cb_(change_cb), wheelbase_meters_(wheelbase_meters), turn_alpha_rad_(std::atan2(0, wheelbase_meters_)),
      relative_tractor_(relative_tractor), plotter_(std::nullopt, std::nullopt), reload_required_(true),
      enforced_(false), farm_(std::make_shared<FarmData>()), state_(TKS_Running),
      pos_update_(&TractorKeeper::pos_update_loop, this), rule_enforcer_(&TractorKeeper::rule_enforcer_loop, this) {

  auto gps_quality = tree->get_node("gps_quality_buffer");
  gps_quality_buffer_.emplace_back(gps_quality->get_node("none"), 1.0);
  gps_quality_buffer_.emplace_back(gps_quality->get_node("floating"), 0.5);
  gps_quality_buffer_.emplace_back(gps_quality->get_node("fixed"), 0.0);
  gps_update_ = std::thread(&TractorKeeper::gps_update_loop, this);
}
void TractorKeeper::set_farm(std::shared_ptr<FarmData> farm) {
  spdlog::info("Loading new farm");
  std::lock_guard lk(farm_mut_);
  farm_ = farm;
  reload_required_ = true;
}

void TractorKeeper::gps_update_loop() {
  auto &bsh = lib::common::bot::BotStopHandler::get();
  uint64_t prev_ts = 0;
  while (!bsh.is_stopped()) {
    auto resp = gps_distributor::GpsDistributor::get_next(prev_ts, std::chrono::milliseconds(1000));
    if (!resp) {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      continue;
    }
    if (!resp->dual) {
      spdlog::warn("got Invalid GPS data");
      continue;
    }
    prev_ts = resp->timestamp_ms;
    proj::Point gps_pos(resp->longitude, resp->latitude);
    auto local_pos = plotter_.geo_to_rel(gps_pos);
    double dist_squared = std::pow(local_pos.x(), 2) + std::pow(local_pos.y(), 2);
    if (dist_squared > MAX_DIST_SQUARED) {
      spdlog::info("Traveled too far need to reset local coordinates");
      reload_required_ = true;
    }
    double heading = plotter_.azimuth_to_xy_theta(deg2rad(resp->dual->heading_deg.value));
    int carr_soln = resp->fix_flags.carr_soln;
    if (carr_soln < 0 || static_cast<size_t>(carr_soln) >= gps_quality_buffer_.size()) {
      spdlog::warn("Invalid Carrier Phase solution {}", carr_soln);
      carr_soln = 0;
    }
    std::lock_guard lk(pos_data_.mut);
    pos_data_.geo_pos = gps_pos;
    pos_data_.carr_soln = carr_soln;
    pos_data_.gps_update_ms = static_cast<int64_t>(prev_ts);
    pos_data_.heading_xy_rad = heading;
    pos_data_.local_pos = local_pos;
    pos_data_.local_update_ms = prev_ts;
    pos_data_.init_cv.notify_all();
  }
}

void TractorKeeper::pos_update_loop() {
  {
    auto test = [&]() -> bool { return pos_data_.geo_pos.valid_geo(); };
    std::unique_lock lk(pos_data_.mut);
    if (!test()) {
      pos_data_.init_cv.wait(lk, test);
    }
  }
  auto &bsh = lib::common::bot::BotStopHandler::get();
  int64_t delta_time = 0;
  while (!bsh.is_stopped()) {
    if (delta_time < UPDATE_DELTA) {
      std::this_thread::sleep_for(std::chrono::milliseconds(UPDATE_DELTA - delta_time));
    } else {
      spdlog::warn("Boundary checker taking too long to run, last frame took {}ms", delta_time);
    }
    check_and_reload();
    auto start = timestamp_ms();
    int64_t gps_ts = 0;
    {
      std::lock_guard lk(pos_data_.mut);
      gps_ts = pos_data_.gps_update_ms;
    }
    if (start - gps_ts > 5000) {
      std::string msg("Last GPS update is too old, cannot estimate position.");
      spdlog::warn(msg);
      set_state(TKS_UpdateFailed, msg);
      continue;
    }

    auto speed_mps = mph2mps(TractorState::get().speed_mph());
    if (TractorState::get().reverse()) {
      speed_mps *= -1.0;
    }
    auto wa_rad = -TractorState::get().wheel_angle_rad();
    proj::Point cur_pos;
    double cur_heading;
    int carr_soln = 0;
    {
      std::lock_guard lk(pos_data_.mut);
      double xdot = speed_mps * std::cos(pos_data_.heading_xy_rad + turn_alpha_rad_);
      double ydot = speed_mps * std::sin(pos_data_.heading_xy_rad + turn_alpha_rad_);
      double thdot = (speed_mps / wheelbase_meters_) * std::tan(wa_rad);
      double dt = static_cast<double>(start - pos_data_.local_update_ms) / 1000.0; // seconds
      pos_data_.local_pos.set_x(pos_data_.local_pos.x() + (xdot * dt));
      pos_data_.local_pos.set_y(pos_data_.local_pos.y() + (ydot * dt));
      pos_data_.heading_xy_rad += (thdot * dt);
      pos_data_.local_update_ms = start;
      cur_pos = pos_data_.local_pos;
      cur_heading = pos_data_.heading_xy_rad;
      carr_soln = pos_data_.carr_soln;
    }
    cur_heading -= RIGHT_ANGLE_RAD; // Since Y is dir of travel need to rotate
    double cos_heading = std::cos(cur_heading);
    double sin_heading = std::sin(cur_heading);
    auto builder = std::make_shared<geos::geom::CoordinateSequence>();
    builder->reserve(relative_tractor_.size());
    for (const auto &pt : relative_tractor_) {
      builder->add((cos_heading * pt.x() - sin_heading * pt.y()) + cur_pos.x(),
                   (sin_heading * pt.x() + cos_heading * pt.y()) + cur_pos.y());
    }
    bc_.update_robot(builder, gps_quality_buffer_[carr_soln].get_value());
    delta_time = timestamp_ms() - start;
  }
}
bool TractorKeeper::check_and_reload() {
  if (!reload_required_.exchange(false)) {
    return false;
  }
  spdlog::info("Resetting all local coordinates");
  {
    std::lock_guard plk(pos_data_.mut);
    plotter_.set_start(pos_data_.geo_pos);
    pos_data_.local_pos.set_x(0.0);
    pos_data_.local_pos.set_y(0.0);
    pos_data_.local_update_ms = pos_data_.gps_update_ms;
  }
  bc_.clear();
  std::lock_guard lk(farm_mut_);
  if (!farm_) {
    spdlog::warn("No farm data to load into boundary checker");
    return true;
  }
  for (const auto &pair : farm_->zones()) {
    if (pair.second.type == BT_Point) {
      bc_.add_point(pair.first, pair.second.boundary_builder(&plotter_), pair.second.buffer);
    } else if (pair.second.type == BT_LineStr) {
      bc_.add_line_string(pair.first, pair.second.boundary_builder(&plotter_), pair.second.buffer);
    } else if (pair.second.type == BT_Polygon) {
      bc_.add_polygon(pair.first, pair.second.boundary_builder(&plotter_), pair.second.hole_builder(&plotter_),
                      pair.second.buffer);
    } else {
      spdlog::warn("Unknown zone boundary type id={}", pair.first);
    }
  }
  return true;
}
void TractorKeeper::rule_enforcer_loop() {
  auto &bsh = lib::common::bot::BotStopHandler::get();
  std::shared_ptr<BoundaryState> curr_state = nullptr;
  bool rule_violated = false;
  std::string msg;
  while (!bsh.is_stopped()) {
    curr_state = bc_.get_next(curr_state);
    bool violation = false;
    if (enforced_) {
      std::lock_guard lk(farm_mut_);
      auto it = curr_state->inside.find(farm_->farm_zone_id());
      if (it == curr_state->inside.end()) {
        // outside of farm rule violation
        if (!rule_violated) {
          msg = "Tractor is no longer within the farm boundary.";
          spdlog::info(msg);
        }
        violation = true;
      } else {
        for (const auto &id : curr_state->inside) {
          if (farm_->obstacles().find(id) != farm_->obstacles().end()) {
            violation = true;
            break;
          }
        }
        if (!violation) {
          for (const auto &id : curr_state->intersects) {
            if (farm_->obstacles().find(id) != farm_->obstacles().end()) {
              violation = true;
              break;
            }
          }
        }
        if (violation && !rule_violated) {
          msg = "Tractor must remain outside of all obstacles.";
          spdlog::info(msg);
        }
      }
    }
    if (violation != rule_violated) {
      rule_violated = violation;
    }
    set_state(rule_violated ? TKS_RuleViolated : TKS_Running, msg);
  }
}
void TractorKeeper::set_state(TractorKeeperState state, const std::string &msg) {
  if (state_.exchange(state) != state) {
    change_cb_(state, msg);
  }
}
} // namespace carbon::boundary_checker
