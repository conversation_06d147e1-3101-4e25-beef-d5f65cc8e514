import os
import zipfile
from collections import defaultdict
from typing import Any, Dict, List, <PERSON>ple

import boto3
import plotly.graph_objects as go
import streamlit as st

from tools.recorder.deepweed_reader import DeepweedReader
from deeplearning.streamlit.trajectory_viewer import get_zips_from_s3, download_zip_from_s3, unzip, list_files


def get_available_rows_and_predicts(files: List[str]) -> Tuple[List[str], Dict[str, List[str]]]:
    """Scan files to find available rows and predict cameras"""
    rows = set()
    row_predicts = defaultdict(set)

    for file in files:
        if "_deepweed.carbon" in file:
            # Expected format: /path/to/row1/predict2_deepweed.carbon
            parts = file.split("/")
            if len(parts) >= 2:
                row_id = parts[-2]  # e.g., "row1"
                filename = parts[-1]  # e.g., "predict2_deepweed.carbon"
                predict_id = filename.split("_deepweed.carbon")[0]  # e.g., "predict2"

                rows.add(row_id)
                row_predicts[row_id].add(predict_id)

    # Convert sets to sorted lists
    sorted_rows = sorted(list(rows))
    sorted_row_predicts = {row: sorted(list(predicts)) for row, predicts in row_predicts.items()}

    return sorted_rows, sorted_row_predicts


def load_deepweed_file(filepath: str) -> Dict[int, Any]:
    """Load deepweed prediction data from carbon file and create timestamp->frame mapping"""
    reader = DeepweedReader(filepath)
    timestamp_to_frame = {}

    if not reader.is_valid:
        st.error(f"Invalid deepweed file: {filepath}")
        return timestamp_to_frame

    record_count = 0
    record = reader.current()

    while record is not None and not reader.is_done:
        # Each record contains a DeepweedPredictionFrame
        frame = record.frame
        timestamp_ms = frame.timestamp_ms

        # Map timestamp to the frame
        timestamp_to_frame[timestamp_ms] = frame
        record_count += 1

        # Move to next record
        record = reader.next()

    st.success(f"Loaded {record_count} deepweed prediction frames from {filepath}")
    return timestamp_to_frame


def main():  # noqa: C901
    st.title("Deduplication Viewer")
    bucket_name = st.sidebar.radio("S3 Bucket", ["carbon-diagnostics", "carbon-automation-testing"])

    robot_id = st.text_input("Robot ID")
    diags = []
    if robot_id == "":
        return
    diags = get_zips_from_s3(robot_id, bucket_name=bucket_name) # TODO remove param

    diags = ["<select>"] + diags

    diag = st.selectbox("Select", diags)

    if diag == "<select>":
        return
    zip_path = download_zip_from_s3(diag, bucket_name=bucket_name)
    unzipped_path = unzip(zip_path)
    st.text(f"Downloaded and unzipped {zip_path}")

    files = list_files(unzipped_path)

    # Scan for available rows and predict cameras
    with st.spinner("Scanning for deepweed files..."):
        available_rows, row_predicts = get_available_rows_and_predicts(files)

    if not available_rows:
        st.warning("No deepweed files found in the diagnostic data")
        return

    # Row selection
    selected_row = st.sidebar.selectbox("Select Row", available_rows)

    if not selected_row:
        return

    # Predict camera selection
    available_predicts = row_predicts.get(selected_row, [])
    if not available_predicts:
        st.warning(f"No predict cameras found for {selected_row}")
        return

    selected_predict = st.sidebar.selectbox("Select Predict Camera", available_predicts)

    if not selected_predict:
        return

    # Find and load the selected deepweed file
    target_filename = f"{selected_predict}_deepweed.carbon"
    target_filepath = None

    for file in files:
        if file.endswith(target_filename) and selected_row in file:
            target_filepath = file
            break

    if not target_filepath:
        st.error(f"Could not find {target_filename} for {selected_row}")
        return

    st.subheader(f"Deepweed Data: {selected_row} - {selected_predict}")

    # Load the deepweed file
    with st.spinner(f"Loading {target_filename}..."):
        timestamp_to_frame = load_deepweed_file(target_filepath)

    if not timestamp_to_frame:
        st.error("Failed to load deepweed data")
        return

    # Display basic information
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Selected Row", selected_row)
    with col2:
        st.metric("Selected Predict", selected_predict)
    with col3:
        st.metric("Loaded Frames", len(timestamp_to_frame))

    st.write(f"**File path:** {target_filepath}")

    # Display timestamp range and add timestamp selector
    if timestamp_to_frame:
        timestamps = sorted(timestamp_to_frame.keys())
        min_timestamp = timestamps[0]
        max_timestamp = timestamps[-1]
        duration_ms = max_timestamp - min_timestamp

        st.subheader("Frame Information")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("First Frame", f"{min_timestamp} ms")
        with col2:
            st.metric("Last Frame", f"{max_timestamp} ms")
        with col3:
            st.metric("Duration", f"{duration_ms / 1000:.2f} sec")

        # Timestamp selector in sidebar
        st.sidebar.subheader("Frame Selection")

        # Create a slider that maps to timestamp indices
        timestamp_index = st.sidebar.slider(
            "Select Frame",
            min_value=0,
            max_value=len(timestamps) - 1,
            value=0,
            format="Frame %d"
        )

        selected_timestamp = timestamps[timestamp_index]
        selected_frame = timestamp_to_frame[selected_timestamp]

        st.sidebar.write(f"**Selected timestamp:** {selected_timestamp} ms")
        st.sidebar.write(f"**Frame {timestamp_index + 1} of {len(timestamps)}**")

    # TODO: Add deepweed data analysis and deduplication functionality here
    st.info("Deepweed data analysis and deduplication functionality will be implemented here.")

    # Show selected frame data
    if timestamp_to_frame and 'selected_frame' in locals():
        st.subheader(f"Selected Frame Data (Frame {timestamp_index + 1})")

        st.write(f"**Timestamp:** {selected_timestamp} ms")
        st.write(f"**Number of detections:** {len(selected_frame.detections)}")
        st.write(f"**Number of tracked items:** {len(selected_frame.tracked_items)}")
        st.write(f"**Plant matcher valid:** {selected_frame.plant_matcher_valid}")

        # Show detections if any
        if selected_frame.detections:
            st.subheader("Detections")
            detection_data = []
            for i, detection in enumerate(selected_frame.detections):
                detection_data.append({
                    "Index": i,
                    "X": f"{detection.x:.2f}",
                    "Y": f"{detection.y:.2f}",
                    "X Undistorted": f"{detection.x_undistorted:.2f}" if hasattr(detection, 'x_undistorted') else "N/A",
                    "Y Undistorted": f"{detection.y_undistorted:.2f}" if hasattr(detection, 'y_undistorted') else "N/A",
                    "Size": f"{detection.size:.2f}",
                    "Score": f"{detection.score:.3f}",
                    "Weed Score": f"{detection.weed_score:.3f}",
                    "Crop Score": f"{detection.crop_score:.3f}",
                    "Plant Score": f"{detection.plant_score:.3f}",
                    "Hit Class": detection.hit_class
                })
            st.dataframe(detection_data)

        # Show tracked items if any
        if selected_frame.tracked_items:
            st.subheader("Tracked Items")
            tracked_data = []
            for i, item in enumerate(selected_frame.tracked_items):
                tracked_data.append({
                    "Index": i,
                    "Trajectory ID": item.trajectory_id,
                    "Deduplicated": item.deduplicated,
                    "In Camera": item.in_camera,
                    "Before X": f"{item.coordinates_before.x_px:.2f}" if item.coordinates_before else "N/A",
                    "Before Y": f"{item.coordinates_before.y_px:.2f}" if item.coordinates_before else "N/A",
                    "After X": f"{item.coordinates_after.x_px:.2f}" if item.coordinates_after else "N/A",
                    "After Y": f"{item.coordinates_after.y_px:.2f}" if item.coordinates_after else "N/A"
                })
            st.dataframe(tracked_data)

        # Visualization of detection coordinates
        if selected_frame.detections:
            st.subheader("Undistortion Visualization")

            fig = go.Figure()

            # Collect coordinates for plotting
            distorted_x = []
            distorted_y = []
            undistorted_x = []
            undistorted_y = []
            detection_indices = []

            for i, detection in enumerate(selected_frame.detections):
                if hasattr(detection, 'used_in_dedup'):
                    if not detection.used_in_dedup:
                        continue
                # Check if undistorted coordinates exist
                if hasattr(detection, 'x_undistorted') and hasattr(detection, 'y_undistorted'):
                    distorted_x.append(detection.x)
                    distorted_y.append(detection.y)
                    undistorted_x.append(detection.x_undistorted)
                    undistorted_y.append(detection.y_undistorted)
                    detection_indices.append(i)

            if distorted_x and undistorted_x:
                # Collect additional data for tooltips
                plant_scores = []
                weed_scores = []
                crop_scores = []
                sizes = []

                for i in detection_indices:
                    detection = selected_frame.detections[i]
                    plant_scores.append(detection.plant_score)
                    weed_scores.append(detection.weed_score)
                    crop_scores.append(detection.crop_score)
                    sizes.append(detection.size)

                # Plot distorted coordinates (original x, y)
                fig.add_trace(go.Scatter(
                    x=distorted_x,
                    y=distorted_y,
                    mode='markers',
                    name='Distorted (x, y)',
                    marker=dict(color='red', size=8, symbol='circle'),
                    hovertemplate="<b>Detection %{customdata}</b><br>" +
                                 "Distorted X: %{x:.2f}<br>" +
                                 "Distorted Y: %{y:.2f}<br>" +
                                 "Plant Score: %{meta:.3f}<br>" +
                                 "<extra></extra>",
                    customdata=detection_indices,
                    meta=plant_scores,
                ))

                # Plot undistorted coordinates
                fig.add_trace(go.Scatter(
                    x=undistorted_x,
                    y=undistorted_y,
                    mode='markers',
                    name='Undistorted (x_undistorted, y_undistorted)',
                    marker=dict(color='blue', size=8, symbol='diamond'),
                    hovertemplate="<b>Detection %{customdata}</b><br>" +
                                 "Undistorted X: %{x:.2f}<br>" +
                                 "Undistorted Y: %{y:.2f}<br>" +
                                 "Plant Score: %{meta:.3f}<br>" +
                                 "<extra></extra>",
                    customdata=detection_indices,
                    meta=plant_scores,
                ))

                # Add lines connecting distorted to undistorted coordinates
                for i in range(len(distorted_x)):
                    fig.add_trace(go.Scatter(
                        x=[distorted_x[i], undistorted_x[i]],
                        y=[distorted_y[i], undistorted_y[i]],
                        mode='lines',
                        line=dict(color='gray', width=1, dash='dot'),
                        showlegend=False,
                        hoverinfo='skip'
                    ))

                # Update layout
                fig.update_layout(
                    title=f"Detection Coordinates - Frame {timestamp_index + 1}",
                    xaxis_title="X Coordinate",
                    yaxis_title="Y Coordinate",
                    hovermode='closest',
                    height=600,
                    showlegend=True
                )

                # Invert y-axis to match image coordinates (origin at top-left)
                fig.update_yaxes(autorange="reversed")

                st.plotly_chart(fig, use_container_width=True)

                # Show statistics about coordinate differences
                st.subheader("Coordinate Transformation Statistics")

                x_diffs = [abs(ux - dx) for ux, dx in zip(undistorted_x, distorted_x)]
                y_diffs = [abs(uy - dy) for uy, dy in zip(undistorted_y, distorted_y)]

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Avg X Difference", f"{sum(x_diffs)/len(x_diffs):.2f} px")
                with col2:
                    st.metric("Avg Y Difference", f"{sum(y_diffs)/len(y_diffs):.2f} px")
                with col3:
                    st.metric("Max Distance", f"{max([((ux-dx)**2 + (uy-dy)**2)**0.5 for ux, dx, uy, dy in zip(undistorted_x, distorted_x, undistorted_y, distorted_y)]):.2f} px")

            else:
                st.warning("No undistorted coordinates found in detections for this frame")

        # Trajectory-Detection Matching Visualization
        if selected_frame.tracked_items or selected_frame.detections:
            st.subheader("Trajectory-Detection Matching")

            # Add checkbox for showing shifts
            show_shifts = st.checkbox("Show shifts", value=False)

            fig = go.Figure()

            # Collect tracked items that are in camera
            tracked_items_data = []
            for item in selected_frame.tracked_items:
                if item.in_camera and item.coordinates_before:
                    tracked_items_data.append({
                        'trajectory_id': item.trajectory_id,
                        'x': item.coordinates_before.x_px,
                        'y': item.coordinates_before.y_px,
                        'deduplicated': item.deduplicated
                    })

            # Collect deepweed detections used in dedup with trajectory IDs
            detection_data = []
            for detection in selected_frame.detections:
                if (hasattr(detection, 'used_in_dedup') and detection.used_in_dedup and
                    hasattr(detection, 'trajectory_id') and hasattr(detection, 'x_undistorted') and
                    hasattr(detection, 'y_undistorted')):
                    detection_data.append({
                        'trajectory_id': detection.trajectory_id,
                        'x': detection.x_undistorted,
                        'y': detection.y_undistorted,
                        'plant_score': detection.plant_score,
                        'weed_score': detection.weed_score,
                        'crop_score': detection.crop_score
                    })

            # Plot tracked items
            if tracked_items_data:
                tracked_x = [item['x'] for item in tracked_items_data]
                tracked_y = [item['y'] for item in tracked_items_data]
                tracked_ids = [item['trajectory_id'] for item in tracked_items_data]
                tracked_dedup = [item['deduplicated'] for item in tracked_items_data]

                fig.add_trace(go.Scatter(
                    x=tracked_x,
                    y=tracked_y,
                    mode='markers',
                    name='Tracked Items (in_camera)',
                    marker=dict(color='green', size=10, symbol='square'),
                    hovertemplate="<b>Tracked Item</b><br>" +
                                 "Trajectory ID: %{customdata[0]}<br>" +
                                 "X (before): %{x:.2f} px<br>" +
                                 "Y (before): %{y:.2f} px<br>" +
                                 "Deduplicated: %{customdata[1]}<br>" +
                                 "<extra></extra>",
                    customdata=list(zip(tracked_ids, tracked_dedup))
                ))

            # Plot deepweed detections
            if detection_data:
                detection_x = [det['x'] for det in detection_data]
                detection_y = [det['y'] for det in detection_data]
                detection_ids = [det['trajectory_id'] for det in detection_data]
                detection_scores = [(det['plant_score'], det['weed_score'], det['crop_score']) for det in detection_data]

                fig.add_trace(go.Scatter(
                    x=detection_x,
                    y=detection_y,
                    mode='markers',
                    name='Deepweed Detections (used_in_dedup)',
                    marker=dict(color='orange', size=8, symbol='circle'),
                    hovertemplate="<b>Deepweed Detection</b><br>" +
                                 "Trajectory ID: %{customdata[0]}<br>" +
                                 "X (undistorted): %{x:.2f}<br>" +
                                 "Y (undistorted): %{y:.2f}<br>" +
                                 "Plant Score: %{customdata[1]:.3f}<br>" +
                                 "Weed Score: %{customdata[2]:.3f}<br>" +
                                 "Crop Score: %{customdata[3]:.3f}<br>" +
                                 "<extra></extra>",
                    customdata=[(det_id, scores[0], scores[1], scores[2]) for det_id, scores in zip(detection_ids, detection_scores)]
                ))

            # Draw lines connecting items with same trajectory_id
            if tracked_items_data and detection_data:
                # Create lookup dictionaries
                tracked_by_id = {item['trajectory_id']: (item['x'], item['y']) for item in tracked_items_data}
                detection_by_id = {det['trajectory_id']: (det['x'], det['y']) for det in detection_data}

                # Find matching trajectory IDs and draw lines
                matching_ids = set(tracked_by_id.keys()) & set(detection_by_id.keys())

                for traj_id in matching_ids:
                    tracked_pos = tracked_by_id[traj_id]
                    detection_pos = detection_by_id[traj_id]

                    fig.add_trace(go.Scatter(
                        x=[tracked_pos[0], detection_pos[0]],
                        y=[tracked_pos[1], detection_pos[1]],
                        mode='lines',
                        line=dict(color='gray', width=2, dash='dot'),
                        showlegend=False,
                        hovertemplate=f"<b>Trajectory ID: {traj_id}</b><br>" +
                                     "Connection between tracked item and detection<br>" +
                                     "<extra></extra>"
                    ))

            # Add shift arrows if checkbox is enabled
            if (show_shifts and selected_frame.plant_matcher_valid and tracked_items_data):

                # Create lookup for tracked items by trajectory_id
                tracked_lookup = {item['trajectory_id']: (item['x'], item['y']) for item in tracked_items_data}

                # Add best_shift arrow if available
                if (hasattr(selected_frame, 'best_shift') and selected_frame.best_shift):
                    best_shift = selected_frame.best_shift
                    shift_trajectory_id = best_shift.trajectory_id

                    if shift_trajectory_id in tracked_lookup:
                        shift_origin = tracked_lookup[shift_trajectory_id]
                        arrow_end_x = shift_origin[0] + best_shift.x_shift
                        arrow_end_y = shift_origin[1] + best_shift.y_shift

                        # Add best shift arrow
                        fig.add_trace(go.Scatter(
                            x=[shift_origin[0], arrow_end_x],
                            y=[shift_origin[1], arrow_end_y],
                            mode='lines+markers',
                            line=dict(color='red', width=3),
                            marker=dict(
                                size=[8, 12],
                                symbol=['circle', 'triangle-up'],
                                color='red'
                            ),
                            name='Best Shift',
                            hovertemplate="<b>Best Shift</b><br>" +
                                         f"Trajectory ID: {shift_trajectory_id}<br>" +
                                         f"X Shift: {best_shift.x_shift:.2f}<br>" +
                                         f"Y Shift: {best_shift.y_shift:.2f}<br>" +
                                         f"Centroid ID: {best_shift.centroid_id}<br>" +
                                         "<extra></extra>"
                        ))

                # Add all candidate shifts if available
                if (hasattr(selected_frame, 'candidate_shifts') and selected_frame.candidate_shifts):
                    for i, candidate_shift in enumerate(selected_frame.candidate_shifts):
                        shift_trajectory_id = candidate_shift.trajectory_id

                        if shift_trajectory_id in tracked_lookup:
                            shift_origin = tracked_lookup[shift_trajectory_id]
                            arrow_end_x = shift_origin[0] + candidate_shift.x_shift
                            arrow_end_y = shift_origin[1] + candidate_shift.y_shift

                            # Add candidate shift arrow (thinner and different color)
                            fig.add_trace(go.Scatter(
                                x=[shift_origin[0], arrow_end_x],
                                y=[shift_origin[1], arrow_end_y],
                                mode='lines+markers',
                                line=dict(color='grey', width=2, dash='dash'),
                                marker=dict(
                                    size=[6, 8],
                                    symbol=['circle', 'triangle-up'],
                                    color='grey'
                                ),
                                name=f'Candidate Shift {i+1}',
                                hovertemplate="<b>Candidate Shift</b><br>" +
                                             f"Trajectory ID: {shift_trajectory_id}<br>" +
                                             f"X Shift: {candidate_shift.x_shift:.2f}<br>" +
                                             f"Y Shift: {candidate_shift.y_shift:.2f}<br>" +
                                             f"Centroid ID: {candidate_shift.centroid_id}<br>" +
                                             "<extra></extra>"
                            ))

            # Update layout
            fig.update_layout(
                title=f"Trajectory-Detection Matching - Frame {timestamp_index + 1}",
                xaxis_title="X Coordinate (px)",
                yaxis_title="Y Coordinate (px)",
                hovermode='closest',
                height=600,
                showlegend=True
            )

            # Invert y-axis to match image coordinates
            fig.update_yaxes(autorange="reversed")

            st.plotly_chart(fig, use_container_width=True)

            # Show matching statistics
            if tracked_items_data and detection_data:
                tracked_ids = set(item['trajectory_id'] for item in tracked_items_data)
                detection_ids = set(det['trajectory_id'] for det in detection_data)
                matching_ids = tracked_ids & detection_ids

                st.subheader("Matching Statistics")
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Tracked Items", len(tracked_items_data))
                with col2:
                    st.metric("Detections", len(detection_data))
                with col3:
                    st.metric("Matched Pairs", len(matching_ids))
                with col4:
                    match_rate = len(matching_ids) / max(len(tracked_ids), 1) * 100
                    st.metric("Match Rate", f"{match_rate:.1f}%")

        # 3D Tracked Items Visualization
        if selected_frame.tracked_items:
            st.subheader("3D Tracked Items Coordinate Space")

            fig_3d = go.Figure()

            # Collect tracked items data for 3D plotting
            before_items = []
            after_items = []
            deduplicated_pairs = []

            for item in selected_frame.tracked_items:
                if item.coordinates_before:
                    # Check if coordinates have the required mm fields
                    if (hasattr(item.coordinates_before, 'x_mm') and
                        hasattr(item.coordinates_before, 'y_mm') and
                        hasattr(item.coordinates_before, 'z_mm')):

                        before_coord = {
                            'trajectory_id': item.trajectory_id,
                            'x': item.coordinates_before.x_mm,
                            'y': item.coordinates_before.y_mm,
                            'z': item.coordinates_before.z_mm,
                            'deduplicated': item.deduplicated,
                            'in_camera': item.in_camera
                        }
                        before_items.append(before_coord)

                        # If deduplicated and has after coordinates, collect those too
                        if (item.deduplicated and item.coordinates_after and
                            hasattr(item.coordinates_after, 'x_mm') and
                            hasattr(item.coordinates_after, 'y_mm') and
                            hasattr(item.coordinates_after, 'z_mm')):

                            after_coord = {
                                'trajectory_id': item.trajectory_id,
                                'x': item.coordinates_after.x_mm,
                                'y': item.coordinates_after.y_mm,
                                'z': item.coordinates_after.z_mm
                            }
                            after_items.append(after_coord)
                            deduplicated_pairs.append((before_coord, after_coord))

            # Plot all tracked items (before coordinates)
            if before_items:
                before_x = [item['x'] for item in before_items]
                before_y = [item['y'] for item in before_items]
                before_z = [item['z'] for item in before_items]
                before_ids = [item['trajectory_id'] for item in before_items]
                before_dedup = [item['deduplicated'] for item in before_items]
                before_camera = [item['in_camera'] for item in before_items]

                fig_3d.add_trace(go.Scatter3d(
                    x=before_x,
                    y=before_y,
                    z=before_z,
                    mode='markers',
                    name='Tracked Items (before)',
                    marker=dict(
                        color=['red' if dedup else 'blue' for dedup in before_dedup],
                        size=8,
                        symbol='circle'
                    ),
                    hovertemplate="<b>Tracked Item (Before)</b><br>" +
                                 "Trajectory ID: %{customdata[0]}<br>" +
                                 "X: %{x:.2f} mm<br>" +
                                 "Y: %{y:.2f} mm<br>" +
                                 "Z: %{z:.2f} mm<br>" +
                                 "Deduplicated: %{customdata[1]}<br>" +
                                 "In Camera: %{customdata[2]}<br>" +
                                 "<extra></extra>",
                    customdata=list(zip(before_ids, before_dedup, before_camera))
                ))

            # Plot after coordinates for deduplicated items
            if after_items:
                after_x = [item['x'] for item in after_items]
                after_y = [item['y'] for item in after_items]
                after_z = [item['z'] for item in after_items]
                after_ids = [item['trajectory_id'] for item in after_items]

                fig_3d.add_trace(go.Scatter3d(
                    x=after_x,
                    y=after_y,
                    z=after_z,
                    mode='markers',
                    name='Deduplicated Items (after)',
                    marker=dict(
                        color='green',
                        size=8,
                        symbol='diamond'
                    ),
                    hovertemplate="<b>Tracked Item (After)</b><br>" +
                                 "Trajectory ID: %{customdata}<br>" +
                                 "X: %{x:.2f} mm<br>" +
                                 "Y: %{y:.2f} mm<br>" +
                                 "Z: %{z:.2f} mm<br>" +
                                 "<extra></extra>",
                    customdata=after_ids
                ))

            # Draw lines connecting before and after coordinates for deduplicated items
            for before_coord, after_coord in deduplicated_pairs:
                fig_3d.add_trace(go.Scatter3d(
                    x=[before_coord['x'], after_coord['x']],
                    y=[before_coord['y'], after_coord['y']],
                    z=[before_coord['z'], after_coord['z']],
                    mode='lines',
                    line=dict(color='purple', width=4),
                    showlegend=False,
                    hovertemplate=f"<b>Deduplication Movement</b><br>" +
                                 f"Trajectory ID: {before_coord['trajectory_id']}<br>" +
                                 "Before → After<br>" +
                                 "<extra></extra>"
                ))

            # Update 3D layout
            fig_3d.update_layout(
                title=f"3D Tracked Items Coordinate Space - Frame {timestamp_index + 1}",
                scene=dict(
                    xaxis_title="X (mm)",
                    yaxis_title="Y (mm)",
                    zaxis_title="Z (mm)",
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    )
                ),
                height=700,
                showlegend=True
            )

            st.plotly_chart(fig_3d, use_container_width=True)

            # Show 3D statistics
            if before_items:
                deduplicated_count = sum(1 for item in before_items if item['deduplicated'])
                in_camera_count = sum(1 for item in before_items if item['in_camera'])

                st.subheader("3D Coordinate Statistics")
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Total Tracked Items", len(before_items))
                with col2:
                    st.metric("Deduplicated Items", deduplicated_count)
                with col3:
                    st.metric("In Camera", in_camera_count)
                with col4:
                    dedup_rate = deduplicated_count / len(before_items) * 100 if before_items else 0
                    st.metric("Deduplication Rate", f"{dedup_rate:.1f}%")


if __name__ == "__main__":
    main()
