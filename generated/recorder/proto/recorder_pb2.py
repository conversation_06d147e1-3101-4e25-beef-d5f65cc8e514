# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: recorder/proto/recorder.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='recorder/proto/recorder.proto',
  package='recorder',
  syntax='proto3',
  serialized_options=b'Z\016proto/recorder',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1drecorder/proto/recorder.proto\x12\x08recorder\".\n\x0e\x44\x65tectionClass\x12\r\n\x05\x63lass\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x02\"\x98\x03\n\x11\x44\x65\x65pweedDetection\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x0c\n\x04size\x18\x03 \x01(\x02\x12\r\n\x05score\x18\x04 \x01(\x02\x12\x37\n\thit_class\x18\x05 \x01(\x0e\x32$.recorder.DeepweedDetection.HitClass\x12\x33\n\x11\x64\x65tection_classes\x18\x06 \x03(\x0b\x32\x18.recorder.DetectionClass\x12\x1a\n\x12mask_intersections\x18\x07 \x03(\x0c\x12\x15\n\rtrajectory_id\x18\x08 \x01(\r\x12\x12\n\nweed_score\x18\t \x01(\x02\x12\x12\n\ncrop_score\x18\n \x01(\x02\x12\x13\n\x0bplant_score\x18\x0b \x01(\x02\x12$\n\x1c\x65mbedding_category_distances\x18\x0c \x03(\x02\x12\x15\n\rx_undistorted\x18\r \x01(\x02\x12\x15\n\ry_undistorted\x18\x0e \x01(\x02\"\x1e\n\x08HitClass\x12\x08\n\x04WEED\x10\x00\x12\x08\n\x04\x43ROP\x10\x01\"^\n\x16TrackedItemCoordinates\x12\x0c\n\x04x_mm\x18\x01 \x01(\x02\x12\x0c\n\x04y_mm\x18\x02 \x01(\x02\x12\x0c\n\x04z_mm\x18\x03 \x01(\x02\x12\x0c\n\x04x_px\x18\x04 \x01(\x02\x12\x0c\n\x04y_px\x18\x05 \x01(\x02\"7\n\x11\x45mbeddingCategory\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\x12\x10\n\x08\x64istance\x18\x02 \x01(\x02\"\xc8\x01\n\x0bTrackedItem\x12\x15\n\rtrajectory_id\x18\x01 \x01(\r\x12\x14\n\x0c\x64\x65\x64uplicated\x18\x02 \x01(\x08\x12<\n\x12\x63oordinates_before\x18\x03 \x01(\x0b\x32 .recorder.TrackedItemCoordinates\x12;\n\x11\x63oordinates_after\x18\x04 \x01(\x0b\x32 .recorder.TrackedItemCoordinates\x12\x11\n\tin_camera\x18\x05 \x01(\x08\"^\n\x0e\x43\x61ndidateShift\x12\x0f\n\x07x_shift\x18\x01 \x01(\x02\x12\x0f\n\x07y_shift\x18\x02 \x01(\x02\x12\x13\n\x0b\x63\x65ntroid_id\x18\x03 \x01(\r\x12\x15\n\rtrajectory_id\x18\x04 \x01(\r\"\xab\x02\n\x17\x44\x65\x65pweedPredictionFrame\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12/\n\ndetections\x18\x02 \x03(\x0b\x32\x1b.recorder.DeepweedDetection\x12\x1c\n\x14\x65mbedding_categories\x18\x03 \x03(\t\x12,\n\rtracked_items\x18\x04 \x03(\x0b\x32\x15.recorder.TrackedItem\x12,\n\nbest_shift\x18\x05 \x01(\x0b\x32\x18.recorder.CandidateShift\x12\x32\n\x10\x63\x61ndidate_shifts\x18\x06 \x03(\x0b\x32\x18.recorder.CandidateShift\x12\x1b\n\x13plant_matcher_valid\x18\x07 \x01(\x08\"i\n\x18\x44\x65\x65pweedPredictionRecord\x12\x1b\n\x13record_timestamp_ms\x18\x01 \x01(\x04\x12\x30\n\x05\x66rame\x18\x02 \x01(\x0b\x32!.recorder.DeepweedPredictionFrame\">\n\x12LaneHeightSnapshot\x12\x13\n\x0bweed_height\x18\x01 \x03(\x01\x12\x13\n\x0b\x63rop_height\x18\x02 \x03(\x01\"_\n\x10LaneHeightRecord\x12\x1b\n\x13record_timestamp_ms\x18\x01 \x01(\x04\x12.\n\x08snapshot\x18\x02 \x01(\x0b\x32\x1c.recorder.LaneHeightSnapshot\"\xab\x01\n\x13RotaryTicksSnapshot\x12\x14\n\x0ctimestamp_us\x18\x01 \x01(\x04\x12\n\n\x02\x66l\x18\x02 \x01(\x05\x12\n\n\x02\x66r\x18\x03 \x01(\x05\x12\n\n\x02\x62l\x18\x04 \x01(\x05\x12\n\n\x02\x62r\x18\x05 \x01(\x05\x12\x12\n\nfl_enabled\x18\x06 \x01(\x08\x12\x12\n\nfr_enabled\x18\x07 \x01(\x08\x12\x12\n\nbl_enabled\x18\x08 \x01(\x08\x12\x12\n\nbr_enabled\x18\t \x01(\x08\"a\n\x11RotaryTicksRecord\x12\x1b\n\x13record_timestamp_ms\x18\x01 \x01(\x04\x12/\n\x08snapshot\x18\x02 \x01(\x0b\x32\x1d.recorder.RotaryTicksSnapshotB\x10Z\x0eproto/recorderb\x06proto3'
)



_DEEPWEEDDETECTION_HITCLASS = _descriptor.EnumDescriptor(
  name='HitClass',
  full_name='recorder.DeepweedDetection.HitClass',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WEED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CROP', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=470,
  serialized_end=500,
)
_sym_db.RegisterEnumDescriptor(_DEEPWEEDDETECTION_HITCLASS)


_DETECTIONCLASS = _descriptor.Descriptor(
  name='DetectionClass',
  full_name='recorder.DetectionClass',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='class', full_name='recorder.DetectionClass.class', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='recorder.DetectionClass.score', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=43,
  serialized_end=89,
)


_DEEPWEEDDETECTION = _descriptor.Descriptor(
  name='DeepweedDetection',
  full_name='recorder.DeepweedDetection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='recorder.DeepweedDetection.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='recorder.DeepweedDetection.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size', full_name='recorder.DeepweedDetection.size', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='recorder.DeepweedDetection.score', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hit_class', full_name='recorder.DeepweedDetection.hit_class', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='detection_classes', full_name='recorder.DeepweedDetection.detection_classes', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mask_intersections', full_name='recorder.DeepweedDetection.mask_intersections', index=6,
      number=7, type=12, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='recorder.DeepweedDetection.trajectory_id', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_score', full_name='recorder.DeepweedDetection.weed_score', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_score', full_name='recorder.DeepweedDetection.crop_score', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_score', full_name='recorder.DeepweedDetection.plant_score', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding_category_distances', full_name='recorder.DeepweedDetection.embedding_category_distances', index=11,
      number=12, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x_undistorted', full_name='recorder.DeepweedDetection.x_undistorted', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_undistorted', full_name='recorder.DeepweedDetection.y_undistorted', index=13,
      number=14, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _DEEPWEEDDETECTION_HITCLASS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=92,
  serialized_end=500,
)


_TRACKEDITEMCOORDINATES = _descriptor.Descriptor(
  name='TrackedItemCoordinates',
  full_name='recorder.TrackedItemCoordinates',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x_mm', full_name='recorder.TrackedItemCoordinates.x_mm', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_mm', full_name='recorder.TrackedItemCoordinates.y_mm', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z_mm', full_name='recorder.TrackedItemCoordinates.z_mm', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x_px', full_name='recorder.TrackedItemCoordinates.x_px', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_px', full_name='recorder.TrackedItemCoordinates.y_px', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=502,
  serialized_end=596,
)


_EMBEDDINGCATEGORY = _descriptor.Descriptor(
  name='EmbeddingCategory',
  full_name='recorder.EmbeddingCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='category', full_name='recorder.EmbeddingCategory.category', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='distance', full_name='recorder.EmbeddingCategory.distance', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=598,
  serialized_end=653,
)


_TRACKEDITEM = _descriptor.Descriptor(
  name='TrackedItem',
  full_name='recorder.TrackedItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='recorder.TrackedItem.trajectory_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deduplicated', full_name='recorder.TrackedItem.deduplicated', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='coordinates_before', full_name='recorder.TrackedItem.coordinates_before', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='coordinates_after', full_name='recorder.TrackedItem.coordinates_after', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='in_camera', full_name='recorder.TrackedItem.in_camera', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=656,
  serialized_end=856,
)


_CANDIDATESHIFT = _descriptor.Descriptor(
  name='CandidateShift',
  full_name='recorder.CandidateShift',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x_shift', full_name='recorder.CandidateShift.x_shift', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_shift', full_name='recorder.CandidateShift.y_shift', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='centroid_id', full_name='recorder.CandidateShift.centroid_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='recorder.CandidateShift.trajectory_id', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=858,
  serialized_end=952,
)


_DEEPWEEDPREDICTIONFRAME = _descriptor.Descriptor(
  name='DeepweedPredictionFrame',
  full_name='recorder.DeepweedPredictionFrame',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='recorder.DeepweedPredictionFrame.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='detections', full_name='recorder.DeepweedPredictionFrame.detections', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding_categories', full_name='recorder.DeepweedPredictionFrame.embedding_categories', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tracked_items', full_name='recorder.DeepweedPredictionFrame.tracked_items', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='best_shift', full_name='recorder.DeepweedPredictionFrame.best_shift', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='candidate_shifts', full_name='recorder.DeepweedPredictionFrame.candidate_shifts', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_matcher_valid', full_name='recorder.DeepweedPredictionFrame.plant_matcher_valid', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=955,
  serialized_end=1254,
)


_DEEPWEEDPREDICTIONRECORD = _descriptor.Descriptor(
  name='DeepweedPredictionRecord',
  full_name='recorder.DeepweedPredictionRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_timestamp_ms', full_name='recorder.DeepweedPredictionRecord.record_timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='frame', full_name='recorder.DeepweedPredictionRecord.frame', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1256,
  serialized_end=1361,
)


_LANEHEIGHTSNAPSHOT = _descriptor.Descriptor(
  name='LaneHeightSnapshot',
  full_name='recorder.LaneHeightSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weed_height', full_name='recorder.LaneHeightSnapshot.weed_height', index=0,
      number=1, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_height', full_name='recorder.LaneHeightSnapshot.crop_height', index=1,
      number=2, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1363,
  serialized_end=1425,
)


_LANEHEIGHTRECORD = _descriptor.Descriptor(
  name='LaneHeightRecord',
  full_name='recorder.LaneHeightRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_timestamp_ms', full_name='recorder.LaneHeightRecord.record_timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='snapshot', full_name='recorder.LaneHeightRecord.snapshot', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1427,
  serialized_end=1522,
)


_ROTARYTICKSSNAPSHOT = _descriptor.Descriptor(
  name='RotaryTicksSnapshot',
  full_name='recorder.RotaryTicksSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_us', full_name='recorder.RotaryTicksSnapshot.timestamp_us', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fl', full_name='recorder.RotaryTicksSnapshot.fl', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fr', full_name='recorder.RotaryTicksSnapshot.fr', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bl', full_name='recorder.RotaryTicksSnapshot.bl', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='br', full_name='recorder.RotaryTicksSnapshot.br', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fl_enabled', full_name='recorder.RotaryTicksSnapshot.fl_enabled', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fr_enabled', full_name='recorder.RotaryTicksSnapshot.fr_enabled', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bl_enabled', full_name='recorder.RotaryTicksSnapshot.bl_enabled', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='br_enabled', full_name='recorder.RotaryTicksSnapshot.br_enabled', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1525,
  serialized_end=1696,
)


_ROTARYTICKSRECORD = _descriptor.Descriptor(
  name='RotaryTicksRecord',
  full_name='recorder.RotaryTicksRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_timestamp_ms', full_name='recorder.RotaryTicksRecord.record_timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='snapshot', full_name='recorder.RotaryTicksRecord.snapshot', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1698,
  serialized_end=1795,
)

_DEEPWEEDDETECTION.fields_by_name['hit_class'].enum_type = _DEEPWEEDDETECTION_HITCLASS
_DEEPWEEDDETECTION.fields_by_name['detection_classes'].message_type = _DETECTIONCLASS
_DEEPWEEDDETECTION_HITCLASS.containing_type = _DEEPWEEDDETECTION
_TRACKEDITEM.fields_by_name['coordinates_before'].message_type = _TRACKEDITEMCOORDINATES
_TRACKEDITEM.fields_by_name['coordinates_after'].message_type = _TRACKEDITEMCOORDINATES
_DEEPWEEDPREDICTIONFRAME.fields_by_name['detections'].message_type = _DEEPWEEDDETECTION
_DEEPWEEDPREDICTIONFRAME.fields_by_name['tracked_items'].message_type = _TRACKEDITEM
_DEEPWEEDPREDICTIONFRAME.fields_by_name['best_shift'].message_type = _CANDIDATESHIFT
_DEEPWEEDPREDICTIONFRAME.fields_by_name['candidate_shifts'].message_type = _CANDIDATESHIFT
_DEEPWEEDPREDICTIONRECORD.fields_by_name['frame'].message_type = _DEEPWEEDPREDICTIONFRAME
_LANEHEIGHTRECORD.fields_by_name['snapshot'].message_type = _LANEHEIGHTSNAPSHOT
_ROTARYTICKSRECORD.fields_by_name['snapshot'].message_type = _ROTARYTICKSSNAPSHOT
DESCRIPTOR.message_types_by_name['DetectionClass'] = _DETECTIONCLASS
DESCRIPTOR.message_types_by_name['DeepweedDetection'] = _DEEPWEEDDETECTION
DESCRIPTOR.message_types_by_name['TrackedItemCoordinates'] = _TRACKEDITEMCOORDINATES
DESCRIPTOR.message_types_by_name['EmbeddingCategory'] = _EMBEDDINGCATEGORY
DESCRIPTOR.message_types_by_name['TrackedItem'] = _TRACKEDITEM
DESCRIPTOR.message_types_by_name['CandidateShift'] = _CANDIDATESHIFT
DESCRIPTOR.message_types_by_name['DeepweedPredictionFrame'] = _DEEPWEEDPREDICTIONFRAME
DESCRIPTOR.message_types_by_name['DeepweedPredictionRecord'] = _DEEPWEEDPREDICTIONRECORD
DESCRIPTOR.message_types_by_name['LaneHeightSnapshot'] = _LANEHEIGHTSNAPSHOT
DESCRIPTOR.message_types_by_name['LaneHeightRecord'] = _LANEHEIGHTRECORD
DESCRIPTOR.message_types_by_name['RotaryTicksSnapshot'] = _ROTARYTICKSSNAPSHOT
DESCRIPTOR.message_types_by_name['RotaryTicksRecord'] = _ROTARYTICKSRECORD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DetectionClass = _reflection.GeneratedProtocolMessageType('DetectionClass', (_message.Message,), {
  'DESCRIPTOR' : _DETECTIONCLASS,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.DetectionClass)
  })
_sym_db.RegisterMessage(DetectionClass)

DeepweedDetection = _reflection.GeneratedProtocolMessageType('DeepweedDetection', (_message.Message,), {
  'DESCRIPTOR' : _DEEPWEEDDETECTION,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.DeepweedDetection)
  })
_sym_db.RegisterMessage(DeepweedDetection)

TrackedItemCoordinates = _reflection.GeneratedProtocolMessageType('TrackedItemCoordinates', (_message.Message,), {
  'DESCRIPTOR' : _TRACKEDITEMCOORDINATES,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.TrackedItemCoordinates)
  })
_sym_db.RegisterMessage(TrackedItemCoordinates)

EmbeddingCategory = _reflection.GeneratedProtocolMessageType('EmbeddingCategory', (_message.Message,), {
  'DESCRIPTOR' : _EMBEDDINGCATEGORY,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.EmbeddingCategory)
  })
_sym_db.RegisterMessage(EmbeddingCategory)

TrackedItem = _reflection.GeneratedProtocolMessageType('TrackedItem', (_message.Message,), {
  'DESCRIPTOR' : _TRACKEDITEM,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.TrackedItem)
  })
_sym_db.RegisterMessage(TrackedItem)

CandidateShift = _reflection.GeneratedProtocolMessageType('CandidateShift', (_message.Message,), {
  'DESCRIPTOR' : _CANDIDATESHIFT,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.CandidateShift)
  })
_sym_db.RegisterMessage(CandidateShift)

DeepweedPredictionFrame = _reflection.GeneratedProtocolMessageType('DeepweedPredictionFrame', (_message.Message,), {
  'DESCRIPTOR' : _DEEPWEEDPREDICTIONFRAME,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.DeepweedPredictionFrame)
  })
_sym_db.RegisterMessage(DeepweedPredictionFrame)

DeepweedPredictionRecord = _reflection.GeneratedProtocolMessageType('DeepweedPredictionRecord', (_message.Message,), {
  'DESCRIPTOR' : _DEEPWEEDPREDICTIONRECORD,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.DeepweedPredictionRecord)
  })
_sym_db.RegisterMessage(DeepweedPredictionRecord)

LaneHeightSnapshot = _reflection.GeneratedProtocolMessageType('LaneHeightSnapshot', (_message.Message,), {
  'DESCRIPTOR' : _LANEHEIGHTSNAPSHOT,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.LaneHeightSnapshot)
  })
_sym_db.RegisterMessage(LaneHeightSnapshot)

LaneHeightRecord = _reflection.GeneratedProtocolMessageType('LaneHeightRecord', (_message.Message,), {
  'DESCRIPTOR' : _LANEHEIGHTRECORD,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.LaneHeightRecord)
  })
_sym_db.RegisterMessage(LaneHeightRecord)

RotaryTicksSnapshot = _reflection.GeneratedProtocolMessageType('RotaryTicksSnapshot', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYTICKSSNAPSHOT,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.RotaryTicksSnapshot)
  })
_sym_db.RegisterMessage(RotaryTicksSnapshot)

RotaryTicksRecord = _reflection.GeneratedProtocolMessageType('RotaryTicksRecord', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYTICKSRECORD,
  '__module__' : 'recorder.proto.recorder_pb2'
  # @@protoc_insertion_point(class_scope:recorder.RotaryTicksRecord)
  })
_sym_db.RegisterMessage(RotaryTicksRecord)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
