import abc
import logging
import random
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple, Type

import pandas as pd

from deeplearning.comparison.data_utils import ComparisonLabels, get_category_key, get_size_key, px2mm


def default_string(item: Optional[str]) -> str:
    if item is None:
        return ""

    return item


class ComparisonLabelSampler:
    def __init__(
        self, comparison_labels: List[ComparisonLabels], image_id_to_geohash: Dict[str, Optional[str]]
    ) -> None:
        self._sampler: Dict[Any, Any] = {}

    @abc.abstractmethod
    def query(self) -> str:
        pass

    def update_probabilities(self, results: List[Dict[str, Any]]) -> None:
        pass


class ComparisonLabelSamplerUniform(ComparisonLabelSampler):
    def __init__(
        self, comparison_labels: List[ComparisonLabels], image_id_to_geohash: Dict[str, Optional[str]]
    ) -> None:
        super().__init__(comparison_labels, image_id_to_geohash)
        for comparison_label in comparison_labels:
            self._add(id=comparison_label.id)

    def _add(self, *, id: str) -> None:
        self._sampler[id] = []

    def query(self) -> str:
        return str(random.choice(list(self._sampler.keys())))

    def __str__(self) -> str:
        return_string = f"Number of items: {len(self._sampler)}"

        return return_string


class ComparisonLabelSamplerBasic(ComparisonLabelSampler):
    def __init__(
        self, comparison_labels: List[ComparisonLabels], image_id_to_geohash: Dict[str, Optional[str]]
    ) -> None:
        super().__init__(comparison_labels, image_id_to_geohash)
        for comparison_label in comparison_labels:
            self._add(id=comparison_label.id, match=comparison_label.match)

    def _add(self, *, id: str, match: bool) -> None:
        if match not in self._sampler:
            self._sampler[match] = []

        self._sampler[match].append(id)

    def query(self) -> str:
        match = random.random() < 0.5

        return str(random.choice(self._sampler[match]))

    def __str__(self) -> str:
        return_string = ""

        for match, items in self._sampler.items():
            return_string += f"Key: {match}, Count: {len(items)}\n"

        return return_string


class ComparisonLabelSamplerSizeGeo(ComparisonLabelSampler):
    def __init__(
        self, comparison_labels: List[ComparisonLabels], image_id_to_geohash: Dict[str, Optional[str]]
    ) -> None:
        super().__init__(comparison_labels, image_id_to_geohash)
        self._size_keys: List[Tuple[str, str]] = []
        self._category_keys: List[Tuple[str, str]] = []

        for comparison_label in comparison_labels:
            self._add(
                id=comparison_label.id,
                match=comparison_label.match,
                same_geo=(
                    default_string(image_id_to_geohash.get(comparison_label.image_one))[:6]
                    == default_string(image_id_to_geohash.get(comparison_label.image_two))[:6]
                ),
                size_key=get_size_key(
                    (px2mm(comparison_label.label_one_radius), px2mm(comparison_label.label_two_radius))
                ),
                category_key=get_category_key(
                    (comparison_label.label_one_category, comparison_label.label_two_category)
                ),
            )

        self.group_small_buckets()

    def query(self) -> str:
        match = random.random() < 0.5
        same_geo = random.random() < 0.1

        size_bucket = random.choice(list(self._sampler[match][same_geo].keys()))
        item = random.choice(self._sampler[match][same_geo][size_bucket])
        return str(item)

    def _add(
        self, *, match: bool, same_geo: bool, size_key: Tuple[str, str], category_key: Tuple[str, str], id: str
    ) -> None:
        root = self._sampler
        for key in [match, same_geo]:
            if key not in root:
                root[key] = {}
            root = root[key]

        if size_key not in root:
            root[size_key] = []

        root[size_key].append(id)

    def group_small_buckets(self, min_size: int = 100) -> None:
        for match, same_geo_buckets in self._sampler.items():
            for same_geo, size_buckets in same_geo_buckets.items():
                to_merge = []
                to_remove = []
                for size, items in size_buckets.items():
                    if len(items) < min_size:
                        to_merge.extend(items)
                        to_remove.append(size)
                self._sampler[match][same_geo]["merged"] = to_merge
                for size in to_remove:
                    del self._sampler[match][same_geo][size]

    def __str__(self) -> str:
        return_string = ""

        for match, same_geo_buckets in self._sampler.items():
            for same_geo, size_buckets in same_geo_buckets.items():
                for size, items in size_buckets.items():
                    return_string += f"Key: {(match, same_geo, size)}, Count: {len(items)}\n"

        return return_string


class ComparisonLabelSamplerCategory(ComparisonLabelSampler):
    def __init__(
        self, comparison_labels: List[ComparisonLabels], image_id_to_geohash: Dict[str, Optional[str]]
    ) -> None:
        # 50/50 match, 1/8 for each weed, 1/2 for something that doesn't include any weeds, sample 50/50 same category
        super().__init__(comparison_labels, image_id_to_geohash)
        self._size_keys: List[Tuple[str, str]] = []
        self._category_keys: List[Tuple[str, str]] = []
        weed_categories = ["broadleaf", "grass", "offshoot", "purslane"]

        comparison_labels_dicts = [comp_label.to_dict() for comp_label in comparison_labels]
        for comp_label in comparison_labels_dicts:
            for weed in weed_categories:
                comp_label[f"contains_{weed}"] = (
                    comp_label["label_one_category"] == weed or comp_label["label_two_category"] == weed
                )
            comp_label["same_category"] = comp_label["label_one_category"] == comp_label["label_two_category"]
            comp_label["size_key"] = get_size_key(
                (px2mm(comp_label["label_one_radius"]), px2mm(comp_label["label_two_radius"]))
            )

        self._headers = [f"contains_{weed}" for weed in weed_categories]
        self._headers.extend(["id", "same_category", "size_key", "match"])
        self._sampler_df = pd.DataFrame(columns=self._headers, data=comparison_labels_dicts)

    def query(self) -> str:
        while True:
            items_df = self._sampler_df
            match = random.random() < 0.5

            items_df = items_df[items_df["match"] == match]

            category_sampling = random.random()

            if category_sampling < 0.5:
                random_contain = random.choice(
                    ["contains_broadleaf", "contains_offshoot", "contains_grass", "contains_purslane"]
                )
                items_df = items_df[items_df[random_contain].isin([True])]
            else:
                items_df = items_df[
                    items_df["contains_broadleaf"].isin([False])
                    & items_df["contains_grass"].isin([False])
                    & items_df["contains_offshoot"].isin([False])
                    & items_df["contains_purslane"].isin([False])
                ]

            if len(items_df) == 0:
                item = self._sampler_df.sample().iloc[0]
            else:
                item = items_df.sample().iloc[0]

            return str(item["id"])

    def __str__(self) -> str:
        return_string = ""

        for match in [True, False]:
            item_df = self._sampler_df[self._sampler_df["match"] == match]

            number_broadleaf = item_df[item_df["contains_broadleaf"]]
            number_offshoot = item_df[item_df["contains_offshoot"]]
            number_grass = item_df[item_df["contains_grass"]]
            number_purslane = item_df[item_df["contains_purslane"]]
            number_none = item_df[
                item_df["contains_broadleaf"].isin([False])
                & item_df["contains_offshoot"].isin([False])
                & item_df["contains_grass"].isin([False])
                & item_df["contains_purslane"].isin([False])
            ]

            broadleaf_and_same_category = number_broadleaf[number_broadleaf["same_category"].isin([True])]
            grass_and_same_category = number_grass[number_grass["same_category"].isin([True])]
            offshoot_and_same_category = number_offshoot[number_offshoot["same_category"].isin([True])]
            purslane_and_same_category = number_purslane[number_purslane["same_category"].isin([True])]
            none_and_same_category = number_none[number_none["same_category"].isin([True])]

            return_string += f"Key: {match}, Count: {self._sampler_df[self._sampler_df['match'] == match].shape[0]}\n"
            return_string += f"     number_broadleaf: {number_broadleaf.shape[0]}, same category {broadleaf_and_same_category.shape[0]}\n"
            return_string += f"     number_offshoot: {number_offshoot.shape[0]}, same category {offshoot_and_same_category.shape[0]}\n"
            return_string += (
                f"     number_grass: {number_grass.shape[0]}, same category {grass_and_same_category.shape[0]}\n"
            )
            return_string += f"     number_purslane: {number_purslane.shape[0]}, same category {purslane_and_same_category.shape[0]}\n"
            return_string += (
                f"     number_none: {number_none.shape[0]}, same category {none_and_same_category.shape[0]}\n"
            )

        return return_string


class ComparisonLabelSamplerHardExample(ComparisonLabelSampler):
    def __init__(
        self, comparison_labels: List[ComparisonLabels], image_id_to_geohash: Dict[str, Optional[str]]
    ) -> None:
        super().__init__(comparison_labels, image_id_to_geohash)
        self._size_keys: List[Tuple[str, str]] = []
        self._category_keys: List[Tuple[str, str]] = []

        comparison_labels_dicts = [comp_label.to_dict() for comp_label in comparison_labels]
        for comp_label in comparison_labels_dicts:
            category_key = get_category_key((comp_label["label_one_category"], comp_label["label_two_category"]))
            comp_label["category_key"] = category_key
            comp_label["size_key"] = get_size_key(
                (px2mm(comp_label["label_one_radius"]), px2mm(comp_label["label_two_radius"]))
            )

            self._category_keys.append(category_key)

        headers = ["id", "size_key", "match", "category_key"]
        self._sampler_df = pd.DataFrame(columns=headers, data=comparison_labels_dicts)

        cat_keys = list(set(self._category_keys))

        # Sample based on match and category
        self._sample_weights: Dict[str, List[Any]] = {
            "categories": [category for category in cat_keys],
            "weights": [1.0 for _ in cat_keys],
        }

    def query(self) -> str:
        while True:
            category_key: Tuple[str, str] = random.choices(
                self._sample_weights["categories"], weights=self._sample_weights["weights"], k=1
            )[0]

            items_category = self._sampler_df[self._sampler_df["category_key"] == category_key]
            match_value = random.random() < 0.5
            items = items_category[items_category["match"].isin([match_value])]
            if items.shape[0] > 0:
                item = items.sample().iloc[0]
                break

        return str(item["id"])

    def update_probabilities(self, results: List[Dict[str, Any]]) -> None:
        logging.info("Updating probabilities")
        sampleable_weights = {
            self._sample_weights["categories"][i]: self._sample_weights["weights"][i]
            for i in range(len(self._sample_weights["categories"]))
        }

        number_with_low_counts = 0
        for result in results:
            if result["count"] >= 10:
                accuracy = result["accuracy"]

                weight = 2 ** (3 * (1 - accuracy))
            else:
                weight = 1
                number_with_low_counts += 1
            sampleable_weights[result["category_key"]] = weight

        category_keys = list(sampleable_weights.keys())

        self._sample_weights["categories"] = category_keys
        self._sample_weights["weights"] = [sampleable_weights[cat_key] for cat_key in category_keys]
        logging.info("Updated probabilities")

        sorted_categories = sorted(list(sampleable_weights.items()), key=lambda x: int(x[1]))
        logging.info(f"     Lowest weights {sorted_categories[:10]}")
        logging.info(f"     Highest weights {sorted_categories[-10:]}")
        logging.info(f"     Number with low counts {number_with_low_counts}")

    def __str__(self) -> str:
        return ""


class ComparisonLabelSamplerSource(ComparisonLabelSampler):
    def __init__(
        self,
        comparison_labels: List[ComparisonLabels],
        image_id_to_geohash: Dict[str, Optional[str]],
        sampler_source_weights: Dict[str, float],
        sampler: Type[ComparisonLabelSampler],
    ) -> None:
        super().__init__(comparison_labels, image_id_to_geohash)
        self._sample_weights: Dict[str, List[Any]] = defaultdict(list)
        self._comparison_labels_by_source: Dict[str, List[ComparisonLabels]] = defaultdict(list)
        for source, weight in sampler_source_weights.items():
            self._sample_weights["sources"].append(source)
            self._sample_weights["weights"].append(weight)

        for comp_label in comparison_labels:
            source = comp_label.source
            assert comp_label.source in set(
                self._sample_weights["sources"]
            ), f"{source} must be in {self._sample_weights['sources']}."
            self._comparison_labels_by_source[source].append(comp_label)

        for source in sampler_source_weights:
            self._sampler[source] = sampler(
                comparison_labels=self._comparison_labels_by_source[source], image_id_to_geohash=image_id_to_geohash
            )

    def query(self) -> str:
        source: Tuple[str, str] = random.choices(
            self._sample_weights["sources"], weights=self._sample_weights["weights"], k=1
        )[0]

        return str(self._sampler[source].query())

    def __str__(self) -> str:
        return_string = ""

        for source, sampler in self._sampler.items():
            return_string += f"Source: {source}\n"
            return_string += f"{str(sampler)}\n"

        return return_string
