# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/weeding_diagnostics.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.core.controls.exterminator.controllers.aimbot.process.proto import aimbot_pb2 as core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2
from generated.cv.runtime.proto import cv_runtime_pb2 as cv_dot_runtime_dot_proto_dot_cv__runtime__pb2
from generated.frontend.proto import image_stream_pb2 as frontend_dot_proto_dot_image__stream__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2
from generated.proto.thinning import thinning_pb2 as proto_dot_thinning_dot_thinning__pb2
from generated.recorder.proto import recorder_pb2 as recorder_dot_proto_dot_recorder__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/weeding_diagnostics.proto',
  package='carbon.frontend.weeding_diagnostics',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n(frontend/proto/weeding_diagnostics.proto\x12#carbon.frontend.weeding_diagnostics\x1aHcore/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto\x1a!cv/runtime/proto/cv_runtime.proto\x1a!frontend/proto/image_stream.proto\x1a\x19\x66rontend/proto/util.proto\x1a\'weed_tracking/proto/weed_tracking.proto\x1a\x1dproto/thinning/thinning.proto\x1a\x1drecorder/proto/recorder.proto\"z\n\x1fRecordWeedingDiagnosticsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07ttl_sec\x18\x02 \x01(\r\x12\x1b\n\x13\x63rop_images_per_sec\x18\x03 \x01(\x02\x12\x1b\n\x13weed_images_per_sec\x18\x04 \x01(\x02\"/\n\x1dGetCurrentTrajectoriesRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\r\"\x1a\n\x18GetRecordingsListRequest\")\n\x19GetRecordingsListResponse\x12\x0c\n\x04name\x18\x01 \x03(\t\"U\n\x12GetSnapshotRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x17\n\x0fsnapshot_number\x18\x03 \x01(\r\"K\n\x13GetSnapshotResponse\x12\x34\n\x08snapshot\x18\x01 \x01(\x0b\x32\".weed_tracking.DiagnosticsSnapshot\".\n\x14OpenRecordingRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\"%\n\x16TrajectoriesWithImages\x12\x0b\n\x03ids\x18\x01 \x03(\r\"\x1e\n\rPredictImages\x12\r\n\x05names\x18\x01 \x03(\t\"\xce\x01\n\x13PredictImagesPerCam\x12T\n\x06images\x18\x01 \x03(\x0b\x32\x44.carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry\x1a\x61\n\x0bImagesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x41\n\x05value\x18\x02 \x01(\x0b\x32\x32.carbon.frontend.weeding_diagnostics.PredictImages:\x02\x38\x01\"\xf9\x05\n\x15OpenRecordingResponse\x12q\n\x15num_snapshots_per_row\x18\x01 \x03(\x0b\x32R.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry\x12P\n\x0erecording_data\x18\x02 \x01(\x0b\x32\x38.carbon.frontend.weeding_diagnostics.StaticRecordingData\x12y\n\x19trajectory_images_per_row\x18\x03 \x03(\x0b\x32V.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry\x12s\n\x16predict_images_per_row\x18\x04 \x03(\x0b\x32S.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry\x1a\x39\n\x17NumSnapshotsPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r:\x02\x38\x01\x1az\n\x1bTrajectoryImagesPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12J\n\x05value\x18\x02 \x01(\x0b\x32;.carbon.frontend.weeding_diagnostics.TrajectoriesWithImages:\x02\x38\x01\x1at\n\x18PredictImagesPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12G\n\x05value\x18\x02 \x01(\x0b\x32\x38.carbon.frontend.weeding_diagnostics.PredictImagesPerCam:\x02\x38\x01\"0\n\x16\x44\x65leteRecordingRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\"\xe2\x02\n\x12\x43onfigNodeSnapshot\x12S\n\x06values\x18\x01 \x03(\x0b\x32\x43.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry\x12\\\n\x0b\x63hild_nodes\x18\x02 \x03(\x0b\x32G.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry\x1a-\n\x0bValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1aj\n\x0f\x43hildNodesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x46\n\x05value\x18\x02 \x01(\x0b\x32\x37.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot:\x02\x38\x01\"1\n\x10\x43\x61meraDimensions\x12\r\n\x05width\x18\x01 \x01(\r\x12\x0e\n\x06height\x18\x02 \x01(\r\"\xb9\x01\n\nRowCameras\x12G\n\x04\x63\x61ms\x18\x01 \x03(\x0b\x32\x39.carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry\x1a\x62\n\tCamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x44\n\x05value\x18\x02 \x01(\x0b\x32\x35.carbon.frontend.weeding_diagnostics.CameraDimensions:\x02\x38\x01\"\xff\x08\n\x13StaticRecordingData\x12\x63\n\x0elasers_enabled\x18\x01 \x03(\x0b\x32K.carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry\x12L\n\x0broot_config\x18\x02 \x01(\x0b\x32\x37.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot\x12\x15\n\rrows_recorded\x18\x03 \x03(\x05\x12\x63\n\x0erow_dimensions\x18\x04 \x03(\x0b\x32K.carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry\x12}\n\x1d\x63rop_safety_radius_mm_per_row\x18\x05 \x03(\x0b\x32V.carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry\x12:\n\x0fthinning_config\x18\x06 \x01(\x0b\x32!.carbon.thinning.ConfigDefinition\x12\x1b\n\x13recording_timestamp\x18\x07 \x01(\x03\x12]\n\x0brow_cameras\x18\x08 \x03(\x0b\x32H.carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry\x12\x1c\n\x14weed_point_threshold\x18\t \x01(\x02\x12\x1c\n\x14\x63rop_point_threshold\x18\n \x01(\x02\x12#\n\x1bwheel_diameter_back_left_in\x18\x0b \x01(\x02\x12$\n\x1cwheel_diameter_back_right_in\x18\x0c \x01(\x02\x12$\n\x1cwheel_diameter_front_left_in\x18\r \x01(\x02\x12%\n\x1dwheel_diameter_front_right_in\x18\x0e \x01(\x02\x1a\x34\n\x12LasersEnabledEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\x1aS\n\x12RowDimensionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12,\n\x05value\x18\x02 \x01(\x0b\x32\x1d.aimbot.GetDimensionsResponse:\x02\x38\x01\x1a?\n\x1d\x43ropSafetyRadiusMmPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x1a\x62\n\x0fRowCamerasEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12>\n\x05value\x18\x02 \x01(\x0b\x32/.carbon.frontend.weeding_diagnostics.RowCameras:\x02\x38\x01\"Y\n\x18GetTrajectoryDataRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x15\n\rtrajectory_id\x18\x03 \x01(\r\"j\n\x0cLastSnapshot\x12\x35\n\ntrajectory\x18\x01 \x01(\x0b\x32!.weed_tracking.TrajectorySnapshot\x12#\n\x1b\x64iagnostics_snapshot_number\x18\x02 \x01(\r\"C\n\x14\x45xtraTrajectoryField\x12\r\n\x05label\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\r\n\x05\x63olor\x18\x03 \x01(\t\"\\\n\x0bTargetImage\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\ttimestamp\x18\x02 \x01(\x04\x12\x15\n\rp2p_predict_x\x18\x03 \x01(\r\x12\x15\n\rp2p_predict_y\x18\x04 \x01(\r\"\x9a\x01\n\x0fP2PPredictImage\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x63\x65nter_x_px\x18\x02 \x01(\x05\x12\x13\n\x0b\x63\x65nter_y_px\x18\x03 \x01(\x05\x12\x11\n\tradius_px\x18\x04 \x01(\x05\x12+\n\x02ts\x18\x05 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0f\n\x07pcam_id\x18\x06 \x01(\t\"\x9b\x01\n\x1eTrajectoryPredictImageMetadata\x12\x13\n\x0b\x63\x65nter_x_px\x18\x01 \x01(\x05\x12\x13\n\x0b\x63\x65nter_y_px\x18\x02 \x01(\x05\x12\x11\n\tradius_px\x18\x03 \x01(\x05\x12+\n\x02ts\x18\x04 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0f\n\x07pcam_id\x18\x05 \x01(\t\"\xcc\x03\n\x0eTrajectoryData\x12Z\n\rpredict_image\x18\x01 \x01(\x0b\x32\x43.carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata\x12G\n\rtarget_images\x18\x02 \x03(\x0b\x32\x30.carbon.frontend.weeding_diagnostics.TargetImage\x12H\n\rlast_snapshot\x18\x03 \x01(\x0b\x32\x31.carbon.frontend.weeding_diagnostics.LastSnapshot\x12O\n\x0c\x65xtra_fields\x18\x04 \x03(\x0b\x32\x39.carbon.frontend.weeding_diagnostics.ExtraTrajectoryField\x12\x13\n\x0b\x63rosshair_x\x18\x05 \x01(\r\x12\x13\n\x0b\x63rosshair_y\x18\x06 \x01(\r\x12P\n\x12p2p_predict_images\x18\x07 \x03(\x0b\x32\x34.carbon.frontend.weeding_diagnostics.P2PPredictImage\"a\n GetTrajectoryPredictImageRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x15\n\rtrajectory_id\x18\x03 \x01(\r\"t\n\x1fGetTrajectoryTargetImageRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x15\n\rtrajectory_id\x18\x03 \x01(\r\x12\x12\n\nimage_name\x18\x04 \x01(\t\"!\n\nImageChunk\x12\x13\n\x0bimage_chunk\x18\x01 \x01(\x0c\"\\\n\x1eGetPredictImageMetadataRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x12\n\nimage_name\x18\x03 \x01(\t\"\x94\x02\n\x1fGetPredictImageMetadataResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12>\n\x0b\x61nnotations\x18\x02 \x01(\x0b\x32).carbon.frontend.image_stream.Annotations\x12\x39\n\x0f\x64\x65\x65pweed_output\x18\x03 \x01(\x0b\x32 .cv.runtime.proto.DeepweedOutput\x12I\n\x1f\x64\x65\x65pweed_output_below_threshold\x18\x04 \x01(\x0b\x32 .cv.runtime.proto.DeepweedOutput\"T\n\x16GetPredictImageRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x12\n\nimage_name\x18\x03 \x01(\t\",\n\x12StartUploadRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\"`\n\x19GetNextUploadStateRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\xab\x01\n\x1aGetNextUploadStateResponse\x12\x46\n\x0cupload_state\x18\x01 \x01(\x0e\x32\x30.carbon.frontend.weeding_diagnostics.UploadState\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x18\n\x10percent_uploaded\x18\x03 \x01(\r\"Y\n\"GetDeepweedPredictionsCountRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0b\n\x03row\x18\x02 \x01(\r\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\r\"4\n#GetDeepweedPredictionsCountResponse\x12\r\n\x05\x63ount\x18\x01 \x01(\r\"a\n\x1dGetDeepweedPredictionsRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0b\n\x03row\x18\x02 \x01(\r\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\r\x12\x0b\n\x03idx\x18\x04 \x01(\r\"Y\n\x1eGetDeepweedPredictionsResponse\x12\x37\n\x0bpredictions\x18\x01 \x01(\x0b\x32\".recorder.DeepweedPredictionRecord\"V\n\x15\x46indTrajectoryRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x15\n\rtrajectory_id\x18\x03 \x01(\r\"d\n\x16\x46indTrajectoryResponse\x12\x13\n\x0bsnapshot_id\x18\x01 \x01(\r\x12\x35\n\ntrajectory\x18\x02 \x01(\x0b\x32!.weed_tracking.TrajectorySnapshot\"?\n\x15GetRotaryTicksRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\"h\n\x16GetRotaryTicksResponse\x12,\n\x07records\x18\x01 \x03(\x0b\x32\x1b.recorder.RotaryTicksRecord\x12 \n\x18wheel_encoder_resolution\x18\x02 \x01(\r\".\n\x1cSnapshotPredictImagesRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\r\"5\n\x0cPcamSnapshot\x12\x0f\n\x07pcam_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"e\n\x1dSnapshotPredictImagesResponse\x12\x44\n\tsnapshots\x18\x01 \x03(\x0b\x32\x31.carbon.frontend.weeding_diagnostics.PcamSnapshot\"\x80\x01\n\x1dGetChipForPredictImageRequest\x12\x0f\n\x07pcam_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x13\n\x0b\x63\x65nter_x_px\x18\x03 \x01(\x05\x12\x13\n\x0b\x63\x65nter_y_px\x18\x04 \x01(\x05\x12\x0e\n\x06row_id\x18\x05 \x01(\r\"4\n\x1eGetChipForPredictImageResponse\x12\x12\n\nchip_image\x18\x01 \x01(\x0c*2\n\x0bUploadState\x12\x08\n\x04NONE\x10\x00\x12\x0f\n\x0bIN_PROGRESS\x10\x01\x12\x08\n\x04\x44ONE\x10\x02\x32\xbf\x15\n\x19WeedingDiagnosticsService\x12}\n\x18RecordWeedingDiagnostics\x12\x44.carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x80\x01\n\x16GetCurrentTrajectories\x12\x42.carbon.frontend.weeding_diagnostics.GetCurrentTrajectoriesRequest\x1a\".weed_tracking.DiagnosticsSnapshot\x12\x92\x01\n\x11GetRecordingsList\x12=.carbon.frontend.weeding_diagnostics.GetRecordingsListRequest\x1a>.carbon.frontend.weeding_diagnostics.GetRecordingsListResponse\x12\x86\x01\n\rOpenRecording\x12\x39.carbon.frontend.weeding_diagnostics.OpenRecordingRequest\x1a:.carbon.frontend.weeding_diagnostics.OpenRecordingResponse\x12\x80\x01\n\x0bGetSnapshot\x12\x37.carbon.frontend.weeding_diagnostics.GetSnapshotRequest\x1a\x38.carbon.frontend.weeding_diagnostics.GetSnapshotResponse\x12k\n\x0f\x44\x65leteRecording\x12;.carbon.frontend.weeding_diagnostics.DeleteRecordingRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x87\x01\n\x11GetTrajectoryData\x12=.carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest\x1a\x33.carbon.frontend.weeding_diagnostics.TrajectoryData\x12\x95\x01\n\x19GetTrajectoryPredictImage\x12\x45.carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest\x1a/.carbon.frontend.weeding_diagnostics.ImageChunk0\x01\x12\x93\x01\n\x18GetTrajectoryTargetImage\x12\x44.carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest\x1a/.carbon.frontend.weeding_diagnostics.ImageChunk0\x01\x12\xa4\x01\n\x17GetPredictImageMetadata\x12\x43.carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest\x1a\x44.carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse\x12\x81\x01\n\x0fGetPredictImage\x12;.carbon.frontend.weeding_diagnostics.GetPredictImageRequest\x1a/.carbon.frontend.weeding_diagnostics.ImageChunk0\x01\x12\x63\n\x0bStartUpload\x12\x37.carbon.frontend.weeding_diagnostics.StartUploadRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x95\x01\n\x12GetNextUploadState\x12>.carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest\x1a?.carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse\x12\xb0\x01\n\x1bGetDeepweedPredictionsCount\x12G.carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest\x1aH.carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountResponse\x12\xa1\x01\n\x16GetDeepweedPredictions\x12\x42.carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest\x1a\x43.carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsResponse\x12\x89\x01\n\x0e\x46indTrajectory\x12:.carbon.frontend.weeding_diagnostics.FindTrajectoryRequest\x1a;.carbon.frontend.weeding_diagnostics.FindTrajectoryResponse\x12\x89\x01\n\x0eGetRotaryTicks\x12:.carbon.frontend.weeding_diagnostics.GetRotaryTicksRequest\x1a;.carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse\x12\x9e\x01\n\x15SnapshotPredictImages\x12\x41.carbon.frontend.weeding_diagnostics.SnapshotPredictImagesRequest\x1a\x42.carbon.frontend.weeding_diagnostics.SnapshotPredictImagesResponse\x12\xa1\x01\n\x16GetChipForPredictImage\x12\x42.carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest\x1a\x43.carbon.frontend.weeding_diagnostics.GetChipForPredictImageResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2.DESCRIPTOR,cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.DESCRIPTOR,frontend_dot_proto_dot_image__stream__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,weed__tracking_dot_proto_dot_weed__tracking__pb2.DESCRIPTOR,proto_dot_thinning_dot_thinning__pb2.DESCRIPTOR,recorder_dot_proto_dot_recorder__pb2.DESCRIPTOR,])

_UPLOADSTATE = _descriptor.EnumDescriptor(
  name='UploadState',
  full_name='carbon.frontend.weeding_diagnostics.UploadState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IN_PROGRESS', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DONE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=6911,
  serialized_end=6961,
)
_sym_db.RegisterEnumDescriptor(_UPLOADSTATE)

UploadState = enum_type_wrapper.EnumTypeWrapper(_UPLOADSTATE)
NONE = 0
IN_PROGRESS = 1
DONE = 2



_RECORDWEEDINGDIAGNOSTICSREQUEST = _descriptor.Descriptor(
  name='RecordWeedingDiagnosticsRequest',
  full_name='carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ttl_sec', full_name='carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest.ttl_sec', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_images_per_sec', full_name='carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest.crop_images_per_sec', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_images_per_sec', full_name='carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest.weed_images_per_sec', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=355,
  serialized_end=477,
)


_GETCURRENTTRAJECTORIESREQUEST = _descriptor.Descriptor(
  name='GetCurrentTrajectoriesRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetCurrentTrajectoriesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetCurrentTrajectoriesRequest.row_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=479,
  serialized_end=526,
)


_GETRECORDINGSLISTREQUEST = _descriptor.Descriptor(
  name='GetRecordingsListRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetRecordingsListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=528,
  serialized_end=554,
)


_GETRECORDINGSLISTRESPONSE = _descriptor.Descriptor(
  name='GetRecordingsListResponse',
  full_name='carbon.frontend.weeding_diagnostics.GetRecordingsListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.weeding_diagnostics.GetRecordingsListResponse.name', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=556,
  serialized_end=597,
)


_GETSNAPSHOTREQUEST = _descriptor.Descriptor(
  name='GetSnapshotRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetSnapshotRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetSnapshotRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetSnapshotRequest.row_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='snapshot_number', full_name='carbon.frontend.weeding_diagnostics.GetSnapshotRequest.snapshot_number', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=599,
  serialized_end=684,
)


_GETSNAPSHOTRESPONSE = _descriptor.Descriptor(
  name='GetSnapshotResponse',
  full_name='carbon.frontend.weeding_diagnostics.GetSnapshotResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='snapshot', full_name='carbon.frontend.weeding_diagnostics.GetSnapshotResponse.snapshot', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=686,
  serialized_end=761,
)


_OPENRECORDINGREQUEST = _descriptor.Descriptor(
  name='OpenRecordingRequest',
  full_name='carbon.frontend.weeding_diagnostics.OpenRecordingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=763,
  serialized_end=809,
)


_TRAJECTORIESWITHIMAGES = _descriptor.Descriptor(
  name='TrajectoriesWithImages',
  full_name='carbon.frontend.weeding_diagnostics.TrajectoriesWithImages',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='carbon.frontend.weeding_diagnostics.TrajectoriesWithImages.ids', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=811,
  serialized_end=848,
)


_PREDICTIMAGES = _descriptor.Descriptor(
  name='PredictImages',
  full_name='carbon.frontend.weeding_diagnostics.PredictImages',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='names', full_name='carbon.frontend.weeding_diagnostics.PredictImages.names', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=850,
  serialized_end=880,
)


_PREDICTIMAGESPERCAM_IMAGESENTRY = _descriptor.Descriptor(
  name='ImagesEntry',
  full_name='carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=992,
  serialized_end=1089,
)

_PREDICTIMAGESPERCAM = _descriptor.Descriptor(
  name='PredictImagesPerCam',
  full_name='carbon.frontend.weeding_diagnostics.PredictImagesPerCam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='images', full_name='carbon.frontend.weeding_diagnostics.PredictImagesPerCam.images', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_PREDICTIMAGESPERCAM_IMAGESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=883,
  serialized_end=1089,
)


_OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY = _descriptor.Descriptor(
  name='NumSnapshotsPerRowEntry',
  full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry.key', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry.value', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1554,
  serialized_end=1611,
)

_OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY = _descriptor.Descriptor(
  name='TrajectoryImagesPerRowEntry',
  full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry.key', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1613,
  serialized_end=1735,
)

_OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY = _descriptor.Descriptor(
  name='PredictImagesPerRowEntry',
  full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry.key', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1737,
  serialized_end=1853,
)

_OPENRECORDINGRESPONSE = _descriptor.Descriptor(
  name='OpenRecordingResponse',
  full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_snapshots_per_row', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.num_snapshots_per_row', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recording_data', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.recording_data', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory_images_per_row', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.trajectory_images_per_row', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_images_per_row', full_name='carbon.frontend.weeding_diagnostics.OpenRecordingResponse.predict_images_per_row', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY, _OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY, _OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1092,
  serialized_end=1853,
)


_DELETERECORDINGREQUEST = _descriptor.Descriptor(
  name='DeleteRecordingRequest',
  full_name='carbon.frontend.weeding_diagnostics.DeleteRecordingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.DeleteRecordingRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1855,
  serialized_end=1903,
)


_CONFIGNODESNAPSHOT_VALUESENTRY = _descriptor.Descriptor(
  name='ValuesEntry',
  full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2107,
  serialized_end=2152,
)

_CONFIGNODESNAPSHOT_CHILDNODESENTRY = _descriptor.Descriptor(
  name='ChildNodesEntry',
  full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2154,
  serialized_end=2260,
)

_CONFIGNODESNAPSHOT = _descriptor.Descriptor(
  name='ConfigNodeSnapshot',
  full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='values', full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.values', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='child_nodes', full_name='carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.child_nodes', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_CONFIGNODESNAPSHOT_VALUESENTRY, _CONFIGNODESNAPSHOT_CHILDNODESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1906,
  serialized_end=2260,
)


_CAMERADIMENSIONS = _descriptor.Descriptor(
  name='CameraDimensions',
  full_name='carbon.frontend.weeding_diagnostics.CameraDimensions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='width', full_name='carbon.frontend.weeding_diagnostics.CameraDimensions.width', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='carbon.frontend.weeding_diagnostics.CameraDimensions.height', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2262,
  serialized_end=2311,
)


_ROWCAMERAS_CAMSENTRY = _descriptor.Descriptor(
  name='CamsEntry',
  full_name='carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2401,
  serialized_end=2499,
)

_ROWCAMERAS = _descriptor.Descriptor(
  name='RowCameras',
  full_name='carbon.frontend.weeding_diagnostics.RowCameras',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cams', full_name='carbon.frontend.weeding_diagnostics.RowCameras.cams', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_ROWCAMERAS_CAMSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2314,
  serialized_end=2499,
)


_STATICRECORDINGDATA_LASERSENABLEDENTRY = _descriptor.Descriptor(
  name='LasersEnabledEntry',
  full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry.value', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3351,
  serialized_end=3403,
)

_STATICRECORDINGDATA_ROWDIMENSIONSENTRY = _descriptor.Descriptor(
  name='RowDimensionsEntry',
  full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry.key', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3405,
  serialized_end=3488,
)

_STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY = _descriptor.Descriptor(
  name='CropSafetyRadiusMmPerRowEntry',
  full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry.key', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3490,
  serialized_end=3553,
)

_STATICRECORDINGDATA_ROWCAMERASENTRY = _descriptor.Descriptor(
  name='RowCamerasEntry',
  full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry.key', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3555,
  serialized_end=3653,
)

_STATICRECORDINGDATA = _descriptor.Descriptor(
  name='StaticRecordingData',
  full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lasers_enabled', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.lasers_enabled', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='root_config', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.root_config', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rows_recorded', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.rows_recorded', index=2,
      number=3, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_dimensions', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.row_dimensions', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_safety_radius_mm_per_row', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.crop_safety_radius_mm_per_row', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_config', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.thinning_config', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recording_timestamp', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.recording_timestamp', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_cameras', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.row_cameras', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_point_threshold', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.weed_point_threshold', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_point_threshold', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.crop_point_threshold', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_diameter_back_left_in', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.wheel_diameter_back_left_in', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_diameter_back_right_in', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.wheel_diameter_back_right_in', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_diameter_front_left_in', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.wheel_diameter_front_left_in', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_diameter_front_right_in', full_name='carbon.frontend.weeding_diagnostics.StaticRecordingData.wheel_diameter_front_right_in', index=13,
      number=14, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_STATICRECORDINGDATA_LASERSENABLEDENTRY, _STATICRECORDINGDATA_ROWDIMENSIONSENTRY, _STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY, _STATICRECORDINGDATA_ROWCAMERASENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2502,
  serialized_end=3653,
)


_GETTRAJECTORYDATAREQUEST = _descriptor.Descriptor(
  name='GetTrajectoryDataRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest.row_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest.trajectory_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3655,
  serialized_end=3744,
)


_LASTSNAPSHOT = _descriptor.Descriptor(
  name='LastSnapshot',
  full_name='carbon.frontend.weeding_diagnostics.LastSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='trajectory', full_name='carbon.frontend.weeding_diagnostics.LastSnapshot.trajectory', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diagnostics_snapshot_number', full_name='carbon.frontend.weeding_diagnostics.LastSnapshot.diagnostics_snapshot_number', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3746,
  serialized_end=3852,
)


_EXTRATRAJECTORYFIELD = _descriptor.Descriptor(
  name='ExtraTrajectoryField',
  full_name='carbon.frontend.weeding_diagnostics.ExtraTrajectoryField',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='label', full_name='carbon.frontend.weeding_diagnostics.ExtraTrajectoryField.label', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.weeding_diagnostics.ExtraTrajectoryField.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='color', full_name='carbon.frontend.weeding_diagnostics.ExtraTrajectoryField.color', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3854,
  serialized_end=3921,
)


_TARGETIMAGE = _descriptor.Descriptor(
  name='TargetImage',
  full_name='carbon.frontend.weeding_diagnostics.TargetImage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.weeding_diagnostics.TargetImage.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='carbon.frontend.weeding_diagnostics.TargetImage.timestamp', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p_predict_x', full_name='carbon.frontend.weeding_diagnostics.TargetImage.p2p_predict_x', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p_predict_y', full_name='carbon.frontend.weeding_diagnostics.TargetImage.p2p_predict_y', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3923,
  serialized_end=4015,
)


_P2PPREDICTIMAGE = _descriptor.Descriptor(
  name='P2PPredictImage',
  full_name='carbon.frontend.weeding_diagnostics.P2PPredictImage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.weeding_diagnostics.P2PPredictImage.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_x_px', full_name='carbon.frontend.weeding_diagnostics.P2PPredictImage.center_x_px', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_y_px', full_name='carbon.frontend.weeding_diagnostics.P2PPredictImage.center_y_px', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='radius_px', full_name='carbon.frontend.weeding_diagnostics.P2PPredictImage.radius_px', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.weeding_diagnostics.P2PPredictImage.ts', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pcam_id', full_name='carbon.frontend.weeding_diagnostics.P2PPredictImage.pcam_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4018,
  serialized_end=4172,
)


_TRAJECTORYPREDICTIMAGEMETADATA = _descriptor.Descriptor(
  name='TrajectoryPredictImageMetadata',
  full_name='carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='center_x_px', full_name='carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata.center_x_px', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_y_px', full_name='carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata.center_y_px', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='radius_px', full_name='carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata.radius_px', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata.ts', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pcam_id', full_name='carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata.pcam_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4175,
  serialized_end=4330,
)


_TRAJECTORYDATA = _descriptor.Descriptor(
  name='TrajectoryData',
  full_name='carbon.frontend.weeding_diagnostics.TrajectoryData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='predict_image', full_name='carbon.frontend.weeding_diagnostics.TrajectoryData.predict_image', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_images', full_name='carbon.frontend.weeding_diagnostics.TrajectoryData.target_images', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_snapshot', full_name='carbon.frontend.weeding_diagnostics.TrajectoryData.last_snapshot', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extra_fields', full_name='carbon.frontend.weeding_diagnostics.TrajectoryData.extra_fields', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crosshair_x', full_name='carbon.frontend.weeding_diagnostics.TrajectoryData.crosshair_x', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crosshair_y', full_name='carbon.frontend.weeding_diagnostics.TrajectoryData.crosshair_y', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p_predict_images', full_name='carbon.frontend.weeding_diagnostics.TrajectoryData.p2p_predict_images', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4333,
  serialized_end=4793,
)


_GETTRAJECTORYPREDICTIMAGEREQUEST = _descriptor.Descriptor(
  name='GetTrajectoryPredictImageRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest.row_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest.trajectory_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4795,
  serialized_end=4892,
)


_GETTRAJECTORYTARGETIMAGEREQUEST = _descriptor.Descriptor(
  name='GetTrajectoryTargetImageRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest.row_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest.trajectory_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='image_name', full_name='carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest.image_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4894,
  serialized_end=5010,
)


_IMAGECHUNK = _descriptor.Descriptor(
  name='ImageChunk',
  full_name='carbon.frontend.weeding_diagnostics.ImageChunk',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='image_chunk', full_name='carbon.frontend.weeding_diagnostics.ImageChunk.image_chunk', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5012,
  serialized_end=5045,
)


_GETPREDICTIMAGEMETADATAREQUEST = _descriptor.Descriptor(
  name='GetPredictImageMetadataRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest.row_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='image_name', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest.image_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5047,
  serialized_end=5139,
)


_GETPREDICTIMAGEMETADATARESPONSE = _descriptor.Descriptor(
  name='GetPredictImageMetadataResponse',
  full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='annotations', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse.annotations', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed_output', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse.deepweed_output', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed_output_below_threshold', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse.deepweed_output_below_threshold', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5142,
  serialized_end=5418,
)


_GETPREDICTIMAGEREQUEST = _descriptor.Descriptor(
  name='GetPredictImageRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetPredictImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageRequest.row_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='image_name', full_name='carbon.frontend.weeding_diagnostics.GetPredictImageRequest.image_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5420,
  serialized_end=5504,
)


_STARTUPLOADREQUEST = _descriptor.Descriptor(
  name='StartUploadRequest',
  full_name='carbon.frontend.weeding_diagnostics.StartUploadRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.StartUploadRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5506,
  serialized_end=5550,
)


_GETNEXTUPLOADSTATEREQUEST = _descriptor.Descriptor(
  name='GetNextUploadStateRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5552,
  serialized_end=5648,
)


_GETNEXTUPLOADSTATERESPONSE = _descriptor.Descriptor(
  name='GetNextUploadStateResponse',
  full_name='carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='upload_state', full_name='carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse.upload_state', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='percent_uploaded', full_name='carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse.percent_uploaded', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5651,
  serialized_end=5822,
)


_GETDEEPWEEDPREDICTIONSCOUNTREQUEST = _descriptor.Descriptor(
  name='GetDeepweedPredictionsCountRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest.row', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest.cam_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5824,
  serialized_end=5913,
)


_GETDEEPWEEDPREDICTIONSCOUNTRESPONSE = _descriptor.Descriptor(
  name='GetDeepweedPredictionsCountResponse',
  full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountResponse.count', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5915,
  serialized_end=5967,
)


_GETDEEPWEEDPREDICTIONSREQUEST = _descriptor.Descriptor(
  name='GetDeepweedPredictionsRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest.row', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest.cam_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='idx', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest.idx', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5969,
  serialized_end=6066,
)


_GETDEEPWEEDPREDICTIONSRESPONSE = _descriptor.Descriptor(
  name='GetDeepweedPredictionsResponse',
  full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='predictions', full_name='carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsResponse.predictions', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6068,
  serialized_end=6157,
)


_FINDTRAJECTORYREQUEST = _descriptor.Descriptor(
  name='FindTrajectoryRequest',
  full_name='carbon.frontend.weeding_diagnostics.FindTrajectoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.FindTrajectoryRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.FindTrajectoryRequest.row_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='carbon.frontend.weeding_diagnostics.FindTrajectoryRequest.trajectory_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6159,
  serialized_end=6245,
)


_FINDTRAJECTORYRESPONSE = _descriptor.Descriptor(
  name='FindTrajectoryResponse',
  full_name='carbon.frontend.weeding_diagnostics.FindTrajectoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='snapshot_id', full_name='carbon.frontend.weeding_diagnostics.FindTrajectoryResponse.snapshot_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory', full_name='carbon.frontend.weeding_diagnostics.FindTrajectoryResponse.trajectory', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6247,
  serialized_end=6347,
)


_GETROTARYTICKSREQUEST = _descriptor.Descriptor(
  name='GetRotaryTicksRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetRotaryTicksRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='recording_name', full_name='carbon.frontend.weeding_diagnostics.GetRotaryTicksRequest.recording_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetRotaryTicksRequest.row_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6349,
  serialized_end=6412,
)


_GETROTARYTICKSRESPONSE = _descriptor.Descriptor(
  name='GetRotaryTicksResponse',
  full_name='carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='records', full_name='carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse.records', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_encoder_resolution', full_name='carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse.wheel_encoder_resolution', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6414,
  serialized_end=6518,
)


_SNAPSHOTPREDICTIMAGESREQUEST = _descriptor.Descriptor(
  name='SnapshotPredictImagesRequest',
  full_name='carbon.frontend.weeding_diagnostics.SnapshotPredictImagesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.SnapshotPredictImagesRequest.row_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6520,
  serialized_end=6566,
)


_PCAMSNAPSHOT = _descriptor.Descriptor(
  name='PcamSnapshot',
  full_name='carbon.frontend.weeding_diagnostics.PcamSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pcam_id', full_name='carbon.frontend.weeding_diagnostics.PcamSnapshot.pcam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.frontend.weeding_diagnostics.PcamSnapshot.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6568,
  serialized_end=6621,
)


_SNAPSHOTPREDICTIMAGESRESPONSE = _descriptor.Descriptor(
  name='SnapshotPredictImagesResponse',
  full_name='carbon.frontend.weeding_diagnostics.SnapshotPredictImagesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='snapshots', full_name='carbon.frontend.weeding_diagnostics.SnapshotPredictImagesResponse.snapshots', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6623,
  serialized_end=6724,
)


_GETCHIPFORPREDICTIMAGEREQUEST = _descriptor.Descriptor(
  name='GetChipForPredictImageRequest',
  full_name='carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pcam_id', full_name='carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest.pcam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_x_px', full_name='carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest.center_x_px', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_y_px', full_name='carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest.center_y_px', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest.row_id', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6727,
  serialized_end=6855,
)


_GETCHIPFORPREDICTIMAGERESPONSE = _descriptor.Descriptor(
  name='GetChipForPredictImageResponse',
  full_name='carbon.frontend.weeding_diagnostics.GetChipForPredictImageResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='chip_image', full_name='carbon.frontend.weeding_diagnostics.GetChipForPredictImageResponse.chip_image', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6857,
  serialized_end=6909,
)

_GETSNAPSHOTRESPONSE.fields_by_name['snapshot'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._DIAGNOSTICSSNAPSHOT
_PREDICTIMAGESPERCAM_IMAGESENTRY.fields_by_name['value'].message_type = _PREDICTIMAGES
_PREDICTIMAGESPERCAM_IMAGESENTRY.containing_type = _PREDICTIMAGESPERCAM
_PREDICTIMAGESPERCAM.fields_by_name['images'].message_type = _PREDICTIMAGESPERCAM_IMAGESENTRY
_OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY.containing_type = _OPENRECORDINGRESPONSE
_OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY.fields_by_name['value'].message_type = _TRAJECTORIESWITHIMAGES
_OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY.containing_type = _OPENRECORDINGRESPONSE
_OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY.fields_by_name['value'].message_type = _PREDICTIMAGESPERCAM
_OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY.containing_type = _OPENRECORDINGRESPONSE
_OPENRECORDINGRESPONSE.fields_by_name['num_snapshots_per_row'].message_type = _OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY
_OPENRECORDINGRESPONSE.fields_by_name['recording_data'].message_type = _STATICRECORDINGDATA
_OPENRECORDINGRESPONSE.fields_by_name['trajectory_images_per_row'].message_type = _OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY
_OPENRECORDINGRESPONSE.fields_by_name['predict_images_per_row'].message_type = _OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY
_CONFIGNODESNAPSHOT_VALUESENTRY.containing_type = _CONFIGNODESNAPSHOT
_CONFIGNODESNAPSHOT_CHILDNODESENTRY.fields_by_name['value'].message_type = _CONFIGNODESNAPSHOT
_CONFIGNODESNAPSHOT_CHILDNODESENTRY.containing_type = _CONFIGNODESNAPSHOT
_CONFIGNODESNAPSHOT.fields_by_name['values'].message_type = _CONFIGNODESNAPSHOT_VALUESENTRY
_CONFIGNODESNAPSHOT.fields_by_name['child_nodes'].message_type = _CONFIGNODESNAPSHOT_CHILDNODESENTRY
_ROWCAMERAS_CAMSENTRY.fields_by_name['value'].message_type = _CAMERADIMENSIONS
_ROWCAMERAS_CAMSENTRY.containing_type = _ROWCAMERAS
_ROWCAMERAS.fields_by_name['cams'].message_type = _ROWCAMERAS_CAMSENTRY
_STATICRECORDINGDATA_LASERSENABLEDENTRY.containing_type = _STATICRECORDINGDATA
_STATICRECORDINGDATA_ROWDIMENSIONSENTRY.fields_by_name['value'].message_type = core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2._GETDIMENSIONSRESPONSE
_STATICRECORDINGDATA_ROWDIMENSIONSENTRY.containing_type = _STATICRECORDINGDATA
_STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY.containing_type = _STATICRECORDINGDATA
_STATICRECORDINGDATA_ROWCAMERASENTRY.fields_by_name['value'].message_type = _ROWCAMERAS
_STATICRECORDINGDATA_ROWCAMERASENTRY.containing_type = _STATICRECORDINGDATA
_STATICRECORDINGDATA.fields_by_name['lasers_enabled'].message_type = _STATICRECORDINGDATA_LASERSENABLEDENTRY
_STATICRECORDINGDATA.fields_by_name['root_config'].message_type = _CONFIGNODESNAPSHOT
_STATICRECORDINGDATA.fields_by_name['row_dimensions'].message_type = _STATICRECORDINGDATA_ROWDIMENSIONSENTRY
_STATICRECORDINGDATA.fields_by_name['crop_safety_radius_mm_per_row'].message_type = _STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY
_STATICRECORDINGDATA.fields_by_name['thinning_config'].message_type = proto_dot_thinning_dot_thinning__pb2._CONFIGDEFINITION
_STATICRECORDINGDATA.fields_by_name['row_cameras'].message_type = _STATICRECORDINGDATA_ROWCAMERASENTRY
_LASTSNAPSHOT.fields_by_name['trajectory'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._TRAJECTORYSNAPSHOT
_P2PPREDICTIMAGE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_TRAJECTORYPREDICTIMAGEMETADATA.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_TRAJECTORYDATA.fields_by_name['predict_image'].message_type = _TRAJECTORYPREDICTIMAGEMETADATA
_TRAJECTORYDATA.fields_by_name['target_images'].message_type = _TARGETIMAGE
_TRAJECTORYDATA.fields_by_name['last_snapshot'].message_type = _LASTSNAPSHOT
_TRAJECTORYDATA.fields_by_name['extra_fields'].message_type = _EXTRATRAJECTORYFIELD
_TRAJECTORYDATA.fields_by_name['p2p_predict_images'].message_type = _P2PPREDICTIMAGE
_GETPREDICTIMAGEMETADATARESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETPREDICTIMAGEMETADATARESPONSE.fields_by_name['annotations'].message_type = frontend_dot_proto_dot_image__stream__pb2._ANNOTATIONS
_GETPREDICTIMAGEMETADATARESPONSE.fields_by_name['deepweed_output'].message_type = cv_dot_runtime_dot_proto_dot_cv__runtime__pb2._DEEPWEEDOUTPUT
_GETPREDICTIMAGEMETADATARESPONSE.fields_by_name['deepweed_output_below_threshold'].message_type = cv_dot_runtime_dot_proto_dot_cv__runtime__pb2._DEEPWEEDOUTPUT
_GETNEXTUPLOADSTATEREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTUPLOADSTATERESPONSE.fields_by_name['upload_state'].enum_type = _UPLOADSTATE
_GETNEXTUPLOADSTATERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETDEEPWEEDPREDICTIONSRESPONSE.fields_by_name['predictions'].message_type = recorder_dot_proto_dot_recorder__pb2._DEEPWEEDPREDICTIONRECORD
_FINDTRAJECTORYRESPONSE.fields_by_name['trajectory'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._TRAJECTORYSNAPSHOT
_GETROTARYTICKSRESPONSE.fields_by_name['records'].message_type = recorder_dot_proto_dot_recorder__pb2._ROTARYTICKSRECORD
_SNAPSHOTPREDICTIMAGESRESPONSE.fields_by_name['snapshots'].message_type = _PCAMSNAPSHOT
DESCRIPTOR.message_types_by_name['RecordWeedingDiagnosticsRequest'] = _RECORDWEEDINGDIAGNOSTICSREQUEST
DESCRIPTOR.message_types_by_name['GetCurrentTrajectoriesRequest'] = _GETCURRENTTRAJECTORIESREQUEST
DESCRIPTOR.message_types_by_name['GetRecordingsListRequest'] = _GETRECORDINGSLISTREQUEST
DESCRIPTOR.message_types_by_name['GetRecordingsListResponse'] = _GETRECORDINGSLISTRESPONSE
DESCRIPTOR.message_types_by_name['GetSnapshotRequest'] = _GETSNAPSHOTREQUEST
DESCRIPTOR.message_types_by_name['GetSnapshotResponse'] = _GETSNAPSHOTRESPONSE
DESCRIPTOR.message_types_by_name['OpenRecordingRequest'] = _OPENRECORDINGREQUEST
DESCRIPTOR.message_types_by_name['TrajectoriesWithImages'] = _TRAJECTORIESWITHIMAGES
DESCRIPTOR.message_types_by_name['PredictImages'] = _PREDICTIMAGES
DESCRIPTOR.message_types_by_name['PredictImagesPerCam'] = _PREDICTIMAGESPERCAM
DESCRIPTOR.message_types_by_name['OpenRecordingResponse'] = _OPENRECORDINGRESPONSE
DESCRIPTOR.message_types_by_name['DeleteRecordingRequest'] = _DELETERECORDINGREQUEST
DESCRIPTOR.message_types_by_name['ConfigNodeSnapshot'] = _CONFIGNODESNAPSHOT
DESCRIPTOR.message_types_by_name['CameraDimensions'] = _CAMERADIMENSIONS
DESCRIPTOR.message_types_by_name['RowCameras'] = _ROWCAMERAS
DESCRIPTOR.message_types_by_name['StaticRecordingData'] = _STATICRECORDINGDATA
DESCRIPTOR.message_types_by_name['GetTrajectoryDataRequest'] = _GETTRAJECTORYDATAREQUEST
DESCRIPTOR.message_types_by_name['LastSnapshot'] = _LASTSNAPSHOT
DESCRIPTOR.message_types_by_name['ExtraTrajectoryField'] = _EXTRATRAJECTORYFIELD
DESCRIPTOR.message_types_by_name['TargetImage'] = _TARGETIMAGE
DESCRIPTOR.message_types_by_name['P2PPredictImage'] = _P2PPREDICTIMAGE
DESCRIPTOR.message_types_by_name['TrajectoryPredictImageMetadata'] = _TRAJECTORYPREDICTIMAGEMETADATA
DESCRIPTOR.message_types_by_name['TrajectoryData'] = _TRAJECTORYDATA
DESCRIPTOR.message_types_by_name['GetTrajectoryPredictImageRequest'] = _GETTRAJECTORYPREDICTIMAGEREQUEST
DESCRIPTOR.message_types_by_name['GetTrajectoryTargetImageRequest'] = _GETTRAJECTORYTARGETIMAGEREQUEST
DESCRIPTOR.message_types_by_name['ImageChunk'] = _IMAGECHUNK
DESCRIPTOR.message_types_by_name['GetPredictImageMetadataRequest'] = _GETPREDICTIMAGEMETADATAREQUEST
DESCRIPTOR.message_types_by_name['GetPredictImageMetadataResponse'] = _GETPREDICTIMAGEMETADATARESPONSE
DESCRIPTOR.message_types_by_name['GetPredictImageRequest'] = _GETPREDICTIMAGEREQUEST
DESCRIPTOR.message_types_by_name['StartUploadRequest'] = _STARTUPLOADREQUEST
DESCRIPTOR.message_types_by_name['GetNextUploadStateRequest'] = _GETNEXTUPLOADSTATEREQUEST
DESCRIPTOR.message_types_by_name['GetNextUploadStateResponse'] = _GETNEXTUPLOADSTATERESPONSE
DESCRIPTOR.message_types_by_name['GetDeepweedPredictionsCountRequest'] = _GETDEEPWEEDPREDICTIONSCOUNTREQUEST
DESCRIPTOR.message_types_by_name['GetDeepweedPredictionsCountResponse'] = _GETDEEPWEEDPREDICTIONSCOUNTRESPONSE
DESCRIPTOR.message_types_by_name['GetDeepweedPredictionsRequest'] = _GETDEEPWEEDPREDICTIONSREQUEST
DESCRIPTOR.message_types_by_name['GetDeepweedPredictionsResponse'] = _GETDEEPWEEDPREDICTIONSRESPONSE
DESCRIPTOR.message_types_by_name['FindTrajectoryRequest'] = _FINDTRAJECTORYREQUEST
DESCRIPTOR.message_types_by_name['FindTrajectoryResponse'] = _FINDTRAJECTORYRESPONSE
DESCRIPTOR.message_types_by_name['GetRotaryTicksRequest'] = _GETROTARYTICKSREQUEST
DESCRIPTOR.message_types_by_name['GetRotaryTicksResponse'] = _GETROTARYTICKSRESPONSE
DESCRIPTOR.message_types_by_name['SnapshotPredictImagesRequest'] = _SNAPSHOTPREDICTIMAGESREQUEST
DESCRIPTOR.message_types_by_name['PcamSnapshot'] = _PCAMSNAPSHOT
DESCRIPTOR.message_types_by_name['SnapshotPredictImagesResponse'] = _SNAPSHOTPREDICTIMAGESRESPONSE
DESCRIPTOR.message_types_by_name['GetChipForPredictImageRequest'] = _GETCHIPFORPREDICTIMAGEREQUEST
DESCRIPTOR.message_types_by_name['GetChipForPredictImageResponse'] = _GETCHIPFORPREDICTIMAGERESPONSE
DESCRIPTOR.enum_types_by_name['UploadState'] = _UPLOADSTATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RecordWeedingDiagnosticsRequest = _reflection.GeneratedProtocolMessageType('RecordWeedingDiagnosticsRequest', (_message.Message,), {
  'DESCRIPTOR' : _RECORDWEEDINGDIAGNOSTICSREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest)
  })
_sym_db.RegisterMessage(RecordWeedingDiagnosticsRequest)

GetCurrentTrajectoriesRequest = _reflection.GeneratedProtocolMessageType('GetCurrentTrajectoriesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCURRENTTRAJECTORIESREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetCurrentTrajectoriesRequest)
  })
_sym_db.RegisterMessage(GetCurrentTrajectoriesRequest)

GetRecordingsListRequest = _reflection.GeneratedProtocolMessageType('GetRecordingsListRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETRECORDINGSLISTREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetRecordingsListRequest)
  })
_sym_db.RegisterMessage(GetRecordingsListRequest)

GetRecordingsListResponse = _reflection.GeneratedProtocolMessageType('GetRecordingsListResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETRECORDINGSLISTRESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetRecordingsListResponse)
  })
_sym_db.RegisterMessage(GetRecordingsListResponse)

GetSnapshotRequest = _reflection.GeneratedProtocolMessageType('GetSnapshotRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSNAPSHOTREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetSnapshotRequest)
  })
_sym_db.RegisterMessage(GetSnapshotRequest)

GetSnapshotResponse = _reflection.GeneratedProtocolMessageType('GetSnapshotResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSNAPSHOTRESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetSnapshotResponse)
  })
_sym_db.RegisterMessage(GetSnapshotResponse)

OpenRecordingRequest = _reflection.GeneratedProtocolMessageType('OpenRecordingRequest', (_message.Message,), {
  'DESCRIPTOR' : _OPENRECORDINGREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.OpenRecordingRequest)
  })
_sym_db.RegisterMessage(OpenRecordingRequest)

TrajectoriesWithImages = _reflection.GeneratedProtocolMessageType('TrajectoriesWithImages', (_message.Message,), {
  'DESCRIPTOR' : _TRAJECTORIESWITHIMAGES,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.TrajectoriesWithImages)
  })
_sym_db.RegisterMessage(TrajectoriesWithImages)

PredictImages = _reflection.GeneratedProtocolMessageType('PredictImages', (_message.Message,), {
  'DESCRIPTOR' : _PREDICTIMAGES,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.PredictImages)
  })
_sym_db.RegisterMessage(PredictImages)

PredictImagesPerCam = _reflection.GeneratedProtocolMessageType('PredictImagesPerCam', (_message.Message,), {

  'ImagesEntry' : _reflection.GeneratedProtocolMessageType('ImagesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PREDICTIMAGESPERCAM_IMAGESENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry)
    })
  ,
  'DESCRIPTOR' : _PREDICTIMAGESPERCAM,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.PredictImagesPerCam)
  })
_sym_db.RegisterMessage(PredictImagesPerCam)
_sym_db.RegisterMessage(PredictImagesPerCam.ImagesEntry)

OpenRecordingResponse = _reflection.GeneratedProtocolMessageType('OpenRecordingResponse', (_message.Message,), {

  'NumSnapshotsPerRowEntry' : _reflection.GeneratedProtocolMessageType('NumSnapshotsPerRowEntry', (_message.Message,), {
    'DESCRIPTOR' : _OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry)
    })
  ,

  'TrajectoryImagesPerRowEntry' : _reflection.GeneratedProtocolMessageType('TrajectoryImagesPerRowEntry', (_message.Message,), {
    'DESCRIPTOR' : _OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry)
    })
  ,

  'PredictImagesPerRowEntry' : _reflection.GeneratedProtocolMessageType('PredictImagesPerRowEntry', (_message.Message,), {
    'DESCRIPTOR' : _OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry)
    })
  ,
  'DESCRIPTOR' : _OPENRECORDINGRESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.OpenRecordingResponse)
  })
_sym_db.RegisterMessage(OpenRecordingResponse)
_sym_db.RegisterMessage(OpenRecordingResponse.NumSnapshotsPerRowEntry)
_sym_db.RegisterMessage(OpenRecordingResponse.TrajectoryImagesPerRowEntry)
_sym_db.RegisterMessage(OpenRecordingResponse.PredictImagesPerRowEntry)

DeleteRecordingRequest = _reflection.GeneratedProtocolMessageType('DeleteRecordingRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETERECORDINGREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.DeleteRecordingRequest)
  })
_sym_db.RegisterMessage(DeleteRecordingRequest)

ConfigNodeSnapshot = _reflection.GeneratedProtocolMessageType('ConfigNodeSnapshot', (_message.Message,), {

  'ValuesEntry' : _reflection.GeneratedProtocolMessageType('ValuesEntry', (_message.Message,), {
    'DESCRIPTOR' : _CONFIGNODESNAPSHOT_VALUESENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry)
    })
  ,

  'ChildNodesEntry' : _reflection.GeneratedProtocolMessageType('ChildNodesEntry', (_message.Message,), {
    'DESCRIPTOR' : _CONFIGNODESNAPSHOT_CHILDNODESENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry)
    })
  ,
  'DESCRIPTOR' : _CONFIGNODESNAPSHOT,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot)
  })
_sym_db.RegisterMessage(ConfigNodeSnapshot)
_sym_db.RegisterMessage(ConfigNodeSnapshot.ValuesEntry)
_sym_db.RegisterMessage(ConfigNodeSnapshot.ChildNodesEntry)

CameraDimensions = _reflection.GeneratedProtocolMessageType('CameraDimensions', (_message.Message,), {
  'DESCRIPTOR' : _CAMERADIMENSIONS,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.CameraDimensions)
  })
_sym_db.RegisterMessage(CameraDimensions)

RowCameras = _reflection.GeneratedProtocolMessageType('RowCameras', (_message.Message,), {

  'CamsEntry' : _reflection.GeneratedProtocolMessageType('CamsEntry', (_message.Message,), {
    'DESCRIPTOR' : _ROWCAMERAS_CAMSENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry)
    })
  ,
  'DESCRIPTOR' : _ROWCAMERAS,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.RowCameras)
  })
_sym_db.RegisterMessage(RowCameras)
_sym_db.RegisterMessage(RowCameras.CamsEntry)

StaticRecordingData = _reflection.GeneratedProtocolMessageType('StaticRecordingData', (_message.Message,), {

  'LasersEnabledEntry' : _reflection.GeneratedProtocolMessageType('LasersEnabledEntry', (_message.Message,), {
    'DESCRIPTOR' : _STATICRECORDINGDATA_LASERSENABLEDENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry)
    })
  ,

  'RowDimensionsEntry' : _reflection.GeneratedProtocolMessageType('RowDimensionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _STATICRECORDINGDATA_ROWDIMENSIONSENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry)
    })
  ,

  'CropSafetyRadiusMmPerRowEntry' : _reflection.GeneratedProtocolMessageType('CropSafetyRadiusMmPerRowEntry', (_message.Message,), {
    'DESCRIPTOR' : _STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry)
    })
  ,

  'RowCamerasEntry' : _reflection.GeneratedProtocolMessageType('RowCamerasEntry', (_message.Message,), {
    'DESCRIPTOR' : _STATICRECORDINGDATA_ROWCAMERASENTRY,
    '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry)
    })
  ,
  'DESCRIPTOR' : _STATICRECORDINGDATA,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.StaticRecordingData)
  })
_sym_db.RegisterMessage(StaticRecordingData)
_sym_db.RegisterMessage(StaticRecordingData.LasersEnabledEntry)
_sym_db.RegisterMessage(StaticRecordingData.RowDimensionsEntry)
_sym_db.RegisterMessage(StaticRecordingData.CropSafetyRadiusMmPerRowEntry)
_sym_db.RegisterMessage(StaticRecordingData.RowCamerasEntry)

GetTrajectoryDataRequest = _reflection.GeneratedProtocolMessageType('GetTrajectoryDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTRAJECTORYDATAREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest)
  })
_sym_db.RegisterMessage(GetTrajectoryDataRequest)

LastSnapshot = _reflection.GeneratedProtocolMessageType('LastSnapshot', (_message.Message,), {
  'DESCRIPTOR' : _LASTSNAPSHOT,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.LastSnapshot)
  })
_sym_db.RegisterMessage(LastSnapshot)

ExtraTrajectoryField = _reflection.GeneratedProtocolMessageType('ExtraTrajectoryField', (_message.Message,), {
  'DESCRIPTOR' : _EXTRATRAJECTORYFIELD,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.ExtraTrajectoryField)
  })
_sym_db.RegisterMessage(ExtraTrajectoryField)

TargetImage = _reflection.GeneratedProtocolMessageType('TargetImage', (_message.Message,), {
  'DESCRIPTOR' : _TARGETIMAGE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.TargetImage)
  })
_sym_db.RegisterMessage(TargetImage)

P2PPredictImage = _reflection.GeneratedProtocolMessageType('P2PPredictImage', (_message.Message,), {
  'DESCRIPTOR' : _P2PPREDICTIMAGE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.P2PPredictImage)
  })
_sym_db.RegisterMessage(P2PPredictImage)

TrajectoryPredictImageMetadata = _reflection.GeneratedProtocolMessageType('TrajectoryPredictImageMetadata', (_message.Message,), {
  'DESCRIPTOR' : _TRAJECTORYPREDICTIMAGEMETADATA,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata)
  })
_sym_db.RegisterMessage(TrajectoryPredictImageMetadata)

TrajectoryData = _reflection.GeneratedProtocolMessageType('TrajectoryData', (_message.Message,), {
  'DESCRIPTOR' : _TRAJECTORYDATA,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.TrajectoryData)
  })
_sym_db.RegisterMessage(TrajectoryData)

GetTrajectoryPredictImageRequest = _reflection.GeneratedProtocolMessageType('GetTrajectoryPredictImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTRAJECTORYPREDICTIMAGEREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest)
  })
_sym_db.RegisterMessage(GetTrajectoryPredictImageRequest)

GetTrajectoryTargetImageRequest = _reflection.GeneratedProtocolMessageType('GetTrajectoryTargetImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTRAJECTORYTARGETIMAGEREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest)
  })
_sym_db.RegisterMessage(GetTrajectoryTargetImageRequest)

ImageChunk = _reflection.GeneratedProtocolMessageType('ImageChunk', (_message.Message,), {
  'DESCRIPTOR' : _IMAGECHUNK,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.ImageChunk)
  })
_sym_db.RegisterMessage(ImageChunk)

GetPredictImageMetadataRequest = _reflection.GeneratedProtocolMessageType('GetPredictImageMetadataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPREDICTIMAGEMETADATAREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest)
  })
_sym_db.RegisterMessage(GetPredictImageMetadataRequest)

GetPredictImageMetadataResponse = _reflection.GeneratedProtocolMessageType('GetPredictImageMetadataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETPREDICTIMAGEMETADATARESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse)
  })
_sym_db.RegisterMessage(GetPredictImageMetadataResponse)

GetPredictImageRequest = _reflection.GeneratedProtocolMessageType('GetPredictImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPREDICTIMAGEREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetPredictImageRequest)
  })
_sym_db.RegisterMessage(GetPredictImageRequest)

StartUploadRequest = _reflection.GeneratedProtocolMessageType('StartUploadRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTUPLOADREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.StartUploadRequest)
  })
_sym_db.RegisterMessage(StartUploadRequest)

GetNextUploadStateRequest = _reflection.GeneratedProtocolMessageType('GetNextUploadStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTUPLOADSTATEREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest)
  })
_sym_db.RegisterMessage(GetNextUploadStateRequest)

GetNextUploadStateResponse = _reflection.GeneratedProtocolMessageType('GetNextUploadStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTUPLOADSTATERESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse)
  })
_sym_db.RegisterMessage(GetNextUploadStateResponse)

GetDeepweedPredictionsCountRequest = _reflection.GeneratedProtocolMessageType('GetDeepweedPredictionsCountRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDPREDICTIONSCOUNTREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest)
  })
_sym_db.RegisterMessage(GetDeepweedPredictionsCountRequest)

GetDeepweedPredictionsCountResponse = _reflection.GeneratedProtocolMessageType('GetDeepweedPredictionsCountResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDPREDICTIONSCOUNTRESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountResponse)
  })
_sym_db.RegisterMessage(GetDeepweedPredictionsCountResponse)

GetDeepweedPredictionsRequest = _reflection.GeneratedProtocolMessageType('GetDeepweedPredictionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDPREDICTIONSREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest)
  })
_sym_db.RegisterMessage(GetDeepweedPredictionsRequest)

GetDeepweedPredictionsResponse = _reflection.GeneratedProtocolMessageType('GetDeepweedPredictionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDPREDICTIONSRESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsResponse)
  })
_sym_db.RegisterMessage(GetDeepweedPredictionsResponse)

FindTrajectoryRequest = _reflection.GeneratedProtocolMessageType('FindTrajectoryRequest', (_message.Message,), {
  'DESCRIPTOR' : _FINDTRAJECTORYREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.FindTrajectoryRequest)
  })
_sym_db.RegisterMessage(FindTrajectoryRequest)

FindTrajectoryResponse = _reflection.GeneratedProtocolMessageType('FindTrajectoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _FINDTRAJECTORYRESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.FindTrajectoryResponse)
  })
_sym_db.RegisterMessage(FindTrajectoryResponse)

GetRotaryTicksRequest = _reflection.GeneratedProtocolMessageType('GetRotaryTicksRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETROTARYTICKSREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetRotaryTicksRequest)
  })
_sym_db.RegisterMessage(GetRotaryTicksRequest)

GetRotaryTicksResponse = _reflection.GeneratedProtocolMessageType('GetRotaryTicksResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETROTARYTICKSRESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse)
  })
_sym_db.RegisterMessage(GetRotaryTicksResponse)

SnapshotPredictImagesRequest = _reflection.GeneratedProtocolMessageType('SnapshotPredictImagesRequest', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPREDICTIMAGESREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.SnapshotPredictImagesRequest)
  })
_sym_db.RegisterMessage(SnapshotPredictImagesRequest)

PcamSnapshot = _reflection.GeneratedProtocolMessageType('PcamSnapshot', (_message.Message,), {
  'DESCRIPTOR' : _PCAMSNAPSHOT,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.PcamSnapshot)
  })
_sym_db.RegisterMessage(PcamSnapshot)

SnapshotPredictImagesResponse = _reflection.GeneratedProtocolMessageType('SnapshotPredictImagesResponse', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPREDICTIMAGESRESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.SnapshotPredictImagesResponse)
  })
_sym_db.RegisterMessage(SnapshotPredictImagesResponse)

GetChipForPredictImageRequest = _reflection.GeneratedProtocolMessageType('GetChipForPredictImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCHIPFORPREDICTIMAGEREQUEST,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest)
  })
_sym_db.RegisterMessage(GetChipForPredictImageRequest)

GetChipForPredictImageResponse = _reflection.GeneratedProtocolMessageType('GetChipForPredictImageResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCHIPFORPREDICTIMAGERESPONSE,
  '__module__' : 'frontend.proto.weeding_diagnostics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.weeding_diagnostics.GetChipForPredictImageResponse)
  })
_sym_db.RegisterMessage(GetChipForPredictImageResponse)


DESCRIPTOR._options = None
_PREDICTIMAGESPERCAM_IMAGESENTRY._options = None
_OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY._options = None
_OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY._options = None
_OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY._options = None
_CONFIGNODESNAPSHOT_VALUESENTRY._options = None
_CONFIGNODESNAPSHOT_CHILDNODESENTRY._options = None
_ROWCAMERAS_CAMSENTRY._options = None
_STATICRECORDINGDATA_LASERSENABLEDENTRY._options = None
_STATICRECORDINGDATA_ROWDIMENSIONSENTRY._options = None
_STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY._options = None
_STATICRECORDINGDATA_ROWCAMERASENTRY._options = None

_WEEDINGDIAGNOSTICSSERVICE = _descriptor.ServiceDescriptor(
  name='WeedingDiagnosticsService',
  full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=6964,
  serialized_end=9715,
  methods=[
  _descriptor.MethodDescriptor(
    name='RecordWeedingDiagnostics',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.RecordWeedingDiagnostics',
    index=0,
    containing_service=None,
    input_type=_RECORDWEEDINGDIAGNOSTICSREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCurrentTrajectories',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetCurrentTrajectories',
    index=1,
    containing_service=None,
    input_type=_GETCURRENTTRAJECTORIESREQUEST,
    output_type=weed__tracking_dot_proto_dot_weed__tracking__pb2._DIAGNOSTICSSNAPSHOT,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRecordingsList',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetRecordingsList',
    index=2,
    containing_service=None,
    input_type=_GETRECORDINGSLISTREQUEST,
    output_type=_GETRECORDINGSLISTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='OpenRecording',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.OpenRecording',
    index=3,
    containing_service=None,
    input_type=_OPENRECORDINGREQUEST,
    output_type=_OPENRECORDINGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSnapshot',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetSnapshot',
    index=4,
    containing_service=None,
    input_type=_GETSNAPSHOTREQUEST,
    output_type=_GETSNAPSHOTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteRecording',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.DeleteRecording',
    index=5,
    containing_service=None,
    input_type=_DELETERECORDINGREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTrajectoryData',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryData',
    index=6,
    containing_service=None,
    input_type=_GETTRAJECTORYDATAREQUEST,
    output_type=_TRAJECTORYDATA,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTrajectoryPredictImage',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryPredictImage',
    index=7,
    containing_service=None,
    input_type=_GETTRAJECTORYPREDICTIMAGEREQUEST,
    output_type=_IMAGECHUNK,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTrajectoryTargetImage',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryTargetImage',
    index=8,
    containing_service=None,
    input_type=_GETTRAJECTORYTARGETIMAGEREQUEST,
    output_type=_IMAGECHUNK,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetPredictImageMetadata',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetPredictImageMetadata',
    index=9,
    containing_service=None,
    input_type=_GETPREDICTIMAGEMETADATAREQUEST,
    output_type=_GETPREDICTIMAGEMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetPredictImage',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetPredictImage',
    index=10,
    containing_service=None,
    input_type=_GETPREDICTIMAGEREQUEST,
    output_type=_IMAGECHUNK,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartUpload',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.StartUpload',
    index=11,
    containing_service=None,
    input_type=_STARTUPLOADREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextUploadState',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetNextUploadState',
    index=12,
    containing_service=None,
    input_type=_GETNEXTUPLOADSTATEREQUEST,
    output_type=_GETNEXTUPLOADSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDeepweedPredictionsCount',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetDeepweedPredictionsCount',
    index=13,
    containing_service=None,
    input_type=_GETDEEPWEEDPREDICTIONSCOUNTREQUEST,
    output_type=_GETDEEPWEEDPREDICTIONSCOUNTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDeepweedPredictions',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetDeepweedPredictions',
    index=14,
    containing_service=None,
    input_type=_GETDEEPWEEDPREDICTIONSREQUEST,
    output_type=_GETDEEPWEEDPREDICTIONSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FindTrajectory',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.FindTrajectory',
    index=15,
    containing_service=None,
    input_type=_FINDTRAJECTORYREQUEST,
    output_type=_FINDTRAJECTORYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRotaryTicks',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetRotaryTicks',
    index=16,
    containing_service=None,
    input_type=_GETROTARYTICKSREQUEST,
    output_type=_GETROTARYTICKSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SnapshotPredictImages',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.SnapshotPredictImages',
    index=17,
    containing_service=None,
    input_type=_SNAPSHOTPREDICTIMAGESREQUEST,
    output_type=_SNAPSHOTPREDICTIMAGESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetChipForPredictImage',
    full_name='carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetChipForPredictImage',
    index=18,
    containing_service=None,
    input_type=_GETCHIPFORPREDICTIMAGEREQUEST,
    output_type=_GETCHIPFORPREDICTIMAGERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_WEEDINGDIAGNOSTICSSERVICE)

DESCRIPTOR.services_by_name['WeedingDiagnosticsService'] = _WEEDINGDIAGNOSTICSSERVICE

# @@protoc_insertion_point(module_scope)
