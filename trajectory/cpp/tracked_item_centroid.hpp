#pragma once

#include <optional>
#include <string>
#include <utility>
#include <vector>

#include <torch/torch.h>

#include <cv/runtime/proto/cv_runtime.pb.h>

namespace lib::common::geometric {
class GeometricCam;
}
namespace carbon::trajectory {
static constexpr std::string_view UNKNOWN_CLASS = "UNKNOWN";
class TrackedItemCentroid {
public:
  TrackedItemCentroid(const std::string &pcam_id, uint32_t tracker_id, float x, float y, double x_mm, double y_mm,
                      double z_mm, float size, float score, float weed_score, float crop_score, float plant_score,
                      int64_t timestamp_ms, bool has_perspective, const std::vector<std::string> &intersection_classes,
                      const std::vector<std::pair<std::string, float>> &detection_class,
                      const std::vector<float> &embedding, cv::runtime::proto::HitClass hit_class,
                      bool perspective_in_dist_buff, lib::common::geometric::GeometricCam *geo_cam, uint32_t index,
                      const std::vector<float> &embedding_category_distances,
                      std::shared_ptr<std::vector<std::string>> embedding_categories, uint32_t detection_id);
  TrackedItemCentroid(const TrackedItemCentroid &orig, float x, float y, double x_mm, double y_mm, double z_mm,
                      int64_t timestamp_ms);
  TrackedItemCentroid(const TrackedItemCentroid &orig);
  std::string get_pcam_id() const;
  inline void set_pcam_id(const std::string &pcam_id) { pcam_id_ = pcam_id; }
  inline void set_tracker_id(uint32_t tracker_id) { tracker_id_ = tracker_id; }
  inline uint32_t get_tracker_id() const { return tracker_id_; }
  std::pair<float, float> get_coord() const;
  float get_x() const;
  float get_y() const;
  void set_coord(float x, float y);
  std::pair<float, float> get_p2p_coord() const;
  void set_p2p_coord(std::pair<float, float> p2p_coord);
  void convert_x_y_from_distortion_undistortion_maps(torch::Tensor conversion_x, torch::Tensor conversion_y);
  double get_x_mm() const;
  double get_y_mm() const;
  double get_z_mm() const;
  float get_ticks() const;
  float get_score() const;
  float get_weed_score() const;
  float get_crop_score() const;
  float get_plant_score() const;
  void shift_coord(float x_shift, float y_shift);
  void set_mm_coord(double x_mm, double y_mm, double z_mm);
  void set_score(float score);
  void set_weed_score(float score);
  void set_crop_score(float score);
  void set_plant_score(float score);
  float get_size() const;
  void set_size(float size);
  bool get_has_perspective() const;
  void set_has_perspective(bool has_perspective);
  int64_t get_timestamp_ms() const;
  std::vector<std::string> get_intersection_classes() const;
  void set_intersection_classes(std::vector<std::string> classes);
  std::vector<std::pair<std::string, float>> get_classes() const;
  void set_classes(std::vector<std::pair<std::string, float>> detection_classes);
  std::vector<float> get_embedding() const;
  void set_embedding(std::vector<float> embedding);
  cv::runtime::proto::HitClass get_hit_class() const;
  void set_hit_class(cv::runtime::proto::HitClass hit_class);
  bool in_dist_buff() const { return in_dist_buff_ && has_perspective_; }
  void set_in_dist_buff(bool in_dist_buff) { in_dist_buff_ = in_dist_buff; }
  inline float get_size_mm() const { return size_mm_; }
  inline bool is_plant_captcha_only() const { return is_plant_captcha_only_; }
  inline void set_is_plant_captcha_only() { is_plant_captcha_only_ = true; }
  inline uint32_t index() const { return index_; } // only guaranteed unique in single deepweed cycle
  // Copy all member variables from orig to this
  void copy(const TrackedItemCentroid &orig);
  // Copy Relevant Perspective Data from orig to this
  void copy_perspective_data(const TrackedItemCentroid &orig);

  const std::vector<float> &get_embedding_category_distances() const;
  const std::shared_ptr<std::vector<std::string>> &get_embedding_categories() const;

  uint32_t detection_id() const { return detection_id_; };

  bool get_available_for_snapshotting() const { return available_for_snapshotting_; }
  void set_available_for_snapshotting(bool available) { available_for_snapshotting_ = available; }

private:
  TrackedItemCentroid(const std::string &pcam_id, uint32_t tracker_id, float x, float y, double x_mm, double y_mm,
                      double z_mm, float size, float score, float weed_score, float crop_score, float plant_score,
                      int64_t timestamp_ms, bool has_perspective, const std::vector<std::string> &intersection_classes,
                      const std::vector<std::pair<std::string, float>> &detection_class,
                      const std::vector<float> &embedding, cv::runtime::proto::HitClass hit_class,
                      bool perspective_in_dist_buff, float size_mm, bool is_plant_captcha_only, uint32_t index,
                      const std::vector<float> &embedding_category_distances,
                      std::shared_ptr<std::vector<std::string>> embedding_categories, uint32_t detection_id_,
                      bool available_for_snapshotting);
  std::string pcam_id_;
  uint32_t tracker_id_;
  float x_; // x coordinate which can change between distorted and undistorted, only used for deduplication
  float y_; // y coordinate which can change between distorted and undistorted, only used for deduplication
  double x_mm_;
  double y_mm_;
  double z_mm_;
  float size_;
  float score_;
  float weed_score_;
  float crop_score_;
  float plant_score_;
  float ticks_since_previous_centroid_;
  int64_t timestamp_ms_;
  bool has_perspective_;
  std::pair<float, float> p2p_coord_; // Coordinates for making a p2p request. This will just be the coords in the
                                      // original, distorted perspective
  std::vector<std::string> intersection_classes_;
  std::vector<std::pair<std::string, float>> detection_classes_;
  std::vector<float> embedding_;
  cv::runtime::proto::HitClass hit_class_;
  bool in_dist_buff_;
  float size_mm_;
  std::atomic<bool> is_plant_captcha_only_;
  uint32_t index_;

  std::vector<float> embedding_category_distances_;
  std::shared_ptr<std::vector<std::string>> embedding_categories_;
  uint32_t detection_id_;
  bool available_for_snapshotting_;

  std::pair<std::string, float> get_best_class() const;
  std::optional<std::pair<std::string, float>> get_best_weed_class() const;
};
inline bool operator==(TrackedItemCentroid &lhs, TrackedItemCentroid &rhs) {
  return lhs.get_pcam_id() == rhs.get_pcam_id() && lhs.get_x() == rhs.get_x() && lhs.get_y() == rhs.get_y() &&
         lhs.get_has_perspective() == rhs.get_has_perspective() && lhs.get_timestamp_ms() == rhs.get_timestamp_ms();
}

inline bool operator!=(TrackedItemCentroid &lhs, TrackedItemCentroid &rhs) { return !(lhs == rhs); }

inline bool operator<(const TrackedItemCentroid &lhs, const TrackedItemCentroid rhs) {
  return lhs.get_timestamp_ms() < rhs.get_timestamp_ms();
}
} // namespace carbon::trajectory