package cv_runtime_client

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

type CVRuntimeClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client cv.CVRuntimeServiceClient
	opts   []grpc.DialOption
	logger *logrus.Entry
}

func destroyConnection(c *CVRuntimeClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewCVRuntimeClient(addr string) *CVRuntimeClient {
	client := &CVRuntimeClient{
		addr:   addr,
		logger: logrus.New().WithFields(logrus.Fields{"module": "CVRuntimeClient"}),
	}
	max_message_size := 1024 * 1024 * 50
	call_opts := grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(max_message_size), grpc.MaxCallSendMsgSize(max_message_size))
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	client.opts = append(client.opts, call_opts)
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

// For legacy support, do not use this function. In other words, DEPRECATED.
func (c *CVRuntimeClient) GetRawClient() (cv.CVRuntimeServiceClient, error) {
	return c.getClient()
}

func (c *CVRuntimeClient) Copy() *CVRuntimeClient {
	return NewCVRuntimeClient(c.addr)
}

func (c *CVRuntimeClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.conn.ResetConnectBackoff()
}

func getDefaultCVRuntimeClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), time.Duration(time.Millisecond*500))
}

func (c *CVRuntimeClient) getClient() (cv.CVRuntimeServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = cv.NewCVRuntimeServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *CVRuntimeClient) sendRequest(f func(cv.CVRuntimeServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			pc, _, _, _ := runtime.Caller(1)
			details := runtime.FuncForPC(pc)
			switch e.Code() {
			case codes.Unavailable:
				c.logger.Warningf("Resetting connection due to unavailable error. Caller: %v", details.Name())
				c.resetConnection()
			case codes.DeadlineExceeded:
				c.logger.Warningf("Resetting connection due to deadline exceeded error. Caller: %v", details.Name())
				c.resetConnection()
			default:
			}
		}
		return nil, err
	}

	return result, nil
}

func (c *CVRuntimeClient) GetCameraInfo() (*cv.GetCameraInfoResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(time.Millisecond*1000))
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.GetCameraInfo(ctx, &cv.GetCameraInfoRequest{})
	})
	if response == nil {
		return nil, err
	}

	return response.(*cv.GetCameraInfoResponse), err
}

func (c *CVRuntimeClient) GetCameraDimensions(camId string) (*cv.GetCameraDimensionsResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.GetCameraDimensions(ctx, &cv.GetCameraDimensionsRequest{
			CamId: camId,
		})
	})
	if response == nil {
		return nil, err
	}

	return response.(*cv.GetCameraDimensionsResponse), err
}

func (c *CVRuntimeClient) GetBooted() bool {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.GetBooted(ctx, &cv.GetBootedRequest{})
	})
	if err != nil {
		return false
	}

	return response.(*cv.GetBootedResponse).Booted
}

func (c *CVRuntimeClient) GetReady() (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(time.Millisecond*1000))
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.GetReady(ctx, &cv.GetReadyRequest{})
	})
	if err != nil {
		return false, err
	}

	return response.(*cv.GetReadyResponse).Ready, nil
}

func (c *CVRuntimeClient) SetAutoWhitebalance(camId string, enableAutoWhitebalance bool) error {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	var camIds []string
	camIds = append(camIds, camId)
	_, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.SetAutoWhitebalance(ctx, &cv.SetAutoWhitebalanceRequest{CamIds: camIds, Enable: enableAutoWhitebalance})
	})
	return err
}

func (c *CVRuntimeClient) GetSettings(camId string) (*cv.GetCameraSettingsResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	var camIds []string
	camIds = append(camIds, camId)
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.GetCameraSettings(ctx, &cv.GetCameraSettingsRequest{
			CamIds: camIds,
		})
	})
	if response == nil {
		return nil, err
	}

	return response.(*cv.GetCameraSettingsResponse), err
}

func (c *CVRuntimeClient) GetTiming() (*cv.GetTimingResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.GetTiming(ctx, &cv.GetTimingRequest{})
	})
	if response == nil {
		return nil, err
	}

	return response.(*cv.GetTimingResponse), err
}

func (c *CVRuntimeClient) GetErrorState() (*cv.GetErrorStateResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.GetErrorState(ctx, &cv.Empty{})
	})
	if response == nil {
		return nil, err
	}

	return response.(*cv.GetErrorStateResponse), err
}

func (c *CVRuntimeClient) GetRecommendedStrobeSettings() (*cv.GetRecommendedStrobeSettingsResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.GetRecommendedStrobeSettings(ctx, &cv.GetRecommendedStrobeSettingsRequest{})
	})
	if response == nil {
		return nil, err
	}

	return response.(*cv.GetRecommendedStrobeSettingsResponse), err
}

func (c *CVRuntimeClient) RemoveDataDir(path string) error {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (interface{}, error) {
		return client.RemoveDataDir(ctx, &cv.RemoveDataDirRequest{Path: path})
	})
	return err
}

func (c *CVRuntimeClient) GetCVSupportedTensorRTVersions() (*cv.SupportedTensorRTVersionsResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (any, error) {
		return client.GetSupportedTensorRTVersions(ctx, &cv.Empty{})
	})
	if response == nil {
		return nil, err
	}

	return response.(*cv.SupportedTensorRTVersionsResponse), err
}

func (c *CVRuntimeClient) GetCVComputeCapabilities() (*cv.ComputeCapabilitiesResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (any, error) {
		return client.GetComputeCapabilities(ctx, &cv.Empty{})
	})
	if response == nil {
		return nil, err
	}

	return response.(*cv.ComputeCapabilitiesResponse), err
}

func (c *CVRuntimeClient) GetCameraTemperatures() (*cv.GetCameraTemperaturesResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (any, error) {
		return client.GetCameraTemperatures(ctx, &cv.GetCameraTemperaturesRequest{})
	})
	if response == nil {
		return nil, err
	}
	return response.(*cv.GetCameraTemperaturesResponse), err
}

func (c *CVRuntimeClient) ReloadCategoryCollection() (*cv.Empty, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (any, error) {
		return client.ReloadCategoryCollection(ctx, &cv.Empty{})
	})
	if response == nil {
		return nil, err
	}
	return response.(*cv.Empty), err
}

func (c *CVRuntimeClient) GetCategoryCollection() (*cv.GetCategoryCollectionResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (any, error) {
		return client.GetCategoryCollection(ctx, &cv.Empty{})
	})
	if response == nil {
		return nil, err
	}
	return response.(*cv.GetCategoryCollectionResponse), err
}

func (c *CVRuntimeClient) SnapshotPredictImages() (*cv.SnapshotPredictImagesResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (any, error) {
		return client.SnapshotPredictImages(ctx, &cv.SnapshotPredictImagesRequest{})
	})
	if response == nil {
		return nil, err
	}
	return response.(*cv.SnapshotPredictImagesResponse), err
}

func (c *CVRuntimeClient) GetChipForPredictImage(pcamId string, timestampMs int64, centerXPx, centerYPx int32) (*cv.GetChipForPredictImageResponse, error) {
	ctx, cancel := getDefaultCVRuntimeClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client cv.CVRuntimeServiceClient) (any, error) {
		return client.GetChipForPredictImage(ctx, &cv.GetChipForPredictImageRequest{
			PcamId:      pcamId,
			TimestampMs: timestampMs,
			CenterXPx:   centerXPx,
			CenterYPx:   centerYPx,
		})
	})
	if response == nil {
		return nil, err
	}
	return response.(*cv.GetChipForPredictImageResponse), err
}
