// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: golang/simulator/hardware/proto/sim_UI/sim_UI.proto
#ifndef GRPC_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto__INCLUDED
#define GRPC_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto__INCLUDED

#include "golang/simulator/hardware/proto/sim_UI/sim_UI.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace simulator_UI {

class SimulatorUIService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.simulator_UI.SimulatorUIService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>> GetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>>(GetVelocityStreamRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>> AsyncGetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>>(AsyncGetVelocityStreamRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>> PrepareAsyncGetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>>(PrepareAsyncGetVelocityStreamRaw(context, request, cq));
    }
    virtual ::grpc::Status SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::carbon::simulator_UI::SetVelocityResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetVelocityResponse>> AsyncSetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetVelocityResponse>>(AsyncSetVelocityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetVelocityResponse>> PrepareAsyncSetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetVelocityResponse>>(PrepareAsyncSetVelocityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>> GetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>>(GetPresetStreamRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>> AsyncGetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>>(AsyncGetPresetStreamRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>> PrepareAsyncGetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>>(PrepareAsyncGetPresetStreamRaw(context, request, cq));
    }
    virtual ::grpc::Status SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::carbon::simulator_UI::SetPresetResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetPresetResponse>> AsyncSetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetPresetResponse>>(AsyncSetPresetRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetPresetResponse>> PrepareAsyncSetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetPresetResponse>>(PrepareAsyncSetPresetRaw(context, request, cq));
    }
    virtual ::grpc::Status GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>> AsyncGetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>>(AsyncGetDiagnosticSettingsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>> PrepareAsyncGetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>>(PrepareAsyncGetDiagnosticSettingsRaw(context, request, cq));
    }
    virtual ::grpc::Status SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>> AsyncSetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>>(AsyncSetDiagnosticSettingsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>> PrepareAsyncSetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>>(PrepareAsyncSetDiagnosticSettingsRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest* request, ::grpc::ClientReadReactor< ::carbon::simulator_UI::GetVelocityResponse>* reactor) = 0;
      virtual void SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest* request, ::grpc::ClientReadReactor< ::carbon::simulator_UI::GetPresetsResponse>* reactor) = 0;
      virtual void SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>* GetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>* AsyncGetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetVelocityResponse>* PrepareAsyncGetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetVelocityResponse>* AsyncSetVelocityRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetVelocityResponse>* PrepareAsyncSetVelocityRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>* GetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>* AsyncGetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::simulator_UI::GetPresetsResponse>* PrepareAsyncGetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetPresetResponse>* AsyncSetPresetRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetPresetResponse>* PrepareAsyncSetPresetRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* AsyncGetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* PrepareAsyncGetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* AsyncSetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* PrepareAsyncSetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    std::unique_ptr< ::grpc::ClientReader< ::carbon::simulator_UI::GetVelocityResponse>> GetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::carbon::simulator_UI::GetVelocityResponse>>(GetVelocityStreamRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetVelocityResponse>> AsyncGetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetVelocityResponse>>(AsyncGetVelocityStreamRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetVelocityResponse>> PrepareAsyncGetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetVelocityResponse>>(PrepareAsyncGetVelocityStreamRaw(context, request, cq));
    }
    ::grpc::Status SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::carbon::simulator_UI::SetVelocityResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetVelocityResponse>> AsyncSetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetVelocityResponse>>(AsyncSetVelocityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetVelocityResponse>> PrepareAsyncSetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetVelocityResponse>>(PrepareAsyncSetVelocityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReader< ::carbon::simulator_UI::GetPresetsResponse>> GetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::carbon::simulator_UI::GetPresetsResponse>>(GetPresetStreamRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetPresetsResponse>> AsyncGetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetPresetsResponse>>(AsyncGetPresetStreamRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetPresetsResponse>> PrepareAsyncGetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetPresetsResponse>>(PrepareAsyncGetPresetStreamRaw(context, request, cq));
    }
    ::grpc::Status SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::carbon::simulator_UI::SetPresetResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetPresetResponse>> AsyncSetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetPresetResponse>>(AsyncSetPresetRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetPresetResponse>> PrepareAsyncSetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetPresetResponse>>(PrepareAsyncSetPresetRaw(context, request, cq));
    }
    ::grpc::Status GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>> AsyncGetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>>(AsyncGetDiagnosticSettingsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>> PrepareAsyncGetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>>(PrepareAsyncGetDiagnosticSettingsRaw(context, request, cq));
    }
    ::grpc::Status SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>> AsyncSetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>>(AsyncSetDiagnosticSettingsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>> PrepareAsyncSetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>>(PrepareAsyncSetDiagnosticSettingsRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest* request, ::grpc::ClientReadReactor< ::carbon::simulator_UI::GetVelocityResponse>* reactor) override;
      void SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response, std::function<void(::grpc::Status)>) override;
      void SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest* request, ::grpc::ClientReadReactor< ::carbon::simulator_UI::GetPresetsResponse>* reactor) override;
      void SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response, std::function<void(::grpc::Status)>) override;
      void SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response, std::function<void(::grpc::Status)>) override;
      void SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientReader< ::carbon::simulator_UI::GetVelocityResponse>* GetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request) override;
    ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetVelocityResponse>* AsyncGetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetVelocityResponse>* PrepareAsyncGetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetVelocityResponse>* AsyncSetVelocityRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetVelocityResponse>* PrepareAsyncSetVelocityRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReader< ::carbon::simulator_UI::GetPresetsResponse>* GetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request) override;
    ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetPresetsResponse>* AsyncGetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetPresetsResponse>* PrepareAsyncGetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetPresetResponse>* AsyncSetPresetRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetPresetResponse>* PrepareAsyncSetPresetRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* AsyncGetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* PrepareAsyncGetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* AsyncSetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* PrepareAsyncSetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetVelocityStream_;
    const ::grpc::internal::RpcMethod rpcmethod_SetVelocity_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPresetStream_;
    const ::grpc::internal::RpcMethod rpcmethod_SetPreset_;
    const ::grpc::internal::RpcMethod rpcmethod_GetDiagnosticSettings_;
    const ::grpc::internal::RpcMethod rpcmethod_SetDiagnosticSettings_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetVelocityStream(::grpc::ServerContext* context, const ::carbon::simulator_UI::GetVelocityRequest* request, ::grpc::ServerWriter< ::carbon::simulator_UI::GetVelocityResponse>* writer);
    virtual ::grpc::Status SetVelocity(::grpc::ServerContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response);
    virtual ::grpc::Status GetPresetStream(::grpc::ServerContext* context, const ::carbon::simulator_UI::GetPresetsRequest* request, ::grpc::ServerWriter< ::carbon::simulator_UI::GetPresetsResponse>* writer);
    virtual ::grpc::Status SetPreset(::grpc::ServerContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response);
    virtual ::grpc::Status GetDiagnosticSettings(::grpc::ServerContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response);
    virtual ::grpc::Status SetDiagnosticSettings(::grpc::ServerContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetVelocityStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetVelocityStream() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetVelocityStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVelocityStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetVelocityRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetVelocityResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetVelocityStream(::grpc::ServerContext* context, ::carbon::simulator_UI::GetVelocityRequest* request, ::grpc::ServerAsyncWriter< ::carbon::simulator_UI::GetVelocityResponse>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(0, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetVelocity() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SetVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetVelocity(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetVelocityRequest* /*request*/, ::carbon::simulator_UI::SetVelocityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetVelocity(::grpc::ServerContext* context, ::carbon::simulator_UI::SetVelocityRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::simulator_UI::SetVelocityResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPresetStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPresetStream() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetPresetStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetPresetsRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetPresetsResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPresetStream(::grpc::ServerContext* context, ::carbon::simulator_UI::GetPresetsRequest* request, ::grpc::ServerAsyncWriter< ::carbon::simulator_UI::GetPresetsResponse>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(2, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetPreset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetPreset() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SetPreset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetPreset(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetPresetRequest* /*request*/, ::carbon::simulator_UI::SetPresetResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetPreset(::grpc::ServerContext* context, ::carbon::simulator_UI::SetPresetRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::simulator_UI::SetPresetResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetDiagnosticSettings() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDiagnosticSettings(::grpc::ServerContext* context, ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetDiagnosticSettings() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_SetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetDiagnosticSettings(::grpc::ServerContext* context, ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetVelocityStream<WithAsyncMethod_SetVelocity<WithAsyncMethod_GetPresetStream<WithAsyncMethod_SetPreset<WithAsyncMethod_GetDiagnosticSettings<WithAsyncMethod_SetDiagnosticSettings<Service > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetVelocityStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetVelocityStream() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackServerStreamingHandler< ::carbon::simulator_UI::GetVelocityRequest, ::carbon::simulator_UI::GetVelocityResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::simulator_UI::GetVelocityRequest* request) { return this->GetVelocityStream(context, request); }));
    }
    ~WithCallbackMethod_GetVelocityStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVelocityStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetVelocityRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetVelocityResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::carbon::simulator_UI::GetVelocityResponse>* GetVelocityStream(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::simulator_UI::GetVelocityRequest* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetVelocity() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::simulator_UI::SetVelocityRequest, ::carbon::simulator_UI::SetVelocityResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response) { return this->SetVelocity(context, request, response); }));}
    void SetMessageAllocatorFor_SetVelocity(
        ::grpc::MessageAllocator< ::carbon::simulator_UI::SetVelocityRequest, ::carbon::simulator_UI::SetVelocityResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::simulator_UI::SetVelocityRequest, ::carbon::simulator_UI::SetVelocityResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetVelocity(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetVelocityRequest* /*request*/, ::carbon::simulator_UI::SetVelocityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetVelocity(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::simulator_UI::SetVelocityRequest* /*request*/, ::carbon::simulator_UI::SetVelocityResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPresetStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPresetStream() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackServerStreamingHandler< ::carbon::simulator_UI::GetPresetsRequest, ::carbon::simulator_UI::GetPresetsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::simulator_UI::GetPresetsRequest* request) { return this->GetPresetStream(context, request); }));
    }
    ~WithCallbackMethod_GetPresetStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetPresetsRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetPresetsResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::carbon::simulator_UI::GetPresetsResponse>* GetPresetStream(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::simulator_UI::GetPresetsRequest* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetPreset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetPreset() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::simulator_UI::SetPresetRequest, ::carbon::simulator_UI::SetPresetResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response) { return this->SetPreset(context, request, response); }));}
    void SetMessageAllocatorFor_SetPreset(
        ::grpc::MessageAllocator< ::carbon::simulator_UI::SetPresetRequest, ::carbon::simulator_UI::SetPresetResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::simulator_UI::SetPresetRequest, ::carbon::simulator_UI::SetPresetResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetPreset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetPreset(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetPresetRequest* /*request*/, ::carbon::simulator_UI::SetPresetResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetPreset(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::simulator_UI::SetPresetRequest* /*request*/, ::carbon::simulator_UI::SetPresetResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetDiagnosticSettings() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::carbon::simulator_UI::GetDiagnosticSettingsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response) { return this->GetDiagnosticSettings(context, request, response); }));}
    void SetMessageAllocatorFor_GetDiagnosticSettings(
        ::grpc::MessageAllocator< ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::carbon::simulator_UI::GetDiagnosticSettingsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDiagnosticSettings(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetDiagnosticSettings() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::carbon::simulator_UI::SetDiagnosticSettingsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response) { return this->SetDiagnosticSettings(context, request, response); }));}
    void SetMessageAllocatorFor_SetDiagnosticSettings(
        ::grpc::MessageAllocator< ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::carbon::simulator_UI::SetDiagnosticSettingsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetDiagnosticSettings(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetVelocityStream<WithCallbackMethod_SetVelocity<WithCallbackMethod_GetPresetStream<WithCallbackMethod_SetPreset<WithCallbackMethod_GetDiagnosticSettings<WithCallbackMethod_SetDiagnosticSettings<Service > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetVelocityStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetVelocityStream() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetVelocityStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVelocityStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetVelocityRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetVelocityResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetVelocity() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SetVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetVelocity(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetVelocityRequest* /*request*/, ::carbon::simulator_UI::SetVelocityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPresetStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPresetStream() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetPresetStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetPresetsRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetPresetsResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetPreset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetPreset() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SetPreset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetPreset(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetPresetRequest* /*request*/, ::carbon::simulator_UI::SetPresetResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetDiagnosticSettings() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetDiagnosticSettings() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_SetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetVelocityStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetVelocityStream() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetVelocityStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVelocityStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetVelocityRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetVelocityResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetVelocityStream(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(0, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetVelocity() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SetVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetVelocity(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetVelocityRequest* /*request*/, ::carbon::simulator_UI::SetVelocityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetVelocity(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPresetStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPresetStream() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetPresetStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetPresetsRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetPresetsResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPresetStream(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(2, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetPreset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetPreset() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SetPreset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetPreset(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetPresetRequest* /*request*/, ::carbon::simulator_UI::SetPresetResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetPreset(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetDiagnosticSettings() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDiagnosticSettings(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetDiagnosticSettings() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_SetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetDiagnosticSettings(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetVelocityStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetVelocityStream() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackServerStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const::grpc::ByteBuffer* request) { return this->GetVelocityStream(context, request); }));
    }
    ~WithRawCallbackMethod_GetVelocityStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVelocityStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetVelocityRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetVelocityResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::grpc::ByteBuffer>* GetVelocityStream(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetVelocity() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetVelocity(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetVelocity(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetVelocityRequest* /*request*/, ::carbon::simulator_UI::SetVelocityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetVelocity(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPresetStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPresetStream() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackServerStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const::grpc::ByteBuffer* request) { return this->GetPresetStream(context, request); }));
    }
    ~WithRawCallbackMethod_GetPresetStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetPresetsRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetPresetsResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::grpc::ByteBuffer>* GetPresetStream(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetPreset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetPreset() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetPreset(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetPreset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetPreset(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetPresetRequest* /*request*/, ::carbon::simulator_UI::SetPresetResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetPreset(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetDiagnosticSettings() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetDiagnosticSettings(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDiagnosticSettings(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetDiagnosticSettings() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetDiagnosticSettings(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetDiagnosticSettings(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetVelocity() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::simulator_UI::SetVelocityRequest, ::carbon::simulator_UI::SetVelocityResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::simulator_UI::SetVelocityRequest, ::carbon::simulator_UI::SetVelocityResponse>* streamer) {
                       return this->StreamedSetVelocity(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetVelocity(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetVelocityRequest* /*request*/, ::carbon::simulator_UI::SetVelocityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetVelocity(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::simulator_UI::SetVelocityRequest,::carbon::simulator_UI::SetVelocityResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetPreset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetPreset() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::simulator_UI::SetPresetRequest, ::carbon::simulator_UI::SetPresetResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::simulator_UI::SetPresetRequest, ::carbon::simulator_UI::SetPresetResponse>* streamer) {
                       return this->StreamedSetPreset(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetPreset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetPreset(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetPresetRequest* /*request*/, ::carbon::simulator_UI::SetPresetResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetPreset(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::simulator_UI::SetPresetRequest,::carbon::simulator_UI::SetPresetResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetDiagnosticSettings() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::carbon::simulator_UI::GetDiagnosticSettingsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* streamer) {
                       return this->StreamedGetDiagnosticSettings(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetDiagnosticSettings(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::simulator_UI::GetDiagnosticSettingsRequest,::carbon::simulator_UI::GetDiagnosticSettingsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetDiagnosticSettings : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetDiagnosticSettings() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::carbon::simulator_UI::SetDiagnosticSettingsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* streamer) {
                       return this->StreamedSetDiagnosticSettings(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetDiagnosticSettings() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetDiagnosticSettings(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* /*request*/, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetDiagnosticSettings(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::simulator_UI::SetDiagnosticSettingsRequest,::carbon::simulator_UI::SetDiagnosticSettingsResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_SetVelocity<WithStreamedUnaryMethod_SetPreset<WithStreamedUnaryMethod_GetDiagnosticSettings<WithStreamedUnaryMethod_SetDiagnosticSettings<Service > > > > StreamedUnaryService;
  template <class BaseClass>
  class WithSplitStreamingMethod_GetVelocityStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithSplitStreamingMethod_GetVelocityStream() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::SplitServerStreamingHandler<
          ::carbon::simulator_UI::GetVelocityRequest, ::carbon::simulator_UI::GetVelocityResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerSplitStreamer<
                     ::carbon::simulator_UI::GetVelocityRequest, ::carbon::simulator_UI::GetVelocityResponse>* streamer) {
                       return this->StreamedGetVelocityStream(context,
                         streamer);
                  }));
    }
    ~WithSplitStreamingMethod_GetVelocityStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetVelocityStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetVelocityRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetVelocityResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedGetVelocityStream(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::carbon::simulator_UI::GetVelocityRequest,::carbon::simulator_UI::GetVelocityResponse>* server_split_streamer) = 0;
  };
  template <class BaseClass>
  class WithSplitStreamingMethod_GetPresetStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithSplitStreamingMethod_GetPresetStream() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::SplitServerStreamingHandler<
          ::carbon::simulator_UI::GetPresetsRequest, ::carbon::simulator_UI::GetPresetsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerSplitStreamer<
                     ::carbon::simulator_UI::GetPresetsRequest, ::carbon::simulator_UI::GetPresetsResponse>* streamer) {
                       return this->StreamedGetPresetStream(context,
                         streamer);
                  }));
    }
    ~WithSplitStreamingMethod_GetPresetStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPresetStream(::grpc::ServerContext* /*context*/, const ::carbon::simulator_UI::GetPresetsRequest* /*request*/, ::grpc::ServerWriter< ::carbon::simulator_UI::GetPresetsResponse>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedGetPresetStream(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::carbon::simulator_UI::GetPresetsRequest,::carbon::simulator_UI::GetPresetsResponse>* server_split_streamer) = 0;
  };
  typedef WithSplitStreamingMethod_GetVelocityStream<WithSplitStreamingMethod_GetPresetStream<Service > > SplitStreamedService;
  typedef WithSplitStreamingMethod_GetVelocityStream<WithStreamedUnaryMethod_SetVelocity<WithSplitStreamingMethod_GetPresetStream<WithStreamedUnaryMethod_SetPreset<WithStreamedUnaryMethod_GetDiagnosticSettings<WithStreamedUnaryMethod_SetDiagnosticSettings<Service > > > > > > StreamedService;
};

}  // namespace simulator_UI
}  // namespace carbon


#endif  // GRPC_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto__INCLUDED
