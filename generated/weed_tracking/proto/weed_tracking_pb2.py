# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: weed_tracking/proto/weed_tracking.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='weed_tracking/proto/weed_tracking.proto',
  package='weed_tracking',
  syntax='proto3',
  serialized_options=b'Z\023proto/weed_tracking',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\'weed_tracking/proto/weed_tracking.proto\x12\rweed_tracking\"\x07\n\x05\x45mpty\"\xe9\x01\n\nTrajectory\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\ntracker_id\x18\x02 \x01(\r\x12\x0e\n\x06status\x18\x03 \x01(\r\x12\x0f\n\x07is_weed\x18\x04 \x01(\x08\x12\x0c\n\x04x_mm\x18\x05 \x01(\x01\x12\x0c\n\x04y_mm\x18\x06 \x01(\x01\x12\x0c\n\x04z_mm\x18\x07 \x01(\x01\x12%\n\x1dintersected_with_nonshootable\x18\x08 \x01(\x08\x12 \n\x18nonshootable_type_string\x18\t \x01(\t\x12#\n\x1b\x64\x65\x64uplicated_across_tracker\x18\n \x01(\x08:\x02\x18\x01\"\x81\x01\n\x06Target\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x15\n\rtrajectory_id\x18\x02 \x01(\r\x12\x1a\n\x12next_trajectory_id\x18\x03 \x01(\r\x12\x16\n\x0estarting_pos_y\x18\x04 \x01(\x02\x12\x14\n\x0c\x65nding_pos_y\x18\x05 \x01(\x02:\x02\x18\x01\"6\n\x06\x42ounds\x12\x12\n\ntracker_id\x18\x01 \x01(\t\x12\x14\n\x0cmax_pos_mm_y\x18\x02 \x01(\x02:\x02\x18\x01\"\x99\x01\n\x13TrackingStatusReply\x12&\n\x07targets\x18\x01 \x03(\x0b\x32\x15.weed_tracking.Target\x12/\n\x0ctrajectories\x18\x02 \x03(\x0b\x32\x19.weed_tracking.Trajectory\x12%\n\x06\x62ounds\x18\x03 \x03(\x0b\x32\x15.weed_tracking.Bounds:\x02\x18\x01\"K\n\x14GetDetectionsRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\r\n\x05max_x\x18\x03 \x01(\x05\"\xcd\x01\n\tDetection\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x0c\n\x04size\x18\x03 \x01(\x02\x12\x0b\n\x03\x63lz\x18\x04 \x01(\t\x12\x0f\n\x07is_weed\x18\x05 \x01(\x08\x12\x13\n\x0bout_of_band\x18\x06 \x01(\x08\x12\n\n\x02id\x18\x07 \x01(\r\x12\r\n\x05score\x18\x08 \x01(\x02\x12\x12\n\nweed_score\x18\t \x01(\x02\x12\x12\n\ncrop_score\x18\n \x01(\x02\x12\x11\n\tembedding\x18\x0b \x03(\x02\x12\x13\n\x0bplant_score\x18\x0c \x01(\x02\"P\n\nDetections\x12,\n\ndetections\x18\x01 \x03(\x0b\x32\x18.weed_tracking.Detection\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\",\n\x04\x42\x61nd\x12\x12\n\nstart_x_px\x18\x01 \x01(\x01\x12\x10\n\x08\x65nd_x_px\x18\x02 \x01(\x01\"b\n\x05\x42\x61nds\x12!\n\x04\x62\x61nd\x18\x01 \x03(\x0b\x32\x13.weed_tracking.Band\x12\x17\n\x0f\x62\x61nding_enabled\x18\x02 \x01(\x08\x12\x1d\n\x15row_has_bands_defined\x18\x03 \x01(\x08\"k\n\x15GetDetectionsResponse\x12-\n\ndetections\x18\x01 \x01(\x0b\x32\x19.weed_tracking.Detections\x12#\n\x05\x62\x61nds\x18\x02 \x01(\x0b\x32\x14.weed_tracking.Bands\"\x1e\n\x1cGetTrajectoryMetadataRequest\"A\n\x13TrackedItemMetadata\x12\x14\n\x0c\x64\x65tection_id\x18\x01 \x01(\r\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"\x93\x01\n\x12TrajectoryMetadata\x12\x15\n\rtrajectory_id\x18\x01 \x01(\r\x12\x0e\n\x06\x63\x61m_id\x18\x02 \x01(\t\x12\x41\n\x15tracked_item_metadata\x18\x03 \x03(\x0b\x32\".weed_tracking.TrackedItemMetadata\x12\x13\n\x0b\x62\x61nd_status\x18\x04 \x01(\t\"T\n\x1dGetTrajectoryMetadataResponse\x12\x33\n\x08metadata\x18\x01 \x03(\x0b\x32!.weed_tracking.TrajectoryMetadata\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\r\"\x16\n\tPongReply\x12\t\n\x01x\x18\x01 \x01(\r\"\xa1\x01\n\x0fTrajectoryScore\x12\r\n\x05score\x18\x01 \x01(\x04\x12\x12\n\nscore_tilt\x18\x02 \x01(\x01\x12\x11\n\tscore_pan\x18\x03 \x01(\x01\x12\x12\n\nscore_busy\x18\x04 \x01(\x01\x12\x16\n\x0escore_in_range\x18\x05 \x01(\x01\x12\x18\n\x10score_importance\x18\x06 \x01(\x01\x12\x12\n\nscore_size\x18\x07 \x01(\x01\"4\n\x0fPerScannerScore\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\r\n\x05score\x18\x02 \x01(\x05\"r\n\nScoreState\x12\x34\n\x0ctarget_state\x18\x01 \x01(\x0e\x32\x1e.weed_tracking.TargetableState\x12.\n\x06scores\x18\x02 \x03(\x0b\x32\x1e.weed_tracking.PerScannerScore\"\x1d\n\x05Pos2D\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\"q\n\x07KillBox\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12&\n\x08top_left\x18\x02 \x01(\x0b\x32\x14.weed_tracking.Pos2D\x12*\n\x0c\x62ottom_right\x18\x03 \x01(\x0b\x32\x14.weed_tracking.Pos2D\"\x95\x01\n\nThresholds\x12\x13\n\x07weeding\x18\x01 \x01(\x02\x42\x02\x18\x01\x12\x14\n\x08thinning\x18\x02 \x01(\x02\x42\x02\x18\x01\x12\x13\n\x07\x62\x61nding\x18\x03 \x01(\x02\x42\x02\x18\x01\x12\x16\n\x0epassed_weeding\x18\x04 \x01(\x08\x12\x17\n\x0fpassed_thinning\x18\x05 \x01(\x08\x12\x16\n\x0epassed_banding\x18\x06 \x01(\x08\"\x92\x01\n\tDecisions\x12\x14\n\x0cweeding_weed\x18\x01 \x01(\x08\x12\x14\n\x0cweeding_crop\x18\x02 \x01(\x08\x12\x15\n\rthinning_weed\x18\x03 \x01(\x08\x12\x15\n\rthinning_crop\x18\x04 \x01(\x08\x12\x15\n\rkeepable_crop\x18\x05 \x01(\x08\x12\x14\n\x0c\x62\x61nding_crop\x18\x06 \x01(\x08\"\xda\n\n\x12TrajectorySnapshot\x12\n\n\x02id\x18\x01 \x01(\r\x12.\n\x0bkill_status\x18\x02 \x01(\x0e\x32\x19.weed_tracking.KillStatus\x12\x13\n\x07is_weed\x18\x03 \x01(\x08\x42\x02\x18\x01\x12\x0c\n\x04x_mm\x18\x04 \x01(\x01\x12\x0c\n\x04y_mm\x18\x05 \x01(\x01\x12\x0c\n\x04z_mm\x18\x06 \x01(\x01\x12/\n\x05score\x18\x07 \x01(\x0b\x32\x1e.weed_tracking.TrajectoryScoreH\x00\x12:\n\rinvalid_score\x18\x08 \x01(\x0e\x32!.weed_tracking.InvalidScoreReasonH\x00\x12\x11\n\tradius_mm\x18\t \x01(\x01\x12\x10\n\x08\x63\x61tegory\x18\n \x01(\t\x12\x1f\n\x17shoot_time_requested_ms\x18\x0b \x01(\x04\x12\x1c\n\x14shoot_time_actual_ms\x18\x0c \x01(\x04\x12\x1b\n\x13marked_for_thinning\x18\r \x01(\x08\x12\x12\n\ntracker_id\x18\x0e \x01(\r\x12\x38\n\x10\x64uplicate_status\x18\x0f \x01(\x0e\x32\x1e.weed_tracking.DuplicateStatus\x12\x1f\n\x17\x64uplicate_trajectory_id\x18\x10 \x01(\r\x12\x17\n\x0f\x61ssigned_lasers\x18\x11 \x03(\r\x12\x13\n\x0bout_of_band\x18\x12 \x01(\x08\x12%\n\x1dintersected_with_nonshootable\x18\x13 \x01(\x08\x12R\n\x11\x64\x65tection_classes\x18\x14 \x03(\x0b\x32\x37.weed_tracking.TrajectorySnapshot.DetectionClassesEntry\x12\x12\n\nconfidence\x18\x15 \x01(\x01\x12\x34\n\x0ethinning_state\x18\x16 \x01(\x0e\x32\x1c.weed_tracking.ThinningState\x12\x12\n\nglobal_pos\x18\x17 \x01(\x01\x12.\n\x0bscore_state\x18\x18 \x01(\x0b\x32\x19.weed_tracking.ScoreState\x12\x0b\n\x03\x64oo\x18\x19 \x01(\x02\x12#\n\x1b\x64istance_perspectives_count\x18\x1a \x01(\r\x12\x1b\n\x13size_category_index\x18\x1b \x01(\x05\x12\x1b\n\x13speculative_allowed\x18\x1c \x01(\x08\x12\x19\n\x11protected_by_traj\x18\x1d \x01(\x08\x12-\n\nthresholds\x18\x1e \x01(\x0b\x32\x19.weed_tracking.Thresholds\x12+\n\tdecisions\x18\x1f \x01(\x0b\x32\x18.weed_tracking.Decisions\x12\x12\n\ncrop_score\x18  \x01(\x01\x12\x12\n\nweed_score\x18! \x01(\x01\x12\x13\n\x0bplant_score\x18\" \x01(\x01\x12(\n num_detections_used_for_decision\x18# \x01(\r\x12\x35\n\x0e\x63lassification\x18$ \x01(\x0e\x32\x1d.weed_tracking.Classification\x12(\n speculative_shoot_time_actual_ms\x18& \x01(\r\x12:\n\x11snapshot_metadata\x18\' \x01(\x0b\x32\x1f.weed_tracking.SnapshotMetadata\x1a\x37\n\x15\x44\x65tectionClassesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\x42\x0f\n\rscore_detailsJ\x04\x08%\x10&\"c\n\x10SnapshotMetadata\x12\x0f\n\x07pcam_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x13\n\x0b\x63\x65nter_x_px\x18\x03 \x01(\x02\x12\x13\n\x0b\x63\x65nter_y_px\x18\x04 \x01(\x02\"A\n\x0e\x42\x61ndDefinition\x12\x11\n\toffset_mm\x18\x01 \x01(\x02\x12\x10\n\x08width_mm\x18\x02 \x01(\x02\x12\n\n\x02id\x18\x03 \x01(\x05\"\x91\x01\n\x14\x43LDAlgorithmSnapshot\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x16\n\x0egraph_points_x\x18\x02 \x03(\x02\x12\x16\n\x0egraph_points_y\x18\x03 \x03(\x02\x12\x11\n\tminimas_x\x18\x04 \x03(\x02\x12\x11\n\tminimas_y\x18\x05 \x03(\x02\x12\r\n\x05lines\x18\x06 \x03(\x02\"\xab\x02\n\x13\x44iagnosticsSnapshot\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x37\n\x0ctrajectories\x18\x02 \x03(\x0b\x32!.weed_tracking.TrajectorySnapshot\x12,\n\x05\x62\x61nds\x18\x03 \x03(\x0b\x32\x1d.weed_tracking.BandDefinition\x12*\n\nkill_boxes\x18\x04 \x03(\x0b\x32\x16.weed_tracking.KillBox\x12L\n\x1a\x62\x61nding_algorithm_snapshot\x18\x05 \x01(\x0b\x32#.weed_tracking.CLDAlgorithmSnapshotH\x00\x88\x01\x01\x42\x1d\n\x1b_banding_algorithm_snapshot\"e\n\x18RecordDiagnosticsRequest\x12\x0f\n\x07ttl_sec\x18\x01 \x01(\r\x12\x1b\n\x13\x63rop_images_per_sec\x18\x02 \x01(\x02\x12\x1b\n\x13weed_images_per_sec\x18\x03 \x01(\x02\"L\n\x1aGetRecordingStatusResponse\x12.\n\x06status\x18\x01 \x01(\x0e\x32\x1e.weed_tracking.RecordingStatus\"M\n)StartSavingCropLineDetectionReplayRequest\x12\x10\n\x08\x66ilename\x18\x01 \x01(\t\x12\x0e\n\x06ttl_ms\x18\x02 \x01(\r\"\xb4\x01\n\x18RecordAimbotInputRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06ttl_ms\x18\x02 \x01(\r\x12\x19\n\x0crotary_ticks\x18\x03 \x01(\x08H\x00\x88\x01\x01\x12\x15\n\x08\x64\x65\x65pweed\x18\x04 \x01(\x08H\x01\x88\x01\x01\x12\x19\n\x0clane_heights\x18\x05 \x01(\x08H\x02\x88\x01\x01\x42\x0f\n\r_rotary_ticksB\x0b\n\t_deepweedB\x0f\n\r_lane_heights\"M\n\x0f\x43onclusionCount\x12+\n\x04type\x18\x01 \x01(\x0e\x32\x1d.weed_tracking.ConclusionType\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"C\n\x11\x43onclusionCounter\x12.\n\x06\x63ounts\x18\x01 \x03(\x0b\x32\x1e.weed_tracking.ConclusionCount\"w\n\x0f\x42\x61ndDefinitions\x12,\n\x05\x62\x61nds\x18\x01 \x03(\x0b\x32\x1d.weed_tracking.BandDefinition\x12\x17\n\x0f\x62\x61nding_enabled\x18\x02 \x01(\x08\x12\x1d\n\x15row_has_bands_defined\x18\x03 \x01(\x08\"\xa2\x02\n\x1aPlantCaptchaStatusResponse\x12\x31\n\x06status\x18\x01 \x01(\x0e\x32!.weed_tracking.PlantCaptchaStatus\x12\x14\n\x0ctotal_images\x18\x02 \x01(\x05\x12\x14\n\x0cimages_taken\x18\x03 \x01(\x05\x12\x16\n\x0emetadata_taken\x18\x04 \x01(\x05\x12V\n\x0f\x65xemplar_counts\x18\x05 \x03(\x0b\x32=.weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry\x1a\x35\n\x13\x45xemplarCountsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\"\x1d\n\tEmbedding\x12\x10\n\x08\x65lements\x18\x01 \x03(\x02\"w\n\x0bWeedClasses\x12\x38\n\x07\x63lasses\x18\x01 \x03(\x0b\x32\'.weed_tracking.WeedClasses.ClassesEntry\x1a.\n\x0c\x43lassesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\xf6\x08\n\x18PlantCaptchaItemMetadata\x12\x12\n\nconfidence\x18\x01 \x01(\x02\x12\x0c\n\x04x_px\x18\x02 \x01(\x05\x12\x0c\n\x04y_px\x18\x03 \x01(\x05\x12\x0c\n\x04x_mm\x18\x04 \x01(\x01\x12\x0c\n\x04y_mm\x18\x05 \x01(\x01\x12\x0c\n\x04z_mm\x18\x06 \x01(\x01\x12\x0f\n\x07size_mm\x18\x07 \x01(\x02\x12K\n\ncategories\x18\x08 \x03(\x0b\x32\x37.weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry\x12\x0b\n\x03\x64oo\x18\t \x01(\x02\x12\x0f\n\x07is_weed\x18\n \x01(\x08\x12%\n\x1dintersected_with_nonshootable\x18\x0b \x01(\x08\x12\x1a\n\x12\x63onfidence_history\x18\x0c \x03(\x02\x12\x12\n\nis_in_band\x18\r \x01(\x08\x12\n\n\x02id\x18\x0e \x01(\t\x12\x0f\n\x07size_px\x18\x0f \x01(\x02\x12\x15\n\rshoot_time_ms\x18\x10 \x01(\r\x12\x1f\n\x17weed_confidence_history\x18\x11 \x03(\x02\x12\x1f\n\x17\x63rop_confidence_history\x18\x12 \x03(\x02\x12\x1b\n\x13size_category_index\x18\x13 \x01(\x05\x12T\n\x0fweed_categories\x18\x14 \x03(\x0b\x32;.weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry\x12\x33\n\x11\x65mbedding_history\x18\x15 \x03(\x0b\x32\x18.weed_tracking.Embedding\x12@\n\rinitial_label\x18\x16 \x01(\x0e\x32).weed_tracking.PlantCaptchaUserPrediction\x12 \n\x18plant_confidence_history\x18\x17 \x03(\x02\x12\x38\n\x14weed_classes_history\x18\x18 \x03(\x0b\x32\x1a.weed_tracking.WeedClasses\x12\x17\n\x0fsize_mm_history\x18\x19 \x03(\x02\x12+\n\tdecisions\x18\x1a \x01(\x0b\x32\x18.weed_tracking.Decisions\x12(\n num_detections_used_for_decision\x18\x1b \x01(\r\x12\\\n\x13\x65mbedding_distances\x18\x1c \x03(\x0b\x32?.weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry\x1a\x31\n\x0f\x43\x61tegoriesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x1a\x35\n\x13WeedCategoriesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x1a\x39\n\x17\x45mbeddingDistancesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\x1c\n\x1aGetTargetingEnabledRequest\".\n\x1bGetTargetingEnabledResponse\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"\x12\n\x10GetBootedRequest\"#\n\x11GetBootedResponse\x12\x0e\n\x06\x62ooted\x18\x01 \x01(\x08*\xc4\x02\n\x12InvalidScoreReason\x12\x08\n\x04NONE\x10\x00\x12\x1a\n\x16\x41NOTHER_LASER_SHOOTING\x10\x01\x12\x1c\n\x18SCANNER_POSITION_INVALID\x10\x02\x12\x17\n\x13WEED_ALREADY_KILLED\x10\x03\x12\x1e\n\x1aWEED_SHOT_BY_ANOTHER_LASER\x10\x04\x12\x14\n\x10WEED_OUT_OF_BAND\x10\x05\x12\x0e\n\nNOT_A_WEED\x10\x06\x12\x12\n\x0eSCORE_NEGATIVE\x10\x07\x12!\n\x1dINTERSECTED_WITH_NONSHOOTABLE\x10\x08\x12\'\n#EXTERMINATION_FAILURES_EXCEEDED_MAX\x10\t\x12+\n\'DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN\x10\n*\xb0\x01\n\nKillStatus\x12\x13\n\x0fSTATUS_NOT_SHOT\x10\x00\x12\x15\n\x11STATUS_BEING_SHOT\x10\x01\x12\x0f\n\x0bSTATUS_SHOT\x10\x02\x12\x19\n\x15STATUS_PARTIALLY_SHOT\x10\x03\x12\x18\n\x14STATUS_P2P_NOT_FOUND\x10\x04\x12\x10\n\x0cSTATUS_ERROR\x10\x05\x12\x1e\n\x1aSTATUS_P2P_MISSING_CONTEXT\x10\x06*9\n\x0f\x44uplicateStatus\x12\n\n\x06UNIQUE\x10\x00\x12\x0b\n\x07PRIMARY\x10\x01\x12\r\n\tDUPLICATE\x10\x02*n\n\rThinningState\x12\x12\n\x0eTHINNING_UNSET\x10\x00\x12 \n\x1cTHINNING_MARKED_FOR_THINNING\x10\x01\x12\x11\n\rTHINNING_KEPT\x10\x02\x12\x14\n\x10THINNING_IGNORED\x10\x03*\xf4\x01\n\x0fTargetableState\x12\x1b\n\x17TARGET_NOT_IN_SCHEDULER\x10\x00\x12\x11\n\rTARGET_SCORED\x10\x01\x12#\n\x1fTARGET_INTERSECTS_NON_SHOOTABLE\x10\x02\x12\x1c\n\x18TARGET_TOO_MANY_FAILURES\x10\x03\x12\x16\n\x12TARGET_DOO_TOO_LOW\x10\x04\x12\x1f\n\x1bTARGET_IGNORED_FROM_ALMANAC\x10\x05\x12\x16\n\x12TARGET_OUT_OF_BAND\x10\x06\x12\x1d\n\x19TARGET_AVOID_FROM_ALMANAC\x10\x07*h\n\x0e\x43lassification\x12\x13\n\x0f\x43LASS_UNDECIDED\x10\x00\x12\x0e\n\nCLASS_WEED\x10\x01\x12\x0e\n\nCLASS_CROP\x10\x02\x12\x0e\n\nCLASS_BOTH\x10\x03\x12\x11\n\rCLASS_UNKNOWN\x10\x04*i\n\x0fRecordingStatus\x12\x11\n\rNOT_RECORDING\x10\x00\x12\x15\n\x11RECORDING_STARTED\x10\x01\x12\x16\n\x12RECORDING_FINISHED\x10\x02\x12\x14\n\x10RECORDING_FAILED\x10\x03*\x93\x02\n\x0e\x43onclusionType\x12\x0f\n\x0bNOT_WEEDING\x10\x00\x12\x0f\n\x0bOUT_OF_BAND\x10\x01\x12!\n\x1dINTERSECTS_WITH_NON_SHOOTABLE\x10\x02\x12\x10\n\x0cOUT_OF_RANGE\x10\x03\x12\x0f\n\x0bUNIMPORTANT\x10\x04\x12\x0c\n\x08NOT_SHOT\x10\x05\x12\x12\n\x0ePARTIALLY_SHOT\x10\x06\x12\x08\n\x04SHOT\x10\x07\x12\x11\n\rP2P_NOT_FOUND\x10\x08\x12\t\n\x05\x45RROR\x10\t\x12\x0b\n\x07\x46LICKER\x10\n\x12\x17\n\x13MARKED_FOR_THINNING\x10\x0b\x12\x10\n\x0cNOT_TARGETED\x10\x0c\x12\x17\n\x13P2P_MISSING_CONTEXT\x10\r*{\n\x12PlantCaptchaStatus\x12\x0f\n\x0bNOT_STARTED\x10\x00\x12\x13\n\x0f\x43\x41PTCHA_STARTED\x10\x01\x12\x14\n\x10\x43\x41PTCHA_FINISHED\x10\x02\x12\x12\n\x0e\x43\x41PTCHA_FAILED\x10\x03\x12\x15\n\x11\x43\x41PTCHA_CANCELLED\x10\x04*\x7f\n\x1aPlantCaptchaUserPrediction\x12\x08\n\x04WEED\x10\x00\x12\x08\n\x04\x43ROP\x10\x01\x12\x0b\n\x07UNKNOWN\x10\x02\x12\t\n\x05OTHER\x10\x03\x12\n\n\x06IGNORE\x10\x04\x12\r\n\tVOLUNTEER\x10\x05\x12\x0e\n\nBENEFICIAL\x10\x06\x12\n\n\x06\x44\x45\x42RIS\x10\x07\x32\x88\x0c\n\x13WeedTrackingService\x12>\n\x04Ping\x12\x1a.weed_tracking.PingRequest\x1a\x18.weed_tracking.PongReply\"\x00\x12Z\n\rGetDetections\x12#.weed_tracking.GetDetectionsRequest\x1a$.weed_tracking.GetDetectionsResponse\x12r\n\x15GetTrajectoryMetadata\x12+.weed_tracking.GetTrajectoryMetadataRequest\x1a,.weed_tracking.GetTrajectoryMetadataResponse\x12\x39\n\x0bUpdateBands\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12P\n\tGetBooted\x12\x1f.weed_tracking.GetBootedRequest\x1a .weed_tracking.GetBootedResponse\"\x00\x12R\n\x16GetCurrentTrajectories\x12\x14.weed_tracking.Empty\x1a\".weed_tracking.DiagnosticsSnapshot\x12t\n\"StartSavingCropLineDetectionReplay\x12\x38.weed_tracking.StartSavingCropLineDetectionReplayRequest\x1a\x14.weed_tracking.Empty\x12Z\n\x19StartRecordingDiagnostics\x12\'.weed_tracking.RecordDiagnosticsRequest\x1a\x14.weed_tracking.Empty\x12`\n\x1dGetDiagnosticsRecordingStatus\x12\x14.weed_tracking.Empty\x1a).weed_tracking.GetRecordingStatusResponse\x12G\n\x19RemoveRecordingsDirectory\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12[\n\x1aStartRecordingAimbotInputs\x12\'.weed_tracking.RecordAimbotInputRequest\x1a\x14.weed_tracking.Empty\x12N\n\x14GetConclusionCounter\x12\x14.weed_tracking.Empty\x1a .weed_tracking.ConclusionCounter\x12@\n\x08GetBands\x12\x14.weed_tracking.Empty\x1a\x1e.weed_tracking.BandDefinitions\x12?\n\x11StartPlantCaptcha\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12X\n\x15GetPlantCaptchaStatus\x12\x14.weed_tracking.Empty\x1a).weed_tracking.PlantCaptchaStatusResponse\x12I\n\x1bRemovePlantCaptchaDirectory\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12@\n\x12\x43\x61ncelPlantCaptcha\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12l\n\x13GetTargetingEnabled\x12).weed_tracking.GetTargetingEnabledRequest\x1a*.weed_tracking.GetTargetingEnabledResponseB\x15Z\x13proto/weed_trackingb\x06proto3'
)

_INVALIDSCOREREASON = _descriptor.EnumDescriptor(
  name='InvalidScoreReason',
  full_name='weed_tracking.InvalidScoreReason',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ANOTHER_LASER_SHOOTING', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SCANNER_POSITION_INVALID', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='WEED_ALREADY_KILLED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='WEED_SHOT_BY_ANOTHER_LASER', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='WEED_OUT_OF_BAND', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NOT_A_WEED', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SCORE_NEGATIVE', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INTERSECTED_WITH_NONSHOOTABLE', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='EXTERMINATION_FAILURES_EXCEEDED_MAX', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=6864,
  serialized_end=7188,
)
_sym_db.RegisterEnumDescriptor(_INVALIDSCOREREASON)

InvalidScoreReason = enum_type_wrapper.EnumTypeWrapper(_INVALIDSCOREREASON)
_KILLSTATUS = _descriptor.EnumDescriptor(
  name='KillStatus',
  full_name='weed_tracking.KillStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STATUS_NOT_SHOT', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_BEING_SHOT', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_SHOT', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_PARTIALLY_SHOT', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_P2P_NOT_FOUND', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_ERROR', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_P2P_MISSING_CONTEXT', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7191,
  serialized_end=7367,
)
_sym_db.RegisterEnumDescriptor(_KILLSTATUS)

KillStatus = enum_type_wrapper.EnumTypeWrapper(_KILLSTATUS)
_DUPLICATESTATUS = _descriptor.EnumDescriptor(
  name='DuplicateStatus',
  full_name='weed_tracking.DuplicateStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNIQUE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PRIMARY', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DUPLICATE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7369,
  serialized_end=7426,
)
_sym_db.RegisterEnumDescriptor(_DUPLICATESTATUS)

DuplicateStatus = enum_type_wrapper.EnumTypeWrapper(_DUPLICATESTATUS)
_THINNINGSTATE = _descriptor.EnumDescriptor(
  name='ThinningState',
  full_name='weed_tracking.ThinningState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='THINNING_UNSET', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='THINNING_MARKED_FOR_THINNING', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='THINNING_KEPT', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='THINNING_IGNORED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7428,
  serialized_end=7538,
)
_sym_db.RegisterEnumDescriptor(_THINNINGSTATE)

ThinningState = enum_type_wrapper.EnumTypeWrapper(_THINNINGSTATE)
_TARGETABLESTATE = _descriptor.EnumDescriptor(
  name='TargetableState',
  full_name='weed_tracking.TargetableState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TARGET_NOT_IN_SCHEDULER', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET_SCORED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET_INTERSECTS_NON_SHOOTABLE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET_TOO_MANY_FAILURES', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET_DOO_TOO_LOW', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET_IGNORED_FROM_ALMANAC', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET_OUT_OF_BAND', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET_AVOID_FROM_ALMANAC', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7541,
  serialized_end=7785,
)
_sym_db.RegisterEnumDescriptor(_TARGETABLESTATE)

TargetableState = enum_type_wrapper.EnumTypeWrapper(_TARGETABLESTATE)
_CLASSIFICATION = _descriptor.EnumDescriptor(
  name='Classification',
  full_name='weed_tracking.Classification',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CLASS_UNDECIDED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CLASS_WEED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CLASS_CROP', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CLASS_BOTH', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CLASS_UNKNOWN', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7787,
  serialized_end=7891,
)
_sym_db.RegisterEnumDescriptor(_CLASSIFICATION)

Classification = enum_type_wrapper.EnumTypeWrapper(_CLASSIFICATION)
_RECORDINGSTATUS = _descriptor.EnumDescriptor(
  name='RecordingStatus',
  full_name='weed_tracking.RecordingStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NOT_RECORDING', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RECORDING_STARTED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RECORDING_FINISHED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RECORDING_FAILED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7893,
  serialized_end=7998,
)
_sym_db.RegisterEnumDescriptor(_RECORDINGSTATUS)

RecordingStatus = enum_type_wrapper.EnumTypeWrapper(_RECORDINGSTATUS)
_CONCLUSIONTYPE = _descriptor.EnumDescriptor(
  name='ConclusionType',
  full_name='weed_tracking.ConclusionType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NOT_WEEDING', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='OUT_OF_BAND', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INTERSECTS_WITH_NON_SHOOTABLE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='OUT_OF_RANGE', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UNIMPORTANT', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NOT_SHOT', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PARTIALLY_SHOT', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SHOT', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='P2P_NOT_FOUND', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ERROR', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FLICKER', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='MARKED_FOR_THINNING', index=11, number=11,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NOT_TARGETED', index=12, number=12,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='P2P_MISSING_CONTEXT', index=13, number=13,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=8001,
  serialized_end=8276,
)
_sym_db.RegisterEnumDescriptor(_CONCLUSIONTYPE)

ConclusionType = enum_type_wrapper.EnumTypeWrapper(_CONCLUSIONTYPE)
_PLANTCAPTCHASTATUS = _descriptor.EnumDescriptor(
  name='PlantCaptchaStatus',
  full_name='weed_tracking.PlantCaptchaStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NOT_STARTED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAPTCHA_STARTED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAPTCHA_FINISHED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAPTCHA_FAILED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAPTCHA_CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=8278,
  serialized_end=8401,
)
_sym_db.RegisterEnumDescriptor(_PLANTCAPTCHASTATUS)

PlantCaptchaStatus = enum_type_wrapper.EnumTypeWrapper(_PLANTCAPTCHASTATUS)
_PLANTCAPTCHAUSERPREDICTION = _descriptor.EnumDescriptor(
  name='PlantCaptchaUserPrediction',
  full_name='weed_tracking.PlantCaptchaUserPrediction',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WEED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CROP', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='OTHER', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IGNORE', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='VOLUNTEER', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BENEFICIAL', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DEBRIS', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=8403,
  serialized_end=8530,
)
_sym_db.RegisterEnumDescriptor(_PLANTCAPTCHAUSERPREDICTION)

PlantCaptchaUserPrediction = enum_type_wrapper.EnumTypeWrapper(_PLANTCAPTCHAUSERPREDICTION)
NONE = 0
ANOTHER_LASER_SHOOTING = 1
SCANNER_POSITION_INVALID = 2
WEED_ALREADY_KILLED = 3
WEED_SHOT_BY_ANOTHER_LASER = 4
WEED_OUT_OF_BAND = 5
NOT_A_WEED = 6
SCORE_NEGATIVE = 7
INTERSECTED_WITH_NONSHOOTABLE = 8
EXTERMINATION_FAILURES_EXCEEDED_MAX = 9
DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN = 10
STATUS_NOT_SHOT = 0
STATUS_BEING_SHOT = 1
STATUS_SHOT = 2
STATUS_PARTIALLY_SHOT = 3
STATUS_P2P_NOT_FOUND = 4
STATUS_ERROR = 5
STATUS_P2P_MISSING_CONTEXT = 6
UNIQUE = 0
PRIMARY = 1
DUPLICATE = 2
THINNING_UNSET = 0
THINNING_MARKED_FOR_THINNING = 1
THINNING_KEPT = 2
THINNING_IGNORED = 3
TARGET_NOT_IN_SCHEDULER = 0
TARGET_SCORED = 1
TARGET_INTERSECTS_NON_SHOOTABLE = 2
TARGET_TOO_MANY_FAILURES = 3
TARGET_DOO_TOO_LOW = 4
TARGET_IGNORED_FROM_ALMANAC = 5
TARGET_OUT_OF_BAND = 6
TARGET_AVOID_FROM_ALMANAC = 7
CLASS_UNDECIDED = 0
CLASS_WEED = 1
CLASS_CROP = 2
CLASS_BOTH = 3
CLASS_UNKNOWN = 4
NOT_RECORDING = 0
RECORDING_STARTED = 1
RECORDING_FINISHED = 2
RECORDING_FAILED = 3
NOT_WEEDING = 0
OUT_OF_BAND = 1
INTERSECTS_WITH_NON_SHOOTABLE = 2
OUT_OF_RANGE = 3
UNIMPORTANT = 4
NOT_SHOT = 5
PARTIALLY_SHOT = 6
SHOT = 7
P2P_NOT_FOUND = 8
ERROR = 9
FLICKER = 10
MARKED_FOR_THINNING = 11
NOT_TARGETED = 12
P2P_MISSING_CONTEXT = 13
NOT_STARTED = 0
CAPTCHA_STARTED = 1
CAPTCHA_FINISHED = 2
CAPTCHA_FAILED = 3
CAPTCHA_CANCELLED = 4
WEED = 0
CROP = 1
UNKNOWN = 2
OTHER = 3
IGNORE = 4
VOLUNTEER = 5
BENEFICIAL = 6
DEBRIS = 7



_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='weed_tracking.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=58,
  serialized_end=65,
)


_TRAJECTORY = _descriptor.Descriptor(
  name='Trajectory',
  full_name='weed_tracking.Trajectory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='weed_tracking.Trajectory.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tracker_id', full_name='weed_tracking.Trajectory.tracker_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='weed_tracking.Trajectory.status', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_weed', full_name='weed_tracking.Trajectory.is_weed', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x_mm', full_name='weed_tracking.Trajectory.x_mm', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_mm', full_name='weed_tracking.Trajectory.y_mm', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z_mm', full_name='weed_tracking.Trajectory.z_mm', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intersected_with_nonshootable', full_name='weed_tracking.Trajectory.intersected_with_nonshootable', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nonshootable_type_string', full_name='weed_tracking.Trajectory.nonshootable_type_string', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deduplicated_across_tracker', full_name='weed_tracking.Trajectory.deduplicated_across_tracker', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=68,
  serialized_end=301,
)


_TARGET = _descriptor.Descriptor(
  name='Target',
  full_name='weed_tracking.Target',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='weed_tracking.Target.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='weed_tracking.Target.trajectory_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='next_trajectory_id', full_name='weed_tracking.Target.next_trajectory_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='starting_pos_y', full_name='weed_tracking.Target.starting_pos_y', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ending_pos_y', full_name='weed_tracking.Target.ending_pos_y', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=304,
  serialized_end=433,
)


_BOUNDS = _descriptor.Descriptor(
  name='Bounds',
  full_name='weed_tracking.Bounds',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tracker_id', full_name='weed_tracking.Bounds.tracker_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_pos_mm_y', full_name='weed_tracking.Bounds.max_pos_mm_y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=435,
  serialized_end=489,
)


_TRACKINGSTATUSREPLY = _descriptor.Descriptor(
  name='TrackingStatusReply',
  full_name='weed_tracking.TrackingStatusReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='targets', full_name='weed_tracking.TrackingStatusReply.targets', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectories', full_name='weed_tracking.TrackingStatusReply.trajectories', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bounds', full_name='weed_tracking.TrackingStatusReply.bounds', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=492,
  serialized_end=645,
)


_GETDETECTIONSREQUEST = _descriptor.Descriptor(
  name='GetDetectionsRequest',
  full_name='weed_tracking.GetDetectionsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='weed_tracking.GetDetectionsRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='weed_tracking.GetDetectionsRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_x', full_name='weed_tracking.GetDetectionsRequest.max_x', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=647,
  serialized_end=722,
)


_DETECTION = _descriptor.Descriptor(
  name='Detection',
  full_name='weed_tracking.Detection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='weed_tracking.Detection.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='weed_tracking.Detection.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size', full_name='weed_tracking.Detection.size', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='clz', full_name='weed_tracking.Detection.clz', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_weed', full_name='weed_tracking.Detection.is_weed', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='out_of_band', full_name='weed_tracking.Detection.out_of_band', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='weed_tracking.Detection.id', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='weed_tracking.Detection.score', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_score', full_name='weed_tracking.Detection.weed_score', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_score', full_name='weed_tracking.Detection.crop_score', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding', full_name='weed_tracking.Detection.embedding', index=10,
      number=11, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_score', full_name='weed_tracking.Detection.plant_score', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=725,
  serialized_end=930,
)


_DETECTIONS = _descriptor.Descriptor(
  name='Detections',
  full_name='weed_tracking.Detections',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='detections', full_name='weed_tracking.Detections.detections', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='weed_tracking.Detections.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=932,
  serialized_end=1012,
)


_BAND = _descriptor.Descriptor(
  name='Band',
  full_name='weed_tracking.Band',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_x_px', full_name='weed_tracking.Band.start_x_px', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_x_px', full_name='weed_tracking.Band.end_x_px', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1014,
  serialized_end=1058,
)


_BANDS = _descriptor.Descriptor(
  name='Bands',
  full_name='weed_tracking.Bands',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='band', full_name='weed_tracking.Bands.band', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding_enabled', full_name='weed_tracking.Bands.banding_enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_has_bands_defined', full_name='weed_tracking.Bands.row_has_bands_defined', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1060,
  serialized_end=1158,
)


_GETDETECTIONSRESPONSE = _descriptor.Descriptor(
  name='GetDetectionsResponse',
  full_name='weed_tracking.GetDetectionsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='detections', full_name='weed_tracking.GetDetectionsResponse.detections', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bands', full_name='weed_tracking.GetDetectionsResponse.bands', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1160,
  serialized_end=1267,
)


_GETTRAJECTORYMETADATAREQUEST = _descriptor.Descriptor(
  name='GetTrajectoryMetadataRequest',
  full_name='weed_tracking.GetTrajectoryMetadataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1269,
  serialized_end=1299,
)


_TRACKEDITEMMETADATA = _descriptor.Descriptor(
  name='TrackedItemMetadata',
  full_name='weed_tracking.TrackedItemMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='detection_id', full_name='weed_tracking.TrackedItemMetadata.detection_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='weed_tracking.TrackedItemMetadata.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1301,
  serialized_end=1366,
)


_TRAJECTORYMETADATA = _descriptor.Descriptor(
  name='TrajectoryMetadata',
  full_name='weed_tracking.TrajectoryMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='trajectory_id', full_name='weed_tracking.TrajectoryMetadata.trajectory_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='weed_tracking.TrajectoryMetadata.cam_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tracked_item_metadata', full_name='weed_tracking.TrajectoryMetadata.tracked_item_metadata', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='band_status', full_name='weed_tracking.TrajectoryMetadata.band_status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1369,
  serialized_end=1516,
)


_GETTRAJECTORYMETADATARESPONSE = _descriptor.Descriptor(
  name='GetTrajectoryMetadataResponse',
  full_name='weed_tracking.GetTrajectoryMetadataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='metadata', full_name='weed_tracking.GetTrajectoryMetadataResponse.metadata', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1518,
  serialized_end=1602,
)


_PINGREQUEST = _descriptor.Descriptor(
  name='PingRequest',
  full_name='weed_tracking.PingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='weed_tracking.PingRequest.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1604,
  serialized_end=1628,
)


_PONGREPLY = _descriptor.Descriptor(
  name='PongReply',
  full_name='weed_tracking.PongReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='weed_tracking.PongReply.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1630,
  serialized_end=1652,
)


_TRAJECTORYSCORE = _descriptor.Descriptor(
  name='TrajectoryScore',
  full_name='weed_tracking.TrajectoryScore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score', full_name='weed_tracking.TrajectoryScore.score', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_tilt', full_name='weed_tracking.TrajectoryScore.score_tilt', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_pan', full_name='weed_tracking.TrajectoryScore.score_pan', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_busy', full_name='weed_tracking.TrajectoryScore.score_busy', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_in_range', full_name='weed_tracking.TrajectoryScore.score_in_range', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_importance', full_name='weed_tracking.TrajectoryScore.score_importance', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_size', full_name='weed_tracking.TrajectoryScore.score_size', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1655,
  serialized_end=1816,
)


_PERSCANNERSCORE = _descriptor.Descriptor(
  name='PerScannerScore',
  full_name='weed_tracking.PerScannerScore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='weed_tracking.PerScannerScore.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='weed_tracking.PerScannerScore.score', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1818,
  serialized_end=1870,
)


_SCORESTATE = _descriptor.Descriptor(
  name='ScoreState',
  full_name='weed_tracking.ScoreState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_state', full_name='weed_tracking.ScoreState.target_state', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scores', full_name='weed_tracking.ScoreState.scores', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1872,
  serialized_end=1986,
)


_POS2D = _descriptor.Descriptor(
  name='Pos2D',
  full_name='weed_tracking.Pos2D',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='weed_tracking.Pos2D.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='weed_tracking.Pos2D.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1988,
  serialized_end=2017,
)


_KILLBOX = _descriptor.Descriptor(
  name='KillBox',
  full_name='weed_tracking.KillBox',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='weed_tracking.KillBox.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='top_left', full_name='weed_tracking.KillBox.top_left', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bottom_right', full_name='weed_tracking.KillBox.bottom_right', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2019,
  serialized_end=2132,
)


_THRESHOLDS = _descriptor.Descriptor(
  name='Thresholds',
  full_name='weed_tracking.Thresholds',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weeding', full_name='weed_tracking.Thresholds.weeding', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning', full_name='weed_tracking.Thresholds.thinning', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding', full_name='weed_tracking.Thresholds.banding', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='passed_weeding', full_name='weed_tracking.Thresholds.passed_weeding', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='passed_thinning', full_name='weed_tracking.Thresholds.passed_thinning', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='passed_banding', full_name='weed_tracking.Thresholds.passed_banding', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2135,
  serialized_end=2284,
)


_DECISIONS = _descriptor.Descriptor(
  name='Decisions',
  full_name='weed_tracking.Decisions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weeding_weed', full_name='weed_tracking.Decisions.weeding_weed', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeding_crop', full_name='weed_tracking.Decisions.weeding_crop', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_weed', full_name='weed_tracking.Decisions.thinning_weed', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_crop', full_name='weed_tracking.Decisions.thinning_crop', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='keepable_crop', full_name='weed_tracking.Decisions.keepable_crop', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding_crop', full_name='weed_tracking.Decisions.banding_crop', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2287,
  serialized_end=2433,
)


_TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY = _descriptor.Descriptor(
  name='DetectionClassesEntry',
  full_name='weed_tracking.TrajectorySnapshot.DetectionClassesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='weed_tracking.TrajectorySnapshot.DetectionClassesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='weed_tracking.TrajectorySnapshot.DetectionClassesEntry.value', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3728,
  serialized_end=3783,
)

_TRAJECTORYSNAPSHOT = _descriptor.Descriptor(
  name='TrajectorySnapshot',
  full_name='weed_tracking.TrajectorySnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='weed_tracking.TrajectorySnapshot.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='kill_status', full_name='weed_tracking.TrajectorySnapshot.kill_status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_weed', full_name='weed_tracking.TrajectorySnapshot.is_weed', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x_mm', full_name='weed_tracking.TrajectorySnapshot.x_mm', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_mm', full_name='weed_tracking.TrajectorySnapshot.y_mm', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z_mm', full_name='weed_tracking.TrajectorySnapshot.z_mm', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='weed_tracking.TrajectorySnapshot.score', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='invalid_score', full_name='weed_tracking.TrajectorySnapshot.invalid_score', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='radius_mm', full_name='weed_tracking.TrajectorySnapshot.radius_mm', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='category', full_name='weed_tracking.TrajectorySnapshot.category', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='shoot_time_requested_ms', full_name='weed_tracking.TrajectorySnapshot.shoot_time_requested_ms', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='shoot_time_actual_ms', full_name='weed_tracking.TrajectorySnapshot.shoot_time_actual_ms', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='marked_for_thinning', full_name='weed_tracking.TrajectorySnapshot.marked_for_thinning', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tracker_id', full_name='weed_tracking.TrajectorySnapshot.tracker_id', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duplicate_status', full_name='weed_tracking.TrajectorySnapshot.duplicate_status', index=14,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duplicate_trajectory_id', full_name='weed_tracking.TrajectorySnapshot.duplicate_trajectory_id', index=15,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='assigned_lasers', full_name='weed_tracking.TrajectorySnapshot.assigned_lasers', index=16,
      number=17, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='out_of_band', full_name='weed_tracking.TrajectorySnapshot.out_of_band', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intersected_with_nonshootable', full_name='weed_tracking.TrajectorySnapshot.intersected_with_nonshootable', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='detection_classes', full_name='weed_tracking.TrajectorySnapshot.detection_classes', index=19,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='confidence', full_name='weed_tracking.TrajectorySnapshot.confidence', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_state', full_name='weed_tracking.TrajectorySnapshot.thinning_state', index=21,
      number=22, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='global_pos', full_name='weed_tracking.TrajectorySnapshot.global_pos', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_state', full_name='weed_tracking.TrajectorySnapshot.score_state', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='doo', full_name='weed_tracking.TrajectorySnapshot.doo', index=24,
      number=25, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='distance_perspectives_count', full_name='weed_tracking.TrajectorySnapshot.distance_perspectives_count', index=25,
      number=26, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size_category_index', full_name='weed_tracking.TrajectorySnapshot.size_category_index', index=26,
      number=27, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speculative_allowed', full_name='weed_tracking.TrajectorySnapshot.speculative_allowed', index=27,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='protected_by_traj', full_name='weed_tracking.TrajectorySnapshot.protected_by_traj', index=28,
      number=29, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thresholds', full_name='weed_tracking.TrajectorySnapshot.thresholds', index=29,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='decisions', full_name='weed_tracking.TrajectorySnapshot.decisions', index=30,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_score', full_name='weed_tracking.TrajectorySnapshot.crop_score', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_score', full_name='weed_tracking.TrajectorySnapshot.weed_score', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_score', full_name='weed_tracking.TrajectorySnapshot.plant_score', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_detections_used_for_decision', full_name='weed_tracking.TrajectorySnapshot.num_detections_used_for_decision', index=34,
      number=35, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='classification', full_name='weed_tracking.TrajectorySnapshot.classification', index=35,
      number=36, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speculative_shoot_time_actual_ms', full_name='weed_tracking.TrajectorySnapshot.speculative_shoot_time_actual_ms', index=36,
      number=38, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='snapshot_metadata', full_name='weed_tracking.TrajectorySnapshot.snapshot_metadata', index=37,
      number=39, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='score_details', full_name='weed_tracking.TrajectorySnapshot.score_details',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2436,
  serialized_end=3806,
)


_SNAPSHOTMETADATA = _descriptor.Descriptor(
  name='SnapshotMetadata',
  full_name='weed_tracking.SnapshotMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pcam_id', full_name='weed_tracking.SnapshotMetadata.pcam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='weed_tracking.SnapshotMetadata.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_x_px', full_name='weed_tracking.SnapshotMetadata.center_x_px', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_y_px', full_name='weed_tracking.SnapshotMetadata.center_y_px', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3808,
  serialized_end=3907,
)


_BANDDEFINITION = _descriptor.Descriptor(
  name='BandDefinition',
  full_name='weed_tracking.BandDefinition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='offset_mm', full_name='weed_tracking.BandDefinition.offset_mm', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width_mm', full_name='weed_tracking.BandDefinition.width_mm', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='weed_tracking.BandDefinition.id', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3909,
  serialized_end=3974,
)


_CLDALGORITHMSNAPSHOT = _descriptor.Descriptor(
  name='CLDAlgorithmSnapshot',
  full_name='weed_tracking.CLDAlgorithmSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='weed_tracking.CLDAlgorithmSnapshot.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='graph_points_x', full_name='weed_tracking.CLDAlgorithmSnapshot.graph_points_x', index=1,
      number=2, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='graph_points_y', full_name='weed_tracking.CLDAlgorithmSnapshot.graph_points_y', index=2,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='minimas_x', full_name='weed_tracking.CLDAlgorithmSnapshot.minimas_x', index=3,
      number=4, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='minimas_y', full_name='weed_tracking.CLDAlgorithmSnapshot.minimas_y', index=4,
      number=5, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lines', full_name='weed_tracking.CLDAlgorithmSnapshot.lines', index=5,
      number=6, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3977,
  serialized_end=4122,
)


_DIAGNOSTICSSNAPSHOT = _descriptor.Descriptor(
  name='DiagnosticsSnapshot',
  full_name='weed_tracking.DiagnosticsSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='weed_tracking.DiagnosticsSnapshot.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trajectories', full_name='weed_tracking.DiagnosticsSnapshot.trajectories', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bands', full_name='weed_tracking.DiagnosticsSnapshot.bands', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='kill_boxes', full_name='weed_tracking.DiagnosticsSnapshot.kill_boxes', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding_algorithm_snapshot', full_name='weed_tracking.DiagnosticsSnapshot.banding_algorithm_snapshot', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_banding_algorithm_snapshot', full_name='weed_tracking.DiagnosticsSnapshot._banding_algorithm_snapshot',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=4125,
  serialized_end=4424,
)


_RECORDDIAGNOSTICSREQUEST = _descriptor.Descriptor(
  name='RecordDiagnosticsRequest',
  full_name='weed_tracking.RecordDiagnosticsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ttl_sec', full_name='weed_tracking.RecordDiagnosticsRequest.ttl_sec', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_images_per_sec', full_name='weed_tracking.RecordDiagnosticsRequest.crop_images_per_sec', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_images_per_sec', full_name='weed_tracking.RecordDiagnosticsRequest.weed_images_per_sec', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4426,
  serialized_end=4527,
)


_GETRECORDINGSTATUSRESPONSE = _descriptor.Descriptor(
  name='GetRecordingStatusResponse',
  full_name='weed_tracking.GetRecordingStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='weed_tracking.GetRecordingStatusResponse.status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4529,
  serialized_end=4605,
)


_STARTSAVINGCROPLINEDETECTIONREPLAYREQUEST = _descriptor.Descriptor(
  name='StartSavingCropLineDetectionReplayRequest',
  full_name='weed_tracking.StartSavingCropLineDetectionReplayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='filename', full_name='weed_tracking.StartSavingCropLineDetectionReplayRequest.filename', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ttl_ms', full_name='weed_tracking.StartSavingCropLineDetectionReplayRequest.ttl_ms', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4607,
  serialized_end=4684,
)


_RECORDAIMBOTINPUTREQUEST = _descriptor.Descriptor(
  name='RecordAimbotInputRequest',
  full_name='weed_tracking.RecordAimbotInputRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='weed_tracking.RecordAimbotInputRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ttl_ms', full_name='weed_tracking.RecordAimbotInputRequest.ttl_ms', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rotary_ticks', full_name='weed_tracking.RecordAimbotInputRequest.rotary_ticks', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed', full_name='weed_tracking.RecordAimbotInputRequest.deepweed', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lane_heights', full_name='weed_tracking.RecordAimbotInputRequest.lane_heights', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_rotary_ticks', full_name='weed_tracking.RecordAimbotInputRequest._rotary_ticks',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_deepweed', full_name='weed_tracking.RecordAimbotInputRequest._deepweed',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_lane_heights', full_name='weed_tracking.RecordAimbotInputRequest._lane_heights',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=4687,
  serialized_end=4867,
)


_CONCLUSIONCOUNT = _descriptor.Descriptor(
  name='ConclusionCount',
  full_name='weed_tracking.ConclusionCount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='weed_tracking.ConclusionCount.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='count', full_name='weed_tracking.ConclusionCount.count', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4869,
  serialized_end=4946,
)


_CONCLUSIONCOUNTER = _descriptor.Descriptor(
  name='ConclusionCounter',
  full_name='weed_tracking.ConclusionCounter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='counts', full_name='weed_tracking.ConclusionCounter.counts', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4948,
  serialized_end=5015,
)


_BANDDEFINITIONS = _descriptor.Descriptor(
  name='BandDefinitions',
  full_name='weed_tracking.BandDefinitions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bands', full_name='weed_tracking.BandDefinitions.bands', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding_enabled', full_name='weed_tracking.BandDefinitions.banding_enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_has_bands_defined', full_name='weed_tracking.BandDefinitions.row_has_bands_defined', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5017,
  serialized_end=5136,
)


_PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY = _descriptor.Descriptor(
  name='ExemplarCountsEntry',
  full_name='weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry.value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5376,
  serialized_end=5429,
)

_PLANTCAPTCHASTATUSRESPONSE = _descriptor.Descriptor(
  name='PlantCaptchaStatusResponse',
  full_name='weed_tracking.PlantCaptchaStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='weed_tracking.PlantCaptchaStatusResponse.status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='total_images', full_name='weed_tracking.PlantCaptchaStatusResponse.total_images', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='images_taken', full_name='weed_tracking.PlantCaptchaStatusResponse.images_taken', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metadata_taken', full_name='weed_tracking.PlantCaptchaStatusResponse.metadata_taken', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exemplar_counts', full_name='weed_tracking.PlantCaptchaStatusResponse.exemplar_counts', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5139,
  serialized_end=5429,
)


_EMBEDDING = _descriptor.Descriptor(
  name='Embedding',
  full_name='weed_tracking.Embedding',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='elements', full_name='weed_tracking.Embedding.elements', index=0,
      number=1, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5431,
  serialized_end=5460,
)


_WEEDCLASSES_CLASSESENTRY = _descriptor.Descriptor(
  name='ClassesEntry',
  full_name='weed_tracking.WeedClasses.ClassesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='weed_tracking.WeedClasses.ClassesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='weed_tracking.WeedClasses.ClassesEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5535,
  serialized_end=5581,
)

_WEEDCLASSES = _descriptor.Descriptor(
  name='WeedClasses',
  full_name='weed_tracking.WeedClasses',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='classes', full_name='weed_tracking.WeedClasses.classes', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_WEEDCLASSES_CLASSESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5462,
  serialized_end=5581,
)


_PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY = _descriptor.Descriptor(
  name='CategoriesEntry',
  full_name='weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6563,
  serialized_end=6612,
)

_PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY = _descriptor.Descriptor(
  name='WeedCategoriesEntry',
  full_name='weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6614,
  serialized_end=6667,
)

_PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY = _descriptor.Descriptor(
  name='EmbeddingDistancesEntry',
  full_name='weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6669,
  serialized_end=6726,
)

_PLANTCAPTCHAITEMMETADATA = _descriptor.Descriptor(
  name='PlantCaptchaItemMetadata',
  full_name='weed_tracking.PlantCaptchaItemMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='confidence', full_name='weed_tracking.PlantCaptchaItemMetadata.confidence', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x_px', full_name='weed_tracking.PlantCaptchaItemMetadata.x_px', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_px', full_name='weed_tracking.PlantCaptchaItemMetadata.y_px', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x_mm', full_name='weed_tracking.PlantCaptchaItemMetadata.x_mm', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_mm', full_name='weed_tracking.PlantCaptchaItemMetadata.y_mm', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z_mm', full_name='weed_tracking.PlantCaptchaItemMetadata.z_mm', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size_mm', full_name='weed_tracking.PlantCaptchaItemMetadata.size_mm', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='categories', full_name='weed_tracking.PlantCaptchaItemMetadata.categories', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='doo', full_name='weed_tracking.PlantCaptchaItemMetadata.doo', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_weed', full_name='weed_tracking.PlantCaptchaItemMetadata.is_weed', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intersected_with_nonshootable', full_name='weed_tracking.PlantCaptchaItemMetadata.intersected_with_nonshootable', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='confidence_history', full_name='weed_tracking.PlantCaptchaItemMetadata.confidence_history', index=11,
      number=12, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_in_band', full_name='weed_tracking.PlantCaptchaItemMetadata.is_in_band', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='weed_tracking.PlantCaptchaItemMetadata.id', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size_px', full_name='weed_tracking.PlantCaptchaItemMetadata.size_px', index=14,
      number=15, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='shoot_time_ms', full_name='weed_tracking.PlantCaptchaItemMetadata.shoot_time_ms', index=15,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_confidence_history', full_name='weed_tracking.PlantCaptchaItemMetadata.weed_confidence_history', index=16,
      number=17, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_confidence_history', full_name='weed_tracking.PlantCaptchaItemMetadata.crop_confidence_history', index=17,
      number=18, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size_category_index', full_name='weed_tracking.PlantCaptchaItemMetadata.size_category_index', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_categories', full_name='weed_tracking.PlantCaptchaItemMetadata.weed_categories', index=19,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding_history', full_name='weed_tracking.PlantCaptchaItemMetadata.embedding_history', index=20,
      number=21, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='initial_label', full_name='weed_tracking.PlantCaptchaItemMetadata.initial_label', index=21,
      number=22, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_confidence_history', full_name='weed_tracking.PlantCaptchaItemMetadata.plant_confidence_history', index=22,
      number=23, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_classes_history', full_name='weed_tracking.PlantCaptchaItemMetadata.weed_classes_history', index=23,
      number=24, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size_mm_history', full_name='weed_tracking.PlantCaptchaItemMetadata.size_mm_history', index=24,
      number=25, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='decisions', full_name='weed_tracking.PlantCaptchaItemMetadata.decisions', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_detections_used_for_decision', full_name='weed_tracking.PlantCaptchaItemMetadata.num_detections_used_for_decision', index=26,
      number=27, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding_distances', full_name='weed_tracking.PlantCaptchaItemMetadata.embedding_distances', index=27,
      number=28, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY, _PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY, _PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5584,
  serialized_end=6726,
)


_GETTARGETINGENABLEDREQUEST = _descriptor.Descriptor(
  name='GetTargetingEnabledRequest',
  full_name='weed_tracking.GetTargetingEnabledRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6728,
  serialized_end=6756,
)


_GETTARGETINGENABLEDRESPONSE = _descriptor.Descriptor(
  name='GetTargetingEnabledResponse',
  full_name='weed_tracking.GetTargetingEnabledResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='weed_tracking.GetTargetingEnabledResponse.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6758,
  serialized_end=6804,
)


_GETBOOTEDREQUEST = _descriptor.Descriptor(
  name='GetBootedRequest',
  full_name='weed_tracking.GetBootedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6806,
  serialized_end=6824,
)


_GETBOOTEDRESPONSE = _descriptor.Descriptor(
  name='GetBootedResponse',
  full_name='weed_tracking.GetBootedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booted', full_name='weed_tracking.GetBootedResponse.booted', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6826,
  serialized_end=6861,
)

_TRACKINGSTATUSREPLY.fields_by_name['targets'].message_type = _TARGET
_TRACKINGSTATUSREPLY.fields_by_name['trajectories'].message_type = _TRAJECTORY
_TRACKINGSTATUSREPLY.fields_by_name['bounds'].message_type = _BOUNDS
_DETECTIONS.fields_by_name['detections'].message_type = _DETECTION
_BANDS.fields_by_name['band'].message_type = _BAND
_GETDETECTIONSRESPONSE.fields_by_name['detections'].message_type = _DETECTIONS
_GETDETECTIONSRESPONSE.fields_by_name['bands'].message_type = _BANDS
_TRAJECTORYMETADATA.fields_by_name['tracked_item_metadata'].message_type = _TRACKEDITEMMETADATA
_GETTRAJECTORYMETADATARESPONSE.fields_by_name['metadata'].message_type = _TRAJECTORYMETADATA
_SCORESTATE.fields_by_name['target_state'].enum_type = _TARGETABLESTATE
_SCORESTATE.fields_by_name['scores'].message_type = _PERSCANNERSCORE
_KILLBOX.fields_by_name['top_left'].message_type = _POS2D
_KILLBOX.fields_by_name['bottom_right'].message_type = _POS2D
_TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY.containing_type = _TRAJECTORYSNAPSHOT
_TRAJECTORYSNAPSHOT.fields_by_name['kill_status'].enum_type = _KILLSTATUS
_TRAJECTORYSNAPSHOT.fields_by_name['score'].message_type = _TRAJECTORYSCORE
_TRAJECTORYSNAPSHOT.fields_by_name['invalid_score'].enum_type = _INVALIDSCOREREASON
_TRAJECTORYSNAPSHOT.fields_by_name['duplicate_status'].enum_type = _DUPLICATESTATUS
_TRAJECTORYSNAPSHOT.fields_by_name['detection_classes'].message_type = _TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY
_TRAJECTORYSNAPSHOT.fields_by_name['thinning_state'].enum_type = _THINNINGSTATE
_TRAJECTORYSNAPSHOT.fields_by_name['score_state'].message_type = _SCORESTATE
_TRAJECTORYSNAPSHOT.fields_by_name['thresholds'].message_type = _THRESHOLDS
_TRAJECTORYSNAPSHOT.fields_by_name['decisions'].message_type = _DECISIONS
_TRAJECTORYSNAPSHOT.fields_by_name['classification'].enum_type = _CLASSIFICATION
_TRAJECTORYSNAPSHOT.fields_by_name['snapshot_metadata'].message_type = _SNAPSHOTMETADATA
_TRAJECTORYSNAPSHOT.oneofs_by_name['score_details'].fields.append(
  _TRAJECTORYSNAPSHOT.fields_by_name['score'])
_TRAJECTORYSNAPSHOT.fields_by_name['score'].containing_oneof = _TRAJECTORYSNAPSHOT.oneofs_by_name['score_details']
_TRAJECTORYSNAPSHOT.oneofs_by_name['score_details'].fields.append(
  _TRAJECTORYSNAPSHOT.fields_by_name['invalid_score'])
_TRAJECTORYSNAPSHOT.fields_by_name['invalid_score'].containing_oneof = _TRAJECTORYSNAPSHOT.oneofs_by_name['score_details']
_DIAGNOSTICSSNAPSHOT.fields_by_name['trajectories'].message_type = _TRAJECTORYSNAPSHOT
_DIAGNOSTICSSNAPSHOT.fields_by_name['bands'].message_type = _BANDDEFINITION
_DIAGNOSTICSSNAPSHOT.fields_by_name['kill_boxes'].message_type = _KILLBOX
_DIAGNOSTICSSNAPSHOT.fields_by_name['banding_algorithm_snapshot'].message_type = _CLDALGORITHMSNAPSHOT
_DIAGNOSTICSSNAPSHOT.oneofs_by_name['_banding_algorithm_snapshot'].fields.append(
  _DIAGNOSTICSSNAPSHOT.fields_by_name['banding_algorithm_snapshot'])
_DIAGNOSTICSSNAPSHOT.fields_by_name['banding_algorithm_snapshot'].containing_oneof = _DIAGNOSTICSSNAPSHOT.oneofs_by_name['_banding_algorithm_snapshot']
_GETRECORDINGSTATUSRESPONSE.fields_by_name['status'].enum_type = _RECORDINGSTATUS
_RECORDAIMBOTINPUTREQUEST.oneofs_by_name['_rotary_ticks'].fields.append(
  _RECORDAIMBOTINPUTREQUEST.fields_by_name['rotary_ticks'])
_RECORDAIMBOTINPUTREQUEST.fields_by_name['rotary_ticks'].containing_oneof = _RECORDAIMBOTINPUTREQUEST.oneofs_by_name['_rotary_ticks']
_RECORDAIMBOTINPUTREQUEST.oneofs_by_name['_deepweed'].fields.append(
  _RECORDAIMBOTINPUTREQUEST.fields_by_name['deepweed'])
_RECORDAIMBOTINPUTREQUEST.fields_by_name['deepweed'].containing_oneof = _RECORDAIMBOTINPUTREQUEST.oneofs_by_name['_deepweed']
_RECORDAIMBOTINPUTREQUEST.oneofs_by_name['_lane_heights'].fields.append(
  _RECORDAIMBOTINPUTREQUEST.fields_by_name['lane_heights'])
_RECORDAIMBOTINPUTREQUEST.fields_by_name['lane_heights'].containing_oneof = _RECORDAIMBOTINPUTREQUEST.oneofs_by_name['_lane_heights']
_CONCLUSIONCOUNT.fields_by_name['type'].enum_type = _CONCLUSIONTYPE
_CONCLUSIONCOUNTER.fields_by_name['counts'].message_type = _CONCLUSIONCOUNT
_BANDDEFINITIONS.fields_by_name['bands'].message_type = _BANDDEFINITION
_PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY.containing_type = _PLANTCAPTCHASTATUSRESPONSE
_PLANTCAPTCHASTATUSRESPONSE.fields_by_name['status'].enum_type = _PLANTCAPTCHASTATUS
_PLANTCAPTCHASTATUSRESPONSE.fields_by_name['exemplar_counts'].message_type = _PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY
_WEEDCLASSES_CLASSESENTRY.containing_type = _WEEDCLASSES
_WEEDCLASSES.fields_by_name['classes'].message_type = _WEEDCLASSES_CLASSESENTRY
_PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY.containing_type = _PLANTCAPTCHAITEMMETADATA
_PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY.containing_type = _PLANTCAPTCHAITEMMETADATA
_PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY.containing_type = _PLANTCAPTCHAITEMMETADATA
_PLANTCAPTCHAITEMMETADATA.fields_by_name['categories'].message_type = _PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY
_PLANTCAPTCHAITEMMETADATA.fields_by_name['weed_categories'].message_type = _PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY
_PLANTCAPTCHAITEMMETADATA.fields_by_name['embedding_history'].message_type = _EMBEDDING
_PLANTCAPTCHAITEMMETADATA.fields_by_name['initial_label'].enum_type = _PLANTCAPTCHAUSERPREDICTION
_PLANTCAPTCHAITEMMETADATA.fields_by_name['weed_classes_history'].message_type = _WEEDCLASSES
_PLANTCAPTCHAITEMMETADATA.fields_by_name['decisions'].message_type = _DECISIONS
_PLANTCAPTCHAITEMMETADATA.fields_by_name['embedding_distances'].message_type = _PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['Trajectory'] = _TRAJECTORY
DESCRIPTOR.message_types_by_name['Target'] = _TARGET
DESCRIPTOR.message_types_by_name['Bounds'] = _BOUNDS
DESCRIPTOR.message_types_by_name['TrackingStatusReply'] = _TRACKINGSTATUSREPLY
DESCRIPTOR.message_types_by_name['GetDetectionsRequest'] = _GETDETECTIONSREQUEST
DESCRIPTOR.message_types_by_name['Detection'] = _DETECTION
DESCRIPTOR.message_types_by_name['Detections'] = _DETECTIONS
DESCRIPTOR.message_types_by_name['Band'] = _BAND
DESCRIPTOR.message_types_by_name['Bands'] = _BANDS
DESCRIPTOR.message_types_by_name['GetDetectionsResponse'] = _GETDETECTIONSRESPONSE
DESCRIPTOR.message_types_by_name['GetTrajectoryMetadataRequest'] = _GETTRAJECTORYMETADATAREQUEST
DESCRIPTOR.message_types_by_name['TrackedItemMetadata'] = _TRACKEDITEMMETADATA
DESCRIPTOR.message_types_by_name['TrajectoryMetadata'] = _TRAJECTORYMETADATA
DESCRIPTOR.message_types_by_name['GetTrajectoryMetadataResponse'] = _GETTRAJECTORYMETADATARESPONSE
DESCRIPTOR.message_types_by_name['PingRequest'] = _PINGREQUEST
DESCRIPTOR.message_types_by_name['PongReply'] = _PONGREPLY
DESCRIPTOR.message_types_by_name['TrajectoryScore'] = _TRAJECTORYSCORE
DESCRIPTOR.message_types_by_name['PerScannerScore'] = _PERSCANNERSCORE
DESCRIPTOR.message_types_by_name['ScoreState'] = _SCORESTATE
DESCRIPTOR.message_types_by_name['Pos2D'] = _POS2D
DESCRIPTOR.message_types_by_name['KillBox'] = _KILLBOX
DESCRIPTOR.message_types_by_name['Thresholds'] = _THRESHOLDS
DESCRIPTOR.message_types_by_name['Decisions'] = _DECISIONS
DESCRIPTOR.message_types_by_name['TrajectorySnapshot'] = _TRAJECTORYSNAPSHOT
DESCRIPTOR.message_types_by_name['SnapshotMetadata'] = _SNAPSHOTMETADATA
DESCRIPTOR.message_types_by_name['BandDefinition'] = _BANDDEFINITION
DESCRIPTOR.message_types_by_name['CLDAlgorithmSnapshot'] = _CLDALGORITHMSNAPSHOT
DESCRIPTOR.message_types_by_name['DiagnosticsSnapshot'] = _DIAGNOSTICSSNAPSHOT
DESCRIPTOR.message_types_by_name['RecordDiagnosticsRequest'] = _RECORDDIAGNOSTICSREQUEST
DESCRIPTOR.message_types_by_name['GetRecordingStatusResponse'] = _GETRECORDINGSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['StartSavingCropLineDetectionReplayRequest'] = _STARTSAVINGCROPLINEDETECTIONREPLAYREQUEST
DESCRIPTOR.message_types_by_name['RecordAimbotInputRequest'] = _RECORDAIMBOTINPUTREQUEST
DESCRIPTOR.message_types_by_name['ConclusionCount'] = _CONCLUSIONCOUNT
DESCRIPTOR.message_types_by_name['ConclusionCounter'] = _CONCLUSIONCOUNTER
DESCRIPTOR.message_types_by_name['BandDefinitions'] = _BANDDEFINITIONS
DESCRIPTOR.message_types_by_name['PlantCaptchaStatusResponse'] = _PLANTCAPTCHASTATUSRESPONSE
DESCRIPTOR.message_types_by_name['Embedding'] = _EMBEDDING
DESCRIPTOR.message_types_by_name['WeedClasses'] = _WEEDCLASSES
DESCRIPTOR.message_types_by_name['PlantCaptchaItemMetadata'] = _PLANTCAPTCHAITEMMETADATA
DESCRIPTOR.message_types_by_name['GetTargetingEnabledRequest'] = _GETTARGETINGENABLEDREQUEST
DESCRIPTOR.message_types_by_name['GetTargetingEnabledResponse'] = _GETTARGETINGENABLEDRESPONSE
DESCRIPTOR.message_types_by_name['GetBootedRequest'] = _GETBOOTEDREQUEST
DESCRIPTOR.message_types_by_name['GetBootedResponse'] = _GETBOOTEDRESPONSE
DESCRIPTOR.enum_types_by_name['InvalidScoreReason'] = _INVALIDSCOREREASON
DESCRIPTOR.enum_types_by_name['KillStatus'] = _KILLSTATUS
DESCRIPTOR.enum_types_by_name['DuplicateStatus'] = _DUPLICATESTATUS
DESCRIPTOR.enum_types_by_name['ThinningState'] = _THINNINGSTATE
DESCRIPTOR.enum_types_by_name['TargetableState'] = _TARGETABLESTATE
DESCRIPTOR.enum_types_by_name['Classification'] = _CLASSIFICATION
DESCRIPTOR.enum_types_by_name['RecordingStatus'] = _RECORDINGSTATUS
DESCRIPTOR.enum_types_by_name['ConclusionType'] = _CONCLUSIONTYPE
DESCRIPTOR.enum_types_by_name['PlantCaptchaStatus'] = _PLANTCAPTCHASTATUS
DESCRIPTOR.enum_types_by_name['PlantCaptchaUserPrediction'] = _PLANTCAPTCHAUSERPREDICTION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Empty)
  })
_sym_db.RegisterMessage(Empty)

Trajectory = _reflection.GeneratedProtocolMessageType('Trajectory', (_message.Message,), {
  'DESCRIPTOR' : _TRAJECTORY,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Trajectory)
  })
_sym_db.RegisterMessage(Trajectory)

Target = _reflection.GeneratedProtocolMessageType('Target', (_message.Message,), {
  'DESCRIPTOR' : _TARGET,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Target)
  })
_sym_db.RegisterMessage(Target)

Bounds = _reflection.GeneratedProtocolMessageType('Bounds', (_message.Message,), {
  'DESCRIPTOR' : _BOUNDS,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Bounds)
  })
_sym_db.RegisterMessage(Bounds)

TrackingStatusReply = _reflection.GeneratedProtocolMessageType('TrackingStatusReply', (_message.Message,), {
  'DESCRIPTOR' : _TRACKINGSTATUSREPLY,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.TrackingStatusReply)
  })
_sym_db.RegisterMessage(TrackingStatusReply)

GetDetectionsRequest = _reflection.GeneratedProtocolMessageType('GetDetectionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDETECTIONSREQUEST,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetDetectionsRequest)
  })
_sym_db.RegisterMessage(GetDetectionsRequest)

Detection = _reflection.GeneratedProtocolMessageType('Detection', (_message.Message,), {
  'DESCRIPTOR' : _DETECTION,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Detection)
  })
_sym_db.RegisterMessage(Detection)

Detections = _reflection.GeneratedProtocolMessageType('Detections', (_message.Message,), {
  'DESCRIPTOR' : _DETECTIONS,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Detections)
  })
_sym_db.RegisterMessage(Detections)

Band = _reflection.GeneratedProtocolMessageType('Band', (_message.Message,), {
  'DESCRIPTOR' : _BAND,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Band)
  })
_sym_db.RegisterMessage(Band)

Bands = _reflection.GeneratedProtocolMessageType('Bands', (_message.Message,), {
  'DESCRIPTOR' : _BANDS,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Bands)
  })
_sym_db.RegisterMessage(Bands)

GetDetectionsResponse = _reflection.GeneratedProtocolMessageType('GetDetectionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETDETECTIONSRESPONSE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetDetectionsResponse)
  })
_sym_db.RegisterMessage(GetDetectionsResponse)

GetTrajectoryMetadataRequest = _reflection.GeneratedProtocolMessageType('GetTrajectoryMetadataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTRAJECTORYMETADATAREQUEST,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetTrajectoryMetadataRequest)
  })
_sym_db.RegisterMessage(GetTrajectoryMetadataRequest)

TrackedItemMetadata = _reflection.GeneratedProtocolMessageType('TrackedItemMetadata', (_message.Message,), {
  'DESCRIPTOR' : _TRACKEDITEMMETADATA,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.TrackedItemMetadata)
  })
_sym_db.RegisterMessage(TrackedItemMetadata)

TrajectoryMetadata = _reflection.GeneratedProtocolMessageType('TrajectoryMetadata', (_message.Message,), {
  'DESCRIPTOR' : _TRAJECTORYMETADATA,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.TrajectoryMetadata)
  })
_sym_db.RegisterMessage(TrajectoryMetadata)

GetTrajectoryMetadataResponse = _reflection.GeneratedProtocolMessageType('GetTrajectoryMetadataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETTRAJECTORYMETADATARESPONSE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetTrajectoryMetadataResponse)
  })
_sym_db.RegisterMessage(GetTrajectoryMetadataResponse)

PingRequest = _reflection.GeneratedProtocolMessageType('PingRequest', (_message.Message,), {
  'DESCRIPTOR' : _PINGREQUEST,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.PingRequest)
  })
_sym_db.RegisterMessage(PingRequest)

PongReply = _reflection.GeneratedProtocolMessageType('PongReply', (_message.Message,), {
  'DESCRIPTOR' : _PONGREPLY,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.PongReply)
  })
_sym_db.RegisterMessage(PongReply)

TrajectoryScore = _reflection.GeneratedProtocolMessageType('TrajectoryScore', (_message.Message,), {
  'DESCRIPTOR' : _TRAJECTORYSCORE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.TrajectoryScore)
  })
_sym_db.RegisterMessage(TrajectoryScore)

PerScannerScore = _reflection.GeneratedProtocolMessageType('PerScannerScore', (_message.Message,), {
  'DESCRIPTOR' : _PERSCANNERSCORE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.PerScannerScore)
  })
_sym_db.RegisterMessage(PerScannerScore)

ScoreState = _reflection.GeneratedProtocolMessageType('ScoreState', (_message.Message,), {
  'DESCRIPTOR' : _SCORESTATE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.ScoreState)
  })
_sym_db.RegisterMessage(ScoreState)

Pos2D = _reflection.GeneratedProtocolMessageType('Pos2D', (_message.Message,), {
  'DESCRIPTOR' : _POS2D,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Pos2D)
  })
_sym_db.RegisterMessage(Pos2D)

KillBox = _reflection.GeneratedProtocolMessageType('KillBox', (_message.Message,), {
  'DESCRIPTOR' : _KILLBOX,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.KillBox)
  })
_sym_db.RegisterMessage(KillBox)

Thresholds = _reflection.GeneratedProtocolMessageType('Thresholds', (_message.Message,), {
  'DESCRIPTOR' : _THRESHOLDS,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Thresholds)
  })
_sym_db.RegisterMessage(Thresholds)

Decisions = _reflection.GeneratedProtocolMessageType('Decisions', (_message.Message,), {
  'DESCRIPTOR' : _DECISIONS,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Decisions)
  })
_sym_db.RegisterMessage(Decisions)

TrajectorySnapshot = _reflection.GeneratedProtocolMessageType('TrajectorySnapshot', (_message.Message,), {

  'DetectionClassesEntry' : _reflection.GeneratedProtocolMessageType('DetectionClassesEntry', (_message.Message,), {
    'DESCRIPTOR' : _TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY,
    '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
    # @@protoc_insertion_point(class_scope:weed_tracking.TrajectorySnapshot.DetectionClassesEntry)
    })
  ,
  'DESCRIPTOR' : _TRAJECTORYSNAPSHOT,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.TrajectorySnapshot)
  })
_sym_db.RegisterMessage(TrajectorySnapshot)
_sym_db.RegisterMessage(TrajectorySnapshot.DetectionClassesEntry)

SnapshotMetadata = _reflection.GeneratedProtocolMessageType('SnapshotMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTMETADATA,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.SnapshotMetadata)
  })
_sym_db.RegisterMessage(SnapshotMetadata)

BandDefinition = _reflection.GeneratedProtocolMessageType('BandDefinition', (_message.Message,), {
  'DESCRIPTOR' : _BANDDEFINITION,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.BandDefinition)
  })
_sym_db.RegisterMessage(BandDefinition)

CLDAlgorithmSnapshot = _reflection.GeneratedProtocolMessageType('CLDAlgorithmSnapshot', (_message.Message,), {
  'DESCRIPTOR' : _CLDALGORITHMSNAPSHOT,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.CLDAlgorithmSnapshot)
  })
_sym_db.RegisterMessage(CLDAlgorithmSnapshot)

DiagnosticsSnapshot = _reflection.GeneratedProtocolMessageType('DiagnosticsSnapshot', (_message.Message,), {
  'DESCRIPTOR' : _DIAGNOSTICSSNAPSHOT,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.DiagnosticsSnapshot)
  })
_sym_db.RegisterMessage(DiagnosticsSnapshot)

RecordDiagnosticsRequest = _reflection.GeneratedProtocolMessageType('RecordDiagnosticsRequest', (_message.Message,), {
  'DESCRIPTOR' : _RECORDDIAGNOSTICSREQUEST,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.RecordDiagnosticsRequest)
  })
_sym_db.RegisterMessage(RecordDiagnosticsRequest)

GetRecordingStatusResponse = _reflection.GeneratedProtocolMessageType('GetRecordingStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETRECORDINGSTATUSRESPONSE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetRecordingStatusResponse)
  })
_sym_db.RegisterMessage(GetRecordingStatusResponse)

StartSavingCropLineDetectionReplayRequest = _reflection.GeneratedProtocolMessageType('StartSavingCropLineDetectionReplayRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTSAVINGCROPLINEDETECTIONREPLAYREQUEST,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.StartSavingCropLineDetectionReplayRequest)
  })
_sym_db.RegisterMessage(StartSavingCropLineDetectionReplayRequest)

RecordAimbotInputRequest = _reflection.GeneratedProtocolMessageType('RecordAimbotInputRequest', (_message.Message,), {
  'DESCRIPTOR' : _RECORDAIMBOTINPUTREQUEST,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.RecordAimbotInputRequest)
  })
_sym_db.RegisterMessage(RecordAimbotInputRequest)

ConclusionCount = _reflection.GeneratedProtocolMessageType('ConclusionCount', (_message.Message,), {
  'DESCRIPTOR' : _CONCLUSIONCOUNT,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.ConclusionCount)
  })
_sym_db.RegisterMessage(ConclusionCount)

ConclusionCounter = _reflection.GeneratedProtocolMessageType('ConclusionCounter', (_message.Message,), {
  'DESCRIPTOR' : _CONCLUSIONCOUNTER,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.ConclusionCounter)
  })
_sym_db.RegisterMessage(ConclusionCounter)

BandDefinitions = _reflection.GeneratedProtocolMessageType('BandDefinitions', (_message.Message,), {
  'DESCRIPTOR' : _BANDDEFINITIONS,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.BandDefinitions)
  })
_sym_db.RegisterMessage(BandDefinitions)

PlantCaptchaStatusResponse = _reflection.GeneratedProtocolMessageType('PlantCaptchaStatusResponse', (_message.Message,), {

  'ExemplarCountsEntry' : _reflection.GeneratedProtocolMessageType('ExemplarCountsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY,
    '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
    # @@protoc_insertion_point(class_scope:weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry)
    })
  ,
  'DESCRIPTOR' : _PLANTCAPTCHASTATUSRESPONSE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.PlantCaptchaStatusResponse)
  })
_sym_db.RegisterMessage(PlantCaptchaStatusResponse)
_sym_db.RegisterMessage(PlantCaptchaStatusResponse.ExemplarCountsEntry)

Embedding = _reflection.GeneratedProtocolMessageType('Embedding', (_message.Message,), {
  'DESCRIPTOR' : _EMBEDDING,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.Embedding)
  })
_sym_db.RegisterMessage(Embedding)

WeedClasses = _reflection.GeneratedProtocolMessageType('WeedClasses', (_message.Message,), {

  'ClassesEntry' : _reflection.GeneratedProtocolMessageType('ClassesEntry', (_message.Message,), {
    'DESCRIPTOR' : _WEEDCLASSES_CLASSESENTRY,
    '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
    # @@protoc_insertion_point(class_scope:weed_tracking.WeedClasses.ClassesEntry)
    })
  ,
  'DESCRIPTOR' : _WEEDCLASSES,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.WeedClasses)
  })
_sym_db.RegisterMessage(WeedClasses)
_sym_db.RegisterMessage(WeedClasses.ClassesEntry)

PlantCaptchaItemMetadata = _reflection.GeneratedProtocolMessageType('PlantCaptchaItemMetadata', (_message.Message,), {

  'CategoriesEntry' : _reflection.GeneratedProtocolMessageType('CategoriesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY,
    '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
    # @@protoc_insertion_point(class_scope:weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry)
    })
  ,

  'WeedCategoriesEntry' : _reflection.GeneratedProtocolMessageType('WeedCategoriesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY,
    '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
    # @@protoc_insertion_point(class_scope:weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry)
    })
  ,

  'EmbeddingDistancesEntry' : _reflection.GeneratedProtocolMessageType('EmbeddingDistancesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY,
    '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
    # @@protoc_insertion_point(class_scope:weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry)
    })
  ,
  'DESCRIPTOR' : _PLANTCAPTCHAITEMMETADATA,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.PlantCaptchaItemMetadata)
  })
_sym_db.RegisterMessage(PlantCaptchaItemMetadata)
_sym_db.RegisterMessage(PlantCaptchaItemMetadata.CategoriesEntry)
_sym_db.RegisterMessage(PlantCaptchaItemMetadata.WeedCategoriesEntry)
_sym_db.RegisterMessage(PlantCaptchaItemMetadata.EmbeddingDistancesEntry)

GetTargetingEnabledRequest = _reflection.GeneratedProtocolMessageType('GetTargetingEnabledRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTARGETINGENABLEDREQUEST,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetTargetingEnabledRequest)
  })
_sym_db.RegisterMessage(GetTargetingEnabledRequest)

GetTargetingEnabledResponse = _reflection.GeneratedProtocolMessageType('GetTargetingEnabledResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETTARGETINGENABLEDRESPONSE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetTargetingEnabledResponse)
  })
_sym_db.RegisterMessage(GetTargetingEnabledResponse)

GetBootedRequest = _reflection.GeneratedProtocolMessageType('GetBootedRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETBOOTEDREQUEST,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetBootedRequest)
  })
_sym_db.RegisterMessage(GetBootedRequest)

GetBootedResponse = _reflection.GeneratedProtocolMessageType('GetBootedResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETBOOTEDRESPONSE,
  '__module__' : 'weed_tracking.proto.weed_tracking_pb2'
  # @@protoc_insertion_point(class_scope:weed_tracking.GetBootedResponse)
  })
_sym_db.RegisterMessage(GetBootedResponse)


DESCRIPTOR._options = None
_TRAJECTORY._options = None
_TARGET._options = None
_BOUNDS._options = None
_TRACKINGSTATUSREPLY._options = None
_THRESHOLDS.fields_by_name['weeding']._options = None
_THRESHOLDS.fields_by_name['thinning']._options = None
_THRESHOLDS.fields_by_name['banding']._options = None
_TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY._options = None
_TRAJECTORYSNAPSHOT.fields_by_name['is_weed']._options = None
_PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY._options = None
_WEEDCLASSES_CLASSESENTRY._options = None
_PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY._options = None
_PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY._options = None
_PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY._options = None

_WEEDTRACKINGSERVICE = _descriptor.ServiceDescriptor(
  name='WeedTrackingService',
  full_name='weed_tracking.WeedTrackingService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=8533,
  serialized_end=10077,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='weed_tracking.WeedTrackingService.Ping',
    index=0,
    containing_service=None,
    input_type=_PINGREQUEST,
    output_type=_PONGREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDetections',
    full_name='weed_tracking.WeedTrackingService.GetDetections',
    index=1,
    containing_service=None,
    input_type=_GETDETECTIONSREQUEST,
    output_type=_GETDETECTIONSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTrajectoryMetadata',
    full_name='weed_tracking.WeedTrackingService.GetTrajectoryMetadata',
    index=2,
    containing_service=None,
    input_type=_GETTRAJECTORYMETADATAREQUEST,
    output_type=_GETTRAJECTORYMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UpdateBands',
    full_name='weed_tracking.WeedTrackingService.UpdateBands',
    index=3,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetBooted',
    full_name='weed_tracking.WeedTrackingService.GetBooted',
    index=4,
    containing_service=None,
    input_type=_GETBOOTEDREQUEST,
    output_type=_GETBOOTEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCurrentTrajectories',
    full_name='weed_tracking.WeedTrackingService.GetCurrentTrajectories',
    index=5,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_DIAGNOSTICSSNAPSHOT,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartSavingCropLineDetectionReplay',
    full_name='weed_tracking.WeedTrackingService.StartSavingCropLineDetectionReplay',
    index=6,
    containing_service=None,
    input_type=_STARTSAVINGCROPLINEDETECTIONREPLAYREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartRecordingDiagnostics',
    full_name='weed_tracking.WeedTrackingService.StartRecordingDiagnostics',
    index=7,
    containing_service=None,
    input_type=_RECORDDIAGNOSTICSREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDiagnosticsRecordingStatus',
    full_name='weed_tracking.WeedTrackingService.GetDiagnosticsRecordingStatus',
    index=8,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_GETRECORDINGSTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='RemoveRecordingsDirectory',
    full_name='weed_tracking.WeedTrackingService.RemoveRecordingsDirectory',
    index=9,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartRecordingAimbotInputs',
    full_name='weed_tracking.WeedTrackingService.StartRecordingAimbotInputs',
    index=10,
    containing_service=None,
    input_type=_RECORDAIMBOTINPUTREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetConclusionCounter',
    full_name='weed_tracking.WeedTrackingService.GetConclusionCounter',
    index=11,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_CONCLUSIONCOUNTER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetBands',
    full_name='weed_tracking.WeedTrackingService.GetBands',
    index=12,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_BANDDEFINITIONS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartPlantCaptcha',
    full_name='weed_tracking.WeedTrackingService.StartPlantCaptcha',
    index=13,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetPlantCaptchaStatus',
    full_name='weed_tracking.WeedTrackingService.GetPlantCaptchaStatus',
    index=14,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_PLANTCAPTCHASTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='RemovePlantCaptchaDirectory',
    full_name='weed_tracking.WeedTrackingService.RemovePlantCaptchaDirectory',
    index=15,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CancelPlantCaptcha',
    full_name='weed_tracking.WeedTrackingService.CancelPlantCaptcha',
    index=16,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTargetingEnabled',
    full_name='weed_tracking.WeedTrackingService.GetTargetingEnabled',
    index=17,
    containing_service=None,
    input_type=_GETTARGETINGENABLEDREQUEST,
    output_type=_GETTARGETINGENABLEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_WEEDTRACKINGSERVICE)

DESCRIPTOR.services_by_name['WeedTrackingService'] = _WEEDTRACKINGSERVICE

# @@protoc_insertion_point(module_scope)
