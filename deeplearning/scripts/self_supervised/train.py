import argparse
import logging
import time

from deeplearning.constants import CARBON_DATA_DIR, S3_BUCKET
from deeplearning.scripts.self_supervised.utils import Config, DatasetWrapper, convert_trt, get_datasets, get_model
from deeplearning.scripts.utils.utils import add_common_arguments, generate_model_id, setup_logging
from deeplearning.self_supervised.train import train
from deeplearning.utils.trainer import (
    compute_md5sum,
    get_compute_capability,
    get_tensorrt_file_name,
    get_tensorrt_version,
    upload_directory,
)
from lib.common.veselka.client import VeselkaClient

setup_logging()

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    add_common_arguments(parser)
    args = parser.parse_args()

    model_id = args.job_id
    if model_id is None:
        model_id = generate_model_id()

    config_dict = {
        "num_epochs": 1 if args.fast_run else 100,
        "num_samples": 64 if args.fast_run else 10000,
        "num_validation_samples": 2 if args.fast_run else 2000,
        "wandb_project": "selfsup-fast-run" if args.fast_run else "selfsup",
        "num_gpus": args.nproc_per_node,
        "model_id": model_id,
        "description": args.description,
        "fast_run": args.fast_run,
        "data_dir": f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
        **args.dl_config,
    }

    config = Config(**config_dict)
    LOG.info(f"Config: \n{config_dict}")

    stub_json = {"id": config.model_id}
    VeselkaClient().post_model(stub_json)

    train_dataset, val_dataset, _ = get_datasets(
        config=config, dataset_id=args.dataset_id, pipeline_id=args.pipeline_id, exist_ok=True
    )

    LOG.info("Downloading Model...")
    model = get_model(config)
    LOG.info("Finished downloading model")
    train(model, train_dataset, val_dataset, **config.model_dump())

    trt_path = f"{config.data_dir}/{get_tensorrt_file_name(convert_int8=True)}"

    num_trt_samples = 100
    convert_trt(
        model.encoder,
        calib_dataset=DatasetWrapper(train_dataset, num_samples=num_trt_samples),
        validation_dataset=DatasetWrapper(val_dataset, num_samples=num_trt_samples),
        config=config,
        trt_path=trt_path,
        max_batch_size=3,
        calibration_batch_size=16,
    )

    model_json = {
        "id": config.model_id,
        "trained_at": int(time.time()),
        "url": f"s3://{S3_BUCKET}/models/{config.model_id}/{get_tensorrt_file_name(convert_int8=True)}",
        "dl_config": config.model_dump(),
        "tensorrt_version": get_tensorrt_version(),
        "compute_capability": get_compute_capability(),
        "pipeline_id": args.pipeline_id,
        "dataset_id": args.dataset_id,
        "checksum": compute_md5sum(trt_path),
        "fast_run": args.fast_run,
        "description": args.description,
        "type": "selfsup",
    }

    VeselkaClient().post_model(model_json)

    upload_directory(config.data_dir)
