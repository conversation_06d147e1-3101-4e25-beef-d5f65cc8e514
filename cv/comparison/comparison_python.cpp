#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <torch/extension.h>
#include <torch/torch.h>

#include "cv/comparison/comparison_model.h"

namespace py = pybind11;

namespace cv {
namespace comparison {

PYBIND11_MODULE(comparison_python, m) {
  py::module::import("lib.common.model.cpp.model_python");

  py::class_<comparison::ComparisonModel>(m, "ComparisonModel")
      .def(py::init<lib::common::model::AtomicModel>(), py::call_guard<py::gil_scoped_release>())
      .def("infer", &comparison::ComparisonModel::infer, py::arg("image_tensor"), py::arg("x"), py::arg("y"),
           py::call_guard<py::gil_scoped_release>())
      .def("infer_chip", &comparison::ComparisonModel::infer_chip, py::arg("image_tensor"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_input_size", &comparison::ComparisonModel::get_input_size, py::call_guard<py::gil_scoped_release>());

  py::class_<comparison::ComparisonOutput>(m, "ComparisonOutput")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def_readwrite("embedding", &comparison::ComparisonOutput::embedding);
}

} // namespace comparison
} // namespace cv
