// @ts-nocheck
// @generated by protobuf-ts 2.9.1
// @generated from protobuf file "sim_UI.proto" (package "carbon.simulator_UI", syntax proto3)
// tslint:disable
import type { RpcTransport } from "@protobuf-ts/runtime-rpc";
import type { ServiceInfo } from "@protobuf-ts/runtime-rpc";
import { SimulatorUIService } from "./sim_UI";
import type { SetDiagnosticSettingsResponse } from "./sim_UI";
import type { SetDiagnosticSettingsRequest } from "./sim_UI";
import type { GetDiagnosticSettingsResponse } from "./sim_UI";
import type { GetDiagnosticSettingsRequest } from "./sim_UI";
import type { SetPresetResponse } from "./sim_UI";
import type { SetPresetRequest } from "./sim_UI";
import type { GetPresetsResponse } from "./sim_UI";
import type { GetPresetsRequest } from "./sim_UI";
import type { SetVelocityResponse } from "./sim_UI";
import type { SetVelocityRequest } from "./sim_UI";
import type { UnaryCall } from "@protobuf-ts/runtime-rpc";
import { stackIntercept } from "@protobuf-ts/runtime-rpc";
import type { GetVelocityResponse } from "./sim_UI";
import type { GetVelocityRequest } from "./sim_UI";
import type { ServerStreamingCall } from "@protobuf-ts/runtime-rpc";
import type { RpcOptions } from "@protobuf-ts/runtime-rpc";
/**
 * @generated from protobuf service carbon.simulator_UI.SimulatorUIService
 */
export interface ISimulatorUIServiceClient {
    /**
     * @generated from protobuf rpc: GetVelocityStream(carbon.simulator_UI.GetVelocityRequest) returns (stream carbon.simulator_UI.GetVelocityResponse);
     */
    getVelocityStream(input: GetVelocityRequest, options?: RpcOptions): ServerStreamingCall<GetVelocityRequest, GetVelocityResponse>;
    /**
     * @generated from protobuf rpc: SetVelocity(carbon.simulator_UI.SetVelocityRequest) returns (carbon.simulator_UI.SetVelocityResponse);
     */
    setVelocity(input: SetVelocityRequest, options?: RpcOptions): UnaryCall<SetVelocityRequest, SetVelocityResponse>;
    /**
     * @generated from protobuf rpc: GetPresetStream(carbon.simulator_UI.GetPresetsRequest) returns (stream carbon.simulator_UI.GetPresetsResponse);
     */
    getPresetStream(input: GetPresetsRequest, options?: RpcOptions): ServerStreamingCall<GetPresetsRequest, GetPresetsResponse>;
    /**
     * @generated from protobuf rpc: SetPreset(carbon.simulator_UI.SetPresetRequest) returns (carbon.simulator_UI.SetPresetResponse);
     */
    setPreset(input: SetPresetRequest, options?: RpcOptions): UnaryCall<SetPresetRequest, SetPresetResponse>;
    /**
     * @generated from protobuf rpc: GetDiagnosticSettings(carbon.simulator_UI.GetDiagnosticSettingsRequest) returns (carbon.simulator_UI.GetDiagnosticSettingsResponse);
     */
    getDiagnosticSettings(input: GetDiagnosticSettingsRequest, options?: RpcOptions): UnaryCall<GetDiagnosticSettingsRequest, GetDiagnosticSettingsResponse>;
    /**
     * @generated from protobuf rpc: SetDiagnosticSettings(carbon.simulator_UI.SetDiagnosticSettingsRequest) returns (carbon.simulator_UI.SetDiagnosticSettingsResponse);
     */
    setDiagnosticSettings(input: SetDiagnosticSettingsRequest, options?: RpcOptions): UnaryCall<SetDiagnosticSettingsRequest, SetDiagnosticSettingsResponse>;
}
/**
 * @generated from protobuf service carbon.simulator_UI.SimulatorUIService
 */
export class SimulatorUIServiceClient implements ISimulatorUIServiceClient, ServiceInfo {
    typeName = SimulatorUIService.typeName;
    methods = SimulatorUIService.methods;
    options = SimulatorUIService.options;
    constructor(private readonly _transport: RpcTransport) {
    }
    /**
     * @generated from protobuf rpc: GetVelocityStream(carbon.simulator_UI.GetVelocityRequest) returns (stream carbon.simulator_UI.GetVelocityResponse);
     */
    getVelocityStream(input: GetVelocityRequest, options?: RpcOptions): ServerStreamingCall<GetVelocityRequest, GetVelocityResponse> {
        const method = this.methods[0], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetVelocityRequest, GetVelocityResponse>("serverStreaming", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: SetVelocity(carbon.simulator_UI.SetVelocityRequest) returns (carbon.simulator_UI.SetVelocityResponse);
     */
    setVelocity(input: SetVelocityRequest, options?: RpcOptions): UnaryCall<SetVelocityRequest, SetVelocityResponse> {
        const method = this.methods[1], opt = this._transport.mergeOptions(options);
        return stackIntercept<SetVelocityRequest, SetVelocityResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetPresetStream(carbon.simulator_UI.GetPresetsRequest) returns (stream carbon.simulator_UI.GetPresetsResponse);
     */
    getPresetStream(input: GetPresetsRequest, options?: RpcOptions): ServerStreamingCall<GetPresetsRequest, GetPresetsResponse> {
        const method = this.methods[2], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetPresetsRequest, GetPresetsResponse>("serverStreaming", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: SetPreset(carbon.simulator_UI.SetPresetRequest) returns (carbon.simulator_UI.SetPresetResponse);
     */
    setPreset(input: SetPresetRequest, options?: RpcOptions): UnaryCall<SetPresetRequest, SetPresetResponse> {
        const method = this.methods[3], opt = this._transport.mergeOptions(options);
        return stackIntercept<SetPresetRequest, SetPresetResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: GetDiagnosticSettings(carbon.simulator_UI.GetDiagnosticSettingsRequest) returns (carbon.simulator_UI.GetDiagnosticSettingsResponse);
     */
    getDiagnosticSettings(input: GetDiagnosticSettingsRequest, options?: RpcOptions): UnaryCall<GetDiagnosticSettingsRequest, GetDiagnosticSettingsResponse> {
        const method = this.methods[4], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetDiagnosticSettingsRequest, GetDiagnosticSettingsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: SetDiagnosticSettings(carbon.simulator_UI.SetDiagnosticSettingsRequest) returns (carbon.simulator_UI.SetDiagnosticSettingsResponse);
     */
    setDiagnosticSettings(input: SetDiagnosticSettingsRequest, options?: RpcOptions): UnaryCall<SetDiagnosticSettingsRequest, SetDiagnosticSettingsResponse> {
        const method = this.methods[5], opt = this._transport.mergeOptions(options);
        return stackIntercept<SetDiagnosticSettingsRequest, SetDiagnosticSettingsResponse>("unary", this._transport, method, opt, input);
    }
}
