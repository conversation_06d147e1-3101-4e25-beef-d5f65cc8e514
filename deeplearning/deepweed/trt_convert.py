import multiprocessing
from multiprocessing import Process
from typing import Any, Dict, List, Optional, Tuple, Union, cast

import fire
import numpy as np
import tensorrt as trt
import torch
from torch2trt import torch2trt
from tqdm import tqdm

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.datasets_types import DatasetLabel
from deeplearning.deepweed.model import Deepweed, DeepweedOutput, DeepweedOutputFactory
from deeplearning.deepweed.remote_veselka_dataset import (
    RemoteVeselkaDataset,
    RemoteVeselkaDatasetClient,
    RemoteVeselkaDatasetServer,
)
from deeplearning.model_io import load_pytorch_model, load_tensorrt_model, peek_pytorch_metadata, save_tensorrt_model
from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.utils.dataset import DatasetType
from deeplearning.utils.resize_utils import tile_crop_origins
from deeplearning.utils.trt_utils import compute_deepweed_metrics
from lib.common.perf.perf_tracker import duration_perf_recorder_decorator, set_verbosity
from lib.common.veselka.client import VeselkaClient

set_verbosity(False)

MAX_CALIBRATION_ITEMS = 500
MIN_CALIBRATION_ITEMS = 50


class DatasetWrapper:
    def __init__(
        self, dataset: RemoteVeselkaDatasetClient, crop_ids: Optional[List[str]], crop_embeddings: bool
    ) -> None:
        self._dataset = dataset
        self._crop_ids = crop_ids
        self._crop_embeddings = crop_embeddings

    def __getitem__(self, index: int) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        datapoint = self._dataset[index]
        image = datapoint.image.cuda()
        if self._crop_embeddings:
            assert self._crop_ids is not None
            assert datapoint.image_meta.crop_id is not None, f"Image {datapoint.image_meta.filepath} has no crop_id set"
            crop_id_idx = self._crop_ids.index(datapoint.image_meta.crop_id)
        datapoint.enabled_hits

        assert datapoint.target_list is not None, "Datapoint is missing target list."
        target_list = [x if x is None else x.cuda() for x in datapoint.target_list]
        image, _, _ = self._dataset.preprocess_datapoint(
            image,
            target_list,
            datapoint.points,
            datapoint.image_meta,
            set([x for i, x in enumerate(self._dataset.weed_classes) if datapoint.enabled_weed_point_classes[i] == 1]),
            set([x for i, x in enumerate(Deepweed.SUPPORTED_HIT_CLASSES) if datapoint.enabled_hits[i] == 1]),
            datapoint.enabled_segm_classes,
        )
        if self._crop_embeddings:
            return image, torch.tensor([crop_id_idx], dtype=torch.int32).cuda()
        else:
            return image

    def __len__(self) -> int:
        return len(self._dataset)


class TrtConvert(object):
    def __init__(
        self,
        calibration_dataset: Optional[RemoteVeselkaDatasetClient] = None,
        validation_dataset: Optional[RemoteVeselkaDatasetClient] = None,
        test_dataset: Optional[RemoteVeselkaDatasetClient] = None,
        metadata: Optional[ModelMetadata] = None,
        model: Optional[torch.nn.Module] = None,
    ) -> None:
        self._calibration_dataset = calibration_dataset
        self._validation_dataset = validation_dataset
        self._test_dataset = test_dataset
        if self._calibration_dataset is not None:
            assert len(self._calibration_dataset), "Provided dataset does not have any labelled data."
        self._ckpt_metadata = metadata
        self._enable_crop_embeddings = (
            False
            if self._ckpt_metadata is None or self._ckpt_metadata.crop_embeddings is None
            else self._ckpt_metadata.crop_embeddings
        )
        self._model = model
        self._remote_dataset_process: Optional[Process] = None

    @duration_perf_recorder_decorator("Training")
    def load_model(
        self,
        checkpoint: str,
        config: DeepweedConfig,
        fixed_crop_id: Optional[str] = None,
        segmentation_checkpoint: Optional[str] = None,
    ) -> "TrtConvert":
        # Grab metadata
        self._ckpt_metadata = peek_pytorch_metadata(checkpoint)
        self._enable_crop_embeddings = (
            self._ckpt_metadata.crop_embeddings if self._ckpt_metadata.crop_embeddings is not None else False
        )

        print(f"Crop embeddings: {self._enable_crop_embeddings}")

        if segmentation_checkpoint is not None:
            self._ckpt_metadata._segm_classes = peek_pytorch_metadata(segmentation_checkpoint).segm_classes
        template_model = Deepweed(
            num_weed_point_classes=len(self._ckpt_metadata.weed_point_classes or []),
            num_segm_classes=len(self._ckpt_metadata.segm_classes or []),
            num_crop_ids=len(self._ckpt_metadata.crop_ids or []),
            enable_crop_embeddings=self._ckpt_metadata.crop_embeddings or False,
            discard_points_border_px=self._ckpt_metadata.discard_points_border_px,
            use_pumap_head=cast(bool, self._ckpt_metadata.contains_pumap_head),
            config=config,
            fixed_crop_idx=None
            if not fixed_crop_id or not self._ckpt_metadata.crop_ids
            else self._ckpt_metadata.crop_ids.index(fixed_crop_id),
            pretrained_segmentation_model=segmentation_checkpoint,
        )

        self._enable_crop_embeddings = False if fixed_crop_id else self._enable_crop_embeddings
        self._ckpt_metadata._crop_embeddings = self._enable_crop_embeddings

        self._model, _ = load_pytorch_model(template_model, checkpoint, strict=False)
        if self._model.use_pumap_head:
            self._model._pumap_head.set_scaler_shifter(self._ckpt_metadata.scaler_shifter_parameters)

        return self

    @duration_perf_recorder_decorator("Training")
    def load_datasets(self, dataset_id: str) -> "TrtConvert":
        assert self._ckpt_metadata is not None
        assert self._ckpt_metadata.input_size is not None

        client = VeselkaClient()
        dataset_info = client.get_dataset(dataset_id)
        client.download_dataset(dataset_info)

        train_path = f"/data/deeplearning/datasets/{dataset_id}/{dataset_info['train'].split('/')[-1]}"
        validation_path = f"/data/deeplearning/datasets/{dataset_id}/{dataset_info['validation'].split('/')[-1]}"
        test_path = f"/data/deeplearning/datasets/{dataset_id}/{dataset_info['test'].split('/')[-1]}"

        height, width = self._ckpt_metadata.input_size

        config = DeepweedConfig(
            evaluation_image_height=height,
            evaluation_image_width=width,
            dataset_version=2 if train_path[-1] == "l" else 1,
        )

        def init_dataset_server() -> None:
            assert self._ckpt_metadata is not None
            assert self._ckpt_metadata.segm_classes is not None
            assert self._ckpt_metadata.weed_point_classes is not None
            assert self._ckpt_metadata.crop_point_classes is not None
            calibration_dataset = RemoteVeselkaDataset(
                config,
                train_path,
                mode=DatasetType.CALIBRATION,
                num_samples=500,
                segm_classes=tuple(self._ckpt_metadata.segm_classes),
                weed_classes=tuple(self._ckpt_metadata.weed_point_classes),
                crop_classes=tuple(self._ckpt_metadata.crop_point_classes),
            )
            validation_dataset = RemoteVeselkaDataset(
                config,
                validation_path,
                mode=DatasetType.VALIDATION,
                segm_classes=tuple(self._ckpt_metadata.segm_classes),
                weed_classes=tuple(self._ckpt_metadata.weed_point_classes),
                crop_classes=tuple(self._ckpt_metadata.crop_point_classes),
            )
            test_dataset = RemoteVeselkaDataset(
                config,
                test_path,
                mode=DatasetType.TEST,
                segm_classes=tuple(self._ckpt_metadata.segm_classes),
                weed_classes=tuple(self._ckpt_metadata.weed_point_classes),
                crop_classes=tuple(self._ckpt_metadata.crop_point_classes),
            )
            remote_dataset_server = RemoteVeselkaDatasetServer(
                {
                    DatasetType.CALIBRATION: calibration_dataset,
                    DatasetType.VALIDATION: validation_dataset,
                    DatasetType.TEST: test_dataset,
                }
            )
            remote_dataset_server.run()

        self._remote_dataset_process = Process(target=init_dataset_server, daemon=True)
        self._remote_dataset_process.start()

        self._calibration_dataset = RemoteVeselkaDatasetClient(DatasetType.CALIBRATION, config)
        self._validation_dataset = RemoteVeselkaDatasetClient(DatasetType.VALIDATION, config)
        self._test_dataset = RemoteVeselkaDatasetClient(DatasetType.TEST, config)
        assert len(self._calibration_dataset), "Provided dataset does not have any labelled data."
        return self

    def process_datapoint(
        self, dataset: RemoteVeselkaDatasetClient, index: int
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], DatasetLabel]:
        datapoint = dataset[index]
        image = datapoint.image.cuda()
        assert datapoint.target_list is not None, "Datapoint is missing target list."
        image_metadata = datapoint.image_meta
        if self._enable_crop_embeddings:
            assert self._ckpt_metadata is not None
            assert self._ckpt_metadata.crop_ids is not None
            assert image_metadata.crop_id is not None, f"Image {image_metadata.filepath} has no crop_id set"
            crop_id_idx = torch.tensor(
                [self._ckpt_metadata.crop_ids.index(image_metadata.crop_id)], dtype=torch.int, device=image.device,
            )
        else:
            crop_id_idx = None
        target_list = [x if x is None else x.cuda() for x in datapoint.target_list]
        image, target, _ = dataset.preprocess_datapoint(
            image,
            target_list,
            datapoint.points,
            datapoint.image_meta,
            set([x for i, x in enumerate(dataset.weed_classes) if datapoint.enabled_weed_point_classes[i] == 1]),
            set([x for i, x in enumerate(Deepweed.SUPPORTED_HIT_CLASSES) if datapoint.enabled_hits[i] == 1]),
            datapoint.enabled_segm_classes,
        )

        return image, crop_id_idx, target

    def run_on_dataset(
        self,
        dataset: RemoteVeselkaDatasetClient,
        model: Any,
        max_batch_size: int,
        fp16: bool = False,
        trt: bool = False,
        num_iters: int = 20,
    ) -> List[DeepweedOutput]:
        assert self._ckpt_metadata is not None
        assert self._ckpt_metadata.input_size is not None
        assert self._ckpt_metadata.tile is not None
        output = []
        for index in range(min(20, len(dataset))):
            image, crop_id_idx, _ = self.process_datapoint(dataset, index)
            if fp16 and trt:
                image = image.half().unsqueeze(0).cuda()
            elif trt:
                image = image.unsqueeze(0).cuda()

            for y, x in tile_crop_origins(
                size=(image.shape[-2], image.shape[-1]),
                crop_size=self._ckpt_metadata.input_size,
                tile=self._ckpt_metadata.tile,
            ):
                input_image = image[
                    ..., y : y + self._ckpt_metadata.input_size[0], x : x + self._ckpt_metadata.input_size[1]
                ]
                if trt:
                    if self._enable_crop_embeddings:
                        assert crop_id_idx is not None
                        packed_out = model(
                            input_image.repeat(max_batch_size, 1, 1, 1), crop_id_idx.repeat(max_batch_size)
                        )
                    else:
                        packed_out = model(input_image.repeat(max_batch_size, 1, 1, 1))
                    out = DeepweedOutputFactory.unpack(packed_out).cpu()
                else:

                    input_image = input_image.unsqueeze(0)
                    out = DeepweedOutputFactory.unpack(model(input_image, crop_id_idx)).detach().cpu()

                output.append(out)

        return output

    @staticmethod
    def evaluate_on_test(
        trt_path: str,
        config: DeepweedConfig,
        dataset: RemoteVeselkaDatasetClient,
        ckpt_metadata: ModelMetadata,
        max_batch_size: int,
        rank: int,
        world_size: int,
        num_iters: int = 20,
        fp16: bool = False,
        enable_crop_embeddings: bool = False,
    ) -> Dict[str, Any]:
        metrics: List[Dict[str, Any]] = []
        metric_count = 0

        assert ckpt_metadata.input_size is not None
        assert ckpt_metadata.tile is not None

        if rank == 0:
            print("Evaluating on test set...")

        start = rank * num_iters // world_size
        end = (rank + 1) * num_iters // world_size

        if rank == world_size - 1:
            end = num_iters

        device = torch.device(f"cuda:{rank}")
        torch.cuda.set_device(device)

        trt_model, _ = load_tensorrt_model(trt_path)
        trt_model.set_cache_context(True)

        print(f"Rank {rank} loaded model")

        iter_target = range(start, end)

        if rank == 0:
            iter_target = tqdm(iter_target)

        for i in iter_target:
            datapoint = dataset[i]
            image = datapoint.image.to(device)

            assert datapoint.target_list is not None, "Datapoint is missing target list."
            image_metadata = datapoint.image_meta
            if enable_crop_embeddings:
                assert ckpt_metadata.crop_ids is not None
                assert image_metadata.crop_id is not None, f"Image {image_metadata.filepath} has no crop_id set"
                crop_id_idx = torch.tensor(
                    [ckpt_metadata.crop_ids.index(image_metadata.crop_id)], dtype=torch.int, device=image.device,
                )
            else:
                crop_id_idx = None
            target_list = [x if x is None else x.to(device) for x in datapoint.target_list]
            image, label, _ = dataset.preprocess_datapoint(
                image,
                target_list,
                datapoint.points,
                datapoint.image_meta,
                set([x for i, x in enumerate(dataset.weed_classes) if datapoint.enabled_weed_point_classes[i] == 1]),
                set([x for i, x in enumerate(Deepweed.SUPPORTED_HIT_CLASSES) if datapoint.enabled_hits[i] == 1]),
                datapoint.enabled_segm_classes,
            )

            label.mask = label.mask.unsqueeze(0) if len(label.mask.shape) == 3 else label.mask

            if fp16:
                image = image.half().unsqueeze(0).to(device)
            else:
                image = image.unsqueeze(0).to(device)

            for y, x in tile_crop_origins(
                size=(image.shape[-2], image.shape[-1]), crop_size=ckpt_metadata.input_size, tile=ckpt_metadata.tile,
            ):
                input_image = image[..., y : y + ckpt_metadata.input_size[0], x : x + ckpt_metadata.input_size[1]]
                if enable_crop_embeddings:
                    assert crop_id_idx is not None
                    packed_out = trt_model(
                        input_image.repeat(max_batch_size, 1, 1, 1), crop_id_idx.repeat(max_batch_size)
                    )
                else:
                    packed_out = trt_model(input_image.repeat(max_batch_size, 1, 1, 1))

                out_hat = DeepweedOutputFactory.unpack(packed_out)

                out_hat.mask = out_hat.mask[0:1]
                out_hat.point_hits = [i[0:1] for i in out_hat.point_hits]
                out_hat.point_offsets = [i[0:1] for i in out_hat.point_offsets]
                out_hat.point_sizes = [i[0:1] for i in out_hat.point_sizes]
                out_hat.point_categories = [i[0:1] for i in out_hat.point_categories]

                tile_mask = label.mask[..., y : y + ckpt_metadata.input_size[0], x : x + ckpt_metadata.input_size[1]]
                tile_label = DatasetLabel(points=label.points, mask=tile_mask)

                out, out_point_confidence, out_point_crop_protection = DeepweedOutputFactory.from_label(
                    tile_label,
                    dataset.weed_classes,
                    use_crop_protection=config.use_crop_protection,
                    crop_protection_padding=config.crop_protection_padding,
                    baby_crop_size=config.baby_crop_size,
                    crop_protection_multiplier=config.crop_protection_multiplier,
                    baby_crop_protection_multiplier=config.baby_crop_protection_multiplier,
                    point_downsample=config.point_downsample,
                )

                weed_point_class_weights = torch.tensor([1.0 for _ in dataset.weed_classes]).to(device)
                weed_point_enabled_classes = (
                    (weed_point_class_weights > 0).unsqueeze(0).repeat(image.shape[0], 1).to(device)
                )
                batch_enabled_hits = datapoint.enabled_hits.unsqueeze(0).to(device)
                batch_enabled_weed_point_classes = datapoint.enabled_weed_point_classes.unsqueeze(0).to(device)
                batch_enabled_segm_classes = datapoint.enabled_segm_classes.unsqueeze(0).to(device)
                loss_multipliers = (torch.ones(1) * datapoint.loss_multiplier).unsqueeze(0).to(device)
                segm_class_weights = torch.tensor([1.0 for _ in dataset.segm_classes]).to(device)
                weed_point_class_weights = torch.tensor([1.0 for _ in dataset.weed_classes]).to(device)
                point_hit_weights = torch.tensor(
                    [1.0 if len(dataset.weed_classes) > 0 else 0.0, 1.0 if len(dataset.crop_classes) > 0 else 0.0, 1.0,]
                ).to(device)

                weed_point_category_to_index = {k: v for v, k in enumerate(dataset.weed_classes)}

                tile_label = tile_label.fence(
                    weed_point_enabled_classes, batch_enabled_hits, weed_point_category_to_index
                )

                if fp16:
                    out_hat = out_hat.float()

                dw_metrics, _ = compute_deepweed_metrics(
                    out,
                    out_hat,
                    out_label=tile_label,
                    batch_enabled_weed_point_classes=batch_enabled_weed_point_classes,
                    batch_enabled_segm_classes=batch_enabled_segm_classes,
                    batch_enabled_hits=batch_enabled_hits,
                    out_point_confidence=out_point_confidence,
                    out_point_crop_protection=out_point_crop_protection,
                    loss_multipliers=loss_multipliers,
                    segm_class_weights=segm_class_weights,
                    crop_classes=dataset.crop_classes,
                    weed_point_class_weights=weed_point_class_weights,
                    point_hit_weights=point_hit_weights,
                    embeddings=out_hat.embedding_output[0][0:1],
                    config=config,
                    image_metadata=[datapoint.image_meta],
                    dataset=dataset,
                    filepaths=None,
                )

                metrics.append(dw_metrics.get_metrics())
                metric_count += 1

        all_metrics: Dict[str, Any] = {}
        for metric_dict in metrics:
            for k, v in metric_dict.items():
                if k in all_metrics:
                    all_metrics[k] += v.item()
                else:
                    all_metrics[k] = v.item()

        for k, v in all_metrics.items():
            all_metrics[k] = v / metric_count

        return all_metrics

    def _nandiff(self, input1: torch.Tensor, input2: torch.Tensor) -> torch.Tensor:
        is_nan = torch.isnan(input1)
        input1[is_nan] = 0

        is_nan = torch.isnan(input2)
        input2[is_nan] = 0

        diff = (input1 - input2).abs()
        if diff.count_nonzero() == 0:
            return diff.sum() / 1
        return diff.sum() / diff.count_nonzero()

    def _nansim(self, input1: torch.Tensor, input2: torch.Tensor) -> torch.Tensor:
        is_nan = torch.isnan(input1)
        input1[is_nan] = 0

        is_nan = torch.isnan(input2)
        input2[is_nan] = 0

        out: torch.Tensor = torch.nn.CosineSimilarity(dim=1)(input1, input2)

        return out

    @duration_perf_recorder_decorator("Training")
    def convert(  # noqa: C901
        self,
        max_batch_size: int,
        save_to: str,
        fp16: bool = False,
        int8: bool = False,
        calibration_batch_size: int = 16,
        calibration_cache_input_file: Optional[str] = None,
        calibration_cache_output_file: Optional[str] = None,
        calibration_only: bool = False,
        error_metrics: bool = False,
        config: Optional[DeepweedConfig] = None,
        num_evaluation_samples: int = 500,
    ) -> Optional[Dict[str, float]]:
        assert self._ckpt_metadata is not None
        assert self._model is not None
        assert self._calibration_dataset is not None
        assert self._ckpt_metadata.input_size is not None and self._ckpt_metadata.tile is not None

        # Load the model.
        self._model.eval().cuda()

        if int8 and len(self._calibration_dataset) < MIN_CALIBRATION_ITEMS:
            print(
                f"WARNING: int8 calibration may not generalize well on small datasets ({len(self._calibration_dataset)})"
            )

        # Calculate metrics pre conversion.
        output_pyt = []
        if error_metrics and self._validation_dataset is not None:
            output_pyt = self.run_on_dataset(self._validation_dataset, self._model, max_batch_size, fp16)
        # Convert model to fp16 if needed.
        if fp16:
            self._model.half()

        # Prepare test tensor.
        datapoint = self._calibration_dataset[0]
        image = datapoint.image.cuda()
        assert datapoint.target_list is not None, "Datapoint is missing target list."
        target_list = [x if x is None else x.cuda() for x in datapoint.target_list]
        image, target, _ = self._calibration_dataset.preprocess_datapoint(
            image,
            target_list,
            datapoint.points,
            datapoint.image_meta,
            set(
                [
                    x
                    for i, x in enumerate(self._calibration_dataset.weed_classes)
                    if datapoint.enabled_weed_point_classes[i] == 1
                ]
            ),
            set([x for i, x in enumerate(Deepweed.SUPPORTED_HIT_CLASSES) if datapoint.enabled_hits[i] == 1]),
            datapoint.enabled_segm_classes,
        )
        datapoint.image = image
        datapoint.target = target

        test_tensor = torch.unsqueeze(datapoint.image, 0).repeat(max_batch_size, 1, 1, 1).cuda()
        if fp16:
            test_tensor = test_tensor.half()

        if self._enable_crop_embeddings:
            assert self._ckpt_metadata is not None
            assert self._ckpt_metadata.crop_ids is not None
            assert datapoint.image_meta.crop_id is not None, f"Image {datapoint.image_meta.filepath} has no crop_id set"
            test_crop_id_idx = torch.tensor(
                [self._ckpt_metadata.crop_ids.index(datapoint.image_meta.crop_id)] * max_batch_size,
                dtype=torch.int,
                device=test_tensor.device,
            )
            test_input = [test_tensor, test_crop_id_idx]
        else:
            test_input = [test_tensor]

        # Run the conversion.
        with torch.no_grad():
            trt_model = torch2trt(
                self._model,
                test_input,
                fp16_mode=fp16,
                int8_mode=int8,
                int8_calib_dataset=DatasetWrapper(
                    self._calibration_dataset,
                    self._ckpt_metadata.crop_ids if self._ckpt_metadata is not None else None,
                    self._enable_crop_embeddings,
                ),
                int8_calib_batch_size=calibration_batch_size,
                int8_calib_cache_input_path=calibration_cache_input_file,
                int8_calib_cache_output_path=calibration_cache_output_file,
                max_batch_size=max_batch_size,
                max_workspace_size=2 ** 22,
                log_level=trt.Logger.INFO,
                strict_type_constraints=True,
                use_implicit_batch_dimension=False,
            )

        # Calculate metrics after the conversion.
        output_trt = []
        if error_metrics and self._validation_dataset is not None:
            # Free model memory to support creating a trt context
            del self._model
            output_trt = self.run_on_dataset(self._validation_dataset, trt_model, max_batch_size, fp16, trt=True)

            point_hit_diffs = []
            point_offset_diffs = []
            point_size_diffs = []
            point_category_diffs = []
            mask_diffs = []
            embedding_sims = []
            for i in range(len(output_pyt)):
                for j in range(len(output_pyt[i].point_hits)):
                    positive_hits = output_pyt[i].point_hits[j] > 0.5
                    point_hit_diffs.append(
                        self._nandiff(
                            output_trt[i].point_hits[j][0:1][positive_hits], output_pyt[i].point_hits[j][positive_hits],
                        )
                    )
                    point_offset_diffs.append(
                        self._nandiff(
                            output_trt[i].point_offsets[j][0:1][positive_hits],
                            output_pyt[i].point_offsets[j][positive_hits],
                        )
                    )
                    point_size_diffs.append(
                        self._nandiff(
                            output_trt[i].point_sizes[j][0:1][positive_hits],
                            output_pyt[i].point_sizes[j][positive_hits],
                        )
                    )
                    if positive_hits.dim() >= 2:
                        positive_hits_categories = positive_hits[:, 0:1].repeat(
                            1, output_trt[i].point_categories[j].shape[1], 1, 1
                        )
                        point_category_diffs.append(
                            self._nandiff(
                                output_trt[i].point_categories[j][0:1][positive_hits_categories],
                                output_pyt[i].point_categories[j][positive_hits_categories],
                            )
                        )

                    mask_diffs.append(self._nandiff(output_trt[i].mask[0:1], output_pyt[i].mask))

                    if len(output_trt[i].embedding_output[0][0:1].shape) > 1:  # Don't run for driptape
                        embedding_sims.append(
                            self._nansim(output_trt[i].embedding_output[0][0:1], output_pyt[i].embedding_output[0])
                        )

            point_hit_diffs_np = np.array(point_hit_diffs)
            if point_hit_diffs_np.size != 0:
                print(
                    "Score absolute distance mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                    % (
                        np.mean(point_hit_diffs_np),
                        np.median(point_hit_diffs_np),
                        np.min(point_hit_diffs_np),
                        np.max(point_hit_diffs_np),
                        np.percentile(point_hit_diffs_np, 99),
                    )
                )
            else:
                print("point_hit_diffs_np has size zero.")

            point_offset_diffs_np = np.array(point_offset_diffs)
            if point_offset_diffs_np.size != 0:
                print(
                    "Offset absolute distance mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                    % (
                        np.mean(point_offset_diffs_np),
                        np.median(point_offset_diffs_np),
                        np.min(point_offset_diffs_np),
                        np.max(point_offset_diffs_np),
                        np.percentile(point_offset_diffs_np, 99),
                    )
                )
            else:
                print("point_offset_diffs_np has size zero.")

            point_size_diffs_np = np.array(point_size_diffs)
            if point_size_diffs_np.size != 0:
                print(
                    "Size absolute distance mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                    % (
                        np.mean(point_size_diffs_np),
                        np.median(point_size_diffs_np),
                        np.min(point_size_diffs_np),
                        np.max(point_size_diffs_np),
                        np.percentile(point_size_diffs_np, 99),
                    )
                )
            else:
                print("point_size_diffs_np has size zero.")

            point_category_diffs_np = np.array(point_category_diffs)
            if point_category_diffs_np.size != 0:
                print(
                    "Category absolute distance mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                    % (
                        np.mean(point_category_diffs_np),
                        np.median(point_category_diffs_np),
                        np.min(point_category_diffs_np),
                        np.max(point_category_diffs_np),
                        np.percentile(point_category_diffs_np, 99),
                    )
                )
            else:
                print("point_category_diffs_np has size zero.")

            mask_diffs_np = np.array(mask_diffs)
            if mask_diffs_np.size != 0:
                print(
                    "Mask absolute distance mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                    % (
                        np.mean(mask_diffs_np),
                        np.median(mask_diffs_np),
                        np.min(mask_diffs_np),
                        np.max(mask_diffs_np),
                        np.percentile(mask_diffs_np, 99),
                    )
                )
            else:
                print("mask_diffs_np has size zero.")

            embedding_sims_np = np.array(embedding_sims)
            if embedding_sims_np.size != 0:
                print(
                    "Embedding cosine similarity mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                    % (
                        np.mean(embedding_sims_np),
                        np.median(embedding_sims_np),
                        np.min(embedding_sims_np),
                        np.max(embedding_sims_np),
                        np.percentile(embedding_sims_np, 99),
                    )
                )
            else:
                print("embedding_sims_np has size zero.")

        if calibration_only:
            return None

        # Save the resulting converted model.
        torch_dtype = torch.float16 if fp16 else torch.float32
        metadata = self._ckpt_metadata.with_input_dtype(torch_dtype).with_max_batch_size(max_batch_size)
        save_tensorrt_model(trt_model, metadata, save_to)

        if error_metrics and self._test_dataset is not None and config is not None:
            num_iters = (
                num_evaluation_samples if num_evaluation_samples <= len(self._test_dataset) else len(self._test_dataset)
            )

            world_size = torch.cuda.device_count()
            with multiprocessing.get_context("spawn").Pool(world_size) as pool:
                futures = []
                for rank in range(world_size):
                    futures.append(
                        pool.apply_async(
                            self.evaluate_on_test,
                            kwds={
                                "trt_path": save_to,
                                "config": config,
                                "dataset": self._test_dataset,
                                "ckpt_metadata": self._ckpt_metadata,
                                "max_batch_size": max_batch_size,
                                "rank": rank,
                                "world_size": world_size,
                                "num_iters": num_iters,
                                "fp16": fp16,
                                "enable_crop_embeddings": self._enable_crop_embeddings,
                            },
                        )
                    )

                all_metrics: Dict[str, Any] = {}
                for i, future in enumerate(futures):
                    metric_dict = future.get()
                    if metric_dict is not None:
                        print(f"Rank {i} processed")
                        for k, v in metric_dict.items():
                            if k in all_metrics:
                                all_metrics[k] += v
                            else:
                                all_metrics[k] = v

            for k, v in all_metrics.items():
                all_metrics[k] = all_metrics[k] / len(futures)
                if np.isnan(all_metrics[k]):
                    all_metrics[k] = 0
                print(f"{k}: {all_metrics[k]:.5f}")

            return all_metrics

        return None


if __name__ == "__main__":
    fire.Fire(TrtConvert)
