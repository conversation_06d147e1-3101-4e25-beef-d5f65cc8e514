#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <torch/extension.h>
#include <torch/torch.h>

#include "cv/encoder/feature_extractor.h"

namespace py = pybind11;

namespace cv {
namespace encoder {

PYBIND11_MODULE(encoder_python, m) {
  py::module::import("lib.common.model.cpp.model_python");

  py::class_<encoder::FeatureExtractor>(m, "FeatureExtractor")
      .def(py::init<lib::common::model::AtomicModel>(), py::call_guard<py::gil_scoped_release>())
      .def("infer", &encoder::FeatureExtractor::infer, py::arg("image"), py::call_guard<py::gil_scoped_release>())
      .def("get_input_size", &encoder::FeatureExtractor::get_input_size, py::call_guard<py::gil_scoped_release>());
}

} // namespace encoder
} // namespace cv