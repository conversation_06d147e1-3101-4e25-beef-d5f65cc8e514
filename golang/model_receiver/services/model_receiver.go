package services

import (
	"context"
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	"github.com/carbonrobotics/robot/golang/model_receiver/state"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type ModelReceiverService struct {
	model_receiver.UnimplementedModelReceiverServer
	modelStatus *state.ModelStatus
	chipStatus  *state.ChipStatus

	triggerChannel     chan bool
	triggerChipChannel chan bool
}

func NewModelReceiverService(grpcServer *grpc.Server, modelStatus *state.ModelStatus, chipStatus *state.ChipStatus) *ModelReceiverService {
	service := &ModelReceiverService{
		triggerChannel: make(chan bool, 10),
		modelStatus:    modelStatus,
		chipStatus:     chipStatus,
	}
	model_receiver.RegisterModelReceiverServer(grpcServer, service)
	return service
}

func (m *ModelReceiverService) TriggerChannel() <-chan bool {
	return m.triggerChannel
}

func (m *ModelReceiverService) ChipTriggerChannel() <-chan bool {
	return m.triggerChannel
}

func (m *ModelReceiverService) Trigger() {
	select {
	case m.triggerChannel <- true:
	default: // no block
	}
}

func (m *ModelReceiverService) DownloadModelArtifact(_ context.Context, req *model_receiver.DownloadModelArtifactRequest) (*model_receiver.Empty, error) {
	modelID := req.GetModelId()
	logrus.Infof("DownloadModelArtifact invoked, receiving model: %s", modelID)

	empty := &model_receiver.Empty{}
	if err := m.modelStatus.WriteModelArtifact(modelID, req.ArtifactName, req.ArtifactContents); err != nil {
		return empty, err
	}

	m.Trigger()
	return empty, nil
}

func (m *ModelReceiverService) DownloadModelMetadata(_ context.Context, req *model_receiver.DownloadModelMetadataRequest) (*model_receiver.Empty, error) {
	modelID := req.GetModelId()
	logrus.Infof("DownloadModelMetadata invoked, receiving model: %s", modelID)

	empty := &model_receiver.Empty{}
	if err := m.modelStatus.WriteModelMetadata(modelID, req.MetadataContents); err != nil {
		return empty, err
	}

	m.Trigger()
	return empty, nil
}

func (m *ModelReceiverService) GetDownloadedModels(_ context.Context, _ *model_receiver.Empty) (*model_receiver.DownloadedModelResponse, error) {
	logrus.Debugln("GetDownloadedModels invoked") // this one is noisy
	response := &model_receiver.DownloadedModelResponse{}

	for _, model := range m.modelStatus.ListModels() {
		artifactIDs := make([]string, 0, len(model.ModelArtifacts))
		for _, artifact := range model.ModelArtifacts {
			artifactIDs = append(artifactIDs, artifact.ModelManagerArtifactID())
		}
		response.Models = append(response.Models, &model_receiver.Model{Id: model.ID, ArtifactIds: artifactIDs})
	}

	return response, nil
}

func (m *ModelReceiverService) CleanupModels(_ context.Context, req *model_receiver.CleanupModelsRequest) (*model_receiver.Empty, error) {
	logrus.Infoln("CleanupModels invoked modelIDs:", req.GetModelId())
	if err := m.modelStatus.CleanupModels(req.GetModelId()); err != nil {
		return nil, fmt.Errorf("failed to clean models: %w", err)
	}
	m.Trigger()
	return &model_receiver.Empty{}, nil
}

func (m *ModelReceiverService) TriggerChip() {
	select {
	case m.triggerChipChannel <- true:
	default: // no block
	}
}

func (m *ModelReceiverService) DownloadChip(_ context.Context, req *model_receiver.DownloadChipRequest) (*model_receiver.Empty, error) {
	chipId := req.GetChipId()
	logrus.Infof("DownloadChip invoked, receiving chip: %s", chipId)

	empty := &model_receiver.Empty{}
	if err := m.chipStatus.WriteChip(chipId, req.Contents); err != nil {
		return empty, err
	}

	m.TriggerChip()
	return empty, nil
}

func (m *ModelReceiverService) DownloadChipMetadata(_ context.Context, req *model_receiver.DownloadChipMetadataRequest) (*model_receiver.Empty, error) {
	chipId := req.GetChipId()
	logrus.Infof("DownloadChipMetadata invoked, receiving chip Id: %s", chipId)

	empty := &model_receiver.Empty{}
	if err := m.chipStatus.WriteChipMetadata(chipId, req.MetadataContents); err != nil {
		return empty, err
	}

	m.TriggerChip()
	return empty, nil
}

func (m *ModelReceiverService) GetDownloadedChips(_ context.Context, _ *model_receiver.Empty) (*model_receiver.GetDownloadedChipsResponse, error) {
	logrus.Debugln("GetDownloadedChips invoked")
	response := &model_receiver.GetDownloadedChipsResponse{}

	for _, chip := range m.chipStatus.ListChips() {
		response.Chips = append(response.Chips, &model_receiver.Chip{Id: chip.Id})
	}

	return response, nil
}

func (m *ModelReceiverService) RemoveChips(_ context.Context, req *model_receiver.RemoveChipsRequest) (*model_receiver.Empty, error) {
	chipIds := req.GetChipId()
	logrus.Infoln("RemoveChips invoked with chipIds:", chipIds)
	if err := m.chipStatus.RemoveChips(chipIds); err != nil {
		return nil, fmt.Errorf("failed to remove chips: %w", err)
	}
	return &model_receiver.Empty{}, nil
}
