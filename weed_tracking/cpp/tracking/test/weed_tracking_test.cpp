#include "weed_tracking/cpp/tracking/weed_tracker.h"
#include "gtest/gtest.h"
#include <chrono>
#include <config/client/cpp/config_subscriber.hpp>
#include <lib/common/geometric/cpp/global_height_estimator_collection.hpp>
#include <lib/drivers/nanopb/nofx_board/cpp/wheel_encoder.hpp>
#include <stdlib.h>
#include <thread>
#include <wheel_encoder/cpp/wheel_encoder.hpp>

#include "test/mocks/lib/common/redis/mock_redis_client.hpp"
#include "weed_tracking/cpp/ingest_clients/banding.hpp"
#include <spdlog/spdlog.h>
#include <weed_tracking/cpp/recording/deepweed_recorder.hpp>
#include <generated/recorder/proto/recorder.pb.h>

using ::testing::_;

class WeedTrackingTest : public ::testing::Test {
public:
  static void SetUpTestSuite() {
    carbon::config::get_global_config_subscriber()->add_config_tree(
        "aimbot", carbon::config::get_computer_config_prefix() + "/aimbot", "services/aimbot.yaml");
    carbon::config::get_global_config_subscriber()->add_config_tree("common", "common", "services/common.yaml");
    carbon::config::get_global_config_subscriber()->add_config_tree(
        "cv", carbon::config::get_computer_config_prefix() + "/cv", "services/cv.yaml");

    carbon::wheel_encoder::WheelEncoder::set(
        std::make_shared<carbon::nanopb::nofx_board::WheelEncoderNOFXSim>(false, 100, 100));

    // Banding disabled.
    auto mock_redis_client = std::make_unique<lib::common::MockRedisClient>();
    EXPECT_CALL(*mock_redis_client, get_long_def("banding/enabled", 0)).WillRepeatedly(::testing::Return(0));
    EXPECT_CALL(*mock_redis_client, get("banding/active_def_uuid"))
        .WillRepeatedly(::testing::Return(sw::redis::OptionalString()));
    EXPECT_CALL(*mock_redis_client, hget("banding/banding_defs_uuid", _))
        .WillRepeatedly(::testing::Return(sw::redis::OptionalString()));
    EXPECT_CALL(*mock_redis_client, get_long_def("banding/dynamic_banding_selected", 0))
        .WillRepeatedly(::testing::Return(0));
    testing::Mock::AllowLeak(mock_redis_client.get());
    carbon::weed_tracking::Banding::instance(
        std::make_unique<carbon::weed_tracking::Banding>(std::move(mock_redis_client)));
  }

  static void TearDownTestSuite() {
    lib::common::bot::BotStopHandler::get().stop();
    lib::common::bot::BotStopHandler::get().wait();
  }
};

std::shared_ptr<lib::common::geometric::GeometricCam> get_geo_cam() {
  auto cam_name = std::string("predict1");
  auto abs_position_mm = lib::common::geometric::Tuple3d(0.0, 0.0, 0.0);
  auto image_size = std::tuple<uint32_t, uint32_t>(3000, 4096);
  auto sensor_size = std::tuple<double, double>(10.3776, 14.1864);
  double focal_length = 16;
  double top_to_vertex = -23.292;
  return std::make_shared<lib::common::geometric::GeometricCam>(
      cam_name, abs_position_mm, image_size, focal_length, top_to_vertex, sensor_size,
      torch::arange((int)std::get<0>(image_size)).repeat({(int)std::get<0>(image_size), 1}),
      torch::arange((int)std::get<1>(image_size))
          .repeat({1, 1})
          .transpose(1, 0)
          .repeat({1, (int)std::get<1>(image_size)}),
      torch::arange((int)std::get<0>(image_size)).repeat({(int)std::get<0>(image_size), 1}),
      torch::arange((int)std::get<1>(image_size))
          .repeat({1, 1})
          .transpose(1, 0)
          .repeat({1, (int)std::get<1>(image_size)}));
}

std::shared_ptr<carbon::weed_tracking::WeedTracker>
get_test_weed_tracker(std::shared_ptr<lib::common::geometric::GeometricCam> predict_geo_cam) {
  return std::make_shared<carbon::weed_tracking::WeedTracker>("predict1", 1, predict_geo_cam, 10000,
                                                              std::get<0>(predict_geo_cam->resolution()),
                                                              std::get<1>(predict_geo_cam->resolution()), 5000, 10000);
}

int64_t constexpr max_time_between = 1500;
inline cv::deepweed::DeepweedOutput form_deepweed_output(const std::vector<std::tuple<float, float>> &weeds,
                                                         const std::vector<std::tuple<float, float>> &crops,
                                                         int64_t timestamp_ms, int64_t drop_n_of_100 = 0,
                                                         int64_t jitter_n_dw_predictions_of_100 = 0) {
  cv::deepweed::DeepweedOutput deepweed_output;
  deepweed_output.timestamp_ms = timestamp_ms;
  deepweed_output.weed_detection_classes = {"BROADLEAF"};
  size_t i = 0;
  for (const auto &weed : weeds) {
    if (rand() % 100 >= drop_n_of_100) {
      float x_jitter = 0.0f;
      float y_jitter = 0.0f;
      if (rand() % 100 < jitter_n_dw_predictions_of_100) {
        x_jitter = (float)(rand() % 32) - 16;
        y_jitter = (float)(rand() % 32) - 16;
      }

      cv::deepweed::DeepweedDetection dw_detection;
      dw_detection.x = (float)std::get<0>(weed) + x_jitter;
      dw_detection.y = (float)std::get<1>(weed) + y_jitter;
      dw_detection.hit_class = cv::runtime::proto::HitClass::WEED;

      dw_detection.weed_detection_class_scores.push_back(1.0f);
      dw_detection.score = 1.0f;
      dw_detection.weed_score = 1.0f;
      dw_detection.crop_score = 0.0f;
      dw_detection.plant_score = 1.0f;
      dw_detection.size = 40;

      deepweed_output.detections.push_back(dw_detection);
      i += 1;
    }
  }

  for (const auto &crop : crops) {
    if (rand() % 100 >= drop_n_of_100) {
      float x_jitter = 0.0f;
      float y_jitter = 0.0f;
      if (rand() % 100 < jitter_n_dw_predictions_of_100) {
        x_jitter = (float)(rand() % 32) - 16;
        y_jitter = (float)(rand() % 32) - 16;
      }
      cv::deepweed::DeepweedDetection dw_detection;
      dw_detection.x = (float)std::get<0>(crop) + x_jitter;
      dw_detection.y = (float)std::get<1>(crop) + y_jitter;
      dw_detection.hit_class = cv::runtime::proto::HitClass::CROP;
      dw_detection.weed_detection_class_scores.push_back(1.0f);
      dw_detection.score = 1.0f;
      dw_detection.weed_score = 0.0f;
      dw_detection.crop_score = 1.0f;
      dw_detection.plant_score = 1.0f;
      dw_detection.size = 40;

      deepweed_output.detections.push_back(dw_detection);
      i += 1;
    }
  }

  return deepweed_output;
}

TEST_F(WeedTrackingTest, HappyPath) {
  float eps = 1;
  auto predict_geo_cam = get_geo_cam();
  auto weed_tracker = get_test_weed_tracker(predict_geo_cam);
  lib::common::geometric::GlobalHeightEstimatorCollection::instance().get_height_estimator()->toggle_estimation(true);

  float mm_per_s = 500.0f;
  int update_interval_ms = 100;
  float mm_per_update = mm_per_s / 1000.0f * (float)update_interval_ms;

  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  weed_tracker->add_rotary_ticks(clock.timestamp_ms(), mm_per_update);

  // Roll up some rotary ticks
  for (int i = 0; i < 5; i++) {
    clock += 100;
    weed_tracker->add_rotary_ticks(clock.timestamp_ms(), mm_per_update);
  }

  {
    // First two weeds appear
    std::tuple<float, float> initial_weed_1_px = std::tuple<float, float>(300.0f, 100.0f);
    std::tuple<float, float> initial_weed_2_px = std::tuple<float, float>(1000.0f, 200.0f);
    std::vector<std::tuple<float, float>> weeds{initial_weed_1_px, initial_weed_2_px};
    std::vector<std::tuple<float, float>> crops;

    auto deepweed_output = form_deepweed_output(weeds, crops, clock.timestamp_ms() - 200);

    std::vector<::recorder::TrackedItem> recorded_tracked_items;
    std::map<size_t, std::pair<float, float>> model_undistorted_coordinates;
    ::recorder::CandidateShift best_candidate_match;
    std::vector<::recorder::CandidateShift> all_candidate_matches;
    std::unordered_set<size_t> centroids_used_in_dedup;
    auto [number_deduped_plants_1, number_unmatched_plants_1, number_undeduped_plants_1, model_to_track, plant_matcher_valid] =
        weed_tracker->add_deepweed(deepweed_output, recorded_tracked_items, model_undistorted_coordinates, best_candidate_match, all_candidate_matches, centroids_used_in_dedup);

    ASSERT_EQ(number_deduped_plants_1, 0);
    ASSERT_EQ(number_unmatched_plants_1, 2);
    ASSERT_EQ(number_undeduped_plants_1, 0);

    // Check that two items now exist at their proper locations, ie approx same x/z and different y
    auto latest_weeds_output = weed_tracker->get_latest_weeds();
    auto trajectories = latest_weeds_output.get_trajectories();
    ASSERT_TRUE(trajectories.size() == 2);

    for (auto &trajectory : trajectories) {
      ASSERT_TRUE(trajectory->id() == 1 || trajectory->id() == 2);
      auto last_item = trajectory->get_last_item();
      auto [position_px_x, position_px_y] = predict_geo_cam->get_undistorted_px_from_abs_position(
          std::make_tuple(last_item.get_x_mm(), last_item.get_y_mm(), last_item.get_z_mm()));
      ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::WEED);
      if (trajectory->id() == 1) {
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(initial_weed_1_px)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 618.415) < eps);
      } else if (trajectory->id() == 2) {
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(initial_weed_2_px)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 718.415) < eps);
      }
    }
  }

  // Roll up some rotary ticks
  for (int i = 0; i < 2; i++) {
    clock += 100;
    weed_tracker->add_rotary_ticks(clock.timestamp_ms(), mm_per_update);
  }

  {
    // Three items appear (two to be deduplicated, one to be added)
    std::tuple<float, float> expected_dedup_weed_1 = std::tuple<float, float>(310.0f, 620.0f);
    std::tuple<float, float> expected_dedup_weed_2 = std::tuple<float, float>(1005.0f, 720.0f);
    std::tuple<float, float> initial_weed_3 = std::tuple<float, float>(500.0f, 200.0f);

    std::vector<std::tuple<float, float>> weeds{expected_dedup_weed_1, expected_dedup_weed_2, initial_weed_3};
    std::vector<std::tuple<float, float>> crops;

    auto deepweed_output = form_deepweed_output(weeds, crops, clock.timestamp_ms() - 200);

    std::vector<::recorder::TrackedItem> recorded_tracked_items;
    std::map<size_t, std::pair<float, float>> model_undistorted_coordinates;
    ::recorder::CandidateShift best_candidate_match;
    std::vector<::recorder::CandidateShift> all_candidate_matches;
    std::unordered_set<size_t> centroids_used_in_dedup;
    auto [number_deduped_plants, number_unmatched_plants, number_undeduped_plants, model_to_track, plant_matcher_valid] =
        weed_tracker->add_deepweed(deepweed_output, recorded_tracked_items, model_undistorted_coordinates, best_candidate_match, all_candidate_matches, centroids_used_in_dedup);

    ASSERT_EQ(number_deduped_plants, 2);
    ASSERT_EQ(number_unmatched_plants, 1);
    ASSERT_EQ(number_undeduped_plants, 0);

    // Check that the three items are in the proper locations. We expect that the first two items are deduplicated and
    // the last item is added to the tracker.
    auto latest_weeds_output = weed_tracker->get_latest_weeds();
    auto trajectories = latest_weeds_output.get_trajectories();
    ASSERT_EQ(trajectories.size(), 3);

    for (auto trajectory : trajectories) {
      ASSERT_TRUE(trajectory->id() == 1 || trajectory->id() == 2 || trajectory->id() == 3);
      auto last_item = trajectory->get_last_item();
      auto [position_px_x, position_px_y] = predict_geo_cam->get_undistorted_px_from_abs_position(
          std::make_tuple(last_item.get_x_mm(), last_item.get_y_mm(), last_item.get_z_mm()));
      ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::WEED);
      if (trajectory->id() == 1) {
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(expected_dedup_weed_1)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 1140.1f) < eps);
      } else if (trajectory->id() == 2) {
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(expected_dedup_weed_2)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 1240.02f) < eps);
      } else if (trajectory->id() == 3) {
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(initial_weed_3)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 719.249f) < eps);
      }
    }
  }

  // Roll up some rotary ticks
  for (int i = 0; i < 2; i++) {
    clock += 100;
    weed_tracker->add_rotary_ticks(clock.timestamp_ms(), mm_per_update);
  }

  {
    // Three items appear (two weeds to be deduplicated, one crop to be added, one weed to be unmatched)
    std::tuple<float, float> expected_dedup_weed_2 = std::tuple<float, float>(1013.0f, 1245.1f);
    std::tuple<float, float> expected_dedup_weed_3 = std::tuple<float, float>(502.0f, 705.1f);
    std::tuple<float, float> initial_crop_1 = std::tuple<float, float>(600.0f, 1140.1f);

    std::vector<std::tuple<float, float>> weeds{expected_dedup_weed_2, expected_dedup_weed_3};
    std::vector<std::tuple<float, float>> crops{initial_crop_1};

    auto deepweed_output = form_deepweed_output(weeds, crops, clock.timestamp_ms() - 200);

    std::vector<::recorder::TrackedItem> recorded_tracked_items;
    std::map<size_t, std::pair<float, float>> model_undistorted_coordinates;
    ::recorder::CandidateShift best_candidate_match;
    std::vector<::recorder::CandidateShift> all_candidate_matches;
    std::unordered_set<size_t> centroids_used_in_dedup;
    auto [number_deduped_plants, number_unmatched_plants, number_undeduped_plants, model_to_track, plant_matcher_valid] =
        weed_tracker->add_deepweed(deepweed_output, recorded_tracked_items, model_undistorted_coordinates, best_candidate_match, all_candidate_matches, centroids_used_in_dedup);

    ASSERT_EQ(number_deduped_plants, 2);
    ASSERT_EQ(number_unmatched_plants, 1);
    ASSERT_EQ(number_undeduped_plants, 1);

    auto latest_weeds_output = weed_tracker->get_latest_weeds();
    auto trajectories = latest_weeds_output.get_trajectories();
    ASSERT_EQ(trajectories.size(), 4);

    for (auto trajectory : trajectories) {
      ASSERT_TRUE(trajectory->id() == 1 || trajectory->id() == 2 || trajectory->id() == 3 || trajectory->id() == 4);
      auto last_item = trajectory->get_last_item();
      auto [position_px_x, position_px_y] = predict_geo_cam->get_undistorted_px_from_abs_position(
          std::make_tuple(last_item.get_x_mm(), last_item.get_y_mm(), last_item.get_z_mm()));
      if (trajectory->id() == 1) {
        ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::WEED);
        ASSERT_TRUE(std::abs(position_px_x - 310.0f) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 1660.19f) < eps);
      } else if (trajectory->id() == 2) {
        ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::WEED);
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(expected_dedup_weed_2)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 1767.54f) < eps);
      } else if (trajectory->id() == 3) {
        ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::WEED);
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(expected_dedup_weed_3)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 1210.0f) < eps);
      } else if (trajectory->id() == 4) {
        ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::CROP);
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(initial_crop_1)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 1658.41));
      }
    }
  }

  // Roll up some rotary ticks but stretch time out so one of the weeds disappears
  for (int i = 0; i < 9; i++) {
    clock += 100;
    weed_tracker->add_rotary_ticks(clock.timestamp_ms(), mm_per_update / 9);
  }

  // Move time forward so that the first weed disappears
  clock += 3000;
  spdlog::info("Manual TS: {}", clock.timestamp_ms());

  {
    // Three items appear, one should time out now so there should only be three items
    std::tuple<float, float> expected_dedup_weed_2 = std::tuple<float, float>(1020.0f, 1775.1f);
    std::tuple<float, float> expected_dedup_weed_3 = std::tuple<float, float>(495.0f, 1220.1f);
    std::tuple<float, float> expected_dedup_crop_1 = std::tuple<float, float>(606.0f, 1670.1f);

    std::vector<std::tuple<float, float>> weeds{expected_dedup_weed_2, expected_dedup_weed_3};
    std::vector<std::tuple<float, float>> crops{expected_dedup_crop_1};

    auto deepweed_output = form_deepweed_output(weeds, crops, clock.timestamp_ms() - 3900);

    std::vector<::recorder::TrackedItem> recorded_tracked_items;
    std::map<size_t, std::pair<float, float>> model_undistorted_coordinates;
    ::recorder::CandidateShift best_candidate_match;
    std::vector<::recorder::CandidateShift> all_candidate_matches;
    std::unordered_set<size_t> centroids_used_in_dedup;
    auto [number_deduped_plants, number_unmatched_plants, number_undeduped_plants, model_to_track, plant_matcher_valid] =
        weed_tracker->add_deepweed(deepweed_output, recorded_tracked_items, model_undistorted_coordinates, best_candidate_match, all_candidate_matches, centroids_used_in_dedup);

    ASSERT_EQ(number_deduped_plants, 3);
    ASSERT_EQ(number_unmatched_plants, 0);
    ASSERT_EQ(number_undeduped_plants, 1);

    auto latest_weeds_output = weed_tracker->get_latest_weeds();
    auto trajectories = latest_weeds_output.get_trajectories();
    ASSERT_EQ(trajectories.size(), 3);

    for (auto trajectory : trajectories) {
      ASSERT_TRUE(trajectory->id() == 2 || trajectory->id() == 3 || trajectory->id() == 4);
      auto last_item = trajectory->get_last_item();
      auto [position_px_x, position_px_y] = predict_geo_cam->get_undistorted_px_from_abs_position(
          std::make_tuple(last_item.get_x_mm(), last_item.get_y_mm(), last_item.get_z_mm()));
      if (trajectory->id() == 2) {
        ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::WEED);
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(expected_dedup_weed_2)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 2037.52f) < eps);
      } else if (trajectory->id() == 3) {
        ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::WEED);
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(expected_dedup_weed_3)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 1475.0f) < eps);
      } else if (trajectory->id() == 4) {
        ASSERT_EQ(last_item.get_hit_class(), cv::runtime::proto::HitClass::CROP);
        ASSERT_TRUE(std::abs(position_px_x - std::get<0>(expected_dedup_crop_1)) < eps);
        ASSERT_TRUE(std::abs(position_px_y - 1935.07f) < eps);
      }
    }
  }
}

inline void jitter_test(const std::string &fname, float jitter_amount, float padding_px = 100.0f,
                        int64_t drop_n_of_100 = 0, int jitter_n_dw_predictions_of_100 = 0,
                        float second_row_y_shift = 100.0f) {
  std::shared_ptr<carbon::recorder::ProtobufStreamFileWriter> writter(nullptr);
  if (!fname.empty()) {
    writter = std::make_shared<carbon::recorder::ProtobufStreamFileWriter>("DeepweedPredictionRecord",
                                                                           fmt::format("/data/{}", fname));
  }
  carbon::weed_tracking::PostDedupFrame output;
  auto predict_geo_cam = get_geo_cam();
  auto weed_tracker = get_test_weed_tracker(predict_geo_cam);
  lib::common::geometric::GlobalHeightEstimatorCollection::instance().get_height_estimator()->toggle_estimation(true);

  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  std::vector<std::tuple<float, float>> weeds;
  std::vector<std::tuple<float, float>> crops;

  for (float i = 0; i < 50; i++) {
    float j = (float)((float)(rand() % 50) + padding_px);
    weeds.push_back(std::tuple<float, float>(50.0f * i + padding_px, j));
  }

  for (float i = 0; i < 50; i++) {
    float j = (float)((float)(rand() % 50) + padding_px + second_row_y_shift);
    weeds.push_back(std::tuple<float, float>(50.0f * i + padding_px, j));
  }

  for (float i = 0; i < 50; i++) {
    float j = (float)((float)(rand() % 50) + padding_px);
    crops.push_back(std::tuple<float, float>(50.0f * i + padding_px + 20, j));
  }

  for (float i = 0; i < 50; i++) {
    float j = (float)((float)(rand() % 50) + padding_px + second_row_y_shift);
    crops.push_back(std::tuple<float, float>(50.0f * i + padding_px + 20, j));
  }

  // Roll up some rotary ticks
  for (int i = 0; i < 10; i++) {
    clock += 50;
    weed_tracker->add_rotary_ticks(clock.timestamp_ms(), 0);
  }
  {
    auto deepweed_output = form_deepweed_output(weeds, crops, clock.timestamp_ms() - 200, 0, 0);
    std::vector<::recorder::TrackedItem> recorded_tracked_items;
    std::map<size_t, std::pair<float, float>> model_undistorted_coordinates;
    ::recorder::CandidateShift best_candidate_match;
    std::vector<::recorder::CandidateShift> all_candidate_matches;
    std::unordered_set<size_t> centroids_used_in_dedup;
    auto response = weed_tracker->add_deepweed(deepweed_output, recorded_tracked_items, model_undistorted_coordinates, best_candidate_match, all_candidate_matches, centroids_used_in_dedup);
  }

  // Loop back and forth with random jittering and deepweed additions/subtractions
  for (int i = 0; i < 100; i++) {
    for (int j = 0; j < 4; j++) {
      clock += 50;
      auto random_int = rand() % 3;
      float mult = 0.0f;
      if (random_int == 0) {
        mult = 1.0f;
      } else if (random_int == 1) {
        mult = -1.0f;
      }
      auto mm_movement = mult * jitter_amount;
      weed_tracker->add_rotary_ticks(clock.timestamp_ms(), mm_movement); // back and forth up to 3 mm
    }

    auto dw_output =
        form_deepweed_output(weeds, crops, clock.timestamp_ms() - 200, drop_n_of_100, jitter_n_dw_predictions_of_100);
    std::vector<::recorder::TrackedItem> recorded_tracked_items;
    std::map<size_t, std::pair<float, float>> model_undistorted_coordinates;
    ::recorder::CandidateShift best_candidate_match;
    std::vector<::recorder::CandidateShift> all_candidate_matches;
    std::unordered_set<size_t> centroids_used_in_dedup;
    auto response = weed_tracker->add_deepweed(dw_output, recorded_tracked_items, model_undistorted_coordinates, best_candidate_match, all_candidate_matches, centroids_used_in_dedup);
    auto latest_weeds_output = weed_tracker->get_latest_weeds();
    auto trajectories = latest_weeds_output.get_trajectories();
    if (writter) {
      output.receive_ts_ms = dw_output.timestamp_ms;
      output.dw_output.detections = dw_output.detections;
      output.dw_output.timestamp_ms = dw_output.timestamp_ms;
      output.dw_to_track = std::get<3>(response);
      auto record = carbon::weed_tracking::DeepweedRecorder::build_record(output);
      writter->append(*record.get());
    }
    if (trajectories.size() != 200) {
      for (auto &pair : std::get<3>(response)) {
        spdlog::warn("Model {} -> Track {}", pair.first, pair.second);
      }
    }
    ASSERT_EQ(trajectories.size(), 200);
  }

  auto latest_weeds_output = weed_tracker->get_latest_weeds();
  auto trajectories = latest_weeds_output.get_trajectories();
  size_t weed_count = 0;
  size_t crop_count = 0;
  for (auto trajectory : trajectories) {
    auto last_item = trajectory->get_last_item();
    if (last_item.get_hit_class() == cv::runtime::proto::HitClass::WEED) {
      weed_count += 1;
    } else {
      crop_count += 1;
    }
  }

  ASSERT_EQ(weed_count, 100);
  ASSERT_EQ(crop_count, 100);
  ASSERT_EQ(trajectories.size(), 200);
}

TEST_F(WeedTrackingTest, Stationary) { jitter_test("", 0, 100.0f, 0); }

TEST_F(WeedTrackingTest, BackAndForth2mm) { jitter_test("", 2, 100.0f, 0); }

TEST_F(WeedTrackingTest, StationaryDWFlicker) { jitter_test("", 0, 100.0f, 10); }

TEST_F(WeedTrackingTest, DISABLED_StationaryDWFlickerAndJitter) { jitter_test("", 0, 100.0f, 5, 5); }

TEST_F(WeedTrackingTest, DISABLED_StationaryDWFlickerAndJitterSuperDense) { jitter_test("", 0, 100.0f, 5, 5, 25.0f); }
