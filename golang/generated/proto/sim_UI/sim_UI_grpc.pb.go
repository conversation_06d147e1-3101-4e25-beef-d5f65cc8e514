// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: golang/simulator/hardware/proto/sim_UI/sim_UI.proto

package sim_UI

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SimulatorUIService_GetVelocityStream_FullMethodName     = "/carbon.simulator_UI.SimulatorUIService/GetVelocityStream"
	SimulatorUIService_SetVelocity_FullMethodName           = "/carbon.simulator_UI.SimulatorUIService/SetVelocity"
	SimulatorUIService_GetPresetStream_FullMethodName       = "/carbon.simulator_UI.SimulatorUIService/GetPresetStream"
	SimulatorUIService_SetPreset_FullMethodName             = "/carbon.simulator_UI.SimulatorUIService/SetPreset"
	SimulatorUIService_GetDiagnosticSettings_FullMethodName = "/carbon.simulator_UI.SimulatorUIService/GetDiagnosticSettings"
	SimulatorUIService_SetDiagnosticSettings_FullMethodName = "/carbon.simulator_UI.SimulatorUIService/SetDiagnosticSettings"
)

// SimulatorUIServiceClient is the client API for SimulatorUIService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SimulatorUIServiceClient interface {
	GetVelocityStream(ctx context.Context, in *GetVelocityRequest, opts ...grpc.CallOption) (SimulatorUIService_GetVelocityStreamClient, error)
	SetVelocity(ctx context.Context, in *SetVelocityRequest, opts ...grpc.CallOption) (*SetVelocityResponse, error)
	GetPresetStream(ctx context.Context, in *GetPresetsRequest, opts ...grpc.CallOption) (SimulatorUIService_GetPresetStreamClient, error)
	SetPreset(ctx context.Context, in *SetPresetRequest, opts ...grpc.CallOption) (*SetPresetResponse, error)
	GetDiagnosticSettings(ctx context.Context, in *GetDiagnosticSettingsRequest, opts ...grpc.CallOption) (*GetDiagnosticSettingsResponse, error)
	SetDiagnosticSettings(ctx context.Context, in *SetDiagnosticSettingsRequest, opts ...grpc.CallOption) (*SetDiagnosticSettingsResponse, error)
}

type simulatorUIServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSimulatorUIServiceClient(cc grpc.ClientConnInterface) SimulatorUIServiceClient {
	return &simulatorUIServiceClient{cc}
}

func (c *simulatorUIServiceClient) GetVelocityStream(ctx context.Context, in *GetVelocityRequest, opts ...grpc.CallOption) (SimulatorUIService_GetVelocityStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &SimulatorUIService_ServiceDesc.Streams[0], SimulatorUIService_GetVelocityStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &simulatorUIServiceGetVelocityStreamClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type SimulatorUIService_GetVelocityStreamClient interface {
	Recv() (*GetVelocityResponse, error)
	grpc.ClientStream
}

type simulatorUIServiceGetVelocityStreamClient struct {
	grpc.ClientStream
}

func (x *simulatorUIServiceGetVelocityStreamClient) Recv() (*GetVelocityResponse, error) {
	m := new(GetVelocityResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *simulatorUIServiceClient) SetVelocity(ctx context.Context, in *SetVelocityRequest, opts ...grpc.CallOption) (*SetVelocityResponse, error) {
	out := new(SetVelocityResponse)
	err := c.cc.Invoke(ctx, SimulatorUIService_SetVelocity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulatorUIServiceClient) GetPresetStream(ctx context.Context, in *GetPresetsRequest, opts ...grpc.CallOption) (SimulatorUIService_GetPresetStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &SimulatorUIService_ServiceDesc.Streams[1], SimulatorUIService_GetPresetStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &simulatorUIServiceGetPresetStreamClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type SimulatorUIService_GetPresetStreamClient interface {
	Recv() (*GetPresetsResponse, error)
	grpc.ClientStream
}

type simulatorUIServiceGetPresetStreamClient struct {
	grpc.ClientStream
}

func (x *simulatorUIServiceGetPresetStreamClient) Recv() (*GetPresetsResponse, error) {
	m := new(GetPresetsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *simulatorUIServiceClient) SetPreset(ctx context.Context, in *SetPresetRequest, opts ...grpc.CallOption) (*SetPresetResponse, error) {
	out := new(SetPresetResponse)
	err := c.cc.Invoke(ctx, SimulatorUIService_SetPreset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulatorUIServiceClient) GetDiagnosticSettings(ctx context.Context, in *GetDiagnosticSettingsRequest, opts ...grpc.CallOption) (*GetDiagnosticSettingsResponse, error) {
	out := new(GetDiagnosticSettingsResponse)
	err := c.cc.Invoke(ctx, SimulatorUIService_GetDiagnosticSettings_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulatorUIServiceClient) SetDiagnosticSettings(ctx context.Context, in *SetDiagnosticSettingsRequest, opts ...grpc.CallOption) (*SetDiagnosticSettingsResponse, error) {
	out := new(SetDiagnosticSettingsResponse)
	err := c.cc.Invoke(ctx, SimulatorUIService_SetDiagnosticSettings_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SimulatorUIServiceServer is the server API for SimulatorUIService service.
// All implementations must embed UnimplementedSimulatorUIServiceServer
// for forward compatibility
type SimulatorUIServiceServer interface {
	GetVelocityStream(*GetVelocityRequest, SimulatorUIService_GetVelocityStreamServer) error
	SetVelocity(context.Context, *SetVelocityRequest) (*SetVelocityResponse, error)
	GetPresetStream(*GetPresetsRequest, SimulatorUIService_GetPresetStreamServer) error
	SetPreset(context.Context, *SetPresetRequest) (*SetPresetResponse, error)
	GetDiagnosticSettings(context.Context, *GetDiagnosticSettingsRequest) (*GetDiagnosticSettingsResponse, error)
	SetDiagnosticSettings(context.Context, *SetDiagnosticSettingsRequest) (*SetDiagnosticSettingsResponse, error)
	mustEmbedUnimplementedSimulatorUIServiceServer()
}

// UnimplementedSimulatorUIServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSimulatorUIServiceServer struct {
}

func (UnimplementedSimulatorUIServiceServer) GetVelocityStream(*GetVelocityRequest, SimulatorUIService_GetVelocityStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method GetVelocityStream not implemented")
}
func (UnimplementedSimulatorUIServiceServer) SetVelocity(context.Context, *SetVelocityRequest) (*SetVelocityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetVelocity not implemented")
}
func (UnimplementedSimulatorUIServiceServer) GetPresetStream(*GetPresetsRequest, SimulatorUIService_GetPresetStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method GetPresetStream not implemented")
}
func (UnimplementedSimulatorUIServiceServer) SetPreset(context.Context, *SetPresetRequest) (*SetPresetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPreset not implemented")
}
func (UnimplementedSimulatorUIServiceServer) GetDiagnosticSettings(context.Context, *GetDiagnosticSettingsRequest) (*GetDiagnosticSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiagnosticSettings not implemented")
}
func (UnimplementedSimulatorUIServiceServer) SetDiagnosticSettings(context.Context, *SetDiagnosticSettingsRequest) (*SetDiagnosticSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDiagnosticSettings not implemented")
}
func (UnimplementedSimulatorUIServiceServer) mustEmbedUnimplementedSimulatorUIServiceServer() {}

// UnsafeSimulatorUIServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SimulatorUIServiceServer will
// result in compilation errors.
type UnsafeSimulatorUIServiceServer interface {
	mustEmbedUnimplementedSimulatorUIServiceServer()
}

func RegisterSimulatorUIServiceServer(s grpc.ServiceRegistrar, srv SimulatorUIServiceServer) {
	s.RegisterService(&SimulatorUIService_ServiceDesc, srv)
}

func _SimulatorUIService_GetVelocityStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetVelocityRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(SimulatorUIServiceServer).GetVelocityStream(m, &simulatorUIServiceGetVelocityStreamServer{stream})
}

type SimulatorUIService_GetVelocityStreamServer interface {
	Send(*GetVelocityResponse) error
	grpc.ServerStream
}

type simulatorUIServiceGetVelocityStreamServer struct {
	grpc.ServerStream
}

func (x *simulatorUIServiceGetVelocityStreamServer) Send(m *GetVelocityResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _SimulatorUIService_SetVelocity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetVelocityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulatorUIServiceServer).SetVelocity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulatorUIService_SetVelocity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulatorUIServiceServer).SetVelocity(ctx, req.(*SetVelocityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulatorUIService_GetPresetStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetPresetsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(SimulatorUIServiceServer).GetPresetStream(m, &simulatorUIServiceGetPresetStreamServer{stream})
}

type SimulatorUIService_GetPresetStreamServer interface {
	Send(*GetPresetsResponse) error
	grpc.ServerStream
}

type simulatorUIServiceGetPresetStreamServer struct {
	grpc.ServerStream
}

func (x *simulatorUIServiceGetPresetStreamServer) Send(m *GetPresetsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _SimulatorUIService_SetPreset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPresetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulatorUIServiceServer).SetPreset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulatorUIService_SetPreset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulatorUIServiceServer).SetPreset(ctx, req.(*SetPresetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulatorUIService_GetDiagnosticSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiagnosticSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulatorUIServiceServer).GetDiagnosticSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulatorUIService_GetDiagnosticSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulatorUIServiceServer).GetDiagnosticSettings(ctx, req.(*GetDiagnosticSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulatorUIService_SetDiagnosticSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDiagnosticSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulatorUIServiceServer).SetDiagnosticSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulatorUIService_SetDiagnosticSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulatorUIServiceServer).SetDiagnosticSettings(ctx, req.(*SetDiagnosticSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SimulatorUIService_ServiceDesc is the grpc.ServiceDesc for SimulatorUIService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SimulatorUIService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.simulator_UI.SimulatorUIService",
	HandlerType: (*SimulatorUIServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetVelocity",
			Handler:    _SimulatorUIService_SetVelocity_Handler,
		},
		{
			MethodName: "SetPreset",
			Handler:    _SimulatorUIService_SetPreset_Handler,
		},
		{
			MethodName: "GetDiagnosticSettings",
			Handler:    _SimulatorUIService_GetDiagnosticSettings_Handler,
		},
		{
			MethodName: "SetDiagnosticSettings",
			Handler:    _SimulatorUIService_SetDiagnosticSettings_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetVelocityStream",
			Handler:       _SimulatorUIService_GetVelocityStream_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetPresetStream",
			Handler:       _SimulatorUIService_GetPresetStream_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "golang/simulator/hardware/proto/sim_UI/sim_UI.proto",
}
