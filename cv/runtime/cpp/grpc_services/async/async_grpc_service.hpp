#pragma once

#include "cv/runtime/cpp/grpc_services/impl/cv_runtime_service_impl.hpp"
#include "cv/runtime/cpp/image_service.h"
#include "lib/common/cpp/logging_service.hpp"
#include "lib/common/cpp/utils/thread_safe_moving_average.hpp"

#include <atomic>
#include <future>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <prometheus/counter.h>
#include <prometheus/family.h>
#include <prometheus/gauge.h>
#include <prometheus/registry.h>
#include <thread>

namespace cv {
namespace runtime {

// Base class for all CallData objects
class CallData {
public:
  virtual ~CallData() = default;
  virtual void Proceed(bool) = 0;
};

class CVRuntimeServiceImplAsync final : public CVRuntimeServiceImplCommon {
public:
  CVRuntimeServiceImplAsync(
      std::shared_ptr<NodeRegistry> node_registry,
      std::shared_ptr<std::map<std::string, std::map<proto::BufferUseCase, std::string>>> camera_to_buffer,
      std::shared_ptr<std::map<std::string, lib::common::camera::Camera>> camera_id_to_camera,
      std::shared_ptr<std::map<std::string, bool>> camera_id_to_transpose,
      std::shared_ptr<std::map<std::string, std::pair<std::string, lib::common::camera::Camera>>> sim_cameras,
      std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry,
      std::shared_ptr<lib::common::GeoData> geo_data, std::shared_ptr<lib::common::ImplementStatus> implement_status,
      std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues,
      std::shared_ptr<carbon::config::ConfigTree> deepweed_config, std::string kLightweightBurstDirectory,
      std::string kFullBurstDirectory,
      const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues,
      std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>>>>
          deepweed_buffers,
      std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>>
          p2p_buffers,
      std::string role,
      std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
          distance_camera_buffer,
      std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> chip_score_queues,
      std::function<void()> reload_category_collection, std::shared_ptr<cv::embeddings::Manager> embeddings_manager,
      std::shared_ptr<prometheus::Registry> registry, std::shared_ptr<cv::runtime::ErrorState> error_state);

  ~CVRuntimeServiceImplAsync();

  void Build(grpc::ServerBuilder &builder) override;
  void Start() override;
  void Shutdown() override;
  void Check() override;
  void ReportMetrics() override;

  // Friend classes to allow CallData objects to access private members
  template <typename RequestType, typename ResponseType, typename Derived>
  friend class AsyncCallData;
  friend class GetBootedCallData;
  friend class GetReadyCallData;
  friend class GetLatestImageCallData;
  friend class GetImageNearTimestampCallData;
  friend class SetImageScoreCallData;
  friend class GetCameraInfoCallData;
  friend class PredictCallData;
  friend class LoadAndQueueCallData;
  friend class SetImageCallData;
  friend class UnsetImageCallData;
  friend class GetCameraDimensionsCallData;
  friend class GetScoreQueueCallData;
  friend class ListScoreQueuesCallData;
  friend class GetMaxImageScoreCallData;
  friend class GetMaxScoredImageCallData;
  friend class GetModelPathsCallData;
  friend class GetCameraSettingsCallData;
  friend class SetCameraSettingsCallData;
  friend class GetDeepweedOutputByTimestampCallData;
  friend class SetP2PContextCallData;
  friend class SetDeepweedDetectionCriteriaCallData;
  friend class GetDeepweedDetectionCriteriaCallData;
  friend class GetDeepweedSupportedCategoriesCallData;
  friend class StartBurstRecordFramesCallData;
  friend class StopBurstRecordFramesCallData;
  friend class GetConnectorsCallData;
  friend class SetConnectorsCallData;
  friend class GetTimingCallData;
  friend class GetCameraTemperaturesCallData;
  friend class GetChipImageCallData;
  friend class GetChipQueueInformationCallData;
  friend class GetLatestP2PImageCallData;
  friend class FlushQueuesCallData;
  friend class GetLightweightBurstRecordCallData;
  friend class P2PBufferringBurstCaptureCallData;
  friend class P2PCaptureCallData;
  friend class SetAutoWhitebalanceCallData;
  friend class GetNextDeepweedOutputCallData;
  friend class GetNextP2POutputCallData;
  friend class SetTargetingStateCallData;
  friend class GetRecommendedStrobeSettingsCallData;
  friend class GetNextFocusMetricCallData;
  friend class RemoveDataDirCallData;
  friend class GetComputeCapabilitiesCallData;
  friend class GetSupportedTensorRTVersionsCallData;
  friend class ReloadCategoryCollectionCallData;
  friend class GetCategoryCollectionCallData;
  friend class GetErrorStateCallData;
  friend class SnapshotPredictImagesCallData;
  friend class GetChipForPredictImageCallData;

private:
  proto::CVRuntimeService::AsyncService service_;
  std::unique_ptr<grpc::ServerCompletionQueue> cq_;
  std::vector<std::future<void>> futures_;
  std::atomic<bool> running_;

  // metrics
  prometheus::Family<prometheus::Gauge> &num_waiting_threads_family_;
  prometheus::Gauge &num_waiting_threads_gauge_;
  std::atomic<uint32_t> num_waiting_threads_;

  prometheus::Family<prometheus::Gauge> &queue_latency_family_;
  prometheus::Gauge &queue_latency_gauge_;
  carbon::common::ThreadSafeMovingAverage<double> queue_latency_;

  prometheus::Family<prometheus::Counter> &num_requests_processed_family_;
  prometheus::Counter &num_requests_processed_counter_;
  std::atomic<uint32_t> num_requests_processed_;

  void HandleRequests();
};

class CVRuntimeGRPCServer {
public:
  CVRuntimeGRPCServer(CVRuntimeServiceImplCommon *service_impl, ImageStreamServiceImpl *image_service_impl,
                      lib::common::logging::LoggingServiceImpl *logging_service_impl,
                      const std::string &server_address);
  ~CVRuntimeGRPCServer();

private:
  CVRuntimeServiceImplCommon *service_impl_;
  ImageStreamServiceImpl *image_service_impl_;
  lib::common::logging::LoggingServiceImpl *logging_service_impl_;
  std::string server_address_;
  std::unique_ptr<grpc::Server> server_;
};

} // namespace runtime
} // namespace cv
