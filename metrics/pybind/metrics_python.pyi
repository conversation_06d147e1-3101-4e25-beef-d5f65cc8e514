from typing import Dict, List, Optional, Tuple

from ingest.pybind.ingest_python import IngestClient

class DailyTimezone:
    def __init__(self, owner: bool = False) -> None: ...
    def get_day(self) -> str: ...

class ConclusionType:
    def __init__(self, value: int) -> None: ...
    kNotWeeding: "ConclusionType"
    kOutOfBand: "ConclusionType"
    kIntersectsWithNonShootable: "ConclusionType"
    kOutOfRange: "ConclusionType"
    kUnimportant: "ConclusionType"
    kNotShot: "ConclusionType"
    kPartiallyShot: "ConclusionType"
    kShot: "ConclusionType"
    kP2PNotFound: "ConclusionType"
    kError: "ConclusionType"
    kFlicker: "ConclusionType"
    kMarkedForThinning: "ConclusionType"
    kNotTargeted: "ConclusionType"
    kP2PMissingContext: "ConclusionType"
    kConclusionTypeNumber: "ConclusionType"
    value: int
    name: str

class PlantConclusionCounter:
    def __init__(self, dtz: Optional[DailyTimezone] = None, writer: bool = False, row: str = "") -> None: ...
    def get(self, type: ConclusionType, armed: bool, is_weed: bool) -> int: ...
    def get_size(self, is_weed: bool) -> Tuple[float, int]: ...
    def get_avg_size(self, is_weed: bool) -> float: ...
    def get_total_count(self, is_weed: bool) -> int: ...
    def get_total_inactive_count(self, is_weed: bool) -> int: ...
    def get_count_by_type(self) -> Dict[str, int]: ...
    def get_required_shoot_time(self, targetable: bool) -> Tuple[int, int]: ...
    def valid_crop_count(self) -> int: ...
    @staticmethod
    def make_key(today: str, row: Optional[str]) -> str: ...

class ConclusionCounterFetcher: ...

class MetricsAggregator(IngestClient, ConclusionCounterFetcher):
    def start(self) -> None: ...
    @staticmethod
    def get() -> "MetricsAggregator": ...

class P2PAccuracy:
    def __init__(self) -> None: ...
    def add(self, scanner_id: int, pan_offset: float, tilt_offset: float, num_p2p_matches: int) -> None: ...

class SpatialBlockOwner:
    def __init__(self) -> None: ...
    def register_client(self) -> int: ...
    def beat(self, id: int) -> bool: ...
    def ack(self, client_id: int, block_id: int) -> bool: ...
    def can_close(self, block_id: int) -> bool: ...
