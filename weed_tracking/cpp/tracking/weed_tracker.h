#pragma once

#include "weed_tracking/cpp/tracking/tracker.h"
#include "weed_tracking/cpp/tracking/tracking_utils.h"
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <lib/common/cpp/utils/thread_safe_moving_average.hpp>
#include <weed_tracking_libs/tracker_roi/cpp/tracker_roi.hpp>
#include <wheel_encoder/cpp/wheel_encoder.hpp>
#include "generated/recorder/proto/recorder.pb.h"

#include <atomic>
#include <boost/lockfree/queue.hpp>
#include <cmath>
#include <functional>
#include <stdint.h>
#include <torch/torch.h>
#include <vector>

#include "cv/deepweed/output.h"

namespace carbon {
namespace weed_tracking {
class WeedTrackerOutput {
public:
  WeedTrackerOutput(int64_t timestamp_ms, std::vector<std::shared_ptr<trajectory::Trajectory>> item_locations,
                    std::vector<uint32_t> newly_tracked_ids,
                    std::map<uint32_t, std::pair<float, float>> deepweed_flow_offsets_xy,
                    std::map<uint32_t, int> number_missed_first_instances,
                    std::map<uint32_t, float> vertical_offset_first_instances);
  int64_t get_timestamp_ms();
  std::vector<std::shared_ptr<trajectory::Trajectory>> get_trajectories();
  std::vector<uint32_t> get_newly_tracked_ids();
  std::map<uint32_t, std::pair<float, float>> get_deepweed_flow_offsets_xy();
  std::map<uint32_t, int> get_number_missed_first_instances();
  std::map<uint32_t, float> get_vertical_offset_first_instances();

private:
  int64_t timestamp_ms_;
  std::vector<std::shared_ptr<trajectory::Trajectory>> item_locations_;
  std::vector<uint32_t> newly_tracked_ids_;
  std::map<uint32_t, std::pair<float, float>> deepweed_flow_offsets_xy_;
  std::map<uint32_t, int> number_missed_first_instances_;
  std::map<uint32_t, float> vertical_offset_first_instances_;
};

class WeedTracker {
public:
  WeedTracker(std::string pcam_id, uint32_t numeric_id, std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam,
              int64_t prune_grace_ms, uint32_t width, uint32_t height, float max_pos_mm_y,
              uint32_t max_number_of_weeds);
  std::tuple<size_t, size_t, size_t, std::map<size_t, trajectory::Trajectory::ID>, bool>
  add_deepweed(cv::deepweed::DeepweedOutput &deepweed_output, std::vector<::recorder::TrackedItem> &recorded_tracked_items, std::map<size_t, std::pair<float, float>> &model_undistorted_coordinates, ::recorder::CandidateShift &best_candidate_match,
      std::vector<::recorder::CandidateShift> &all_candidate_matches);
  int64_t add_rotary_ticks(int64_t timestamp_ms, float mm_travelled);
  WeedTrackerOutput get_latest_weeds();
  std::shared_ptr<Tracker> get_tracker();
  std::shared_ptr<trajectory::FlowAccumulator> get_robot_distance_accumulator();
  float get_pix_per_ticks();
  std::vector<double> get_averages_for_all_columns(std::optional<float> size_mm = std::nullopt);

  inline uint32_t numeric_id() { return numeric_id_; }
  std::tuple<uint32_t, size_t, size_t> weed_tracker_state() { return tracker_->tracker_state(); }
  float max_pos_mm_y() { return max_pos_mm_y_; }

  inline float latency() const { return latency_.avg(); }
  inline float processing_time() const { return processing_time_.avg(); }
  void untrack();

private:
  struct Config {
    float plant_dedup_radius_px;
    float crop_safety_radius_mm;
    float min_plant_captcha_weed_threshold;
    float min_plant_captcha_crop_threshold;
    float min_plant_score;
    Config(std::shared_ptr<config::ConfigTree> aimbot_tree, std::shared_ptr<config::ConfigTree> plant_captcha_tree);
  };
  config::AtomicFlagConfigScopedCallback scoped_tree_;
  config::AtomicFlagConfigScopedCallback plant_captcha_scoped_tree_;
  std::unique_ptr<Config> conf_;
  std::string pcam_id_;
  uint32_t numeric_id_;
  std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam_;
  std::shared_ptr<config::ConfigTree> plant_captcha_node_;

  std::shared_ptr<Tracker> tracker_;
  std::shared_ptr<trajectory::FlowAccumulator> robot_distance_accumulator_;
  float rotary_velocity_;
  int64_t deepweed_timestamp_ms_;
  int64_t latest_of_timestamp_ms_;
  int64_t latest_rotary_timestamp_ms_;
  int64_t prune_grace_ms_;
  float current_x_direction_;
  float current_y_direction_;
  float current_pix_per_ticks_;
  int32_t flow_height_;
  int32_t flow_width_;

  boost::lockfree::queue<uint32_t> to_be_untracked_;
  std::vector<uint32_t> newly_tracked_ids_;
  std::map<uint32_t, std::pair<float, float>> deepweed_flow_offsets_xy_;

  bool started_getting_ticks_;

  uint32_t width_;
  uint32_t height_;
  const float max_pos_mm_y_;
  common::ThreadSafeMovingAverage<float> latency_;
  common::ThreadSafeMovingAverage<float> processing_time_;
  tracker_roi::TrackerRoiAccessor roi_accessor_;

  void mark_untrack(uint32_t id);
};

} // namespace weed_tracking
} // namespace carbon