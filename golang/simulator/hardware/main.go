package main

import (
	"context"
	"fmt"
	"net"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/service"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/simulator_controller"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/supervisory"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/web_server"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

const ServicePort = 64544
const WebServerPort = 8090 // if you change this, you must change envoy.yaml

func bindAndListen(port int) (*net.Listener, error) {
	addr := fmt.Sprintf("0.0.0.0:%d", port)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		logrus.WithError(err).Fatalf("failed to listen on port: %v", port)
	}
	return &lis, err
}

func serve(grpcServer *grpc.Server, listener *net.Listener, wg *sync.WaitGroup) {
	wg.Add(1)
	// Serve() is blocking hence go routine
	err := grpcServer.Serve(*listener)
	if err != nil {
		logrus.WithError(err).Fatalf("Failed to Serve GRPC Server")
	}
	wg.Done()
}

func main() {
	stopCtx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	customFormatter := new(logrus.TextFormatter)
	customFormatter.TimestampFormat = "2006-01-02 15:04:05.000"
	customFormatter.FullTimestamp = true
	logrus.SetFormatter(customFormatter)

	configSubscriber := config.NewConfigSubscriber(config.MakeRobotLocalAddr(61001))
	configSubscriber.AddConfigTree("simulator", "simulator", "services/simulator.yaml")
	configSubscriber.Start()
	configSubscriber.WaitUntilReady()

	configClient := config.NewConfigClient(config.MakeRobotLocalAddr(61001))

	simState, err := simulator_controller.CreateSimulators(configSubscriber, configClient)
	for err != nil {
		logrus.WithError(err).Error("Error Creating New Simulators, will retry in 5 seconds")
		time.Sleep(5 * time.Second)
		simState, err = simulator_controller.CreateSimulators(configSubscriber, configClient)
	}
	simState.StartSimulators(false)

	var wg sync.WaitGroup

	serviceListener, err := bindAndListen(ServicePort)
	if err != nil {
		return
	}
	// Create GRPC Server
	var opts []grpc.ServerOption
	serviceGRPCServer := grpc.NewServer(opts...)
	_ = service.NewSimulatorService(serviceGRPCServer, simState.SupervisorySim, simState.SafetySim, simState.SimSpace)
	go serve(serviceGRPCServer, serviceListener, &wg)

	webServerListener, err := bindAndListen(WebServerPort)
	if err != nil {
		return
	}

	wsGRPCServer := grpc.NewServer()
	webServer := web_server.NewSimulatorUIService(wsGRPCServer, configSubscriber, simState)
	go webServer.Start()
	go serve(wsGRPCServer, webServerListener, &wg)

	routine := supervisory.NewSupervisoryRoutine(configClient)
	routine.Run([]supervisory.Routine{}) // an empty routine array will do nothing, example routine: {"Good", "Good"}

	<-stopCtx.Done()

	serviceGRPCServer.Stop()
	wsGRPCServer.Stop()

	webServer.Stop()
	simState.Stop()

	wg.Wait()
	logrus.Infof("Hardware Simulator Shut Down Gracefully")
}
