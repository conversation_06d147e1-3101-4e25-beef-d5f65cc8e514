// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: golang/simulator/hardware/proto/sim_UI/sim_UI.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[14]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
namespace carbon {
namespace simulator_UI {
class DiagnosticSettings;
struct DiagnosticSettingsDefaultTypeInternal;
extern DiagnosticSettingsDefaultTypeInternal _DiagnosticSettings_default_instance_;
class GetDiagnosticSettingsRequest;
struct GetDiagnosticSettingsRequestDefaultTypeInternal;
extern GetDiagnosticSettingsRequestDefaultTypeInternal _GetDiagnosticSettingsRequest_default_instance_;
class GetDiagnosticSettingsResponse;
struct GetDiagnosticSettingsResponseDefaultTypeInternal;
extern GetDiagnosticSettingsResponseDefaultTypeInternal _GetDiagnosticSettingsResponse_default_instance_;
class GetPresetsRequest;
struct GetPresetsRequestDefaultTypeInternal;
extern GetPresetsRequestDefaultTypeInternal _GetPresetsRequest_default_instance_;
class GetPresetsResponse;
struct GetPresetsResponseDefaultTypeInternal;
extern GetPresetsResponseDefaultTypeInternal _GetPresetsResponse_default_instance_;
class GetVelocityRequest;
struct GetVelocityRequestDefaultTypeInternal;
extern GetVelocityRequestDefaultTypeInternal _GetVelocityRequest_default_instance_;
class GetVelocityResponse;
struct GetVelocityResponseDefaultTypeInternal;
extern GetVelocityResponseDefaultTypeInternal _GetVelocityResponse_default_instance_;
class PresetSetting;
struct PresetSettingDefaultTypeInternal;
extern PresetSettingDefaultTypeInternal _PresetSetting_default_instance_;
class SetDiagnosticSettingsRequest;
struct SetDiagnosticSettingsRequestDefaultTypeInternal;
extern SetDiagnosticSettingsRequestDefaultTypeInternal _SetDiagnosticSettingsRequest_default_instance_;
class SetDiagnosticSettingsResponse;
struct SetDiagnosticSettingsResponseDefaultTypeInternal;
extern SetDiagnosticSettingsResponseDefaultTypeInternal _SetDiagnosticSettingsResponse_default_instance_;
class SetPresetRequest;
struct SetPresetRequestDefaultTypeInternal;
extern SetPresetRequestDefaultTypeInternal _SetPresetRequest_default_instance_;
class SetPresetResponse;
struct SetPresetResponseDefaultTypeInternal;
extern SetPresetResponseDefaultTypeInternal _SetPresetResponse_default_instance_;
class SetVelocityRequest;
struct SetVelocityRequestDefaultTypeInternal;
extern SetVelocityRequestDefaultTypeInternal _SetVelocityRequest_default_instance_;
class SetVelocityResponse;
struct SetVelocityResponseDefaultTypeInternal;
extern SetVelocityResponseDefaultTypeInternal _SetVelocityResponse_default_instance_;
}  // namespace simulator_UI
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::simulator_UI::DiagnosticSettings* Arena::CreateMaybeMessage<::carbon::simulator_UI::DiagnosticSettings>(Arena*);
template<> ::carbon::simulator_UI::GetDiagnosticSettingsRequest* Arena::CreateMaybeMessage<::carbon::simulator_UI::GetDiagnosticSettingsRequest>(Arena*);
template<> ::carbon::simulator_UI::GetDiagnosticSettingsResponse* Arena::CreateMaybeMessage<::carbon::simulator_UI::GetDiagnosticSettingsResponse>(Arena*);
template<> ::carbon::simulator_UI::GetPresetsRequest* Arena::CreateMaybeMessage<::carbon::simulator_UI::GetPresetsRequest>(Arena*);
template<> ::carbon::simulator_UI::GetPresetsResponse* Arena::CreateMaybeMessage<::carbon::simulator_UI::GetPresetsResponse>(Arena*);
template<> ::carbon::simulator_UI::GetVelocityRequest* Arena::CreateMaybeMessage<::carbon::simulator_UI::GetVelocityRequest>(Arena*);
template<> ::carbon::simulator_UI::GetVelocityResponse* Arena::CreateMaybeMessage<::carbon::simulator_UI::GetVelocityResponse>(Arena*);
template<> ::carbon::simulator_UI::PresetSetting* Arena::CreateMaybeMessage<::carbon::simulator_UI::PresetSetting>(Arena*);
template<> ::carbon::simulator_UI::SetDiagnosticSettingsRequest* Arena::CreateMaybeMessage<::carbon::simulator_UI::SetDiagnosticSettingsRequest>(Arena*);
template<> ::carbon::simulator_UI::SetDiagnosticSettingsResponse* Arena::CreateMaybeMessage<::carbon::simulator_UI::SetDiagnosticSettingsResponse>(Arena*);
template<> ::carbon::simulator_UI::SetPresetRequest* Arena::CreateMaybeMessage<::carbon::simulator_UI::SetPresetRequest>(Arena*);
template<> ::carbon::simulator_UI::SetPresetResponse* Arena::CreateMaybeMessage<::carbon::simulator_UI::SetPresetResponse>(Arena*);
template<> ::carbon::simulator_UI::SetVelocityRequest* Arena::CreateMaybeMessage<::carbon::simulator_UI::SetVelocityRequest>(Arena*);
template<> ::carbon::simulator_UI::SetVelocityResponse* Arena::CreateMaybeMessage<::carbon::simulator_UI::SetVelocityResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace simulator_UI {

// ===================================================================

class GetVelocityRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.GetVelocityRequest) */ {
 public:
  inline GetVelocityRequest() : GetVelocityRequest(nullptr) {}
  explicit constexpr GetVelocityRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetVelocityRequest(const GetVelocityRequest& from);
  GetVelocityRequest(GetVelocityRequest&& from) noexcept
    : GetVelocityRequest() {
    *this = ::std::move(from);
  }

  inline GetVelocityRequest& operator=(const GetVelocityRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetVelocityRequest& operator=(GetVelocityRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetVelocityRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetVelocityRequest* internal_default_instance() {
    return reinterpret_cast<const GetVelocityRequest*>(
               &_GetVelocityRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetVelocityRequest& a, GetVelocityRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetVelocityRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetVelocityRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetVelocityRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetVelocityRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetVelocityRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetVelocityRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.GetVelocityRequest";
  }
  protected:
  explicit GetVelocityRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetVelocityRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class GetVelocityResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.GetVelocityResponse) */ {
 public:
  inline GetVelocityResponse() : GetVelocityResponse(nullptr) {}
  ~GetVelocityResponse() override;
  explicit constexpr GetVelocityResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetVelocityResponse(const GetVelocityResponse& from);
  GetVelocityResponse(GetVelocityResponse&& from) noexcept
    : GetVelocityResponse() {
    *this = ::std::move(from);
  }

  inline GetVelocityResponse& operator=(const GetVelocityResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetVelocityResponse& operator=(GetVelocityResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetVelocityResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetVelocityResponse* internal_default_instance() {
    return reinterpret_cast<const GetVelocityResponse*>(
               &_GetVelocityResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetVelocityResponse& a, GetVelocityResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetVelocityResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetVelocityResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetVelocityResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetVelocityResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetVelocityResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetVelocityResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetVelocityResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.GetVelocityResponse";
  }
  protected:
  explicit GetVelocityResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVelocityFieldNumber = 1,
  };
  // float velocity = 1;
  void clear_velocity();
  float velocity() const;
  void set_velocity(float value);
  private:
  float _internal_velocity() const;
  void _internal_set_velocity(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetVelocityResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float velocity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetVelocityRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.SetVelocityRequest) */ {
 public:
  inline SetVelocityRequest() : SetVelocityRequest(nullptr) {}
  ~SetVelocityRequest() override;
  explicit constexpr SetVelocityRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetVelocityRequest(const SetVelocityRequest& from);
  SetVelocityRequest(SetVelocityRequest&& from) noexcept
    : SetVelocityRequest() {
    *this = ::std::move(from);
  }

  inline SetVelocityRequest& operator=(const SetVelocityRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetVelocityRequest& operator=(SetVelocityRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetVelocityRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetVelocityRequest* internal_default_instance() {
    return reinterpret_cast<const SetVelocityRequest*>(
               &_SetVelocityRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SetVelocityRequest& a, SetVelocityRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetVelocityRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetVelocityRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetVelocityRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetVelocityRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetVelocityRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetVelocityRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetVelocityRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.SetVelocityRequest";
  }
  protected:
  explicit SetVelocityRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVelocityFieldNumber = 1,
  };
  // float velocity = 1;
  void clear_velocity();
  float velocity() const;
  void set_velocity(float value);
  private:
  float _internal_velocity() const;
  void _internal_set_velocity(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetVelocityRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float velocity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetVelocityResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.SetVelocityResponse) */ {
 public:
  inline SetVelocityResponse() : SetVelocityResponse(nullptr) {}
  explicit constexpr SetVelocityResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetVelocityResponse(const SetVelocityResponse& from);
  SetVelocityResponse(SetVelocityResponse&& from) noexcept
    : SetVelocityResponse() {
    *this = ::std::move(from);
  }

  inline SetVelocityResponse& operator=(const SetVelocityResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetVelocityResponse& operator=(SetVelocityResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetVelocityResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetVelocityResponse* internal_default_instance() {
    return reinterpret_cast<const SetVelocityResponse*>(
               &_SetVelocityResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SetVelocityResponse& a, SetVelocityResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetVelocityResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetVelocityResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetVelocityResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetVelocityResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetVelocityResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetVelocityResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.SetVelocityResponse";
  }
  protected:
  explicit SetVelocityResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetVelocityResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class GetPresetsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.GetPresetsRequest) */ {
 public:
  inline GetPresetsRequest() : GetPresetsRequest(nullptr) {}
  explicit constexpr GetPresetsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPresetsRequest(const GetPresetsRequest& from);
  GetPresetsRequest(GetPresetsRequest&& from) noexcept
    : GetPresetsRequest() {
    *this = ::std::move(from);
  }

  inline GetPresetsRequest& operator=(const GetPresetsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPresetsRequest& operator=(GetPresetsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPresetsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPresetsRequest* internal_default_instance() {
    return reinterpret_cast<const GetPresetsRequest*>(
               &_GetPresetsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetPresetsRequest& a, GetPresetsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPresetsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPresetsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPresetsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPresetsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetPresetsRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetPresetsRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.GetPresetsRequest";
  }
  protected:
  explicit GetPresetsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetPresetsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class PresetSetting final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.PresetSetting) */ {
 public:
  inline PresetSetting() : PresetSetting(nullptr) {}
  ~PresetSetting() override;
  explicit constexpr PresetSetting(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PresetSetting(const PresetSetting& from);
  PresetSetting(PresetSetting&& from) noexcept
    : PresetSetting() {
    *this = ::std::move(from);
  }

  inline PresetSetting& operator=(const PresetSetting& from) {
    CopyFrom(from);
    return *this;
  }
  inline PresetSetting& operator=(PresetSetting&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PresetSetting& default_instance() {
    return *internal_default_instance();
  }
  static inline const PresetSetting* internal_default_instance() {
    return reinterpret_cast<const PresetSetting*>(
               &_PresetSetting_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(PresetSetting& a, PresetSetting& b) {
    a.Swap(&b);
  }
  inline void Swap(PresetSetting* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PresetSetting* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PresetSetting* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PresetSetting>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PresetSetting& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PresetSetting& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PresetSetting* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.PresetSetting";
  }
  protected:
  explicit PresetSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPathFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // repeated string path = 1;
  int path_size() const;
  private:
  int _internal_path_size() const;
  public:
  void clear_path();
  const std::string& path(int index) const;
  std::string* mutable_path(int index);
  void set_path(int index, const std::string& value);
  void set_path(int index, std::string&& value);
  void set_path(int index, const char* value);
  void set_path(int index, const char* value, size_t size);
  std::string* add_path();
  void add_path(const std::string& value);
  void add_path(std::string&& value);
  void add_path(const char* value);
  void add_path(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& path() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_path();
  private:
  const std::string& _internal_path(int index) const;
  std::string* _internal_add_path();
  public:

  // string value = 2;
  void clear_value();
  const std::string& value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value();
  PROTOBUF_NODISCARD std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.PresetSetting)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class GetPresetsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.GetPresetsResponse) */ {
 public:
  inline GetPresetsResponse() : GetPresetsResponse(nullptr) {}
  ~GetPresetsResponse() override;
  explicit constexpr GetPresetsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPresetsResponse(const GetPresetsResponse& from);
  GetPresetsResponse(GetPresetsResponse&& from) noexcept
    : GetPresetsResponse() {
    *this = ::std::move(from);
  }

  inline GetPresetsResponse& operator=(const GetPresetsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPresetsResponse& operator=(GetPresetsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPresetsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPresetsResponse* internal_default_instance() {
    return reinterpret_cast<const GetPresetsResponse*>(
               &_GetPresetsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(GetPresetsResponse& a, GetPresetsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPresetsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPresetsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPresetsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPresetsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPresetsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPresetsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPresetsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.GetPresetsResponse";
  }
  protected:
  explicit GetPresetsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSettingsFieldNumber = 1,
  };
  // repeated .carbon.simulator_UI.PresetSetting settings = 1;
  int settings_size() const;
  private:
  int _internal_settings_size() const;
  public:
  void clear_settings();
  ::carbon::simulator_UI::PresetSetting* mutable_settings(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator_UI::PresetSetting >*
      mutable_settings();
  private:
  const ::carbon::simulator_UI::PresetSetting& _internal_settings(int index) const;
  ::carbon::simulator_UI::PresetSetting* _internal_add_settings();
  public:
  const ::carbon::simulator_UI::PresetSetting& settings(int index) const;
  ::carbon::simulator_UI::PresetSetting* add_settings();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator_UI::PresetSetting >&
      settings() const;

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetPresetsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator_UI::PresetSetting > settings_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetPresetRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.SetPresetRequest) */ {
 public:
  inline SetPresetRequest() : SetPresetRequest(nullptr) {}
  ~SetPresetRequest() override;
  explicit constexpr SetPresetRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetPresetRequest(const SetPresetRequest& from);
  SetPresetRequest(SetPresetRequest&& from) noexcept
    : SetPresetRequest() {
    *this = ::std::move(from);
  }

  inline SetPresetRequest& operator=(const SetPresetRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetPresetRequest& operator=(SetPresetRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetPresetRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetPresetRequest* internal_default_instance() {
    return reinterpret_cast<const SetPresetRequest*>(
               &_SetPresetRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(SetPresetRequest& a, SetPresetRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetPresetRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetPresetRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetPresetRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetPresetRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetPresetRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetPresetRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetPresetRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.SetPresetRequest";
  }
  protected:
  explicit SetPresetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSettingFieldNumber = 1,
  };
  // .carbon.simulator_UI.PresetSetting setting = 1;
  bool has_setting() const;
  private:
  bool _internal_has_setting() const;
  public:
  void clear_setting();
  const ::carbon::simulator_UI::PresetSetting& setting() const;
  PROTOBUF_NODISCARD ::carbon::simulator_UI::PresetSetting* release_setting();
  ::carbon::simulator_UI::PresetSetting* mutable_setting();
  void set_allocated_setting(::carbon::simulator_UI::PresetSetting* setting);
  private:
  const ::carbon::simulator_UI::PresetSetting& _internal_setting() const;
  ::carbon::simulator_UI::PresetSetting* _internal_mutable_setting();
  public:
  void unsafe_arena_set_allocated_setting(
      ::carbon::simulator_UI::PresetSetting* setting);
  ::carbon::simulator_UI::PresetSetting* unsafe_arena_release_setting();

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetPresetRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::simulator_UI::PresetSetting* setting_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetPresetResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.SetPresetResponse) */ {
 public:
  inline SetPresetResponse() : SetPresetResponse(nullptr) {}
  explicit constexpr SetPresetResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetPresetResponse(const SetPresetResponse& from);
  SetPresetResponse(SetPresetResponse&& from) noexcept
    : SetPresetResponse() {
    *this = ::std::move(from);
  }

  inline SetPresetResponse& operator=(const SetPresetResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetPresetResponse& operator=(SetPresetResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetPresetResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetPresetResponse* internal_default_instance() {
    return reinterpret_cast<const SetPresetResponse*>(
               &_SetPresetResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(SetPresetResponse& a, SetPresetResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetPresetResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetPresetResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetPresetResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetPresetResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetPresetResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetPresetResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.SetPresetResponse";
  }
  protected:
  explicit SetPresetResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetPresetResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class DiagnosticSettings final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.DiagnosticSettings) */ {
 public:
  inline DiagnosticSettings() : DiagnosticSettings(nullptr) {}
  ~DiagnosticSettings() override;
  explicit constexpr DiagnosticSettings(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DiagnosticSettings(const DiagnosticSettings& from);
  DiagnosticSettings(DiagnosticSettings&& from) noexcept
    : DiagnosticSettings() {
    *this = ::std::move(from);
  }

  inline DiagnosticSettings& operator=(const DiagnosticSettings& from) {
    CopyFrom(from);
    return *this;
  }
  inline DiagnosticSettings& operator=(DiagnosticSettings&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DiagnosticSettings& default_instance() {
    return *internal_default_instance();
  }
  static inline const DiagnosticSettings* internal_default_instance() {
    return reinterpret_cast<const DiagnosticSettings*>(
               &_DiagnosticSettings_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(DiagnosticSettings& a, DiagnosticSettings& b) {
    a.Swap(&b);
  }
  inline void Swap(DiagnosticSettings* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DiagnosticSettings* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DiagnosticSettings* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DiagnosticSettings>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DiagnosticSettings& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DiagnosticSettings& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DiagnosticSettings* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.DiagnosticSettings";
  }
  protected:
  explicit DiagnosticSettings(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDiagnosticsFieldNumber = 2,
    kCurrentDiagnosticFieldNumber = 1,
    kRowFieldNumber = 3,
    kTicketFieldNumber = 5,
  };
  // repeated string diagnostics = 2;
  int diagnostics_size() const;
  private:
  int _internal_diagnostics_size() const;
  public:
  void clear_diagnostics();
  const std::string& diagnostics(int index) const;
  std::string* mutable_diagnostics(int index);
  void set_diagnostics(int index, const std::string& value);
  void set_diagnostics(int index, std::string&& value);
  void set_diagnostics(int index, const char* value);
  void set_diagnostics(int index, const char* value, size_t size);
  std::string* add_diagnostics();
  void add_diagnostics(const std::string& value);
  void add_diagnostics(std::string&& value);
  void add_diagnostics(const char* value);
  void add_diagnostics(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& diagnostics() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_diagnostics();
  private:
  const std::string& _internal_diagnostics(int index) const;
  std::string* _internal_add_diagnostics();
  public:

  // string current_diagnostic = 1;
  void clear_current_diagnostic();
  const std::string& current_diagnostic() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_current_diagnostic(ArgT0&& arg0, ArgT... args);
  std::string* mutable_current_diagnostic();
  PROTOBUF_NODISCARD std::string* release_current_diagnostic();
  void set_allocated_current_diagnostic(std::string* current_diagnostic);
  private:
  const std::string& _internal_current_diagnostic() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_current_diagnostic(const std::string& value);
  std::string* _internal_mutable_current_diagnostic();
  public:

  // int64 row = 3;
  void clear_row();
  int64_t row() const;
  void set_row(int64_t value);
  private:
  int64_t _internal_row() const;
  void _internal_set_row(int64_t value);
  public:

  // int64 ticket = 5;
  void clear_ticket();
  int64_t ticket() const;
  void set_ticket(int64_t value);
  private:
  int64_t _internal_ticket() const;
  void _internal_set_ticket(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.DiagnosticSettings)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> diagnostics_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr current_diagnostic_;
  int64_t row_;
  int64_t ticket_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class GetDiagnosticSettingsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.GetDiagnosticSettingsRequest) */ {
 public:
  inline GetDiagnosticSettingsRequest() : GetDiagnosticSettingsRequest(nullptr) {}
  ~GetDiagnosticSettingsRequest() override;
  explicit constexpr GetDiagnosticSettingsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetDiagnosticSettingsRequest(const GetDiagnosticSettingsRequest& from);
  GetDiagnosticSettingsRequest(GetDiagnosticSettingsRequest&& from) noexcept
    : GetDiagnosticSettingsRequest() {
    *this = ::std::move(from);
  }

  inline GetDiagnosticSettingsRequest& operator=(const GetDiagnosticSettingsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetDiagnosticSettingsRequest& operator=(GetDiagnosticSettingsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetDiagnosticSettingsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetDiagnosticSettingsRequest* internal_default_instance() {
    return reinterpret_cast<const GetDiagnosticSettingsRequest*>(
               &_GetDiagnosticSettingsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GetDiagnosticSettingsRequest& a, GetDiagnosticSettingsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetDiagnosticSettingsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetDiagnosticSettingsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetDiagnosticSettingsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetDiagnosticSettingsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetDiagnosticSettingsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetDiagnosticSettingsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetDiagnosticSettingsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.GetDiagnosticSettingsRequest";
  }
  protected:
  explicit GetDiagnosticSettingsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTicketFieldNumber = 1,
  };
  // int64 ticket = 1;
  void clear_ticket();
  int64_t ticket() const;
  void set_ticket(int64_t value);
  private:
  int64_t _internal_ticket() const;
  void _internal_set_ticket(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetDiagnosticSettingsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int64_t ticket_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class GetDiagnosticSettingsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.GetDiagnosticSettingsResponse) */ {
 public:
  inline GetDiagnosticSettingsResponse() : GetDiagnosticSettingsResponse(nullptr) {}
  ~GetDiagnosticSettingsResponse() override;
  explicit constexpr GetDiagnosticSettingsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetDiagnosticSettingsResponse(const GetDiagnosticSettingsResponse& from);
  GetDiagnosticSettingsResponse(GetDiagnosticSettingsResponse&& from) noexcept
    : GetDiagnosticSettingsResponse() {
    *this = ::std::move(from);
  }

  inline GetDiagnosticSettingsResponse& operator=(const GetDiagnosticSettingsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetDiagnosticSettingsResponse& operator=(GetDiagnosticSettingsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetDiagnosticSettingsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetDiagnosticSettingsResponse* internal_default_instance() {
    return reinterpret_cast<const GetDiagnosticSettingsResponse*>(
               &_GetDiagnosticSettingsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(GetDiagnosticSettingsResponse& a, GetDiagnosticSettingsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetDiagnosticSettingsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetDiagnosticSettingsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetDiagnosticSettingsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetDiagnosticSettingsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetDiagnosticSettingsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetDiagnosticSettingsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetDiagnosticSettingsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.GetDiagnosticSettingsResponse";
  }
  protected:
  explicit GetDiagnosticSettingsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSettingsFieldNumber = 1,
  };
  // .carbon.simulator_UI.DiagnosticSettings settings = 1;
  bool has_settings() const;
  private:
  bool _internal_has_settings() const;
  public:
  void clear_settings();
  const ::carbon::simulator_UI::DiagnosticSettings& settings() const;
  PROTOBUF_NODISCARD ::carbon::simulator_UI::DiagnosticSettings* release_settings();
  ::carbon::simulator_UI::DiagnosticSettings* mutable_settings();
  void set_allocated_settings(::carbon::simulator_UI::DiagnosticSettings* settings);
  private:
  const ::carbon::simulator_UI::DiagnosticSettings& _internal_settings() const;
  ::carbon::simulator_UI::DiagnosticSettings* _internal_mutable_settings();
  public:
  void unsafe_arena_set_allocated_settings(
      ::carbon::simulator_UI::DiagnosticSettings* settings);
  ::carbon::simulator_UI::DiagnosticSettings* unsafe_arena_release_settings();

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetDiagnosticSettingsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::simulator_UI::DiagnosticSettings* settings_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetDiagnosticSettingsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.SetDiagnosticSettingsRequest) */ {
 public:
  inline SetDiagnosticSettingsRequest() : SetDiagnosticSettingsRequest(nullptr) {}
  ~SetDiagnosticSettingsRequest() override;
  explicit constexpr SetDiagnosticSettingsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetDiagnosticSettingsRequest(const SetDiagnosticSettingsRequest& from);
  SetDiagnosticSettingsRequest(SetDiagnosticSettingsRequest&& from) noexcept
    : SetDiagnosticSettingsRequest() {
    *this = ::std::move(from);
  }

  inline SetDiagnosticSettingsRequest& operator=(const SetDiagnosticSettingsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetDiagnosticSettingsRequest& operator=(SetDiagnosticSettingsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetDiagnosticSettingsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetDiagnosticSettingsRequest* internal_default_instance() {
    return reinterpret_cast<const SetDiagnosticSettingsRequest*>(
               &_SetDiagnosticSettingsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(SetDiagnosticSettingsRequest& a, SetDiagnosticSettingsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetDiagnosticSettingsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetDiagnosticSettingsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetDiagnosticSettingsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetDiagnosticSettingsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetDiagnosticSettingsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetDiagnosticSettingsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetDiagnosticSettingsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.SetDiagnosticSettingsRequest";
  }
  protected:
  explicit SetDiagnosticSettingsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCurrentDiagnosticFieldNumber = 1,
    kRowFieldNumber = 2,
  };
  // string current_diagnostic = 1;
  void clear_current_diagnostic();
  const std::string& current_diagnostic() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_current_diagnostic(ArgT0&& arg0, ArgT... args);
  std::string* mutable_current_diagnostic();
  PROTOBUF_NODISCARD std::string* release_current_diagnostic();
  void set_allocated_current_diagnostic(std::string* current_diagnostic);
  private:
  const std::string& _internal_current_diagnostic() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_current_diagnostic(const std::string& value);
  std::string* _internal_mutable_current_diagnostic();
  public:

  // int64 row = 2;
  void clear_row();
  int64_t row() const;
  void set_row(int64_t value);
  private:
  int64_t _internal_row() const;
  void _internal_set_row(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetDiagnosticSettingsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr current_diagnostic_;
  int64_t row_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetDiagnosticSettingsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.simulator_UI.SetDiagnosticSettingsResponse) */ {
 public:
  inline SetDiagnosticSettingsResponse() : SetDiagnosticSettingsResponse(nullptr) {}
  explicit constexpr SetDiagnosticSettingsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetDiagnosticSettingsResponse(const SetDiagnosticSettingsResponse& from);
  SetDiagnosticSettingsResponse(SetDiagnosticSettingsResponse&& from) noexcept
    : SetDiagnosticSettingsResponse() {
    *this = ::std::move(from);
  }

  inline SetDiagnosticSettingsResponse& operator=(const SetDiagnosticSettingsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetDiagnosticSettingsResponse& operator=(SetDiagnosticSettingsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetDiagnosticSettingsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetDiagnosticSettingsResponse* internal_default_instance() {
    return reinterpret_cast<const SetDiagnosticSettingsResponse*>(
               &_SetDiagnosticSettingsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(SetDiagnosticSettingsResponse& a, SetDiagnosticSettingsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetDiagnosticSettingsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetDiagnosticSettingsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetDiagnosticSettingsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetDiagnosticSettingsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetDiagnosticSettingsResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetDiagnosticSettingsResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator_UI.SetDiagnosticSettingsResponse";
  }
  protected:
  explicit SetDiagnosticSettingsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetDiagnosticSettingsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetVelocityRequest

// -------------------------------------------------------------------

// GetVelocityResponse

// float velocity = 1;
inline void GetVelocityResponse::clear_velocity() {
  velocity_ = 0;
}
inline float GetVelocityResponse::_internal_velocity() const {
  return velocity_;
}
inline float GetVelocityResponse::velocity() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.GetVelocityResponse.velocity)
  return _internal_velocity();
}
inline void GetVelocityResponse::_internal_set_velocity(float value) {
  
  velocity_ = value;
}
inline void GetVelocityResponse::set_velocity(float value) {
  _internal_set_velocity(value);
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.GetVelocityResponse.velocity)
}

// -------------------------------------------------------------------

// SetVelocityRequest

// float velocity = 1;
inline void SetVelocityRequest::clear_velocity() {
  velocity_ = 0;
}
inline float SetVelocityRequest::_internal_velocity() const {
  return velocity_;
}
inline float SetVelocityRequest::velocity() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.SetVelocityRequest.velocity)
  return _internal_velocity();
}
inline void SetVelocityRequest::_internal_set_velocity(float value) {
  
  velocity_ = value;
}
inline void SetVelocityRequest::set_velocity(float value) {
  _internal_set_velocity(value);
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.SetVelocityRequest.velocity)
}

// -------------------------------------------------------------------

// SetVelocityResponse

// -------------------------------------------------------------------

// GetPresetsRequest

// -------------------------------------------------------------------

// PresetSetting

// repeated string path = 1;
inline int PresetSetting::_internal_path_size() const {
  return path_.size();
}
inline int PresetSetting::path_size() const {
  return _internal_path_size();
}
inline void PresetSetting::clear_path() {
  path_.Clear();
}
inline std::string* PresetSetting::add_path() {
  std::string* _s = _internal_add_path();
  // @@protoc_insertion_point(field_add_mutable:carbon.simulator_UI.PresetSetting.path)
  return _s;
}
inline const std::string& PresetSetting::_internal_path(int index) const {
  return path_.Get(index);
}
inline const std::string& PresetSetting::path(int index) const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.PresetSetting.path)
  return _internal_path(index);
}
inline std::string* PresetSetting::mutable_path(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.simulator_UI.PresetSetting.path)
  return path_.Mutable(index);
}
inline void PresetSetting::set_path(int index, const std::string& value) {
  path_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.PresetSetting.path)
}
inline void PresetSetting::set_path(int index, std::string&& value) {
  path_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.PresetSetting.path)
}
inline void PresetSetting::set_path(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  path_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.simulator_UI.PresetSetting.path)
}
inline void PresetSetting::set_path(int index, const char* value, size_t size) {
  path_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.simulator_UI.PresetSetting.path)
}
inline std::string* PresetSetting::_internal_add_path() {
  return path_.Add();
}
inline void PresetSetting::add_path(const std::string& value) {
  path_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.simulator_UI.PresetSetting.path)
}
inline void PresetSetting::add_path(std::string&& value) {
  path_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.simulator_UI.PresetSetting.path)
}
inline void PresetSetting::add_path(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  path_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.simulator_UI.PresetSetting.path)
}
inline void PresetSetting::add_path(const char* value, size_t size) {
  path_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.simulator_UI.PresetSetting.path)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
PresetSetting::path() const {
  // @@protoc_insertion_point(field_list:carbon.simulator_UI.PresetSetting.path)
  return path_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
PresetSetting::mutable_path() {
  // @@protoc_insertion_point(field_mutable_list:carbon.simulator_UI.PresetSetting.path)
  return &path_;
}

// string value = 2;
inline void PresetSetting::clear_value() {
  value_.ClearToEmpty();
}
inline const std::string& PresetSetting::value() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.PresetSetting.value)
  return _internal_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PresetSetting::set_value(ArgT0&& arg0, ArgT... args) {
 
 value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.PresetSetting.value)
}
inline std::string* PresetSetting::mutable_value() {
  std::string* _s = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:carbon.simulator_UI.PresetSetting.value)
  return _s;
}
inline const std::string& PresetSetting::_internal_value() const {
  return value_.Get();
}
inline void PresetSetting::_internal_set_value(const std::string& value) {
  
  value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PresetSetting::_internal_mutable_value() {
  
  return value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PresetSetting::release_value() {
  // @@protoc_insertion_point(field_release:carbon.simulator_UI.PresetSetting.value)
  return value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PresetSetting::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    
  } else {
    
  }
  value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.simulator_UI.PresetSetting.value)
}

// -------------------------------------------------------------------

// GetPresetsResponse

// repeated .carbon.simulator_UI.PresetSetting settings = 1;
inline int GetPresetsResponse::_internal_settings_size() const {
  return settings_.size();
}
inline int GetPresetsResponse::settings_size() const {
  return _internal_settings_size();
}
inline void GetPresetsResponse::clear_settings() {
  settings_.Clear();
}
inline ::carbon::simulator_UI::PresetSetting* GetPresetsResponse::mutable_settings(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.simulator_UI.GetPresetsResponse.settings)
  return settings_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator_UI::PresetSetting >*
GetPresetsResponse::mutable_settings() {
  // @@protoc_insertion_point(field_mutable_list:carbon.simulator_UI.GetPresetsResponse.settings)
  return &settings_;
}
inline const ::carbon::simulator_UI::PresetSetting& GetPresetsResponse::_internal_settings(int index) const {
  return settings_.Get(index);
}
inline const ::carbon::simulator_UI::PresetSetting& GetPresetsResponse::settings(int index) const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.GetPresetsResponse.settings)
  return _internal_settings(index);
}
inline ::carbon::simulator_UI::PresetSetting* GetPresetsResponse::_internal_add_settings() {
  return settings_.Add();
}
inline ::carbon::simulator_UI::PresetSetting* GetPresetsResponse::add_settings() {
  ::carbon::simulator_UI::PresetSetting* _add = _internal_add_settings();
  // @@protoc_insertion_point(field_add:carbon.simulator_UI.GetPresetsResponse.settings)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator_UI::PresetSetting >&
GetPresetsResponse::settings() const {
  // @@protoc_insertion_point(field_list:carbon.simulator_UI.GetPresetsResponse.settings)
  return settings_;
}

// -------------------------------------------------------------------

// SetPresetRequest

// .carbon.simulator_UI.PresetSetting setting = 1;
inline bool SetPresetRequest::_internal_has_setting() const {
  return this != internal_default_instance() && setting_ != nullptr;
}
inline bool SetPresetRequest::has_setting() const {
  return _internal_has_setting();
}
inline void SetPresetRequest::clear_setting() {
  if (GetArenaForAllocation() == nullptr && setting_ != nullptr) {
    delete setting_;
  }
  setting_ = nullptr;
}
inline const ::carbon::simulator_UI::PresetSetting& SetPresetRequest::_internal_setting() const {
  const ::carbon::simulator_UI::PresetSetting* p = setting_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::simulator_UI::PresetSetting&>(
      ::carbon::simulator_UI::_PresetSetting_default_instance_);
}
inline const ::carbon::simulator_UI::PresetSetting& SetPresetRequest::setting() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.SetPresetRequest.setting)
  return _internal_setting();
}
inline void SetPresetRequest::unsafe_arena_set_allocated_setting(
    ::carbon::simulator_UI::PresetSetting* setting) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(setting_);
  }
  setting_ = setting;
  if (setting) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.simulator_UI.SetPresetRequest.setting)
}
inline ::carbon::simulator_UI::PresetSetting* SetPresetRequest::release_setting() {
  
  ::carbon::simulator_UI::PresetSetting* temp = setting_;
  setting_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::simulator_UI::PresetSetting* SetPresetRequest::unsafe_arena_release_setting() {
  // @@protoc_insertion_point(field_release:carbon.simulator_UI.SetPresetRequest.setting)
  
  ::carbon::simulator_UI::PresetSetting* temp = setting_;
  setting_ = nullptr;
  return temp;
}
inline ::carbon::simulator_UI::PresetSetting* SetPresetRequest::_internal_mutable_setting() {
  
  if (setting_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::simulator_UI::PresetSetting>(GetArenaForAllocation());
    setting_ = p;
  }
  return setting_;
}
inline ::carbon::simulator_UI::PresetSetting* SetPresetRequest::mutable_setting() {
  ::carbon::simulator_UI::PresetSetting* _msg = _internal_mutable_setting();
  // @@protoc_insertion_point(field_mutable:carbon.simulator_UI.SetPresetRequest.setting)
  return _msg;
}
inline void SetPresetRequest::set_allocated_setting(::carbon::simulator_UI::PresetSetting* setting) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete setting_;
  }
  if (setting) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::simulator_UI::PresetSetting>::GetOwningArena(setting);
    if (message_arena != submessage_arena) {
      setting = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, setting, submessage_arena);
    }
    
  } else {
    
  }
  setting_ = setting;
  // @@protoc_insertion_point(field_set_allocated:carbon.simulator_UI.SetPresetRequest.setting)
}

// -------------------------------------------------------------------

// SetPresetResponse

// -------------------------------------------------------------------

// DiagnosticSettings

// string current_diagnostic = 1;
inline void DiagnosticSettings::clear_current_diagnostic() {
  current_diagnostic_.ClearToEmpty();
}
inline const std::string& DiagnosticSettings::current_diagnostic() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.DiagnosticSettings.current_diagnostic)
  return _internal_current_diagnostic();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DiagnosticSettings::set_current_diagnostic(ArgT0&& arg0, ArgT... args) {
 
 current_diagnostic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.DiagnosticSettings.current_diagnostic)
}
inline std::string* DiagnosticSettings::mutable_current_diagnostic() {
  std::string* _s = _internal_mutable_current_diagnostic();
  // @@protoc_insertion_point(field_mutable:carbon.simulator_UI.DiagnosticSettings.current_diagnostic)
  return _s;
}
inline const std::string& DiagnosticSettings::_internal_current_diagnostic() const {
  return current_diagnostic_.Get();
}
inline void DiagnosticSettings::_internal_set_current_diagnostic(const std::string& value) {
  
  current_diagnostic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DiagnosticSettings::_internal_mutable_current_diagnostic() {
  
  return current_diagnostic_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DiagnosticSettings::release_current_diagnostic() {
  // @@protoc_insertion_point(field_release:carbon.simulator_UI.DiagnosticSettings.current_diagnostic)
  return current_diagnostic_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DiagnosticSettings::set_allocated_current_diagnostic(std::string* current_diagnostic) {
  if (current_diagnostic != nullptr) {
    
  } else {
    
  }
  current_diagnostic_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), current_diagnostic,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (current_diagnostic_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    current_diagnostic_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.simulator_UI.DiagnosticSettings.current_diagnostic)
}

// repeated string diagnostics = 2;
inline int DiagnosticSettings::_internal_diagnostics_size() const {
  return diagnostics_.size();
}
inline int DiagnosticSettings::diagnostics_size() const {
  return _internal_diagnostics_size();
}
inline void DiagnosticSettings::clear_diagnostics() {
  diagnostics_.Clear();
}
inline std::string* DiagnosticSettings::add_diagnostics() {
  std::string* _s = _internal_add_diagnostics();
  // @@protoc_insertion_point(field_add_mutable:carbon.simulator_UI.DiagnosticSettings.diagnostics)
  return _s;
}
inline const std::string& DiagnosticSettings::_internal_diagnostics(int index) const {
  return diagnostics_.Get(index);
}
inline const std::string& DiagnosticSettings::diagnostics(int index) const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.DiagnosticSettings.diagnostics)
  return _internal_diagnostics(index);
}
inline std::string* DiagnosticSettings::mutable_diagnostics(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.simulator_UI.DiagnosticSettings.diagnostics)
  return diagnostics_.Mutable(index);
}
inline void DiagnosticSettings::set_diagnostics(int index, const std::string& value) {
  diagnostics_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.DiagnosticSettings.diagnostics)
}
inline void DiagnosticSettings::set_diagnostics(int index, std::string&& value) {
  diagnostics_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.DiagnosticSettings.diagnostics)
}
inline void DiagnosticSettings::set_diagnostics(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  diagnostics_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.simulator_UI.DiagnosticSettings.diagnostics)
}
inline void DiagnosticSettings::set_diagnostics(int index, const char* value, size_t size) {
  diagnostics_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.simulator_UI.DiagnosticSettings.diagnostics)
}
inline std::string* DiagnosticSettings::_internal_add_diagnostics() {
  return diagnostics_.Add();
}
inline void DiagnosticSettings::add_diagnostics(const std::string& value) {
  diagnostics_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.simulator_UI.DiagnosticSettings.diagnostics)
}
inline void DiagnosticSettings::add_diagnostics(std::string&& value) {
  diagnostics_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.simulator_UI.DiagnosticSettings.diagnostics)
}
inline void DiagnosticSettings::add_diagnostics(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  diagnostics_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.simulator_UI.DiagnosticSettings.diagnostics)
}
inline void DiagnosticSettings::add_diagnostics(const char* value, size_t size) {
  diagnostics_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.simulator_UI.DiagnosticSettings.diagnostics)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DiagnosticSettings::diagnostics() const {
  // @@protoc_insertion_point(field_list:carbon.simulator_UI.DiagnosticSettings.diagnostics)
  return diagnostics_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DiagnosticSettings::mutable_diagnostics() {
  // @@protoc_insertion_point(field_mutable_list:carbon.simulator_UI.DiagnosticSettings.diagnostics)
  return &diagnostics_;
}

// int64 row = 3;
inline void DiagnosticSettings::clear_row() {
  row_ = int64_t{0};
}
inline int64_t DiagnosticSettings::_internal_row() const {
  return row_;
}
inline int64_t DiagnosticSettings::row() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.DiagnosticSettings.row)
  return _internal_row();
}
inline void DiagnosticSettings::_internal_set_row(int64_t value) {
  
  row_ = value;
}
inline void DiagnosticSettings::set_row(int64_t value) {
  _internal_set_row(value);
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.DiagnosticSettings.row)
}

// int64 ticket = 5;
inline void DiagnosticSettings::clear_ticket() {
  ticket_ = int64_t{0};
}
inline int64_t DiagnosticSettings::_internal_ticket() const {
  return ticket_;
}
inline int64_t DiagnosticSettings::ticket() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.DiagnosticSettings.ticket)
  return _internal_ticket();
}
inline void DiagnosticSettings::_internal_set_ticket(int64_t value) {
  
  ticket_ = value;
}
inline void DiagnosticSettings::set_ticket(int64_t value) {
  _internal_set_ticket(value);
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.DiagnosticSettings.ticket)
}

// -------------------------------------------------------------------

// GetDiagnosticSettingsRequest

// int64 ticket = 1;
inline void GetDiagnosticSettingsRequest::clear_ticket() {
  ticket_ = int64_t{0};
}
inline int64_t GetDiagnosticSettingsRequest::_internal_ticket() const {
  return ticket_;
}
inline int64_t GetDiagnosticSettingsRequest::ticket() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.GetDiagnosticSettingsRequest.ticket)
  return _internal_ticket();
}
inline void GetDiagnosticSettingsRequest::_internal_set_ticket(int64_t value) {
  
  ticket_ = value;
}
inline void GetDiagnosticSettingsRequest::set_ticket(int64_t value) {
  _internal_set_ticket(value);
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.GetDiagnosticSettingsRequest.ticket)
}

// -------------------------------------------------------------------

// GetDiagnosticSettingsResponse

// .carbon.simulator_UI.DiagnosticSettings settings = 1;
inline bool GetDiagnosticSettingsResponse::_internal_has_settings() const {
  return this != internal_default_instance() && settings_ != nullptr;
}
inline bool GetDiagnosticSettingsResponse::has_settings() const {
  return _internal_has_settings();
}
inline void GetDiagnosticSettingsResponse::clear_settings() {
  if (GetArenaForAllocation() == nullptr && settings_ != nullptr) {
    delete settings_;
  }
  settings_ = nullptr;
}
inline const ::carbon::simulator_UI::DiagnosticSettings& GetDiagnosticSettingsResponse::_internal_settings() const {
  const ::carbon::simulator_UI::DiagnosticSettings* p = settings_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::simulator_UI::DiagnosticSettings&>(
      ::carbon::simulator_UI::_DiagnosticSettings_default_instance_);
}
inline const ::carbon::simulator_UI::DiagnosticSettings& GetDiagnosticSettingsResponse::settings() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.GetDiagnosticSettingsResponse.settings)
  return _internal_settings();
}
inline void GetDiagnosticSettingsResponse::unsafe_arena_set_allocated_settings(
    ::carbon::simulator_UI::DiagnosticSettings* settings) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(settings_);
  }
  settings_ = settings;
  if (settings) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.simulator_UI.GetDiagnosticSettingsResponse.settings)
}
inline ::carbon::simulator_UI::DiagnosticSettings* GetDiagnosticSettingsResponse::release_settings() {
  
  ::carbon::simulator_UI::DiagnosticSettings* temp = settings_;
  settings_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::simulator_UI::DiagnosticSettings* GetDiagnosticSettingsResponse::unsafe_arena_release_settings() {
  // @@protoc_insertion_point(field_release:carbon.simulator_UI.GetDiagnosticSettingsResponse.settings)
  
  ::carbon::simulator_UI::DiagnosticSettings* temp = settings_;
  settings_ = nullptr;
  return temp;
}
inline ::carbon::simulator_UI::DiagnosticSettings* GetDiagnosticSettingsResponse::_internal_mutable_settings() {
  
  if (settings_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::simulator_UI::DiagnosticSettings>(GetArenaForAllocation());
    settings_ = p;
  }
  return settings_;
}
inline ::carbon::simulator_UI::DiagnosticSettings* GetDiagnosticSettingsResponse::mutable_settings() {
  ::carbon::simulator_UI::DiagnosticSettings* _msg = _internal_mutable_settings();
  // @@protoc_insertion_point(field_mutable:carbon.simulator_UI.GetDiagnosticSettingsResponse.settings)
  return _msg;
}
inline void GetDiagnosticSettingsResponse::set_allocated_settings(::carbon::simulator_UI::DiagnosticSettings* settings) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete settings_;
  }
  if (settings) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::simulator_UI::DiagnosticSettings>::GetOwningArena(settings);
    if (message_arena != submessage_arena) {
      settings = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, settings, submessage_arena);
    }
    
  } else {
    
  }
  settings_ = settings;
  // @@protoc_insertion_point(field_set_allocated:carbon.simulator_UI.GetDiagnosticSettingsResponse.settings)
}

// -------------------------------------------------------------------

// SetDiagnosticSettingsRequest

// string current_diagnostic = 1;
inline void SetDiagnosticSettingsRequest::clear_current_diagnostic() {
  current_diagnostic_.ClearToEmpty();
}
inline const std::string& SetDiagnosticSettingsRequest::current_diagnostic() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.SetDiagnosticSettingsRequest.current_diagnostic)
  return _internal_current_diagnostic();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetDiagnosticSettingsRequest::set_current_diagnostic(ArgT0&& arg0, ArgT... args) {
 
 current_diagnostic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.SetDiagnosticSettingsRequest.current_diagnostic)
}
inline std::string* SetDiagnosticSettingsRequest::mutable_current_diagnostic() {
  std::string* _s = _internal_mutable_current_diagnostic();
  // @@protoc_insertion_point(field_mutable:carbon.simulator_UI.SetDiagnosticSettingsRequest.current_diagnostic)
  return _s;
}
inline const std::string& SetDiagnosticSettingsRequest::_internal_current_diagnostic() const {
  return current_diagnostic_.Get();
}
inline void SetDiagnosticSettingsRequest::_internal_set_current_diagnostic(const std::string& value) {
  
  current_diagnostic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetDiagnosticSettingsRequest::_internal_mutable_current_diagnostic() {
  
  return current_diagnostic_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetDiagnosticSettingsRequest::release_current_diagnostic() {
  // @@protoc_insertion_point(field_release:carbon.simulator_UI.SetDiagnosticSettingsRequest.current_diagnostic)
  return current_diagnostic_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetDiagnosticSettingsRequest::set_allocated_current_diagnostic(std::string* current_diagnostic) {
  if (current_diagnostic != nullptr) {
    
  } else {
    
  }
  current_diagnostic_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), current_diagnostic,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (current_diagnostic_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    current_diagnostic_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.simulator_UI.SetDiagnosticSettingsRequest.current_diagnostic)
}

// int64 row = 2;
inline void SetDiagnosticSettingsRequest::clear_row() {
  row_ = int64_t{0};
}
inline int64_t SetDiagnosticSettingsRequest::_internal_row() const {
  return row_;
}
inline int64_t SetDiagnosticSettingsRequest::row() const {
  // @@protoc_insertion_point(field_get:carbon.simulator_UI.SetDiagnosticSettingsRequest.row)
  return _internal_row();
}
inline void SetDiagnosticSettingsRequest::_internal_set_row(int64_t value) {
  
  row_ = value;
}
inline void SetDiagnosticSettingsRequest::set_row(int64_t value) {
  _internal_set_row(value);
  // @@protoc_insertion_point(field_set:carbon.simulator_UI.SetDiagnosticSettingsRequest.row)
}

// -------------------------------------------------------------------

// SetDiagnosticSettingsResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace simulator_UI
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_2fproto_2fsim_5fUI_2fsim_5fUI_2eproto
