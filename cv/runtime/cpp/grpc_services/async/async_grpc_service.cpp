#include "cv/runtime/cpp/grpc_services/async/async_grpc_service.hpp"
#include "cv/runtime/cpp/grpc_services/impl/cv_runtime_service_impl.hpp"
#include <chrono>
#include <grpcpp/grpcpp.h>
#include <prometheus/gauge.h>
#include <prometheus/registry.h>
#include <spdlog/spdlog.h>

#include "lib/common/cpp/utils/thread_safe_moving_average.hpp"

namespace cv {
namespace runtime {

using namespace lib::common::camera;
using namespace cv::runtime::proto;
constexpr std::string_view data_dir("/data");
constexpr size_t kNumAsyncThreads = 32; // TODO profile and adjust

// Template class for handling async RPC calls
template <typename RequestType, typename ResponseType, typename Derived>
class AsyncCallData : public CallData {
public:
  AsyncCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                CVRuntimeServiceImplAsync *service_impl)
      : service_(service), cq_(cq), responder_(&ctx_), service_impl_(service_impl), status_(CREATE) {
    // Don't call Proceed() here - let derived classes call it after construction
  }

  // TODO: support a cancellation tag / context cancellation
  void Proceed(bool ok = true) override {
    if (status_ == CREATE && ok) {
      // Make this instance ready to process a new request
      status_ = PROCESS;

      // Request a new call
      RequestCall();
    } else if (status_ == PROCESS && ok) {
      // Create a new instance to handle the next request
      new Derived(service_, cq_, service_impl_);

      try {
        // Process the current request
        service_impl_->num_requests_processed_++;
        ProcessRequest();
      } catch (const std::exception &e) {
        spdlog::error("Exception processing request: {}", e.what());
        status = grpc::Status(grpc::StatusCode::UNKNOWN, e.what());
      }

      // Record the time we responded to the request, so we can measure the time between when we were done with the
      // request and when we delete this object the goal is to get the latency between 2 proceed calls on the same tag,
      // assuming the completion queue is roughly FIFO
      start_time_ = std::chrono::steady_clock::now();

      // And finish the call
      status_ = FINISH;
      responder_.Finish(response_, status, this);
      // NEVER PUT ANY CODE AFTER THE FINISH CALL
      // the tag has been returned to the completion queue and may be reused by another thread
      // so delete might be called on another thread before we get here
    } else { // status_ == FINISH or !ok
      if (status_ == FINISH && ok) {
        if (start_time_ != std::chrono::time_point<std::chrono::steady_clock>()) {
          auto end_time = std::chrono::steady_clock::now();
          auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time_);
          service_impl_->queue_latency_.add(duration.count());
        }
      }
      // We're done with this call
      delete this;
    }
  }

protected:
  virtual void RequestCall() = 0;
  virtual void ProcessRequest() = 0;

  proto::CVRuntimeService::AsyncService *service_;
  grpc::ServerCompletionQueue *cq_;
  grpc::ServerContext ctx_;
  RequestType request_;
  ResponseType response_;
  grpc::ServerAsyncResponseWriter<ResponseType> responder_;
  CVRuntimeServiceImplAsync *service_impl_;
  grpc::Status status;

  // The current state of the call
  enum CallStatus { CREATE, PROCESS, FINISH };
  CallStatus status_;

  std::chrono::time_point<std::chrono::steady_clock> start_time_;
};

class GetBootedCallData : public AsyncCallData<GetBootedRequest, GetBootedResponse, GetBootedCallData> {
public:
  GetBootedCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                    CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetBootedRequest, GetBootedResponse, GetBootedCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetBooted(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetBooted(&request_, &response_); }
};

class GetReadyCallData : public AsyncCallData<GetReadyRequest, GetReadyResponse, GetReadyCallData> {
public:
  GetReadyCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                   CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetReadyRequest, GetReadyResponse, GetReadyCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetReady(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetReady(&request_, &response_); }
};

class GetLatestImageCallData
    : public AsyncCallData<GetLatestImageRequest, ImageAndMetadataResponse, GetLatestImageCallData> {
public:
  GetLatestImageCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                         CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetLatestImageRequest, ImageAndMetadataResponse, GetLatestImageCallData>(service, cq,
                                                                                               service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetLatestImage(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetLatestImage(&request_, &response_); }
};

class GetImageNearTimestampCallData
    : public AsyncCallData<GetImageNearTimestampRequest, ImageAndMetadataResponse, GetImageNearTimestampCallData> {
public:
  GetImageNearTimestampCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetImageNearTimestampRequest, ImageAndMetadataResponse, GetImageNearTimestampCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetImageNearTimestamp(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetImageNearTimestamp(&request_, &response_); }
};

class SetImageScoreCallData : public AsyncCallData<SetImageScoreRequest, SetImageScoreResponse, SetImageScoreCallData> {
public:
  SetImageScoreCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SetImageScoreRequest, SetImageScoreResponse, SetImageScoreCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestSetImageScore(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processSetImageScore(&request_, &response_); }
};

class GetCameraInfoCallData : public AsyncCallData<GetCameraInfoRequest, GetCameraInfoResponse, GetCameraInfoCallData> {
public:
  GetCameraInfoCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetCameraInfoRequest, GetCameraInfoResponse, GetCameraInfoCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetCameraInfo(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetCameraInfo(&request_, &response_); }
};
class PredictCallData : public AsyncCallData<PredictRequest, PredictResponse, PredictCallData> {
public:
  PredictCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                  CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<PredictRequest, PredictResponse, PredictCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestPredict(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processPredict(&request_, &response_); }
};

class LoadAndQueueCallData : public AsyncCallData<LoadAndQueueRequest, LoadAndQueueResponse, LoadAndQueueCallData> {
public:
  LoadAndQueueCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                       CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<LoadAndQueueRequest, LoadAndQueueResponse, LoadAndQueueCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestLoadAndQueue(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processLoadAndQueue(&request_, &response_); }
};

class SetImageCallData : public AsyncCallData<SetImageRequest, SetImageResponse, SetImageCallData> {
public:
  SetImageCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                   CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SetImageRequest, SetImageResponse, SetImageCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestSetImage(&ctx_, &request_, &responder_, cq_, cq_, this); }

  void ProcessRequest() override { status = service_impl_->processSetImage(&request_, &response_); }
};

class UnsetImageCallData : public AsyncCallData<UnsetImageRequest, UnsetImageResponse, UnsetImageCallData> {
public:
  UnsetImageCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                     CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<UnsetImageRequest, UnsetImageResponse, UnsetImageCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestUnsetImage(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processUnsetImage(&request_, &response_); }
};

class GetCameraDimensionsCallData
    : public AsyncCallData<GetCameraDimensionsRequest, GetCameraDimensionsResponse, GetCameraDimensionsCallData> {
public:
  GetCameraDimensionsCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                              CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetCameraDimensionsRequest, GetCameraDimensionsResponse, GetCameraDimensionsCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetCameraDimensions(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetCameraDimensions(&request_, &response_); }
};

class GetScoreQueueCallData : public AsyncCallData<GetScoreQueueRequest, GetScoreQueueResponse, GetScoreQueueCallData> {
public:
  GetScoreQueueCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetScoreQueueRequest, GetScoreQueueResponse, GetScoreQueueCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetScoreQueue(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetScoreQueue(&request_, &response_); }
};

class ListScoreQueuesCallData : public AsyncCallData<Empty, ListScoreQueuesResponse, ListScoreQueuesCallData> {
public:
  ListScoreQueuesCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                          CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<Empty, ListScoreQueuesResponse, ListScoreQueuesCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestListScoreQueues(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processListScoreQueues(&request_, &response_); }
};

class GetMaxImageScoreCallData
    : public AsyncCallData<GetMaxImageScoreRequest, GetMaxImageScoreResponse, GetMaxImageScoreCallData> {
public:
  GetMaxImageScoreCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                           CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetMaxImageScoreRequest, GetMaxImageScoreResponse, GetMaxImageScoreCallData>(service, cq,
                                                                                                   service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetMaxImageScore(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetMaxImageScore(&request_, &response_); }
};

class GetMaxScoredImageCallData
    : public AsyncCallData<GetMaxScoredImageRequest, ImageAndMetadataResponse, GetMaxScoredImageCallData> {
public:
  GetMaxScoredImageCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                            CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetMaxScoredImageRequest, ImageAndMetadataResponse, GetMaxScoredImageCallData>(service, cq,
                                                                                                     service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetMaxScoredImage(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetMaxScoredImage(&request_, &response_); }
};

class GetModelPathsCallData : public AsyncCallData<GetModelPathsRequest, GetModelPathsResponse, GetModelPathsCallData> {
public:
  GetModelPathsCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetModelPathsRequest, GetModelPathsResponse, GetModelPathsCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetModelPaths(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetModelPaths(&request_, &response_); }
};

class GetCameraSettingsCallData
    : public AsyncCallData<GetCameraSettingsRequest, GetCameraSettingsResponse, GetCameraSettingsCallData> {
public:
  GetCameraSettingsCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                            CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetCameraSettingsRequest, GetCameraSettingsResponse, GetCameraSettingsCallData>(service, cq,
                                                                                                      service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetCameraSettings(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetCameraSettings(&request_, &response_); }
};

class SetCameraSettingsCallData
    : public AsyncCallData<SetCameraSettingsRequest, SetCameraSettingsResponse, SetCameraSettingsCallData> {
public:
  SetCameraSettingsCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                            CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SetCameraSettingsRequest, SetCameraSettingsResponse, SetCameraSettingsCallData>(service, cq,
                                                                                                      service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestSetCameraSettings(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processSetCameraSettings(&request_, &response_); }
};

class GetDeepweedOutputByTimestampCallData
    : public AsyncCallData<GetDeepweedOutputByTimestampRequest, DeepweedOutput, GetDeepweedOutputByTimestampCallData> {
public:
  GetDeepweedOutputByTimestampCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                       CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetDeepweedOutputByTimestampRequest, DeepweedOutput, GetDeepweedOutputByTimestampCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetDeepweedOutputByTimestamp(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processGetDeepweedOutputByTimestamp(&request_, &response_); }
};

class SetP2PContextCallData : public AsyncCallData<SetP2PContextRequest, SetP2PContextResponse, SetP2PContextCallData> {
public:
  SetP2PContextCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SetP2PContextRequest, SetP2PContextResponse, SetP2PContextCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestSetP2PContext(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processSetP2PContext(&request_, &response_); }
};

class SetDeepweedDetectionCriteriaCallData
    : public AsyncCallData<SetDeepweedDetectionCriteriaRequest, SetDeepweedDetectionCriteriaResponse,
                           SetDeepweedDetectionCriteriaCallData> {
public:
  SetDeepweedDetectionCriteriaCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                       CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SetDeepweedDetectionCriteriaRequest, SetDeepweedDetectionCriteriaResponse,
                      SetDeepweedDetectionCriteriaCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestSetDeepweedDetectionCriteria(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processSetDeepweedDetectionCriteria(&request_, &response_); }
};

class GetDeepweedDetectionCriteriaCallData
    : public AsyncCallData<GetDeepweedDetectionCriteriaRequest, GetDeepweedDetectionCriteriaResponse,
                           GetDeepweedDetectionCriteriaCallData> {
public:
  GetDeepweedDetectionCriteriaCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                       CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetDeepweedDetectionCriteriaRequest, GetDeepweedDetectionCriteriaResponse,
                      GetDeepweedDetectionCriteriaCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetDeepweedDetectionCriteria(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processGetDeepweedDetectionCriteria(&request_, &response_); }
};

class GetDeepweedSupportedCategoriesCallData
    : public AsyncCallData<GetDeepweedSupportedCategoriesRequest, GetDeepweedSupportedCategoriesResponse,
                           GetDeepweedSupportedCategoriesCallData> {
public:
  GetDeepweedSupportedCategoriesCallData(proto::CVRuntimeService::AsyncService *service,
                                         grpc::ServerCompletionQueue *cq, CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetDeepweedSupportedCategoriesRequest, GetDeepweedSupportedCategoriesResponse,
                      GetDeepweedSupportedCategoriesCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetDeepweedSupportedCategories(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override {
    status = service_impl_->processGetDeepweedSupportedCategories(&request_, &response_);
  }
};

class StartBurstRecordFramesCallData
    : public AsyncCallData<StartBurstRecordFramesRequest, StartBurstRecordFramesResponse,
                           StartBurstRecordFramesCallData> {
public:
  StartBurstRecordFramesCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                 CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<StartBurstRecordFramesRequest, StartBurstRecordFramesResponse, StartBurstRecordFramesCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestStartBurstRecordFrames(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processStartBurstRecordFrames(&request_, &response_); }
};

class StopBurstRecordFramesCallData
    : public AsyncCallData<StopBurstRecordFramesRequest, StopBurstRecordFramesResponse, StopBurstRecordFramesCallData> {
public:
  StopBurstRecordFramesCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<StopBurstRecordFramesRequest, StopBurstRecordFramesResponse, StopBurstRecordFramesCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestStopBurstRecordFrames(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processStopBurstRecordFrames(&request_, &response_); }
};

class GetConnectorsCallData : public AsyncCallData<GetConnectorsRequest, GetConnectorsResponse, GetConnectorsCallData> {
public:
  GetConnectorsCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetConnectorsRequest, GetConnectorsResponse, GetConnectorsCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetConnectors(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetConnectors(&request_, &response_); }
};

class SetConnectorsCallData : public AsyncCallData<SetConnectorsRequest, SetConnectorsResponse, SetConnectorsCallData> {
public:
  SetConnectorsCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SetConnectorsRequest, SetConnectorsResponse, SetConnectorsCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestSetConnectors(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processSetConnectors(&request_, &response_); }
};

class GetTimingCallData : public AsyncCallData<GetTimingRequest, GetTimingResponse, GetTimingCallData> {
public:
  GetTimingCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                    CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetTimingRequest, GetTimingResponse, GetTimingCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetTiming(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetTiming(&request_, &response_); }
};

class GetCameraTemperaturesCallData
    : public AsyncCallData<GetCameraTemperaturesRequest, GetCameraTemperaturesResponse, GetCameraTemperaturesCallData> {
public:
  GetCameraTemperaturesCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetCameraTemperaturesRequest, GetCameraTemperaturesResponse, GetCameraTemperaturesCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetCameraTemperatures(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetCameraTemperatures(&request_, &response_); }
};

class GetChipImageCallData
    : public AsyncCallData<GetChipImageRequest, ChipImageAndMetadataResponse, GetChipImageCallData> {
public:
  GetChipImageCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                       CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetChipImageRequest, ChipImageAndMetadataResponse, GetChipImageCallData>(service, cq,
                                                                                               service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetChipImage(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetChipImage(&request_, &response_); }
};

class GetChipQueueInformationCallData
    : public AsyncCallData<ChipQueueInformationRequest, ChipQueueInformationResponse, GetChipQueueInformationCallData> {
public:
  GetChipQueueInformationCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                  CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<ChipQueueInformationRequest, ChipQueueInformationResponse, GetChipQueueInformationCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetChipQueueInformation(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processGetChipQueueInformation(&request_, &response_); }
};

class GetLatestP2PImageCallData
    : public AsyncCallData<GetLatestP2PImageRequest, P2PImageAndMetadataResponse, GetLatestP2PImageCallData> {
public:
  GetLatestP2PImageCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                            CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetLatestP2PImageRequest, P2PImageAndMetadataResponse, GetLatestP2PImageCallData>(service, cq,
                                                                                                        service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetLatestP2PImage(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetLatestP2PImage(&request_, &response_); }
};

class FlushQueuesCallData : public AsyncCallData<FlushQueuesRequest, FlushQueuesResponse, FlushQueuesCallData> {
public:
  FlushQueuesCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                      CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<FlushQueuesRequest, FlushQueuesResponse, FlushQueuesCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestFlushQueues(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processFlushQueues(&request_, &response_); }
};

class GetLightweightBurstRecordCallData
    : public AsyncCallData<GetLightweightBurstRecordRequest, GetLightweightBurstRecordResponse,
                           GetLightweightBurstRecordCallData> {
public:
  GetLightweightBurstRecordCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                    CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetLightweightBurstRecordRequest, GetLightweightBurstRecordResponse,
                      GetLightweightBurstRecordCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetLightweightBurstRecord(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processGetLightweightBurstRecord(&request_, &response_); }
};

class P2PBufferringBurstCaptureCallData
    : public AsyncCallData<P2PBufferringBurstCaptureRequest, P2PBufferringBurstCaptureResponse,
                           P2PBufferringBurstCaptureCallData> {
public:
  P2PBufferringBurstCaptureCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                    CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<P2PBufferringBurstCaptureRequest, P2PBufferringBurstCaptureResponse,
                      P2PBufferringBurstCaptureCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestP2PBufferringBurstCapture(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processP2PBufferringBurstCapture(&request_, &response_); }
};

class P2PCaptureCallData : public AsyncCallData<P2PCaptureRequest, P2PCaptureResponse, P2PCaptureCallData> {
public:
  P2PCaptureCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                     CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<P2PCaptureRequest, P2PCaptureResponse, P2PCaptureCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestP2PCapture(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processP2PCapture(&request_, &response_); }
};

class SetAutoWhitebalanceCallData
    : public AsyncCallData<SetAutoWhitebalanceRequest, SetAutoWhitebalanceResponse, SetAutoWhitebalanceCallData> {
public:
  SetAutoWhitebalanceCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                              CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SetAutoWhitebalanceRequest, SetAutoWhitebalanceResponse, SetAutoWhitebalanceCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestSetAutoWhitebalance(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processSetAutoWhitebalance(&request_, &response_); }
};

class GetNextDeepweedOutputCallData
    : public AsyncCallData<GetNextDeepweedOutputRequest, DeepweedOutput, GetNextDeepweedOutputCallData> {
public:
  GetNextDeepweedOutputCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetNextDeepweedOutputRequest, DeepweedOutput, GetNextDeepweedOutputCallData>(service, cq,
                                                                                                   service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetNextDeepweedOutput(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetNextDeepweedOutput(&request_, &response_); }
};

class GetNextP2POutputCallData
    : public AsyncCallData<GetNextP2POutputRequest, P2POutputProto, GetNextP2POutputCallData> {
public:
  GetNextP2POutputCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                           CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetNextP2POutputRequest, P2POutputProto, GetNextP2POutputCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetNextP2POutput(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetNextP2POutput(&request_, &response_); }
};

class SetTargetingStateCallData
    : public AsyncCallData<SetTargetingStateRequest, SetTargetingStateResponse, SetTargetingStateCallData> {
public:
  SetTargetingStateCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                            CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SetTargetingStateRequest, SetTargetingStateResponse, SetTargetingStateCallData>(service, cq,
                                                                                                      service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestSetTargetingState(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processSetTargetingState(&request_, &response_); }
};

class GetRecommendedStrobeSettingsCallData
    : public AsyncCallData<GetRecommendedStrobeSettingsRequest, GetRecommendedStrobeSettingsResponse,
                           GetRecommendedStrobeSettingsCallData> {
public:
  GetRecommendedStrobeSettingsCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                       CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetRecommendedStrobeSettingsRequest, GetRecommendedStrobeSettingsResponse,
                      GetRecommendedStrobeSettingsCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetRecommendedStrobeSettings(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processGetRecommendedStrobeSettings(&request_, &response_); }
};

class GetNextFocusMetricCallData
    : public AsyncCallData<GetNextFocusMetricRequest, GetNextFocusMetricResponse, GetNextFocusMetricCallData> {
public:
  GetNextFocusMetricCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                             CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetNextFocusMetricRequest, GetNextFocusMetricResponse, GetNextFocusMetricCallData>(service, cq,
                                                                                                         service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetNextFocusMetric(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetNextFocusMetric(&request_, &response_); }
};

class RemoveDataDirCallData : public AsyncCallData<RemoveDataDirRequest, RemoveDataDirResponse, RemoveDataDirCallData> {
public:
  RemoveDataDirCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<RemoveDataDirRequest, RemoveDataDirResponse, RemoveDataDirCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestRemoveDataDir(&ctx_, &request_, &responder_, cq_, cq_, this); }

  void ProcessRequest() override { status = service_impl_->processRemoveDataDir(&request_, &response_); }
};

class GetComputeCapabilitiesCallData
    : public AsyncCallData<Empty, ComputeCapabilitiesResponse, GetComputeCapabilitiesCallData> {
public:
  GetComputeCapabilitiesCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                 CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<Empty, ComputeCapabilitiesResponse, GetComputeCapabilitiesCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetComputeCapabilities(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processGetComputeCapabilities(&request_, &response_); }
};

class GetSupportedTensorRTVersionsCallData
    : public AsyncCallData<Empty, SupportedTensorRTVersionsResponse, GetSupportedTensorRTVersionsCallData> {
public:
  GetSupportedTensorRTVersionsCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                       CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<Empty, SupportedTensorRTVersionsResponse, GetSupportedTensorRTVersionsCallData>(service, cq,
                                                                                                      service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetSupportedTensorRTVersions(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processGetSupportedTensorRTVersions(&request_, &response_); }
};

class ReloadCategoryCollectionCallData : public AsyncCallData<Empty, Empty, ReloadCategoryCollectionCallData> {
public:
  ReloadCategoryCollectionCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                   CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<Empty, Empty, ReloadCategoryCollectionCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestReloadCategoryCollection(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processReloadCategoryCollection(&request_, &response_); }
};

class GetCategoryCollectionCallData
    : public AsyncCallData<Empty, GetCategoryCollectionResponse, GetCategoryCollectionCallData> {
public:
  GetCategoryCollectionCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<Empty, GetCategoryCollectionResponse, GetCategoryCollectionCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetCategoryCollection(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetCategoryCollection(&request_, &response_); }
};

class GetErrorStateCallData : public AsyncCallData<Empty, GetErrorStateResponse, GetErrorStateCallData> {
public:
  GetErrorStateCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                        CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<Empty, GetErrorStateResponse, GetErrorStateCallData>(service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestGetErrorState(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processGetErrorState(&request_, &response_); }
};

class SnapshotPredictImagesCallData
    : public AsyncCallData<SnapshotPredictImagesRequest, SnapshotPredictImagesResponse, SnapshotPredictImagesCallData> {
public:
  SnapshotPredictImagesCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<SnapshotPredictImagesRequest, SnapshotPredictImagesResponse, SnapshotPredictImagesCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override { service_->RequestSnapshotPredictImages(&ctx_, &request_, &responder_, cq_, cq_, this); }
  void ProcessRequest() override { status = service_impl_->processSnapshotPredictImages(&request_, &response_); }
};

class GetChipForPredictImageCallData
    : public AsyncCallData<GetChipForPredictImageRequest, GetChipForPredictImageResponse,
                           GetChipForPredictImageCallData> {
public:
  GetChipForPredictImageCallData(proto::CVRuntimeService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                                 CVRuntimeServiceImplAsync *service_impl)
      : AsyncCallData<GetChipForPredictImageRequest, GetChipForPredictImageResponse, GetChipForPredictImageCallData>(
            service, cq, service_impl) {
    Proceed();
  }

protected:
  void RequestCall() override {
    service_->RequestGetChipForPredictImage(&ctx_, &request_, &responder_, cq_, cq_, this);
  }
  void ProcessRequest() override { status = service_impl_->processGetChipForPredictImage(&request_, &response_); }
};

CVRuntimeServiceImplAsync::CVRuntimeServiceImplAsync(
    std::shared_ptr<NodeRegistry> node_registry,
    std::shared_ptr<std::map<std::string, std::map<proto::BufferUseCase, std::string>>> camera_to_buffer,
    std::shared_ptr<std::map<std::string, lib::common::camera::Camera>> camera_id_to_camera,
    std::shared_ptr<std::map<std::string, bool>> camera_id_to_transpose,
    std::shared_ptr<std::map<std::string, std::pair<std::string, lib::common::camera::Camera>>> sim_cameras,
    std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry,
    std::shared_ptr<lib::common::GeoData> geo_data, std::shared_ptr<lib::common::ImplementStatus> implement_status,
    std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues,
    std::shared_ptr<carbon::config::ConfigTree> deepweed_config, std::string kLightweightBurstDirectory,
    std::string kFullBurstDirectory,
    const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues,
    std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>>>>
        deepweed_buffers,
    std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>>
        p2p_buffers,
    std::string role,
    std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
        distance_camera_buffer,
    std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> chip_score_queues,
    std::function<void()> reload_category_collection, std::shared_ptr<cv::embeddings::Manager> embeddings_manager,
    std::shared_ptr<prometheus::Registry> registry, std::shared_ptr<cv::runtime::ErrorState> error_state)
    : CVRuntimeServiceImplCommon(node_registry, camera_to_buffer, camera_id_to_camera, camera_id_to_transpose,
                                 sim_cameras, model_registry, geo_data, implement_status, score_queues, deepweed_config,
                                 kLightweightBurstDirectory, kFullBurstDirectory, p2p_queues, deepweed_buffers,
                                 p2p_buffers, role, distance_camera_buffer, chip_score_queues,
                                 reload_category_collection, embeddings_manager, error_state),
      running_(false), num_waiting_threads_family_(prometheus::BuildGauge()
                                                       .Name("cv_runtime_service_num_waiting_threads")
                                                       .Help("Number of threads waiting for a request")
                                                       .Register(*registry)),
      num_waiting_threads_gauge_(num_waiting_threads_family_.Add({})), num_waiting_threads_(0),
      queue_latency_family_(prometheus::BuildGauge()
                                .Name("cv_runtime_service_queue_latency_us")
                                .Help("Latency between processing calls on the same rpc tag")
                                .Register(*registry)),
      queue_latency_gauge_(queue_latency_family_.Add({})), queue_latency_(100),
      num_requests_processed_family_(prometheus::BuildCounter()
                                         .Name("cv_runtime_service_num_requests_processed")
                                         .Help("Number of requests processed")
                                         .Register(*registry)),
      num_requests_processed_counter_(num_requests_processed_family_.Add({})), num_requests_processed_(0) {}

CVRuntimeServiceImplAsync::~CVRuntimeServiceImplAsync() { Shutdown(); }

void CVRuntimeServiceImplAsync::Build(grpc::ServerBuilder &builder) {
  builder.RegisterService(&service_);
  cq_ = builder.AddCompletionQueue();
}

void CVRuntimeServiceImplAsync::Start() {
  if (running_) {
    return;
  }

  running_ = true;
  for (size_t i = 0; i < kNumAsyncThreads; i++) {
    futures_.emplace_back(std::async(std::launch::async, &CVRuntimeServiceImplAsync::HandleRequests, this));
  }
}

void CVRuntimeServiceImplAsync::Shutdown() {
  if (!running_) {
    return;
  }

  running_ = false;
  cq_->Shutdown();

  for (auto &future : futures_) {
    if (future.valid()) {
      future.get(); // rethrow any exception
    }
  }

  // drain the queue
  void *tag;
  bool ok;
  while (cq_->Next(&tag, &ok)) {
    static_cast<CallData *>(tag)->Proceed(ok);
  }
}

void CVRuntimeServiceImplAsync::Check() {
  for (auto &future : futures_) {
    if (future.valid() && future.wait_for(std::chrono::seconds(0)) == std::future_status::ready) {
      future.get(); // rethrow any exception
    }
  }
}

void CVRuntimeServiceImplAsync::ReportMetrics() {
  num_waiting_threads_gauge_.Set(static_cast<double>(num_waiting_threads_));
  queue_latency_gauge_.Set(queue_latency_.avg());
  num_requests_processed_counter_.Increment(num_requests_processed_.exchange(0));
}

void CVRuntimeServiceImplAsync::HandleRequests() {
  // Initialize all CallData classes to handle different RPCs
  new GetBootedCallData(&service_, cq_.get(), this);
  new GetReadyCallData(&service_, cq_.get(), this);
  new GetLatestImageCallData(&service_, cq_.get(), this);
  new SetImageScoreCallData(&service_, cq_.get(), this);
  new GetCameraInfoCallData(&service_, cq_.get(), this);
  new PredictCallData(&service_, cq_.get(), this);
  new LoadAndQueueCallData(&service_, cq_.get(), this);
  new SetImageCallData(&service_, cq_.get(), this);
  new UnsetImageCallData(&service_, cq_.get(), this);
  new GetCameraDimensionsCallData(&service_, cq_.get(), this);
  new GetScoreQueueCallData(&service_, cq_.get(), this);
  new ListScoreQueuesCallData(&service_, cq_.get(), this);
  new GetMaxImageScoreCallData(&service_, cq_.get(), this);
  new GetMaxScoredImageCallData(&service_, cq_.get(), this);
  new GetModelPathsCallData(&service_, cq_.get(), this);
  new GetCameraSettingsCallData(&service_, cq_.get(), this);
  new SetCameraSettingsCallData(&service_, cq_.get(), this);
  new GetDeepweedOutputByTimestampCallData(&service_, cq_.get(), this);
  new SetP2PContextCallData(&service_, cq_.get(), this);
  new SetDeepweedDetectionCriteriaCallData(&service_, cq_.get(), this);
  new GetDeepweedDetectionCriteriaCallData(&service_, cq_.get(), this);
  new GetDeepweedSupportedCategoriesCallData(&service_, cq_.get(), this);
  new StartBurstRecordFramesCallData(&service_, cq_.get(), this);
  new StopBurstRecordFramesCallData(&service_, cq_.get(), this);
  new GetConnectorsCallData(&service_, cq_.get(), this);
  new SetConnectorsCallData(&service_, cq_.get(), this);
  new GetTimingCallData(&service_, cq_.get(), this);
  new GetCameraTemperaturesCallData(&service_, cq_.get(), this);
  new GetChipImageCallData(&service_, cq_.get(), this);
  new GetChipQueueInformationCallData(&service_, cq_.get(), this);
  new GetLatestP2PImageCallData(&service_, cq_.get(), this);
  new FlushQueuesCallData(&service_, cq_.get(), this);
  new GetLightweightBurstRecordCallData(&service_, cq_.get(), this);
  new P2PBufferringBurstCaptureCallData(&service_, cq_.get(), this);
  new P2PCaptureCallData(&service_, cq_.get(), this);
  new SetAutoWhitebalanceCallData(&service_, cq_.get(), this);
  new GetNextDeepweedOutputCallData(&service_, cq_.get(), this);
  new GetNextP2POutputCallData(&service_, cq_.get(), this);
  new SetTargetingStateCallData(&service_, cq_.get(), this);
  new GetRecommendedStrobeSettingsCallData(&service_, cq_.get(), this);
  new GetNextFocusMetricCallData(&service_, cq_.get(), this);
  new RemoveDataDirCallData(&service_, cq_.get(), this);
  new GetComputeCapabilitiesCallData(&service_, cq_.get(), this);
  new GetSupportedTensorRTVersionsCallData(&service_, cq_.get(), this);
  new ReloadCategoryCollectionCallData(&service_, cq_.get(), this);
  new GetCategoryCollectionCallData(&service_, cq_.get(), this);
  new SnapshotPredictImagesCallData(&service_, cq_.get(), this);
  new GetChipForPredictImageCallData(&service_, cq_.get(), this);

  void *tag;
  bool ok;

  while (running_) {
    num_waiting_threads_++;
    if (cq_->Next(&tag, &ok)) {
      num_waiting_threads_--;
      static_cast<CallData *>(tag)->Proceed(ok);
    } else {
      // shutdown
      num_waiting_threads_--;
      break;
    }
  }
}

CVRuntimeGRPCServer::CVRuntimeGRPCServer(CVRuntimeServiceImplCommon *service_impl,
                                         ImageStreamServiceImpl *image_service_impl,
                                         lib::common::logging::LoggingServiceImpl *logging_service_impl,
                                         const std::string &server_address)
    : service_impl_(service_impl), image_service_impl_(image_service_impl), logging_service_impl_(logging_service_impl),
      server_address_(server_address) {

  grpc::ServerBuilder builder;
  builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
  builder.SetMaxSendMessageSize(50 * 1024 * 1024); // 50 MB for image transfer
  builder.SetResourceQuota(
      grpc::ResourceQuota().SetMaxThreads(60)); // does not impact the threads used for async handling requests

  service_impl->Build(builder);
  builder.RegisterService(image_service_impl);
  builder.RegisterService(logging_service_impl);

  server_ = builder.BuildAndStart();
  service_impl->Start();
}

CVRuntimeGRPCServer::~CVRuntimeGRPCServer() {
  // shutdown the server before the services
  server_->Shutdown();
  service_impl_->Shutdown();
}

} // namespace runtime
} // namespace cv
