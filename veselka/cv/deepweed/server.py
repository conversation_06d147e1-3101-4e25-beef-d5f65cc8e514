import argparse
import base64
import io
import json
import logging
import os
import traceback
from pathlib import Path
from threading import Lock
from typing import Any, Dict, List, Optional, Tuple, Union, cast
from uuid import uuid4

import cv2
import numpy as np
import requests
import torch
import torchvision.transforms.functional as TF
from botocore.exceptions import ClientError
from PIL import Image
from prometheus_client import start_http_server

from cv.comparison.comparison_python import ComparisonModel
from cv.deepweed.deepweed_python import DeepweedModel
from cv.encoder.encoder_python import FeatureExtractor
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass
from lib.common.model.cpp.model_python import AtomicModel
from lib.common.s3_cache_proxy.client import S3CacheProxyClient
from lib.common.time import maka_control_timestamp_ms
from veselka.cv.deepweed import metrics, types
from veselka.cv.server import MODEL_BUCKET, PredictHandler, Server

MODEL_CACHE: Dict[str, Union[DeepweedModel, FeatureExtractor, ComparisonModel]] = {}
MODEL_CACHE_INFO: List[Tuple[str, str]] = []  # (model_id, model_type)
MODEL_CACHE_SIZE = 6

PREDICT_IMAGE_DIR = os.environ.get("VESELKA_PREDICT_DIR", "/data/veselka_predict_images")
PREDICT_MODEL_DIR = os.environ.get("VESELKA_MODEL_DIR", "/data/veselka_models")

CHIP_RADIUS = 300

PORT = int(os.environ.get("PORT", 8080))

LOG = logging.getLogger(__name__)


def save_to_cache(
    model_id: str, model: Union[DeepweedModel, FeatureExtractor, ComparisonModel], low_queue_priority: bool = False,
) -> None:
    if model_id not in MODEL_CACHE and low_queue_priority:
        for i, (old_model_id, priority) in enumerate(
            MODEL_CACHE_INFO
        ):  # Allow only one low priority model to be cached at a time
            if priority == "low":
                MODEL_CACHE_INFO.pop(i)
                del MODEL_CACHE[old_model_id]
                break

    while len(MODEL_CACHE_INFO) >= MODEL_CACHE_SIZE:
        model_cache_path = MODEL_CACHE_INFO.pop(0)
        del MODEL_CACHE[model_cache_path[0]]
    if model_id not in MODEL_CACHE:
        MODEL_CACHE[model_id] = model
        MODEL_CACHE_INFO.append((model_id, "high" if not low_queue_priority else "low"))


class DeepweedPredictionHandler(PredictHandler):
    def __init__(self, predict_lock: Lock, gpu_index: int = 0):
        super().__init__(predict_lock)
        self._gpu_index = gpu_index
        self._s3_cache_proxy_client = S3CacheProxyClient(self._carbon_cache_host)

    # TODO: Remove the requirement on Veselka CV to understand our s3 model path structure
    @metrics.MODEL_LOAD_TIME.time()  # type: ignore
    def _get_model_from_cache(self, model_id: str, model_s3_path: Optional[str] = None) -> str:
        LOG.info(f"retrieving from cache model: {model_id} s3_path: {model_s3_path}")
        model_path = f"{PREDICT_MODEL_DIR}/{model_id}-{uuid4()}/model.trt"
        Path(os.path.dirname(model_path)).mkdir(parents=True, exist_ok=True)
        if model_s3_path:
            s3_bucket, s3_key = model_s3_path[5:].split("/", 1)
            response = requests.get(f"http://{self._carbon_cache_host}/{s3_bucket}/{s3_key}", timeout=30)
            assert response.ok, f"Bad response: {response.reason}"
        else:
            model_s3_path = f"models/{model_id}/trt_fp16.trt"
            response = requests.get(f"http://{self._carbon_cache_host}/{MODEL_BUCKET}/{model_s3_path}", timeout=30)
            if response.status_code == 404:
                model_s3_path = f"models/{model_id}/trt_int8.trt"
                response = requests.get(f"http://{self._carbon_cache_host}/{MODEL_BUCKET}/{model_s3_path}", timeout=30)
                assert response.ok, f"Bad response: {response.reason}"
            elif not response.ok:
                raise RuntimeError(f"Failed to find model: {model_id}")

        with open(model_path, "wb") as f:
            f.write(response.content)
        return model_path

    @metrics.MODEL_LOAD_TIME.time()  # type: ignore
    def _get_model(self, model_id: str, model_s3_path: Optional[str] = None) -> str:
        LOG.info(f"retrieving model: {model_id} s3_path: {model_s3_path}")
        model_path = f"{PREDICT_MODEL_DIR}/{model_id}-{uuid4()}/model.trt"
        Path(os.path.dirname(model_path)).mkdir(parents=True, exist_ok=True)
        if model_s3_path:
            s3_bucket, s3_key = model_s3_path[5:].split("/", 1)
            self.s3_client.download_file(s3_bucket, s3_key, model_path)
        else:
            try:
                model_s3_path = f"models/{model_id}/trt_fp16.trt"
                self.s3_client.download_file(MODEL_BUCKET, model_s3_path, model_path)
            except ClientError:
                model_s3_path = f"models/{model_id}/trt_int8.trt"
                self.s3_client.download_file(MODEL_BUCKET, model_s3_path, model_path)
        return model_path

    @metrics.IMAGE_LOAD_TIME.time()  # type: ignore
    def _get_image(self, s3_bucket: str, image_s3_path: str) -> torch.Tensor:
        LOG.info(f"retrieving image bucket: {s3_bucket} key: {image_s3_path}")
        # Only for testing
        local_image_path = f"{PREDICT_IMAGE_DIR}/{os.path.basename(image_s3_path)}"
        if os.path.exists(local_image_path):
            image_path = local_image_path
        else:
            os.makedirs(PREDICT_IMAGE_DIR, exist_ok=True)
            image_path = f"{PREDICT_IMAGE_DIR}/{uuid4()}-{os.path.basename(image_s3_path)}"
            self.s3_client.download_file(s3_bucket, image_s3_path, image_path)
        image = cv2.imread(image_path)
        os.remove(image_path)

        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image = (TF.to_tensor(image) * 255).to(torch.uint8)
        return cast(torch.Tensor, image)

    @metrics.IMAGE_LOAD_TIME.time()  # type: ignore
    def _get_image_from_cache(self, s3_bucket: str, image_s3_path: str) -> torch.Tensor:
        LOG.info(f"retrieving image from cache bucket: {s3_bucket} key: {image_s3_path}")
        request_str = f"http://{self._carbon_cache_host}/{s3_bucket}/{image_s3_path}"
        response = requests.get(request_str, timeout=30)
        assert response.ok
        image = Image.open(io.BytesIO(response.content), formats=["png"]).convert("RGB")
        image = (TF.to_tensor(image) * 255).to(torch.uint8)
        return cast(torch.Tensor, image)

    def _dl_prediction_v1(self, request: Dict[str, Any]) -> Dict[str, Any]:
        crop_threshold = request["crop_threshold"]
        weed_threshold = request["weed_threshold"]
        segmentation_threshold = request["segmentation_threshold"]
        predict_border = request.get("predict_border")

        model_id = request["model_id"]
        model_s3_path = request.get("model_s3_path")

        with self.predict_lock:
            img_s3_bucket, img_s3_key = request["image"]["s3_path"][5:].split("/", 1)
            LOG.info(
                f"predicting for model: {model_id} model_s3_path: {model_s3_path} image: {img_s3_key} cpt: {crop_threshold} wpt: {weed_threshold} segm: {segmentation_threshold} border: {predict_border}"
            )
            # Set model_id
            if model_id in MODEL_CACHE:
                model = MODEL_CACHE[model_id]
                assert isinstance(model, DeepweedModel)
            else:
                local_model_path = f"{PREDICT_MODEL_DIR}/{model_id}/model.trt"
                # Only for testing
                if os.path.exists(local_model_path):
                    model_path = local_model_path
                elif self._carbon_cache_host is not None:
                    model_path = self._get_model_from_cache(model_id, model_s3_path)
                else:
                    model_path = self._get_model(model_id, model_s3_path)
                model = DeepweedModel(AtomicModel(model_path, 0))
                os.remove(model_path)

            if self._carbon_cache_host is not None:
                image = self._get_image_from_cache(img_s3_bucket, img_s3_key)
            else:
                image = self._get_image(img_s3_bucket, img_s3_key)

            padding = 0
            if predict_border:
                padding = int(model.get_discard_points_border_px())
                image = torch.nn.functional.pad(image, (padding, padding, padding, padding))

            crop_id = request.get("crop_id")
            if crop_id is not None:
                set_crop_id_status = model.set_crop_id(crop_id)

                if set_crop_id_status:
                    LOG.info(f"Set crop ID for model: {crop_id} for model: {model_id}")
                else:
                    LOG.warn(f"Failed to set crop ID: {crop_id} for model: {model_id}")

            results = model.infer(
                image,
                maka_control_timestamp_ms(),
                request["image"]["ppi"],
                segmentation_threshold,
                1.0,
                weed_threshold,
                crop_threshold,
                min(weed_threshold, crop_threshold),
                False,
            )
            metrics.PREDICTION_COUNTER.inc()

            # TODO: Use the mask and classes to produce segmentation annotations for veselka
            result_masks: List[Dict[str, Any]] = []

            mask = results.get_mask_numpy_array(results)
            mask[mask < segmentation_threshold * 255] = 0
            mask[mask >= segmentation_threshold * 255] = 1
            mask = np.moveaxis(mask, (0, 1, 2), (2, 0, 1))
            if mask.size > 0:
                mask_scale = int(image.shape[1] / mask.shape[0])
                padding_mask = padding // mask_scale
                mask = mask[padding_mask : mask.shape[0] - padding_mask, padding : mask.shape[1] - padding_mask]
                png = np.repeat(mask, 4, axis=2)
                png[np.where((png == [1, 1, 1, 1]).all(axis=2))] = [100, 100, 100, 255]
                is_success, buffer = cv2.imencode(".png", png)
                if is_success:
                    mask_classes = results.mask_channel_classes
                    mask_class = mask_classes[0] if mask_classes else None
                    mask_bytes = buffer.tobytes()
                    result_masks = [
                        {
                            "width": mask.shape[2],
                            "height": mask.shape[1],
                            "ppi": int(request["image"]["ppi"] / mask_scale),
                            "data": base64.b64encode(mask_bytes).decode("utf-8"),
                            "cls": mask_class,
                        }
                    ]

            points = []
            for p in results.detections:
                if p.get_hit_class() != HitClass.WEED:
                    max_detection_class = HitClass.Name(p.get_hit_class())
                    detections = [{"cls": max_detection_class, "score": 1.0}]
                else:
                    max_detection_class = max(
                        p.get_detection_classes(results.weed_detection_classes), key=lambda x: x[1]
                    )[0]
                    detections = [
                        {"cls": d[0], "score": d[1]} for d in p.get_detection_classes(results.weed_detection_classes)
                    ]
                points.append(
                    {
                        "x": p.x - padding,
                        "y": p.y - padding,
                        "radius": p.size,
                        "score": p.score,
                        "cls": max_detection_class,
                        "detections": detections,
                    }
                )

            response = {"masks": result_masks, "points": points}
            save_to_cache(model_id, model)

        return response

    @metrics.MODEL_LOAD_TIME.time()  # type: ignore
    def _get_model_v2(self, s3_path: str) -> DeepweedModel:
        filepath = f"temp-model-file-{os.getpid()}.trt"
        bucket, key = self._s3_cache_proxy_client.split_uri(s3_path)
        self._s3_cache_proxy_client.download(bucket, key, filepath)
        model = DeepweedModel(AtomicModel(filepath, self._gpu_index))
        os.remove(filepath)
        return model

    def _get_feature_extractor(self, s3_path: str) -> FeatureExtractor:
        filepath = f"temp-fst-model-file-{os.getpid()}.trt"
        bucket, key = self._s3_cache_proxy_client.split_uri(s3_path)
        self._s3_cache_proxy_client.download(bucket, key, filepath)
        model = FeatureExtractor(AtomicModel(filepath, self._gpu_index))
        os.remove(filepath)
        return model

    def _get_comparison_model(self, s3_path: str) -> ComparisonModel:
        filepath = f"temp-cmp-model-file-{os.getpid()}.trt"
        bucket, key = self._s3_cache_proxy_client.split_uri(s3_path)
        self._s3_cache_proxy_client.download(bucket, key, filepath)
        model = ComparisonModel(AtomicModel(filepath, self._gpu_index))
        os.remove(filepath)
        return model

    @metrics.IMAGE_LOAD_TIME.time()  # type: ignore
    def _get_image_v2(self, s3_path) -> torch.Tensor:
        LOG.info(f"Loading image: {s3_path}")
        bucket, key = self._s3_cache_proxy_client.split_uri(s3_path)
        pil_image = self._s3_cache_proxy_client.get_image(bucket=bucket, key=key)
        image: torch.Tensor = (TF.to_tensor(pil_image) * 255).to(torch.uint8)
        return image

    def _get_chip(self, s3_path: str, x: int, y: int, radius: int = CHIP_RADIUS) -> torch.Tensor:
        LOG.info(f"Loading chip: {s3_path}, x: {x}, y: {y}")
        bucket, key = self._s3_cache_proxy_client.split_uri(s3_path)
        pil_image = self._s3_cache_proxy_client.get_image_subset(
            bucket=bucket, key=key, x=x, y=y, radius=radius, fill=True
        )
        image: torch.Tensor = (TF.to_tensor(pil_image) * 255)
        return image

    def _dl_prediction_v2(self, request_dict: Dict[str, Any]) -> Dict[str, Any]:

        request = types.DeepweedPredictionRequest(**request_dict)

        try:
            LOG.info(f"Attempting prediction: image_id: {request.image_id} model_id: {request.model_id}")

            image = self._get_image_v2(request.image_s3_path)

            LOG.info(f"Image loaded: image_s3_path: {request.image_s3_path}")

            with self.predict_lock:
                model = MODEL_CACHE.get(request.model_id)

                if model is None:
                    model = self._get_model_v2(request.model_s3_path)
                assert isinstance(model, DeepweedModel)

                LOG.info(f"Model loaded: model_id: {request.model_id}")

                padding = 0
                if request.predict_border:
                    padding = int(model.get_discard_points_border_px())
                    image = torch.nn.functional.pad(image, (padding, padding, padding, padding))

                if request.crop_id is not None:
                    set_crop_id_status = model.set_crop_id(request.crop_id)

                    if set_crop_id_status:
                        LOG.info(f"Set crop ID for model: {request.crop_id} for model: {request.model_id}")
                    else:
                        LOG.warn(f"Failed to set crop ID: {request.crop_id} for model: {request.model_id}")

                results = model.infer(
                    image,
                    maka_control_timestamp_ms(),
                    request.ppi,
                    request.threshold,
                    1.0,
                    request.threshold,
                    request.threshold,
                    request.threshold,
                    return_embeddings=True,
                )

                metrics.PREDICTION_COUNTER.inc()
                LOG.info(
                    f"Model inference complete: image_s3_path: {request.image_s3_path} model_id: {request.model_id}"
                )

                save_to_cache(request.model_id, model)

            # Format predicted detection points
            points = []
            for point in results.detections:
                if point.get_hit_class() == HitClass.PLANT:
                    points.append(
                        {
                            "x": point.x - padding,
                            "y": point.y - padding,
                            "radius": point.size,
                            "plant_score": point.plant_score,
                            "weed_score": point.weed_score,
                            "crop_score": point.crop_score,
                            "categories": {
                                d[0]: d[1] for d in point.get_detection_classes(results.weed_detection_classes)
                            },
                            "hit_class": point.get_hit_class(),
                            "embedding": point.embedding.tolist() if point.embedding is not None else None,
                            "reduced_scaled_embedding": point.reduced_scaled_embedding,
                        }
                    )

            # Format predicted segmentation masks
            result_masks: List[Dict[str, Any]] = []

            mask = results.get_mask_numpy_array(results)
            mask = np.moveaxis(mask, (0, 1, 2), (2, 0, 1))

            if mask.size > 0:
                mask_scale = int(image.shape[1] / mask.shape[0])
                padding_mask = padding // mask_scale
                mask = mask[padding_mask : mask.shape[0] - padding_mask, padding_mask : mask.shape[1] - padding_mask]

                is_success, buffer = cv2.imencode(".png", mask)

                if is_success:
                    mask_classes = results.mask_channel_classes
                    mask_class = mask_classes[0] if mask_classes else None
                    mask_bytes = buffer.tobytes()
                    result_masks = [
                        {
                            "width": mask.shape[2],
                            "height": mask.shape[1],
                            "ppi": int(request.ppi / mask_scale),
                            "data": base64.b64encode(mask_bytes).decode("utf-8"),
                            "cls": mask_class,
                        }
                    ]

            response = types.DeepweedPredictionResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                model_id=request.model_id,
                threshold=request.threshold,
                predict_border=request.predict_border,
                ppi=request.ppi,
                points=points,
                masks=result_masks,
                message_version=request.message_version or "v2",
                message_type=request.message_type,
                success=True,
                metadata=request.metadata,
            )
        except Exception as e:
            LOG.warning(f"dl_prediction_v2 failed: {e}")
            traceback.print_exc()
            response = types.DeepweedPredictionResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                model_id=request.model_id,
                threshold=request.threshold,
                predict_border=request.predict_border,
                ppi=request.ppi,
                points=[],
                masks=[],
                message_version=request.message_version or "v2",
                message_type=request.message_type,
                success=False,
                metadata=request.metadata,
            )

        return response.model_dump()

    def _dl_embedding_v1(self, request_dict: Dict[str, Any]) -> Dict[str, Any]:
        request = types.DeepweedEmbeddingRequest(**request_dict)

        try:
            LOG.info(f"Attempting embedding: image_id: {request.image_id} model_id: {request.model_id}")

            x, y = request.x or 0, request.y or 0

            chip = self._get_chip(request.image_s3_path, x, y)

            LOG.info(f"Image loaded: image_s3_path: {request.image_s3_path}")

            with self.predict_lock:
                model = MODEL_CACHE.get(request.model_id)

                if model is None:
                    model = self._get_model_v2(request.model_s3_path)

                assert isinstance(model, DeepweedModel)

                LOG.info(f"Model loaded: model_id: {request.model_id}")

                if request.crop_id is not None:
                    set_crop_id_status = model.set_crop_id(request.crop_id)

                    if set_crop_id_status:
                        LOG.info(f"Set crop ID for model: {request.crop_id} for model: {request.model_id}")
                    else:
                        LOG.warn(f"Failed to set crop ID: {request.crop_id} for model: {request.model_id}")

                if len(chip.shape) == 3:
                    chip = chip.unsqueeze(0)

                embeddings = model.get_embedding(chip, chip.shape[-1] // 2, chip.shape[-2] // 2)
                assert len(embeddings) == 1, "Embedding requested but model does not support embeddings"

                embedding = embeddings[0].embedding.cpu().reshape(-1,)

                metrics.PREDICTION_COUNTER.inc()
                LOG.info(
                    f"Model inference complete: image_s3_path: {request.image_s3_path} model_id: {request.model_id}"
                )

                save_to_cache(request.model_id, model)

            response = types.DeepweedEmbeddingResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                model_id=request.model_id,
                ppi=request.ppi,
                message_version=request.message_version or "v1",
                message_type=request.message_type,
                success=True,
                embedding=embedding.tolist(),
                x=x,
                y=y,
                metadata=request.metadata,
            )
        except Exception as e:
            LOG.warning(f"dl_embedding_v1 failed: {e}")
            traceback.print_exc()
            response = types.DeepweedEmbeddingResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                model_id=request.model_id,
                ppi=request.ppi,
                message_version=request.message_version or "v1",
                message_type=request.message_type,
                success=False,
                metadata=request.metadata,
                embedding=[],
                x=x,
                y=y,
            )

        return response.model_dump()

    def _dl_focus_score_v1(self, request_dict: Dict[str, Any]) -> Dict[str, Any]:
        request = types.DeepweedFocusScoreRequest(**request_dict)

        LOG.info(f"Attempting focus score computation for image ID: {request.image_id}")

        bucket, key = self._s3_cache_proxy_client.split_uri(request.image_s3_path)
        image = self._s3_cache_proxy_client.get_image(bucket, key)

        image_np = np.array(image.convert("L"), dtype=np.float32)

        width, height = image.size

        # Compute FFT magnitude spectrum
        fft_image = np.fft.fft2(image_np)
        fft_spectrum = np.abs(np.fft.fftshift(fft_image))

        # Compute radial distances
        h, w = fft_spectrum.shape
        cy, cx = h // 2, w // 2
        y, x = np.indices((h, w))
        r = np.sqrt((x - cx) ** 2 + (y - cy) ** 2).astype(int)

        # Compute radial sums
        radial_sum = np.bincount(r.ravel(), weights=fft_spectrum.ravel())
        radial_count = np.bincount(r.ravel())

        # Cut off and normalize
        radial_sum = radial_sum[: min(width // 2, height // 2)]
        radial_count = radial_count[: min(width // 2, height // 2)]

        y_values = radial_sum / np.maximum(radial_count, 1)
        y_values = y_values / y_values.mean()

        # Compute true integral: sum weighted by 2πr
        x_values = np.arange(len(y_values)) / len(y_values)

        index = np.argmin(np.abs(x_values - request.sample_frequency))
        score = float(y_values[index])

        if score < 0 or score == float("nan") or score == float("inf") or score == float("-inf"):
            response = types.DeepweedFocusScoreResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                message_type=request.message_type,
                message_version=request.message_version,
                sample_frequency=request.sample_frequency,
                success=False,
                score=None,
            )
        else:
            response = types.DeepweedFocusScoreResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                message_type=request.message_type,
                message_version=request.message_version,
                sample_frequency=request.sample_frequency,
                success=True,
                score=score,
            )

        return response.model_dump()

    def _dl_feature_extractor_embed_v1(self, request_dict: Dict[str, Any]) -> Dict[str, Any]:
        request = types.FeatureExtractorEmbeddingRequest(**request_dict)

        try:
            LOG.info(f"Attempting {request.message_type} embedding computation for image ID: {request.image_id}")

            chip = self._get_chip(request.image_s3_path, request.x, request.y)

            LOG.info(f"Chip loaded: image_s3_path: {request.image_s3_path}")

            with self.predict_lock:
                model = MODEL_CACHE.get(request.model_id)

                if model is None:
                    model = self._get_feature_extractor(request.model_s3_path)
                assert isinstance(model, FeatureExtractor)

                input_size = model.get_input_size()
                chip = TF.resize(chip, input_size)

                chip = chip.to(torch.float32).unsqueeze(0)
                embedding = model.infer(image=chip)
                embedding = embedding.cpu().numpy().reshape(-1,)

                save_to_cache(request.model_id, model, low_queue_priority=True)

            response = types.FeatureExtractorEmbeddingResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                model_id=request.model_id,
                message_version=request.message_version or "v1",
                message_type=request.message_type,
                success=True,
                embedding=embedding.tolist(),
                x=request.x,
                y=request.y,
                metadata=request.metadata,
            )

        except Exception:
            LOG.warning(
                f"Failed {request.message_type} embedding computation for image ID: {request.image_id} with error: {traceback.format_exc()}"
            )
            response = types.FeatureExtractorEmbeddingResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                model_id=request.model_id,
                message_version=request.message_version or "v1",
                message_type=request.message_type,
                success=False,
                embedding=[],
                x=request.x or 0,
                y=request.y or 0,
                metadata=request.metadata,
            )

        return response.model_dump()

    def _dl_comparison_embedding_v1(self, request_dict: Dict[str, Any]) -> Dict[str, Any]:
        request = types.ComparisonEmbeddingRequest(**request_dict)

        LOG.info(f"Attempting comparison embedding computation for image ID: {request.image_id}")

        try:
            chip = self._get_chip(request.image_s3_path, request.x, request.y)

            with self.predict_lock:
                model = MODEL_CACHE.get(request.model_id)

                if model is None:
                    model = self._get_comparison_model(request.model_s3_path)

                assert isinstance(model, ComparisonModel)

                if len(chip.shape) == 3:
                    chip = chip.unsqueeze(0)

                embedding = model.infer(chip, chip.shape[-2] // 2, chip.shape[-1] // 2).embedding[0]

                embedding = embedding.cpu().numpy().reshape(-1,)

                save_to_cache(request.model_id, model, low_queue_priority=True)

            response = types.ComparisonEmbeddingResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                model_id=request.model_id,
                message_version=request.message_version or "v1",
                message_type=request.message_type,
                success=True,
                embedding=embedding.tolist(),
                x=request.x,
                y=request.y,
                metadata=request.metadata,
            )

        except Exception as e:
            LOG.warning(f"dl_comparison_embedding_v1 failed: {e}")
            traceback.print_exc()
            response = types.ComparisonEmbeddingResponse(
                image_id=request.image_id,
                image_s3_path=request.image_s3_path,
                model_id=request.model_id,
                message_version=request.message_version or "v1",
                message_type=request.message_type,
                success=False,
                embedding=[],
                x=request.x,
                y=request.y,
                metadata=request.metadata,
            )

        return response.model_dump()

    @metrics.PREDICT_TIME.time()  # type: ignore
    def dl_prediction(self, request: Dict[str, Any]) -> Dict[str, Any]:

        message_type = request.get("message_type", types.DeepweedRequestType.PREDICTION.value)
        message_version = request.get("message_version", "v1")

        if message_type == types.DeepweedRequestType.PREDICTION.value:  # noqa
            if message_version == "v1":
                response = self._dl_prediction_v1(request)
            elif message_version == "v2":
                response = self._dl_prediction_v2(request)
        elif message_type == types.DeepweedRequestType.EMBEDDING.value:  # noqa
            response = self._dl_embedding_v1(request)
        elif message_type == types.DeepweedRequestType.FOCUS_SCORE.value:  # noqa
            response = self._dl_focus_score_v1(request)
        elif (
            message_type == types.DeepweedRequestType.FEWSHOT_EMBEDDING.value  # noqa
            or message_type == types.DeepweedRequestType.SELFSUP_EMBEDDING.value  # noqa
        ):
            response = self._dl_feature_extractor_embed_v1(request)
        elif message_type == types.DeepweedRequestType.COMPARISON_EMBEDDING.value:  # noqa
            response = self._dl_comparison_embedding_v1(request)
        else:
            raise RuntimeError(f"Request had an unrecognized message type: {message_type}")

        return response


def main() -> None:
    parser = argparse.ArgumentParser("Veselka CV Prediction Server")
    parser.add_argument("queues", type=json.loads)
    parser.add_argument("--debug", action="store_true")

    parser.set_defaults(debug=False)

    args = parser.parse_args()

    if args.debug:
        LOG.setLevel(level=logging.DEBUG)

    counters = {
        "PREDICTION_POSTS_COUNTER": metrics.PREDICTION_POSTS_COUNTER,
        "SQS_RECEIVE_COUNTER": metrics.SQS_RECEIVE_COUNTER,
        "SQS_DELETE_COUNTER": metrics.SQS_DELETE_COUNTER,
    }

    Server.run(args.queues, DeepweedPredictionHandler(Lock()), "/ml/predictions", counters)


if __name__ == "__main__":
    LOG.info(f"Starting server, metrics port: {PORT}")
    start_http_server(PORT)
    main()
