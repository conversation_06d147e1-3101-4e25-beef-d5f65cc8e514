import io
import logging
import os
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Type, Union, cast

import boto3
import cv2
import numpy as np
import numpy.typing as npt
import pandas as pd
import requests
import torch
import torchvision.transforms.functional as TF
from PIL import Image
from sqlalchemy import create_engine, event, func
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from tenacity import retry, stop_after_delay, wait_exponential
from torch.utils.data import Dataset

import deeplearning.comparison.transforms as T
from deeplearning.comparison.data_utils import ComparisonLabels, create_hash_key_for_overlapping_point
from deeplearning.comparison.sampler import (
    ComparisonLabelSampler,
    ComparisonLabelSamplerBasic,
    ComparisonLabelSamplerSource,
)
from deeplearning.utils.dataset import pil_loader
from deeplearning.utils.images import IMAGENET_MEANS, IMAGENET_STDS


class ComparisonImage:
    def __init__(
        self,
        image: Union[npt.NDArray[np.uint8], npt.NDArray[Any], torch.Tensor],
        x: float,
        y: float,
        radius: float,
        id: str,
        label_id: str,
        geohash: Optional[str],
        og_x: float,
        og_y: float,
        og_radius: float,
        og_category: str,
        uuid: str,
        overlap_by_circle: Optional[bool],
        overlap_by_center: Optional[bool],
    ):
        self._image = image
        self._x = x
        self._y = y
        self._radius = radius

        self._recovering_information = {
            "id": id,
            "x": og_x,
            "y": og_y,
            "radius": og_radius,
            "category": og_category,
            "label_id": label_id,
            "geohash": geohash,
            "uuid": uuid,
            "overlap_by_circle": overlap_by_circle,
            "overlap_by_center": overlap_by_center,
        }

    @property
    def image(self) -> Union[npt.NDArray[np.uint8], npt.NDArray[Any], torch.Tensor]:
        return self._image

    @property
    def x(self) -> float:
        return self._x

    @property
    def y(self) -> float:
        return self._y

    @property
    def radius(self) -> float:
        return self._radius

    @property
    def recovery_info(self) -> Dict[str, Any]:
        return self._recovering_information

    def set_image(self, image: Union[npt.NDArray[np.uint8], npt.NDArray[Any], torch.Tensor]) -> None:
        self._image = image

    def set_y_x(self, y: float, x: float) -> None:
        self._y = y
        self._x = x

    def __getstate__(self) -> Dict[str, Any]:
        return {
            "image": cast(torch.Tensor, self._image).numpy() if torch.is_tensor(self._image) else self._image,
            "x": self._x,
            "y": self._y,
            "radius": self._radius,
            "recovering_information": self._recovering_information,
        }

    def __setstate__(self, state: Dict[str, Any]) -> None:
        self._image = (
            torch.tensor(state["image"]) if isinstance(state["image"], np.floating) else TF.to_tensor(state["image"])
        )
        self._x = state["x"]
        self._y = state["y"]
        self._radius = state["radius"]
        self._recovering_information = state["recovering_information"]


class ComparisonLabel:
    def __init__(self, target: float):
        self._target = target

    @property
    def target(self) -> float:
        return self._target


class ComparisonDatapointMetadata:
    def __init__(self, label_id: str, user: str, source: str):
        self._label_id = label_id
        self._user = user
        self._source = source

    @property
    def label_id(self) -> str:
        return self._label_id

    @property
    def user(self) -> str:
        return self._user

    @property
    def source(self) -> str:
        return self._source


class ComparisonDatapoint:
    def __init__(
        self,
        image_one: ComparisonImage,
        image_two: ComparisonImage,
        label: ComparisonLabel,
        metadata: ComparisonDatapointMetadata,
    ):
        self._image_one = image_one
        self._image_two = image_two
        self._label = label
        self._metadata = metadata

    @property
    def image_one(self) -> ComparisonImage:
        return self._image_one

    @property
    def image_two(self) -> ComparisonImage:
        return self._image_two

    @property
    def label(self) -> ComparisonLabel:
        return self._label

    @property
    def metadata(self) -> ComparisonDatapointMetadata:
        return self._metadata


def get_transforms(training: bool = False, extra_augmentations: bool = False) -> T.Compose:
    transforms: List[Any] = []

    if training:
        transforms += [T.RandomRotation()]

        if extra_augmentations:
            transforms += [
                T.RandomChoice(
                    [
                        T.ColorJitter(brightness=(0.5, 1.5), gamma=(0.83, 1.2), saturation=(0.75, 1.5), hue=(0, 0)),
                        T.GaussianNoise(stddev=0.03),
                        T.RandomVerticalFlip(p=1.0),
                        T.RandomHorizontalFlip(p=1.0),
                        T.Compose([T.RandomVerticalFlip(p=1.0), T.RandomHorizontalFlip(p=1.0)]),
                        T.GaussianBlur(3, (0.1, 1.0)),
                    ]
                )
            ]

    transforms += [T.Normalize(mean=IMAGENET_MEANS[:3], std=IMAGENET_STDS[:3])]

    return T.Compose(transforms)


class ComparisonDataset(Dataset[ComparisonDatapoint]):
    def __init__(
        self,
        label_database_path: str,
        model_dir: str,
        image_id_to_url: Dict[str, str],
        image_id_to_geohash: Dict[str, Optional[str]],
        ids_in_dataset: List[str],
        source_weights: Dict[str, float],
        source_sampler: Type[ComparisonLabelSampler],
        phase: str,
        num_balanced_examples: Optional[int] = None,
        image_radius: int = 200,
        training: bool = False,
        mask_around_plant: bool = False,
        sampler: Type[ComparisonLabelSampler] = ComparisonLabelSamplerBasic,
        extra_augmentations: bool = False,
        crop_on_server: bool = True,
        label_id_to_uuids: Optional[Dict[str, Any]] = None,
        image_id_to_height_width: Optional[Dict[str, Tuple[int, int]]] = None,
        overlapped_points_dict: Optional[Dict[str, Dict[str, Dict[Tuple[float, float, str], bool]]]] = None,
    ) -> None:
        label_id_to_uuids = label_id_to_uuids if label_id_to_uuids is not None else {}
        image_id_to_height_width = image_id_to_height_width if image_id_to_height_width is not None else {}
        self._image_id_to_url = image_id_to_url
        self._ids_in_dataset = ids_in_dataset
        self._phase = phase
        self._num_balanced_examples = num_balanced_examples
        self._image_radius = image_radius
        self._initial_cropping_radius = int(1.5 * self._image_radius)
        self._crop_on_server = crop_on_server
        self._mask_around_plant = mask_around_plant
        self._image_id_to_geohash = image_id_to_geohash
        self._label_id_to_uuids = label_id_to_uuids
        self._image_id_to_height_width = image_id_to_height_width

        self._carbon_cache_host: Optional[str] = None

        self._latest_image: Dict[str, Any] = {"filepath": None, "image": None}

        self._overlap_points_dict_by_circle = (
            overlapped_points_dict["circle"] if overlapped_points_dict is not None else None
        )
        self._overlap_points_dict_by_center = (
            overlapped_points_dict["center"] if overlapped_points_dict is not None else None
        )

        if os.getenv("DISABLE_S3_CACHE_PROXY"):
            logging.warning("Running without s3 cache proxy!")
        else:
            self._carbon_cache_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")

        if self._carbon_cache_host is None:
            self._tmp_data = "/data/temp_stream_for_datasets"
            try:
                os.mkdir(self._tmp_data)
            except FileExistsError:
                pass

        self._label_database_path = label_database_path
        self._model_dir = model_dir
        os.makedirs(self._model_dir, exist_ok=True)
        self._cached_sessionmaker = None

        comparison_labels = self.create_comparison_labels()

        self._sampler = (
            sampler(comparison_labels, image_id_to_geohash, source_weights, source_sampler)
            if issubclass(sampler, ComparisonLabelSamplerSource)
            else sampler(comparison_labels, image_id_to_geohash)
        )

        if torch.distributed.get_rank() == 0:
            logging.info(f"Dataset sampling breakdown\n{self._sampler}")
        self._transforms = get_transforms(training=training, extra_augmentations=extra_augmentations)

    def __getstate__(self) -> Dict[str, Any]:
        state = self.__dict__.copy()
        # Don't pickle sessionmaker
        state["_cached_sessionmaker"] = None
        return state

    @property
    def _sessionmaker(self) -> sessionmaker:
        if self._cached_sessionmaker is None:
            engine = create_engine(f"sqlite:///{self._label_database_path}?_pragma=busy_timeout(5000)")

            def _fk_pragma_on_connect(dbapi_connection: Engine, connection_record: Any) -> None:
                dbapi_connection.execute("pragma foreign_keys=ON")

            event.listen(engine, "connect", _fk_pragma_on_connect)

            self._cached_sessionmaker = sessionmaker(engine)
        return self._cached_sessionmaker

    @property
    def sampler_breakdown(self) -> str:
        return str(self._sampler)

    def update_probabilities(self, results: List[Dict[str, Any]]) -> None:
        self._sampler.update_probabilities(results)

    @property
    def image_radius(self) -> int:
        return self._image_radius

    @property
    def model_ppi(self) -> int:
        return 200

    def sample_id(self) -> str:
        # TODO sample better?
        id = self._sampler.query()
        with self._sessionmaker() as session:
            item = (
                session.query(ComparisonLabels).filter(ComparisonLabels.id.like(id)).order_by(func.random())
            ).first()

        return cast(str, item.id)

    def __len__(self) -> int:
        if self._num_balanced_examples is not None:
            return self._num_balanced_examples
        return len(self._ids_in_dataset)

    def __getitem__(self, index: int) -> ComparisonDatapoint:
        if self._num_balanced_examples is not None:
            id = self.sample_id()
        else:
            id = self._ids_in_dataset[index]

        with self._sessionmaker() as session:
            # [Raven] Weirdness but doing = for some reason doesn't always return the row. In the db I compared results of = <id> and like <id> on an id
            # that didn't seem to return an object here. Oddly enough, like always returned the object but = did not.
            comparison_label_item = session.query(ComparisonLabels).filter(ComparisonLabels.id.like(id)).first()

        comparison_label_dict = comparison_label_item.to_dict()

        x_one_label = comparison_label_dict["label_one_x"]
        y_one_label = comparison_label_dict["label_one_y"]

        height_width_1 = self._image_id_to_height_width[comparison_label_dict["image_one"]]
        image_one, x_one_transformed, y_one_transformed = self._load_image(
            self._image_id_to_url[comparison_label_dict["image_one"]],
            x_one_label,
            y_one_label,
            height_width=height_width_1,
            cropping_radius=self._initial_cropping_radius if self._crop_on_server else None,
        )
        image_one = self._pad_and_initial_crop(
            image_one, x_one_transformed, y_one_transformed, comparison_label_dict["label_one_radius"],
        )

        x_two_label = comparison_label_dict["label_two_x"]
        y_two_label = comparison_label_dict["label_two_y"]

        height_width_2 = self._image_id_to_height_width[comparison_label_dict["image_two"]]
        image_two, x_two_transformed, y_two_transformed = self._load_image(
            self._image_id_to_url[comparison_label_dict["image_two"]],
            x_two_label,
            y_two_label,
            height_width=height_width_2,
            cropping_radius=self._initial_cropping_radius if self._crop_on_server else None,
        )
        image_two = self._pad_and_initial_crop(
            image_two, x_two_transformed, y_two_transformed, comparison_label_dict["label_two_radius"],
        )

        overlap_hash_key_1 = create_hash_key_for_overlapping_point(
            x=comparison_label_dict["label_one_x"],
            y=comparison_label_dict["label_one_y"],
            category=comparison_label_dict["label_one_category"],
        )
        overlap_hash_key_2 = create_hash_key_for_overlapping_point(
            x=comparison_label_dict["label_two_x"],
            y=comparison_label_dict["label_two_y"],
            category=comparison_label_dict["label_two_category"],
        )

        overlap_by_circle_1 = (
            self._overlap_points_dict_by_circle.get(comparison_label_dict["image_one"], {}).get(
                overlap_hash_key_1, None
            )
            if self._overlap_points_dict_by_circle is not None
            else None
        )
        overlap_by_circle_2 = (
            self._overlap_points_dict_by_circle.get(comparison_label_dict["image_two"], {}).get(
                overlap_hash_key_2, None
            )
            if self._overlap_points_dict_by_circle is not None
            else None
        )
        overlap_by_center_1 = (
            self._overlap_points_dict_by_center.get(comparison_label_dict["image_one"], {}).get(
                overlap_hash_key_1, None
            )
            if self._overlap_points_dict_by_center is not None
            else None
        )
        overlap_by_center_2 = (
            self._overlap_points_dict_by_center.get(comparison_label_dict["image_two"], {}).get(
                overlap_hash_key_2, None
            )
            if self._overlap_points_dict_by_center is not None
            else None
        )

        comparison_image_one = ComparisonImage(
            image=image_one,
            x=self._initial_cropping_radius,
            y=self._initial_cropping_radius,
            radius=self._image_radius,
            id=comparison_label_dict["image_one"],
            label_id=comparison_label_dict["label_one"],
            geohash=self._image_id_to_geohash.get(comparison_label_dict["image_one"]),
            og_x=comparison_label_dict["label_one_x"],
            og_y=comparison_label_dict["label_one_y"],
            og_radius=comparison_label_dict["label_one_radius"],
            og_category=comparison_label_dict["label_one_category"],
            uuid=self._label_id_to_uuids.get(id, {}).get("label_one_uuid"),
            overlap_by_circle=overlap_by_circle_1,
            overlap_by_center=overlap_by_center_1,
        )
        comparison_image_two = ComparisonImage(
            image=image_two,
            x=self._initial_cropping_radius,
            y=self._initial_cropping_radius,
            radius=self._image_radius,
            id=comparison_label_dict["image_two"],
            label_id=comparison_label_dict["label_two"],
            geohash=self._image_id_to_geohash.get(comparison_label_dict["image_two"]),
            og_x=comparison_label_dict["label_two_x"],
            og_y=comparison_label_dict["label_two_y"],
            og_radius=comparison_label_dict["label_two_radius"],
            og_category=comparison_label_dict["label_two_category"],
            uuid=self._label_id_to_uuids.get(id, {}).get("label_two_uuid"),
            overlap_by_circle=overlap_by_circle_2,
            overlap_by_center=overlap_by_center_2,
        )

        # 0 will mean distant, 1 will mean similar
        comparison_label = ComparisonLabel(target=float(max(0, min(1, float(comparison_label_dict["match"])))))

        comparison_metadata = ComparisonDatapointMetadata(
            label_id=comparison_label_dict["id"],
            user=comparison_label_dict["user"],
            source=comparison_label_dict["source"],
        )

        return ComparisonDatapoint(
            image_one=comparison_image_one,
            image_two=comparison_image_two,
            label=comparison_label,
            metadata=comparison_metadata,
        )

    @property
    def image_ids_to_urls(self) -> Dict[str, str]:
        return self._image_id_to_url

    @retry(wait=wait_exponential(multiplier=1, min=60, max=5 * 60), stop=stop_after_delay(30 * 60))
    def _get_image_from_cache(
        self,
        image_s3_path: str,
        x: float,
        y: float,
        height_width: Tuple[int, int],
        cropping_radius: Optional[float] = None,
        break_cache: bool = False,
        bucket_name: str = "maka-pono",
    ) -> Tuple[requests.Response, float, float, str]:
        request_str = f"http://{self._carbon_cache_host}/{bucket_name}/{image_s3_path}"
        return_x = x
        return_y = y
        query_params = ""

        if cropping_radius is not None:
            max_height, max_width = height_width
            # TODO make this simpler
            x_origin = int(max(0, min(max(x - cropping_radius, 0), max_width - 2 * cropping_radius - 1)))
            y_origin = int(max(0, min(max(y - cropping_radius, 0), max_height - 2 * cropping_radius - 1,)))

            return_x = x - x_origin
            return_y = y - y_origin

            height = int(2 * cropping_radius)
            width = int(2 * cropping_radius)
            query_params += f"x={x_origin}&y={y_origin}&height={height}&width={width}"
            if break_cache:
                query_params += "break=1"
        if len(query_params) > 0:
            request_str += f"?{query_params}"

        response = requests.get(request_str, timeout=30,)
        assert response.ok, f"Request {request_str} failed, {response.status_code}"
        return (response, return_x, return_y, request_str)

    def _fetch_and_load_image(
        self,
        image_s3_path: str,
        x: float,
        y: float,
        cropping_radius: Optional[float],
        bucket_name: str,
        height_width: Tuple[int, int],
    ) -> Tuple[npt.NDArray[Any], float, float]:
        num_iterations = 2
        break_cache = False
        request_str = ""
        for i in range(num_iterations):
            if i == num_iterations - 1:
                break_cache = cropping_radius is not None
            try:
                response, x, y, request_str = self._get_image_from_cache(
                    image_s3_path,
                    x,
                    y,
                    height_width=height_width,
                    cropping_radius=cropping_radius,
                    break_cache=break_cache,
                    bucket_name=bucket_name,
                )
                image = np.array(Image.open(io.BytesIO(response.content), formats=["png"]).convert("RGB"))
                return image, x, y
            except Exception as e:
                logging.warning(f"Couldn't download/load image {request_str}: {e}")

        raise Exception(f"Couldn't download and load image {image_s3_path}")

    def _load_image(
        self, image_url: str, x: float, y: float, height_width: Tuple[int, int], cropping_radius: Optional[float] = None
    ) -> Tuple[npt.NDArray[Any], float, float]:
        if (
            cropping_radius is None
            and self._latest_image["filepath"] is not None
            and self._latest_image["filepath"] == image_url
        ):
            return self._latest_image["image"], x, y

        if image_url.startswith("s3"):
            assert (
                "media" in image_url or "predict_burst" in image_url or "chip_image" in image_url
            ), f"Unexpected url {image_url}"
            split_s3_path = image_url.split("/")
            bucket_name = None
            directory_name = None
            if "media" in image_url:
                bucket_name = "maka-pono"
                directory_name = "media"
            elif "predict_burst" in image_url:
                bucket_name = "carbon-images"
                directory_name = "predict_burst"
            elif "chip_image" in image_url:
                bucket_name = "carbon-images"
                directory_name = "chip_image"
            assert bucket_name is not None and directory_name is not None, f"FAILED: {image_url}"
            split_index = split_s3_path.index(directory_name)
            relative_image_path = os.path.join(*split_s3_path[split_index + 1 :])
            image_s3_path = os.path.join(directory_name, relative_image_path)
            if self._carbon_cache_host is not None:
                image, x, y = self._fetch_and_load_image(
                    image_s3_path, x, y, cropping_radius, bucket_name, height_width
                )
            else:
                boto3.resource("s3").Bucket(bucket_name).download_file(
                    image_s3_path, os.path.join(self._tmp_data, split_s3_path[-1])
                )
                image = np.array(pil_loader(os.path.join(self._tmp_data, split_s3_path[-1])))
        else:
            image = np.array(pil_loader(image_url))

        if cropping_radius is None:
            self._latest_image["filepath"] = image_url
            self._latest_image["image"] = image
        return image, x, y

    def _pad_and_initial_crop(self, image: npt.NDArray[Any], x: float, y: float, radius: float) -> npt.NDArray[Any]:
        padded_image_og = np.pad(
            image,
            [
                (self._initial_cropping_radius, self._initial_cropping_radius),
                (self._initial_cropping_radius, self._initial_cropping_radius),
                (0, 0),
            ],
            mode="constant",
        )
        padded_x = int(x + self._initial_cropping_radius)
        padded_y = int(y + self._initial_cropping_radius)
        padded_image: npt.NDArray[Any] = padded_image_og[
            padded_y - self._initial_cropping_radius : padded_y + self._initial_cropping_radius,
            padded_x - self._initial_cropping_radius : padded_x + self._initial_cropping_radius,
            :,
        ]

        if self._mask_around_plant:
            mask = np.zeros(padded_image.shape, dtype=padded_image.dtype)
            mask = cv2.circle(
                mask, (self._initial_cropping_radius, self._initial_cropping_radius), int(radius), (1, 1, 1), -1
            )
            padded_image = mask * padded_image

        return padded_image

    def preprocess_datapoint(self, image: torch.Tensor, x: float, y: float) -> torch.Tensor:
        img = self._transforms(image)

        # crop image
        left = int(x - self._image_radius)
        right = int(x + self._image_radius)
        top = int(y - self._image_radius)
        bottom = int(y + self._image_radius)
        cropped_image = img[:, top:bottom, left:right]

        return cropped_image

    def crop_image_by_image_id(
        self, image_id: str, x: float, y: float, r: float, cache_cropped_image: bool
    ) -> npt.NDArray[Any]:
        assert image_id in self._image_id_to_url, f"Image id {image_id} not in dataset"

        height_width = self._image_id_to_height_width[image_id]

        img, x, y = self._load_image(
            self._image_id_to_url[image_id],
            x,
            y,
            height_width=height_width,
            cropping_radius=self._initial_cropping_radius if cache_cropped_image else None,
        )

        return self._pad_and_initial_crop(img, x, y, r)

    def create_comparison_labels(self,) -> List[ComparisonLabels]:
        comparison_labels = []
        increment = 100000

        with self._sessionmaker() as session:
            for i in range(0, len(self._ids_in_dataset), increment):
                comp_labels = (
                    session.query(ComparisonLabels)
                    .filter(ComparisonLabels.id.in_(self._ids_in_dataset[i : i + increment]))
                    .all()
                )
                if comp_labels is not None:
                    comparison_labels.extend(comp_labels)

        saved_df = pd.DataFrame([label.to_dict() for label in comparison_labels])
        saved_df.to_csv(os.path.join(self._model_dir, f"{self._phase}.csv"), index=False)

        return comparison_labels


class ComparisonDatasets:
    def __init__(
        self,
        train_dataset: ComparisonDataset,
        validation_dataset: ComparisonDataset,
        test_dataset: ComparisonDataset,
        calibration_dataset: ComparisonDataset,
    ):
        self._train_dataset = train_dataset
        self._validation_dataset = validation_dataset
        self._test_dataset = test_dataset
        self._calibration_dataset = calibration_dataset

    def get_training(self) -> ComparisonDataset:
        return self._train_dataset

    def get_validation(self) -> ComparisonDataset:
        return self._validation_dataset

    def get_test(self) -> ComparisonDataset:
        return self._test_dataset

    def get_calibration(self) -> ComparisonDataset:
        return self._calibration_dataset


class DatasetType(Enum):
    TRAIN = 1
    TEST = 2
    VALIDATION = 3


def transform_batch(batch: List[ComparisonDatapoint], dataset: ComparisonDataset) -> List[ComparisonDatapoint]:
    for datapoint in batch:
        image_one = datapoint.image_one
        image_two = datapoint.image_two

        transformed_and_cropped_image_one = dataset.preprocess_datapoint(
            cast(torch.Tensor, image_one.image).cuda(), image_one.x, image_one.y
        )
        transformed_and_cropped_image_two = dataset.preprocess_datapoint(
            cast(torch.Tensor, image_two.image).cuda(), image_two.x, image_two.y
        )
        datapoint.image_one.set_image(transformed_and_cropped_image_one)
        datapoint.image_two.set_image(transformed_and_cropped_image_two)
        datapoint.image_one.set_y_x(
            transformed_and_cropped_image_one.shape[1] // 2, transformed_and_cropped_image_one.shape[2] // 2
        )
        datapoint.image_two.set_y_x(
            transformed_and_cropped_image_two.shape[1] // 2, transformed_and_cropped_image_two.shape[2] // 2
        )

    return batch
