// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: golang/simulator/hardware/proto/sim_UI/sim_UI.proto

package sim_UI

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetVelocityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetVelocityRequest) Reset() {
	*x = GetVelocityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVelocityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVelocityRequest) ProtoMessage() {}

func (x *GetVelocityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVelocityRequest.ProtoReflect.Descriptor instead.
func (*GetVelocityRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{0}
}

type GetVelocityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Velocity float32 `protobuf:"fixed32,1,opt,name=velocity,proto3" json:"velocity,omitempty"`
}

func (x *GetVelocityResponse) Reset() {
	*x = GetVelocityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVelocityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVelocityResponse) ProtoMessage() {}

func (x *GetVelocityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVelocityResponse.ProtoReflect.Descriptor instead.
func (*GetVelocityResponse) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{1}
}

func (x *GetVelocityResponse) GetVelocity() float32 {
	if x != nil {
		return x.Velocity
	}
	return 0
}

type SetVelocityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Velocity float32 `protobuf:"fixed32,1,opt,name=velocity,proto3" json:"velocity,omitempty"`
}

func (x *SetVelocityRequest) Reset() {
	*x = SetVelocityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetVelocityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetVelocityRequest) ProtoMessage() {}

func (x *SetVelocityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetVelocityRequest.ProtoReflect.Descriptor instead.
func (*SetVelocityRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{2}
}

func (x *SetVelocityRequest) GetVelocity() float32 {
	if x != nil {
		return x.Velocity
	}
	return 0
}

type SetVelocityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetVelocityResponse) Reset() {
	*x = SetVelocityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetVelocityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetVelocityResponse) ProtoMessage() {}

func (x *SetVelocityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetVelocityResponse.ProtoReflect.Descriptor instead.
func (*SetVelocityResponse) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{3}
}

type GetPresetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetPresetsRequest) Reset() {
	*x = GetPresetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPresetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresetsRequest) ProtoMessage() {}

func (x *GetPresetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresetsRequest.ProtoReflect.Descriptor instead.
func (*GetPresetsRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{4}
}

// [parents..., leaf], value
type PresetSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path  []string `protobuf:"bytes,1,rep,name=path,proto3" json:"path,omitempty"`
	Value string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PresetSetting) Reset() {
	*x = PresetSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PresetSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetSetting) ProtoMessage() {}

func (x *PresetSetting) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetSetting.ProtoReflect.Descriptor instead.
func (*PresetSetting) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{5}
}

func (x *PresetSetting) GetPath() []string {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *PresetSetting) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// [Setting, Setting, Setting...]
type GetPresetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Settings []*PresetSetting `protobuf:"bytes,1,rep,name=settings,proto3" json:"settings,omitempty"`
}

func (x *GetPresetsResponse) Reset() {
	*x = GetPresetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPresetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresetsResponse) ProtoMessage() {}

func (x *GetPresetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresetsResponse.ProtoReflect.Descriptor instead.
func (*GetPresetsResponse) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{6}
}

func (x *GetPresetsResponse) GetSettings() []*PresetSetting {
	if x != nil {
		return x.Settings
	}
	return nil
}

type SetPresetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Setting *PresetSetting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *SetPresetRequest) Reset() {
	*x = SetPresetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPresetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPresetRequest) ProtoMessage() {}

func (x *SetPresetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPresetRequest.ProtoReflect.Descriptor instead.
func (*SetPresetRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{7}
}

func (x *SetPresetRequest) GetSetting() *PresetSetting {
	if x != nil {
		return x.Setting
	}
	return nil
}

type SetPresetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetPresetResponse) Reset() {
	*x = SetPresetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPresetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPresetResponse) ProtoMessage() {}

func (x *SetPresetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPresetResponse.ProtoReflect.Descriptor instead.
func (*SetPresetResponse) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{8}
}

type DiagnosticSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentDiagnostic string   `protobuf:"bytes,1,opt,name=current_diagnostic,json=currentDiagnostic,proto3" json:"current_diagnostic,omitempty"`
	Diagnostics       []string `protobuf:"bytes,2,rep,name=diagnostics,proto3" json:"diagnostics,omitempty"`
	Row               int64    `protobuf:"varint,3,opt,name=row,proto3" json:"row,omitempty"`
	Ticket            int64    `protobuf:"varint,5,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *DiagnosticSettings) Reset() {
	*x = DiagnosticSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiagnosticSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiagnosticSettings) ProtoMessage() {}

func (x *DiagnosticSettings) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiagnosticSettings.ProtoReflect.Descriptor instead.
func (*DiagnosticSettings) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{9}
}

func (x *DiagnosticSettings) GetCurrentDiagnostic() string {
	if x != nil {
		return x.CurrentDiagnostic
	}
	return ""
}

func (x *DiagnosticSettings) GetDiagnostics() []string {
	if x != nil {
		return x.Diagnostics
	}
	return nil
}

func (x *DiagnosticSettings) GetRow() int64 {
	if x != nil {
		return x.Row
	}
	return 0
}

func (x *DiagnosticSettings) GetTicket() int64 {
	if x != nil {
		return x.Ticket
	}
	return 0
}

type GetDiagnosticSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ticket int64 `protobuf:"varint,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *GetDiagnosticSettingsRequest) Reset() {
	*x = GetDiagnosticSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiagnosticSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiagnosticSettingsRequest) ProtoMessage() {}

func (x *GetDiagnosticSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiagnosticSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetDiagnosticSettingsRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{10}
}

func (x *GetDiagnosticSettingsRequest) GetTicket() int64 {
	if x != nil {
		return x.Ticket
	}
	return 0
}

type GetDiagnosticSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Settings *DiagnosticSettings `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *GetDiagnosticSettingsResponse) Reset() {
	*x = GetDiagnosticSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiagnosticSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiagnosticSettingsResponse) ProtoMessage() {}

func (x *GetDiagnosticSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiagnosticSettingsResponse.ProtoReflect.Descriptor instead.
func (*GetDiagnosticSettingsResponse) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{11}
}

func (x *GetDiagnosticSettingsResponse) GetSettings() *DiagnosticSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

type SetDiagnosticSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentDiagnostic string `protobuf:"bytes,1,opt,name=current_diagnostic,json=currentDiagnostic,proto3" json:"current_diagnostic,omitempty"`
	Row               int64  `protobuf:"varint,2,opt,name=row,proto3" json:"row,omitempty"`
}

func (x *SetDiagnosticSettingsRequest) Reset() {
	*x = SetDiagnosticSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDiagnosticSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDiagnosticSettingsRequest) ProtoMessage() {}

func (x *SetDiagnosticSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDiagnosticSettingsRequest.ProtoReflect.Descriptor instead.
func (*SetDiagnosticSettingsRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{12}
}

func (x *SetDiagnosticSettingsRequest) GetCurrentDiagnostic() string {
	if x != nil {
		return x.CurrentDiagnostic
	}
	return ""
}

func (x *SetDiagnosticSettingsRequest) GetRow() int64 {
	if x != nil {
		return x.Row
	}
	return 0
}

type SetDiagnosticSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetDiagnosticSettingsResponse) Reset() {
	*x = SetDiagnosticSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDiagnosticSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDiagnosticSettingsResponse) ProtoMessage() {}

func (x *SetDiagnosticSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDiagnosticSettingsResponse.ProtoReflect.Descriptor instead.
func (*SetDiagnosticSettingsResponse) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP(), []int{13}
}

var File_golang_simulator_hardware_proto_sim_UI_sim_UI_proto protoreflect.FileDescriptor

var file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDesc = []byte{
	0x0a, 0x33, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x2f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x22, 0x14, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x31, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x22, 0x30, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x6c,
	0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x76, 0x65, 0x6c,
	0x6f, 0x63, 0x69, 0x74, 0x79, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x13, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x39, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x54, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x22, 0x50, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x50, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x22, 0x13, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x12, 0x44, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x2d, 0x0a, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x72, 0x6f, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x36, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x22, 0x64, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x44, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52,
	0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x5f, 0x0a, 0x1c, 0x53, 0x65, 0x74,
	0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x72, 0x6f, 0x77, 0x22, 0x1f, 0x0a, 0x1d, 0x53, 0x65,
	0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xb0, 0x05, 0x0a, 0x12,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x49, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x6a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x47, 0x65,
	0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x62,
	0x0a, 0x0b, 0x53, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x27, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74,
	0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x66, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x55, 0x49, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x5c, 0x0a, 0x09, 0x53, 0x65,
	0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65,
	0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x15,
	0x53, 0x65, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74, 0x44,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x49, 0x2e, 0x53,
	0x65, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0e,
	0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescOnce sync.Once
	file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescData = file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDesc
)

func file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescGZIP() []byte {
	file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescOnce.Do(func() {
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescData = protoimpl.X.CompressGZIP(file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescData)
	})
	return file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDescData
}

var file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_goTypes = []interface{}{
	(*GetVelocityRequest)(nil),            // 0: carbon.simulator_UI.GetVelocityRequest
	(*GetVelocityResponse)(nil),           // 1: carbon.simulator_UI.GetVelocityResponse
	(*SetVelocityRequest)(nil),            // 2: carbon.simulator_UI.SetVelocityRequest
	(*SetVelocityResponse)(nil),           // 3: carbon.simulator_UI.SetVelocityResponse
	(*GetPresetsRequest)(nil),             // 4: carbon.simulator_UI.GetPresetsRequest
	(*PresetSetting)(nil),                 // 5: carbon.simulator_UI.PresetSetting
	(*GetPresetsResponse)(nil),            // 6: carbon.simulator_UI.GetPresetsResponse
	(*SetPresetRequest)(nil),              // 7: carbon.simulator_UI.SetPresetRequest
	(*SetPresetResponse)(nil),             // 8: carbon.simulator_UI.SetPresetResponse
	(*DiagnosticSettings)(nil),            // 9: carbon.simulator_UI.DiagnosticSettings
	(*GetDiagnosticSettingsRequest)(nil),  // 10: carbon.simulator_UI.GetDiagnosticSettingsRequest
	(*GetDiagnosticSettingsResponse)(nil), // 11: carbon.simulator_UI.GetDiagnosticSettingsResponse
	(*SetDiagnosticSettingsRequest)(nil),  // 12: carbon.simulator_UI.SetDiagnosticSettingsRequest
	(*SetDiagnosticSettingsResponse)(nil), // 13: carbon.simulator_UI.SetDiagnosticSettingsResponse
}
var file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_depIdxs = []int32{
	5,  // 0: carbon.simulator_UI.GetPresetsResponse.settings:type_name -> carbon.simulator_UI.PresetSetting
	5,  // 1: carbon.simulator_UI.SetPresetRequest.setting:type_name -> carbon.simulator_UI.PresetSetting
	9,  // 2: carbon.simulator_UI.GetDiagnosticSettingsResponse.settings:type_name -> carbon.simulator_UI.DiagnosticSettings
	0,  // 3: carbon.simulator_UI.SimulatorUIService.GetVelocityStream:input_type -> carbon.simulator_UI.GetVelocityRequest
	2,  // 4: carbon.simulator_UI.SimulatorUIService.SetVelocity:input_type -> carbon.simulator_UI.SetVelocityRequest
	4,  // 5: carbon.simulator_UI.SimulatorUIService.GetPresetStream:input_type -> carbon.simulator_UI.GetPresetsRequest
	7,  // 6: carbon.simulator_UI.SimulatorUIService.SetPreset:input_type -> carbon.simulator_UI.SetPresetRequest
	10, // 7: carbon.simulator_UI.SimulatorUIService.GetDiagnosticSettings:input_type -> carbon.simulator_UI.GetDiagnosticSettingsRequest
	12, // 8: carbon.simulator_UI.SimulatorUIService.SetDiagnosticSettings:input_type -> carbon.simulator_UI.SetDiagnosticSettingsRequest
	1,  // 9: carbon.simulator_UI.SimulatorUIService.GetVelocityStream:output_type -> carbon.simulator_UI.GetVelocityResponse
	3,  // 10: carbon.simulator_UI.SimulatorUIService.SetVelocity:output_type -> carbon.simulator_UI.SetVelocityResponse
	6,  // 11: carbon.simulator_UI.SimulatorUIService.GetPresetStream:output_type -> carbon.simulator_UI.GetPresetsResponse
	8,  // 12: carbon.simulator_UI.SimulatorUIService.SetPreset:output_type -> carbon.simulator_UI.SetPresetResponse
	11, // 13: carbon.simulator_UI.SimulatorUIService.GetDiagnosticSettings:output_type -> carbon.simulator_UI.GetDiagnosticSettingsResponse
	13, // 14: carbon.simulator_UI.SimulatorUIService.SetDiagnosticSettings:output_type -> carbon.simulator_UI.SetDiagnosticSettingsResponse
	9,  // [9:15] is the sub-list for method output_type
	3,  // [3:9] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_init() }
func file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_init() {
	if File_golang_simulator_hardware_proto_sim_UI_sim_UI_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVelocityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVelocityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetVelocityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetVelocityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPresetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PresetSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPresetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPresetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPresetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiagnosticSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiagnosticSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiagnosticSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDiagnosticSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDiagnosticSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_goTypes,
		DependencyIndexes: file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_depIdxs,
		MessageInfos:      file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_msgTypes,
	}.Build()
	File_golang_simulator_hardware_proto_sim_UI_sim_UI_proto = out.File
	file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_rawDesc = nil
	file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_goTypes = nil
	file_golang_simulator_hardware_proto_sim_UI_sim_UI_proto_depIdxs = nil
}
