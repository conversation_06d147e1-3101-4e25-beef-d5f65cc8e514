package services

import (
	"context"
	"errors"
	"fmt"
	"io"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/metrics_aggregator"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const useDefault = -1
const laserPowerLowerBound = 0 // in watts
const laserPowerUpperBound = 1000

type LaserService struct {
	frontend.UnimplementedLaserServiceServer
	scState       *state.OverallScannerState
	scWatcher     *state.ScannerWatcher
	stopCtx       context.Context
	rows          map[int]*rows.RowClients
	redisClient   *redis.Client
	metricsClient *metrics_aggregator.MetricsAggregatorClient
	configClient  *config.ConfigClient
	robot         environment.Robot
	// TODO(jfroel): add logging module
}

func NewLaserService(grpcServer *grpc.Server, stopCtx context.Context, scState *state.OverallScannerState, scWatcher *state.ScannerWatcher, rowClients map[int]*rows.RowClients, redisClient *redis.Client, metricsClient *metrics_aggregator.MetricsAggregatorClient, configClient *config.ConfigClient, robot environment.Robot) *LaserService {
	service := &LaserService{
		stopCtx:       stopCtx,
		scState:       scState,
		scWatcher:     scWatcher,
		rows:          rowClients,
		redisClient:   redisClient,
		metricsClient: metricsClient,
		configClient:  configClient,
		robot:         robot,
	}
	frontend.RegisterLaserServiceServer(grpcServer, service)
	return service
}

func (s *LaserService) FireLaser(stream frontend.LaserService_FireLaserServer) error {
	var requests chan *frontend.CameraRequest = make(chan *frontend.CameraRequest)
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		for {
			req, err := stream.Recv()
			if errors.Is(err, io.EOF) {
				close(requests)
				return
			} else if err != nil {
				close(requests)
				logrus.Errorf("Error Received in Fire Laser Stream: %s", err)
				return
			}
			requests <- req
		}
	}()

	var running bool = true
	var camId string = ""

	for running {
		select {
		case <-s.stopCtx.Done():
			return status.Error(codes.Aborted, "Service Stopped before Completion")
		case <-time.After(time.Second * 1): // TODO Make this time configurable
			logrus.Error("Fire Laser Aborted After Client Stream Timeout")
			return status.Error(codes.Aborted, "Timeout between Laser Fire Requests")
		case req := <-requests:
			if req == nil {
				logrus.Info("Stopping Laser Firing")
				running = false
			} else {
				logrus.Info("Fire Laser!")
				if camId == "" {
					camId = req.GetCamId()
					defer func() {
						if rowNum, targetId, ok := s.scState.CamIdToRowTargetId(camId); ok {
							_, err := s.rows[int(rowNum)].AimbotClient.LaserFire(targetId, false)
							if err != nil {
								logrus.Errorf("Failed to stop laser %s", camId)
							}
						} else {
							logrus.Errorf("Failed to find laser for cam id %s", camId)
						}
					}()
				}
				if rowNum, targetId, ok := s.scState.CamIdToRowTargetId(camId); ok {
					_, err := s.rows[int(rowNum)].AimbotClient.LaserFire(targetId, true)
					if err != nil {
						logrus.Errorf("Failed to fire laser %s, err: %v", camId, err)
						running = false
					}
				} else {
					logrus.Errorf("Failed to find laser for cam id %s", camId)
					running = false
				}
			}
		}
	}

	stream.SendAndClose(&frontend.Empty{})
	return nil
}

func (s *LaserService) GetNextLaserState(ctx context.Context, req *frontend.Timestamp) (*frontend.LaserStateList, error) {
	var ts int64 = req.GetTimestampMs()

	return s.scState.GetLaserState(s.redisClient, func(f func()) bool {
		return s.scState.ReadOnNext(ctx, ts, f)
	})
}

func (s *LaserService) ToggleLaserEnabled(ctx context.Context, req *frontend.LaserDescriptor) (*frontend.Empty, error) {
	logrus.Infof("Toggle Laser: %d", req.GetRowNumber())

	valid := false
	enabled := false
	camId := state.ScannerIdToCamId(s.robot, req.GetRowNumber(), req.GetLaserId())
	s.scState.ReadOnCurrent(func() {
		if scanner, ok := s.scState.CamMapping[camId]; ok {
			valid = ok
			enabled = scanner.Laser.Enabled
		}
	})
	if !valid {
		logrus.Errorf("LaserService.ToggleLaserEnabled: Laser %d not found, camId %s", req.GetLaserId(), camId)
		return nil, status.Errorf(codes.NotFound, "Invalid Laser ID: %d, camId %s lookup failed", req.LaserId, camId)
	}
	if rowNum, targetId, ok := s.scState.CamIdToRowTargetId(camId); ok {
		resp, err := s.rows[int(rowNum)].AimbotClient.LaserEnable(targetId, !enabled)
		if err == nil && !resp.Status {
			logrus.Error(resp.Message)
			return nil, status.Error(codes.FailedPrecondition, resp.Message)
		} else if err == nil {
			s.scWatcher.Trigger()
		} else {
			return nil, err
		}
	} else {
		logrus.Errorf("LaserService.ToggleLaserEnabled: Laser %d not found, camId %s", req.GetLaserId(), camId)
		return nil, status.Errorf(codes.NotFound, "Invalid Laser ID: %d, camId %s lookup failed", req.LaserId, camId)
	}
	return &frontend.Empty{}, nil
}

func (s *LaserService) EnableRow(ctx context.Context, req *frontend.RowRequest) (*frontend.Empty, error) {
	logrus.Infof("Enable Row Called: %d", req.GetRowNumber())

	clients, ok := s.rows[int(req.GetRowNumber())]
	if ok {
		resp, err := clients.AimbotClient.LaserEnable(0, true)
		if err == nil && !resp.Status {
			logrus.Error(resp.Message)
			return nil, status.Error(codes.FailedPrecondition, resp.Message)
		} else if err == nil {
			s.scWatcher.Trigger()
		} else {
			logrus.WithError(err).Error("Error Enabling Row")
			return nil, err
		}

		return &frontend.Empty{}, nil
	} else {
		logrus.Errorf("LaserService.EnableRow: Row %d not found", req.GetRowNumber())
		return nil, status.Errorf(codes.NotFound, "Invalid Row Number: %d", req.GetRowNumber())
	}
}

func (s *LaserService) DisableRow(ctx context.Context, req *frontend.RowRequest) (*frontend.Empty, error) {
	logrus.Infof("Disable Row Called: %d", req.GetRowNumber())

	var clients *rows.RowClients
	var ok bool
	if clients, ok = s.rows[int(req.GetRowNumber())]; !ok {
		logrus.Errorf("LaserService.DisableRow: Row %d not found", req.GetRowNumber())
		return nil, status.Errorf(codes.NotFound, "Invalid Row Number: %d", req.GetRowNumber())
	}

	aimbotClient := clients.AimbotClient
	if aimbotClient == nil {
		logrus.Errorf("LaserService.DisableRow: Aimbot Client Not Found for Row: %d", req.GetRowNumber())
		return nil, status.Errorf(codes.Internal, "Aimbot Client Not Found for Row: %d", req.GetRowNumber())
	}

	resp, err := aimbotClient.LaserEnable(0, false)
	if err == nil && !resp.Status {
		logrus.Error(resp.Message)
		return nil, status.Error(codes.FailedPrecondition, resp.Message)
	} else if err == nil {
		s.scWatcher.Trigger()
	} else {
		logrus.WithError(err).Error("Error Disabling Row")
		return nil, err
	}

	return &frontend.Empty{}, nil
}

func (s *LaserService) ResetLaserMetrics(ctx context.Context, req *frontend.LaserDescriptor) (*frontend.Empty, error) {
	logrus.Infof("Reset Laser Metrics called with %v", req)

	if _, ok := s.rows[int(req.GetRowNumber())]; !ok {
		return nil, status.Errorf(codes.NotFound, "Invalid Row Number: %d", req.GetRowNumber())
	}

	err := s.rows[int(req.GetRowNumber())].AimbotClient.ResetLaserMetrics(&aimbot.ScannerDescriptor{Id: req.GetLaserId()})
	if err != nil {
		logrus.Errorf("Couldn't reset laser metrics, error=%v", err)
		return nil, err
	}
	err = s.metricsClient.SetLaser(req.GetSerial(), req.GetRowNumber(), req.GetLaserId())
	if err != nil {
		logrus.Errorf("Couldn't reset laser metrics, error=%v", err)
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *LaserService) FixLaserMetrics(ctx context.Context, req *frontend.FixLaserMetricsRequest) (*frontend.Empty, error) {
	logrus.Infof("Fix Laser Metrics called with %v", req)

	laser_descriptor := req.GetLaserDescriptor()

	if laser_descriptor == nil {
		return nil, errors.New("Laser Descriptor is nil")
	}

	if _, ok := s.rows[int(laser_descriptor.GetRowNumber())]; !ok {
		return nil, status.Errorf(codes.NotFound, "Invalid Row Number: %d", laser_descriptor.GetRowNumber())
	}

	err := s.rows[int(laser_descriptor.GetRowNumber())].AimbotClient.FixLaserMetrics(&aimbot.FixLaserMetricsRequest{
		Scanner:         &aimbot.ScannerDescriptor{Id: laser_descriptor.GetLaserId()},
		TotalFireCount:  req.TotalFireCount,
		TotalFireTimeMs: req.TotalFireTimeMs,
	})
	if err != nil {
		logrus.Errorf("Couldn't reset laser metrics, error=%v", err)
		return nil, err
	}
	err = s.metricsClient.OverrideLaser(laser_descriptor.GetSerial(), laser_descriptor.GetRowNumber(), laser_descriptor.GetLaserId(), req.LifetimeSec)
	if err != nil {
		logrus.Errorf("Couldn't reset laser metrics, error=%v", err)
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *LaserService) SetLaserPower(ctx context.Context, req *frontend.SetLaserPowerRequest) (*frontend.Empty, error) {
	logrus.Infof("Set Laser Power called with %v", req)

	descriptor := req.GetLaserDescriptor()
	if _, ok := s.rows[int(descriptor.GetRowNumber())]; !ok {
		return nil, status.Errorf(codes.NotFound, "Invalid Row Number: %d", descriptor.GetRowNumber())
	}

	if req.GetPowerLevel() != useDefault &&
		(req.GetPowerLevel() < laserPowerLowerBound || req.GetPowerLevel() > laserPowerUpperBound) {
		return nil, status.Errorf(codes.FailedPrecondition, "Invalid Value for Power: %v", req.GetPowerLevel())
	}

	prefix := config.GetRowComputerConfigPrefix(int(descriptor.GetRowNumber()))
	pathToPower := fmt.Sprintf("%v/aimbot/scanners/scanner%v/laser/power", prefix, descriptor.GetLaserId())
	err := s.configClient.SetDoubleValue(pathToPower, float64(req.GetPowerLevel()))
	if err != nil {
		logrus.Errorf("Couldn't set laser power, error=%v", err)
		return nil, err
	}

	return &frontend.Empty{}, nil
}
