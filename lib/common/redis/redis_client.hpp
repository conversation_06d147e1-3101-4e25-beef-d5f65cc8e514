#pragma once

#include <chrono>
#include <sw/redis++/redis++.h>
#include <type_traits>

namespace lib {
namespace common {

class RedisClient {
public:
  RedisClient();

  std::optional<std::string> get(const sw::redis::StringView &key);
  std::optional<int64_t> get_long(const sw::redis::StringView &key);
  int64_t get_long_def(const sw::redis::StringView &key, int64_t def);
  std::optional<double> get_double(const sw::redis::StringView &key);

  bool set(const sw::redis::StringView &key, const sw::redis::StringView &val,
           const std::chrono::milliseconds &ttl = std::chrono::milliseconds(0));
  bool set_long(const sw::redis::StringView &key, int64_t val,
                const std::chrono::milliseconds &ttl = std::chrono::milliseconds(0));
  bool set_long_safe(const sw::redis::StringView &key, int64_t val,
                     const std::chrono::milliseconds &ttl = std::chrono::milliseconds(0));
  bool set_double(const sw::redis::StringView &key, double val,
                  const std::chrono::milliseconds &ttl = std::chrono::milliseconds(0));

  sw::redis::OptionalString hget(const sw::redis::StringView &key, const sw::redis::StringView &field);
  sw::redis::OptionalLongLong hget_long(const sw::redis::StringView &key, const sw::redis::StringView &field);
  sw::redis::OptionalDouble hget_double(const sw::redis::StringView &key, const sw::redis::StringView &field);
  int64_t hget_long_def(const sw::redis::StringView &key, const sw::redis::StringView &field, int64_t def);
  double hget_double_def(const sw::redis::StringView &key, const sw::redis::StringView &field, double def);
  bool hset(const sw::redis::StringView &key, const sw::redis::StringView &field, const sw::redis::StringView &val);
  bool hset_safe(const sw::redis::StringView &key, const sw::redis::StringView &field,
                 const sw::redis::StringView &val);
  bool expire(const sw::redis::StringView &key, const std::chrono::seconds &timeout);
  template <typename T>
  bool hset_numeric_safe(const sw::redis::StringView &key, const sw::redis::StringView &field, T val) {
    static_assert(std::is_arithmetic<T>::value, "Not an arithmetic type");
    return hset_safe(key, field, std::string_view{std::to_string(val)});
  }
  bool exists(const sw::redis::StringView &key);
  bool hexists(const sw::redis::StringView &key, const sw::redis::StringView &field);
  bool hincrby_safe(const sw::redis::StringView &key, const sw::redis::StringView &field, int64_t inc);
  inline bool hincrby_safe(const sw::redis::StringView &key, const sw::redis::StringView &field, uint64_t inc) {
    return hincrby_safe(key, field, static_cast<int64_t>(inc));
  }
  inline bool hincrby_safe(const sw::redis::StringView &key, const sw::redis::StringView &field, uint32_t inc) {
    return hincrby_safe(key, field, static_cast<int64_t>(inc));
  }
  bool hincrbyfloat_safe(const sw::redis::StringView &key, const sw::redis::StringView &field, double inc);
  sw::redis::Redis *operator->() const { return client_.get(); }
  void ping();
  void wait_until_ready();

private:
  std::unique_ptr<sw::redis::Redis> client_;
};

} // namespace common
} // namespace lib
