package main

import (
	"os"
	"runtime"
	"strings"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/bot/commands"
	"github.com/carbonrobotics/robot/golang/bot/parameters"
	"github.com/hellflame/argparse"
	"github.com/sirupsen/logrus"
)

func AddBuildCommand(parser *argparse.Parser) {
	container := parser.String("c", "container", &argparse.Option{Required: false, Positional: true, Help: "Name of Container to Build", Default: ""})
	release := parser.String("r", "release", &argparse.Option{Required: false, Help: "Release Tag, Triggers Production Build", Default: ""})
	fast := parser.Flag("f", "fast", &argparse.Option{Required: false, Help: "Skips Building the Base Container and Runs the Build Command"})
	all := parser.Flag("a", "all", &argparse.Option{Required: false, Help: "Build all containers"})
	id := parser.String("i", "id", &argparse.Option{Required: false, Help: "Container Id", Default: ""})
	version := parser.String("v", "version", &argparse.Option{Required: false, Help: "Container Version", Default: ""})
	buildArgs := parser.Strings("ba", "build-arg", &argparse.Option{Required: false, Help: "Docker Build Arg"})
	optionalTag := parser.Strings("t", "tag", &argparse.Option{Required: false, Help: "Optional container tag", Default: ""})
	optionalCacheFrom := parser.Strings("cf", "cache-from", &argparse.Option{Required: false, Help: "Optional cache-from tag", Default: ""})
	multiarch := parser.Flag("ma", "multiarch", &argparse.Option{Required: false, Help: "Builds for both ARM and AMD. Requires push"})
	push := parser.Flag("p", "push", &argparse.Option{Required: false, Help: "Automatically push build"})
	platform := parser.String("pf", "platform", &argparse.Option{Required: false, Help: "Platform to build for, defaults to system", Default: runtime.GOARCH})
	ciTag := parser.Flag("ci", "ci_tag", &argparse.Option{Required: false, Help: "Automatically Generate Tag from SHA1 of Git Branch Name"})
	ciRev := parser.String("cibr", "ci-base-revision", &argparse.Option{Required: false, Help: "Base revision for ci, branch head will be compared to limit container building (ex: 'master' or some release branch"})
	noBuildCmd := parser.Flag("nbc", "no-build-cmd", &argparse.Option{Required: false, Help: "Skip the build commands"})
	srcTag := parser.String("srt", "source-tag", &argparse.Option{Required: false, Help: "target a specific source tag"})
	containerBuildRetries := parser.Int("cbr", "container-build-retries", &argparse.Option{Required: false, Help: "number of times to retry container builds when 'retry-able' errors occur", Default: "0"})
	parser.InvokeAction = func(_ bool) {
		commands.BotBuild(parameters.BotBuildOpts{
			Container:             *container,
			Release:               *release,
			Fast:                  *fast,
			All:                   *all,
			ID:                    *id,
			Version:               *version,
			BuildArgs:             parseBuildArgs(*buildArgs),
			OptionalTag:           *optionalTag,
			OptionalCacheFrom:     *optionalCacheFrom,
			Multiarch:             *multiarch,
			Platform:              *platform,
			Push:                  *push,
			CiTag:                 *ciTag,
			CiRevision:            *ciRev,
			NoBuildCmd:            *noBuildCmd,
			SourceTag:             *srcTag,
			ContainerBuildRetries: *containerBuildRetries,
		})
	}
}

func AddTagCommand(parser *argparse.Parser) {
	container := parser.String("c", "container", &argparse.Option{Required: false, Positional: true, Help: "Name of Container to Tag", Default: ""})
	source := parser.String("s", "source", &argparse.Option{Required: false, Help: "Source Tag", Default: "latest"})
	tag := parser.String("t", "tag", &argparse.Option{Required: false, Help: "Target Tag", Default: ""})
	all := parser.Flag("a", "all", &argparse.Option{Required: false, Help: "Build all containers"})
	registrySrc := parser.String("rs", "registry-src", &argparse.Option{Required: false, Help: "Registry to tag from, (e.g. '10.10.3.1:5000/robot')", Default: ""})
	registryDst := parser.String("rd", "registry-dst", &argparse.Option{Required: false, Help: "Registry to tag to, (e.g. '10.10.3.1:5000/robot')", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotTag(*container, *source, *tag, *all, *registrySrc, *registryDst)
	}
}

func AddContainersCommand(parser *argparse.Parser) {
	all := parser.Flag("a", "all", &argparse.Option{Required: false, Help: "Build all containers "})
	parser.InvokeAction = func(_ bool) {
		commands.BotContainers(*all)
	}
}

func AddPushCommand(parser *argparse.Parser) {
	container := parser.String("c", "container", &argparse.Option{Required: false, Positional: true, Help: "Name of Container to Push", Default: ""})
	release := parser.String("r", "release", &argparse.Option{Required: false, Help: "Release Tag", Default: ""})
	remoteTag := parser.String("rt", "remote_tag", &argparse.Option{Required: false, Help: "Remote Tag To Push Container With", Default: ""})
	ciTag := parser.Flag("ci", "ci_tag", &argparse.Option{Required: false, Help: "Automatically Generate Tag from SHA1 of Git Branch Name"})
	all := parser.Flag("a", "all", &argparse.Option{Required: false, Help: "Build all containers"})
	platform := parser.String("pf", "platform", &argparse.Option{Required: false, Help: "Platform to build for, defaults to system", Default: runtime.GOARCH})
	registry := parser.String("rg", "registry", &argparse.Option{Required: false, Help: "Registry to push to, (e.g. '10.10.3.1:5000/robot')", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotPush(commands.PushOpts{
			Container: *container,
			Release:   *release,
			RemoteTag: *remoteTag,
			CiTag:     *ciTag,
			All:       *all,
			Platform:  *platform,
			Registry:  *registry,
		})
	}
}

func AddPullCommand(parser *argparse.Parser) {
	container := parser.String("c", "container", &argparse.Option{Required: false, Positional: true, Help: "Name of Container to Pull", Default: ""})
	release := parser.String("r", "release", &argparse.Option{Required: false, Help: "Release Tag", Default: ""})
	all := parser.Flag("a", "all", &argparse.Option{Required: false, Help: "Pull all containers"})
	gobot := parser.Flag("g", "gobot", &argparse.Option{Required: false, Help: "Pull Gobot as well"})
	platform := parser.String("pf", "platform", &argparse.Option{Required: false, Help: "Platform to build for, defaults to system", Default: runtime.GOARCH})
	registry := parser.String("rg", "registry", &argparse.Option{Required: false, Help: "Registry to pull from, (e.g. '10.10.3.1:5000/robot')", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotPull(commands.PullOpts{
			Container: *container,
			Release:   *release,
			All:       *all,
			Gobot:     *gobot,
			Platform:  *platform,
			Registry:  *registry,
		})
	}
}

func AddDLBuildPushCommand(parser *argparse.Parser) {
	version := parser.String("v", "version", &argparse.Option{Required: true, Positional: true, Help: "Container Version", Default: ""})
	slow := parser.Flag("s", "slow", &argparse.Option{Required: false, Help: "Build base container"})
	parser.InvokeAction = func(_ bool) {
		commands.BotDLBuildPush(*version, *slow)
	}
}

func AddRunCommand(parser *argparse.Parser) {
	user := parser.String("u", "user", &argparse.Option{Required: false, Positional: false, Help: "user whose env to load to run container", Default: ""})
	container := parser.String("c", "container", &argparse.Option{Required: false, Positional: true, Help: "Name of Container to Run Command In", Default: "common"})
	command := parser.Strings("cmd", "command", &argparse.Option{Required: false, Positional: true, Help: "Command to Run", Default: "/bin/bash"})
	gpus := parser.Flag("g", "gpus", &argparse.Option{Required: false, Help: "Enable gpus in container"})
	privileged := parser.Flag("p", "privileged", &argparse.Option{Required: false, Help: "Run container in priveleged mode"})
	name := parser.String("n", "name", &argparse.Option{Required: false, Help: "Set name for Docker container", Default: ""})
	prod := parser.Flag("prod", "production", &argparse.Option{Required: false, Help: "Run container in production mode"})
	// Note the flag name below is purposefully long to avoid any possible conflict
	release := parser.String("carbon-rel-tag", "carbon-release-tag", &argparse.Option{Required: false, Help: "Release Tag", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotRun(*container, *command, *release, *gpus, *privileged, *user, *name, *prod)
	}
}

func AddCheckoutCommand(parser *argparse.Parser) {
	user := parser.String("u", "user", &argparse.Option{Required: false, Positional: true, Help: "User to Checkout for", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotCheckout(*user)
	}
}

func AddReturnCommand(parser *argparse.Parser) {
	parser.InvokeAction = func(_ bool) {
		commands.BotReturn()
	}
}

func AddStartCommand(parser *argparse.Parser) {
	services := parser.Strings("s", "service", &argparse.Option{Required: false, Positional: true, Help: "Service(s) To Start", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotStart(*services)
	}
}

func AddRestartCommand(parser *argparse.Parser) {
	services := parser.Strings("s", "service", &argparse.Option{Required: false, Positional: true, Help: "Service(s) To Restart", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotStop(*services)
		commands.BotStart(*services)
	}
}

func AddStopCommand(parser *argparse.Parser) {
	services := parser.Strings("s", "service", &argparse.Option{Required: false, Positional: true, Help: "Service(s) To Stop", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotStop(*services)
	}
}

func AddTailCommand(parser *argparse.Parser) {
	service := parser.String("s", "service", &argparse.Option{Required: false, Positional: true, Help: "Service To Tail", Default: ""})
	lines := parser.Int("l", "lines", &argparse.Option{Required: false, Positional: false, Help: "Number of Existing Lines to Tail", Default: "100"})
	parser.InvokeAction = func(_ bool) {
		commands.BotTail(*service, *lines)
	}
}

func AddStatusCommand(parser *argparse.Parser) {
	parser.InvokeAction = func(_ bool) {
		commands.BotStatus()
	}
}

func AddConfigCommand(parser *argparse.Parser) {
	parser.InvokeAction = func(_ bool) {
		commands.BotConfig()
	}
}

func AddDevReleaseCommand(parser *argparse.Parser) {
	release := parser.String("r", "release", &argparse.Option{Required: true, Help: "Release Tag, Triggers Production Build", Default: ""})
	id := parser.String("i", "id", &argparse.Option{Required: false, Help: "Container Id", Default: ""})
	version := parser.String("v", "version", &argparse.Option{Required: false, Help: "Container Version", Default: ""})
	buildArgs := parser.Strings("ba", "build-arg", &argparse.Option{Required: false, Help: "Docker Build Args"})
	multiarch := parser.Flag("ma", "multiarch", &argparse.Option{Required: false, Help: "Builds for both ARM and AMD. Requires push"})
	platform := parser.String("pf", "platform", &argparse.Option{Required: false, Help: "Platform to build for, defaults to system", Default: runtime.GOARCH})
	parser.InvokeAction = func(_ bool) {
		commands.BotDevRelease(*release, *id, *version, parseBuildArgs(*buildArgs), *multiarch, *platform)
	}
}

func AddReleaseCommand(parser *argparse.Parser) {
	release := parser.String("r", "release", &argparse.Option{Required: true, Help: "Release Tag, Triggers Production Build", Default: ""})
	id := parser.String("i", "id", &argparse.Option{Required: false, Help: "Container Id", Default: ""})
	version := parser.String("v", "version", &argparse.Option{Required: false, Help: "Container Version", Default: ""})
	buildArgs := parser.Strings("ba", "build-arg", &argparse.Option{Required: false, Help: "Docker Build Args"})
	multiarch := parser.Flag("ma", "multiarch", &argparse.Option{Required: false, Help: "Builds for both ARM and AMD. Requires push"})
	platform := parser.String("pf", "platform", &argparse.Option{Required: false, Help: "Platform to build for, defaults to system", Default: runtime.GOARCH})
	optionalTag := parser.Strings("t", "tag", &argparse.Option{Required: false, Help: "Optional container tag", Default: ""})
	optionalCacheFrom := parser.Strings("cf", "cache-from", &argparse.Option{Required: false, Help: "Optional cache-from tag", Default: ""})
	containerBuildRetries := parser.Int("cbr", "container-build-retries", &argparse.Option{Required: false, Help: "number of times to retry container builds when 'retry-able' errors occur", Default: "0"})
	skipSemverCheck := parser.Flag("sc", "skip-semver-check", &argparse.Option{Required: false, Help: "Skip checking if the release tag is a valid semantic version"})
	parser.InvokeAction = func(_ bool) {
		commands.BotRelease(parameters.BotBuildOpts{
			Container:             "",
			Release:               *release,
			Fast:                  false,
			All:                   true,
			ID:                    *id,
			Version:               *version,
			BuildArgs:             parseBuildArgs(*buildArgs),
			OptionalTag:           *optionalTag,
			OptionalCacheFrom:     *optionalCacheFrom,
			Multiarch:             *multiarch,
			Platform:              *platform,
			Push:                  true,
			CiTag:                 false,
			NoBuildCmd:            false,
			ContainerBuildRetries: *containerBuildRetries,
			SkipSemverCheck:       *skipSemverCheck,
		})
	}
}

func AddVerifyImagesCommand(parser *argparse.Parser) {
	container := parser.String("c", "container", &argparse.Option{Required: false, Positional: true, Help: "Name of Container to Test", Default: ""})
	release := parser.String("r", "release", &argparse.Option{Required: false, Help: "Release Tag to Test", Default: ""})
	parser.InvokeAction = func(_ bool) {
		commands.BotVerifyImages(*container, *release)
	}
}

func AddSwitchCommand(parser *argparse.Parser) {
	generation := parser.String("g", "generation", &argparse.Option{Required: true, Positional: true, Help: "Switch to a different generation", Choices: []interface{}{"slayer", "reaper"}})
	parser.InvokeAction = func(_ bool) {
		err := bot_context.BotSwitch(*generation)
		if err != nil {
			logrus.WithError(err).Error("Failed to switch generations")
		}
	}
}

func main() {
	customFormatter := new(logrus.TextFormatter)
	customFormatter.TimestampFormat = "2006-01-02 15:04:05.000"
	customFormatter.FullTimestamp = true
	logrus.SetFormatter(customFormatter)
	logrus.Infof("Starting Bot Tool")
	logrus.Infoln("bot env:", os.Environ())
	parser := argparse.NewParser("bot", "Carbon Bot Tool", &argparse.ParserConfig{DisableDefaultShowHelp: true})

	AddBuildCommand(parser.AddCommand("build", "Build Container(s)", nil))
	AddTagCommand(parser.AddCommand("tag", "Tag Container(s)", nil))
	AddContainersCommand(parser.AddCommand("containers", "List Container(s)", nil))
	AddPushCommand(parser.AddCommand("push", "Push Container(s)", nil))
	AddPullCommand(parser.AddCommand("pull", "Pull Container(s)", nil))
	AddDLBuildPushCommand(parser.AddCommand("dl-build-push", "Build and push deeplearning container", nil))
	AddRunCommand(parser.AddCommand("run", "Run Command in Container", nil))
	AddCheckoutCommand(parser.AddCommand("checkout", "Checkout Robot for Development", nil))
	AddReturnCommand(parser.AddCommand("return", "Return Robot to Production Mode", nil))
	AddStartCommand(parser.AddCommand("start", "Start Service(s)", nil))
	AddRestartCommand(parser.AddCommand("restart", "Restart Service(s)", nil))
	AddStopCommand(parser.AddCommand("stop", "Stop Service(s)", nil))
	AddTailCommand(parser.AddCommand("tail", "Tail Service", nil))
	AddStatusCommand(parser.AddCommand("status", "Services Status", nil))
	AddConfigCommand(parser.AddCommand("config", "Current Config", nil))
	AddVerifyImagesCommand(parser.AddCommand("verify-images", "Container Checksum Validation", nil))
	AddDevReleaseCommand(parser.AddCommand("dev-release", "Build Container(s) for development release", nil))
	AddReleaseCommand(parser.AddCommand("release", "Build and push containers for production release", nil))
	AddSwitchCommand(parser.AddCommand("switch", "Switch between generations", nil))
	if err := parser.Parse(nil); err != nil {
		logrus.Fatal(err)
	}
}

func parseBuildArgs(args []string) map[string]string {
	result := make(map[string]string)
	for _, a := range args {
		parts := strings.Split(a, "=")
		if len(parts) == 1 {
			result[parts[0]] = ""
		} else if len(parts) > 1 {
			p := strings.Join(parts[1:], "=")
			result[parts[0]] = p
		}
	}
	return result
}
