from collections import defaultdict
from typing import Dict, <PERSON><PERSON>

from config.client.cpp.config_client_python import get_global_config_subscriber
from lib.common.logging import get_logger
from lib.common.role import ROLE, role

LOG = get_logger(__name__)


def get_secondary_hostname(primary_hostname: str) -> str:
    if role() == ROLE.SIMULATOR_MINICOMPUTERS:
        return "localhost"
    return "**********"


def get_secondary_port() -> int:
    if role() == ROLE.SIMULATOR_MINICOMPUTERS:
        return 15054
    return 15053


class CVCameraMap:
    def __init__(self) -> None:
        self.cam_is_on_primary, self._has_secondary = self.get_cameras_on_primary_map()

    def get_single_client_cam_map(self) -> Dict[str, bool]:
        return defaultdict(lambda: True)

    def get_cameras_on_primary_map(self) -> Tuple[Dict[str, bool], bool]:
        m: Dict[str, bool] = {}

        try:
            config_subscriber = get_global_config_subscriber()
            node = config_subscriber.get_config_node("cv", "cameras")
            for camNode in node.get_children_nodes():
                m[camNode.get_name()] = camNode.get_node("is_on_primary").get_bool_value()
        except Exception:
            m = self.get_single_client_cam_map()

        has_secondary = False
        for v in m.values():
            if v is False:
                has_secondary = True
                break

        return m, has_secondary

    def is_on_primary(self, cam_id: str) -> bool:
        primary = self.cam_is_on_primary.get(cam_id)
        if primary is None:
            return True
        return primary

    def has_secondary(self) -> bool:
        return self._has_secondary
