"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.lib.common.camera.proto.camera_pb2 import (
    LightSourcePresetValue as lib___common___camera___proto___camera_pb2___LightSourcePresetValue,
)

from generated.proto.cv.cv_pb2 import (
    P2PCaptureReasonValue as proto___cv___cv_pb2___P2PCaptureReasonValue,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)

from generated.weed_tracking.proto.weed_tracking_pb2 import (
    Detection as weed_tracking___proto___weed_tracking_pb2___Detection,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

BufferUseCaseValue = typing___NewType('BufferUseCaseValue', builtin___int)
type___BufferUseCaseValue = BufferUseCaseValue
BufferUseCase: _BufferUseCase
class _BufferUseCase(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[BufferUseCaseValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    P2P = typing___cast(BufferUseCaseValue, 0)
    OpticalFlow = typing___cast(BufferUseCaseValue, 1)
    Predict = typing___cast(BufferUseCaseValue, 2)
    Drive = typing___cast(BufferUseCaseValue, 3)
P2P = typing___cast(BufferUseCaseValue, 0)
OpticalFlow = typing___cast(BufferUseCaseValue, 1)
Predict = typing___cast(BufferUseCaseValue, 2)
Drive = typing___cast(BufferUseCaseValue, 3)

HitClassValue = typing___NewType('HitClassValue', builtin___int)
type___HitClassValue = HitClassValue
HitClass: _HitClass
class _HitClass(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[HitClassValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    WEED = typing___cast(HitClassValue, 0)
    CROP = typing___cast(HitClassValue, 1)
    PLANT = typing___cast(HitClassValue, 2)
WEED = typing___cast(HitClassValue, 0)
CROP = typing___cast(HitClassValue, 1)
PLANT = typing___cast(HitClassValue, 2)

ScoreQueueTypeValue = typing___NewType('ScoreQueueTypeValue', builtin___int)
type___ScoreQueueTypeValue = ScoreQueueTypeValue
ScoreQueueType: _ScoreQueueType
class _ScoreQueueType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ScoreQueueTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    PREDICT = typing___cast(ScoreQueueTypeValue, 0)
    CHIP = typing___cast(ScoreQueueTypeValue, 1)
PREDICT = typing___cast(ScoreQueueTypeValue, 0)
CHIP = typing___cast(ScoreQueueTypeValue, 1)

ErrorTypeValue = typing___NewType('ErrorTypeValue', builtin___int)
type___ErrorTypeValue = ErrorTypeValue
ErrorType: _ErrorType
class _ErrorType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ErrorTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NONE = typing___cast(ErrorTypeValue, 0)
    GRAB = typing___cast(ErrorTypeValue, 1)
    CONNECTION = typing___cast(ErrorTypeValue, 2)
    NO_IMPLEMENTATION = typing___cast(ErrorTypeValue, 3)
    NO_IMAGE_IN_LAST_MINUTE = typing___cast(ErrorTypeValue, 4)
NONE = typing___cast(ErrorTypeValue, 0)
GRAB = typing___cast(ErrorTypeValue, 1)
CONNECTION = typing___cast(ErrorTypeValue, 2)
NO_IMPLEMENTATION = typing___cast(ErrorTypeValue, 3)
NO_IMAGE_IN_LAST_MINUTE = typing___cast(ErrorTypeValue, 4)

class TargetSafetyZone(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    up: builtin___float = ...
    down: builtin___float = ...
    left: builtin___float = ...
    right: builtin___float = ...

    def __init__(self,
        *,
        up : typing___Optional[builtin___float] = None,
        down : typing___Optional[builtin___float] = None,
        left : typing___Optional[builtin___float] = None,
        right : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"down",b"down",u"left",b"left",u"right",b"right",u"up",b"up"]) -> None: ...
type___TargetSafetyZone = TargetSafetyZone

class P2PContext(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    predict_cam_id: typing___Text = ...
    predict_timestamp_ms: builtin___int = ...
    predict_coord_x: builtin___float = ...
    predict_coord_y: builtin___float = ...

    def __init__(self,
        *,
        predict_cam_id : typing___Optional[typing___Text] = None,
        predict_timestamp_ms : typing___Optional[builtin___int] = None,
        predict_coord_x : typing___Optional[builtin___float] = None,
        predict_coord_y : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"predict_cam_id",b"predict_cam_id",u"predict_coord_x",b"predict_coord_x",u"predict_coord_y",b"predict_coord_y",u"predict_timestamp_ms",b"predict_timestamp_ms"]) -> None: ...
type___P2PContext = P2PContext

class SetP2PContextRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    target_cam_id: typing___Text = ...
    target_id: builtin___int = ...

    @property
    def primary_context(self) -> type___P2PContext: ...

    @property
    def secondary_context(self) -> type___P2PContext: ...

    @property
    def safety_zone(self) -> type___TargetSafetyZone: ...

    def __init__(self,
        *,
        target_cam_id : typing___Optional[typing___Text] = None,
        primary_context : typing___Optional[type___P2PContext] = None,
        secondary_context : typing___Optional[type___P2PContext] = None,
        safety_zone : typing___Optional[type___TargetSafetyZone] = None,
        target_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_secondary_context",b"_secondary_context",u"primary_context",b"primary_context",u"safety_zone",b"safety_zone",u"secondary_context",b"secondary_context"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_secondary_context",b"_secondary_context",u"primary_context",b"primary_context",u"safety_zone",b"safety_zone",u"secondary_context",b"secondary_context",u"target_cam_id",b"target_cam_id",u"target_id",b"target_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_secondary_context",b"_secondary_context"]) -> typing_extensions___Literal["secondary_context"]: ...
type___SetP2PContextRequest = SetP2PContextRequest

class SetP2PContextResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetP2PContextResponse = SetP2PContextResponse

class GetCameraDimensionsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id"]) -> None: ...
type___GetCameraDimensionsRequest = GetCameraDimensionsRequest

class GetCameraDimensionsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width: builtin___int = ...
    height: builtin___int = ...
    transpose: builtin___bool = ...

    def __init__(self,
        *,
        width : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        transpose : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"height",b"height",u"transpose",b"transpose",u"width",b"width"]) -> None: ...
type___GetCameraDimensionsResponse = GetCameraDimensionsResponse

class StartP2PDataCaptureRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    target_cam_id: typing___Text = ...
    capture_miss_rate: builtin___float = ...
    capture_success_rate: builtin___float = ...
    capture_enabled: builtin___bool = ...
    capture_path: typing___Text = ...
    after_timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        target_cam_id : typing___Optional[typing___Text] = None,
        capture_miss_rate : typing___Optional[builtin___float] = None,
        capture_success_rate : typing___Optional[builtin___float] = None,
        capture_enabled : typing___Optional[builtin___bool] = None,
        capture_path : typing___Optional[typing___Text] = None,
        after_timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"after_timestamp_ms",b"after_timestamp_ms",u"capture_enabled",b"capture_enabled",u"capture_miss_rate",b"capture_miss_rate",u"capture_path",b"capture_path",u"capture_success_rate",b"capture_success_rate",u"target_cam_id",b"target_cam_id"]) -> None: ...
type___StartP2PDataCaptureRequest = StartP2PDataCaptureRequest

class PointDetectionCategory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    threshold: builtin___float = ...
    category: typing___Text = ...

    def __init__(self,
        *,
        threshold : typing___Optional[builtin___float] = None,
        category : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category",b"category",u"threshold",b"threshold"]) -> None: ...
type___PointDetectionCategory = PointDetectionCategory

class SegmentationDetectionCategory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    threshold: builtin___float = ...
    category: typing___Text = ...
    safety_radius_in: builtin___float = ...

    def __init__(self,
        *,
        threshold : typing___Optional[builtin___float] = None,
        category : typing___Optional[typing___Text] = None,
        safety_radius_in : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category",b"category",u"safety_radius_in",b"safety_radius_in",u"threshold",b"threshold"]) -> None: ...
type___SegmentationDetectionCategory = SegmentationDetectionCategory

class DeepweedDetectionCriteriaSetting(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weed_point_threshold: builtin___float = ...
    crop_point_threshold: builtin___float = ...

    @property
    def point_categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PointDetectionCategory]: ...

    @property
    def segmentation_categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___SegmentationDetectionCategory]: ...

    def __init__(self,
        *,
        point_categories : typing___Optional[typing___Iterable[type___PointDetectionCategory]] = None,
        weed_point_threshold : typing___Optional[builtin___float] = None,
        crop_point_threshold : typing___Optional[builtin___float] = None,
        segmentation_categories : typing___Optional[typing___Iterable[type___SegmentationDetectionCategory]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_point_threshold",b"crop_point_threshold",u"point_categories",b"point_categories",u"segmentation_categories",b"segmentation_categories",u"weed_point_threshold",b"weed_point_threshold"]) -> None: ...
type___DeepweedDetectionCriteriaSetting = DeepweedDetectionCriteriaSetting

class SetDeepweedDetectionCriteriaRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weed_point_threshold: builtin___float = ...
    crop_point_threshold: builtin___float = ...

    @property
    def point_categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PointDetectionCategory]: ...

    @property
    def segmentation_categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___SegmentationDetectionCategory]: ...

    def __init__(self,
        *,
        weed_point_threshold : typing___Optional[builtin___float] = None,
        crop_point_threshold : typing___Optional[builtin___float] = None,
        point_categories : typing___Optional[typing___Iterable[type___PointDetectionCategory]] = None,
        segmentation_categories : typing___Optional[typing___Iterable[type___SegmentationDetectionCategory]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_point_threshold",b"crop_point_threshold",u"point_categories",b"point_categories",u"segmentation_categories",b"segmentation_categories",u"weed_point_threshold",b"weed_point_threshold"]) -> None: ...
type___SetDeepweedDetectionCriteriaRequest = SetDeepweedDetectionCriteriaRequest

class SetDeepweedDetectionCriteriaResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetDeepweedDetectionCriteriaResponse = SetDeepweedDetectionCriteriaResponse

class GetDeepweedDetectionCriteriaRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetDeepweedDetectionCriteriaRequest = GetDeepweedDetectionCriteriaRequest

class GetDeepweedDetectionCriteriaResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weed_point_threshold: builtin___float = ...
    crop_point_threshold: builtin___float = ...

    @property
    def point_categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PointDetectionCategory]: ...

    @property
    def segmentation_categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___SegmentationDetectionCategory]: ...

    def __init__(self,
        *,
        weed_point_threshold : typing___Optional[builtin___float] = None,
        crop_point_threshold : typing___Optional[builtin___float] = None,
        point_categories : typing___Optional[typing___Iterable[type___PointDetectionCategory]] = None,
        segmentation_categories : typing___Optional[typing___Iterable[type___SegmentationDetectionCategory]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_point_threshold",b"crop_point_threshold",u"point_categories",b"point_categories",u"segmentation_categories",b"segmentation_categories",u"weed_point_threshold",b"weed_point_threshold"]) -> None: ...
type___GetDeepweedDetectionCriteriaResponse = GetDeepweedDetectionCriteriaResponse

class GetDeepweedSupportedCategoriesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_cam_id",b"_cam_id",u"cam_id",b"cam_id"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_cam_id",b"_cam_id",u"cam_id",b"cam_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_cam_id",b"_cam_id"]) -> typing_extensions___Literal["cam_id"]: ...
type___GetDeepweedSupportedCategoriesRequest = GetDeepweedSupportedCategoriesRequest

class GetDeepweedSupportedCategoriesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    segmentation_categories: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    point_categories: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        segmentation_categories : typing___Optional[typing___Iterable[typing___Text]] = None,
        point_categories : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"point_categories",b"point_categories",u"segmentation_categories",b"segmentation_categories"]) -> None: ...
type___GetDeepweedSupportedCategoriesResponse = GetDeepweedSupportedCategoriesResponse

class GetDeepweedIndexToCategoryRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id"]) -> None: ...
type___GetDeepweedIndexToCategoryRequest = GetDeepweedIndexToCategoryRequest

class GetDeepweedIndexToCategoryResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class WeedPointIndexToCategoryEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___WeedPointIndexToCategoryEntry = WeedPointIndexToCategoryEntry

    class CropPointIndexToCategoryEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___CropPointIndexToCategoryEntry = CropPointIndexToCategoryEntry

    class IntersectionIndexToCategoryEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___IntersectionIndexToCategoryEntry = IntersectionIndexToCategoryEntry


    @property
    def weed_point_index_to_category(self) -> google___protobuf___internal___containers___ScalarMap[builtin___int, typing___Text]: ...

    @property
    def crop_point_index_to_category(self) -> google___protobuf___internal___containers___ScalarMap[builtin___int, typing___Text]: ...

    @property
    def intersection_index_to_category(self) -> google___protobuf___internal___containers___ScalarMap[builtin___int, typing___Text]: ...

    def __init__(self,
        *,
        weed_point_index_to_category : typing___Optional[typing___Mapping[builtin___int, typing___Text]] = None,
        crop_point_index_to_category : typing___Optional[typing___Mapping[builtin___int, typing___Text]] = None,
        intersection_index_to_category : typing___Optional[typing___Mapping[builtin___int, typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_point_index_to_category",b"crop_point_index_to_category",u"intersection_index_to_category",b"intersection_index_to_category",u"weed_point_index_to_category",b"weed_point_index_to_category"]) -> None: ...
type___GetDeepweedIndexToCategoryResponse = GetDeepweedIndexToCategoryResponse

class GetPredictCamMatrixRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    predict_cam_id: typing___Text = ...

    def __init__(self,
        *,
        predict_cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"predict_cam_id",b"predict_cam_id"]) -> None: ...
type___GetPredictCamMatrixRequest = GetPredictCamMatrixRequest

class GetPredictCamDistortionCoefficientsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    predict_cam_id: typing___Text = ...

    def __init__(self,
        *,
        predict_cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"predict_cam_id",b"predict_cam_id"]) -> None: ...
type___GetPredictCamDistortionCoefficientsRequest = GetPredictCamDistortionCoefficientsRequest

class SetCameraSettingsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    exposure_us: builtin___float = ...
    gamma: builtin___float = ...
    gain_db: builtin___float = ...
    light_source_preset: lib___common___camera___proto___camera_pb2___LightSourcePresetValue = ...
    wb_ratio_red: builtin___float = ...
    wb_ratio_green: builtin___float = ...
    wb_ratio_blue: builtin___float = ...
    roi_offset_x: builtin___int = ...
    roi_offset_y: builtin___int = ...
    mirror: builtin___bool = ...
    flip: builtin___bool = ...
    strobing: builtin___bool = ...
    ptp: builtin___bool = ...
    auto_whitebalance: builtin___bool = ...

    def __init__(self,
        *,
        cam_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        exposure_us : typing___Optional[builtin___float] = None,
        gamma : typing___Optional[builtin___float] = None,
        gain_db : typing___Optional[builtin___float] = None,
        light_source_preset : typing___Optional[lib___common___camera___proto___camera_pb2___LightSourcePresetValue] = None,
        wb_ratio_red : typing___Optional[builtin___float] = None,
        wb_ratio_green : typing___Optional[builtin___float] = None,
        wb_ratio_blue : typing___Optional[builtin___float] = None,
        roi_offset_x : typing___Optional[builtin___int] = None,
        roi_offset_y : typing___Optional[builtin___int] = None,
        mirror : typing___Optional[builtin___bool] = None,
        flip : typing___Optional[builtin___bool] = None,
        strobing : typing___Optional[builtin___bool] = None,
        ptp : typing___Optional[builtin___bool] = None,
        auto_whitebalance : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_auto_whitebalance",b"_auto_whitebalance",u"_exposure_us",b"_exposure_us",u"_flip",b"_flip",u"_gain_db",b"_gain_db",u"_gamma",b"_gamma",u"_light_source_preset",b"_light_source_preset",u"_mirror",b"_mirror",u"_ptp",b"_ptp",u"_roi_offset_x",b"_roi_offset_x",u"_roi_offset_y",b"_roi_offset_y",u"_strobing",b"_strobing",u"_wb_ratio_blue",b"_wb_ratio_blue",u"_wb_ratio_green",b"_wb_ratio_green",u"_wb_ratio_red",b"_wb_ratio_red",u"auto_whitebalance",b"auto_whitebalance",u"exposure_us",b"exposure_us",u"flip",b"flip",u"gain_db",b"gain_db",u"gamma",b"gamma",u"light_source_preset",b"light_source_preset",u"mirror",b"mirror",u"ptp",b"ptp",u"roi_offset_x",b"roi_offset_x",u"roi_offset_y",b"roi_offset_y",u"strobing",b"strobing",u"wb_ratio_blue",b"wb_ratio_blue",u"wb_ratio_green",b"wb_ratio_green",u"wb_ratio_red",b"wb_ratio_red"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_auto_whitebalance",b"_auto_whitebalance",u"_exposure_us",b"_exposure_us",u"_flip",b"_flip",u"_gain_db",b"_gain_db",u"_gamma",b"_gamma",u"_light_source_preset",b"_light_source_preset",u"_mirror",b"_mirror",u"_ptp",b"_ptp",u"_roi_offset_x",b"_roi_offset_x",u"_roi_offset_y",b"_roi_offset_y",u"_strobing",b"_strobing",u"_wb_ratio_blue",b"_wb_ratio_blue",u"_wb_ratio_green",b"_wb_ratio_green",u"_wb_ratio_red",b"_wb_ratio_red",u"auto_whitebalance",b"auto_whitebalance",u"cam_ids",b"cam_ids",u"exposure_us",b"exposure_us",u"flip",b"flip",u"gain_db",b"gain_db",u"gamma",b"gamma",u"light_source_preset",b"light_source_preset",u"mirror",b"mirror",u"ptp",b"ptp",u"roi_offset_x",b"roi_offset_x",u"roi_offset_y",b"roi_offset_y",u"strobing",b"strobing",u"wb_ratio_blue",b"wb_ratio_blue",u"wb_ratio_green",b"wb_ratio_green",u"wb_ratio_red",b"wb_ratio_red"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_auto_whitebalance",b"_auto_whitebalance"]) -> typing_extensions___Literal["auto_whitebalance"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_exposure_us",b"_exposure_us"]) -> typing_extensions___Literal["exposure_us"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_flip",b"_flip"]) -> typing_extensions___Literal["flip"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_gain_db",b"_gain_db"]) -> typing_extensions___Literal["gain_db"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_gamma",b"_gamma"]) -> typing_extensions___Literal["gamma"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_light_source_preset",b"_light_source_preset"]) -> typing_extensions___Literal["light_source_preset"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_mirror",b"_mirror"]) -> typing_extensions___Literal["mirror"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_ptp",b"_ptp"]) -> typing_extensions___Literal["ptp"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_roi_offset_x",b"_roi_offset_x"]) -> typing_extensions___Literal["roi_offset_x"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_roi_offset_y",b"_roi_offset_y"]) -> typing_extensions___Literal["roi_offset_y"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_strobing",b"_strobing"]) -> typing_extensions___Literal["strobing"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_wb_ratio_blue",b"_wb_ratio_blue"]) -> typing_extensions___Literal["wb_ratio_blue"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_wb_ratio_green",b"_wb_ratio_green"]) -> typing_extensions___Literal["wb_ratio_green"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_wb_ratio_red",b"_wb_ratio_red"]) -> typing_extensions___Literal["wb_ratio_red"]: ...
type___SetCameraSettingsRequest = SetCameraSettingsRequest

class SetCameraSettingsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        cam_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_ids",b"cam_ids"]) -> None: ...
type___SetCameraSettingsResponse = SetCameraSettingsResponse

class SetAutoWhitebalanceRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enable: builtin___bool = ...
    cam_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        enable : typing___Optional[builtin___bool] = None,
        cam_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_ids",b"cam_ids",u"enable",b"enable"]) -> None: ...
type___SetAutoWhitebalanceRequest = SetAutoWhitebalanceRequest

class SetAutoWhitebalanceResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetAutoWhitebalanceResponse = SetAutoWhitebalanceResponse

class GetCameraSettingsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        cam_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_ids",b"cam_ids"]) -> None: ...
type___GetCameraSettingsRequest = GetCameraSettingsRequest

class CameraSettingsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    exposure_us: builtin___float = ...
    gamma: builtin___float = ...
    gain_db: builtin___float = ...
    light_source_preset: lib___common___camera___proto___camera_pb2___LightSourcePresetValue = ...
    wb_ratio_red: builtin___float = ...
    wb_ratio_green: builtin___float = ...
    wb_ratio_blue: builtin___float = ...
    roi_width: builtin___int = ...
    roi_height: builtin___int = ...
    roi_offset_x: builtin___int = ...
    roi_offset_y: builtin___int = ...
    gpu_id: builtin___int = ...
    mirror: builtin___bool = ...
    flip: builtin___bool = ...
    strobing: builtin___bool = ...
    ptp: builtin___bool = ...
    auto_whitebalance: builtin___bool = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        exposure_us : typing___Optional[builtin___float] = None,
        gamma : typing___Optional[builtin___float] = None,
        gain_db : typing___Optional[builtin___float] = None,
        light_source_preset : typing___Optional[lib___common___camera___proto___camera_pb2___LightSourcePresetValue] = None,
        wb_ratio_red : typing___Optional[builtin___float] = None,
        wb_ratio_green : typing___Optional[builtin___float] = None,
        wb_ratio_blue : typing___Optional[builtin___float] = None,
        roi_width : typing___Optional[builtin___int] = None,
        roi_height : typing___Optional[builtin___int] = None,
        roi_offset_x : typing___Optional[builtin___int] = None,
        roi_offset_y : typing___Optional[builtin___int] = None,
        gpu_id : typing___Optional[builtin___int] = None,
        mirror : typing___Optional[builtin___bool] = None,
        flip : typing___Optional[builtin___bool] = None,
        strobing : typing___Optional[builtin___bool] = None,
        ptp : typing___Optional[builtin___bool] = None,
        auto_whitebalance : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_auto_whitebalance",b"_auto_whitebalance",u"_exposure_us",b"_exposure_us",u"_gain_db",b"_gain_db",u"_gamma",b"_gamma",u"_gpu_id",b"_gpu_id",u"_light_source_preset",b"_light_source_preset",u"_roi_height",b"_roi_height",u"_roi_offset_x",b"_roi_offset_x",u"_roi_offset_y",b"_roi_offset_y",u"_roi_width",b"_roi_width",u"_wb_ratio_blue",b"_wb_ratio_blue",u"_wb_ratio_green",b"_wb_ratio_green",u"_wb_ratio_red",b"_wb_ratio_red",u"auto_whitebalance",b"auto_whitebalance",u"exposure_us",b"exposure_us",u"gain_db",b"gain_db",u"gamma",b"gamma",u"gpu_id",b"gpu_id",u"light_source_preset",b"light_source_preset",u"roi_height",b"roi_height",u"roi_offset_x",b"roi_offset_x",u"roi_offset_y",b"roi_offset_y",u"roi_width",b"roi_width",u"wb_ratio_blue",b"wb_ratio_blue",u"wb_ratio_green",b"wb_ratio_green",u"wb_ratio_red",b"wb_ratio_red"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_auto_whitebalance",b"_auto_whitebalance",u"_exposure_us",b"_exposure_us",u"_gain_db",b"_gain_db",u"_gamma",b"_gamma",u"_gpu_id",b"_gpu_id",u"_light_source_preset",b"_light_source_preset",u"_roi_height",b"_roi_height",u"_roi_offset_x",b"_roi_offset_x",u"_roi_offset_y",b"_roi_offset_y",u"_roi_width",b"_roi_width",u"_wb_ratio_blue",b"_wb_ratio_blue",u"_wb_ratio_green",b"_wb_ratio_green",u"_wb_ratio_red",b"_wb_ratio_red",u"auto_whitebalance",b"auto_whitebalance",u"cam_id",b"cam_id",u"exposure_us",b"exposure_us",u"flip",b"flip",u"gain_db",b"gain_db",u"gamma",b"gamma",u"gpu_id",b"gpu_id",u"light_source_preset",b"light_source_preset",u"mirror",b"mirror",u"ptp",b"ptp",u"roi_height",b"roi_height",u"roi_offset_x",b"roi_offset_x",u"roi_offset_y",b"roi_offset_y",u"roi_width",b"roi_width",u"strobing",b"strobing",u"wb_ratio_blue",b"wb_ratio_blue",u"wb_ratio_green",b"wb_ratio_green",u"wb_ratio_red",b"wb_ratio_red"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_auto_whitebalance",b"_auto_whitebalance"]) -> typing_extensions___Literal["auto_whitebalance"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_exposure_us",b"_exposure_us"]) -> typing_extensions___Literal["exposure_us"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_gain_db",b"_gain_db"]) -> typing_extensions___Literal["gain_db"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_gamma",b"_gamma"]) -> typing_extensions___Literal["gamma"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_gpu_id",b"_gpu_id"]) -> typing_extensions___Literal["gpu_id"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_light_source_preset",b"_light_source_preset"]) -> typing_extensions___Literal["light_source_preset"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_roi_height",b"_roi_height"]) -> typing_extensions___Literal["roi_height"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_roi_offset_x",b"_roi_offset_x"]) -> typing_extensions___Literal["roi_offset_x"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_roi_offset_y",b"_roi_offset_y"]) -> typing_extensions___Literal["roi_offset_y"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_roi_width",b"_roi_width"]) -> typing_extensions___Literal["roi_width"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_wb_ratio_blue",b"_wb_ratio_blue"]) -> typing_extensions___Literal["wb_ratio_blue"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_wb_ratio_green",b"_wb_ratio_green"]) -> typing_extensions___Literal["wb_ratio_green"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_wb_ratio_red",b"_wb_ratio_red"]) -> typing_extensions___Literal["wb_ratio_red"]: ...
type___CameraSettingsResponse = CameraSettingsResponse

class GetCameraSettingsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def camera_settings_response(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CameraSettingsResponse]: ...

    def __init__(self,
        *,
        camera_settings_response : typing___Optional[typing___Iterable[type___CameraSettingsResponse]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera_settings_response",b"camera_settings_response"]) -> None: ...
type___GetCameraSettingsResponse = GetCameraSettingsResponse

class StartBurstRecordFramesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    duration_ms: builtin___int = ...
    path: typing___Text = ...
    dont_capture_predict_image: builtin___bool = ...
    downsample_factor: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        duration_ms : typing___Optional[builtin___int] = None,
        path : typing___Optional[typing___Text] = None,
        dont_capture_predict_image : typing___Optional[builtin___bool] = None,
        downsample_factor : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"dont_capture_predict_image",b"dont_capture_predict_image",u"downsample_factor",b"downsample_factor",u"duration_ms",b"duration_ms",u"path",b"path"]) -> None: ...
type___StartBurstRecordFramesRequest = StartBurstRecordFramesRequest

class StartBurstRecordFramesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StartBurstRecordFramesResponse = StartBurstRecordFramesResponse

class StopBurstRecordFramesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    last_frame_timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        last_frame_timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_last_frame_timestamp_ms",b"_last_frame_timestamp_ms",u"last_frame_timestamp_ms",b"last_frame_timestamp_ms"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_last_frame_timestamp_ms",b"_last_frame_timestamp_ms",u"cam_id",b"cam_id",u"last_frame_timestamp_ms",b"last_frame_timestamp_ms"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_last_frame_timestamp_ms",b"_last_frame_timestamp_ms"]) -> typing_extensions___Literal["last_frame_timestamp_ms"]: ...
type___StopBurstRecordFramesRequest = StopBurstRecordFramesRequest

class StopBurstRecordFramesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StopBurstRecordFramesResponse = StopBurstRecordFramesResponse

class P2POutputProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    matched: builtin___bool = ...
    target_coord_x: builtin___float = ...
    target_coord_y: builtin___float = ...
    target_timestamp_ms: builtin___int = ...
    predict_timestamp_ms: builtin___int = ...
    safe: builtin___bool = ...
    predict_coord_x: builtin___float = ...
    predict_coord_y: builtin___float = ...
    predict_cam: typing___Text = ...

    def __init__(self,
        *,
        matched : typing___Optional[builtin___bool] = None,
        target_coord_x : typing___Optional[builtin___float] = None,
        target_coord_y : typing___Optional[builtin___float] = None,
        target_timestamp_ms : typing___Optional[builtin___int] = None,
        predict_timestamp_ms : typing___Optional[builtin___int] = None,
        safe : typing___Optional[builtin___bool] = None,
        predict_coord_x : typing___Optional[builtin___float] = None,
        predict_coord_y : typing___Optional[builtin___float] = None,
        predict_cam : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"matched",b"matched",u"predict_cam",b"predict_cam",u"predict_coord_x",b"predict_coord_x",u"predict_coord_y",b"predict_coord_y",u"predict_timestamp_ms",b"predict_timestamp_ms",u"safe",b"safe",u"target_coord_x",b"target_coord_x",u"target_coord_y",b"target_coord_y",u"target_timestamp_ms",b"target_timestamp_ms"]) -> None: ...
type___P2POutputProto = P2POutputProto

class GetNextP2POutputRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...
    timeout_ms: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        timeout_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"timeout_ms",b"timeout_ms",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextP2POutputRequest = GetNextP2POutputRequest

class GetConnectorsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    connector_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        cam_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        connector_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_ids",b"cam_ids",u"connector_ids",b"connector_ids"]) -> None: ...
type___GetConnectorsRequest = GetConnectorsRequest

class ConnectorResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    connector_id: typing___Text = ...
    is_enabled: builtin___bool = ...
    reduction_ratio: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        connector_id : typing___Optional[typing___Text] = None,
        is_enabled : typing___Optional[builtin___bool] = None,
        reduction_ratio : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"connector_id",b"connector_id",u"is_enabled",b"is_enabled",u"reduction_ratio",b"reduction_ratio"]) -> None: ...
type___ConnectorResponse = ConnectorResponse

class GetConnectorsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def connector_response(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ConnectorResponse]: ...

    def __init__(self,
        *,
        connector_response : typing___Optional[typing___Iterable[type___ConnectorResponse]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"connector_response",b"connector_response"]) -> None: ...
type___GetConnectorsResponse = GetConnectorsResponse

class SetConnectorsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    connector_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    is_enabled: builtin___bool = ...
    reduction_ratio: builtin___int = ...

    def __init__(self,
        *,
        cam_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        connector_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        is_enabled : typing___Optional[builtin___bool] = None,
        reduction_ratio : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_is_enabled",b"_is_enabled",u"_reduction_ratio",b"_reduction_ratio",u"is_enabled",b"is_enabled",u"reduction_ratio",b"reduction_ratio"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_is_enabled",b"_is_enabled",u"_reduction_ratio",b"_reduction_ratio",u"cam_ids",b"cam_ids",u"connector_ids",b"connector_ids",u"is_enabled",b"is_enabled",u"reduction_ratio",b"reduction_ratio"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_is_enabled",b"_is_enabled"]) -> typing_extensions___Literal["is_enabled"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_reduction_ratio",b"_reduction_ratio"]) -> typing_extensions___Literal["reduction_ratio"]: ...
type___SetConnectorsRequest = SetConnectorsRequest

class SetConnectorsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetConnectorsResponse = SetConnectorsResponse

class NodeTiming(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class StateTimingsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___StateTimingsEntry = StateTimingsEntry

    name: typing___Text = ...
    fps_mean: builtin___float = ...
    fps_99pct: builtin___float = ...
    latency_ms_mean: builtin___float = ...
    latency_ms_99pct: builtin___float = ...
    state: typing___Text = ...

    @property
    def state_timings(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        fps_mean : typing___Optional[builtin___float] = None,
        fps_99pct : typing___Optional[builtin___float] = None,
        latency_ms_mean : typing___Optional[builtin___float] = None,
        latency_ms_99pct : typing___Optional[builtin___float] = None,
        state : typing___Optional[typing___Text] = None,
        state_timings : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fps_99pct",b"fps_99pct",u"fps_mean",b"fps_mean",u"latency_ms_99pct",b"latency_ms_99pct",u"latency_ms_mean",b"latency_ms_mean",u"name",b"name",u"state",b"state",u"state_timings",b"state_timings"]) -> None: ...
type___NodeTiming = NodeTiming

class GetTimingRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetTimingRequest = GetTimingRequest

class GetTimingResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def node_timing(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___NodeTiming]: ...

    def __init__(self,
        *,
        node_timing : typing___Optional[typing___Iterable[type___NodeTiming]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"node_timing",b"node_timing"]) -> None: ...
type___GetTimingResponse = GetTimingResponse

class PredictRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    file_paths: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    timestamps_ms: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        file_paths : typing___Optional[typing___Iterable[typing___Text]] = None,
        timestamps_ms : typing___Optional[typing___Iterable[builtin___int]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"file_paths",b"file_paths",u"timestamps_ms",b"timestamps_ms"]) -> None: ...
type___PredictRequest = PredictRequest

class PredictResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___PredictResponse = PredictResponse

class LoadAndQueueRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    file_paths: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    timestamps_ms: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        file_paths : typing___Optional[typing___Iterable[typing___Text]] = None,
        timestamps_ms : typing___Optional[typing___Iterable[builtin___int]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"file_paths",b"file_paths",u"timestamps_ms",b"timestamps_ms"]) -> None: ...
type___LoadAndQueueRequest = LoadAndQueueRequest

class LoadAndQueueResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___LoadAndQueueResponse = LoadAndQueueResponse

class SetImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    file_path: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        file_path : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"file_path",b"file_path"]) -> None: ...
type___SetImageRequest = SetImageRequest

class SetImageResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetImageResponse = SetImageResponse

class UnsetImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id"]) -> None: ...
type___UnsetImageRequest = UnsetImageRequest

class UnsetImageResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___UnsetImageResponse = UnsetImageResponse

class GetModelPathsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetModelPathsRequest = GetModelPathsRequest

class GetModelPathsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    p2p: typing___Text = ...
    deepweed: typing___Text = ...
    furrows: typing___Text = ...

    def __init__(self,
        *,
        p2p : typing___Optional[typing___Text] = None,
        deepweed : typing___Optional[typing___Text] = None,
        furrows : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_deepweed",b"_deepweed",u"_furrows",b"_furrows",u"_p2p",b"_p2p",u"deepweed",b"deepweed",u"furrows",b"furrows",u"p2p",b"p2p"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_deepweed",b"_deepweed",u"_furrows",b"_furrows",u"_p2p",b"_p2p",u"deepweed",b"deepweed",u"furrows",b"furrows",u"p2p",b"p2p"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_deepweed",b"_deepweed"]) -> typing_extensions___Literal["deepweed"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_furrows",b"_furrows"]) -> typing_extensions___Literal["furrows"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_p2p",b"_p2p"]) -> typing_extensions___Literal["p2p"]: ...
type___GetModelPathsResponse = GetModelPathsResponse

class GetCameraTemperaturesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetCameraTemperaturesRequest = GetCameraTemperaturesRequest

class CameraTemperature(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    temperature: builtin___float = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        temperature : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"temperature",b"temperature"]) -> None: ...
type___CameraTemperature = CameraTemperature

class GetCameraTemperaturesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def temperature(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CameraTemperature]: ...

    def __init__(self,
        *,
        temperature : typing___Optional[typing___Iterable[type___CameraTemperature]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"temperature",b"temperature"]) -> None: ...
type___GetCameraTemperaturesResponse = GetCameraTemperaturesResponse

class GeoLLA(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lat: builtin___float = ...
    lng: builtin___float = ...
    alt: builtin___float = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        lat : typing___Optional[builtin___float] = None,
        lng : typing___Optional[builtin___float] = None,
        alt : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_alt",b"_alt",u"_lat",b"_lat",u"_lng",b"_lng",u"_timestamp_ms",b"_timestamp_ms",u"alt",b"alt",u"lat",b"lat",u"lng",b"lng",u"timestamp_ms",b"timestamp_ms"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_alt",b"_alt",u"_lat",b"_lat",u"_lng",b"_lng",u"_timestamp_ms",b"_timestamp_ms",u"alt",b"alt",u"lat",b"lat",u"lng",b"lng",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_alt",b"_alt"]) -> typing_extensions___Literal["alt"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_lat",b"_lat"]) -> typing_extensions___Literal["lat"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_lng",b"_lng"]) -> typing_extensions___Literal["lng"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_timestamp_ms",b"_timestamp_ms"]) -> typing_extensions___Literal["timestamp_ms"]: ...
type___GeoLLA = GeoLLA

class GeoECEF(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___float = ...
    y: builtin___float = ...
    z: builtin___float = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        z : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_timestamp_ms",b"_timestamp_ms",u"_x",b"_x",u"_y",b"_y",u"_z",b"_z",u"timestamp_ms",b"timestamp_ms",u"x",b"x",u"y",b"y",u"z",b"z"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_timestamp_ms",b"_timestamp_ms",u"_x",b"_x",u"_y",b"_y",u"_z",b"_z",u"timestamp_ms",b"timestamp_ms",u"x",b"x",u"y",b"y",u"z",b"z"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_timestamp_ms",b"_timestamp_ms"]) -> typing_extensions___Literal["timestamp_ms"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_x",b"_x"]) -> typing_extensions___Literal["x"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_y",b"_y"]) -> typing_extensions___Literal["y"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_z",b"_z"]) -> typing_extensions___Literal["z"]: ...
type___GeoECEF = GeoECEF

class SetGPSLocationRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def lla(self) -> type___GeoLLA: ...

    @property
    def ecef(self) -> type___GeoECEF: ...

    def __init__(self,
        *,
        lla : typing___Optional[type___GeoLLA] = None,
        ecef : typing___Optional[type___GeoECEF] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ecef",b"ecef",u"lla",b"lla"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ecef",b"ecef",u"lla",b"lla"]) -> None: ...
type___SetGPSLocationRequest = SetGPSLocationRequest

class SetGPSLocationResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetGPSLocationResponse = SetGPSLocationResponse

class SetImplementStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lifted: builtin___bool = ...
    estopped: builtin___bool = ...

    def __init__(self,
        *,
        lifted : typing___Optional[builtin___bool] = None,
        estopped : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"estopped",b"estopped",u"lifted",b"lifted"]) -> None: ...
type___SetImplementStatusRequest = SetImplementStatusRequest

class SetImplementStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetImplementStatusResponse = SetImplementStatusResponse

class SetImageScoreRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score: builtin___float = ...
    timestamp_ms: builtin___int = ...
    cam_id: typing___Text = ...

    @property
    def deepweed_output(self) -> type___DeepweedOutput: ...

    def __init__(self,
        *,
        score : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        cam_id : typing___Optional[typing___Text] = None,
        deepweed_output : typing___Optional[type___DeepweedOutput] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"deepweed_output",b"deepweed_output"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"deepweed_output",b"deepweed_output",u"score",b"score",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___SetImageScoreRequest = SetImageScoreRequest

class SetImageScoreResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetImageScoreResponse = SetImageScoreResponse

class GetScoreQueueRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score_type: typing___Text = ...

    def __init__(self,
        *,
        score_type : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"score_type",b"score_type"]) -> None: ...
type___GetScoreQueueRequest = GetScoreQueueRequest

class ScoreObject(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score: builtin___float = ...
    timestamp_ms: builtin___int = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        score : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"score",b"score",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___ScoreObject = ScoreObject

class GetScoreQueueResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def score_object(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ScoreObject]: ...

    def __init__(self,
        *,
        score_object : typing___Optional[typing___Iterable[type___ScoreObject]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"score_object",b"score_object"]) -> None: ...
type___GetScoreQueueResponse = GetScoreQueueResponse

class GetMaxImageScoreRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score_type: typing___Text = ...

    def __init__(self,
        *,
        score_type : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"score_type",b"score_type"]) -> None: ...
type___GetMaxImageScoreRequest = GetMaxImageScoreRequest

class GetMaxImageScoreResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score: builtin___float = ...
    type: typing___Text = ...

    def __init__(self,
        *,
        score : typing___Optional[builtin___float] = None,
        type : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"score",b"score",u"type",b"type"]) -> None: ...
type___GetMaxImageScoreResponse = GetMaxImageScoreResponse

class GetMaxScoredImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score_type: typing___Text = ...

    def __init__(self,
        *,
        score_type : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"score_type",b"score_type"]) -> None: ...
type___GetMaxScoredImageRequest = GetMaxScoredImageRequest

class GetLatestP2PImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score_type: typing___Text = ...
    reason: proto___cv___cv_pb2___P2PCaptureReasonValue = ...

    def __init__(self,
        *,
        score_type : typing___Optional[typing___Text] = None,
        reason : typing___Optional[proto___cv___cv_pb2___P2PCaptureReasonValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"reason",b"reason",u"score_type",b"score_type"]) -> None: ...
type___GetLatestP2PImageRequest = GetLatestP2PImageRequest

class GetLatestImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id"]) -> None: ...
type___GetLatestImageRequest = GetLatestImageRequest

class GetImageNearTimestampRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetImageNearTimestampRequest = GetImageNearTimestampRequest

class GetChipImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score_type: typing___Text = ...

    def __init__(self,
        *,
        score_type : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"score_type",b"score_type"]) -> None: ...
type___GetChipImageRequest = GetChipImageRequest

class FlushQueuesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score_type: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    score_queue_type: type___ScoreQueueTypeValue = ...

    def __init__(self,
        *,
        score_type : typing___Optional[typing___Iterable[typing___Text]] = None,
        score_queue_type : typing___Optional[type___ScoreQueueTypeValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_score_queue_type",b"_score_queue_type",u"score_queue_type",b"score_queue_type"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_score_queue_type",b"_score_queue_type",u"score_queue_type",b"score_queue_type",u"score_type",b"score_type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_score_queue_type",b"_score_queue_type"]) -> typing_extensions___Literal["score_queue_type"]: ...
type___FlushQueuesRequest = FlushQueuesRequest

class FlushQueuesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___FlushQueuesResponse = FlushQueuesResponse

class ImageAndMetadataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    bytes: builtin___bytes = ...
    width: builtin___int = ...
    height: builtin___int = ...
    timestamp_ms: builtin___int = ...
    score: builtin___float = ...
    cam_id: typing___Text = ...
    iso_formatted_time: typing___Text = ...
    lla_lat: builtin___float = ...
    lla_lng: builtin___float = ...
    lla_alt: builtin___float = ...
    lla_timestamp_ms: builtin___int = ...
    ecef_x: builtin___float = ...
    ecef_y: builtin___float = ...
    ecef_z: builtin___float = ...
    ecef_timestamp_ms: builtin___int = ...
    ppi: builtin___float = ...
    score_type: typing___Text = ...
    image_type: typing___Text = ...
    model_url: typing___Text = ...
    crop: typing___Text = ...
    weed_height_columns: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    crop_height_columns: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    bbh_offset_mm: builtin___float = ...
    focus_metric: builtin___float = ...
    exposure_us: builtin___float = ...
    crop_point_threshold: builtin___float = ...
    weed_point_threshold: builtin___float = ...
    weeding_enabled: builtin___bool = ...
    thinning_enabled: builtin___bool = ...
    deepweed_id: typing___Text = ...
    p2p_id: typing___Text = ...
    segmentation_threshold: builtin___float = ...
    simulator_generated: builtin___bool = ...
    gain_db: builtin___float = ...

    @property
    def deepweed_detections(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[weed_tracking___proto___weed_tracking_pb2___Detection]: ...

    def __init__(self,
        *,
        bytes : typing___Optional[builtin___bytes] = None,
        width : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        score : typing___Optional[builtin___float] = None,
        cam_id : typing___Optional[typing___Text] = None,
        iso_formatted_time : typing___Optional[typing___Text] = None,
        lla_lat : typing___Optional[builtin___float] = None,
        lla_lng : typing___Optional[builtin___float] = None,
        lla_alt : typing___Optional[builtin___float] = None,
        lla_timestamp_ms : typing___Optional[builtin___int] = None,
        ecef_x : typing___Optional[builtin___float] = None,
        ecef_y : typing___Optional[builtin___float] = None,
        ecef_z : typing___Optional[builtin___float] = None,
        ecef_timestamp_ms : typing___Optional[builtin___int] = None,
        ppi : typing___Optional[builtin___float] = None,
        score_type : typing___Optional[typing___Text] = None,
        image_type : typing___Optional[typing___Text] = None,
        model_url : typing___Optional[typing___Text] = None,
        crop : typing___Optional[typing___Text] = None,
        weed_height_columns : typing___Optional[typing___Iterable[builtin___float]] = None,
        crop_height_columns : typing___Optional[typing___Iterable[builtin___float]] = None,
        bbh_offset_mm : typing___Optional[builtin___float] = None,
        focus_metric : typing___Optional[builtin___float] = None,
        exposure_us : typing___Optional[builtin___float] = None,
        crop_point_threshold : typing___Optional[builtin___float] = None,
        weed_point_threshold : typing___Optional[builtin___float] = None,
        weeding_enabled : typing___Optional[builtin___bool] = None,
        thinning_enabled : typing___Optional[builtin___bool] = None,
        deepweed_id : typing___Optional[typing___Text] = None,
        p2p_id : typing___Optional[typing___Text] = None,
        deepweed_detections : typing___Optional[typing___Iterable[weed_tracking___proto___weed_tracking_pb2___Detection]] = None,
        segmentation_threshold : typing___Optional[builtin___float] = None,
        simulator_generated : typing___Optional[builtin___bool] = None,
        gain_db : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_score_type",b"_score_type",u"score_type",b"score_type"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_score_type",b"_score_type",u"bbh_offset_mm",b"bbh_offset_mm",u"bytes",b"bytes",u"cam_id",b"cam_id",u"crop",b"crop",u"crop_height_columns",b"crop_height_columns",u"crop_point_threshold",b"crop_point_threshold",u"deepweed_detections",b"deepweed_detections",u"deepweed_id",b"deepweed_id",u"ecef_timestamp_ms",b"ecef_timestamp_ms",u"ecef_x",b"ecef_x",u"ecef_y",b"ecef_y",u"ecef_z",b"ecef_z",u"exposure_us",b"exposure_us",u"focus_metric",b"focus_metric",u"gain_db",b"gain_db",u"height",b"height",u"image_type",b"image_type",u"iso_formatted_time",b"iso_formatted_time",u"lla_alt",b"lla_alt",u"lla_lat",b"lla_lat",u"lla_lng",b"lla_lng",u"lla_timestamp_ms",b"lla_timestamp_ms",u"model_url",b"model_url",u"p2p_id",b"p2p_id",u"ppi",b"ppi",u"score",b"score",u"score_type",b"score_type",u"segmentation_threshold",b"segmentation_threshold",u"simulator_generated",b"simulator_generated",u"thinning_enabled",b"thinning_enabled",u"timestamp_ms",b"timestamp_ms",u"weed_height_columns",b"weed_height_columns",u"weed_point_threshold",b"weed_point_threshold",u"weeding_enabled",b"weeding_enabled",u"width",b"width"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_score_type",b"_score_type"]) -> typing_extensions___Literal["score_type"]: ...
type___ImageAndMetadataResponse = ImageAndMetadataResponse

class CategoryPrediction(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    category: typing___Text = ...
    score: builtin___float = ...

    def __init__(self,
        *,
        category : typing___Optional[typing___Text] = None,
        score : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category",b"category",u"score",b"score"]) -> None: ...
type___CategoryPrediction = CategoryPrediction

class ChipPrediction(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___float = ...
    y: builtin___float = ...
    radius: builtin___float = ...
    model_id: typing___Text = ...
    band_status: typing___Text = ...

    @property
    def score(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CategoryPrediction]: ...

    @property
    def embedding_distance(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CategoryPrediction]: ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        radius : typing___Optional[builtin___float] = None,
        model_id : typing___Optional[typing___Text] = None,
        score : typing___Optional[typing___Iterable[type___CategoryPrediction]] = None,
        embedding_distance : typing___Optional[typing___Iterable[type___CategoryPrediction]] = None,
        band_status : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"band_status",b"band_status",u"embedding_distance",b"embedding_distance",u"model_id",b"model_id",u"radius",b"radius",u"score",b"score",u"x",b"x",u"y",b"y"]) -> None: ...
type___ChipPrediction = ChipPrediction

class ChipImageAndMetadataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def image_and_metadata(self) -> type___ImageAndMetadataResponse: ...

    @property
    def prediction_metadata(self) -> type___ChipPrediction: ...

    def __init__(self,
        *,
        image_and_metadata : typing___Optional[type___ImageAndMetadataResponse] = None,
        prediction_metadata : typing___Optional[type___ChipPrediction] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"image_and_metadata",b"image_and_metadata",u"prediction_metadata",b"prediction_metadata"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"image_and_metadata",b"image_and_metadata",u"prediction_metadata",b"prediction_metadata"]) -> None: ...
type___ChipImageAndMetadataResponse = ChipImageAndMetadataResponse

class ChipQueueInformationRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ChipQueueInformationRequest = ChipQueueInformationRequest

class ChipQueueInformationResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def queue_score(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CategoryPrediction]: ...

    def __init__(self,
        *,
        queue_score : typing___Optional[typing___Iterable[type___CategoryPrediction]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"queue_score",b"queue_score"]) -> None: ...
type___ChipQueueInformationResponse = ChipQueueInformationResponse

class P2PImageAndMetadataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    target_bytes: builtin___bytes = ...
    target_width: builtin___int = ...
    target_height: builtin___int = ...
    perspective_bytes: builtin___bytes = ...
    perspective_width: builtin___int = ...
    perspective_height: builtin___int = ...
    annotated_target_bytes: builtin___bytes = ...
    annotated_target_width: builtin___int = ...
    annotated_target_height: builtin___int = ...
    timestamp_ms: builtin___int = ...
    score: builtin___float = ...
    cam_id: typing___Text = ...
    iso_formatted_time: typing___Text = ...
    lla_lat: builtin___float = ...
    lla_lng: builtin___float = ...
    lla_alt: builtin___float = ...
    lla_timestamp_ms: builtin___int = ...
    ecef_x: builtin___float = ...
    ecef_y: builtin___float = ...
    ecef_z: builtin___float = ...
    ecef_timestamp_ms: builtin___int = ...
    ppi: builtin___float = ...
    perspective_ppi: builtin___float = ...
    image_type: typing___Text = ...
    model_url: typing___Text = ...
    crop: typing___Text = ...
    focus_metric: builtin___float = ...
    exposure_us: builtin___float = ...
    weeding_enabled: builtin___bool = ...
    thinning_enabled: builtin___bool = ...
    deepweed_id: typing___Text = ...
    p2p_id: typing___Text = ...

    def __init__(self,
        *,
        target_bytes : typing___Optional[builtin___bytes] = None,
        target_width : typing___Optional[builtin___int] = None,
        target_height : typing___Optional[builtin___int] = None,
        perspective_bytes : typing___Optional[builtin___bytes] = None,
        perspective_width : typing___Optional[builtin___int] = None,
        perspective_height : typing___Optional[builtin___int] = None,
        annotated_target_bytes : typing___Optional[builtin___bytes] = None,
        annotated_target_width : typing___Optional[builtin___int] = None,
        annotated_target_height : typing___Optional[builtin___int] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        score : typing___Optional[builtin___float] = None,
        cam_id : typing___Optional[typing___Text] = None,
        iso_formatted_time : typing___Optional[typing___Text] = None,
        lla_lat : typing___Optional[builtin___float] = None,
        lla_lng : typing___Optional[builtin___float] = None,
        lla_alt : typing___Optional[builtin___float] = None,
        lla_timestamp_ms : typing___Optional[builtin___int] = None,
        ecef_x : typing___Optional[builtin___float] = None,
        ecef_y : typing___Optional[builtin___float] = None,
        ecef_z : typing___Optional[builtin___float] = None,
        ecef_timestamp_ms : typing___Optional[builtin___int] = None,
        ppi : typing___Optional[builtin___float] = None,
        perspective_ppi : typing___Optional[builtin___float] = None,
        image_type : typing___Optional[typing___Text] = None,
        model_url : typing___Optional[typing___Text] = None,
        crop : typing___Optional[typing___Text] = None,
        focus_metric : typing___Optional[builtin___float] = None,
        exposure_us : typing___Optional[builtin___float] = None,
        weeding_enabled : typing___Optional[builtin___bool] = None,
        thinning_enabled : typing___Optional[builtin___bool] = None,
        deepweed_id : typing___Optional[typing___Text] = None,
        p2p_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"annotated_target_bytes",b"annotated_target_bytes",u"annotated_target_height",b"annotated_target_height",u"annotated_target_width",b"annotated_target_width",u"cam_id",b"cam_id",u"crop",b"crop",u"deepweed_id",b"deepweed_id",u"ecef_timestamp_ms",b"ecef_timestamp_ms",u"ecef_x",b"ecef_x",u"ecef_y",b"ecef_y",u"ecef_z",b"ecef_z",u"exposure_us",b"exposure_us",u"focus_metric",b"focus_metric",u"image_type",b"image_type",u"iso_formatted_time",b"iso_formatted_time",u"lla_alt",b"lla_alt",u"lla_lat",b"lla_lat",u"lla_lng",b"lla_lng",u"lla_timestamp_ms",b"lla_timestamp_ms",u"model_url",b"model_url",u"p2p_id",b"p2p_id",u"perspective_bytes",b"perspective_bytes",u"perspective_height",b"perspective_height",u"perspective_ppi",b"perspective_ppi",u"perspective_width",b"perspective_width",u"ppi",b"ppi",u"score",b"score",u"target_bytes",b"target_bytes",u"target_height",b"target_height",u"target_width",b"target_width",u"thinning_enabled",b"thinning_enabled",u"timestamp_ms",b"timestamp_ms",u"weeding_enabled",b"weeding_enabled"]) -> None: ...
type___P2PImageAndMetadataResponse = P2PImageAndMetadataResponse

class GetCameraInfoRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetCameraInfoRequest = GetCameraInfoRequest

class CameraInfo(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    ip_address: typing___Text = ...
    serial_number: typing___Text = ...
    model: typing___Text = ...
    width: builtin___int = ...
    height: builtin___int = ...
    connected: builtin___bool = ...
    link_speed: builtin___int = ...
    error_type: type___ErrorTypeValue = ...
    v4l2_device_id: typing___Text = ...
    firmware_version: typing___Text = ...
    latest_firmware_version: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ip_address : typing___Optional[typing___Text] = None,
        serial_number : typing___Optional[typing___Text] = None,
        model : typing___Optional[typing___Text] = None,
        width : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        connected : typing___Optional[builtin___bool] = None,
        link_speed : typing___Optional[builtin___int] = None,
        error_type : typing___Optional[type___ErrorTypeValue] = None,
        v4l2_device_id : typing___Optional[typing___Text] = None,
        firmware_version : typing___Optional[typing___Text] = None,
        latest_firmware_version : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_ip_address",b"_ip_address",u"_latest_firmware_version",b"_latest_firmware_version",u"_serial_number",b"_serial_number",u"_v4l2_device_id",b"_v4l2_device_id",u"ip_address",b"ip_address",u"latest_firmware_version",b"latest_firmware_version",u"serial_number",b"serial_number",u"v4l2_device_id",b"v4l2_device_id"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_ip_address",b"_ip_address",u"_latest_firmware_version",b"_latest_firmware_version",u"_serial_number",b"_serial_number",u"_v4l2_device_id",b"_v4l2_device_id",u"cam_id",b"cam_id",u"connected",b"connected",u"error_type",b"error_type",u"firmware_version",b"firmware_version",u"height",b"height",u"ip_address",b"ip_address",u"latest_firmware_version",b"latest_firmware_version",u"link_speed",b"link_speed",u"model",b"model",u"serial_number",b"serial_number",u"v4l2_device_id",b"v4l2_device_id",u"width",b"width"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_ip_address",b"_ip_address"]) -> typing_extensions___Literal["ip_address"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_latest_firmware_version",b"_latest_firmware_version"]) -> typing_extensions___Literal["latest_firmware_version"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_serial_number",b"_serial_number"]) -> typing_extensions___Literal["serial_number"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_v4l2_device_id",b"_v4l2_device_id"]) -> typing_extensions___Literal["v4l2_device_id"]: ...
type___CameraInfo = CameraInfo

class GetCameraInfoResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def camera_info(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CameraInfo]: ...

    def __init__(self,
        *,
        camera_info : typing___Optional[typing___Iterable[type___CameraInfo]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera_info",b"camera_info"]) -> None: ...
type___GetCameraInfoResponse = GetCameraInfoResponse

class GetLightweightBurstRecordRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetLightweightBurstRecordRequest = GetLightweightBurstRecordRequest

class GetLightweightBurstRecordResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    zip_file: builtin___bytes = ...
    metadata_file: builtin___bytes = ...

    def __init__(self,
        *,
        zip_file : typing___Optional[builtin___bytes] = None,
        metadata_file : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"metadata_file",b"metadata_file",u"zip_file",b"zip_file"]) -> None: ...
type___GetLightweightBurstRecordResponse = GetLightweightBurstRecordResponse

class GetBootedRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetBootedRequest = GetBootedRequest

class GetBootedResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    booted: builtin___bool = ...

    def __init__(self,
        *,
        booted : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"booted",b"booted"]) -> None: ...
type___GetBootedResponse = GetBootedResponse

class GetReadyRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetReadyRequest = GetReadyRequest

class GetReadyResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class DeepweedReadyStateEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___bool = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___bool] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___DeepweedReadyStateEntry = DeepweedReadyStateEntry

    class P2pReadyStateEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___bool = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___bool] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___P2pReadyStateEntry = P2pReadyStateEntry

    ready: builtin___bool = ...
    booted: builtin___bool = ...

    @property
    def deepweed_ready_state(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___bool]: ...

    @property
    def p2p_ready_state(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___bool]: ...

    def __init__(self,
        *,
        ready : typing___Optional[builtin___bool] = None,
        deepweed_ready_state : typing___Optional[typing___Mapping[typing___Text, builtin___bool]] = None,
        p2p_ready_state : typing___Optional[typing___Mapping[typing___Text, builtin___bool]] = None,
        booted : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"booted",b"booted",u"deepweed_ready_state",b"deepweed_ready_state",u"p2p_ready_state",b"p2p_ready_state",u"ready",b"ready"]) -> None: ...
type___GetReadyResponse = GetReadyResponse

class GetErrorStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    plant_profile_error: builtin___bool = ...
    model_unsupported_embeddings: builtin___bool = ...

    def __init__(self,
        *,
        plant_profile_error : typing___Optional[builtin___bool] = None,
        model_unsupported_embeddings : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model_unsupported_embeddings",b"model_unsupported_embeddings",u"plant_profile_error",b"plant_profile_error"]) -> None: ...
type___GetErrorStateResponse = GetErrorStateResponse

class DeepweedDetection(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___float = ...
    y: builtin___float = ...
    size: builtin___float = ...
    score: builtin___float = ...
    hit_class: type___HitClassValue = ...
    mask_intersections: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    weed_score: builtin___float = ...
    crop_score: builtin___float = ...
    weed_detection_class_scores: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    embedding: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    plant_score: builtin___float = ...
    embedding_category_distances: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    detection_id: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        size : typing___Optional[builtin___float] = None,
        score : typing___Optional[builtin___float] = None,
        hit_class : typing___Optional[type___HitClassValue] = None,
        mask_intersections : typing___Optional[typing___Iterable[builtin___int]] = None,
        weed_score : typing___Optional[builtin___float] = None,
        crop_score : typing___Optional[builtin___float] = None,
        weed_detection_class_scores : typing___Optional[typing___Iterable[builtin___float]] = None,
        embedding : typing___Optional[typing___Iterable[builtin___float]] = None,
        plant_score : typing___Optional[builtin___float] = None,
        embedding_category_distances : typing___Optional[typing___Iterable[builtin___float]] = None,
        detection_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_score",b"crop_score",u"detection_id",b"detection_id",u"embedding",b"embedding",u"embedding_category_distances",b"embedding_category_distances",u"hit_class",b"hit_class",u"mask_intersections",b"mask_intersections",u"plant_score",b"plant_score",u"score",b"score",u"size",b"size",u"weed_detection_class_scores",b"weed_detection_class_scores",u"weed_score",b"weed_score",u"x",b"x",u"y",b"y"]) -> None: ...
type___DeepweedDetection = DeepweedDetection

class DeepweedOutput(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    mask_width: builtin___int = ...
    mask_height: builtin___int = ...
    mask_channels: builtin___int = ...
    mask: builtin___bytes = ...
    mask_channel_classes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    predict_in_distance_buffer: builtin___bool = ...
    timestamp_ms: builtin___int = ...
    weed_detection_classes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    embedding_categories: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    available_for_snapshotting: builtin___bool = ...

    @property
    def detections(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___DeepweedDetection]: ...

    def __init__(self,
        *,
        detections : typing___Optional[typing___Iterable[type___DeepweedDetection]] = None,
        mask_width : typing___Optional[builtin___int] = None,
        mask_height : typing___Optional[builtin___int] = None,
        mask_channels : typing___Optional[builtin___int] = None,
        mask : typing___Optional[builtin___bytes] = None,
        mask_channel_classes : typing___Optional[typing___Iterable[typing___Text]] = None,
        predict_in_distance_buffer : typing___Optional[builtin___bool] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        weed_detection_classes : typing___Optional[typing___Iterable[typing___Text]] = None,
        embedding_categories : typing___Optional[typing___Iterable[typing___Text]] = None,
        available_for_snapshotting : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"available_for_snapshotting",b"available_for_snapshotting",u"detections",b"detections",u"embedding_categories",b"embedding_categories",u"mask",b"mask",u"mask_channel_classes",b"mask_channel_classes",u"mask_channels",b"mask_channels",u"mask_height",b"mask_height",u"mask_width",b"mask_width",u"predict_in_distance_buffer",b"predict_in_distance_buffer",u"timestamp_ms",b"timestamp_ms",u"weed_detection_classes",b"weed_detection_classes"]) -> None: ...
type___DeepweedOutput = DeepweedOutput

class GetDeepweedOutputByTimestampRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetDeepweedOutputByTimestampRequest = GetDeepweedOutputByTimestampRequest

class GetRecommendedStrobeSettingsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetRecommendedStrobeSettingsRequest = GetRecommendedStrobeSettingsRequest

class GetRecommendedStrobeSettingsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    target_camera_fps: builtin___float = ...
    targets_per_predict_ratio: builtin___int = ...

    def __init__(self,
        *,
        target_camera_fps : typing___Optional[builtin___float] = None,
        targets_per_predict_ratio : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"target_camera_fps",b"target_camera_fps",u"targets_per_predict_ratio",b"targets_per_predict_ratio"]) -> None: ...
type___GetRecommendedStrobeSettingsResponse = GetRecommendedStrobeSettingsResponse

class StartP2PBufferringRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id"]) -> None: ...
type___StartP2PBufferringRequest = StartP2PBufferringRequest

class StartP2PBufferringResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StartP2PBufferringResponse = StartP2PBufferringResponse

class StopP2PBufferringRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    save_burst: builtin___bool = ...
    cam_id: typing___Text = ...
    path: typing___Text = ...
    dont_capture_predict_image: builtin___bool = ...
    start_timestamp_ms: builtin___int = ...
    end_timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        save_burst : typing___Optional[builtin___bool] = None,
        cam_id : typing___Optional[typing___Text] = None,
        path : typing___Optional[typing___Text] = None,
        dont_capture_predict_image : typing___Optional[builtin___bool] = None,
        start_timestamp_ms : typing___Optional[builtin___int] = None,
        end_timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"dont_capture_predict_image",b"dont_capture_predict_image",u"end_timestamp_ms",b"end_timestamp_ms",u"path",b"path",u"save_burst",b"save_burst",u"start_timestamp_ms",b"start_timestamp_ms"]) -> None: ...
type___StopP2PBufferringRequest = StopP2PBufferringRequest

class StopP2PBufferringResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StopP2PBufferringResponse = StopP2PBufferringResponse

class P2PCaptureRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    name: typing___Text = ...
    timestamp_ms: builtin___int = ...
    write_to_disk: builtin___bool = ...
    reason: proto___cv___cv_pb2___P2PCaptureReasonValue = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        write_to_disk : typing___Optional[builtin___bool] = None,
        reason : typing___Optional[proto___cv___cv_pb2___P2PCaptureReasonValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"name",b"name",u"reason",b"reason",u"timestamp_ms",b"timestamp_ms",u"write_to_disk",b"write_to_disk"]) -> None: ...
type___P2PCaptureRequest = P2PCaptureRequest

class P2PCaptureResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___P2PCaptureResponse = P2PCaptureResponse

class P2PBufferingBurstPredictMetadata(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    plant_size_px: builtin___float = ...

    def __init__(self,
        *,
        plant_size_px : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"plant_size_px",b"plant_size_px"]) -> None: ...
type___P2PBufferingBurstPredictMetadata = P2PBufferingBurstPredictMetadata

class P2PBufferringBurstCaptureRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    path: typing___Text = ...
    dont_capture_predict_image: builtin___bool = ...
    start_timestamp_ms: builtin___int = ...
    end_timestamp_ms: builtin___int = ...
    predict_path: typing___Text = ...
    predict_path_exists: builtin___bool = ...
    save_predict_metadata: builtin___bool = ...

    @property
    def predict_metadata(self) -> type___P2PBufferingBurstPredictMetadata: ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        path : typing___Optional[typing___Text] = None,
        dont_capture_predict_image : typing___Optional[builtin___bool] = None,
        start_timestamp_ms : typing___Optional[builtin___int] = None,
        end_timestamp_ms : typing___Optional[builtin___int] = None,
        predict_path : typing___Optional[typing___Text] = None,
        predict_path_exists : typing___Optional[builtin___bool] = None,
        save_predict_metadata : typing___Optional[builtin___bool] = None,
        predict_metadata : typing___Optional[type___P2PBufferingBurstPredictMetadata] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_predict_path",b"_predict_path",u"predict_metadata",b"predict_metadata",u"predict_path",b"predict_path"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_predict_path",b"_predict_path",u"cam_id",b"cam_id",u"dont_capture_predict_image",b"dont_capture_predict_image",u"end_timestamp_ms",b"end_timestamp_ms",u"path",b"path",u"predict_metadata",b"predict_metadata",u"predict_path",b"predict_path",u"predict_path_exists",b"predict_path_exists",u"save_predict_metadata",b"save_predict_metadata",u"start_timestamp_ms",b"start_timestamp_ms"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_predict_path",b"_predict_path"]) -> typing_extensions___Literal["predict_path"]: ...
type___P2PBufferringBurstCaptureRequest = P2PBufferringBurstCaptureRequest

class P2PBufferringBurstCaptureResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___P2PBufferringBurstCaptureResponse = P2PBufferringBurstCaptureResponse

class GetNextDeepweedOutputRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    timeout_ms: builtin___int = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        timeout_ms : typing___Optional[builtin___int] = None,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"timeout_ms",b"timeout_ms",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextDeepweedOutputRequest = GetNextDeepweedOutputRequest

class SetTargetingStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weeding_enabled: builtin___bool = ...
    thinning_enabled: builtin___bool = ...

    def __init__(self,
        *,
        weeding_enabled : typing___Optional[builtin___bool] = None,
        thinning_enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"thinning_enabled",b"thinning_enabled",u"weeding_enabled",b"weeding_enabled"]) -> None: ...
type___SetTargetingStateRequest = SetTargetingStateRequest

class SetTargetingStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetTargetingStateResponse = SetTargetingStateResponse

class GetNextFocusMetricRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextFocusMetricRequest = GetNextFocusMetricRequest

class GetNextFocusMetricResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    focus_metric: builtin___float = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        focus_metric : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"focus_metric",b"focus_metric",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextFocusMetricResponse = GetNextFocusMetricResponse

class RemoveDataDirRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    path: typing___Text = ...

    def __init__(self,
        *,
        path : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"path",b"path"]) -> None: ...
type___RemoveDataDirRequest = RemoveDataDirRequest

class RemoveDataDirResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___RemoveDataDirResponse = RemoveDataDirResponse

class LastNImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    num_images: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        num_images : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"num_images",b"num_images"]) -> None: ...
type___LastNImageRequest = LastNImageRequest

class ComputeCapabilitiesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    capabilities: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        capabilities : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"capabilities",b"capabilities"]) -> None: ...
type___ComputeCapabilitiesResponse = ComputeCapabilitiesResponse

class SupportedTensorRTVersionsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    versions: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        versions : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"versions",b"versions"]) -> None: ...
type___SupportedTensorRTVersionsResponse = SupportedTensorRTVersionsResponse

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class ScoreQueueAndCount(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score_queue: typing___Text = ...
    num_items: builtin___int = ...

    def __init__(self,
        *,
        score_queue : typing___Optional[typing___Text] = None,
        num_items : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"num_items",b"num_items",u"score_queue",b"score_queue"]) -> None: ...
type___ScoreQueueAndCount = ScoreQueueAndCount

class ListScoreQueuesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def score_queue(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ScoreQueueAndCount]: ...

    def __init__(self,
        *,
        score_queue : typing___Optional[typing___Iterable[type___ScoreQueueAndCount]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"score_queue",b"score_queue"]) -> None: ...
type___ListScoreQueuesResponse = ListScoreQueuesResponse

class GetCategoryCollectionResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    category_collection_id: typing___Text = ...
    last_updated_timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        category_collection_id : typing___Optional[typing___Text] = None,
        last_updated_timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category_collection_id",b"category_collection_id",u"last_updated_timestamp_ms",b"last_updated_timestamp_ms"]) -> None: ...
type___GetCategoryCollectionResponse = GetCategoryCollectionResponse

class SnapshotPredictImagesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SnapshotPredictImagesRequest = SnapshotPredictImagesRequest

class PcamSnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pcam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        pcam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pcam_id",b"pcam_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___PcamSnapshot = PcamSnapshot

class SnapshotPredictImagesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def snapshots(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PcamSnapshot]: ...

    def __init__(self,
        *,
        snapshots : typing___Optional[typing___Iterable[type___PcamSnapshot]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"snapshots",b"snapshots"]) -> None: ...
type___SnapshotPredictImagesResponse = SnapshotPredictImagesResponse

class GetChipForPredictImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pcam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...
    center_x_px: builtin___int = ...
    center_y_px: builtin___int = ...

    def __init__(self,
        *,
        pcam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        center_x_px : typing___Optional[builtin___int] = None,
        center_y_px : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_x_px",b"center_x_px",u"center_y_px",b"center_y_px",u"pcam_id",b"pcam_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetChipForPredictImageRequest = GetChipForPredictImageRequest

class GetChipForPredictImageResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def image_and_metadata(self) -> type___ImageAndMetadataResponse: ...

    def __init__(self,
        *,
        image_and_metadata : typing___Optional[type___ImageAndMetadataResponse] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"image_and_metadata",b"image_and_metadata"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"image_and_metadata",b"image_and_metadata"]) -> None: ...
type___GetChipForPredictImageResponse = GetChipForPredictImageResponse
