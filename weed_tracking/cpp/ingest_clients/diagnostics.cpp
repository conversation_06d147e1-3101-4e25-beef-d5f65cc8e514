#include "diagnostics.hpp"
#include "cv/runtime/cpp/client/cv_runtime_client.hpp"
#include "frontend/proto/weeding_diagnostics.pb.h"
#include <almanac/cpp/almanac.hpp>
#include <config/client/cpp/config_subscriber.hpp>
#include <filesystem>
#include <google/protobuf/util/json_util.h>
#include <lib/common/cpp/utils/generation.hpp>
#include <lib/common/cpp/utils/role.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>
#include <lib/common/geometric/cpp/global_height_estimator_collection.hpp>
#include <lib/common/robot_definition/cpp/robot_definition.hpp>
#include <scanner/cpp/scanner_wrapper.h>
#include <weed_tracking/cpp/ingest_clients/banding.hpp>
#include <weed_tracking/cpp/ingest_clients/crop_line_detector.hpp>
#include <weed_tracking_libs/arbiter/cpp/decision_algorithms.hpp>

#include <algorithm>
#include <atomic>
#include <random>
#include <unordered_map>

namespace cvrt_isc = cv::runtime::image_service_client;

namespace carbon::weed_tracking {
constexpr size_t num_predict_save_threads = 4;
constexpr size_t num_fill_traj_threads = 5;
const std::string dir_path = "/data/ftp/recorder/diagnostics/";
const std::string local_ip_and_port = "127.0.0.1:15053";
const std::string bridge_ip_and_port = "**********:15053";

std::unordered_map<std::string, std::string> slayer_predict_to_addr_map_builder() {
  std::unordered_map<std::string, std::string> cam_map;
  auto cameras = config::get_global_config_subscriber()->get_config_node("cv", "cameras");
  for (const auto &cam_name : cameras->get_children_names()) {
    if (cam_name.rfind("predict", 0) == 0) {
      bool is_on_primary = cameras->get_node(fmt::format("{}/is_on_primary", cam_name))->get_value<bool>();
      cam_map[cam_name] = !common::has_secondary() || is_on_primary ? local_ip_and_port : bridge_ip_and_port;
    }
  }
  return cam_map;
}

std::unordered_map<std::string, std::string> reaper_predict_to_addr_map_builder() {
  std::unordered_map<std::string, std::string> cam_map;
  auto predict_to_cv_addr =
      lib::common::robot_definition::RobotDefinition::get()->get_local_row_definition()->get_pid_to_cv_address();
  for (const auto &[pid, cv_addr] : predict_to_cv_addr) {
    cam_map[pid] = fmt::format("{}:15053", cv_addr);
  }
  return cam_map;
}

std::unordered_map<std::string, std::string> predict_to_addr_map_builder() {
  if (common::is_reaper()) {
    return reaper_predict_to_addr_map_builder();
  } else {
    return slayer_predict_to_addr_map_builder();
  }
}

const std::unordered_map<std::string, std::string> &predict_to_addr() {
  static std::unordered_map<std::string, std::string> cam_map(predict_to_addr_map_builder());
  return cam_map;
}

void save_predict(const std::string &cam, const std::string &addr_and_port) {
  int64_t ts =
      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
          .count();
  auto image_service_client = cvrt_isc::ImageServiceClient(addr_and_port);
  auto cv_runtime_client = cv::runtime::client::CVRuntimeClient(addr_and_port);
  std::string img_dir_path = dir_path + "predict_images";

  try {
    auto image = image_service_client.get_next_camera_image(cam, ts, false, true, true, true);
    auto image_ts = image->ts().timestamp_ms();
    auto deepweed_output = cv_runtime_client.get_deepweed_output_by_ts(cam, image_ts);

    auto image_file_name = fmt::format("{}/{}_{}.png", img_dir_path, cam, image_ts);
    auto image_meta_file_name = fmt::format("{}/{}_{}.meta.json", img_dir_path, cam, image_ts);
    auto deepweed_file_name = fmt::format("{}/{}_{}.deepweed.json", img_dir_path, cam, image_ts);

    std::ofstream img_file(image_file_name, std::ios::binary | std::ios::out);
    img_file.write(image->data().c_str(), image->data().size());
    img_file.close();

    std::ofstream img_meta_file(image_meta_file_name);
    std::string meta_json;
    google::protobuf::util::JsonPrintOptions print_options;
    print_options.always_print_primitive_fields = true;
    google::protobuf::util::MessageToJsonString(image->annotations(), &meta_json, print_options);
    img_meta_file << meta_json;
    img_meta_file.close();

    std::ofstream img_deepweed_file(deepweed_file_name);
    std::string deepweed_json;
    google::protobuf::util::MessageToJsonString(*deepweed_output.get(), &deepweed_json, print_options);
    img_deepweed_file << deepweed_json;
    img_deepweed_file.close();
  } catch (const std::exception &e) {
    spdlog::error("WeedingDiagnostics: could not capture predict image from {}, error={}", cam, e.what());
  }
}

WeedingDiagnostics::Config::Config()
    : scoped_tree(config::get_global_config_subscriber()->get_config_node("common", "weeding_diagnostics"), false) {
  reload();
}
void WeedingDiagnostics::Config::reload() {
  if (scoped_tree.reload_required()) {
    work_interval_ms = scoped_tree->get_child("work_interval_ms")->get_value<uint64_t>();
    end_recording_images_timeout_ms = scoped_tree->get_child("end_recording_images_timeout_ms")->get_value<uint64_t>();
  }
}

WeedingDiagnostics::WeedingDiagnostics()
    : IngestClient(), status_(::weed_tracking::RecordingStatus::NOT_RECORDING),
      last_snapshot_(std::make_shared<::weed_tracking::DiagnosticsSnapshot>()),
      full_predict_save_pool_(num_predict_save_threads), fill_traj_pool_(num_fill_traj_threads),
      wheel_encoder_recorder_(std::make_unique<wheel_encoder::WheelEncoderRecorder>()), frame_(0),
      last_crop_img_frame_(0), last_weed_img_frame_(0), work_loop_latency_ms_(100), snapshot_job_latency_ns_(100),
      work_thread_(&WeedingDiagnostics::work_loop, this) {
  srand((uint32_t)time(0));
  for (auto pair : AimbotTrackerOwner::get().get_trackers()) {
    deepweed_recorders_.push_back(std::make_unique<DeepweedRecorder>(pair.second));
    lane_height_recorders_.push_back(std::make_unique<LaneHeightRecorder>(pair.second));
  }
}
std::shared_ptr<WeedingDiagnostics> WeedingDiagnostics::get() {
  static std::shared_ptr<WeedingDiagnostics> inst{new WeedingDiagnostics()};
  return inst;
}

void WeedingDiagnostics::work_loop() {
  auto last_start = std::chrono::system_clock::now();
  float crop_imgs_per_sec(0), weed_imgs_per_sec(0);
  size_t frames_per_crop_img(0), frames_per_weed_img(0);
  while (true) {
    ++frame_;
    if (!is_recording()) {
      // Do not update while in the middle of a recording
      cfg_.reload();
    }
    auto now = std::chrono::system_clock::now();
    auto end_time = last_start + std::chrono::milliseconds(cfg_.work_interval_ms);
    work_loop_latency_ms_.add(std::chrono::duration_cast<std::chrono::milliseconds>(now - last_start).count());
    if (end_time > now) {
      std::this_thread::sleep_until(end_time);
    } else {
      spdlog::warn("Diagnostics is overloaded. took {}ms must be less than {}ms",
                   std::chrono::duration_cast<std::chrono::milliseconds>(now - last_start).count(),
                   cfg_.work_interval_ms);
    }
    last_start = std::chrono::system_clock::now();
    bool end_recording = false;
    bool reset_img_info = false;
    {
      std::lock_guard<std::mutex> g(status_mutex_);
      if (status_ == ::weed_tracking::RecordingStatus::RECORDING_STARTED) {
        if (maka_control_timestamp_ms() > end_recording_ts_ms_) {
          end_recording = true;
          snapshot_writer_.reset();
          save_full_predict_ts_ = 0;
        } else {
          if (snapshot_writer_ == nullptr) {
            initialize_recording();
            reset_img_info = true;
            crop_imgs_per_sec = crop_images_per_sec_;
            weed_imgs_per_sec = weed_images_per_sec_;
          }
        }
      }
    }
    if (end_recording) {
      stop_recording();
    }
    if (reset_img_info) {
      float iterations_per_sec = 1 / (float)((float)cfg_.work_interval_ms / 1000.0);
      if (crop_imgs_per_sec <= 0.0f) {
        frames_per_crop_img = 0;
      } else if (crop_imgs_per_sec > iterations_per_sec) {
        frames_per_crop_img = 1;
        spdlog::info("WeedingDiagnostics: reducing img count for crops, cannot capture at specified rate.");
      } else {
        frames_per_crop_img = static_cast<size_t>(ceil(iterations_per_sec / crop_imgs_per_sec));
      }
      if (weed_imgs_per_sec <= 0.0f) {
        frames_per_weed_img = 0;
      } else if (weed_imgs_per_sec > iterations_per_sec) {
        frames_per_weed_img = 1;
        spdlog::info("WeedingDiagnostics: reducing img count for weeds, cannot capture at specified rate.");
      } else {
        frames_per_weed_img = static_cast<size_t>(ceil(iterations_per_sec / weed_imgs_per_sec));
      }
    }

    process_updates();
    mark_for_images(frames_per_crop_img, frames_per_weed_img);
    create_snapshot();
    save_full_predict_images();
  }
}
void WeedingDiagnostics::initialize_recording() {
  std::string file_path = dir_path + "/diagnostic_snapshots.carbon";
  std::filesystem::remove_all(dir_path);
  std::filesystem::create_directories(dir_path);
  snapshot_writer_ = std::make_unique<carbon::recorder::ProtobufStreamFileWriter>("DiagnosticSnapshot", file_path);
  wheel_encoder_recorder_->start(end_recording_ts_ms_ - maka_control_timestamp_ms(), "recorder/diagnostics");
  for (auto &deepweed_recorder : deepweed_recorders_) {
    deepweed_recorder->start(end_recording_ts_ms_ - maka_control_timestamp_ms(), "recorder/diagnostics");
  }
  for (auto &lane_height_recorder : lane_height_recorders_) {
    lane_height_recorder->start(end_recording_ts_ms_ - maka_control_timestamp_ms(), "recorder/diagnostics");
  }
}

void WeedingDiagnostics::attempting_to_save_predict(trajectory::Trajectory::ID id) {
  std::unique_lock<std::mutex> lck(saving_predicts_mutex_);
  attempting_to_save_predicts_.emplace(id);
}
void WeedingDiagnostics::finished_attempting_to_save_predict(trajectory::Trajectory::ID id) {
  std::unique_lock<std::mutex> lck(saving_predicts_mutex_);
  attempting_to_save_predicts_.erase(id);
  saving_predicts_cv_.notify_one();
}
void WeedingDiagnostics::stop_recording() {
  {
    std::unique_lock<std::mutex> lck(saving_predicts_mutex_);
    saving_predicts_cv_.wait(lck, [this] { return attempting_to_save_predicts_.empty(); });
  }

  {
    std::lock_guard<std::mutex> g(marked_for_images_mutex_);
    marked_for_images_.clear();
  }

  full_predict_save_pool_.await();
  for (auto &deepweed_recorder : deepweed_recorders_) {
    deepweed_recorder->join();
  }
  for (auto &lane_height_recorder : lane_height_recorders_) {
    lane_height_recorder->join();
  }
  wheel_encoder_recorder_->join();

  // wait for burst records to finish
  std::this_thread::sleep_for(std::chrono::seconds(5));

  {
    std::lock_guard<std::mutex> g(status_mutex_);
    status_ = ::weed_tracking::RecordingStatus::RECORDING_FINISHED;
  }
}

bool WeedingDiagnostics::should_record_images() {
  std::lock_guard<std::mutex> g(status_mutex_);
  return maka_control_timestamp_ms() < (int64_t)(end_recording_ts_ms_ - cfg_.end_recording_images_timeout_ms);
}

void WeedingDiagnostics::save_full_predict_images() {
  static std::default_random_engine dre;
  if (!is_recording()) {
    return;
  }

  int64_t ts =
      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
          .count();
  if (save_full_predict_ts_ == 0 || save_full_predict_ts_ > ts) {
    return;
  }

  spdlog::info("WeedingDiagnostics: about to capture predict images");
  std::string img_dir_path = dir_path + "predict_images";
  std::filesystem::create_directories(img_dir_path);
  std::vector<std::pair<std::string, std::string>> cams;
  for (const auto &pair : predict_to_addr()) {
    cams.emplace_back(pair);
  }
  std::shuffle(std::begin(cams), std::end(cams), dre);
  for (size_t i = 0; i < 2 && i < cams.size(); ++i) {
    full_predict_save_pool_.add([=]() { save_predict(cams[i].first, cams[i].second); });
  }

  save_full_predict_ts_ = 0;
}

std::tuple<bool, bool> is_x_for_vis(const trajectory::Trajectory *traj) {
  bool is_weed = traj->get_decision(trajectory::DecisionFlag::kWeedingWeed) ||
                 traj->get_decision(trajectory::DecisionFlag::kThinningWeed);
  bool is_crop = traj->get_decision(trajectory::DecisionFlag::kWeedingCrop) ||
                 traj->get_decision(trajectory::DecisionFlag::kThinningCrop) ||
                 traj->get_decision(trajectory::DecisionFlag::kKeepableCrop) ||
                 traj->get_decision(trajectory::DecisionFlag::kBandingCrop);
  return std::make_tuple(is_weed, is_crop);
}

void WeedingDiagnostics::mark_for_images(const size_t &frames_per_crop_img, const size_t &frames_per_weed_img) {
  if (!is_recording() || !should_record_images()) {
    return;
  }
  bool capture_crop = frames_per_crop_img > 0 && frame_ - last_crop_img_frame_ >= frames_per_crop_img;
  bool capture_weed = frames_per_weed_img > 0 && frame_ - last_weed_img_frame_ >= frames_per_weed_img;
  if (!capture_crop && !capture_weed) {
    return;
  }
  std::vector<std::shared_ptr<trajectory::Trajectory>> crops, weeds;
  std::vector<size_t> flaged_crop_idxs, flaged_weed_idxs;
  for (const auto &pair : current_trajectories_) {
    if (pair.second->banding_state() != OutOfBand && pair.second->last_update_in_predict_space()) {
      // Note we can read marked_for_images_ without lock as this thread is the only modifier of the set
      if (!pair.second->decisions_set()) {
        continue;
      }
      auto [is_weed, is_crop] = is_x_for_vis(pair.second.get());
      if (is_weed) {
        if (capture_weed && marked_for_images_.find(pair.first) == marked_for_images_.end()) {
          weeds.emplace_back(pair.second);
          if (pair.second->get_triggered_interest_reasons().size()) {
            flaged_weed_idxs.emplace_back(weeds.size() - 1);
          }
        }
      } else if (is_crop) {
        if (capture_crop && marked_for_images_.find(pair.first) == marked_for_images_.end()) {
          crops.emplace_back(pair.second);
          if (pair.second->get_triggered_interest_reasons().size()) {
            flaged_crop_idxs.emplace_back(crops.size() - 1);
          }
        }
      }
    }
  }
  if (capture_crop && !crops.empty()) {
    last_crop_img_frame_ = frame_;
    size_t idx;
    if (!flaged_crop_idxs.empty()) {
      idx = flaged_crop_idxs[rand() % flaged_crop_idxs.size()];
    } else {
      idx = rand() % crops.size();
    }
    auto crop_to_add = crops[idx];
    {
      std::lock_guard<std::mutex> lock(marked_for_images_mutex_);
      marked_for_images_.insert(crop_to_add->id());
      save_trajectory_predict_images_.add(crop_to_add);
    }
  }
  if (capture_weed && !weeds.empty()) {
    last_weed_img_frame_ = frame_;
    size_t idx;
    if (!flaged_weed_idxs.empty()) {
      idx = flaged_weed_idxs[rand() % flaged_weed_idxs.size()];
    } else {
      idx = rand() % weeds.size();
    }
    auto weed_to_add = weeds[idx];
    {
      std::lock_guard<std::mutex> lock(marked_for_images_mutex_);
      marked_for_images_.insert(weed_to_add->id());
      save_trajectory_predict_images_.add(weed_to_add);
    }
  }
}

void WeedingDiagnostics::process_updates() {
  // process adds
  for (const auto &req : new_requests_.pop_all()) {
    if (req.type == ingest::INGEST_CLIENT_ADD) {
      current_trajectories_.emplace(req.traj->id(), req.traj);
    } else {
      current_trajectories_.erase(req.traj->id());
      {
        std::lock_guard<std::mutex> lock(marked_for_images_mutex_);
        marked_for_images_.erase(req.traj->id());
      }
    }
  }
}

void set_centroid_data(::weed_tracking::TrajectorySnapshot *ts, trajectory::TrackedItemCentroid &centroid) {
  ts->set_x_mm(centroid.get_x_mm());
  ts->set_y_mm(centroid.get_y_mm());
  ts->set_z_mm(centroid.get_z_mm());
}

void set_decisions(::weed_tracking::Decisions *decision, const trajectory::Trajectory *traj) {
  if (traj->decisions_set()) {
    decision->set_weeding_weed(traj->get_decision(trajectory::DecisionFlag::kWeedingWeed));
    decision->set_weeding_crop(traj->get_decision(trajectory::DecisionFlag::kWeedingCrop));
    decision->set_thinning_weed(traj->get_decision(trajectory::DecisionFlag::kThinningWeed));
    decision->set_thinning_crop(traj->get_decision(trajectory::DecisionFlag::kThinningCrop));
    decision->set_keepable_crop(traj->get_decision(trajectory::DecisionFlag::kKeepableCrop));
    decision->set_banding_crop(traj->get_decision(trajectory::DecisionFlag::kBandingCrop));
  }
}

void set_class_data(::weed_tracking::TrajectorySnapshot *ts, const trajectory::Trajectory *traj) {
  auto decision_algorithm = traj->decision_algorithm();

  std::unordered_map<std::string, float> class_map;
  if (decision_algorithm == kEmbeddingsAlgorithm) {
    auto emb_distances = traj->get_avg_embedding_category_distances();
    for (auto const &[k, v] : emb_distances) {
      auto transformed_v = ((-1.0f * v) + 1.0f) / 2.0f;
      class_map[k] = transformed_v;
    }
  } else {
    class_map = traj->get_avg_detection_probabilities();
  }
  for (auto const &[k, v] : class_map) {
    (*ts->mutable_detection_classes())[k] = v;
  }

  set_decisions(ts->mutable_decisions(), traj);
  auto [weed_score, crop_score, plant_score] = traj->get_scores();
  ts->set_weed_score(weed_score);
  ts->set_crop_score(crop_score);
  ts->set_plant_score(plant_score);
  ts->set_num_detections_used_for_decision(static_cast<uint32_t>(traj->get_num_detections()));

  if (!traj->decisions_set()) {
    ts->set_classification(::weed_tracking::Classification::CLASS_UNDECIDED);
    ts->set_num_detections_used_for_decision(0);
    ts->set_confidence(0.0f);
    ts->set_category("UNSET"); // TODO(jfroel)
  } else {
    auto [vis_weed, vis_crop] = is_x_for_vis(traj);
    if (vis_weed && vis_crop) {
      ts->set_classification(::weed_tracking::Classification::CLASS_BOTH);
      ts->set_confidence(std::max(weed_score, crop_score));
      ts->set_category(traj->get_decision_weed_class()); // TODO, using weed for now since crop is always "crop"
      auto size_index = std::max(
          almanac::Almanac::get().get_protobuf_size_category_index(traj->get_decision_weed_class(), traj->size_mm()),
          almanac::Almanac::get().get_protobuf_size_category_index(traj->get_decision_crop_class(), traj->size_mm()));
      ts->set_size_category_index((int32_t)size_index);
    } else if (vis_crop) {
      ts->set_classification(::weed_tracking::Classification::CLASS_CROP);
      ts->set_confidence(crop_score);
      ts->set_category(traj->get_decision_crop_class());
      ts->set_size_category_index((int32_t)almanac::Almanac::get().get_protobuf_size_category_index(
          traj->get_decision_crop_class(), traj->size_mm()));
    } else if (vis_weed) {
      ts->set_classification(::weed_tracking::Classification::CLASS_WEED);
      ts->set_confidence(weed_score);
      ts->set_category(traj->get_decision_weed_class());
      ts->set_size_category_index((int32_t)almanac::Almanac::get().get_protobuf_size_category_index(
          traj->get_decision_weed_class(), traj->size_mm()));
    } else {
      ts->set_classification(::weed_tracking::Classification::CLASS_UNKNOWN);
      ts->set_confidence(plant_score);
      ts->set_category("UNKNOWN");    // TODO
      ts->set_size_category_index(0); // TODO
    }
  }
}

::weed_tracking::ThinningState to_proto(trajectory::ThinningState state) {
  static_assert(::weed_tracking::ThinningState_ARRAYSIZE == (int)trajectory::ThinningState::kThinningStateSize,
                "Proto generated thinning state type enum size does not match Local thinning state type enum size");
  switch (state) {
  case trajectory::ThinningState::kUnset:
    return ::weed_tracking::ThinningState::THINNING_UNSET;
  case trajectory::ThinningState::kMarkedForThinning:
    return ::weed_tracking::ThinningState::THINNING_MARKED_FOR_THINNING;
  case trajectory::ThinningState::kKept:
    return ::weed_tracking::ThinningState::THINNING_KEPT;
  case trajectory::ThinningState::kIgnored:
    return ::weed_tracking::ThinningState::THINNING_IGNORED;
  default:
    return ::weed_tracking::ThinningState::THINNING_UNSET;
  }
  return ::weed_tracking::ThinningState::THINNING_UNSET;
}
::weed_tracking::KillStatus to_proto(trajectory::TrackedItemKillStatus status) {
  static_assert(::weed_tracking::KillStatus_ARRAYSIZE ==
                    (int)trajectory::TrackedItemKillStatus::kTrackedItemKillStatusSize,
                "Proto generated kill status type enum size does not match Local kill status type enum size");
  switch (status) {
  case trajectory::TrackedItemKillStatus::kNotShot:
    return ::weed_tracking::KillStatus::STATUS_NOT_SHOT;
  case trajectory::TrackedItemKillStatus::kBeingShot:
    return ::weed_tracking::KillStatus::STATUS_BEING_SHOT;
  case trajectory::TrackedItemKillStatus::kShot:
    return ::weed_tracking::KillStatus::STATUS_SHOT;
  case trajectory::TrackedItemKillStatus::kPartiallyShot:
    return ::weed_tracking::KillStatus::STATUS_PARTIALLY_SHOT;
  case trajectory::TrackedItemKillStatus::kP2PNotFound:
    return ::weed_tracking::KillStatus::STATUS_P2P_NOT_FOUND;
  case trajectory::TrackedItemKillStatus::kP2PMissingContext:
    return ::weed_tracking::KillStatus::STATUS_P2P_MISSING_CONTEXT;
  default:
    return ::weed_tracking::KillStatus::STATUS_ERROR;
  }
  return ::weed_tracking::KillStatus::STATUS_ERROR;
}

void fill_score_data(
    const trajectory::Trajectory *traj,
    const std::unordered_map<uint32_t, std::unordered_map<trajectory::Trajectory::ID, int>> &current_scores,
    const std::unordered_map<trajectory::Trajectory::ID, scheduling::InvalidReason> &current_unshootables,
    ::weed_tracking::ScoreState *state) {
  state->set_target_state(::weed_tracking::TargetableState::TARGET_NOT_IN_SCHEDULER);
  auto us_it = current_unshootables.find(traj->id());
  if (us_it != current_unshootables.end()) {
    if (us_it->second != scheduling::kNotInvalid) {
      if (us_it->second == scheduling::kMinDoo) {
        state->set_target_state(::weed_tracking::TargetableState::TARGET_DOO_TOO_LOW);
        return;
      } else if (us_it->second == scheduling::kAlmanacAvoid) {
        state->set_target_state(::weed_tracking::TargetableState::TARGET_AVOID_FROM_ALMANAC);
        return;
      } else if (us_it->second == scheduling::kAlmanacIgnored) {
        state->set_target_state(::weed_tracking::TargetableState::TARGET_IGNORED_FROM_ALMANAC);
      } else if (us_it->second == scheduling::kOutOfBand) {
        state->set_target_state(::weed_tracking::TargetableState::TARGET_OUT_OF_BAND);
      } else if (us_it->second == scheduling::kIntersectsWithCrop) {
        state->set_target_state(::weed_tracking::TargetableState::TARGET_INTERSECTS_NON_SHOOTABLE);
      }
    }
  } else {
    if (traj->is_killed()) {
      return;
    } else if (traj->ignorable()) {
      // Ignorable can still be scored but probably won't be shot so want to indicate state still
      state->set_target_state(::weed_tracking::TargetableState::TARGET_IGNORED_FROM_ALMANAC);
    } else if (traj->avoid()) {
      state->set_target_state(::weed_tracking::TargetableState::TARGET_AVOID_FROM_ALMANAC);
    } else if (traj->intersected_with_nonshootable()) {
      state->set_target_state(::weed_tracking::TargetableState::TARGET_INTERSECTS_NON_SHOOTABLE);
    } else if (traj->too_many_extirmination_failures()) {
      state->set_target_state(::weed_tracking::TargetableState::TARGET_TOO_MANY_FAILURES);
    }
  }

  for (auto &sc_mp : current_scores) {
    auto score = sc_mp.second.find(traj->id());
    if (score == sc_mp.second.end()) {
      return; // all maps are the same keys so if not in one not in any
    }
    auto proto_score = state->add_scores();
    proto_score->set_scanner_id(sc_mp.first);
    proto_score->set_score(score->second);
  }
  // since we return if not found we can assume we have scored for all available scanners here
  if (state->target_state() == ::weed_tracking::TargetableState::TARGET_NOT_IN_SCHEDULER) {
    state->set_target_state(::weed_tracking::TargetableState::TARGET_SCORED);
  }
}
void fill_thresholds(const trajectory::Trajectory *traj, ::weed_tracking::Thresholds *thresholds) {
  if (!traj->decisions_set()) {
    thresholds->set_passed_weeding(false);
    thresholds->set_passed_thinning(false);
    thresholds->set_passed_banding(false);
  } else {
    thresholds->set_passed_weeding(traj->get_decision(trajectory::DecisionFlag::kWeedingWeed) ||
                                   traj->get_decision(trajectory::DecisionFlag::kWeedingCrop));
    thresholds->set_passed_thinning(traj->get_decision(trajectory::DecisionFlag::kThinningWeed) ||
                                    traj->get_decision(trajectory::DecisionFlag::kThinningCrop));
    thresholds->set_passed_banding(traj->get_decision(trajectory::DecisionFlag::kBandingCrop));
  }
}
void set_kill_box(::weed_tracking::DiagnosticsSnapshot *snapshot) {
  const auto &scanners = scanner::ScannerWrapperOwner::get().get_scanners();
  float default_height = static_cast<float>(lib::common::geometric::GlobalHeightEstimatorCollection::instance()
                                                .get_height_estimator()
                                                ->get_ground_position_z_mm());
  for (auto &pair : scanners) {
    if (!pair.second->enabled()) {
      continue;
    }
    float avg_height = pair.second->avg_height();
    if (avg_height < default_height / 10.0f) {
      avg_height = default_height;
    }
    auto [min_pan, max_pan] = pair.second->pan_limits();
    auto [min_tilt, max_tilt] = pair.second->tilt_limits();
    auto pos_tl = pair.second->geo_scanner()->get_abs_position_from_servo_with_z(
        std::make_tuple<int32_t, int32_t>(std::forward<int32_t>(max_pan), std::forward<int32_t>(max_tilt)), avg_height);
    auto pos_br = pair.second->geo_scanner()->get_abs_position_from_servo_with_z(
        std::make_tuple<int32_t, int32_t>(std::forward<int32_t>(min_pan), std::forward<int32_t>(min_tilt)), avg_height);
    auto kb = snapshot->add_kill_boxes();
    kb->set_scanner_id(pair.first);
    kb->mutable_top_left()->set_x(std::get<0>(pos_tl));
    kb->mutable_top_left()->set_y(std::get<1>(pos_tl));
    kb->mutable_bottom_right()->set_x(std::get<0>(pos_br));
    kb->mutable_bottom_right()->set_y(std::get<1>(pos_br));
  }
}

void populate_snapshot_metadata(trajectory::Trajectory *traj, ::weed_tracking::SnapshotMetadata *snapshot_metadata) {
  // Find centroids available for snapshotting and randomly select one
  auto distance_perspectives = traj->get_distance_based_perspectives();
  std::vector<std::shared_ptr<trajectory::TrackedItemCentroid>> available_for_snapshotting;
  for (const auto &perspective_centroid : distance_perspectives) {
    if (perspective_centroid->get_available_for_snapshotting()) {
      available_for_snapshotting.push_back(perspective_centroid);
    }
  }

  if (!available_for_snapshotting.empty()) {
    // Set image and coord from random centroid
    size_t random_idx = rand() % available_for_snapshotting.size();
    auto selected_centroid = available_for_snapshotting[random_idx];

    snapshot_metadata->set_pcam_id(selected_centroid->get_pcam_id());
    snapshot_metadata->set_timestamp_ms(selected_centroid->get_timestamp_ms());
    auto coords = selected_centroid->get_p2p_coord();
    snapshot_metadata->set_center_x_px(coords.first);
    snapshot_metadata->set_center_y_px(coords.second);
  }
}

struct SnapshotFactory {
  int64_t timestamp;
  std::shared_ptr<trajectory::Trajectory> trajectory;
  std::unordered_map<uint32_t, std::unordered_map<trajectory::Trajectory::ID, int>> &current_scores;
  std::unordered_map<trajectory::Trajectory::ID, scheduling::InvalidReason> &current_unshootables;
  std::atomic<std::chrono::nanoseconds> *total_time;
  ::weed_tracking::TrajectorySnapshot *ts;

  SnapshotFactory(int64_t &_timestamp, std::shared_ptr<trajectory::Trajectory> _trajectory,
                  std::unordered_map<uint32_t, std::unordered_map<trajectory::Trajectory::ID, int>> &_current_scores,
                  std::unordered_map<trajectory::Trajectory::ID, scheduling::InvalidReason> &_current_unshootables,
                  std::atomic<std::chrono::nanoseconds> *_total_time, ::weed_tracking::TrajectorySnapshot *_ts)
      : timestamp(_timestamp), trajectory(_trajectory), current_scores(_current_scores),
        current_unshootables(_current_unshootables), total_time(_total_time), ts(_ts) {}

  void Build() {
    if (!trajectory) {
      spdlog::error("WeedingDiagnostics:::SnapshotFactory::Build() Missing trajectory");
      return;
    } else if (!ts) {
      spdlog::error("WeedingDiagnostics:::SnapshotFactory::Build() Missing trajectory snapshot");
      return;
    }

    auto start = std::chrono::steady_clock::now();
    ts->set_id(trajectory->id());
    auto centroid = trajectory->get_coords_at_timestamp(timestamp, true);
    if (!centroid) {
      // Since we allow last if ts past available we should never get here
      spdlog::error("Missing centroid for traj {}", trajectory->id());
      auto last_centroid = trajectory->get_last_item();
      set_centroid_data(ts, last_centroid);
    } else {
      set_centroid_data(ts, *centroid);
    }
    set_class_data(ts, trajectory.get());
    ts->set_radius_mm(trajectory->size_mm());
    ts->set_kill_status(to_proto(trajectory->get_kill_status()));
    ts->set_shoot_time_actual_ms(trajectory->total_laser_power_received_at_power());
    ts->set_speculative_shoot_time_actual_ms(trajectory->total_speculative_laser_power_received_at_power());
    ts->set_shoot_time_requested_ms(trajectory->total_laser_power_required_at_power());
    ts->set_marked_for_thinning(trajectory->is_marked_for_thinning()); // TODO remove this as it was deprecated in 1.15
    ts->set_thinning_state(to_proto(trajectory->thinning_state()));
    ts->set_global_pos(trajectory->global_pos_m());
    ts->set_tracker_id(trajectory->tracker_id());
    switch (trajectory->duplicate_status()) {
    case trajectory::DuplicateStatus::kUnset:
      // for now, fallthrough to unique, TODO(jfroel) coordinate with the frontend
    case trajectory::DuplicateStatus::kUnique:
      ts->set_duplicate_status(::weed_tracking::DuplicateStatus::UNIQUE);
      break;
    case trajectory::DuplicateStatus::kPrimary:
      ts->set_duplicate_status(::weed_tracking::DuplicateStatus::PRIMARY);
      ts->set_duplicate_trajectory_id(trajectory->get_duplicate_trajectory()->id());
      break;
    case trajectory::DuplicateStatus::kDuplicate:
      ts->set_duplicate_status(::weed_tracking::DuplicateStatus::DUPLICATE);
      ts->set_duplicate_trajectory_id(trajectory->get_primary_trajectory_id());
      break;
    }
    auto lasers = trajectory->get_assigned_lasers();
    for (auto &l : lasers) {
      ts->add_assigned_lasers(l);
    }
    ts->set_out_of_band(trajectory->banding_state() == OutOfBand);
    ts->set_intersected_with_nonshootable(trajectory->intersected_with_nonshootable());
    fill_score_data(trajectory.get(), current_scores, current_unshootables, ts->mutable_score_state());
    ts->set_doo(trajectory->get_detections_over_opportunities());
    ts->set_distance_perspectives_count(static_cast<uint32_t>(trajectory->distance_based_perspective_count()));
    ts->set_speculative_allowed(trajectory->speculative_allowed());
    ts->set_protected_by_traj((trajectory->nonshootable_type() == trajectory::NonshootableType::kCrop) ||
                              trajectory->crop_intersects_with_weed());
    fill_thresholds(trajectory.get(), ts->mutable_thresholds());

    populate_snapshot_metadata(trajectory.get(), ts->mutable_snapshot_metadata());

    auto end = std::chrono::steady_clock::now();
    total_time->store(total_time->load() + std::chrono::duration_cast<std::chrono::nanoseconds>(end - start));
  }
};

void WeedingDiagnostics::create_snapshot() {
  // adding snapshot
  auto snapshot = std::make_shared<::weed_tracking::DiagnosticsSnapshot>();
  auto timestamp = maka_control_timestamp_ms();

  std::unordered_map<uint32_t, std::unordered_map<trajectory::Trajectory::ID, int>> current_scores;
  std::unordered_map<trajectory::Trajectory::ID, scheduling::InvalidReason> current_unshootables;
  {
    std::unique_lock<std::mutex> lk(score_data_mut_);
    current_scores = current_scores_;
    current_unshootables = current_unshootables_;
  }

  size_t total_vis_trajectories = current_trajectories_.size();

  snapshot->set_timestamp_ms(timestamp);
  snapshot->mutable_trajectories()->Reserve(static_cast<int>(total_vis_trajectories));

  for (size_t i = 0; i < total_vis_trajectories; ++i) {
    snapshot->add_trajectories();
  }

  std::vector<common::ThreadPool::JobID> jobs;
  jobs.reserve(total_vis_trajectories);
  std::vector<SnapshotFactory> factories;
  factories.reserve(total_vis_trajectories);
  std::atomic<std::chrono::nanoseconds> total_time(std::chrono::nanoseconds(0));
  int i = 0;
  for (const auto &[id, trajectory] : current_trajectories_) {
    auto ts = snapshot->mutable_trajectories(i);
    // vector used to store the factories so they don't get destroyed before the jobs are done
    factories.emplace_back(timestamp, trajectory, current_scores, current_unshootables, &total_time, ts);
    jobs.emplace_back(fill_traj_pool_.add_job([factory_ptr = &factories[i]]() { factory_ptr->Build(); }));
    ++i;
  }
  for (const auto &job : jobs) {
    fill_traj_pool_.await_job(job);
  }

  auto avg_time =
      total_vis_trajectories ? total_time.load() / (int64_t)total_vis_trajectories : std::chrono::nanoseconds(0);

  snapshot_job_latency_ns_.add(avg_time.count());

  auto bands = Banding::instance()->get_bands();
  for (const auto &band : bands) {
    auto b = snapshot->add_bands();
    auto width_mm = band.get_end_mm() - band.get_start_mm();
    auto offset_mm = band.get_start_mm() + width_mm / 2;
    b->set_offset_mm(offset_mm);
    b->set_width_mm(width_mm);
  }
  set_kill_box(snapshot.get());

  if (auto cld_snapshot = CLDDiagnosticsConnector::get().get_snapshot(); cld_snapshot) {
    // TODO(jfroel) figure out swap semantics for better performance
    snapshot->mutable_banding_algorithm_snapshot()->CopyFrom(*cld_snapshot);
  }

  if (is_recording()) {
    if (snapshot_writer_ != nullptr) {
      bool res = snapshot_writer_->append(*snapshot.get());
      if (!res) {
        set_status(::weed_tracking::RecordingStatus::RECORDING_FAILED);
        snapshot_writer_ = nullptr;
      }
    }
  }

  {
    std::lock_guard<std::mutex> guard(last_snapshot_mutex_);
    last_snapshot_ = snapshot;
  }
}

void WeedingDiagnostics::start_recording(uint32_t ttl, float crop_images_per_sec, float weed_images_per_sec) {
  spdlog::info("WeedingDiagnostics: Received request to start recording diagnostics");
  {
    std::lock_guard<std::mutex> g(status_mutex_);
    if (status_ == ::weed_tracking::RecordingStatus::RECORDING_STARTED) {
      throw std::runtime_error("WeedingDiagnostics: Recording already in progress");
    }
    status_ = ::weed_tracking::RecordingStatus::RECORDING_STARTED;
    crop_images_per_sec_ = crop_images_per_sec;
    weed_images_per_sec_ = weed_images_per_sec;
    end_recording_ts_ms_ = maka_control_timestamp_ms() + ttl * 1000;
    save_full_predict_ts_ = maka_control_timestamp_ms() + ttl * 500;
  }
}

void WeedingDiagnostics::trajectory_request(const ingest::Request<> &req) { new_requests_.add(req); }

void WeedingDiagnostics::get_last_snapshot(::weed_tracking::DiagnosticsSnapshot *response) {
  std::lock_guard<std::mutex> guard(last_snapshot_mutex_);
  if (last_snapshot_ != nullptr) {
    // TODO(jfroel) figure out swap semantics for better performance
    response->CopyFrom(*last_snapshot_);
  }
}

void WeedingDiagnostics::get_recording_status(::weed_tracking::GetRecordingStatusResponse *response) {
  std::lock_guard<std::mutex> g(status_mutex_);
  response->set_status(status_);
}

bool WeedingDiagnostics::is_recording() {
  std::lock_guard<std::mutex> g(status_mutex_);
  return status_ == ::weed_tracking::RecordingStatus::RECORDING_STARTED;
}

void WeedingDiagnostics::set_status(::weed_tracking::RecordingStatus status) {
  std::lock_guard<std::mutex> g(status_mutex_);
  status_ = status;
}

void WeedingDiagnostics::remove_recordings_directory() {
  // this is only performed by commander when no other
  // recording can be run, so should be safe

  std::filesystem::remove_all(dir_path);
}

bool WeedingDiagnostics::is_marked_for_images(trajectory::Trajectory::ID tid) {
  if (!is_recording() || !should_record_images()) {
    return false;
  }

  std::lock_guard<std::mutex> g(marked_for_images_mutex_);
  return marked_for_images_.find(tid) != marked_for_images_.end();
}

void SaveTrajectoryPredictImages::work() {
  std::unordered_map<std::string, std::shared_ptr<cvrt_isc::ImageServiceClient>> client_map;
  for (const auto &pair : predict_to_addr()) {
    client_map[pair.first] = std::make_shared<cvrt_isc::ImageServiceClient>(pair.second);
  }

  while (true) {
    auto t = queue_.wait_pop(60000);
    if (!t) {
      continue;
    }

    try {
      auto centroid = t.value()->get_first_location();
      auto pcam_id = centroid.get_pcam_id();
      auto coords = centroid.get_p2p_coord();
      auto ts = centroid.get_timestamp_ms();

      spdlog::info("WeedingDiagnostics: about to save predict images for trajectory {}, pcam={}, ts={}, coords={},{}",
                   t.value()->id(), pcam_id, ts, coords.first, coords.second);

      if (client_map.find(pcam_id) == client_map.end()) {
        spdlog::error("WeedingDiagnostics: no image service client for predict camera {}", pcam_id);
        continue;
      }

      auto image =
          client_map[pcam_id]->get_predict_image_by_timestamp(pcam_id, ts, (int)coords.first, (int)coords.second);

      std::string img_dir_path = dir_path + "trajectory_predict_images";
      std::filesystem::create_directories(img_dir_path);
      auto image_file_name = fmt::format("{}/{}.png", img_dir_path, t.value()->id());
      std::ofstream img_file(image_file_name, std::ios::binary | std::ios::out);
      img_file.write(image->data().c_str(), image->data().size());
      img_file.close();

      carbon::frontend::weeding_diagnostics::TrajectoryPredictImageMetadata metadata;
      metadata.set_center_x_px(image->center_x());
      metadata.set_center_y_px(image->center_y());
      metadata.set_radius_px((int)centroid.get_size());
      metadata.set_pcam_id(pcam_id);
      metadata.mutable_ts()->set_timestamp_ms(ts);
      auto image_meta_file_name = fmt::format("{}/{}.meta.json", img_dir_path, t.value()->id());
      std::ofstream img_meta_file(image_meta_file_name);
      std::string meta_json;
      google::protobuf::util::JsonPrintOptions print_options;
      print_options.always_print_primitive_fields = true;
      google::protobuf::util::MessageToJsonString(metadata, &meta_json, print_options);
      img_meta_file << meta_json;
      img_meta_file.close();
    } catch (const std::exception &e) {
      spdlog::error("WeedingDiagnostics: Error in saving trajectory predict image: {}", e.what());
    }
  }
}

void SaveTrajectoryPredictImages::add(std::shared_ptr<trajectory::Trajectory> t) { queue_.add(t); }

void WeedingDiagnostics::set_score_data(
    const std::unordered_map<uint32_t, std::unordered_map<trajectory::Trajectory::ID, int>> &current_scores,
    const std::unordered_map<trajectory::Trajectory::ID, scheduling::InvalidReason> &current_unshootables) {

  std::unique_lock<std::mutex> lk(score_data_mut_);
  current_scores_ = current_scores;
  current_unshootables_ = current_unshootables;
}

} // namespace carbon::weed_tracking