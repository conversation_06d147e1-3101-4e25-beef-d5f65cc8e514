# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: golang/simulator/hardware/proto/sim_UI/sim_UI.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='golang/simulator/hardware/proto/sim_UI/sim_UI.proto',
  package='carbon.simulator_UI',
  syntax='proto3',
  serialized_options=b'Z\014proto/sim_UI',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n3golang/simulator/hardware/proto/sim_UI/sim_UI.proto\x12\x13\x63\x61rbon.simulator_UI\"\x14\n\x12GetVelocityRequest\"\'\n\x13GetVelocityResponse\x12\x10\n\x08velocity\x18\x01 \x01(\x02\"&\n\x12SetVelocityRequest\x12\x10\n\x08velocity\x18\x01 \x01(\x02\"\x15\n\x13SetVelocityResponse\"\x13\n\x11GetPresetsRequest\",\n\rPresetSetting\x12\x0c\n\x04path\x18\x01 \x03(\t\x12\r\n\x05value\x18\x02 \x01(\t\"J\n\x12GetPresetsResponse\x12\x34\n\x08settings\x18\x01 \x03(\x0b\x32\".carbon.simulator_UI.PresetSetting\"G\n\x10SetPresetRequest\x12\x33\n\x07setting\x18\x01 \x01(\x0b\x32\".carbon.simulator_UI.PresetSetting\"\x13\n\x11SetPresetResponse\"b\n\x12\x44iagnosticSettings\x12\x1a\n\x12\x63urrent_diagnostic\x18\x01 \x01(\t\x12\x13\n\x0b\x64iagnostics\x18\x02 \x03(\t\x12\x0b\n\x03row\x18\x03 \x01(\x03\x12\x0e\n\x06ticket\x18\x05 \x01(\x03\".\n\x1cGetDiagnosticSettingsRequest\x12\x0e\n\x06ticket\x18\x01 \x01(\x03\"Z\n\x1dGetDiagnosticSettingsResponse\x12\x39\n\x08settings\x18\x01 \x01(\x0b\x32\'.carbon.simulator_UI.DiagnosticSettings\"G\n\x1cSetDiagnosticSettingsRequest\x12\x1a\n\x12\x63urrent_diagnostic\x18\x01 \x01(\t\x12\x0b\n\x03row\x18\x02 \x01(\x03\"\x1f\n\x1dSetDiagnosticSettingsResponse2\xb0\x05\n\x12SimulatorUIService\x12j\n\x11GetVelocityStream\x12\'.carbon.simulator_UI.GetVelocityRequest\x1a(.carbon.simulator_UI.GetVelocityResponse\"\x00\x30\x01\x12\x62\n\x0bSetVelocity\x12\'.carbon.simulator_UI.SetVelocityRequest\x1a(.carbon.simulator_UI.SetVelocityResponse\"\x00\x12\x66\n\x0fGetPresetStream\x12&.carbon.simulator_UI.GetPresetsRequest\x1a\'.carbon.simulator_UI.GetPresetsResponse\"\x00\x30\x01\x12\\\n\tSetPreset\x12%.carbon.simulator_UI.SetPresetRequest\x1a&.carbon.simulator_UI.SetPresetResponse\"\x00\x12\x80\x01\n\x15GetDiagnosticSettings\x12\x31.carbon.simulator_UI.GetDiagnosticSettingsRequest\x1a\x32.carbon.simulator_UI.GetDiagnosticSettingsResponse\"\x00\x12\x80\x01\n\x15SetDiagnosticSettings\x12\x31.carbon.simulator_UI.SetDiagnosticSettingsRequest\x1a\x32.carbon.simulator_UI.SetDiagnosticSettingsResponse\"\x00\x42\x0eZ\x0cproto/sim_UIb\x06proto3'
)




_GETVELOCITYREQUEST = _descriptor.Descriptor(
  name='GetVelocityRequest',
  full_name='carbon.simulator_UI.GetVelocityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=76,
  serialized_end=96,
)


_GETVELOCITYRESPONSE = _descriptor.Descriptor(
  name='GetVelocityResponse',
  full_name='carbon.simulator_UI.GetVelocityResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='velocity', full_name='carbon.simulator_UI.GetVelocityResponse.velocity', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=98,
  serialized_end=137,
)


_SETVELOCITYREQUEST = _descriptor.Descriptor(
  name='SetVelocityRequest',
  full_name='carbon.simulator_UI.SetVelocityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='velocity', full_name='carbon.simulator_UI.SetVelocityRequest.velocity', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=139,
  serialized_end=177,
)


_SETVELOCITYRESPONSE = _descriptor.Descriptor(
  name='SetVelocityResponse',
  full_name='carbon.simulator_UI.SetVelocityResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=179,
  serialized_end=200,
)


_GETPRESETSREQUEST = _descriptor.Descriptor(
  name='GetPresetsRequest',
  full_name='carbon.simulator_UI.GetPresetsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=202,
  serialized_end=221,
)


_PRESETSETTING = _descriptor.Descriptor(
  name='PresetSetting',
  full_name='carbon.simulator_UI.PresetSetting',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='path', full_name='carbon.simulator_UI.PresetSetting.path', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.simulator_UI.PresetSetting.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=223,
  serialized_end=267,
)


_GETPRESETSRESPONSE = _descriptor.Descriptor(
  name='GetPresetsResponse',
  full_name='carbon.simulator_UI.GetPresetsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='settings', full_name='carbon.simulator_UI.GetPresetsResponse.settings', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=269,
  serialized_end=343,
)


_SETPRESETREQUEST = _descriptor.Descriptor(
  name='SetPresetRequest',
  full_name='carbon.simulator_UI.SetPresetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='setting', full_name='carbon.simulator_UI.SetPresetRequest.setting', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=345,
  serialized_end=416,
)


_SETPRESETRESPONSE = _descriptor.Descriptor(
  name='SetPresetResponse',
  full_name='carbon.simulator_UI.SetPresetResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=418,
  serialized_end=437,
)


_DIAGNOSTICSETTINGS = _descriptor.Descriptor(
  name='DiagnosticSettings',
  full_name='carbon.simulator_UI.DiagnosticSettings',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_diagnostic', full_name='carbon.simulator_UI.DiagnosticSettings.current_diagnostic', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diagnostics', full_name='carbon.simulator_UI.DiagnosticSettings.diagnostics', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row', full_name='carbon.simulator_UI.DiagnosticSettings.row', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ticket', full_name='carbon.simulator_UI.DiagnosticSettings.ticket', index=3,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=439,
  serialized_end=537,
)


_GETDIAGNOSTICSETTINGSREQUEST = _descriptor.Descriptor(
  name='GetDiagnosticSettingsRequest',
  full_name='carbon.simulator_UI.GetDiagnosticSettingsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ticket', full_name='carbon.simulator_UI.GetDiagnosticSettingsRequest.ticket', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=539,
  serialized_end=585,
)


_GETDIAGNOSTICSETTINGSRESPONSE = _descriptor.Descriptor(
  name='GetDiagnosticSettingsResponse',
  full_name='carbon.simulator_UI.GetDiagnosticSettingsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='settings', full_name='carbon.simulator_UI.GetDiagnosticSettingsResponse.settings', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=587,
  serialized_end=677,
)


_SETDIAGNOSTICSETTINGSREQUEST = _descriptor.Descriptor(
  name='SetDiagnosticSettingsRequest',
  full_name='carbon.simulator_UI.SetDiagnosticSettingsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_diagnostic', full_name='carbon.simulator_UI.SetDiagnosticSettingsRequest.current_diagnostic', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row', full_name='carbon.simulator_UI.SetDiagnosticSettingsRequest.row', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=679,
  serialized_end=750,
)


_SETDIAGNOSTICSETTINGSRESPONSE = _descriptor.Descriptor(
  name='SetDiagnosticSettingsResponse',
  full_name='carbon.simulator_UI.SetDiagnosticSettingsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=752,
  serialized_end=783,
)

_GETPRESETSRESPONSE.fields_by_name['settings'].message_type = _PRESETSETTING
_SETPRESETREQUEST.fields_by_name['setting'].message_type = _PRESETSETTING
_GETDIAGNOSTICSETTINGSRESPONSE.fields_by_name['settings'].message_type = _DIAGNOSTICSETTINGS
DESCRIPTOR.message_types_by_name['GetVelocityRequest'] = _GETVELOCITYREQUEST
DESCRIPTOR.message_types_by_name['GetVelocityResponse'] = _GETVELOCITYRESPONSE
DESCRIPTOR.message_types_by_name['SetVelocityRequest'] = _SETVELOCITYREQUEST
DESCRIPTOR.message_types_by_name['SetVelocityResponse'] = _SETVELOCITYRESPONSE
DESCRIPTOR.message_types_by_name['GetPresetsRequest'] = _GETPRESETSREQUEST
DESCRIPTOR.message_types_by_name['PresetSetting'] = _PRESETSETTING
DESCRIPTOR.message_types_by_name['GetPresetsResponse'] = _GETPRESETSRESPONSE
DESCRIPTOR.message_types_by_name['SetPresetRequest'] = _SETPRESETREQUEST
DESCRIPTOR.message_types_by_name['SetPresetResponse'] = _SETPRESETRESPONSE
DESCRIPTOR.message_types_by_name['DiagnosticSettings'] = _DIAGNOSTICSETTINGS
DESCRIPTOR.message_types_by_name['GetDiagnosticSettingsRequest'] = _GETDIAGNOSTICSETTINGSREQUEST
DESCRIPTOR.message_types_by_name['GetDiagnosticSettingsResponse'] = _GETDIAGNOSTICSETTINGSRESPONSE
DESCRIPTOR.message_types_by_name['SetDiagnosticSettingsRequest'] = _SETDIAGNOSTICSETTINGSREQUEST
DESCRIPTOR.message_types_by_name['SetDiagnosticSettingsResponse'] = _SETDIAGNOSTICSETTINGSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetVelocityRequest = _reflection.GeneratedProtocolMessageType('GetVelocityRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETVELOCITYREQUEST,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetVelocityRequest)
  })
_sym_db.RegisterMessage(GetVelocityRequest)

GetVelocityResponse = _reflection.GeneratedProtocolMessageType('GetVelocityResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETVELOCITYRESPONSE,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetVelocityResponse)
  })
_sym_db.RegisterMessage(GetVelocityResponse)

SetVelocityRequest = _reflection.GeneratedProtocolMessageType('SetVelocityRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETVELOCITYREQUEST,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetVelocityRequest)
  })
_sym_db.RegisterMessage(SetVelocityRequest)

SetVelocityResponse = _reflection.GeneratedProtocolMessageType('SetVelocityResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETVELOCITYRESPONSE,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetVelocityResponse)
  })
_sym_db.RegisterMessage(SetVelocityResponse)

GetPresetsRequest = _reflection.GeneratedProtocolMessageType('GetPresetsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPRESETSREQUEST,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetPresetsRequest)
  })
_sym_db.RegisterMessage(GetPresetsRequest)

PresetSetting = _reflection.GeneratedProtocolMessageType('PresetSetting', (_message.Message,), {
  'DESCRIPTOR' : _PRESETSETTING,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.PresetSetting)
  })
_sym_db.RegisterMessage(PresetSetting)

GetPresetsResponse = _reflection.GeneratedProtocolMessageType('GetPresetsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETPRESETSRESPONSE,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetPresetsResponse)
  })
_sym_db.RegisterMessage(GetPresetsResponse)

SetPresetRequest = _reflection.GeneratedProtocolMessageType('SetPresetRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETPRESETREQUEST,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetPresetRequest)
  })
_sym_db.RegisterMessage(SetPresetRequest)

SetPresetResponse = _reflection.GeneratedProtocolMessageType('SetPresetResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETPRESETRESPONSE,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetPresetResponse)
  })
_sym_db.RegisterMessage(SetPresetResponse)

DiagnosticSettings = _reflection.GeneratedProtocolMessageType('DiagnosticSettings', (_message.Message,), {
  'DESCRIPTOR' : _DIAGNOSTICSETTINGS,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.DiagnosticSettings)
  })
_sym_db.RegisterMessage(DiagnosticSettings)

GetDiagnosticSettingsRequest = _reflection.GeneratedProtocolMessageType('GetDiagnosticSettingsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDIAGNOSTICSETTINGSREQUEST,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetDiagnosticSettingsRequest)
  })
_sym_db.RegisterMessage(GetDiagnosticSettingsRequest)

GetDiagnosticSettingsResponse = _reflection.GeneratedProtocolMessageType('GetDiagnosticSettingsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETDIAGNOSTICSETTINGSRESPONSE,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.GetDiagnosticSettingsResponse)
  })
_sym_db.RegisterMessage(GetDiagnosticSettingsResponse)

SetDiagnosticSettingsRequest = _reflection.GeneratedProtocolMessageType('SetDiagnosticSettingsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETDIAGNOSTICSETTINGSREQUEST,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetDiagnosticSettingsRequest)
  })
_sym_db.RegisterMessage(SetDiagnosticSettingsRequest)

SetDiagnosticSettingsResponse = _reflection.GeneratedProtocolMessageType('SetDiagnosticSettingsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETDIAGNOSTICSETTINGSRESPONSE,
  '__module__' : 'golang.simulator.hardware.proto.sim_UI.sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator_UI.SetDiagnosticSettingsResponse)
  })
_sym_db.RegisterMessage(SetDiagnosticSettingsResponse)


DESCRIPTOR._options = None

_SIMULATORUISERVICE = _descriptor.ServiceDescriptor(
  name='SimulatorUIService',
  full_name='carbon.simulator_UI.SimulatorUIService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=786,
  serialized_end=1474,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetVelocityStream',
    full_name='carbon.simulator_UI.SimulatorUIService.GetVelocityStream',
    index=0,
    containing_service=None,
    input_type=_GETVELOCITYREQUEST,
    output_type=_GETVELOCITYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetVelocity',
    full_name='carbon.simulator_UI.SimulatorUIService.SetVelocity',
    index=1,
    containing_service=None,
    input_type=_SETVELOCITYREQUEST,
    output_type=_SETVELOCITYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetPresetStream',
    full_name='carbon.simulator_UI.SimulatorUIService.GetPresetStream',
    index=2,
    containing_service=None,
    input_type=_GETPRESETSREQUEST,
    output_type=_GETPRESETSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetPreset',
    full_name='carbon.simulator_UI.SimulatorUIService.SetPreset',
    index=3,
    containing_service=None,
    input_type=_SETPRESETREQUEST,
    output_type=_SETPRESETRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDiagnosticSettings',
    full_name='carbon.simulator_UI.SimulatorUIService.GetDiagnosticSettings',
    index=4,
    containing_service=None,
    input_type=_GETDIAGNOSTICSETTINGSREQUEST,
    output_type=_GETDIAGNOSTICSETTINGSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetDiagnosticSettings',
    full_name='carbon.simulator_UI.SimulatorUIService.SetDiagnosticSettings',
    index=5,
    containing_service=None,
    input_type=_SETDIAGNOSTICSETTINGSREQUEST,
    output_type=_SETDIAGNOSTICSETTINGSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_SIMULATORUISERVICE)

DESCRIPTOR.services_by_name['SimulatorUIService'] = _SIMULATORUISERVICE

# @@protoc_insertion_point(module_scope)
