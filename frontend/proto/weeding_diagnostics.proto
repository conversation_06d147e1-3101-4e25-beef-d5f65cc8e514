syntax = "proto3";

package carbon.frontend.weeding_diagnostics;
option go_package = "proto/frontend";

import "core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto";
import "cv/runtime/proto/cv_runtime.proto";
import "frontend/proto/image_stream.proto";
import "frontend/proto/util.proto";
import "weed_tracking/proto/weed_tracking.proto";
import "proto/thinning/thinning.proto";
import "recorder/proto/recorder.proto";

message RecordWeedingDiagnosticsRequest {
    string name = 1;
    uint32 ttl_sec = 2;
    float crop_images_per_sec = 3;
    float weed_images_per_sec = 4;
}

message GetCurrentTrajectoriesRequest {
    uint32 row_id = 1;
}

message GetRecordingsListRequest {

}

message GetRecordingsListResponse {
    repeated string name = 1;
}

message GetSnapshotRequest {
    string recording_name = 1;
    uint32 row_id = 2;
    uint32 snapshot_number = 3;
}

message GetSnapshotResponse {
    weed_tracking.DiagnosticsSnapshot snapshot = 1;
}

message OpenRecordingRequest {
    string recording_name = 1;
}

message TrajectoriesWithImages {
    repeated uint32 ids = 1;
}

message PredictImages {
    repeated string names = 1;
}

message PredictImagesPerCam {
    map<int32, PredictImages> images = 1;
}

message OpenRecordingResponse {
    map<uint32, uint32> num_snapshots_per_row = 1;
    StaticRecordingData recording_data = 2;
    map<uint32, TrajectoriesWithImages> trajectory_images_per_row = 3;
    map<uint32, PredictImagesPerCam> predict_images_per_row = 4;
}

message DeleteRecordingRequest {
    string recording_name = 1;
}

message ConfigNodeSnapshot {
    map<string, string> values = 1;
    map<string, ConfigNodeSnapshot> child_nodes = 2;
}

message CameraDimensions {
    uint32 width = 1;
    uint32 height = 2;
}

message RowCameras {
    map<string, CameraDimensions> cams = 1;
}

message StaticRecordingData {
    map<string, bool> lasers_enabled = 1;
    ConfigNodeSnapshot root_config = 2;
    repeated int32 rows_recorded = 3;
    map<uint32, aimbot.GetDimensionsResponse> row_dimensions = 4;
    map<uint32, float> crop_safety_radius_mm_per_row = 5;
    carbon.thinning.ConfigDefinition  thinning_config = 6;
    int64 recording_timestamp = 7;
    map<uint32, RowCameras> row_cameras = 8;
    float weed_point_threshold = 9;
    float crop_point_threshold = 10;
    float wheel_diameter_back_left_in = 11;
    float wheel_diameter_back_right_in = 12;
    float wheel_diameter_front_left_in = 13;
    float wheel_diameter_front_right_in = 14;
}

message GetTrajectoryDataRequest {
    string recording_name = 1;
    uint32 row_id = 2;
    uint32 trajectory_id = 3;
}

message LastSnapshot {
    weed_tracking.TrajectorySnapshot trajectory = 1;
    uint32 diagnostics_snapshot_number = 2;
}

message ExtraTrajectoryField {
    string label = 1;
    string value = 2;
    string color = 3;
}

message TargetImage {
    string name = 1;
    uint64 timestamp = 2;
    uint32 p2p_predict_x = 3;
    uint32 p2p_predict_y = 4;
}

message P2PPredictImage {
    string name = 1;
    int32 center_x_px = 2;
    int32 center_y_px = 3;
    int32 radius_px = 4;
    carbon.frontend.util.Timestamp ts = 5;
    string pcam_id = 6;
}

message TrajectoryPredictImageMetadata {
    int32 center_x_px = 1;
    int32 center_y_px = 2;
    int32 radius_px = 3;
    carbon.frontend.util.Timestamp ts = 4;
    string pcam_id = 5;
}

message TrajectoryData {
    TrajectoryPredictImageMetadata predict_image = 1;
    repeated TargetImage target_images = 2;
    LastSnapshot last_snapshot = 3;
    repeated ExtraTrajectoryField extra_fields = 4;
    uint32 crosshair_x = 5;
    uint32 crosshair_y = 6;
    repeated P2PPredictImage p2p_predict_images = 7;
}

message GetTrajectoryPredictImageRequest {
    string recording_name = 1;
    uint32 row_id = 2;
    uint32 trajectory_id = 3;
}

message GetTrajectoryTargetImageRequest {
    string recording_name = 1;
    uint32 row_id = 2;
    uint32 trajectory_id = 3;
    string image_name = 4;
}

message ImageChunk {
    bytes image_chunk = 1;
}

message GetPredictImageMetadataRequest {
    string recording_name = 1;
    uint32 row_id = 2;
    string image_name = 3;
}

message GetPredictImageMetadataResponse {
    carbon.frontend.util.Timestamp ts = 1;
    carbon.frontend.image_stream.Annotations annotations = 2;
    cv.runtime.proto.DeepweedOutput deepweed_output = 3;
    cv.runtime.proto.DeepweedOutput deepweed_output_below_threshold = 4;
}

message GetPredictImageRequest {
    string recording_name = 1;
    uint32 row_id = 2;
    string image_name = 3;
}

message StartUploadRequest {
    string recording_name = 1;
}

enum UploadState {
    NONE = 0;
    IN_PROGRESS = 1;
    DONE = 2;
}

message GetNextUploadStateRequest {
    string recording_name = 1;
    carbon.frontend.util.Timestamp ts = 2;
}

message GetNextUploadStateResponse {
    UploadState upload_state = 1;
    carbon.frontend.util.Timestamp ts = 2;
    uint32 percent_uploaded = 3;
}

message GetDeepweedPredictionsCountRequest {
    string recording_name = 1;
    uint32 row = 2;
    uint32 cam_id = 3;
}

message GetDeepweedPredictionsCountResponse {
    uint32 count = 1;
}

message GetDeepweedPredictionsRequest {
    string recording_name = 1;
    uint32 row = 2;
    uint32 cam_id = 3;
    uint32 idx = 4;
}

message GetDeepweedPredictionsResponse {
    recorder.DeepweedPredictionRecord predictions = 1;
}

message FindTrajectoryRequest {
    string recording_name = 1;
    uint32 row_id = 2;
    uint32 trajectory_id = 3;
}

message FindTrajectoryResponse {
    uint32 snapshot_id = 1;
    weed_tracking.TrajectorySnapshot trajectory = 2;
}

message GetRotaryTicksRequest {
    string recording_name = 1;
    uint32 row_id = 2;
}

message GetRotaryTicksResponse {
    repeated recorder.RotaryTicksRecord records = 1;
    uint32 wheel_encoder_resolution = 2;
}

message SnapshotPredictImagesRequest {
    uint32 row_id = 1;
}

message PcamSnapshot {
    string pcam_id = 1;
    int64 timestamp_ms = 2;
}

message SnapshotPredictImagesResponse {
    repeated PcamSnapshot snapshots = 1;
}

message GetChipForPredictImageRequest {
    string pcam_id = 1;
    int64 timestamp_ms = 2;
    int32 center_x_px = 3;
    int32 center_y_px = 4;
    uint32 row_id = 5;
}

message GetChipForPredictImageResponse {
    bytes chip_image = 1;
}

service WeedingDiagnosticsService {
    rpc RecordWeedingDiagnostics(RecordWeedingDiagnosticsRequest) returns (carbon.frontend.util.Empty);
    rpc GetCurrentTrajectories(GetCurrentTrajectoriesRequest) returns (weed_tracking.DiagnosticsSnapshot);
    rpc GetRecordingsList(GetRecordingsListRequest) returns (GetRecordingsListResponse);
    rpc OpenRecording(OpenRecordingRequest) returns (OpenRecordingResponse);
    rpc GetSnapshot(GetSnapshotRequest) returns (GetSnapshotResponse);
    rpc DeleteRecording(DeleteRecordingRequest) returns (carbon.frontend.util.Empty);
    rpc GetTrajectoryData(GetTrajectoryDataRequest) returns (TrajectoryData);
    rpc GetTrajectoryPredictImage(GetTrajectoryPredictImageRequest) returns (stream ImageChunk);
    rpc GetTrajectoryTargetImage(GetTrajectoryTargetImageRequest) returns (stream ImageChunk);
    rpc GetPredictImageMetadata(GetPredictImageMetadataRequest) returns (GetPredictImageMetadataResponse);
    rpc GetPredictImage(GetPredictImageRequest) returns (stream ImageChunk);
    rpc StartUpload(StartUploadRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextUploadState(GetNextUploadStateRequest) returns (GetNextUploadStateResponse);
    rpc GetDeepweedPredictionsCount(GetDeepweedPredictionsCountRequest) returns (GetDeepweedPredictionsCountResponse);
    rpc GetDeepweedPredictions(GetDeepweedPredictionsRequest) returns (GetDeepweedPredictionsResponse);
    rpc FindTrajectory(FindTrajectoryRequest) returns (FindTrajectoryResponse);
    rpc GetRotaryTicks(GetRotaryTicksRequest) returns (GetRotaryTicksResponse);
    rpc SnapshotPredictImages(SnapshotPredictImagesRequest) returns (SnapshotPredictImagesResponse);
    rpc GetChipForPredictImage(GetChipForPredictImageRequest) returns (GetChipForPredictImageResponse);
}