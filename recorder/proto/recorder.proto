syntax = "proto3";

package recorder;
option go_package = "proto/recorder";

message DetectionClass {
  string class = 1;
  float score = 2;
}


message DeepweedDetection {
  enum HitClass {
    WEED = 0;
    CROP = 1;
  }

  float x = 1;
  float y = 2;
  float size = 3;
  float score = 4;
  HitClass hit_class = 5;

  repeated DetectionClass detection_classes = 6;
  repeated bytes mask_intersections = 7;
  uint32 trajectory_id = 8;
  float weed_score = 9;
  float crop_score = 10;
  float plant_score = 11;
  repeated float embedding_category_distances = 12;

  float x_undistorted = 13;
  float y_undistorted = 14;
}

message TrackedItemCoordinates {
  float x_mm = 1;
  float y_mm = 2;
  float z_mm = 3;
  float x_px = 4;
  float y_px = 5;
}

message EmbeddingCategory {
  string category = 1;
  float distance = 2;
}

message TrackedItem {
  uint32 trajectory_id = 1;
  bool deduplicated = 2;
  TrackedItemCoordinates coordinates_before = 3;
  TrackedItemCoordinates coordinates_after = 4;
  bool in_camera = 5;
}

message CandidateShift {
  float x_shift = 1;
  float y_shift = 2;
  uint32 centroid_id = 3;
  uint32 trajectory_id = 4;
}

message DeepweedPredictionFrame {
  int64 timestamp_ms = 1;
  repeated DeepweedDetection detections = 2;
  repeated string embedding_categories = 3;
  repeated TrackedItem tracked_items = 4;
  CandidateShift best_shift = 5;
  repeated CandidateShift candidate_shifts = 6;
  bool plant_matcher_valid = 7;
}

message DeepweedPredictionRecord {
  uint64 record_timestamp_ms = 1;
  DeepweedPredictionFrame frame = 2;
}

message LaneHeightSnapshot {
  repeated double weed_height = 1;
  repeated double crop_height = 2;
}

message LaneHeightRecord {
  uint64 record_timestamp_ms = 1;
  LaneHeightSnapshot snapshot = 2;
}

message RotaryTicksSnapshot {
  uint64 timestamp_us = 1;
  int32 fl = 2;
  int32 fr = 3;
  int32 bl = 4;
  int32 br = 5;
  bool fl_enabled = 6;
  bool fr_enabled = 7;
  bool bl_enabled = 8;
  bool br_enabled = 9;
}

message RotaryTicksRecord {
  uint64 record_timestamp_ms = 1;
  RotaryTicksSnapshot snapshot = 2;
}
