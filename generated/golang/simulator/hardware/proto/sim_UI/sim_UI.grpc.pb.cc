// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: golang/simulator/hardware/proto/sim_UI/sim_UI.proto

#include "golang/simulator/hardware/proto/sim_UI/sim_UI.pb.h"
#include "golang/simulator/hardware/proto/sim_UI/sim_UI.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace simulator_UI {

static const char* SimulatorUIService_method_names[] = {
  "/carbon.simulator_UI.SimulatorUIService/GetVelocityStream",
  "/carbon.simulator_UI.SimulatorUIService/SetVelocity",
  "/carbon.simulator_UI.SimulatorUIService/GetPresetStream",
  "/carbon.simulator_UI.SimulatorUIService/SetPreset",
  "/carbon.simulator_UI.SimulatorUIService/GetDiagnosticSettings",
  "/carbon.simulator_UI.SimulatorUIService/SetDiagnosticSettings",
};

std::unique_ptr< SimulatorUIService::Stub> SimulatorUIService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< SimulatorUIService::Stub> stub(new SimulatorUIService::Stub(channel, options));
  return stub;
}

SimulatorUIService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetVelocityStream_(SimulatorUIService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_SetVelocity_(SimulatorUIService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetPresetStream_(SimulatorUIService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_SetPreset_(SimulatorUIService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDiagnosticSettings_(SimulatorUIService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetDiagnosticSettings_(SimulatorUIService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::ClientReader< ::carbon::simulator_UI::GetVelocityResponse>* SimulatorUIService::Stub::GetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::carbon::simulator_UI::GetVelocityResponse>::Create(channel_.get(), rpcmethod_GetVelocityStream_, context, request);
}

void SimulatorUIService::Stub::async::GetVelocityStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest* request, ::grpc::ClientReadReactor< ::carbon::simulator_UI::GetVelocityResponse>* reactor) {
  ::grpc::internal::ClientCallbackReaderFactory< ::carbon::simulator_UI::GetVelocityResponse>::Create(stub_->channel_.get(), stub_->rpcmethod_GetVelocityStream_, context, request, reactor);
}

::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetVelocityResponse>* SimulatorUIService::Stub::AsyncGetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::simulator_UI::GetVelocityResponse>::Create(channel_.get(), cq, rpcmethod_GetVelocityStream_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetVelocityResponse>* SimulatorUIService::Stub::PrepareAsyncGetVelocityStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::simulator_UI::GetVelocityResponse>::Create(channel_.get(), cq, rpcmethod_GetVelocityStream_, context, request, false, nullptr);
}

::grpc::Status SimulatorUIService::Stub::SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::carbon::simulator_UI::SetVelocityResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::simulator_UI::SetVelocityRequest, ::carbon::simulator_UI::SetVelocityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetVelocity_, context, request, response);
}

void SimulatorUIService::Stub::async::SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::simulator_UI::SetVelocityRequest, ::carbon::simulator_UI::SetVelocityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetVelocity_, context, request, response, std::move(f));
}

void SimulatorUIService::Stub::async::SetVelocity(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetVelocity_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetVelocityResponse>* SimulatorUIService::Stub::PrepareAsyncSetVelocityRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::simulator_UI::SetVelocityResponse, ::carbon::simulator_UI::SetVelocityRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetVelocity_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetVelocityResponse>* SimulatorUIService::Stub::AsyncSetVelocityRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetVelocityRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::ClientReader< ::carbon::simulator_UI::GetPresetsResponse>* SimulatorUIService::Stub::GetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::carbon::simulator_UI::GetPresetsResponse>::Create(channel_.get(), rpcmethod_GetPresetStream_, context, request);
}

void SimulatorUIService::Stub::async::GetPresetStream(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest* request, ::grpc::ClientReadReactor< ::carbon::simulator_UI::GetPresetsResponse>* reactor) {
  ::grpc::internal::ClientCallbackReaderFactory< ::carbon::simulator_UI::GetPresetsResponse>::Create(stub_->channel_.get(), stub_->rpcmethod_GetPresetStream_, context, request, reactor);
}

::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetPresetsResponse>* SimulatorUIService::Stub::AsyncGetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::simulator_UI::GetPresetsResponse>::Create(channel_.get(), cq, rpcmethod_GetPresetStream_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::carbon::simulator_UI::GetPresetsResponse>* SimulatorUIService::Stub::PrepareAsyncGetPresetStreamRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetPresetsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::simulator_UI::GetPresetsResponse>::Create(channel_.get(), cq, rpcmethod_GetPresetStream_, context, request, false, nullptr);
}

::grpc::Status SimulatorUIService::Stub::SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::carbon::simulator_UI::SetPresetResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::simulator_UI::SetPresetRequest, ::carbon::simulator_UI::SetPresetResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetPreset_, context, request, response);
}

void SimulatorUIService::Stub::async::SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::simulator_UI::SetPresetRequest, ::carbon::simulator_UI::SetPresetResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetPreset_, context, request, response, std::move(f));
}

void SimulatorUIService::Stub::async::SetPreset(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetPreset_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetPresetResponse>* SimulatorUIService::Stub::PrepareAsyncSetPresetRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::simulator_UI::SetPresetResponse, ::carbon::simulator_UI::SetPresetRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetPreset_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetPresetResponse>* SimulatorUIService::Stub::AsyncSetPresetRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetPresetRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetPresetRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status SimulatorUIService::Stub::GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::carbon::simulator_UI::GetDiagnosticSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDiagnosticSettings_, context, request, response);
}

void SimulatorUIService::Stub::async::GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::carbon::simulator_UI::GetDiagnosticSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDiagnosticSettings_, context, request, response, std::move(f));
}

void SimulatorUIService::Stub::async::GetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDiagnosticSettings_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* SimulatorUIService::Stub::PrepareAsyncGetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::simulator_UI::GetDiagnosticSettingsResponse, ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDiagnosticSettings_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::GetDiagnosticSettingsResponse>* SimulatorUIService::Stub::AsyncGetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDiagnosticSettingsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status SimulatorUIService::Stub::SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::carbon::simulator_UI::SetDiagnosticSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetDiagnosticSettings_, context, request, response);
}

void SimulatorUIService::Stub::async::SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::carbon::simulator_UI::SetDiagnosticSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetDiagnosticSettings_, context, request, response, std::move(f));
}

void SimulatorUIService::Stub::async::SetDiagnosticSettings(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetDiagnosticSettings_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* SimulatorUIService::Stub::PrepareAsyncSetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::simulator_UI::SetDiagnosticSettingsResponse, ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetDiagnosticSettings_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator_UI::SetDiagnosticSettingsResponse>* SimulatorUIService::Stub::AsyncSetDiagnosticSettingsRaw(::grpc::ClientContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetDiagnosticSettingsRaw(context, request, cq);
  result->StartCall();
  return result;
}

SimulatorUIService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SimulatorUIService_method_names[0],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< SimulatorUIService::Service, ::carbon::simulator_UI::GetVelocityRequest, ::carbon::simulator_UI::GetVelocityResponse>(
          [](SimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::simulator_UI::GetVelocityRequest* req,
             ::grpc::ServerWriter<::carbon::simulator_UI::GetVelocityResponse>* writer) {
               return service->GetVelocityStream(ctx, req, writer);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SimulatorUIService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SimulatorUIService::Service, ::carbon::simulator_UI::SetVelocityRequest, ::carbon::simulator_UI::SetVelocityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::simulator_UI::SetVelocityRequest* req,
             ::carbon::simulator_UI::SetVelocityResponse* resp) {
               return service->SetVelocity(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SimulatorUIService_method_names[2],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< SimulatorUIService::Service, ::carbon::simulator_UI::GetPresetsRequest, ::carbon::simulator_UI::GetPresetsResponse>(
          [](SimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::simulator_UI::GetPresetsRequest* req,
             ::grpc::ServerWriter<::carbon::simulator_UI::GetPresetsResponse>* writer) {
               return service->GetPresetStream(ctx, req, writer);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SimulatorUIService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SimulatorUIService::Service, ::carbon::simulator_UI::SetPresetRequest, ::carbon::simulator_UI::SetPresetResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::simulator_UI::SetPresetRequest* req,
             ::carbon::simulator_UI::SetPresetResponse* resp) {
               return service->SetPreset(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SimulatorUIService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SimulatorUIService::Service, ::carbon::simulator_UI::GetDiagnosticSettingsRequest, ::carbon::simulator_UI::GetDiagnosticSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* req,
             ::carbon::simulator_UI::GetDiagnosticSettingsResponse* resp) {
               return service->GetDiagnosticSettings(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SimulatorUIService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SimulatorUIService::Service, ::carbon::simulator_UI::SetDiagnosticSettingsRequest, ::carbon::simulator_UI::SetDiagnosticSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* req,
             ::carbon::simulator_UI::SetDiagnosticSettingsResponse* resp) {
               return service->SetDiagnosticSettings(ctx, req, resp);
             }, this)));
}

SimulatorUIService::Service::~Service() {
}

::grpc::Status SimulatorUIService::Service::GetVelocityStream(::grpc::ServerContext* context, const ::carbon::simulator_UI::GetVelocityRequest* request, ::grpc::ServerWriter< ::carbon::simulator_UI::GetVelocityResponse>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SimulatorUIService::Service::SetVelocity(::grpc::ServerContext* context, const ::carbon::simulator_UI::SetVelocityRequest* request, ::carbon::simulator_UI::SetVelocityResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SimulatorUIService::Service::GetPresetStream(::grpc::ServerContext* context, const ::carbon::simulator_UI::GetPresetsRequest* request, ::grpc::ServerWriter< ::carbon::simulator_UI::GetPresetsResponse>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SimulatorUIService::Service::SetPreset(::grpc::ServerContext* context, const ::carbon::simulator_UI::SetPresetRequest* request, ::carbon::simulator_UI::SetPresetResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SimulatorUIService::Service::GetDiagnosticSettings(::grpc::ServerContext* context, const ::carbon::simulator_UI::GetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::GetDiagnosticSettingsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SimulatorUIService::Service::SetDiagnosticSettings(::grpc::ServerContext* context, const ::carbon::simulator_UI::SetDiagnosticSettingsRequest* request, ::carbon::simulator_UI::SetDiagnosticSettingsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace simulator_UI

