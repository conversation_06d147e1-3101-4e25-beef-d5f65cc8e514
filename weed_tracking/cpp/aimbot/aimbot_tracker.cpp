#include <almanac/cpp/almanac.hpp>
#include <chrono>
#include <spdlog/spdlog.h>
#include <weed_tracking/cpp/aimbot/aimbot_tracker.hpp>
#include <weed_tracking/cpp/ingest_clients/banding.hpp>
#include <weed_tracking/proto/weed_tracking.grpc.pb.h>
#include "generated/recorder/proto/recorder.pb.h"

namespace carbon {
namespace weed_tracking {

AimbotWeedTracker::AimbotWeedTracker(std::string pcam_id, uint32_t numeric_id, uint32_t width, uint32_t height,
                                     bool log_ticks, std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam,
                                     float max_pos_mm_y, bool score_images, uint32_t max_number_of_weeds,
                                     std::string cv_address)
    : pcam_id_(pcam_id), numeric_id_(numeric_id), latest_dw_timestamp_ms_(0),
      cv_runtime_client_(std::make_unique<cv::runtime::client::CVRuntimeClient>(cv_address)), latest_buffer_index_(0),
      latest_post_dedup_buffer_index_(0), start_ready_(false), previous_re_timestamp_ms_(0), current_re_timestamp_ms_(0),
      log_ticks_(log_ticks), geo_cam_(geo_cam), score_images_(score_images), rotary_timeout_(true), deepweed_error_(true),
      weed_tracker_(std::make_shared<WeedTracker>(pcam_id_, numeric_id_, geo_cam, 10000, width, height, max_pos_mm_y,
                                                  max_number_of_weeds)),
      stopped_(false), receive_ts_(0), deepweed_output_processed_count_(0), deepweed_output_timeout_count_(0),
      deepweed_output_error_count_(0), dw_task_(&AimbotWeedTracker::dw_poll_loop, this),
      we_task_(&AimbotWeedTracker::we_poll_loop, this),
      record_predictions_(
          carbon::config::get_global_config_subscriber()->get_config_node("aimbot", "record_predictions")) {}

AimbotWeedTracker::~AimbotWeedTracker() {
  stopped_ = true;
  this->join();
}
void AimbotWeedTracker::add_rotary_ticks(int64_t timestamp_ms, float mm_travelled) {
  weed_tracker_->add_rotary_ticks(timestamp_ms, mm_travelled);
}

void AimbotWeedTracker::join() {
  this->dw_task_.join();
  this->we_task_.join();
}

std::string AimbotWeedTracker::get_pcam_id() { return this->pcam_id_; }

std::tuple<uint32_t, size_t, size_t, bool, bool> AimbotWeedTracker::aimbot_weed_tracker_state() {
  auto wt_state = weed_tracker_->weed_tracker_state();
  return std::tuple<uint32_t, size_t, size_t, bool, bool>((uint32_t)std::get<0>(wt_state),
                                                          (size_t)std::get<1>(wt_state), (size_t)std::get<2>(wt_state),
                                                          rotary_timeout_, deepweed_error_);
}

WeedTrackerOutput AimbotWeedTracker::get_latest_weeds() {
  std::lock_guard<std::mutex> lk(this->mutex_);
  return this->weed_tracker_->get_latest_weeds();
}

void AimbotWeedTracker::wait_for_ready() {
  std::unique_lock<std::mutex> lk(this->mutex_);
  this->start_cv_.wait(lk, [&] { return this->start_ready_; });
}

std::shared_ptr<lib::common::geometric::GeometricCam> AimbotWeedTracker::get_geo_cam() { return geo_cam_; }

void AimbotWeedTracker::write_dw_predictions_at_time(
    int64_t timestamp_ms, std::shared_ptr<carbon::recorder::ProtobufStreamFileWriter> writer) {
  try {
    ::weed_tracking::Detections detections;
    auto weed_tracker_output = this->weed_tracker_->get_latest_weeds();
    for (auto trajectory : weed_tracker_output.get_trajectories()) {
      auto distorted = trajectory->get_estimated_position_at_timestamp(timestamp_ms);
      auto distorted_x = distorted.get_x();
      auto distorted_y = distorted.get_y();
      auto last_centroid = trajectory->get_last_centroid_with_perspective();

      auto detection_classes = last_centroid.get_classes();
      std::string category = get_detection_clz(detection_classes);
      auto *detection = detections.add_detections();
      detection->set_x((float)distorted_x);
      detection->set_y((float)distorted_y);
      detection->set_size(last_centroid.get_size());
      detection->set_is_weed(last_centroid.get_hit_class() == cv::runtime::proto::HitClass::WEED);
      detection->set_clz(category);
      detection->set_id(trajectory->id());
    }

    detections.set_timestamp_ms(timestamp_ms);
    auto valid = writer->append(detections);
    if (!valid) {
      spdlog::error("Not a valid write");
    }
  } catch (const std::exception &ex) {
    spdlog::error("Couldn't get the latest weeds {}", ex.what());
  }
}

void AimbotWeedTracker::dw_poll_loop() {
  auto bse = lib::common::bot::BotStopHandler::get().create_scoped_event(fmt::format("dw_poll_{}", numeric_id_));
  cv_runtime_client_->wait_for_cv(bse.get());
  {
    std::lock_guard<std::mutex> lk(this->mutex_);
    this->start_ready_ = true;
  }
  this->start_cv_.notify_all();
  std::string prediction_recording_dir_path = "/data/ftp/predictions_recorder/";
  std::filesystem::create_directories(prediction_recording_dir_path);
  std::shared_ptr<carbon::recorder::ProtobufStreamFileWriter> deepweed_prediction_recording;

  int timeout_failure_count = 0;

  while (!bse.is_stopped() && !stopped_) {
    if (deepweed_error_) {
      weed_tracker_->untrack(); // keep tracks up to date even if we fail to get deepweed
    }
    int current_index = (latest_buffer_index_ + 1) % NUMBER_OF_DW_BUFFERS;
    try {
      this->cv_runtime_client_->get_next_deepweed_prediction(this->pcam_id_, 2000, this->latest_dw_timestamp_ms_,
                                                             this->dw_buffers_ + current_index);

      // check if the timestamp on the deepweed output is ahead of current system time, sometimes on boot cv reports a
      // timestamp in the future, in this case we just drop this frame and re-request
      auto current_ts = maka_control_timestamp_ms();
      if (this->dw_buffers_[current_index].timestamp_ms > current_ts + 5000) {
        spdlog::warn(fmt::format("pcam {}: Deepweed Timestamp in the future by more than 5 seconds: {} > {}",
                                 this->pcam_id_, this->dw_buffers_[current_index].timestamp_ms, current_ts));
        continue;
      }
      {
        std::lock_guard<std::mutex> lock(buffer_mutex_);
        latest_buffer_index_ = current_index;
        receive_ts_ = maka_control_timestamp_ms();
      }
      deepweed_output_processed_count_++;
      timeout_failure_count = 0;
    } catch (lib::common::shmem::shmem_error &ex) {
      if (stopped_) {
        return;
      }
      spdlog::warn(fmt::format("Deepweed Shmem Error with pcam id {} and latest_timestamps_ms {}:  {}", this->pcam_id_,
                               this->latest_dw_timestamp_ms_, ex.what()));
      deepweed_error_ = true;
      this->latest_dw_timestamp_ms_ = 0;
      std::this_thread::sleep_for(std::chrono::seconds(1));
      continue;
    } catch (cv::runtime::client::cv_runtime_client_aborted_error &ex) {
      if (stopped_) {
        return;
      }
      // don't log, aborts due to timeouts are expected when using distance buffering and standing still
      // don't sleep, just immediately rerequest a frame
      weed_tracker_->untrack(); // untrack but don't signal deepweed error, a deepweed error goes all the way back to
                                // commander as an alarm

      // if we have timed out 5 times in a row, reset the timestamp to current time so we don't get stuck timing out
      // requesting some timestamp in the future. Should equate to about 10 seconds of timeout, want this to be long so
      // we don't reset just because the machine is moving very slow Reseting to system time so we don't receive old
      // frames
      if (timeout_failure_count++ > 4) {
        auto current_ts = maka_control_timestamp_ms();
        spdlog::debug("pcam {}: Resetting Deepweed Timestamp to {} due to timeout", this->pcam_id_, current_ts);
        this->latest_dw_timestamp_ms_ = current_ts;
        timeout_failure_count = 0;
        deepweed_output_timeout_count_++;
      }
      continue;
    } catch (std::exception &ex) {
      if (stopped_) {
        return;
      }
      spdlog::error(fmt::format("Deepweed Unkown Error with pcam_id {} and latest_timestamps_ms {}:  {}",
                                this->pcam_id_, this->latest_dw_timestamp_ms_, ex.what()));
      deepweed_error_ = true;
      this->latest_dw_timestamp_ms_ = 0;
      deepweed_output_error_count_++;
      std::this_thread::sleep_for(std::chrono::seconds(1));
      continue;
    }
    deepweed_error_ = false;

    {
      this->latest_dw_timestamp_ms_ = this->dw_buffers_[current_index].timestamp_ms;
      if (!await_we(1000, latest_dw_timestamp_ms_)) {
        rotary_timeout_ = true;
        spdlog::warn(fmt::format("Timeout Waiting for Rotary Signal: {}", this->latest_dw_timestamp_ms_));
        continue;
      }
      rotary_timeout_ = false;
      try {
        double weed_tracking_score = 0;
        cv::deepweed::DeepweedOutput deepweed_output;
        std::vector<::recorder::TrackedItem> recorded_tracked_items;
        std::map<size_t, std::pair<float, float>> model_undistorted_coordinates;
        ::recorder::CandidateShift best_candidate_match;
        std::vector<::recorder::CandidateShift> all_candidate_matches;
        std::unordered_set<size_t> centroids_used_in_dedup;
        {
          std::unique_lock<std::mutex> lk(this->mutex_);
          auto [number_deduplicated_plants, number_unmatched_plant_centroids, number_undeduplicated_plants,
                model_to_track, plant_matcher_valid] = weed_tracker_->add_deepweed(this->dw_buffers_[current_index], recorded_tracked_items, model_undistorted_coordinates, best_candidate_match, all_candidate_matches, centroids_used_in_dedup);

          if (record_predictions_->get_value<bool>()) {
            if (!deepweed_prediction_recording) {
              std::string file_path = fmt::format("{}/deepweed_predictions_{}_{}.carbon", prediction_recording_dir_path,
                                                  pcam_id_, std::to_string(maka_control_timestamp_ms()));
              deepweed_prediction_recording = std::make_shared<carbon::recorder::ProtobufStreamFileWriter>(
                  "DeepweedPredictionsRecording", file_path);
            }
            write_dw_predictions_at_time(this->dw_buffers_[current_index].timestamp_ms, deepweed_prediction_recording);
          } else {
            if (deepweed_prediction_recording) {
              deepweed_prediction_recording.reset();
            }
          }

          auto total_plants =
              number_deduplicated_plants + number_unmatched_plant_centroids + number_undeduplicated_plants;

          if (!score_images_ || total_plants == 0) {
            continue;
          }

          weed_tracking_score = (double)number_undeduplicated_plants / (double)total_plants;
          deepweed_output = this->dw_buffers_[current_index];
          lk.unlock();
          {
            std::unique_lock<std::mutex> blk(this->buffer_mutex_);
            latest_post_dedup_buffer_index_ = (latest_post_dedup_buffer_index_ + 1) % NUMBER_OF_DW_BUFFERS;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].timestamp_ms = deepweed_output.timestamp_ms;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].dw_output = deepweed_output;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].dw_to_track = model_to_track;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].tracked_items = recorded_tracked_items;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].model_undistorted_coordinates = model_undistorted_coordinates;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].best_candidate_match = best_candidate_match;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].all_candidate_matches = all_candidate_matches;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].plant_matcher_valid = plant_matcher_valid;
            post_dedup_buffers_[latest_post_dedup_buffer_index_].centroids_used_in_dedup = centroids_used_in_dedup;
          }
          buffer_cv_.notify_all();
        }
        cv_runtime_client_->set_image_score(pcam_id_, this->latest_dw_timestamp_ms_, weed_tracking_score,
                                            deepweed_output);
      } catch (almanac::AlmanacException &ex) {
        throw ex;
      } catch (std::exception &ex) {
        buffer_cv_.notify_all();
        spdlog::error(fmt::format("Deepweed Unknown Error While Adding: {}", ex.what()));
        std::this_thread::sleep_for(std::chrono::seconds(1));
        continue;
      }
    }
  }
}

bool AimbotWeedTracker::await_we(uint32_t timeout_ms, uint64_t compare_time_ms) {
  std::unique_lock<std::mutex> lk(this->we_mut_);
  return (this->we_cv_.wait_for(lk, std::chrono::milliseconds(timeout_ms),
                                [&] { return compare_time_ms < last_we_timestamp_ms_; }));
}

void AimbotWeedTracker::we_poll_loop() {
  static config::ConfigAtomicAccessor<uint32_t> wheel_encoder_update_threshold(
      config::get_global_config_subscriber()->get_config_node("aimbot", "weed_tracking/wheel_encoder_update_threshold"),
      0);
  this->wait_for_ready();
  auto bse = lib::common::bot::BotStopHandler::get().create_scoped_event(fmt::format("aimbot_we_poll_{}", numeric_id_));
  auto *wheel_encoder = wheel_encoder::WheelEncoder::get();
  wheel_encoder::EncoderData prev_snapshot;
  while (!bse.is_stopped() && !stopped_) {
    auto next_snapshot = wheel_encoder->get_next(prev_snapshot.msec());
    float dist_travelled_mm = wheel_encoder->avg_dist(prev_snapshot, next_snapshot);
    if (fabs(dist_travelled_mm) < static_cast<float>(wheel_encoder_update_threshold.get_value())) {
      continue;
    }
    add_rotary_ticks((int64_t)(next_snapshot.msec()), dist_travelled_mm);
    prev_snapshot = next_snapshot;
    {
      std::lock_guard<std::mutex> lk(we_mut_);
      last_we_timestamp_ms_ = prev_snapshot.msec();
    }
    we_cv_.notify_all();
  }
}

std::shared_ptr<WeedTracker> AimbotWeedTracker::get_weed_tracker() { return weed_tracker_; }

trajectory::TrackedItemPosition AimbotWeedTracker::get_estimated_position_at_timestamp_from_trajectory(
    std::shared_ptr<trajectory::Trajectory> trajectory, int64_t timestamp_ms) {
  std::lock_guard<std::mutex> lk(this->mutex_);
  return trajectory->get_estimated_position_at_timestamp(timestamp_ms);
}

trajectory::TrackedItemCentroid AimbotWeedTracker::get_last_centroid_with_perspective_before_timestamp_from_trajectory(
    std::shared_ptr<trajectory::Trajectory> trajectory, int64_t timestamp_ms) {
  std::lock_guard<std::mutex> lk(this->mutex_);
  return trajectory->get_last_centroid_with_perspective_before_timestamp(timestamp_ms);
}

std::pair<float, float> AimbotWeedTracker::get_flow(int64_t from_timestamp_ms, int64_t to_timestamp_ms) {
  std::lock_guard<std::mutex> lk(this->mutex_);
  return weed_tracker_->get_robot_distance_accumulator()->get_flow(from_timestamp_ms, to_timestamp_ms);
}

trajectory::TrackedItemCentroid
AimbotWeedTracker::get_last_item_from_trajectory(std::shared_ptr<trajectory::Trajectory> trajectory) {
  std::lock_guard<std::mutex> lk(this->mutex_);
  return trajectory->get_last_item();
}

float AimbotWeedTracker::get_pix_per_ticks() { return weed_tracker_->get_pix_per_ticks(); }

std::vector<double> AimbotWeedTracker::get_averages_for_all_columns(std::optional<float> size_mm) {
  return weed_tracker_->get_averages_for_all_columns(size_mm);
}
std::shared_ptr<trajectory::Trajectory> AimbotWeedTracker::get_trajectory_for_id(uint32_t id) {
  return weed_tracker_->get_tracker()->get_nullable_trajectory(id);
}

void AimbotWeedTracker::get_bands(int max_x, ::weed_tracking::Bands *result) {
  auto bands = Banding::instance()->get_bands();
  double y;
  double z;
  std::tie(std::ignore, y, z) = geo_cam_->get_abs_position_from_px(std::make_tuple(0, 0));
  for (auto &b : bands) {
    auto start_x_px =
        std::get<0>(geo_cam_->get_distorted_px_from_abs_position(std::make_tuple(b.get_start_mm(), y, z)));
    auto end_x_px = std::get<0>(geo_cam_->get_distorted_px_from_abs_position(std::make_tuple(b.get_end_mm(), y, z)));
    if ((start_x_px < 0 && end_x_px <= 0) || (start_x_px >= max_x && end_x_px >= max_x)) {
      continue;
    }

    if (start_x_px < 0) {
      start_x_px = 0;
    }
    if (end_x_px > max_x) {
      end_x_px = max_x;
    }

    auto *band = result->add_band();
    band->set_start_x_px(start_x_px);
    band->set_end_x_px(end_x_px);
  }
  result->set_banding_enabled(Banding::instance()->enabled());
  result->set_row_has_bands_defined(bands.size() > 0);
}

bool AimbotWeedTracker::get_next_retrieved_deepweed(uint32_t timeout_ms, int64_t previous_timestamp_ms,
      PostDedupFrame *output) {
  std::unique_lock<std::mutex> lk(this->buffer_mutex_);
  if (this->buffer_cv_.wait_for(lk, std::chrono::milliseconds(timeout_ms), [&] {
        return previous_timestamp_ms < this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].timestamp_ms;
      })) {
    // Note we ignore the mask portion of Deepweed Output on purpose for effciency since it is not needed
    // TODO Can I just copy the item directly instead of adding all this shit?
    output->receive_ts_ms = receive_ts_; // Is there a problem with this?
    auto dw_output = this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].dw_output;
    output->dw_output.detections = dw_output.detections;
    output->dw_output.weed_detection_classes = dw_output.weed_detection_classes;
    output->dw_output.mask_channel_classes = dw_output.mask_channel_classes;
    output->dw_output.timestamp_ms = dw_output.timestamp_ms;
    output->dw_output.embedding_categories = dw_output.embedding_categories;
    output->dw_to_track = this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].dw_to_track; // Could this be called after DW buffer is set but before dw_to_track_? Yeah
    output->tracked_items = this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].tracked_items;
    output->model_undistorted_coordinates = this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].model_undistorted_coordinates;
    output->best_candidate_match = this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].best_candidate_match;
    output->all_candidate_matches = this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].all_candidate_matches;
    output->plant_matcher_valid = this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].plant_matcher_valid;
    output->centroids_used_in_dedup = this->post_dedup_buffers_[this->latest_post_dedup_buffer_index_].centroids_used_in_dedup;
    // Proposal: Have another buffer that's for this information
    // Then we can rely on that and basically use latest_buffer_index_
    return true;
  }
  return false;
}

AimbotTrackerOwner::AimbotTrackerOwner() : set_(false) {}
AimbotTrackerOwner &AimbotTrackerOwner::get() {
  static AimbotTrackerOwner ato;
  return ato;
}
void AimbotTrackerOwner::set(const std::vector<std::shared_ptr<AimbotWeedTracker>> &trackers) {
  const std::unique_lock<std::mutex> lk(mut_);
  if (set_) {
    spdlog::warn("AimbotWeedTrackers have already been set.");
    return;
  }
  for (auto tracker : trackers) {
    trackers_.emplace(tracker->numeric_id(), tracker);
  }
  set_ = true;
}
const std::unordered_map<uint32_t, std::shared_ptr<AimbotWeedTracker>> &AimbotTrackerOwner::get_trackers() const {
  // Trick to force this check to run only first time
  static bool run_once_hack = check_exists();
  (void)run_once_hack;
  return trackers_;
}
const std::shared_ptr<AimbotWeedTracker> AimbotTrackerOwner::get_tracker(uint32_t id) const {
  // Trick to force this check to run only first time
  static bool run_once_hack = check_exists();
  (void)run_once_hack;
  auto it = trackers_.find(id);
  if (it == trackers_.end()) {
    return nullptr;
  }
  return it->second;
}
bool AimbotTrackerOwner::check_exists() const {
  const std::unique_lock<std::mutex> lk(mut_);
  if (!set_) {
    throw std::runtime_error("trackers have not been set yet");
  }
  return true;
}

} // namespace weed_tracking
} // namespace carbon
