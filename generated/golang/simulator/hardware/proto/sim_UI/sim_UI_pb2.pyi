"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GetVelocityRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetVelocityRequest = GetVelocityRequest

class GetVelocityResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    velocity: builtin___float = ...

    def __init__(self,
        *,
        velocity : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"velocity",b"velocity"]) -> None: ...
type___GetVelocityResponse = GetVelocityResponse

class SetVelocityRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    velocity: builtin___float = ...

    def __init__(self,
        *,
        velocity : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"velocity",b"velocity"]) -> None: ...
type___SetVelocityRequest = SetVelocityRequest

class SetVelocityResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetVelocityResponse = SetVelocityResponse

class GetPresetsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetPresetsRequest = GetPresetsRequest

class PresetSetting(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    path: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    value: typing___Text = ...

    def __init__(self,
        *,
        path : typing___Optional[typing___Iterable[typing___Text]] = None,
        value : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"path",b"path",u"value",b"value"]) -> None: ...
type___PresetSetting = PresetSetting

class GetPresetsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def settings(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PresetSetting]: ...

    def __init__(self,
        *,
        settings : typing___Optional[typing___Iterable[type___PresetSetting]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"settings",b"settings"]) -> None: ...
type___GetPresetsResponse = GetPresetsResponse

class SetPresetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def setting(self) -> type___PresetSetting: ...

    def __init__(self,
        *,
        setting : typing___Optional[type___PresetSetting] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"setting",b"setting"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"setting",b"setting"]) -> None: ...
type___SetPresetRequest = SetPresetRequest

class SetPresetResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetPresetResponse = SetPresetResponse

class DiagnosticSettings(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current_diagnostic: typing___Text = ...
    diagnostics: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    row: builtin___int = ...
    ticket: builtin___int = ...

    def __init__(self,
        *,
        current_diagnostic : typing___Optional[typing___Text] = None,
        diagnostics : typing___Optional[typing___Iterable[typing___Text]] = None,
        row : typing___Optional[builtin___int] = None,
        ticket : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_diagnostic",b"current_diagnostic",u"diagnostics",b"diagnostics",u"row",b"row",u"ticket",b"ticket"]) -> None: ...
type___DiagnosticSettings = DiagnosticSettings

class GetDiagnosticSettingsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ticket: builtin___int = ...

    def __init__(self,
        *,
        ticket : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ticket",b"ticket"]) -> None: ...
type___GetDiagnosticSettingsRequest = GetDiagnosticSettingsRequest

class GetDiagnosticSettingsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def settings(self) -> type___DiagnosticSettings: ...

    def __init__(self,
        *,
        settings : typing___Optional[type___DiagnosticSettings] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"settings",b"settings"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"settings",b"settings"]) -> None: ...
type___GetDiagnosticSettingsResponse = GetDiagnosticSettingsResponse

class SetDiagnosticSettingsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current_diagnostic: typing___Text = ...
    row: builtin___int = ...

    def __init__(self,
        *,
        current_diagnostic : typing___Optional[typing___Text] = None,
        row : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_diagnostic",b"current_diagnostic",u"row",b"row"]) -> None: ...
type___SetDiagnosticSettingsRequest = SetDiagnosticSettingsRequest

class SetDiagnosticSettingsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetDiagnosticSettingsResponse = SetDiagnosticSettingsResponse
