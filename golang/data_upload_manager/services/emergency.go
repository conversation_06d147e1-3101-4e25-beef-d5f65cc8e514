package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"strings"
	"time"

	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	pb "github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/generated/proto/data_upload_manager"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type CameraMap map[int]map[uint32][]string

type EmergencyService struct {
	data_upload_manager.UnimplementedEmergencyServiceServer
	stopCtx                  context.Context
	rowClients               map[int]*rows.RowClients
	robotClient              RobotClient
	robotName                string
	configClient             *config.ConfigClient
	emergencySessionName     *config.ConfigTree
	emergencyCaptureTarget   *config.ConfigTree
	emergencyUploadTarget    *config.ConfigTree
	captureSession           *EmergencyCaptureSession
	activeUploadSession      *EmergencyUploadSession
	backgroundUploadSessions map[string]*EmergencyUploadSession
	admin                    *config.ConfigTree
	enableEmergencyPriority  *config.ConfigTree
	hardwareManagerClient    *hardware_manager.HardwareManagerClient
	uploadProportions        *config.ConfigTree
	useOfflineFlow           *config.ConfigTree
	offlineAgentMaxLoad      *config.ConfigTree
	maxImagesPer24Hours      *config.ConfigTree
	predictAperture          *config.ConfigTree
	softwareVersion          string
	redisClient              *redis.Client
	weedingDiagnosticsNode   *config.ConfigTree
	plantCaptchaNode         *config.ConfigTree
}

func NewEmergencyService(
	grpcServer *grpc.Server,
	stopCtx context.Context,
	rowClients map[int]*rows.RowClients,
	robotClient RobotClient,
	robotName string,
	configClient *config.ConfigClient,
	emergencySessionName *config.ConfigTree,
	emergencyCaptureTarget *config.ConfigTree,
	emergencyUploadTarget *config.ConfigTree,
	admin *config.ConfigTree,
	enableEmergencyPriority *config.ConfigTree,
	hardwareManagerClient *hardware_manager.HardwareManagerClient,
	uploadProportions *config.ConfigTree,
	useOfflineFlow *config.ConfigTree,
	offlineAgentMaxLoad *config.ConfigTree,
	maxImagesPer24Hours *config.ConfigTree,
	predictAperture *config.ConfigTree,
	softwareVersion string,
	redisClient *redis.Client,
	weedingDiagnosticsNode *config.ConfigTree,
	plantCaptchaNode *config.ConfigTree) *EmergencyService {
	service := &EmergencyService{
		stopCtx:                  stopCtx,
		rowClients:               rowClients,
		robotClient:              robotClient,
		robotName:                robotName,
		configClient:             configClient,
		emergencySessionName:     emergencySessionName,
		emergencyCaptureTarget:   emergencyCaptureTarget,
		emergencyUploadTarget:    emergencyUploadTarget,
		admin:                    admin,
		enableEmergencyPriority:  enableEmergencyPriority,
		hardwareManagerClient:    hardwareManagerClient,
		uploadProportions:        uploadProportions,
		useOfflineFlow:           useOfflineFlow,
		offlineAgentMaxLoad:      offlineAgentMaxLoad,
		backgroundUploadSessions: make(map[string]*EmergencyUploadSession),
		maxImagesPer24Hours:      maxImagesPer24Hours,
		predictAperture:          predictAperture,
		softwareVersion:          softwareVersion,
		redisClient:              redisClient,
		weedingDiagnosticsNode:   weedingDiagnosticsNode,
		plantCaptchaNode:         plantCaptchaNode,
	}
	data_upload_manager.RegisterEmergencyServiceServer(grpcServer, service)
	return service
}

// Capture

func (s *EmergencyService) StartDataCaptureSession(ctx context.Context, req *data_upload_manager.StartDataCaptureSessionRequest) (*data_upload_manager.StartDataCaptureSessionResponse, error) {
	if req.GetCrop() == "" && req.GetCropId() == "" {
		return nil, status.Errorf(codes.FailedPrecondition, "Couldn't start the emergency capture service: No crop specified")
	}

	key := fmt.Sprintf("%v/data_upload_manager/emergency_capture_rate", config.GetComputerConfigPrefix())
	err := s.configClient.SetDoubleValue(key, req.GetCaptureRate())

	if err != nil {
		return nil, err
	}

	sessionName := strings.TrimSpace(req.GetSessionName())
	key = fmt.Sprintf("%v/data_upload_manager/emergency_session_name", config.GetComputerConfigPrefix())
	err = s.configClient.SetStringValue(key, sessionName)

	if err != nil {
		return nil, err
	}

	s.captureSession = NewEmergencyCaptureSession(s.stopCtx, s.rowClients, s.robotName, req.GetCaptureRate(), sessionName, s.emergencyCaptureTarget, req.GetCrop(), req.GetCropId(), s.admin, s.enableEmergencyPriority, s.hardwareManagerClient, s.uploadProportions, req.GetEnableSinglePredict(), LatestCaptureParameters{rowId: req.GetRowInd(), camId: req.GetCamId()}, s.predictAperture, s.softwareVersion, req.GetSnapCapture(), s.redisClient)
	logrus.Infof("EmergencyDataCapture: Starting data capture session with params: capture_rate %v session_name %v crop %v enable_single_predict_capture %v row_ind %v cam_id %v", req.GetCaptureRate(), sessionName, req.GetCrop(), req.GetEnableSinglePredict(), req.GetRowInd(), req.GetCamId())

	s.captureSession.Start()
	return &data_upload_manager.StartDataCaptureSessionResponse{}, nil
}

func (s *EmergencyService) StopDataCaptureSession(context.Context, *data_upload_manager.StopDataCaptureSessionRequest) (*data_upload_manager.StopDataCaptureSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Stopping data capture session")
	if s.captureSession != nil {
		s.captureSession.Stop()
		s.captureSession = nil
	}
	return &data_upload_manager.StopDataCaptureSessionResponse{}, nil
}

func (s *EmergencyService) ResumeDataCaptureSession(ctx context.Context, req *data_upload_manager.ResumeDataCaptureSessionRequest) (*data_upload_manager.ResumeDataCaptureSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Resuming data capture session")
	if s.captureSession == nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Capture session doesn't exist")
	}
	s.captureSession.Resume()
	return &data_upload_manager.ResumeDataCaptureSessionResponse{}, nil
}

func (s *EmergencyService) PauseDataCaptureSession(ctx context.Context, req *data_upload_manager.PauseDataCaptureSessionRequest) (*data_upload_manager.PauseDataCaptureSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Pausing data capture session")
	if s.captureSession == nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Capture session doesn't exist")
	}
	s.captureSession.Pause()
	return &data_upload_manager.PauseDataCaptureSessionResponse{}, nil
}

func (s *EmergencyService) CompleteDataCaptureSession(ctx context.Context, req *data_upload_manager.CompleteDataCaptureSessionRequest) (*data_upload_manager.CompleteDataCaptureSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Complete data capture session")
	if s.captureSession == nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Capture session doesn't exist")
	}
	s.captureSession.Complete()
	return &data_upload_manager.CompleteDataCaptureSessionResponse{}, nil
}

// Upload

func (s *EmergencyService) StartDataUploadSession(ctx context.Context, req *data_upload_manager.StartDataUploadSessionRequest) (*data_upload_manager.StartDataUploadSessionResponse, error) {
	var method state.Method
	if req.GetMethod() == "usb" {
		method = state.USB
	} else if req.GetMethod() == "wireless" {
		method = state.Wireless
	} else {
		return &data_upload_manager.StartDataUploadSessionResponse{}, status.Errorf(codes.FailedPrecondition, "Cannot create an upload session with %v method", req.GetMethod())
	}

	logrus.Infof("EmergencyDataCapture: Starting %v upload session", req.GetMethod())
	s.activeUploadSession = NewEmergencyUploadSession(s.stopCtx, s.robotClient, s.emergencySessionName.GetStringValue(), s.emergencyUploadTarget, method, s.robotName, s.useOfflineFlow, s.hardwareManagerClient, s.offlineAgentMaxLoad)
	s.activeUploadSession.Start()
	return &data_upload_manager.StartDataUploadSessionResponse{}, nil
}

func (s *EmergencyService) StopDataUploadSession(context.Context, *data_upload_manager.StopDataUploadSessionRequest) (*data_upload_manager.StopDataUploadSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Stopping data upload session")
	if s.activeUploadSession != nil {
		s.activeUploadSession.Stop()
		s.activeUploadSession = nil
	}
	return &data_upload_manager.StopDataUploadSessionResponse{}, nil
}

func (s *EmergencyService) ResumeDataUploadSession(ctx context.Context, req *data_upload_manager.ResumeDataUploadSessionRequest) (*data_upload_manager.ResumeDataUploadSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Resuming data upload session")
	if s.activeUploadSession == nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Capture session doesn't exist")
	}
	s.activeUploadSession.Resume()
	return &data_upload_manager.ResumeDataUploadSessionResponse{}, nil
}

func (s *EmergencyService) PauseDataUploadSession(ctx context.Context, req *data_upload_manager.PauseDataUploadSessionRequest) (*data_upload_manager.PauseDataUploadSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Pausing data upload session")
	if s.activeUploadSession == nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Capture session doesn't exist")
	}
	s.activeUploadSession.Pause()
	return &data_upload_manager.PauseDataUploadSessionResponse{}, nil
}

// Background Upload

func (s *EmergencyService) StartBackgroundDataUploadSession(ctx context.Context, req *data_upload_manager.StartBackgroundDataUploadSessionRequest) (*data_upload_manager.StartDataUploadSessionResponse, error) {
	var method state.Method
	if req.GetMethod() == "usb" {
		method = state.USB
	} else if req.GetMethod() == "wireless" {
		method = state.Wireless
	} else {
		return &data_upload_manager.StartDataUploadSessionResponse{}, status.Errorf(codes.FailedPrecondition, "Cannot create an upload session with %v method", req.GetMethod())
	}

	if req.GetEmergencySessionName() == s.emergencySessionName.GetStringValue() && s.captureSession != nil && !s.captureSession.dataCaptureState.HasCompleted() {
		return &data_upload_manager.StartDataUploadSessionResponse{}, status.Errorf(codes.FailedPrecondition, "Session %v is still capturing, please wait to upload until it's finished.", req.GetEmergencySessionName())
	}

	logrus.Infof("EmergencyDataCapture: Starting %v background upload session", req.GetMethod())
	backgroundUploadSession := NewEmergencyUploadSession(s.stopCtx, s.robotClient, req.GetEmergencySessionName(), s.emergencyUploadTarget, method, s.robotName, s.useOfflineFlow, s.hardwareManagerClient, s.offlineAgentMaxLoad)
	s.backgroundUploadSessions[req.GetEmergencySessionName()] = backgroundUploadSession
	s.backgroundUploadSessions[req.GetEmergencySessionName()].Start()
	return &data_upload_manager.StartDataUploadSessionResponse{}, nil
}

func (s *EmergencyService) StopBackgroundDataUploadSession(ctx context.Context, req *data_upload_manager.StopBackgroundDataUploadSessionRequest) (*data_upload_manager.StopDataUploadSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Stopping background data upload session")
	if s.backgroundUploadSessions[req.GetEmergencySessionName()] != nil {
		s.backgroundUploadSessions[req.GetEmergencySessionName()].Stop()
		s.backgroundUploadSessions[req.GetEmergencySessionName()] = nil
	}
	return &data_upload_manager.StopDataUploadSessionResponse{}, nil
}

func (s *EmergencyService) ResumeBackgroundDataUploadSession(ctx context.Context, req *data_upload_manager.ResumeBackgroundDataUploadSessionRequest) (*data_upload_manager.ResumeDataUploadSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Resuming background data upload session")
	if s.backgroundUploadSessions[req.GetEmergencySessionName()] == nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Capture session doesn't exist")
	}
	s.backgroundUploadSessions[req.GetEmergencySessionName()].Resume()
	return &data_upload_manager.ResumeDataUploadSessionResponse{}, nil
}

func (s *EmergencyService) PauseBackgroundDataUploadSession(ctx context.Context, req *data_upload_manager.PauseBackgroundDataUploadSessionRequest) (*data_upload_manager.PauseDataUploadSessionResponse, error) {
	logrus.Infof("EmergencyDataCapture: Pausing background data upload session")
	if s.backgroundUploadSessions[req.GetEmergencySessionName()] == nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Capture session doesn't exist")
	}
	s.backgroundUploadSessions[req.GetEmergencySessionName()].Pause()
	return &data_upload_manager.PauseDataUploadSessionResponse{}, nil
}

// Progress

func (s *EmergencyService) GetCaptureProgress(context.Context, *data_upload_manager.GetCaptureProgressRequest) (*data_upload_manager.GetCaptureProgressResponse, error) {
	isCapturing := false
	imagesCaptured := uint32(0)
	exists := s.captureSession != nil
	hasCompleted := false

	if exists {
		isCapturing = s.captureSession.dataCaptureState.IsCapturing()
		imagesCaptured = s.captureSession.dataCaptureState.ImagesCaptured()
		hasCompleted = s.captureSession.dataCaptureState.HasCompleted()
	}

	resp := data_upload_manager.GetCaptureProgressResponse{ImagesCaptured: imagesCaptured, IsCapturing: isCapturing, Exists: exists, HasCompleted: hasCompleted}
	return &resp, nil
}

func (s *EmergencyService) GetUploadProgress(context.Context, *data_upload_manager.GetUploadProgressRequest) (*data_upload_manager.GetUploadProgressResponse, error) {
	isUploading := false
	imagesUploaded := uint32(0)
	methodString := ""
	exists := s.activeUploadSession != nil
	hasCompleted := false

	if exists {
		isUploading = s.activeUploadSession.dataUploadState.IsUploading()
		imagesUploaded = s.activeUploadSession.dataUploadState.ImagesUploaded()
		method := s.activeUploadSession.dataUploadState.Method()
		switch method {
		case state.USB:
			methodString = "usb"
		case state.Wireless:
			methodString = "wireless"
		}
		hasCompleted = s.activeUploadSession.dataUploadState.HasCompleted()
	}

	resp := data_upload_manager.GetUploadProgressResponse{ImagesUploaded: imagesUploaded, IsUploading: isUploading, Method: methodString, Exists: exists, HasCompleted: hasCompleted}
	return &resp, nil
}

// Clean

func (s *EmergencyService) CleanOldDirectories(duration time.Duration) error {
	logrus.Infof("EmergencyDataCapture: Removing directories older than %s from %v", duration, getEmergencyDir())
	emergencyDir := getEmergencyDir()
	files, err := ioutil.ReadDir(emergencyDir)
	if err != nil {
		return err
	}

	for _, file := range files {
		if time.Now().Sub(file.ModTime()) > duration {
			filepath := fmt.Sprintf("%v/%v", emergencyDir, file.Name())
			logrus.Infof("EmergencyDataCapture: Removing directory %v", filepath)
			os.RemoveAll(filepath)
		}
	}
	return nil
}

func (s *EmergencyService) CleanOldSnapshots(duration time.Duration) error {
	snapDir := getSnapDirectory()
	files, err := ioutil.ReadDir(snapDir)
	if err != nil {
		return err
	}

	for _, file := range files {
		if time.Now().Sub(file.ModTime()) > duration {
			filepath := fmt.Sprintf("%v/%v", snapDir, file.Name())
			os.Remove(filepath)
		}
	}
	return nil
}

// Snaps

func (s *EmergencyService) GetCameras() (CameraMap, error) {
	var clientCameraIds CameraMap = make(CameraMap)
	if len(s.rowClients) == 0 {
		return clientCameraIds, errors.New("No cv runtime clients")
	}
	atLeastOneValidRow := false
	for rowID, rowClient := range s.rowClients {
		pcs := make(map[uint32][]string)
		for pcID, cvClient := range rowClient.CVRuntimeClients {
			var camIds []string
			client, err := cvClient.GetRawClient()
			if err != nil {
				logrus.Errorf("Couldn't get raw client: %v", err)
				pcs[pcID] = camIds
				continue
			}

			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()
			cams, err := client.GetCameraInfo(ctx, &pb.GetCameraInfoRequest{})
			if err != nil {
				logrus.Errorf("Couldn't get camera info %v", err)
				pcs[pcID] = camIds
				continue
			}
			camInfo := cams.GetCameraInfo()
			for _, cInfo := range camInfo {
				id := cInfo.GetCamId()
				if id != "" && strings.HasPrefix(id, "predict") && cInfo.GetConnected() {
					camIds = append(camIds, id)
					atLeastOneValidRow = true
				}
			}
			pcs[pcID] = camIds
		}

		clientCameraIds[rowID] = pcs
	}

	if !atLeastOneValidRow {
		return clientCameraIds, errors.New("No valid rows, is CVRT running on any row computer?")
	}

	return clientCameraIds, nil
}

func getSnapDirectory() string {
	return fmt.Sprintf("%v/data_upload_manager/predict-images/snapshots", getDataDir())
}

func (s *EmergencyService) snapImageAndSave(cvClient *cv_runtime_client.CVRuntimeClient, camId string, rowInd int, errs chan error, crop, cropID string, timestampMs int64, dir string, sessionName string) {
	client, err := cvClient.GetRawClient()
	if err != nil {
		logrus.Errorf("Couldn't get raw client: %v", err)
		errs <- err
		return
	}
	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)
	imageAndMetadata, err := client.GetImageNearTimestamp(ctx, &pb.GetImageNearTimestampRequest{CamId: camId, TimestampMs: timestampMs})
	if err != nil {
		logrus.Errorf("Couldn't get image near timestamp image: %v", err)
		errs <- err
		return
	}
	jobId, jobName := getCurrentJobIdAndName(s.redisClient)
	priority := UploadPriorityEmergency
	if !s.enableEmergencyPriority.GetBoolValue() {
		priority = UploadPriorityNormal
	}

	_, errWriting := writeImageAndMetadata(imageAndMetadata, rowInd, "emergency_snap", s.robotName, dir, crop, cropID, int(s.admin.GetIntValue()), priority, "predict", sessionName, s.predictAperture.GetStringValue(), s.softwareVersion, jobId, jobName, s.redisClient, true)

	if errWriting != nil {
		logrus.Errorf("Couldn't write image in image capture mode: %v", err)
		errs <- err
		return
	}

	errs <- nil
}

func (s *EmergencyService) snapImagesAndSave(client *cv_runtime_client.CVRuntimeClient, predictCamIds []string, rowInd int, crop, cropID string, timestampMs int64, dir string, sessionName string) error {
	var error_channels []chan error
	for _, camId := range predictCamIds {
		errs := make(chan error, 1)
		error_channels = append(error_channels, errs)
		go s.snapImageAndSave(client, camId, rowInd, errs, crop, cropID, timestampMs, dir, sessionName)
	}

	for _, ec := range error_channels {
		err := <-ec
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *EmergencyService) SnapImages(ctx context.Context, req *data_upload_manager.SnapImagesRequest) (*data_upload_manager.SnapImagesResponse, error) {
	if req.GetCrop() == "" && req.GetCropId() == "" {
		return nil, status.Errorf(codes.FailedPrecondition, "Couldn't take images: No crop specified")
	}

	sessionName := req.GetSessionName()

	snapDir := getSessionDir(fmt.Sprintf("%v", sessionName))

	if err := os.MkdirAll(snapDir, 0777); err != nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Couldn't make directory for snaps")
	}

	var clientErrors []error

	numberAttempts := 0

	if req.GetCamId() != "" && req.GetTimestampMs() != 0 {
		rowInd := int(req.GetRowInd())
		client, ok := s.rowClients[rowInd]
		if !ok {
			return nil, status.Errorf(codes.FailedPrecondition, "Couldn't take images: No row client for row %v", rowInd)
		}

		cvClient, err := GetCVClientForCamId(client, req.GetCamId())
		if err != nil {
			return nil, err
		}

		camIds := []string{req.GetCamId()}
		numberAttempts += 1
		if err := s.snapImagesAndSave(cvClient, camIds, rowInd, req.GetCrop(), req.GetCropId(), req.GetTimestampMs(), snapDir, sessionName); err != nil {
			return nil, err
		}

	} else {
		cams, err := s.GetCameras()

		if err != nil {
			return nil, err
		}
		now := time.Now().UnixMilli()
		for rowIdx, rowClient := range s.rowClients {
			for pcIdx, cvClient := range rowClient.CVRuntimeClients {
				camList, ok := cams[rowIdx][pcIdx]
				if !ok {
					logrus.Infof("Camera doesn't exist in cams list row %v, pc %v", rowIdx, pcIdx)
					continue
				}
				numberAttempts += 1
				err := s.snapImagesAndSave(cvClient, camList, rowIdx+1, req.GetCrop(), req.GetCropId(), now, snapDir, sessionName)

				if err != nil {
					clientErrors = append(clientErrors, err)
				}
			}
		}
	}

	if len(clientErrors) > 0 {
		logrus.Errorf("Failed to snap %v times (out of %v attempts)", len(clientErrors), numberAttempts)
		for _, err := range clientErrors {
			logrus.Errorf("		Err: %v", err)
		}
	}

	if len(clientErrors) == numberAttempts {
		return nil, errors.New("Failed to snap any images for cameras")
	}

	resp := data_upload_manager.SnapImagesResponse{}
	return &resp, nil
}

// Sessions

func (s *EmergencyService) GetSessions(context.Context, *data_upload_manager.GetSessionsRequest) (*data_upload_manager.GetSessionsResponse, error) {
	directory, err := ioutil.ReadDir(getEmergencyDir())
	if err != nil {
		logrus.Errorf("Failed to read dir")
		return nil, err
	}

	resp := data_upload_manager.GetSessionsResponse{}

	for _, dir := range directory {
		if dir.IsDir() {
			files, e := ioutil.ReadDir(getSessionDir(dir.Name()))

			if e != nil {
				logrus.Errorf("Couldn't read directory %v", e)
				continue
			}

			var length uint32
			length = 0

			for _, file := range files {
				if strings.HasSuffix(file.Name(), ".png") {
					length += 1
				}
			}

			if length > 0 {
				sesh := &data_upload_manager.Session{
					Name:         dir.Name(),
					NumberImages: length,
					IsUploading:  false,
					HasCompleted: false,
					IsCapturing:  false,
				}

				if s.backgroundUploadSessions[dir.Name()] != nil {
					sesh.IsUploading = s.backgroundUploadSessions[dir.Name()].dataUploadState.IsUploading()
					sesh.HasCompleted = s.backgroundUploadSessions[dir.Name()].dataUploadState.HasCompleted()
				} else if s.captureSession != nil && dir.Name() == s.captureSession.emergencySessionName && s.captureSession.dataCaptureState.IsCapturing() { // Checking IsCapturing because a capture session sticks around until upload is complete, but we don't want the session status to report isCapturing if the session is uploading
					sesh.IsCapturing = true
					sesh.HasCompleted = s.captureSession.dataCaptureState.HasCompleted()
				} else if s.activeUploadSession != nil && dir.Name() == s.activeUploadSession.emergencySessionName {
					sesh.IsUploading = s.activeUploadSession.dataUploadState.IsUploading()
					sesh.HasCompleted = s.activeUploadSession.dataUploadState.HasCompleted()
				}

				resp.Sessions = append(resp.Sessions, sesh)
			}
		}
	}
	return &resp, nil
}

// Regular Status

func (s *EmergencyService) GetRegularCaptureStatus(context.Context, *data_upload_manager.GetRegularCaptureStatusRequest) (*data_upload_manager.GetRegularCaptureStatusResponse, error) {
	data, err := ioutil.ReadFile(getUploadedTimesLog())
	if err != nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Cannot read upload log: %v", err)
	}

	var timestamps []TimestampedFile
	if err := json.Unmarshal(data, &timestamps); err != nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Log can't be unmarshaled: %v", err)
	}

	budget := uint32(s.maxImagesPer24Hours.GetIntValue())

	if len(timestamps) > 0 {
		resp := data_upload_manager.GetRegularCaptureStatusResponse{Uploaded: uint32(len(timestamps)), Budget: budget, LastUploadTimestamp: timestamps[len(timestamps)-1].TimestampMs}
		return &resp, nil
	}

	resp := data_upload_manager.GetRegularCaptureStatusResponse{Uploaded: 0, Budget: budget, LastUploadTimestamp: 0}
	return &resp, nil
}

func (s *EmergencyService) StartWeedingDiagnosticsUpload(ctx context.Context, req *data_upload_manager.StartWeedingDiagnosticsUploadRequest) (*data_upload_manager.StartWeedingDiagnosticsUploadResponse, error) {
	logrus.Infof("EmergencyDataCapture: Received request to upload weeding diagnostic %v", req.Name)
	uploader := NewWeeedingDiagnosticsUploader()
	go uploader.uploadDirectory(req.Name, s.robotClient, s.redisClient, s.weedingDiagnosticsNode)
	return &data_upload_manager.StartWeedingDiagnosticsUploadResponse{}, nil
}

func (s *EmergencyService) StartPlantCaptchaUpload(ctx context.Context, req *data_upload_manager.StartPlantCaptchaUploadRequest) (*data_upload_manager.StartPlantCaptchaUploadResponse, error) {
	logrus.Infof("EmergencyDataCapture: Received request to upload plant captcha %v", req.Name)
	uploader := NewPlantCaptchaUploader()
	go uploader.uploadDirectory(req.Name, s.robotClient, s.redisClient, s.plantCaptchaNode)
	return &data_upload_manager.StartPlantCaptchaUploadResponse{}, nil
}
