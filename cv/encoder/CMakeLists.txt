add_library(encoder SHARED feature_extractor.cpp)
target_link_libraries(encoder PUBLIC trt_runtime exceptions lib_common_model)

pybind11_add_module(encoder_python SHARED encoder_python.cpp)
target_link_libraries(encoder_python PUBLIC encoder torch_python)
target_compile_options(encoder_python PRIVATE -fvisibility=default)
set_target_properties(encoder_python PROPERTIES OUTPUT_NAME encoder_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})
