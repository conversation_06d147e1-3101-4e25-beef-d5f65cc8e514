#pragma once

#include <boost/lockfree/queue.hpp>
#include <cassert>
#include <deque>
#include <functional>
#include <optional>
#include <spdlog/spdlog.h>
#include <stdint.h>
#include <torch/torch.h>
#include <vector>

#include "trajectory/cpp/flow_accumulator.h"
#include "weed_tracking/cpp/deduplication/deduplication.h"
#include "weed_tracking/cpp/tracking/tracker.h"
#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_atomic_accessor.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <cv/runtime/cpp/client/cv_runtime_client.hpp>
#include <lib/common/cpp/time.h>
#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <lib/common/geometric/cpp/global_height_estimator_collection.hpp>
#include "generated/recorder/proto/recorder.pb.h"

namespace carbon {
namespace weed_tracking {

inline std::pair<float, float> get_new_location_mm(float robot_distance_x, float robot_distance_y, double x_mm,
                                                   double y_mm) {
  auto delta_y_mm = robot_distance_y;
  auto delta_x_mm = robot_distance_x;

  return std::make_pair(x_mm + delta_x_mm, y_mm + delta_y_mm);
}

inline void update_tracked_objects_by_mm(Tracker *tracker,
                                         std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam,
                                         float robot_distance_mm_x, float robot_distance_mm_y, float max_pos_mm_y,
                                         int64_t current_timestamp_ms,
                                         std::pair<uint32_t, uint32_t> original_height_width,
                                         std::function<void(uint32_t)> untrack_callback) {

  auto tracked_ids = tracker->get_ids();

  std::vector<trajectory::Trajectory::ID> updated_ids;
  updated_ids.reserve(tracked_ids.size());
  for (auto tracked_id : tracked_ids) {
    auto traj = tracker->get_nullable_trajectory(tracked_id);
    if (!traj) {
      continue; // This trajectory has been removed
    }
    auto last_location = traj->get_last_item();
    auto x_mm = last_location.get_x_mm();
    auto y_mm = last_location.get_y_mm();
    auto z_mm = last_location.get_z_mm();
    auto new_location = get_new_location_mm(robot_distance_mm_x, robot_distance_mm_y, x_mm, y_mm);

    auto new_x_mm = new_location.first;
    auto new_y_mm = new_location.second;

    traj->append(trajectory::TrackedItemCentroid(last_location, 0, 0, new_x_mm, new_y_mm, z_mm, current_timestamp_ms));

    auto [new_x_px, new_y_px] =
        geo_cam->get_undistorted_px_from_abs_position(std::make_tuple(new_x_mm, new_y_mm, z_mm));
    if (!(new_y_px >= 0 && new_x_px >= 0 && new_x_px < (float)original_height_width.second &&
          new_y_mm < max_pos_mm_y)) {
      untrack_callback(tracked_id);
    } else {
      updated_ids.push_back(tracked_id);
    }
  }
  tracker->push_position_update(updated_ids);
}

struct ReconciliationResults {
  std::vector<uint32_t> new_ids;
  std::map<uint32_t, std::pair<float, float>> deepweed_flow_offsets_xy;
  size_t number_deduplicated_plants;
  size_t number_unmatched_plants_predictions;
  size_t number_undeduplicated_plants;
  std::map<size_t, trajectory::Trajectory::ID> model_to_track;
  bool plant_matcher_valid;
};

inline std::tuple<float, float, bool> undistort(std::pair<float, float> coords, torch::Tensor map_x,
                                                torch::Tensor map_y) {
  auto x = coords.first;
  auto y = coords.second;
  if (x >= 0 && x < (float)map_x.size(1) && y >= 0 && y < (float)map_y.size(0)) {
    x = map_x.index({(int64_t)y, (int64_t)x}).item<float>();
    y = map_y.index({(int64_t)y, (int64_t)x}).item<float>();
  } else {
    spdlog::warn("Not able to undistort: x {} y {}", x, y);
    return std::tuple<float, float, bool>(0, 0, false);
  }

  return std::tuple<float, float, bool>(x, y, true);
}

inline ReconciliationResults reconcile_tracked_objects_with_model_predictions_deepweed_tracking(
    Tracker *tracker, std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam,
    std::vector<trajectory::TrackedItemCentroid> &model_centroids, int64_t dedup_timestamp_ms,
    int64_t latest_timestamp_ms, std::shared_ptr<trajectory::FlowAccumulator> robot_distance_accumulator,
    float plant_dedup_radius, std::vector<::recorder::TrackedItem> &recorded_tracked_items,
    std::map<size_t, std::pair<float, float>> &model_undistorted_coords,
    ::recorder::CandidateShift &best_candidate_match,
    std::vector<::recorder::CandidateShift> &all_candidate_matches
  ) {
  static config::ConfigAtomicAccessor<uint32_t> max_age_in_bounds(
      config::get_global_config_subscriber()->get_config_node("aimbot", "weed_tracking/max_age_in_bounds"));
  static config::ConfigAtomicAccessor<uint32_t> max_age_out_of_bounds(
      config::get_global_config_subscriber()->get_config_node("aimbot", "weed_tracking/max_age_out_of_bounds"));
  static config::ConfigAtomicAccessor<uint32_t> position_range_offset(
      config::get_global_config_subscriber()->get_config_node("aimbot", "weed_tracking/position_range_offset"));

  std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> plant_centroids_with_keys;

  undistort_model_centroids(model_centroids, geo_cam->d2u_mapx(), geo_cam->d2u_mapy(), plant_centroids_with_keys);

  for (auto &plant_centroid : plant_centroids_with_keys) {
    model_undistorted_coords[plant_centroid.first] = std::make_pair(plant_centroid.second.get_x(), plant_centroid.second.get_y());
  } 

  auto kd_tree_plant_centroids = KDTreeAdaptor(plant_centroids_with_keys);
  std::vector<std::optional<std::tuple<float, float>>> tracked_plant_xy;

  auto tracked_item_ids = tracker->get_ids();
  std::map<size_t, std::optional<std::pair<bool, ::recorder::TrackedItemCoordinates>>> tracked_items_coordinates;
  auto [matched_tracked_items, unmatched_model_predictions, unmatched_tracked_items, plant_matcher_valid] = deduplicate_ransac(
      tracker, geo_cam, dedup_timestamp_ms, plant_dedup_radius, kd_tree_plant_centroids, plant_centroids_with_keys, tracked_item_ids, tracked_plant_xy, best_candidate_match, all_candidate_matches, tracked_items_coordinates);

  uint32_t id;

  uint32_t number_deduplicated_plants = 0;
  uint32_t number_unmatched_plants = 0;
  uint32_t number_undeduplicated_plants = 0;

  std::map<uint32_t, std::pair<float, float>> deepweed_flow_offsets_xy;
  std::vector<std::tuple<uint32_t, float, float>> distances_and_errors;
  std::map<size_t, trajectory::Trajectory::ID> model_id_to_track_id;

  std::vector<trajectory::Trajectory::ID> updated_ids;
  updated_ids.reserve(model_centroids.size());

  // std::map<trajectory::Trajectory::ID, std::tuple<float, float>> tracked_ids_to_undistorted_coordinates;
  // std::map<trajectory::Trajectory::ID, std::tuple<float, float, float>> tracked_ids_to_mm_locations;
  // for (size_t i = 0; i < tracked_item_ids.size(); i++) {
  //   if (tracked_plant_xy[i].has_value() && tracked_plant_xyz_mm[i].has_value()) {
  //     tracked_ids_to_undistorted_coordinates[tracked_item_ids[i]] = tracked_plant_xy[i].value();
  //     tracked_ids_to_mm_locations[tracked_item_ids[i]] = tracked_plant_xyz_mm[i].value();
  //   }
  // }

  for (auto it = matched_tracked_items.begin(); it != matched_tracked_items.end(); ++it) {
    id = it->first;
    auto centroid = it->second.second;

    ::recorder::TrackedItem recorder_tracked_item;
    recorder_tracked_item.set_deduplicated(true);
    recorder_tracked_item.set_trajectory_id(id);

    if (
      tracked_items_coordinates.find(id) != tracked_items_coordinates.end() && tracked_items_coordinates[id].has_value()
    ) {
      auto coordinates_before = recorder_tracked_item.mutable_coordinates_before();
      *coordinates_before = tracked_items_coordinates[id].value().second;
      recorder_tracked_item.set_in_camera(tracked_items_coordinates[id].value().first);
    }

    model_id_to_track_id.emplace(centroid.index(), id);
    auto dl_location_at_timestamp = it->second.second;

    auto trajectory = tracker->get_trajectory(id);
    if (centroid.is_plant_captcha_only()) {
      trajectory->increment_number_of_undetected_subsequent_instances();
    }

    number_deduplicated_plants += 1;

    // Push height estimation
    auto earliest_time = robot_distance_accumulator->get_earliest_time();
    auto first_perspective_optional = trajectory->get_first_centroid_with_perspective_after_timestamp(earliest_time);

    if (!first_perspective_optional.has_value()) {
      trajectory->set_perspective_at_timestamp(dedup_timestamp_ms, dl_location_at_timestamp);
      continue;
    }

    auto first_perspective = first_perspective_optional.value();

    auto first_perspective_timestamp = first_perspective.get_timestamp_ms();

    auto p2p_coord = first_perspective.get_p2p_coord();

    auto [first_perspective_px_x, first_perspective_px_y, undistort_success] =
        undistort(p2p_coord, geo_cam->d2u_mapx(), geo_cam->d2u_mapy());

    auto distance_px_x = dl_location_at_timestamp.get_x() - first_perspective_px_x;
    auto distance_px_y = dl_location_at_timestamp.get_y() - first_perspective_px_y;

    std::tuple<double, double> flow_over_time_range = (std::tuple<double, double>)robot_distance_accumulator->get_flow(
        first_perspective_timestamp, dl_location_at_timestamp.get_timestamp_ms());

    if (undistort_success) {
      trajectory->push_height_datapoint(std::make_tuple(distance_px_x, distance_px_y), flow_over_time_range);
    }
    spdlog::debug("Estimated height for {} is {}", id, trajectory->get_ground_position_z_mm() / 25.4);

    spdlog::debug("  AVERAGE id: {} PX DIST PER TICK. dist_px_x {} dist_px_y {} flow_over_time_range x {} "
                  "flow_over_time_range y {}, avg x {}, avg y {}. New distance {}",
                  id, distance_px_x, distance_px_y, std::get<0>(flow_over_time_range),
                  std::get<1>(flow_over_time_range), distance_px_x / std::get<1>(flow_over_time_range),
                  distance_px_y / std::get<1>(flow_over_time_range), trajectory->get_ground_position_z_mm());

    auto new_height_mm = trajectory->get_ground_position_z_mm();

    auto tracked_location_at_timestamp = trajectory->get_coords_at_timestamp(dedup_timestamp_ms);

    if (!tracked_location_at_timestamp.has_value()) {
      continue;
    }

    // Update coords since timestamp
    auto [dl_location_at_timestamp_mm_x, dl_location_at_timestamp_mm_y, dl_location_at_timestamp_mm_z] =
        geo_cam->get_abs_position_from_undistorted_px_from_height_estimate(
            std::make_tuple((double)dl_location_at_timestamp.get_x(), (double)dl_location_at_timestamp.get_y()),
            new_height_mm);

    auto coordinates_after = recorder_tracked_item.mutable_coordinates_after();
    coordinates_after->set_x_mm((float)dl_location_at_timestamp_mm_x);
    coordinates_after->set_y_mm((float)dl_location_at_timestamp_mm_y);
    coordinates_after->set_z_mm((float)dl_location_at_timestamp_mm_z);
    coordinates_after->set_x_px(dl_location_at_timestamp.get_x());
    coordinates_after->set_y_px(dl_location_at_timestamp.get_y());
    (void)dl_location_at_timestamp_mm_z;
    trajectory->set_perspective_at_timestamp(dedup_timestamp_ms, dl_location_at_timestamp);
    trajectory->set_mm_coords_at_timestamp(dedup_timestamp_ms, dl_location_at_timestamp_mm_x,
                                           dl_location_at_timestamp_mm_y, new_height_mm);
    trajectory->update_coords_since_timestamp(dedup_timestamp_ms, new_height_mm, robot_distance_accumulator,
                                              dl_location_at_timestamp);

    auto [tracked_location_at_timestamp_px_x, tracked_location_at_timestamp_px_y] =
        geo_cam->get_undistorted_px_from_abs_position(std::make_tuple(
            tracked_location_at_timestamp.value().get_x_mm(), tracked_location_at_timestamp.value().get_y_mm(),
            tracked_location_at_timestamp.value().get_z_mm()));

    float diff_y = dl_location_at_timestamp.get_y() - (float)tracked_location_at_timestamp_px_y;
    float diff_x = dl_location_at_timestamp.get_x() - (float)tracked_location_at_timestamp_px_x;

    deepweed_flow_offsets_xy[id] = std::pair<float, float>(diff_x, diff_y);

    updated_ids.push_back(id);
    recorded_tracked_items.push_back(recorder_tracked_item);
  }

  int64_t now = CarbonClock::instance().timestamp_ms();

  // Update all unmatched tracked items (items being tracked without corresponding model predictions)
  for (auto it = unmatched_tracked_items.begin(); it != unmatched_tracked_items.end(); ++it) {
    id = it->first;

    ::recorder::TrackedItem recorder_tracked_item;
    recorder_tracked_item.set_deduplicated(false);
    recorder_tracked_item.set_trajectory_id(id);

    if (
      tracked_items_coordinates.find(id) != tracked_items_coordinates.end() && tracked_items_coordinates[id].has_value()
    ) {
      auto coordinates_before = recorder_tracked_item.mutable_coordinates_before();
      *coordinates_before = tracked_items_coordinates[id].value().second;
      recorder_tracked_item.set_in_camera(tracked_items_coordinates[id].value().first);
    }

    auto trajectory = tracker->get_trajectory(id);
    number_undeduplicated_plants += 1;

    // allow x cam so we keep tracks that are dups alive if either has new perspective
    auto c_perspective = trajectory->get_last_centroid_with_perspective(true);
    auto c_last = trajectory->get_last_item();
    auto predict_pos_px = geo_cam->get_undistorted_px_from_abs_position(
        std::make_tuple(c_last.get_x_mm(), c_last.get_y_mm(), c_last.get_z_mm()));

    uint32_t max_age = max_age_out_of_bounds.get_value();
    // Inbounds check
    if (std::get<1>(predict_pos_px) >= 0 &&
        (std::get<1>(predict_pos_px) + position_range_offset.get_value()) < std::get<1>(geo_cam->resolution())) {
      trajectory->increment_number_of_undetected_subsequent_instances();
      max_age = max_age_in_bounds.get_value();
    } else {
      // this is out of bounds, mark track as such
      trajectory->set_out_of_predict_space();
    }

    if (max_age > 0 && now - c_perspective.get_timestamp_ms() > static_cast<int64_t>(max_age)) {
      // Only want to filter out if timed out while in bounds
      tracker->untrack(id, max_age == max_age_in_bounds.get_value());
    } else {
      updated_ids.push_back(id);
    }

    recorded_tracked_items.push_back(recorder_tracked_item);
  }

  auto accumulated_flow = robot_distance_accumulator->get_flow(dedup_timestamp_ms, latest_timestamp_ms);
  std::vector<uint32_t> new_ids;

  // Update all unmatched model_predictions (model predictions that don't match a tracked item)
  for (auto &unmatched_model_prediction : unmatched_model_predictions) {
    id = tracker->track(unmatched_model_prediction.second);
    model_id_to_track_id.emplace(unmatched_model_prediction.second.index(), id);
    auto new_tracker_height = tracker->get_ground_position_z_mm((uint32_t)unmatched_model_prediction.second.get_x(),
                                                                unmatched_model_prediction.second.get_size_mm());
    auto [abs_position_x, abs_position_y, abs_position_z] =
        geo_cam->get_abs_position_from_undistorted_px_from_height_estimate(
            std::make_tuple((double)unmatched_model_prediction.second.get_x(),
                            (double)unmatched_model_prediction.second.get_y()),
            new_tracker_height);
    (void)abs_position_z;
    auto trajectory = tracker->get_trajectory(id);
    trajectory->set_mm_coords_at_timestamp(unmatched_model_prediction.second.get_timestamp_ms(), abs_position_x,
                                           abs_position_y, new_tracker_height);
    new_ids.push_back(id);

    number_unmatched_plants += 1;

    // Keep track of the number of times the weed could have been detected but wasn't
    auto timestamp_ms_when_weed_first_appeared = robot_distance_accumulator->get_timestamp_for_flow_y_mm(
        (float)abs_position_x, (float)abs_position_y, (float)new_tracker_height, geo_cam,
        unmatched_model_prediction.second.get_timestamp_ms());
    if (timestamp_ms_when_weed_first_appeared >= 0) {
      int number_of_deepweed_timestamps =
          tracker->number_of_deepweed_timestamps_since_timestamp(timestamp_ms_when_weed_first_appeared);
      trajectory->set_number_of_undetected_initial_instances(number_of_deepweed_timestamps);
    }

    auto centroid = unmatched_model_prediction.second;
    if (centroid.is_plant_captcha_only()) {
      trajectory->increment_number_of_undetected_subsequent_instances();
    }

    if (dedup_timestamp_ms != latest_timestamp_ms) {
      auto new_location =
          get_new_location_mm(accumulated_flow.first, accumulated_flow.second, abs_position_x, abs_position_y);

      auto new_x_mm = new_location.first;
      auto new_y_mm = new_location.second;

      trajectory->append(trajectory::TrackedItemCentroid(unmatched_model_prediction.second, 0, 0, new_x_mm, new_y_mm,
                                                         new_tracker_height, latest_timestamp_ms));
    } else {
      spdlog::warn("Weed {} has the same dedup and OF timestamps. Dedup {} OF {}", id, dedup_timestamp_ms,
                   latest_timestamp_ms);
    }

    updated_ids.push_back(id);
  }

  tracker->push_position_update(updated_ids);
  tracker->append_deepweed_timestamp(dedup_timestamp_ms);

  ReconciliationResults results = {
      new_ids,
      deepweed_flow_offsets_xy,
      number_deduplicated_plants,
      number_unmatched_plants,
      number_undeduplicated_plants,
      model_id_to_track_id,
      plant_matcher_valid
  };

  return results;
}

inline std::string get_detection_clz(std::vector<std::pair<std::string, float>> detection_classes) {
  auto max_it = std::max_element(detection_classes.begin(), detection_classes.end(),
                                 [](const auto &first, const auto &second) { return first.second < second.second; });
  std::string category = "WEED";
  if (max_it != detection_classes.end() && max_it->second > 0.5f) {
    category = max_it->first;
  }
  return category;
}

} // namespace weed_tracking
} // namespace carbon
