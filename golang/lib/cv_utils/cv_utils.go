package cv_utils

import (
	"errors"
	"fmt"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/rows"
)

// GetCVClientForCamId finds the CV runtime client that has a camera with the specified ID.
// It queries all CV runtime clients in the row to find which one has the camera.
func GetCVClientForCamId(rowClient *rows.RowClients, camID string) (*cv_runtime_client.CVRuntimeClient, error) {
	type Result struct {
		client *cv_runtime_client.CVRuntimeClient
		resp   *cv.GetCameraInfoResponse
		err    error
	}
	var retErr error
	wg := sync.WaitGroup{}
	results := make(chan Result, len(rowClient.CVRuntimeClients))
	for _, client := range rowClient.CVRuntimeClients {
		wg.Add(1)
		go func(client *cv_runtime_client.CVRuntimeClient) {
			defer wg.Done()
			resp, err := client.GetCameraInfo()
			res := Result{
				client: client,
				resp:   resp,
				err:    err,
			}
			results <- res
		}(client)
	}
	wg.Wait()
	close(results)
	for res := range results {
		if res.err != nil {
			retErr = errors.Join(retErr, res.err)
			continue
		}
		for _, camInfo := range res.resp.GetCameraInfo() {
			if camInfo.GetCamId() == camID {
				return res.client, nil
			}
		}
	}
	retErr = fmt.Errorf("no camera with ID %v found: %v", camID, retErr)
	return nil, retErr
}
