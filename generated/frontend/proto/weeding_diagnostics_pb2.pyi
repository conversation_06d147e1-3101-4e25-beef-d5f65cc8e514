"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2 import (
    GetDimensionsResponse as core___controls___exterminator___controllers___aimbot___process___proto___aimbot_pb2___GetDimensionsResponse,
)

from generated.cv.runtime.proto.cv_runtime_pb2 import (
    DeepweedOutput as cv___runtime___proto___cv_runtime_pb2___DeepweedOutput,
)

from generated.frontend.proto.image_stream_pb2 import (
    Annotations as frontend___proto___image_stream_pb2___Annotations,
)

from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.thinning.thinning_pb2 import (
    ConfigDefinition as proto___thinning___thinning_pb2___ConfigDefinition,
)

from generated.recorder.proto.recorder_pb2 import (
    DeepweedPredictionRecord as recorder___proto___recorder_pb2___DeepweedPredictionRecord,
    RotaryTicksRecord as recorder___proto___recorder_pb2___RotaryTicksRecord,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)

from generated.weed_tracking.proto.weed_tracking_pb2 import (
    DiagnosticsSnapshot as weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot,
    TrajectorySnapshot as weed_tracking___proto___weed_tracking_pb2___TrajectorySnapshot,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

UploadStateValue = typing___NewType('UploadStateValue', builtin___int)
type___UploadStateValue = UploadStateValue
UploadState: _UploadState
class _UploadState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[UploadStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NONE = typing___cast(UploadStateValue, 0)
    IN_PROGRESS = typing___cast(UploadStateValue, 1)
    DONE = typing___cast(UploadStateValue, 2)
NONE = typing___cast(UploadStateValue, 0)
IN_PROGRESS = typing___cast(UploadStateValue, 1)
DONE = typing___cast(UploadStateValue, 2)

class RecordWeedingDiagnosticsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    ttl_sec: builtin___int = ...
    crop_images_per_sec: builtin___float = ...
    weed_images_per_sec: builtin___float = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ttl_sec : typing___Optional[builtin___int] = None,
        crop_images_per_sec : typing___Optional[builtin___float] = None,
        weed_images_per_sec : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_images_per_sec",b"crop_images_per_sec",u"name",b"name",u"ttl_sec",b"ttl_sec",u"weed_images_per_sec",b"weed_images_per_sec"]) -> None: ...
type___RecordWeedingDiagnosticsRequest = RecordWeedingDiagnosticsRequest

class GetCurrentTrajectoriesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_id",b"row_id"]) -> None: ...
type___GetCurrentTrajectoriesRequest = GetCurrentTrajectoriesRequest

class GetRecordingsListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetRecordingsListRequest = GetRecordingsListRequest

class GetRecordingsListResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___GetRecordingsListResponse = GetRecordingsListResponse

class GetSnapshotRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row_id: builtin___int = ...
    snapshot_number: builtin___int = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row_id : typing___Optional[builtin___int] = None,
        snapshot_number : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name",u"row_id",b"row_id",u"snapshot_number",b"snapshot_number"]) -> None: ...
type___GetSnapshotRequest = GetSnapshotRequest

class GetSnapshotResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def snapshot(self) -> weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot: ...

    def __init__(self,
        *,
        snapshot : typing___Optional[weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"snapshot",b"snapshot"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"snapshot",b"snapshot"]) -> None: ...
type___GetSnapshotResponse = GetSnapshotResponse

class OpenRecordingRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name"]) -> None: ...
type___OpenRecordingRequest = OpenRecordingRequest

class TrajectoriesWithImages(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...

    def __init__(self,
        *,
        ids : typing___Optional[typing___Iterable[builtin___int]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ids",b"ids"]) -> None: ...
type___TrajectoriesWithImages = TrajectoriesWithImages

class PredictImages(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    names: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        names : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"names",b"names"]) -> None: ...
type___PredictImages = PredictImages

class PredictImagesPerCam(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ImagesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___PredictImages: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___PredictImages] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ImagesEntry = ImagesEntry


    @property
    def images(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___PredictImages]: ...

    def __init__(self,
        *,
        images : typing___Optional[typing___Mapping[builtin___int, type___PredictImages]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"images",b"images"]) -> None: ...
type___PredictImagesPerCam = PredictImagesPerCam

class OpenRecordingResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class NumSnapshotsPerRowEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...
        value: builtin___int = ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[builtin___int] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___NumSnapshotsPerRowEntry = NumSnapshotsPerRowEntry

    class TrajectoryImagesPerRowEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___TrajectoriesWithImages: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___TrajectoriesWithImages] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___TrajectoryImagesPerRowEntry = TrajectoryImagesPerRowEntry

    class PredictImagesPerRowEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___PredictImagesPerCam: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___PredictImagesPerCam] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___PredictImagesPerRowEntry = PredictImagesPerRowEntry


    @property
    def num_snapshots_per_row(self) -> google___protobuf___internal___containers___ScalarMap[builtin___int, builtin___int]: ...

    @property
    def recording_data(self) -> type___StaticRecordingData: ...

    @property
    def trajectory_images_per_row(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___TrajectoriesWithImages]: ...

    @property
    def predict_images_per_row(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___PredictImagesPerCam]: ...

    def __init__(self,
        *,
        num_snapshots_per_row : typing___Optional[typing___Mapping[builtin___int, builtin___int]] = None,
        recording_data : typing___Optional[type___StaticRecordingData] = None,
        trajectory_images_per_row : typing___Optional[typing___Mapping[builtin___int, type___TrajectoriesWithImages]] = None,
        predict_images_per_row : typing___Optional[typing___Mapping[builtin___int, type___PredictImagesPerCam]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"recording_data",b"recording_data"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"num_snapshots_per_row",b"num_snapshots_per_row",u"predict_images_per_row",b"predict_images_per_row",u"recording_data",b"recording_data",u"trajectory_images_per_row",b"trajectory_images_per_row"]) -> None: ...
type___OpenRecordingResponse = OpenRecordingResponse

class DeleteRecordingRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name"]) -> None: ...
type___DeleteRecordingRequest = DeleteRecordingRequest

class ConfigNodeSnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ValuesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ValuesEntry = ValuesEntry

    class ChildNodesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...

        @property
        def value(self) -> type___ConfigNodeSnapshot: ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[type___ConfigNodeSnapshot] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ChildNodesEntry = ChildNodesEntry


    @property
    def values(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    @property
    def child_nodes(self) -> google___protobuf___internal___containers___MessageMap[typing___Text, type___ConfigNodeSnapshot]: ...

    def __init__(self,
        *,
        values : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        child_nodes : typing___Optional[typing___Mapping[typing___Text, type___ConfigNodeSnapshot]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"child_nodes",b"child_nodes",u"values",b"values"]) -> None: ...
type___ConfigNodeSnapshot = ConfigNodeSnapshot

class CameraDimensions(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width: builtin___int = ...
    height: builtin___int = ...

    def __init__(self,
        *,
        width : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"height",b"height",u"width",b"width"]) -> None: ...
type___CameraDimensions = CameraDimensions

class RowCameras(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class CamsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...

        @property
        def value(self) -> type___CameraDimensions: ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[type___CameraDimensions] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___CamsEntry = CamsEntry


    @property
    def cams(self) -> google___protobuf___internal___containers___MessageMap[typing___Text, type___CameraDimensions]: ...

    def __init__(self,
        *,
        cams : typing___Optional[typing___Mapping[typing___Text, type___CameraDimensions]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cams",b"cams"]) -> None: ...
type___RowCameras = RowCameras

class StaticRecordingData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class LasersEnabledEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___bool = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___bool] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___LasersEnabledEntry = LasersEnabledEntry

    class RowDimensionsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> core___controls___exterminator___controllers___aimbot___process___proto___aimbot_pb2___GetDimensionsResponse: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[core___controls___exterminator___controllers___aimbot___process___proto___aimbot_pb2___GetDimensionsResponse] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___RowDimensionsEntry = RowDimensionsEntry

    class CropSafetyRadiusMmPerRowEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___CropSafetyRadiusMmPerRowEntry = CropSafetyRadiusMmPerRowEntry

    class RowCamerasEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___RowCameras: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___RowCameras] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___RowCamerasEntry = RowCamerasEntry

    rows_recorded: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    recording_timestamp: builtin___int = ...
    weed_point_threshold: builtin___float = ...
    crop_point_threshold: builtin___float = ...
    wheel_diameter_back_left_in: builtin___float = ...
    wheel_diameter_back_right_in: builtin___float = ...
    wheel_diameter_front_left_in: builtin___float = ...
    wheel_diameter_front_right_in: builtin___float = ...

    @property
    def lasers_enabled(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___bool]: ...

    @property
    def root_config(self) -> type___ConfigNodeSnapshot: ...

    @property
    def row_dimensions(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, core___controls___exterminator___controllers___aimbot___process___proto___aimbot_pb2___GetDimensionsResponse]: ...

    @property
    def crop_safety_radius_mm_per_row(self) -> google___protobuf___internal___containers___ScalarMap[builtin___int, builtin___float]: ...

    @property
    def thinning_config(self) -> proto___thinning___thinning_pb2___ConfigDefinition: ...

    @property
    def row_cameras(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___RowCameras]: ...

    def __init__(self,
        *,
        lasers_enabled : typing___Optional[typing___Mapping[typing___Text, builtin___bool]] = None,
        root_config : typing___Optional[type___ConfigNodeSnapshot] = None,
        rows_recorded : typing___Optional[typing___Iterable[builtin___int]] = None,
        row_dimensions : typing___Optional[typing___Mapping[builtin___int, core___controls___exterminator___controllers___aimbot___process___proto___aimbot_pb2___GetDimensionsResponse]] = None,
        crop_safety_radius_mm_per_row : typing___Optional[typing___Mapping[builtin___int, builtin___float]] = None,
        thinning_config : typing___Optional[proto___thinning___thinning_pb2___ConfigDefinition] = None,
        recording_timestamp : typing___Optional[builtin___int] = None,
        row_cameras : typing___Optional[typing___Mapping[builtin___int, type___RowCameras]] = None,
        weed_point_threshold : typing___Optional[builtin___float] = None,
        crop_point_threshold : typing___Optional[builtin___float] = None,
        wheel_diameter_back_left_in : typing___Optional[builtin___float] = None,
        wheel_diameter_back_right_in : typing___Optional[builtin___float] = None,
        wheel_diameter_front_left_in : typing___Optional[builtin___float] = None,
        wheel_diameter_front_right_in : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"root_config",b"root_config",u"thinning_config",b"thinning_config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_point_threshold",b"crop_point_threshold",u"crop_safety_radius_mm_per_row",b"crop_safety_radius_mm_per_row",u"lasers_enabled",b"lasers_enabled",u"recording_timestamp",b"recording_timestamp",u"root_config",b"root_config",u"row_cameras",b"row_cameras",u"row_dimensions",b"row_dimensions",u"rows_recorded",b"rows_recorded",u"thinning_config",b"thinning_config",u"weed_point_threshold",b"weed_point_threshold",u"wheel_diameter_back_left_in",b"wheel_diameter_back_left_in",u"wheel_diameter_back_right_in",b"wheel_diameter_back_right_in",u"wheel_diameter_front_left_in",b"wheel_diameter_front_left_in",u"wheel_diameter_front_right_in",b"wheel_diameter_front_right_in"]) -> None: ...
type___StaticRecordingData = StaticRecordingData

class GetTrajectoryDataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row_id: builtin___int = ...
    trajectory_id: builtin___int = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row_id : typing___Optional[builtin___int] = None,
        trajectory_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name",u"row_id",b"row_id",u"trajectory_id",b"trajectory_id"]) -> None: ...
type___GetTrajectoryDataRequest = GetTrajectoryDataRequest

class LastSnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    diagnostics_snapshot_number: builtin___int = ...

    @property
    def trajectory(self) -> weed_tracking___proto___weed_tracking_pb2___TrajectorySnapshot: ...

    def __init__(self,
        *,
        trajectory : typing___Optional[weed_tracking___proto___weed_tracking_pb2___TrajectorySnapshot] = None,
        diagnostics_snapshot_number : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"trajectory",b"trajectory"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"diagnostics_snapshot_number",b"diagnostics_snapshot_number",u"trajectory",b"trajectory"]) -> None: ...
type___LastSnapshot = LastSnapshot

class ExtraTrajectoryField(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    label: typing___Text = ...
    value: typing___Text = ...
    color: typing___Text = ...

    def __init__(self,
        *,
        label : typing___Optional[typing___Text] = None,
        value : typing___Optional[typing___Text] = None,
        color : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"color",b"color",u"label",b"label",u"value",b"value"]) -> None: ...
type___ExtraTrajectoryField = ExtraTrajectoryField

class TargetImage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    timestamp: builtin___int = ...
    p2p_predict_x: builtin___int = ...
    p2p_predict_y: builtin___int = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        timestamp : typing___Optional[builtin___int] = None,
        p2p_predict_x : typing___Optional[builtin___int] = None,
        p2p_predict_y : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"p2p_predict_x",b"p2p_predict_x",u"p2p_predict_y",b"p2p_predict_y",u"timestamp",b"timestamp"]) -> None: ...
type___TargetImage = TargetImage

class P2PPredictImage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    center_x_px: builtin___int = ...
    center_y_px: builtin___int = ...
    radius_px: builtin___int = ...
    pcam_id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        center_x_px : typing___Optional[builtin___int] = None,
        center_y_px : typing___Optional[builtin___int] = None,
        radius_px : typing___Optional[builtin___int] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        pcam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_x_px",b"center_x_px",u"center_y_px",b"center_y_px",u"name",b"name",u"pcam_id",b"pcam_id",u"radius_px",b"radius_px",u"ts",b"ts"]) -> None: ...
type___P2PPredictImage = P2PPredictImage

class TrajectoryPredictImageMetadata(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    center_x_px: builtin___int = ...
    center_y_px: builtin___int = ...
    radius_px: builtin___int = ...
    pcam_id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        center_x_px : typing___Optional[builtin___int] = None,
        center_y_px : typing___Optional[builtin___int] = None,
        radius_px : typing___Optional[builtin___int] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        pcam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_x_px",b"center_x_px",u"center_y_px",b"center_y_px",u"pcam_id",b"pcam_id",u"radius_px",b"radius_px",u"ts",b"ts"]) -> None: ...
type___TrajectoryPredictImageMetadata = TrajectoryPredictImageMetadata

class TrajectoryData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crosshair_x: builtin___int = ...
    crosshair_y: builtin___int = ...

    @property
    def predict_image(self) -> type___TrajectoryPredictImageMetadata: ...

    @property
    def target_images(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TargetImage]: ...

    @property
    def last_snapshot(self) -> type___LastSnapshot: ...

    @property
    def extra_fields(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ExtraTrajectoryField]: ...

    @property
    def p2p_predict_images(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___P2PPredictImage]: ...

    def __init__(self,
        *,
        predict_image : typing___Optional[type___TrajectoryPredictImageMetadata] = None,
        target_images : typing___Optional[typing___Iterable[type___TargetImage]] = None,
        last_snapshot : typing___Optional[type___LastSnapshot] = None,
        extra_fields : typing___Optional[typing___Iterable[type___ExtraTrajectoryField]] = None,
        crosshair_x : typing___Optional[builtin___int] = None,
        crosshair_y : typing___Optional[builtin___int] = None,
        p2p_predict_images : typing___Optional[typing___Iterable[type___P2PPredictImage]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"last_snapshot",b"last_snapshot",u"predict_image",b"predict_image"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crosshair_x",b"crosshair_x",u"crosshair_y",b"crosshair_y",u"extra_fields",b"extra_fields",u"last_snapshot",b"last_snapshot",u"p2p_predict_images",b"p2p_predict_images",u"predict_image",b"predict_image",u"target_images",b"target_images"]) -> None: ...
type___TrajectoryData = TrajectoryData

class GetTrajectoryPredictImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row_id: builtin___int = ...
    trajectory_id: builtin___int = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row_id : typing___Optional[builtin___int] = None,
        trajectory_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name",u"row_id",b"row_id",u"trajectory_id",b"trajectory_id"]) -> None: ...
type___GetTrajectoryPredictImageRequest = GetTrajectoryPredictImageRequest

class GetTrajectoryTargetImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row_id: builtin___int = ...
    trajectory_id: builtin___int = ...
    image_name: typing___Text = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row_id : typing___Optional[builtin___int] = None,
        trajectory_id : typing___Optional[builtin___int] = None,
        image_name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"image_name",b"image_name",u"recording_name",b"recording_name",u"row_id",b"row_id",u"trajectory_id",b"trajectory_id"]) -> None: ...
type___GetTrajectoryTargetImageRequest = GetTrajectoryTargetImageRequest

class ImageChunk(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    image_chunk: builtin___bytes = ...

    def __init__(self,
        *,
        image_chunk : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"image_chunk",b"image_chunk"]) -> None: ...
type___ImageChunk = ImageChunk

class GetPredictImageMetadataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row_id: builtin___int = ...
    image_name: typing___Text = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row_id : typing___Optional[builtin___int] = None,
        image_name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"image_name",b"image_name",u"recording_name",b"recording_name",u"row_id",b"row_id"]) -> None: ...
type___GetPredictImageMetadataRequest = GetPredictImageMetadataRequest

class GetPredictImageMetadataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def annotations(self) -> frontend___proto___image_stream_pb2___Annotations: ...

    @property
    def deepweed_output(self) -> cv___runtime___proto___cv_runtime_pb2___DeepweedOutput: ...

    @property
    def deepweed_output_below_threshold(self) -> cv___runtime___proto___cv_runtime_pb2___DeepweedOutput: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        annotations : typing___Optional[frontend___proto___image_stream_pb2___Annotations] = None,
        deepweed_output : typing___Optional[cv___runtime___proto___cv_runtime_pb2___DeepweedOutput] = None,
        deepweed_output_below_threshold : typing___Optional[cv___runtime___proto___cv_runtime_pb2___DeepweedOutput] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"annotations",b"annotations",u"deepweed_output",b"deepweed_output",u"deepweed_output_below_threshold",b"deepweed_output_below_threshold",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"annotations",b"annotations",u"deepweed_output",b"deepweed_output",u"deepweed_output_below_threshold",b"deepweed_output_below_threshold",u"ts",b"ts"]) -> None: ...
type___GetPredictImageMetadataResponse = GetPredictImageMetadataResponse

class GetPredictImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row_id: builtin___int = ...
    image_name: typing___Text = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row_id : typing___Optional[builtin___int] = None,
        image_name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"image_name",b"image_name",u"recording_name",b"recording_name",u"row_id",b"row_id"]) -> None: ...
type___GetPredictImageRequest = GetPredictImageRequest

class StartUploadRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name"]) -> None: ...
type___StartUploadRequest = StartUploadRequest

class GetNextUploadStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name",u"ts",b"ts"]) -> None: ...
type___GetNextUploadStateRequest = GetNextUploadStateRequest

class GetNextUploadStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    upload_state: type___UploadStateValue = ...
    percent_uploaded: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        upload_state : typing___Optional[type___UploadStateValue] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        percent_uploaded : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"percent_uploaded",b"percent_uploaded",u"ts",b"ts",u"upload_state",b"upload_state"]) -> None: ...
type___GetNextUploadStateResponse = GetNextUploadStateResponse

class GetDeepweedPredictionsCountRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row: builtin___int = ...
    cam_id: builtin___int = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row : typing___Optional[builtin___int] = None,
        cam_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"recording_name",b"recording_name",u"row",b"row"]) -> None: ...
type___GetDeepweedPredictionsCountRequest = GetDeepweedPredictionsCountRequest

class GetDeepweedPredictionsCountResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    count: builtin___int = ...

    def __init__(self,
        *,
        count : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"count",b"count"]) -> None: ...
type___GetDeepweedPredictionsCountResponse = GetDeepweedPredictionsCountResponse

class GetDeepweedPredictionsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row: builtin___int = ...
    cam_id: builtin___int = ...
    idx: builtin___int = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row : typing___Optional[builtin___int] = None,
        cam_id : typing___Optional[builtin___int] = None,
        idx : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"idx",b"idx",u"recording_name",b"recording_name",u"row",b"row"]) -> None: ...
type___GetDeepweedPredictionsRequest = GetDeepweedPredictionsRequest

class GetDeepweedPredictionsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def predictions(self) -> recorder___proto___recorder_pb2___DeepweedPredictionRecord: ...

    def __init__(self,
        *,
        predictions : typing___Optional[recorder___proto___recorder_pb2___DeepweedPredictionRecord] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"predictions",b"predictions"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"predictions",b"predictions"]) -> None: ...
type___GetDeepweedPredictionsResponse = GetDeepweedPredictionsResponse

class FindTrajectoryRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row_id: builtin___int = ...
    trajectory_id: builtin___int = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row_id : typing___Optional[builtin___int] = None,
        trajectory_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name",u"row_id",b"row_id",u"trajectory_id",b"trajectory_id"]) -> None: ...
type___FindTrajectoryRequest = FindTrajectoryRequest

class FindTrajectoryResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    snapshot_id: builtin___int = ...

    @property
    def trajectory(self) -> weed_tracking___proto___weed_tracking_pb2___TrajectorySnapshot: ...

    def __init__(self,
        *,
        snapshot_id : typing___Optional[builtin___int] = None,
        trajectory : typing___Optional[weed_tracking___proto___weed_tracking_pb2___TrajectorySnapshot] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"trajectory",b"trajectory"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"snapshot_id",b"snapshot_id",u"trajectory",b"trajectory"]) -> None: ...
type___FindTrajectoryResponse = FindTrajectoryResponse

class GetRotaryTicksRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    recording_name: typing___Text = ...
    row_id: builtin___int = ...

    def __init__(self,
        *,
        recording_name : typing___Optional[typing___Text] = None,
        row_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"recording_name",b"recording_name",u"row_id",b"row_id"]) -> None: ...
type___GetRotaryTicksRequest = GetRotaryTicksRequest

class GetRotaryTicksResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    wheel_encoder_resolution: builtin___int = ...

    @property
    def records(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[recorder___proto___recorder_pb2___RotaryTicksRecord]: ...

    def __init__(self,
        *,
        records : typing___Optional[typing___Iterable[recorder___proto___recorder_pb2___RotaryTicksRecord]] = None,
        wheel_encoder_resolution : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"records",b"records",u"wheel_encoder_resolution",b"wheel_encoder_resolution"]) -> None: ...
type___GetRotaryTicksResponse = GetRotaryTicksResponse

class SnapshotPredictImagesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_id",b"row_id"]) -> None: ...
type___SnapshotPredictImagesRequest = SnapshotPredictImagesRequest

class PcamSnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pcam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        pcam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pcam_id",b"pcam_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___PcamSnapshot = PcamSnapshot

class SnapshotPredictImagesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def snapshots(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PcamSnapshot]: ...

    def __init__(self,
        *,
        snapshots : typing___Optional[typing___Iterable[type___PcamSnapshot]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"snapshots",b"snapshots"]) -> None: ...
type___SnapshotPredictImagesResponse = SnapshotPredictImagesResponse

class GetChipForPredictImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pcam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...
    center_x_px: builtin___int = ...
    center_y_px: builtin___int = ...
    row_id: builtin___int = ...

    def __init__(self,
        *,
        pcam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        center_x_px : typing___Optional[builtin___int] = None,
        center_y_px : typing___Optional[builtin___int] = None,
        row_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_x_px",b"center_x_px",u"center_y_px",b"center_y_px",u"pcam_id",b"pcam_id",u"row_id",b"row_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetChipForPredictImageRequest = GetChipForPredictImageRequest

class GetChipForPredictImageResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    chip_image: builtin___bytes = ...

    def __init__(self,
        *,
        chip_image : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"chip_image",b"chip_image"]) -> None: ...
type___GetChipForPredictImageResponse = GetChipForPredictImageResponse
