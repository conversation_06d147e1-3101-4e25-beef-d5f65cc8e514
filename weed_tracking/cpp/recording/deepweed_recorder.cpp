#include <weed_tracking/cpp/recording/deepweed_recorder.hpp>

namespace carbon {
namespace weed_tracking {

DeepweedRecorder::DeepweedRecorder(std::shared_ptr<AimbotWeedTracker> weed_tracker)
    : weed_tracker_(weed_tracker), stop_at_timestamp_ms_(0), record_writer_(nullptr),
      task_(&DeepweedRecorder::run, this) {}

void DeepweedRecorder::start(int64_t duration_ms, std::string name) {
  std::lock_guard<std::mutex> lk(mut_);
  stop_at_timestamp_ms_ = maka_control_timestamp_ms() + duration_ms;
  std::string dir_path = "/data/ftp/" + name;
  std::string file_path = dir_path + "/" + weed_tracker_->get_pcam_id() + "_deepweed.carbon";
  std::filesystem::create_directories(dir_path);
  record_writer_ = std::make_shared<recorder::RecordWriter>("DeepweedPredictionRecord", file_path);
  cond_.notify_all();
}

std::shared_ptr<::recorder::DeepweedPredictionRecord>
DeepweedRecorder::build_record(const PostDedupFrame &frame_in) {
  std::shared_ptr<::recorder::DeepweedPredictionRecord> record =
      std::make_shared<::recorder::DeepweedPredictionRecord>();
  int64_t received_timestamp_ms = frame_in.receive_ts_ms;

  record->set_record_timestamp_ms(received_timestamp_ms);
  auto frame = record->mutable_frame();

  frame->set_plant_matcher_valid(frame_in.plant_matcher_valid);
  if (frame_in.plant_matcher_valid) {
    auto best_shift = frame->mutable_best_shift();
    *best_shift = frame_in.best_candidate_match;
  }
  frame->mutable_candidate_shifts()->Reserve((int)frame_in.all_candidate_matches.size());
  for (auto &candidate : frame_in.all_candidate_matches) {
    auto shift = frame->add_candidate_shifts();
    *shift = candidate;
  }

  frame->mutable_tracked_items()->Reserve((int)frame_in.tracked_items.size());
  for (auto &tracked_item : frame_in.tracked_items) {
    auto item = frame->add_tracked_items();
    *item = tracked_item;
  }

  frame->clear_detections();
  frame->set_timestamp_ms(frame_in.dw_output.timestamp_ms);
  for (auto &cat : frame_in.dw_output.embedding_categories) {
    frame->add_embedding_categories(cat);
  }
  frame->set_plant_matcher_valid(frame_in.plant_matcher_valid);
  frame->mutable_detections()->Reserve((int)frame_in.dw_output.detections.size());
  for (std::size_t j = 0; j < frame_in.dw_output.detections.size(); j++) {
    auto &det = frame_in.dw_output.detections[j];
    auto detection = frame->add_detections();
    detection->set_x(det.x);
    detection->set_y(det.y);
    detection->set_size(det.size);
    detection->set_score(det.score);
    detection->set_weed_score(det.weed_score);
    detection->set_crop_score(det.crop_score);
    detection->set_plant_score(det.plant_score);
    detection->set_hit_class(det.hit_class == cv::runtime::proto::WEED ? ::recorder::DeepweedDetection_HitClass_WEED
                                                                       : ::recorder::DeepweedDetection_HitClass_CROP);
    for (auto &pair : det.get_detection_classes(frame_in.dw_output.weed_detection_classes)) {
      auto cls = detection->add_detection_classes();
      auto class_name = cls->mutable_class_();
      *class_name = std::get<0>(pair);
      cls->set_score(std::get<1>(pair));
    }
    for (std::size_t i = 0; i < det.mask_intersections.size(); i++) {
      auto inter = detection->add_mask_intersections();
      *inter = frame_in.dw_output.mask_channel_classes[det.mask_intersections[i]];
    }

    {
      auto it = frame_in.dw_to_track.find(j);
      if (it != frame_in.dw_to_track.end()) {
        detection->set_trajectory_id(it->second);
      } else {
        detection->set_trajectory_id(0);
      }
    }

    {
      auto it = frame_in.model_undistorted_coordinates.find(j);
      if (it != frame_in.model_undistorted_coordinates.end()) {
        detection->set_x_undistorted(it->second.first);
        detection->set_y_undistorted(it->second.second);
      }
    }

    for (auto &emb_dist : det.embedding_category_distances) {
      detection->add_embedding_category_distances(emb_dist);
    }
  }
  return record;
}
void DeepweedRecorder::run() {
  int64_t previous_timestamp_ms = 0;
  PostDedupFrame output;
  bool done = true;
  while (true) {
    if (done) {
      std::unique_lock<std::mutex> lk(mut_);
      cond_.wait(lk, [&] { return this->stop_at_timestamp_ms_ > maka_control_timestamp_ms(); });
      spdlog::info("Starting Aimbot Tracker {} recording for {} ms", weed_tracker_->get_pcam_id(),
                   this->stop_at_timestamp_ms_ - maka_control_timestamp_ms());
    }
    done = false;
    bool result = weed_tracker_->get_next_retrieved_deepweed(10000, previous_timestamp_ms, &output);

    if (!result) {
      // Handle case where we have no data but still are done recording.
      std::lock_guard<std::mutex> lk(mut_);
      if (this->stop_at_timestamp_ms_ <= maka_control_timestamp_ms()) {
        record_writer_->terminate();
        done = true;
      }
      continue;
    }

    previous_timestamp_ms = output.dw_output.timestamp_ms;
    auto record = build_record(output);
    {
      std::lock_guard<std::mutex> lk(mut_);

      record_writer_->append(record);

      if (this->stop_at_timestamp_ms_ <= maka_control_timestamp_ms()) {
        record_writer_->terminate();
        done = true;
      }
    }
  }
}

void DeepweedRecorder::join() { record_writer_->join(); }

} // namespace weed_tracking
} // namespace carbon