// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: cv/runtime/proto/cv_runtime.proto

#include "cv/runtime/proto/cv_runtime.pb.h"
#include "cv/runtime/proto/cv_runtime.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace cv {
namespace runtime {
namespace proto {

static const char* CVRuntimeService_method_names[] = {
  "/cv.runtime.proto.CVRuntimeService/SetP2PContext",
  "/cv.runtime.proto.CVRuntimeService/GetCameraDimensions",
  "/cv.runtime.proto.CVRuntimeService/GetCameraInfo",
  "/cv.runtime.proto.CVRuntimeService/GetDeepweedIndexToCategory",
  "/cv.runtime.proto.CVRuntimeService/SetDeepweedDetectionCriteria",
  "/cv.runtime.proto.CVRuntimeService/GetDeepweedDetectionCriteria",
  "/cv.runtime.proto.CVRuntimeService/GetDeepweedSupportedCategories",
  "/cv.runtime.proto.CVRuntimeService/GetCameraTemperatures",
  "/cv.runtime.proto.CVRuntimeService/SetCameraSettings",
  "/cv.runtime.proto.CVRuntimeService/GetCameraSettings",
  "/cv.runtime.proto.CVRuntimeService/StartBurstRecordFrames",
  "/cv.runtime.proto.CVRuntimeService/StopBurstRecordFrames",
  "/cv.runtime.proto.CVRuntimeService/GetConnectors",
  "/cv.runtime.proto.CVRuntimeService/SetConnectors",
  "/cv.runtime.proto.CVRuntimeService/GetTiming",
  "/cv.runtime.proto.CVRuntimeService/Predict",
  "/cv.runtime.proto.CVRuntimeService/LoadAndQueue",
  "/cv.runtime.proto.CVRuntimeService/SetImage",
  "/cv.runtime.proto.CVRuntimeService/UnsetImage",
  "/cv.runtime.proto.CVRuntimeService/GetModelPaths",
  "/cv.runtime.proto.CVRuntimeService/SetGPSLocation",
  "/cv.runtime.proto.CVRuntimeService/SetImplementStatus",
  "/cv.runtime.proto.CVRuntimeService/SetImageScore",
  "/cv.runtime.proto.CVRuntimeService/GetScoreQueue",
  "/cv.runtime.proto.CVRuntimeService/ListScoreQueues",
  "/cv.runtime.proto.CVRuntimeService/GetMaxImageScore",
  "/cv.runtime.proto.CVRuntimeService/GetMaxScoredImage",
  "/cv.runtime.proto.CVRuntimeService/GetLatestP2PImage",
  "/cv.runtime.proto.CVRuntimeService/GetChipImage",
  "/cv.runtime.proto.CVRuntimeService/GetChipQueueInformation",
  "/cv.runtime.proto.CVRuntimeService/FlushQueues",
  "/cv.runtime.proto.CVRuntimeService/GetLatestImage",
  "/cv.runtime.proto.CVRuntimeService/GetImageNearTimestamp",
  "/cv.runtime.proto.CVRuntimeService/GetLightweightBurstRecord",
  "/cv.runtime.proto.CVRuntimeService/GetBooted",
  "/cv.runtime.proto.CVRuntimeService/GetReady",
  "/cv.runtime.proto.CVRuntimeService/GetDeepweedOutputByTimestamp",
  "/cv.runtime.proto.CVRuntimeService/GetRecommendedStrobeSettings",
  "/cv.runtime.proto.CVRuntimeService/P2PCapture",
  "/cv.runtime.proto.CVRuntimeService/SetAutoWhitebalance",
  "/cv.runtime.proto.CVRuntimeService/GetNextDeepweedOutput",
  "/cv.runtime.proto.CVRuntimeService/GetNextP2POutput",
  "/cv.runtime.proto.CVRuntimeService/SetTargetingState",
  "/cv.runtime.proto.CVRuntimeService/P2PBufferringBurstCapture",
  "/cv.runtime.proto.CVRuntimeService/GetNextFocusMetric",
  "/cv.runtime.proto.CVRuntimeService/RemoveDataDir",
  "/cv.runtime.proto.CVRuntimeService/GetLastNImages",
  "/cv.runtime.proto.CVRuntimeService/GetComputeCapabilities",
  "/cv.runtime.proto.CVRuntimeService/GetSupportedTensorRTVersions",
  "/cv.runtime.proto.CVRuntimeService/ReloadCategoryCollection",
  "/cv.runtime.proto.CVRuntimeService/GetCategoryCollection",
  "/cv.runtime.proto.CVRuntimeService/GetErrorState",
  "/cv.runtime.proto.CVRuntimeService/SnapshotPredictImages",
  "/cv.runtime.proto.CVRuntimeService/GetChipForPredictImage",
};

std::unique_ptr< CVRuntimeService::Stub> CVRuntimeService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< CVRuntimeService::Stub> stub(new CVRuntimeService::Stub(channel, options));
  return stub;
}

CVRuntimeService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_SetP2PContext_(CVRuntimeService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCameraDimensions_(CVRuntimeService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCameraInfo_(CVRuntimeService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDeepweedIndexToCategory_(CVRuntimeService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetDeepweedDetectionCriteria_(CVRuntimeService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDeepweedDetectionCriteria_(CVRuntimeService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDeepweedSupportedCategories_(CVRuntimeService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCameraTemperatures_(CVRuntimeService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetCameraSettings_(CVRuntimeService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCameraSettings_(CVRuntimeService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartBurstRecordFrames_(CVRuntimeService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopBurstRecordFrames_(CVRuntimeService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetConnectors_(CVRuntimeService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetConnectors_(CVRuntimeService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTiming_(CVRuntimeService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Predict_(CVRuntimeService_method_names[15], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LoadAndQueue_(CVRuntimeService_method_names[16], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetImage_(CVRuntimeService_method_names[17], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UnsetImage_(CVRuntimeService_method_names[18], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetModelPaths_(CVRuntimeService_method_names[19], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetGPSLocation_(CVRuntimeService_method_names[20], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetImplementStatus_(CVRuntimeService_method_names[21], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetImageScore_(CVRuntimeService_method_names[22], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetScoreQueue_(CVRuntimeService_method_names[23], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ListScoreQueues_(CVRuntimeService_method_names[24], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetMaxImageScore_(CVRuntimeService_method_names[25], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetMaxScoredImage_(CVRuntimeService_method_names[26], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetLatestP2PImage_(CVRuntimeService_method_names[27], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetChipImage_(CVRuntimeService_method_names[28], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetChipQueueInformation_(CVRuntimeService_method_names[29], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_FlushQueues_(CVRuntimeService_method_names[30], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetLatestImage_(CVRuntimeService_method_names[31], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetImageNearTimestamp_(CVRuntimeService_method_names[32], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetLightweightBurstRecord_(CVRuntimeService_method_names[33], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetBooted_(CVRuntimeService_method_names[34], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetReady_(CVRuntimeService_method_names[35], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDeepweedOutputByTimestamp_(CVRuntimeService_method_names[36], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetRecommendedStrobeSettings_(CVRuntimeService_method_names[37], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_P2PCapture_(CVRuntimeService_method_names[38], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetAutoWhitebalance_(CVRuntimeService_method_names[39], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextDeepweedOutput_(CVRuntimeService_method_names[40], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextP2POutput_(CVRuntimeService_method_names[41], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetTargetingState_(CVRuntimeService_method_names[42], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_P2PBufferringBurstCapture_(CVRuntimeService_method_names[43], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextFocusMetric_(CVRuntimeService_method_names[44], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_RemoveDataDir_(CVRuntimeService_method_names[45], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetLastNImages_(CVRuntimeService_method_names[46], options.suffix_for_stats(),::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_GetComputeCapabilities_(CVRuntimeService_method_names[47], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSupportedTensorRTVersions_(CVRuntimeService_method_names[48], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReloadCategoryCollection_(CVRuntimeService_method_names[49], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCategoryCollection_(CVRuntimeService_method_names[50], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetErrorState_(CVRuntimeService_method_names[51], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SnapshotPredictImages_(CVRuntimeService_method_names[52], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetChipForPredictImage_(CVRuntimeService_method_names[53], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status CVRuntimeService::Stub::SetP2PContext(::grpc::ClientContext* context, const ::cv::runtime::proto::SetP2PContextRequest& request, ::cv::runtime::proto::SetP2PContextResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetP2PContextRequest, ::cv::runtime::proto::SetP2PContextResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetP2PContext_, context, request, response);
}

void CVRuntimeService::Stub::async::SetP2PContext(::grpc::ClientContext* context, const ::cv::runtime::proto::SetP2PContextRequest* request, ::cv::runtime::proto::SetP2PContextResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetP2PContextRequest, ::cv::runtime::proto::SetP2PContextResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetP2PContext_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetP2PContext(::grpc::ClientContext* context, const ::cv::runtime::proto::SetP2PContextRequest* request, ::cv::runtime::proto::SetP2PContextResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetP2PContext_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetP2PContextResponse>* CVRuntimeService::Stub::PrepareAsyncSetP2PContextRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetP2PContextRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetP2PContextResponse, ::cv::runtime::proto::SetP2PContextRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetP2PContext_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetP2PContextResponse>* CVRuntimeService::Stub::AsyncSetP2PContextRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetP2PContextRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetP2PContextRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetCameraDimensions(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraDimensionsRequest& request, ::cv::runtime::proto::GetCameraDimensionsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetCameraDimensionsRequest, ::cv::runtime::proto::GetCameraDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCameraDimensions_, context, request, response);
}

void CVRuntimeService::Stub::async::GetCameraDimensions(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraDimensionsRequest* request, ::cv::runtime::proto::GetCameraDimensionsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetCameraDimensionsRequest, ::cv::runtime::proto::GetCameraDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraDimensions_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetCameraDimensions(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraDimensionsRequest* request, ::cv::runtime::proto::GetCameraDimensionsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraDimensions_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCameraDimensionsResponse>* CVRuntimeService::Stub::PrepareAsyncGetCameraDimensionsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraDimensionsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetCameraDimensionsResponse, ::cv::runtime::proto::GetCameraDimensionsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCameraDimensions_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCameraDimensionsResponse>* CVRuntimeService::Stub::AsyncGetCameraDimensionsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraDimensionsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCameraDimensionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetCameraInfo(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraInfoRequest& request, ::cv::runtime::proto::GetCameraInfoResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetCameraInfoRequest, ::cv::runtime::proto::GetCameraInfoResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCameraInfo_, context, request, response);
}

void CVRuntimeService::Stub::async::GetCameraInfo(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraInfoRequest* request, ::cv::runtime::proto::GetCameraInfoResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetCameraInfoRequest, ::cv::runtime::proto::GetCameraInfoResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraInfo_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetCameraInfo(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraInfoRequest* request, ::cv::runtime::proto::GetCameraInfoResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraInfo_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCameraInfoResponse>* CVRuntimeService::Stub::PrepareAsyncGetCameraInfoRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraInfoRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetCameraInfoResponse, ::cv::runtime::proto::GetCameraInfoRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCameraInfo_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCameraInfoResponse>* CVRuntimeService::Stub::AsyncGetCameraInfoRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraInfoRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCameraInfoRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetDeepweedIndexToCategory(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest& request, ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest, ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDeepweedIndexToCategory_, context, request, response);
}

void CVRuntimeService::Stub::async::GetDeepweedIndexToCategory(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest* request, ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest, ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedIndexToCategory_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetDeepweedIndexToCategory(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest* request, ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedIndexToCategory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse>* CVRuntimeService::Stub::PrepareAsyncGetDeepweedIndexToCategoryRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse, ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDeepweedIndexToCategory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse>* CVRuntimeService::Stub::AsyncGetDeepweedIndexToCategoryRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDeepweedIndexToCategoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetDeepweedDetectionCriteria(::grpc::ClientContext* context, const ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest& request, ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest, ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetDeepweedDetectionCriteria_, context, request, response);
}

void CVRuntimeService::Stub::async::SetDeepweedDetectionCriteria(::grpc::ClientContext* context, const ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest* request, ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest, ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetDeepweedDetectionCriteria_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetDeepweedDetectionCriteria(::grpc::ClientContext* context, const ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest* request, ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetDeepweedDetectionCriteria_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse>* CVRuntimeService::Stub::PrepareAsyncSetDeepweedDetectionCriteriaRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse, ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetDeepweedDetectionCriteria_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse>* CVRuntimeService::Stub::AsyncSetDeepweedDetectionCriteriaRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetDeepweedDetectionCriteriaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetDeepweedDetectionCriteria(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest& request, ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest, ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDeepweedDetectionCriteria_, context, request, response);
}

void CVRuntimeService::Stub::async::GetDeepweedDetectionCriteria(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest* request, ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest, ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedDetectionCriteria_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetDeepweedDetectionCriteria(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest* request, ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedDetectionCriteria_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse>* CVRuntimeService::Stub::PrepareAsyncGetDeepweedDetectionCriteriaRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse, ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDeepweedDetectionCriteria_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse>* CVRuntimeService::Stub::AsyncGetDeepweedDetectionCriteriaRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDeepweedDetectionCriteriaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetDeepweedSupportedCategories(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest& request, ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest, ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDeepweedSupportedCategories_, context, request, response);
}

void CVRuntimeService::Stub::async::GetDeepweedSupportedCategories(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest* request, ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest, ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedSupportedCategories_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetDeepweedSupportedCategories(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest* request, ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedSupportedCategories_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse>* CVRuntimeService::Stub::PrepareAsyncGetDeepweedSupportedCategoriesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse, ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDeepweedSupportedCategories_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse>* CVRuntimeService::Stub::AsyncGetDeepweedSupportedCategoriesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDeepweedSupportedCategoriesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetCameraTemperatures(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraTemperaturesRequest& request, ::cv::runtime::proto::GetCameraTemperaturesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetCameraTemperaturesRequest, ::cv::runtime::proto::GetCameraTemperaturesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCameraTemperatures_, context, request, response);
}

void CVRuntimeService::Stub::async::GetCameraTemperatures(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraTemperaturesRequest* request, ::cv::runtime::proto::GetCameraTemperaturesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetCameraTemperaturesRequest, ::cv::runtime::proto::GetCameraTemperaturesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraTemperatures_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetCameraTemperatures(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraTemperaturesRequest* request, ::cv::runtime::proto::GetCameraTemperaturesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraTemperatures_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCameraTemperaturesResponse>* CVRuntimeService::Stub::PrepareAsyncGetCameraTemperaturesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraTemperaturesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetCameraTemperaturesResponse, ::cv::runtime::proto::GetCameraTemperaturesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCameraTemperatures_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCameraTemperaturesResponse>* CVRuntimeService::Stub::AsyncGetCameraTemperaturesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraTemperaturesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCameraTemperaturesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetCameraSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::SetCameraSettingsRequest& request, ::cv::runtime::proto::SetCameraSettingsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetCameraSettingsRequest, ::cv::runtime::proto::SetCameraSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetCameraSettings_, context, request, response);
}

void CVRuntimeService::Stub::async::SetCameraSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::SetCameraSettingsRequest* request, ::cv::runtime::proto::SetCameraSettingsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetCameraSettingsRequest, ::cv::runtime::proto::SetCameraSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCameraSettings_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetCameraSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::SetCameraSettingsRequest* request, ::cv::runtime::proto::SetCameraSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCameraSettings_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetCameraSettingsResponse>* CVRuntimeService::Stub::PrepareAsyncSetCameraSettingsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetCameraSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetCameraSettingsResponse, ::cv::runtime::proto::SetCameraSettingsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetCameraSettings_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetCameraSettingsResponse>* CVRuntimeService::Stub::AsyncSetCameraSettingsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetCameraSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetCameraSettingsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetCameraSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraSettingsRequest& request, ::cv::runtime::proto::GetCameraSettingsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetCameraSettingsRequest, ::cv::runtime::proto::GetCameraSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCameraSettings_, context, request, response);
}

void CVRuntimeService::Stub::async::GetCameraSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraSettingsRequest* request, ::cv::runtime::proto::GetCameraSettingsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetCameraSettingsRequest, ::cv::runtime::proto::GetCameraSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraSettings_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetCameraSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraSettingsRequest* request, ::cv::runtime::proto::GetCameraSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraSettings_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCameraSettingsResponse>* CVRuntimeService::Stub::PrepareAsyncGetCameraSettingsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetCameraSettingsResponse, ::cv::runtime::proto::GetCameraSettingsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCameraSettings_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCameraSettingsResponse>* CVRuntimeService::Stub::AsyncGetCameraSettingsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetCameraSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCameraSettingsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::StartBurstRecordFrames(::grpc::ClientContext* context, const ::cv::runtime::proto::StartBurstRecordFramesRequest& request, ::cv::runtime::proto::StartBurstRecordFramesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::StartBurstRecordFramesRequest, ::cv::runtime::proto::StartBurstRecordFramesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartBurstRecordFrames_, context, request, response);
}

void CVRuntimeService::Stub::async::StartBurstRecordFrames(::grpc::ClientContext* context, const ::cv::runtime::proto::StartBurstRecordFramesRequest* request, ::cv::runtime::proto::StartBurstRecordFramesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::StartBurstRecordFramesRequest, ::cv::runtime::proto::StartBurstRecordFramesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartBurstRecordFrames_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::StartBurstRecordFrames(::grpc::ClientContext* context, const ::cv::runtime::proto::StartBurstRecordFramesRequest* request, ::cv::runtime::proto::StartBurstRecordFramesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartBurstRecordFrames_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::StartBurstRecordFramesResponse>* CVRuntimeService::Stub::PrepareAsyncStartBurstRecordFramesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::StartBurstRecordFramesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::StartBurstRecordFramesResponse, ::cv::runtime::proto::StartBurstRecordFramesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartBurstRecordFrames_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::StartBurstRecordFramesResponse>* CVRuntimeService::Stub::AsyncStartBurstRecordFramesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::StartBurstRecordFramesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartBurstRecordFramesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::StopBurstRecordFrames(::grpc::ClientContext* context, const ::cv::runtime::proto::StopBurstRecordFramesRequest& request, ::cv::runtime::proto::StopBurstRecordFramesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::StopBurstRecordFramesRequest, ::cv::runtime::proto::StopBurstRecordFramesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopBurstRecordFrames_, context, request, response);
}

void CVRuntimeService::Stub::async::StopBurstRecordFrames(::grpc::ClientContext* context, const ::cv::runtime::proto::StopBurstRecordFramesRequest* request, ::cv::runtime::proto::StopBurstRecordFramesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::StopBurstRecordFramesRequest, ::cv::runtime::proto::StopBurstRecordFramesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopBurstRecordFrames_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::StopBurstRecordFrames(::grpc::ClientContext* context, const ::cv::runtime::proto::StopBurstRecordFramesRequest* request, ::cv::runtime::proto::StopBurstRecordFramesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopBurstRecordFrames_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::StopBurstRecordFramesResponse>* CVRuntimeService::Stub::PrepareAsyncStopBurstRecordFramesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::StopBurstRecordFramesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::StopBurstRecordFramesResponse, ::cv::runtime::proto::StopBurstRecordFramesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopBurstRecordFrames_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::StopBurstRecordFramesResponse>* CVRuntimeService::Stub::AsyncStopBurstRecordFramesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::StopBurstRecordFramesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopBurstRecordFramesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetConnectors(::grpc::ClientContext* context, const ::cv::runtime::proto::GetConnectorsRequest& request, ::cv::runtime::proto::GetConnectorsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetConnectorsRequest, ::cv::runtime::proto::GetConnectorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetConnectors_, context, request, response);
}

void CVRuntimeService::Stub::async::GetConnectors(::grpc::ClientContext* context, const ::cv::runtime::proto::GetConnectorsRequest* request, ::cv::runtime::proto::GetConnectorsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetConnectorsRequest, ::cv::runtime::proto::GetConnectorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConnectors_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetConnectors(::grpc::ClientContext* context, const ::cv::runtime::proto::GetConnectorsRequest* request, ::cv::runtime::proto::GetConnectorsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConnectors_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetConnectorsResponse>* CVRuntimeService::Stub::PrepareAsyncGetConnectorsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetConnectorsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetConnectorsResponse, ::cv::runtime::proto::GetConnectorsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetConnectors_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetConnectorsResponse>* CVRuntimeService::Stub::AsyncGetConnectorsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetConnectorsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetConnectorsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetConnectors(::grpc::ClientContext* context, const ::cv::runtime::proto::SetConnectorsRequest& request, ::cv::runtime::proto::SetConnectorsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetConnectorsRequest, ::cv::runtime::proto::SetConnectorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetConnectors_, context, request, response);
}

void CVRuntimeService::Stub::async::SetConnectors(::grpc::ClientContext* context, const ::cv::runtime::proto::SetConnectorsRequest* request, ::cv::runtime::proto::SetConnectorsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetConnectorsRequest, ::cv::runtime::proto::SetConnectorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetConnectors_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetConnectors(::grpc::ClientContext* context, const ::cv::runtime::proto::SetConnectorsRequest* request, ::cv::runtime::proto::SetConnectorsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetConnectors_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetConnectorsResponse>* CVRuntimeService::Stub::PrepareAsyncSetConnectorsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetConnectorsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetConnectorsResponse, ::cv::runtime::proto::SetConnectorsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetConnectors_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetConnectorsResponse>* CVRuntimeService::Stub::AsyncSetConnectorsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetConnectorsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetConnectorsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetTiming(::grpc::ClientContext* context, const ::cv::runtime::proto::GetTimingRequest& request, ::cv::runtime::proto::GetTimingResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetTimingRequest, ::cv::runtime::proto::GetTimingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTiming_, context, request, response);
}

void CVRuntimeService::Stub::async::GetTiming(::grpc::ClientContext* context, const ::cv::runtime::proto::GetTimingRequest* request, ::cv::runtime::proto::GetTimingResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetTimingRequest, ::cv::runtime::proto::GetTimingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTiming_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetTiming(::grpc::ClientContext* context, const ::cv::runtime::proto::GetTimingRequest* request, ::cv::runtime::proto::GetTimingResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTiming_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetTimingResponse>* CVRuntimeService::Stub::PrepareAsyncGetTimingRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetTimingRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetTimingResponse, ::cv::runtime::proto::GetTimingRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTiming_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetTimingResponse>* CVRuntimeService::Stub::AsyncGetTimingRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetTimingRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTimingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::Predict(::grpc::ClientContext* context, const ::cv::runtime::proto::PredictRequest& request, ::cv::runtime::proto::PredictResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::PredictRequest, ::cv::runtime::proto::PredictResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Predict_, context, request, response);
}

void CVRuntimeService::Stub::async::Predict(::grpc::ClientContext* context, const ::cv::runtime::proto::PredictRequest* request, ::cv::runtime::proto::PredictResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::PredictRequest, ::cv::runtime::proto::PredictResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Predict_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::Predict(::grpc::ClientContext* context, const ::cv::runtime::proto::PredictRequest* request, ::cv::runtime::proto::PredictResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Predict_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::PredictResponse>* CVRuntimeService::Stub::PrepareAsyncPredictRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::PredictRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::PredictResponse, ::cv::runtime::proto::PredictRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Predict_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::PredictResponse>* CVRuntimeService::Stub::AsyncPredictRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::PredictRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPredictRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::LoadAndQueue(::grpc::ClientContext* context, const ::cv::runtime::proto::LoadAndQueueRequest& request, ::cv::runtime::proto::LoadAndQueueResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::LoadAndQueueRequest, ::cv::runtime::proto::LoadAndQueueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LoadAndQueue_, context, request, response);
}

void CVRuntimeService::Stub::async::LoadAndQueue(::grpc::ClientContext* context, const ::cv::runtime::proto::LoadAndQueueRequest* request, ::cv::runtime::proto::LoadAndQueueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::LoadAndQueueRequest, ::cv::runtime::proto::LoadAndQueueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadAndQueue_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::LoadAndQueue(::grpc::ClientContext* context, const ::cv::runtime::proto::LoadAndQueueRequest* request, ::cv::runtime::proto::LoadAndQueueResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadAndQueue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::LoadAndQueueResponse>* CVRuntimeService::Stub::PrepareAsyncLoadAndQueueRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::LoadAndQueueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::LoadAndQueueResponse, ::cv::runtime::proto::LoadAndQueueRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LoadAndQueue_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::LoadAndQueueResponse>* CVRuntimeService::Stub::AsyncLoadAndQueueRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::LoadAndQueueRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLoadAndQueueRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetImage(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageRequest& request, ::cv::runtime::proto::SetImageResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetImageRequest, ::cv::runtime::proto::SetImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetImage_, context, request, response);
}

void CVRuntimeService::Stub::async::SetImage(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageRequest* request, ::cv::runtime::proto::SetImageResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetImageRequest, ::cv::runtime::proto::SetImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetImage_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetImage(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageRequest* request, ::cv::runtime::proto::SetImageResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetImageResponse>* CVRuntimeService::Stub::PrepareAsyncSetImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetImageResponse, ::cv::runtime::proto::SetImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetImageResponse>* CVRuntimeService::Stub::AsyncSetImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::UnsetImage(::grpc::ClientContext* context, const ::cv::runtime::proto::UnsetImageRequest& request, ::cv::runtime::proto::UnsetImageResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::UnsetImageRequest, ::cv::runtime::proto::UnsetImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UnsetImage_, context, request, response);
}

void CVRuntimeService::Stub::async::UnsetImage(::grpc::ClientContext* context, const ::cv::runtime::proto::UnsetImageRequest* request, ::cv::runtime::proto::UnsetImageResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::UnsetImageRequest, ::cv::runtime::proto::UnsetImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UnsetImage_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::UnsetImage(::grpc::ClientContext* context, const ::cv::runtime::proto::UnsetImageRequest* request, ::cv::runtime::proto::UnsetImageResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UnsetImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::UnsetImageResponse>* CVRuntimeService::Stub::PrepareAsyncUnsetImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::UnsetImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::UnsetImageResponse, ::cv::runtime::proto::UnsetImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UnsetImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::UnsetImageResponse>* CVRuntimeService::Stub::AsyncUnsetImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::UnsetImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUnsetImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetModelPaths(::grpc::ClientContext* context, const ::cv::runtime::proto::GetModelPathsRequest& request, ::cv::runtime::proto::GetModelPathsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetModelPathsRequest, ::cv::runtime::proto::GetModelPathsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetModelPaths_, context, request, response);
}

void CVRuntimeService::Stub::async::GetModelPaths(::grpc::ClientContext* context, const ::cv::runtime::proto::GetModelPathsRequest* request, ::cv::runtime::proto::GetModelPathsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetModelPathsRequest, ::cv::runtime::proto::GetModelPathsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModelPaths_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetModelPaths(::grpc::ClientContext* context, const ::cv::runtime::proto::GetModelPathsRequest* request, ::cv::runtime::proto::GetModelPathsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModelPaths_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetModelPathsResponse>* CVRuntimeService::Stub::PrepareAsyncGetModelPathsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetModelPathsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetModelPathsResponse, ::cv::runtime::proto::GetModelPathsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetModelPaths_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetModelPathsResponse>* CVRuntimeService::Stub::AsyncGetModelPathsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetModelPathsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetModelPathsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetGPSLocation(::grpc::ClientContext* context, const ::cv::runtime::proto::SetGPSLocationRequest& request, ::cv::runtime::proto::SetGPSLocationResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetGPSLocationRequest, ::cv::runtime::proto::SetGPSLocationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetGPSLocation_, context, request, response);
}

void CVRuntimeService::Stub::async::SetGPSLocation(::grpc::ClientContext* context, const ::cv::runtime::proto::SetGPSLocationRequest* request, ::cv::runtime::proto::SetGPSLocationResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetGPSLocationRequest, ::cv::runtime::proto::SetGPSLocationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetGPSLocation_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetGPSLocation(::grpc::ClientContext* context, const ::cv::runtime::proto::SetGPSLocationRequest* request, ::cv::runtime::proto::SetGPSLocationResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetGPSLocation_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetGPSLocationResponse>* CVRuntimeService::Stub::PrepareAsyncSetGPSLocationRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetGPSLocationRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetGPSLocationResponse, ::cv::runtime::proto::SetGPSLocationRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetGPSLocation_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetGPSLocationResponse>* CVRuntimeService::Stub::AsyncSetGPSLocationRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetGPSLocationRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetGPSLocationRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetImplementStatus(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImplementStatusRequest& request, ::cv::runtime::proto::SetImplementStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetImplementStatusRequest, ::cv::runtime::proto::SetImplementStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetImplementStatus_, context, request, response);
}

void CVRuntimeService::Stub::async::SetImplementStatus(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImplementStatusRequest* request, ::cv::runtime::proto::SetImplementStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetImplementStatusRequest, ::cv::runtime::proto::SetImplementStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetImplementStatus_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetImplementStatus(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImplementStatusRequest* request, ::cv::runtime::proto::SetImplementStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetImplementStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetImplementStatusResponse>* CVRuntimeService::Stub::PrepareAsyncSetImplementStatusRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImplementStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetImplementStatusResponse, ::cv::runtime::proto::SetImplementStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetImplementStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetImplementStatusResponse>* CVRuntimeService::Stub::AsyncSetImplementStatusRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImplementStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetImplementStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetImageScore(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageScoreRequest& request, ::cv::runtime::proto::SetImageScoreResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetImageScoreRequest, ::cv::runtime::proto::SetImageScoreResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetImageScore_, context, request, response);
}

void CVRuntimeService::Stub::async::SetImageScore(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageScoreRequest* request, ::cv::runtime::proto::SetImageScoreResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetImageScoreRequest, ::cv::runtime::proto::SetImageScoreResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetImageScore_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetImageScore(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageScoreRequest* request, ::cv::runtime::proto::SetImageScoreResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetImageScore_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetImageScoreResponse>* CVRuntimeService::Stub::PrepareAsyncSetImageScoreRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageScoreRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetImageScoreResponse, ::cv::runtime::proto::SetImageScoreRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetImageScore_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetImageScoreResponse>* CVRuntimeService::Stub::AsyncSetImageScoreRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetImageScoreRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetImageScoreRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetScoreQueue(::grpc::ClientContext* context, const ::cv::runtime::proto::GetScoreQueueRequest& request, ::cv::runtime::proto::GetScoreQueueResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetScoreQueueRequest, ::cv::runtime::proto::GetScoreQueueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetScoreQueue_, context, request, response);
}

void CVRuntimeService::Stub::async::GetScoreQueue(::grpc::ClientContext* context, const ::cv::runtime::proto::GetScoreQueueRequest* request, ::cv::runtime::proto::GetScoreQueueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetScoreQueueRequest, ::cv::runtime::proto::GetScoreQueueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetScoreQueue_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetScoreQueue(::grpc::ClientContext* context, const ::cv::runtime::proto::GetScoreQueueRequest* request, ::cv::runtime::proto::GetScoreQueueResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetScoreQueue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetScoreQueueResponse>* CVRuntimeService::Stub::PrepareAsyncGetScoreQueueRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetScoreQueueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetScoreQueueResponse, ::cv::runtime::proto::GetScoreQueueRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetScoreQueue_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetScoreQueueResponse>* CVRuntimeService::Stub::AsyncGetScoreQueueRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetScoreQueueRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetScoreQueueRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::ListScoreQueues(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::cv::runtime::proto::ListScoreQueuesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::ListScoreQueuesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ListScoreQueues_, context, request, response);
}

void CVRuntimeService::Stub::async::ListScoreQueues(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::ListScoreQueuesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::ListScoreQueuesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListScoreQueues_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::ListScoreQueues(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::ListScoreQueuesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListScoreQueues_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ListScoreQueuesResponse>* CVRuntimeService::Stub::PrepareAsyncListScoreQueuesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::ListScoreQueuesResponse, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ListScoreQueues_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ListScoreQueuesResponse>* CVRuntimeService::Stub::AsyncListScoreQueuesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncListScoreQueuesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetMaxImageScore(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxImageScoreRequest& request, ::cv::runtime::proto::GetMaxImageScoreResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetMaxImageScoreRequest, ::cv::runtime::proto::GetMaxImageScoreResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetMaxImageScore_, context, request, response);
}

void CVRuntimeService::Stub::async::GetMaxImageScore(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxImageScoreRequest* request, ::cv::runtime::proto::GetMaxImageScoreResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetMaxImageScoreRequest, ::cv::runtime::proto::GetMaxImageScoreResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetMaxImageScore_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetMaxImageScore(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxImageScoreRequest* request, ::cv::runtime::proto::GetMaxImageScoreResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetMaxImageScore_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetMaxImageScoreResponse>* CVRuntimeService::Stub::PrepareAsyncGetMaxImageScoreRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxImageScoreRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetMaxImageScoreResponse, ::cv::runtime::proto::GetMaxImageScoreRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetMaxImageScore_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetMaxImageScoreResponse>* CVRuntimeService::Stub::AsyncGetMaxImageScoreRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxImageScoreRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetMaxImageScoreRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetMaxScoredImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxScoredImageRequest& request, ::cv::runtime::proto::ImageAndMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetMaxScoredImageRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetMaxScoredImage_, context, request, response);
}

void CVRuntimeService::Stub::async::GetMaxScoredImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxScoredImageRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetMaxScoredImageRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetMaxScoredImage_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetMaxScoredImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxScoredImageRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetMaxScoredImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::PrepareAsyncGetMaxScoredImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxScoredImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::ImageAndMetadataResponse, ::cv::runtime::proto::GetMaxScoredImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetMaxScoredImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::AsyncGetMaxScoredImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetMaxScoredImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetMaxScoredImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetLatestP2PImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestP2PImageRequest& request, ::cv::runtime::proto::P2PImageAndMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetLatestP2PImageRequest, ::cv::runtime::proto::P2PImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetLatestP2PImage_, context, request, response);
}

void CVRuntimeService::Stub::async::GetLatestP2PImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestP2PImageRequest* request, ::cv::runtime::proto::P2PImageAndMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetLatestP2PImageRequest, ::cv::runtime::proto::P2PImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLatestP2PImage_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetLatestP2PImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestP2PImageRequest* request, ::cv::runtime::proto::P2PImageAndMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLatestP2PImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::P2PImageAndMetadataResponse>* CVRuntimeService::Stub::PrepareAsyncGetLatestP2PImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestP2PImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::P2PImageAndMetadataResponse, ::cv::runtime::proto::GetLatestP2PImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetLatestP2PImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::P2PImageAndMetadataResponse>* CVRuntimeService::Stub::AsyncGetLatestP2PImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestP2PImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetLatestP2PImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetChipImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipImageRequest& request, ::cv::runtime::proto::ChipImageAndMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetChipImageRequest, ::cv::runtime::proto::ChipImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetChipImage_, context, request, response);
}

void CVRuntimeService::Stub::async::GetChipImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipImageRequest* request, ::cv::runtime::proto::ChipImageAndMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetChipImageRequest, ::cv::runtime::proto::ChipImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipImage_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetChipImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipImageRequest* request, ::cv::runtime::proto::ChipImageAndMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ChipImageAndMetadataResponse>* CVRuntimeService::Stub::PrepareAsyncGetChipImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::ChipImageAndMetadataResponse, ::cv::runtime::proto::GetChipImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetChipImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ChipImageAndMetadataResponse>* CVRuntimeService::Stub::AsyncGetChipImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetChipImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetChipQueueInformation(::grpc::ClientContext* context, const ::cv::runtime::proto::ChipQueueInformationRequest& request, ::cv::runtime::proto::ChipQueueInformationResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::ChipQueueInformationRequest, ::cv::runtime::proto::ChipQueueInformationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetChipQueueInformation_, context, request, response);
}

void CVRuntimeService::Stub::async::GetChipQueueInformation(::grpc::ClientContext* context, const ::cv::runtime::proto::ChipQueueInformationRequest* request, ::cv::runtime::proto::ChipQueueInformationResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::ChipQueueInformationRequest, ::cv::runtime::proto::ChipQueueInformationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipQueueInformation_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetChipQueueInformation(::grpc::ClientContext* context, const ::cv::runtime::proto::ChipQueueInformationRequest* request, ::cv::runtime::proto::ChipQueueInformationResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipQueueInformation_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ChipQueueInformationResponse>* CVRuntimeService::Stub::PrepareAsyncGetChipQueueInformationRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::ChipQueueInformationRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::ChipQueueInformationResponse, ::cv::runtime::proto::ChipQueueInformationRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetChipQueueInformation_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ChipQueueInformationResponse>* CVRuntimeService::Stub::AsyncGetChipQueueInformationRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::ChipQueueInformationRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetChipQueueInformationRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::FlushQueues(::grpc::ClientContext* context, const ::cv::runtime::proto::FlushQueuesRequest& request, ::cv::runtime::proto::FlushQueuesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::FlushQueuesRequest, ::cv::runtime::proto::FlushQueuesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_FlushQueues_, context, request, response);
}

void CVRuntimeService::Stub::async::FlushQueues(::grpc::ClientContext* context, const ::cv::runtime::proto::FlushQueuesRequest* request, ::cv::runtime::proto::FlushQueuesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::FlushQueuesRequest, ::cv::runtime::proto::FlushQueuesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FlushQueues_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::FlushQueues(::grpc::ClientContext* context, const ::cv::runtime::proto::FlushQueuesRequest* request, ::cv::runtime::proto::FlushQueuesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FlushQueues_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::FlushQueuesResponse>* CVRuntimeService::Stub::PrepareAsyncFlushQueuesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::FlushQueuesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::FlushQueuesResponse, ::cv::runtime::proto::FlushQueuesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_FlushQueues_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::FlushQueuesResponse>* CVRuntimeService::Stub::AsyncFlushQueuesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::FlushQueuesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncFlushQueuesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetLatestImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestImageRequest& request, ::cv::runtime::proto::ImageAndMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetLatestImageRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetLatestImage_, context, request, response);
}

void CVRuntimeService::Stub::async::GetLatestImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestImageRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetLatestImageRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLatestImage_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetLatestImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestImageRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLatestImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::PrepareAsyncGetLatestImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::ImageAndMetadataResponse, ::cv::runtime::proto::GetLatestImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetLatestImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::AsyncGetLatestImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLatestImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetLatestImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetImageNearTimestamp(::grpc::ClientContext* context, const ::cv::runtime::proto::GetImageNearTimestampRequest& request, ::cv::runtime::proto::ImageAndMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetImageNearTimestampRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetImageNearTimestamp_, context, request, response);
}

void CVRuntimeService::Stub::async::GetImageNearTimestamp(::grpc::ClientContext* context, const ::cv::runtime::proto::GetImageNearTimestampRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetImageNearTimestampRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetImageNearTimestamp_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetImageNearTimestamp(::grpc::ClientContext* context, const ::cv::runtime::proto::GetImageNearTimestampRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetImageNearTimestamp_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::PrepareAsyncGetImageNearTimestampRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetImageNearTimestampRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::ImageAndMetadataResponse, ::cv::runtime::proto::GetImageNearTimestampRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetImageNearTimestamp_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::AsyncGetImageNearTimestampRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetImageNearTimestampRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetImageNearTimestampRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetLightweightBurstRecord(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLightweightBurstRecordRequest& request, ::cv::runtime::proto::GetLightweightBurstRecordResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetLightweightBurstRecordRequest, ::cv::runtime::proto::GetLightweightBurstRecordResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetLightweightBurstRecord_, context, request, response);
}

void CVRuntimeService::Stub::async::GetLightweightBurstRecord(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLightweightBurstRecordRequest* request, ::cv::runtime::proto::GetLightweightBurstRecordResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetLightweightBurstRecordRequest, ::cv::runtime::proto::GetLightweightBurstRecordResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLightweightBurstRecord_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetLightweightBurstRecord(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLightweightBurstRecordRequest* request, ::cv::runtime::proto::GetLightweightBurstRecordResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLightweightBurstRecord_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetLightweightBurstRecordResponse>* CVRuntimeService::Stub::PrepareAsyncGetLightweightBurstRecordRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLightweightBurstRecordRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetLightweightBurstRecordResponse, ::cv::runtime::proto::GetLightweightBurstRecordRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetLightweightBurstRecord_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetLightweightBurstRecordResponse>* CVRuntimeService::Stub::AsyncGetLightweightBurstRecordRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetLightweightBurstRecordRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetLightweightBurstRecordRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetBooted(::grpc::ClientContext* context, const ::cv::runtime::proto::GetBootedRequest& request, ::cv::runtime::proto::GetBootedResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetBootedRequest, ::cv::runtime::proto::GetBootedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetBooted_, context, request, response);
}

void CVRuntimeService::Stub::async::GetBooted(::grpc::ClientContext* context, const ::cv::runtime::proto::GetBootedRequest* request, ::cv::runtime::proto::GetBootedResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetBootedRequest, ::cv::runtime::proto::GetBootedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBooted_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetBooted(::grpc::ClientContext* context, const ::cv::runtime::proto::GetBootedRequest* request, ::cv::runtime::proto::GetBootedResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBooted_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetBootedResponse>* CVRuntimeService::Stub::PrepareAsyncGetBootedRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetBootedRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetBootedResponse, ::cv::runtime::proto::GetBootedRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetBooted_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetBootedResponse>* CVRuntimeService::Stub::AsyncGetBootedRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetBootedRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetBootedRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetReady(::grpc::ClientContext* context, const ::cv::runtime::proto::GetReadyRequest& request, ::cv::runtime::proto::GetReadyResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetReadyRequest, ::cv::runtime::proto::GetReadyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetReady_, context, request, response);
}

void CVRuntimeService::Stub::async::GetReady(::grpc::ClientContext* context, const ::cv::runtime::proto::GetReadyRequest* request, ::cv::runtime::proto::GetReadyResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetReadyRequest, ::cv::runtime::proto::GetReadyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReady_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetReady(::grpc::ClientContext* context, const ::cv::runtime::proto::GetReadyRequest* request, ::cv::runtime::proto::GetReadyResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReady_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetReadyResponse>* CVRuntimeService::Stub::PrepareAsyncGetReadyRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetReadyRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetReadyResponse, ::cv::runtime::proto::GetReadyRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetReady_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetReadyResponse>* CVRuntimeService::Stub::AsyncGetReadyRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetReadyRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetReadyRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetDeepweedOutputByTimestamp(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest& request, ::cv::runtime::proto::DeepweedOutput* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest, ::cv::runtime::proto::DeepweedOutput, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDeepweedOutputByTimestamp_, context, request, response);
}

void CVRuntimeService::Stub::async::GetDeepweedOutputByTimestamp(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest* request, ::cv::runtime::proto::DeepweedOutput* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest, ::cv::runtime::proto::DeepweedOutput, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedOutputByTimestamp_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetDeepweedOutputByTimestamp(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest* request, ::cv::runtime::proto::DeepweedOutput* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedOutputByTimestamp_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::DeepweedOutput>* CVRuntimeService::Stub::PrepareAsyncGetDeepweedOutputByTimestampRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::DeepweedOutput, ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDeepweedOutputByTimestamp_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::DeepweedOutput>* CVRuntimeService::Stub::AsyncGetDeepweedOutputByTimestampRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDeepweedOutputByTimestampRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetRecommendedStrobeSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest& request, ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest, ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRecommendedStrobeSettings_, context, request, response);
}

void CVRuntimeService::Stub::async::GetRecommendedStrobeSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest* request, ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest, ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRecommendedStrobeSettings_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetRecommendedStrobeSettings(::grpc::ClientContext* context, const ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest* request, ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRecommendedStrobeSettings_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse>* CVRuntimeService::Stub::PrepareAsyncGetRecommendedStrobeSettingsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse, ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRecommendedStrobeSettings_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse>* CVRuntimeService::Stub::AsyncGetRecommendedStrobeSettingsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRecommendedStrobeSettingsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::P2PCapture(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PCaptureRequest& request, ::cv::runtime::proto::P2PCaptureResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::P2PCaptureRequest, ::cv::runtime::proto::P2PCaptureResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_P2PCapture_, context, request, response);
}

void CVRuntimeService::Stub::async::P2PCapture(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PCaptureRequest* request, ::cv::runtime::proto::P2PCaptureResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::P2PCaptureRequest, ::cv::runtime::proto::P2PCaptureResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_P2PCapture_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::P2PCapture(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PCaptureRequest* request, ::cv::runtime::proto::P2PCaptureResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_P2PCapture_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::P2PCaptureResponse>* CVRuntimeService::Stub::PrepareAsyncP2PCaptureRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PCaptureRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::P2PCaptureResponse, ::cv::runtime::proto::P2PCaptureRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_P2PCapture_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::P2PCaptureResponse>* CVRuntimeService::Stub::AsyncP2PCaptureRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PCaptureRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncP2PCaptureRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetAutoWhitebalance(::grpc::ClientContext* context, const ::cv::runtime::proto::SetAutoWhitebalanceRequest& request, ::cv::runtime::proto::SetAutoWhitebalanceResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetAutoWhitebalanceRequest, ::cv::runtime::proto::SetAutoWhitebalanceResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetAutoWhitebalance_, context, request, response);
}

void CVRuntimeService::Stub::async::SetAutoWhitebalance(::grpc::ClientContext* context, const ::cv::runtime::proto::SetAutoWhitebalanceRequest* request, ::cv::runtime::proto::SetAutoWhitebalanceResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetAutoWhitebalanceRequest, ::cv::runtime::proto::SetAutoWhitebalanceResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetAutoWhitebalance_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetAutoWhitebalance(::grpc::ClientContext* context, const ::cv::runtime::proto::SetAutoWhitebalanceRequest* request, ::cv::runtime::proto::SetAutoWhitebalanceResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetAutoWhitebalance_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetAutoWhitebalanceResponse>* CVRuntimeService::Stub::PrepareAsyncSetAutoWhitebalanceRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetAutoWhitebalanceRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetAutoWhitebalanceResponse, ::cv::runtime::proto::SetAutoWhitebalanceRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetAutoWhitebalance_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetAutoWhitebalanceResponse>* CVRuntimeService::Stub::AsyncSetAutoWhitebalanceRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetAutoWhitebalanceRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetAutoWhitebalanceRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetNextDeepweedOutput(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextDeepweedOutputRequest& request, ::cv::runtime::proto::DeepweedOutput* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetNextDeepweedOutputRequest, ::cv::runtime::proto::DeepweedOutput, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextDeepweedOutput_, context, request, response);
}

void CVRuntimeService::Stub::async::GetNextDeepweedOutput(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextDeepweedOutputRequest* request, ::cv::runtime::proto::DeepweedOutput* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetNextDeepweedOutputRequest, ::cv::runtime::proto::DeepweedOutput, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDeepweedOutput_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetNextDeepweedOutput(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextDeepweedOutputRequest* request, ::cv::runtime::proto::DeepweedOutput* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDeepweedOutput_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::DeepweedOutput>* CVRuntimeService::Stub::PrepareAsyncGetNextDeepweedOutputRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextDeepweedOutputRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::DeepweedOutput, ::cv::runtime::proto::GetNextDeepweedOutputRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextDeepweedOutput_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::DeepweedOutput>* CVRuntimeService::Stub::AsyncGetNextDeepweedOutputRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextDeepweedOutputRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextDeepweedOutputRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetNextP2POutput(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextP2POutputRequest& request, ::cv::runtime::proto::P2POutputProto* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetNextP2POutputRequest, ::cv::runtime::proto::P2POutputProto, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextP2POutput_, context, request, response);
}

void CVRuntimeService::Stub::async::GetNextP2POutput(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextP2POutputRequest* request, ::cv::runtime::proto::P2POutputProto* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetNextP2POutputRequest, ::cv::runtime::proto::P2POutputProto, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextP2POutput_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetNextP2POutput(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextP2POutputRequest* request, ::cv::runtime::proto::P2POutputProto* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextP2POutput_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::P2POutputProto>* CVRuntimeService::Stub::PrepareAsyncGetNextP2POutputRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextP2POutputRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::P2POutputProto, ::cv::runtime::proto::GetNextP2POutputRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextP2POutput_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::P2POutputProto>* CVRuntimeService::Stub::AsyncGetNextP2POutputRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextP2POutputRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextP2POutputRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SetTargetingState(::grpc::ClientContext* context, const ::cv::runtime::proto::SetTargetingStateRequest& request, ::cv::runtime::proto::SetTargetingStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SetTargetingStateRequest, ::cv::runtime::proto::SetTargetingStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetTargetingState_, context, request, response);
}

void CVRuntimeService::Stub::async::SetTargetingState(::grpc::ClientContext* context, const ::cv::runtime::proto::SetTargetingStateRequest* request, ::cv::runtime::proto::SetTargetingStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SetTargetingStateRequest, ::cv::runtime::proto::SetTargetingStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTargetingState_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SetTargetingState(::grpc::ClientContext* context, const ::cv::runtime::proto::SetTargetingStateRequest* request, ::cv::runtime::proto::SetTargetingStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTargetingState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetTargetingStateResponse>* CVRuntimeService::Stub::PrepareAsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetTargetingStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SetTargetingStateResponse, ::cv::runtime::proto::SetTargetingStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetTargetingState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SetTargetingStateResponse>* CVRuntimeService::Stub::AsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SetTargetingStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetTargetingStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::P2PBufferringBurstCapture(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PBufferringBurstCaptureRequest& request, ::cv::runtime::proto::P2PBufferringBurstCaptureResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::P2PBufferringBurstCaptureRequest, ::cv::runtime::proto::P2PBufferringBurstCaptureResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_P2PBufferringBurstCapture_, context, request, response);
}

void CVRuntimeService::Stub::async::P2PBufferringBurstCapture(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PBufferringBurstCaptureRequest* request, ::cv::runtime::proto::P2PBufferringBurstCaptureResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::P2PBufferringBurstCaptureRequest, ::cv::runtime::proto::P2PBufferringBurstCaptureResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_P2PBufferringBurstCapture_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::P2PBufferringBurstCapture(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PBufferringBurstCaptureRequest* request, ::cv::runtime::proto::P2PBufferringBurstCaptureResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_P2PBufferringBurstCapture_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::P2PBufferringBurstCaptureResponse>* CVRuntimeService::Stub::PrepareAsyncP2PBufferringBurstCaptureRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PBufferringBurstCaptureRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::P2PBufferringBurstCaptureResponse, ::cv::runtime::proto::P2PBufferringBurstCaptureRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_P2PBufferringBurstCapture_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::P2PBufferringBurstCaptureResponse>* CVRuntimeService::Stub::AsyncP2PBufferringBurstCaptureRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::P2PBufferringBurstCaptureRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncP2PBufferringBurstCaptureRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetNextFocusMetric(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextFocusMetricRequest& request, ::cv::runtime::proto::GetNextFocusMetricResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetNextFocusMetricRequest, ::cv::runtime::proto::GetNextFocusMetricResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextFocusMetric_, context, request, response);
}

void CVRuntimeService::Stub::async::GetNextFocusMetric(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextFocusMetricRequest* request, ::cv::runtime::proto::GetNextFocusMetricResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetNextFocusMetricRequest, ::cv::runtime::proto::GetNextFocusMetricResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextFocusMetric_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetNextFocusMetric(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextFocusMetricRequest* request, ::cv::runtime::proto::GetNextFocusMetricResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextFocusMetric_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetNextFocusMetricResponse>* CVRuntimeService::Stub::PrepareAsyncGetNextFocusMetricRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextFocusMetricRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetNextFocusMetricResponse, ::cv::runtime::proto::GetNextFocusMetricRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextFocusMetric_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetNextFocusMetricResponse>* CVRuntimeService::Stub::AsyncGetNextFocusMetricRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetNextFocusMetricRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextFocusMetricRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::RemoveDataDir(::grpc::ClientContext* context, const ::cv::runtime::proto::RemoveDataDirRequest& request, ::cv::runtime::proto::RemoveDataDirResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::RemoveDataDirRequest, ::cv::runtime::proto::RemoveDataDirResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_RemoveDataDir_, context, request, response);
}

void CVRuntimeService::Stub::async::RemoveDataDir(::grpc::ClientContext* context, const ::cv::runtime::proto::RemoveDataDirRequest* request, ::cv::runtime::proto::RemoveDataDirResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::RemoveDataDirRequest, ::cv::runtime::proto::RemoveDataDirResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RemoveDataDir_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::RemoveDataDir(::grpc::ClientContext* context, const ::cv::runtime::proto::RemoveDataDirRequest* request, ::cv::runtime::proto::RemoveDataDirResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RemoveDataDir_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::RemoveDataDirResponse>* CVRuntimeService::Stub::PrepareAsyncRemoveDataDirRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::RemoveDataDirRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::RemoveDataDirResponse, ::cv::runtime::proto::RemoveDataDirRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_RemoveDataDir_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::RemoveDataDirResponse>* CVRuntimeService::Stub::AsyncRemoveDataDirRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::RemoveDataDirRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncRemoveDataDirRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::ClientReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::GetLastNImagesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::LastNImageRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::cv::runtime::proto::ImageAndMetadataResponse>::Create(channel_.get(), rpcmethod_GetLastNImages_, context, request);
}

void CVRuntimeService::Stub::async::GetLastNImages(::grpc::ClientContext* context, const ::cv::runtime::proto::LastNImageRequest* request, ::grpc::ClientReadReactor< ::cv::runtime::proto::ImageAndMetadataResponse>* reactor) {
  ::grpc::internal::ClientCallbackReaderFactory< ::cv::runtime::proto::ImageAndMetadataResponse>::Create(stub_->channel_.get(), stub_->rpcmethod_GetLastNImages_, context, request, reactor);
}

::grpc::ClientAsyncReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::AsyncGetLastNImagesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::LastNImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::cv::runtime::proto::ImageAndMetadataResponse>::Create(channel_.get(), cq, rpcmethod_GetLastNImages_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::cv::runtime::proto::ImageAndMetadataResponse>* CVRuntimeService::Stub::PrepareAsyncGetLastNImagesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::LastNImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::cv::runtime::proto::ImageAndMetadataResponse>::Create(channel_.get(), cq, rpcmethod_GetLastNImages_, context, request, false, nullptr);
}

::grpc::Status CVRuntimeService::Stub::GetComputeCapabilities(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::cv::runtime::proto::ComputeCapabilitiesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::ComputeCapabilitiesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetComputeCapabilities_, context, request, response);
}

void CVRuntimeService::Stub::async::GetComputeCapabilities(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::ComputeCapabilitiesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::ComputeCapabilitiesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetComputeCapabilities_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetComputeCapabilities(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::ComputeCapabilitiesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetComputeCapabilities_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ComputeCapabilitiesResponse>* CVRuntimeService::Stub::PrepareAsyncGetComputeCapabilitiesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::ComputeCapabilitiesResponse, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetComputeCapabilities_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::ComputeCapabilitiesResponse>* CVRuntimeService::Stub::AsyncGetComputeCapabilitiesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetComputeCapabilitiesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetSupportedTensorRTVersions(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::cv::runtime::proto::SupportedTensorRTVersionsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::SupportedTensorRTVersionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSupportedTensorRTVersions_, context, request, response);
}

void CVRuntimeService::Stub::async::GetSupportedTensorRTVersions(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::SupportedTensorRTVersionsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::SupportedTensorRTVersionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSupportedTensorRTVersions_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetSupportedTensorRTVersions(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::SupportedTensorRTVersionsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSupportedTensorRTVersions_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SupportedTensorRTVersionsResponse>* CVRuntimeService::Stub::PrepareAsyncGetSupportedTensorRTVersionsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SupportedTensorRTVersionsResponse, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSupportedTensorRTVersions_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SupportedTensorRTVersionsResponse>* CVRuntimeService::Stub::AsyncGetSupportedTensorRTVersionsRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSupportedTensorRTVersionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::ReloadCategoryCollection(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::cv::runtime::proto::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReloadCategoryCollection_, context, request, response);
}

void CVRuntimeService::Stub::async::ReloadCategoryCollection(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadCategoryCollection_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::ReloadCategoryCollection(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadCategoryCollection_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::Empty>* CVRuntimeService::Stub::PrepareAsyncReloadCategoryCollectionRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::Empty, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReloadCategoryCollection_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::Empty>* CVRuntimeService::Stub::AsyncReloadCategoryCollectionRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReloadCategoryCollectionRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetCategoryCollection(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::cv::runtime::proto::GetCategoryCollectionResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::GetCategoryCollectionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCategoryCollection_, context, request, response);
}

void CVRuntimeService::Stub::async::GetCategoryCollection(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::GetCategoryCollectionResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::GetCategoryCollectionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCategoryCollection_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetCategoryCollection(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::GetCategoryCollectionResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCategoryCollection_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCategoryCollectionResponse>* CVRuntimeService::Stub::PrepareAsyncGetCategoryCollectionRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetCategoryCollectionResponse, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCategoryCollection_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetCategoryCollectionResponse>* CVRuntimeService::Stub::AsyncGetCategoryCollectionRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCategoryCollectionRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetErrorState(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::cv::runtime::proto::GetErrorStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::GetErrorStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetErrorState_, context, request, response);
}

void CVRuntimeService::Stub::async::GetErrorState(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::GetErrorStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::Empty, ::cv::runtime::proto::GetErrorStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetErrorState_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetErrorState(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::GetErrorStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetErrorState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetErrorStateResponse>* CVRuntimeService::Stub::PrepareAsyncGetErrorStateRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetErrorStateResponse, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetErrorState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetErrorStateResponse>* CVRuntimeService::Stub::AsyncGetErrorStateRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetErrorStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::SnapshotPredictImages(::grpc::ClientContext* context, const ::cv::runtime::proto::SnapshotPredictImagesRequest& request, ::cv::runtime::proto::SnapshotPredictImagesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::SnapshotPredictImagesRequest, ::cv::runtime::proto::SnapshotPredictImagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SnapshotPredictImages_, context, request, response);
}

void CVRuntimeService::Stub::async::SnapshotPredictImages(::grpc::ClientContext* context, const ::cv::runtime::proto::SnapshotPredictImagesRequest* request, ::cv::runtime::proto::SnapshotPredictImagesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::SnapshotPredictImagesRequest, ::cv::runtime::proto::SnapshotPredictImagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SnapshotPredictImages_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::SnapshotPredictImages(::grpc::ClientContext* context, const ::cv::runtime::proto::SnapshotPredictImagesRequest* request, ::cv::runtime::proto::SnapshotPredictImagesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SnapshotPredictImages_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SnapshotPredictImagesResponse>* CVRuntimeService::Stub::PrepareAsyncSnapshotPredictImagesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::SnapshotPredictImagesResponse, ::cv::runtime::proto::SnapshotPredictImagesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SnapshotPredictImages_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::SnapshotPredictImagesResponse>* CVRuntimeService::Stub::AsyncSnapshotPredictImagesRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSnapshotPredictImagesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CVRuntimeService::Stub::GetChipForPredictImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipForPredictImageRequest& request, ::cv::runtime::proto::GetChipForPredictImageResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::runtime::proto::GetChipForPredictImageRequest, ::cv::runtime::proto::GetChipForPredictImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetChipForPredictImage_, context, request, response);
}

void CVRuntimeService::Stub::async::GetChipForPredictImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipForPredictImageRequest* request, ::cv::runtime::proto::GetChipForPredictImageResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::runtime::proto::GetChipForPredictImageRequest, ::cv::runtime::proto::GetChipForPredictImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipForPredictImage_, context, request, response, std::move(f));
}

void CVRuntimeService::Stub::async::GetChipForPredictImage(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipForPredictImageRequest* request, ::cv::runtime::proto::GetChipForPredictImageResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipForPredictImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetChipForPredictImageResponse>* CVRuntimeService::Stub::PrepareAsyncGetChipForPredictImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::runtime::proto::GetChipForPredictImageResponse, ::cv::runtime::proto::GetChipForPredictImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetChipForPredictImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::runtime::proto::GetChipForPredictImageResponse>* CVRuntimeService::Stub::AsyncGetChipForPredictImageRaw(::grpc::ClientContext* context, const ::cv::runtime::proto::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetChipForPredictImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

CVRuntimeService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetP2PContextRequest, ::cv::runtime::proto::SetP2PContextResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetP2PContextRequest* req,
             ::cv::runtime::proto::SetP2PContextResponse* resp) {
               return service->SetP2PContext(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetCameraDimensionsRequest, ::cv::runtime::proto::GetCameraDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetCameraDimensionsRequest* req,
             ::cv::runtime::proto::GetCameraDimensionsResponse* resp) {
               return service->GetCameraDimensions(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetCameraInfoRequest, ::cv::runtime::proto::GetCameraInfoResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetCameraInfoRequest* req,
             ::cv::runtime::proto::GetCameraInfoResponse* resp) {
               return service->GetCameraInfo(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest, ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest* req,
             ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse* resp) {
               return service->GetDeepweedIndexToCategory(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest, ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest* req,
             ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse* resp) {
               return service->SetDeepweedDetectionCriteria(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest, ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest* req,
             ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse* resp) {
               return service->GetDeepweedDetectionCriteria(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest, ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest* req,
             ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse* resp) {
               return service->GetDeepweedSupportedCategories(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetCameraTemperaturesRequest, ::cv::runtime::proto::GetCameraTemperaturesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetCameraTemperaturesRequest* req,
             ::cv::runtime::proto::GetCameraTemperaturesResponse* resp) {
               return service->GetCameraTemperatures(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetCameraSettingsRequest, ::cv::runtime::proto::SetCameraSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetCameraSettingsRequest* req,
             ::cv::runtime::proto::SetCameraSettingsResponse* resp) {
               return service->SetCameraSettings(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetCameraSettingsRequest, ::cv::runtime::proto::GetCameraSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetCameraSettingsRequest* req,
             ::cv::runtime::proto::GetCameraSettingsResponse* resp) {
               return service->GetCameraSettings(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::StartBurstRecordFramesRequest, ::cv::runtime::proto::StartBurstRecordFramesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::StartBurstRecordFramesRequest* req,
             ::cv::runtime::proto::StartBurstRecordFramesResponse* resp) {
               return service->StartBurstRecordFrames(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::StopBurstRecordFramesRequest, ::cv::runtime::proto::StopBurstRecordFramesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::StopBurstRecordFramesRequest* req,
             ::cv::runtime::proto::StopBurstRecordFramesResponse* resp) {
               return service->StopBurstRecordFrames(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetConnectorsRequest, ::cv::runtime::proto::GetConnectorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetConnectorsRequest* req,
             ::cv::runtime::proto::GetConnectorsResponse* resp) {
               return service->GetConnectors(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetConnectorsRequest, ::cv::runtime::proto::SetConnectorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetConnectorsRequest* req,
             ::cv::runtime::proto::SetConnectorsResponse* resp) {
               return service->SetConnectors(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetTimingRequest, ::cv::runtime::proto::GetTimingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetTimingRequest* req,
             ::cv::runtime::proto::GetTimingResponse* resp) {
               return service->GetTiming(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::PredictRequest, ::cv::runtime::proto::PredictResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::PredictRequest* req,
             ::cv::runtime::proto::PredictResponse* resp) {
               return service->Predict(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[16],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::LoadAndQueueRequest, ::cv::runtime::proto::LoadAndQueueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::LoadAndQueueRequest* req,
             ::cv::runtime::proto::LoadAndQueueResponse* resp) {
               return service->LoadAndQueue(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[17],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetImageRequest, ::cv::runtime::proto::SetImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetImageRequest* req,
             ::cv::runtime::proto::SetImageResponse* resp) {
               return service->SetImage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[18],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::UnsetImageRequest, ::cv::runtime::proto::UnsetImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::UnsetImageRequest* req,
             ::cv::runtime::proto::UnsetImageResponse* resp) {
               return service->UnsetImage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[19],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetModelPathsRequest, ::cv::runtime::proto::GetModelPathsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetModelPathsRequest* req,
             ::cv::runtime::proto::GetModelPathsResponse* resp) {
               return service->GetModelPaths(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[20],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetGPSLocationRequest, ::cv::runtime::proto::SetGPSLocationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetGPSLocationRequest* req,
             ::cv::runtime::proto::SetGPSLocationResponse* resp) {
               return service->SetGPSLocation(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[21],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetImplementStatusRequest, ::cv::runtime::proto::SetImplementStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetImplementStatusRequest* req,
             ::cv::runtime::proto::SetImplementStatusResponse* resp) {
               return service->SetImplementStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[22],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetImageScoreRequest, ::cv::runtime::proto::SetImageScoreResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetImageScoreRequest* req,
             ::cv::runtime::proto::SetImageScoreResponse* resp) {
               return service->SetImageScore(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[23],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetScoreQueueRequest, ::cv::runtime::proto::GetScoreQueueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetScoreQueueRequest* req,
             ::cv::runtime::proto::GetScoreQueueResponse* resp) {
               return service->GetScoreQueue(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[24],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::Empty, ::cv::runtime::proto::ListScoreQueuesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::Empty* req,
             ::cv::runtime::proto::ListScoreQueuesResponse* resp) {
               return service->ListScoreQueues(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[25],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetMaxImageScoreRequest, ::cv::runtime::proto::GetMaxImageScoreResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetMaxImageScoreRequest* req,
             ::cv::runtime::proto::GetMaxImageScoreResponse* resp) {
               return service->GetMaxImageScore(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[26],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetMaxScoredImageRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetMaxScoredImageRequest* req,
             ::cv::runtime::proto::ImageAndMetadataResponse* resp) {
               return service->GetMaxScoredImage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[27],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetLatestP2PImageRequest, ::cv::runtime::proto::P2PImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetLatestP2PImageRequest* req,
             ::cv::runtime::proto::P2PImageAndMetadataResponse* resp) {
               return service->GetLatestP2PImage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[28],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetChipImageRequest, ::cv::runtime::proto::ChipImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetChipImageRequest* req,
             ::cv::runtime::proto::ChipImageAndMetadataResponse* resp) {
               return service->GetChipImage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[29],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::ChipQueueInformationRequest, ::cv::runtime::proto::ChipQueueInformationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::ChipQueueInformationRequest* req,
             ::cv::runtime::proto::ChipQueueInformationResponse* resp) {
               return service->GetChipQueueInformation(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[30],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::FlushQueuesRequest, ::cv::runtime::proto::FlushQueuesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::FlushQueuesRequest* req,
             ::cv::runtime::proto::FlushQueuesResponse* resp) {
               return service->FlushQueues(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[31],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetLatestImageRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetLatestImageRequest* req,
             ::cv::runtime::proto::ImageAndMetadataResponse* resp) {
               return service->GetLatestImage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[32],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetImageNearTimestampRequest, ::cv::runtime::proto::ImageAndMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetImageNearTimestampRequest* req,
             ::cv::runtime::proto::ImageAndMetadataResponse* resp) {
               return service->GetImageNearTimestamp(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[33],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetLightweightBurstRecordRequest, ::cv::runtime::proto::GetLightweightBurstRecordResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetLightweightBurstRecordRequest* req,
             ::cv::runtime::proto::GetLightweightBurstRecordResponse* resp) {
               return service->GetLightweightBurstRecord(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[34],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetBootedRequest, ::cv::runtime::proto::GetBootedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetBootedRequest* req,
             ::cv::runtime::proto::GetBootedResponse* resp) {
               return service->GetBooted(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[35],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetReadyRequest, ::cv::runtime::proto::GetReadyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetReadyRequest* req,
             ::cv::runtime::proto::GetReadyResponse* resp) {
               return service->GetReady(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[36],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest, ::cv::runtime::proto::DeepweedOutput, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest* req,
             ::cv::runtime::proto::DeepweedOutput* resp) {
               return service->GetDeepweedOutputByTimestamp(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[37],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest, ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest* req,
             ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse* resp) {
               return service->GetRecommendedStrobeSettings(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[38],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::P2PCaptureRequest, ::cv::runtime::proto::P2PCaptureResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::P2PCaptureRequest* req,
             ::cv::runtime::proto::P2PCaptureResponse* resp) {
               return service->P2PCapture(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[39],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetAutoWhitebalanceRequest, ::cv::runtime::proto::SetAutoWhitebalanceResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetAutoWhitebalanceRequest* req,
             ::cv::runtime::proto::SetAutoWhitebalanceResponse* resp) {
               return service->SetAutoWhitebalance(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[40],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetNextDeepweedOutputRequest, ::cv::runtime::proto::DeepweedOutput, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetNextDeepweedOutputRequest* req,
             ::cv::runtime::proto::DeepweedOutput* resp) {
               return service->GetNextDeepweedOutput(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[41],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetNextP2POutputRequest, ::cv::runtime::proto::P2POutputProto, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetNextP2POutputRequest* req,
             ::cv::runtime::proto::P2POutputProto* resp) {
               return service->GetNextP2POutput(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[42],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SetTargetingStateRequest, ::cv::runtime::proto::SetTargetingStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SetTargetingStateRequest* req,
             ::cv::runtime::proto::SetTargetingStateResponse* resp) {
               return service->SetTargetingState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[43],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::P2PBufferringBurstCaptureRequest, ::cv::runtime::proto::P2PBufferringBurstCaptureResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::P2PBufferringBurstCaptureRequest* req,
             ::cv::runtime::proto::P2PBufferringBurstCaptureResponse* resp) {
               return service->P2PBufferringBurstCapture(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[44],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetNextFocusMetricRequest, ::cv::runtime::proto::GetNextFocusMetricResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetNextFocusMetricRequest* req,
             ::cv::runtime::proto::GetNextFocusMetricResponse* resp) {
               return service->GetNextFocusMetric(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[45],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::RemoveDataDirRequest, ::cv::runtime::proto::RemoveDataDirResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::RemoveDataDirRequest* req,
             ::cv::runtime::proto::RemoveDataDirResponse* resp) {
               return service->RemoveDataDir(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[46],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< CVRuntimeService::Service, ::cv::runtime::proto::LastNImageRequest, ::cv::runtime::proto::ImageAndMetadataResponse>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::LastNImageRequest* req,
             ::grpc::ServerWriter<::cv::runtime::proto::ImageAndMetadataResponse>* writer) {
               return service->GetLastNImages(ctx, req, writer);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[47],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::Empty, ::cv::runtime::proto::ComputeCapabilitiesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::Empty* req,
             ::cv::runtime::proto::ComputeCapabilitiesResponse* resp) {
               return service->GetComputeCapabilities(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[48],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::Empty, ::cv::runtime::proto::SupportedTensorRTVersionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::Empty* req,
             ::cv::runtime::proto::SupportedTensorRTVersionsResponse* resp) {
               return service->GetSupportedTensorRTVersions(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[49],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::Empty, ::cv::runtime::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::Empty* req,
             ::cv::runtime::proto::Empty* resp) {
               return service->ReloadCategoryCollection(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[50],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::Empty, ::cv::runtime::proto::GetCategoryCollectionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::Empty* req,
             ::cv::runtime::proto::GetCategoryCollectionResponse* resp) {
               return service->GetCategoryCollection(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[51],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::Empty, ::cv::runtime::proto::GetErrorStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::Empty* req,
             ::cv::runtime::proto::GetErrorStateResponse* resp) {
               return service->GetErrorState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[52],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::SnapshotPredictImagesRequest, ::cv::runtime::proto::SnapshotPredictImagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::SnapshotPredictImagesRequest* req,
             ::cv::runtime::proto::SnapshotPredictImagesResponse* resp) {
               return service->SnapshotPredictImages(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CVRuntimeService_method_names[53],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CVRuntimeService::Service, ::cv::runtime::proto::GetChipForPredictImageRequest, ::cv::runtime::proto::GetChipForPredictImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CVRuntimeService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::runtime::proto::GetChipForPredictImageRequest* req,
             ::cv::runtime::proto::GetChipForPredictImageResponse* resp) {
               return service->GetChipForPredictImage(ctx, req, resp);
             }, this)));
}

CVRuntimeService::Service::~Service() {
}

::grpc::Status CVRuntimeService::Service::SetP2PContext(::grpc::ServerContext* context, const ::cv::runtime::proto::SetP2PContextRequest* request, ::cv::runtime::proto::SetP2PContextResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetCameraDimensions(::grpc::ServerContext* context, const ::cv::runtime::proto::GetCameraDimensionsRequest* request, ::cv::runtime::proto::GetCameraDimensionsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetCameraInfo(::grpc::ServerContext* context, const ::cv::runtime::proto::GetCameraInfoRequest* request, ::cv::runtime::proto::GetCameraInfoResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetDeepweedIndexToCategory(::grpc::ServerContext* context, const ::cv::runtime::proto::GetDeepweedIndexToCategoryRequest* request, ::cv::runtime::proto::GetDeepweedIndexToCategoryResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetDeepweedDetectionCriteria(::grpc::ServerContext* context, const ::cv::runtime::proto::SetDeepweedDetectionCriteriaRequest* request, ::cv::runtime::proto::SetDeepweedDetectionCriteriaResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetDeepweedDetectionCriteria(::grpc::ServerContext* context, const ::cv::runtime::proto::GetDeepweedDetectionCriteriaRequest* request, ::cv::runtime::proto::GetDeepweedDetectionCriteriaResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetDeepweedSupportedCategories(::grpc::ServerContext* context, const ::cv::runtime::proto::GetDeepweedSupportedCategoriesRequest* request, ::cv::runtime::proto::GetDeepweedSupportedCategoriesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetCameraTemperatures(::grpc::ServerContext* context, const ::cv::runtime::proto::GetCameraTemperaturesRequest* request, ::cv::runtime::proto::GetCameraTemperaturesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetCameraSettings(::grpc::ServerContext* context, const ::cv::runtime::proto::SetCameraSettingsRequest* request, ::cv::runtime::proto::SetCameraSettingsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetCameraSettings(::grpc::ServerContext* context, const ::cv::runtime::proto::GetCameraSettingsRequest* request, ::cv::runtime::proto::GetCameraSettingsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::StartBurstRecordFrames(::grpc::ServerContext* context, const ::cv::runtime::proto::StartBurstRecordFramesRequest* request, ::cv::runtime::proto::StartBurstRecordFramesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::StopBurstRecordFrames(::grpc::ServerContext* context, const ::cv::runtime::proto::StopBurstRecordFramesRequest* request, ::cv::runtime::proto::StopBurstRecordFramesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetConnectors(::grpc::ServerContext* context, const ::cv::runtime::proto::GetConnectorsRequest* request, ::cv::runtime::proto::GetConnectorsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetConnectors(::grpc::ServerContext* context, const ::cv::runtime::proto::SetConnectorsRequest* request, ::cv::runtime::proto::SetConnectorsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetTiming(::grpc::ServerContext* context, const ::cv::runtime::proto::GetTimingRequest* request, ::cv::runtime::proto::GetTimingResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::Predict(::grpc::ServerContext* context, const ::cv::runtime::proto::PredictRequest* request, ::cv::runtime::proto::PredictResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::LoadAndQueue(::grpc::ServerContext* context, const ::cv::runtime::proto::LoadAndQueueRequest* request, ::cv::runtime::proto::LoadAndQueueResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetImage(::grpc::ServerContext* context, const ::cv::runtime::proto::SetImageRequest* request, ::cv::runtime::proto::SetImageResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::UnsetImage(::grpc::ServerContext* context, const ::cv::runtime::proto::UnsetImageRequest* request, ::cv::runtime::proto::UnsetImageResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetModelPaths(::grpc::ServerContext* context, const ::cv::runtime::proto::GetModelPathsRequest* request, ::cv::runtime::proto::GetModelPathsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetGPSLocation(::grpc::ServerContext* context, const ::cv::runtime::proto::SetGPSLocationRequest* request, ::cv::runtime::proto::SetGPSLocationResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetImplementStatus(::grpc::ServerContext* context, const ::cv::runtime::proto::SetImplementStatusRequest* request, ::cv::runtime::proto::SetImplementStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetImageScore(::grpc::ServerContext* context, const ::cv::runtime::proto::SetImageScoreRequest* request, ::cv::runtime::proto::SetImageScoreResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetScoreQueue(::grpc::ServerContext* context, const ::cv::runtime::proto::GetScoreQueueRequest* request, ::cv::runtime::proto::GetScoreQueueResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::ListScoreQueues(::grpc::ServerContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::ListScoreQueuesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetMaxImageScore(::grpc::ServerContext* context, const ::cv::runtime::proto::GetMaxImageScoreRequest* request, ::cv::runtime::proto::GetMaxImageScoreResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetMaxScoredImage(::grpc::ServerContext* context, const ::cv::runtime::proto::GetMaxScoredImageRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetLatestP2PImage(::grpc::ServerContext* context, const ::cv::runtime::proto::GetLatestP2PImageRequest* request, ::cv::runtime::proto::P2PImageAndMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetChipImage(::grpc::ServerContext* context, const ::cv::runtime::proto::GetChipImageRequest* request, ::cv::runtime::proto::ChipImageAndMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetChipQueueInformation(::grpc::ServerContext* context, const ::cv::runtime::proto::ChipQueueInformationRequest* request, ::cv::runtime::proto::ChipQueueInformationResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::FlushQueues(::grpc::ServerContext* context, const ::cv::runtime::proto::FlushQueuesRequest* request, ::cv::runtime::proto::FlushQueuesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetLatestImage(::grpc::ServerContext* context, const ::cv::runtime::proto::GetLatestImageRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetImageNearTimestamp(::grpc::ServerContext* context, const ::cv::runtime::proto::GetImageNearTimestampRequest* request, ::cv::runtime::proto::ImageAndMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetLightweightBurstRecord(::grpc::ServerContext* context, const ::cv::runtime::proto::GetLightweightBurstRecordRequest* request, ::cv::runtime::proto::GetLightweightBurstRecordResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetBooted(::grpc::ServerContext* context, const ::cv::runtime::proto::GetBootedRequest* request, ::cv::runtime::proto::GetBootedResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetReady(::grpc::ServerContext* context, const ::cv::runtime::proto::GetReadyRequest* request, ::cv::runtime::proto::GetReadyResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetDeepweedOutputByTimestamp(::grpc::ServerContext* context, const ::cv::runtime::proto::GetDeepweedOutputByTimestampRequest* request, ::cv::runtime::proto::DeepweedOutput* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetRecommendedStrobeSettings(::grpc::ServerContext* context, const ::cv::runtime::proto::GetRecommendedStrobeSettingsRequest* request, ::cv::runtime::proto::GetRecommendedStrobeSettingsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::P2PCapture(::grpc::ServerContext* context, const ::cv::runtime::proto::P2PCaptureRequest* request, ::cv::runtime::proto::P2PCaptureResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetAutoWhitebalance(::grpc::ServerContext* context, const ::cv::runtime::proto::SetAutoWhitebalanceRequest* request, ::cv::runtime::proto::SetAutoWhitebalanceResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetNextDeepweedOutput(::grpc::ServerContext* context, const ::cv::runtime::proto::GetNextDeepweedOutputRequest* request, ::cv::runtime::proto::DeepweedOutput* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetNextP2POutput(::grpc::ServerContext* context, const ::cv::runtime::proto::GetNextP2POutputRequest* request, ::cv::runtime::proto::P2POutputProto* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SetTargetingState(::grpc::ServerContext* context, const ::cv::runtime::proto::SetTargetingStateRequest* request, ::cv::runtime::proto::SetTargetingStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::P2PBufferringBurstCapture(::grpc::ServerContext* context, const ::cv::runtime::proto::P2PBufferringBurstCaptureRequest* request, ::cv::runtime::proto::P2PBufferringBurstCaptureResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetNextFocusMetric(::grpc::ServerContext* context, const ::cv::runtime::proto::GetNextFocusMetricRequest* request, ::cv::runtime::proto::GetNextFocusMetricResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::RemoveDataDir(::grpc::ServerContext* context, const ::cv::runtime::proto::RemoveDataDirRequest* request, ::cv::runtime::proto::RemoveDataDirResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetLastNImages(::grpc::ServerContext* context, const ::cv::runtime::proto::LastNImageRequest* request, ::grpc::ServerWriter< ::cv::runtime::proto::ImageAndMetadataResponse>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetComputeCapabilities(::grpc::ServerContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::ComputeCapabilitiesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetSupportedTensorRTVersions(::grpc::ServerContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::SupportedTensorRTVersionsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::ReloadCategoryCollection(::grpc::ServerContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetCategoryCollection(::grpc::ServerContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::GetCategoryCollectionResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetErrorState(::grpc::ServerContext* context, const ::cv::runtime::proto::Empty* request, ::cv::runtime::proto::GetErrorStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::SnapshotPredictImages(::grpc::ServerContext* context, const ::cv::runtime::proto::SnapshotPredictImagesRequest* request, ::cv::runtime::proto::SnapshotPredictImagesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CVRuntimeService::Service::GetChipForPredictImage(::grpc::ServerContext* context, const ::cv::runtime::proto::GetChipForPredictImageRequest* request, ::cv::runtime::proto::GetChipForPredictImageResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace cv
}  // namespace runtime
}  // namespace proto

