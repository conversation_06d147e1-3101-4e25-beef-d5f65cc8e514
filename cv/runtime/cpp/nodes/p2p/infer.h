#pragma once

#include <vector>

#include <c10/cuda/CUDAStream.h>
#include <filesystem>
#include <glm/glm.hpp>
#include <random>
#include <torch/torch.h>

#include "cv/p2p/output.h"
#include "cv/runtime/cpp/cv_http_client.h"
#include "cv/runtime/cpp/image_score_queue.h"
#include "cv/runtime/cpp/metadata_writer.h"
#include "cv/runtime/cpp/node.h"
#include "deeplearning/server/trt_runtime/cpp/runtime.h"
#include "generated/deeplearning/model_io/proto/metadata.pb.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/fixed_buffer.h"
#include "lib/common/image/cpp/async_image_io.h"
#include "lib/common/model/cpp/model_registry.h"

namespace F = torch::nn::functional;

namespace cv {
namespace runtime {
namespace nodes {
namespace p2p {

using namespace lib::common;

using CameraBufferMap =
    std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>>;

class P2PInfer : public CVNodeImpl {
public:
  P2PInfer(camera::Camera camera, Input<camera::CameraImage> input_target, CameraBufferMap distance_camera_buffers,
           std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry, int gpu_id,
           bool enable_incremental_p2p, bool is_simulator, std::unordered_map<std::string, CVHttpClient> &http_clients);
  Output<cv::p2p::P2POutput> &get_output() { return output_; }
  Output<cv::p2p::P2POutputForBurstRecord> &get_output_for_burst_record() { return output_for_burst_record_; }
  bool ready();

  bool set_context(glm::vec2 predict_coords, int64_t predict_timestamp, std::string predict_cam_id);
  void set_context_with_image(glm::vec2 predict_coords, camera::CameraImage image);
  void set_safety_zone(float safety_zone_up, float safety_zone_down, float safety_zone_left, float safety_zone_right);
  void reload_model();
  void clear_model();

protected:
  void close_connectors();
  torch::Tensor normalize(torch::Tensor image);
  int64_t tick();

private:
  std::tuple<torch::Tensor, glm::vec2, glm::vec2> crop_and_resize_ppi(torch::Tensor input, float input_ppi,
                                                                      glm::ivec2 crop_center, glm::ivec2 crop_size);
  void set_context_with_remote_image(camera::CameraImage image);

  const std::chrono::duration<long int> kCaptureTimeout = std::chrono::seconds(1);

  Input<camera::CameraImage> input_target_;
  Output<cv::p2p::P2POutput> output_;
  std::mutex last_predict_mutex_;
  std::shared_mutex tick_mutex_;
  std::optional<camera::CameraImage> last_predict_image_;
  std::optional<torch::Tensor> last_predict_image_t_;
  std::optional<glm::vec2> last_predict_coords_;
  std::optional<std::string> last_predict_cam_id_;
  camera::CameraImage last_target_image_;
  glm::vec2 last_target_coords_;
  bool incremental_p2p_;
  bool context_updated_;
  float safety_zone_up_;
  float safety_zone_down_;
  float safety_zone_left_;
  float safety_zone_right_;

  CameraBufferMap camera_buffers_;
  CameraBufferMap distance_camera_buffers_;
  model::AtomicModel model_;
  torch::Tensor means_;
  torch::Tensor stds_;
  glm::ivec2 predict_crop_size_;
  glm::ivec2 target_crop_size_;
  std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry_;
  int gpu_id_;
  bool enable_incremental_p2p_;
  c10::cuda::CUDAStream cuda_stream_;
  Output<cv::p2p::P2POutputForBurstRecord> output_for_burst_record_;
  std::shared_ptr<ScoreQueue<P2PScoreQueueObject>> p2p_success_queue_;
  std::shared_ptr<ScoreQueue<P2PScoreQueueObject>> p2p_miss_queue_;
  bool is_simulator_;
  std::unordered_map<std::string, CVHttpClient> cv_http_clients_; // predict_id -> http_client
};

} // namespace p2p
} // namespace nodes
} // namespace runtime
} // namespace cv
