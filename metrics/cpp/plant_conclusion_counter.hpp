#pragma once

#include <memory>
#include <mutex>
#include <optional>
#include <string>
#include <thread>
#include <tuple>
#include <unordered_map>
#include <vector>

#include <lib/common/cpp/utils/default_unordered_map.hpp>
#include <metrics/cpp/conclusion_types.hpp>

namespace lib::common {
class RedisClient;
}
namespace carbon::metrics {
class DailyTimezone;
class PlantConclusionCounter {
public:
  PlantConclusionCounter(std::shared_ptr<DailyTimezone> dtz = nullptr);
  virtual ~PlantConclusionCounter();
  void increment(ConclusionType type, bool armed, bool is_weed, float size_mm, const std::string &type_name,
                 uint32_t required_laser_time, bool valid, bool is_marked_for_thinning);
  uint32_t get_weeds_shot_today();

private:
  void loop();
  void write();
  std::shared_ptr<DailyTimezone> dtz_;
  struct PlantData {
    std::vector<std::vector<uint32_t>> counts;
    double total_weeds_size_mm;
    double total_crops_size_mm;
    uint64_t total_targetable_req_laser_time_ms;
    uint64_t total_untargetable_req_laser_time_ms;
    uint64_t total_targetable_count;
    uint64_t total_untargetable_count;
    uint64_t total_valid_crop_count;
    common::DefaultUnorderedMap<std::string, uint32_t> count_by_type;
    PlantData();
    void reset();
    void write(const std::string &key, lib::common::RedisClient &redis);
  };
  struct PlantDataWrapper {
    PlantDataWrapper();
    std::mutex mut;
    std::unique_ptr<PlantData> data;
    void increment(ConclusionType type, bool armed, bool is_weed, float size_mm, const std::string &type_name,
                   uint32_t required_laser_time, bool valid, bool is_marked_for_thinning);
  };
  PlantDataWrapper daily_data_;
  PlantDataWrapper job_data_;

  std::thread thread_;
};
} // namespace carbon::metrics