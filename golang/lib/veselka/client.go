package veselka

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/google/go-querystring/query"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/sirupsen/logrus"
)

var (
	ErrModelNotFound = errors.New("model not found")
	appVeselkaLogger = logrus.New()
)

type ModelType string

const (
	userAgent           = "veselka-robot-client" // TODO:(smt) this should have version number
	HttpContentType     = "Content-Type"
	HttpApplicationJSON = "application/json"
)

type Client struct {
	apiBase          *url.URL
	robotEnv         environment.Robot
	environment      *config.ConfigTree
	experimental     *config.ConfigTree
	httpClient       *http.Client
	httpClientNoAuth *http.Client
	logger           *logrus.Entry
}

func New(robot environment.Robot, environment *config.ConfigTree, experimental *config.ConfigTree, httpClient *http.Client) *Client {
	return &Client{
		apiBase:          &url.URL{Scheme: "https", Host: robot.CarbonVeselkaHost},
		robotEnv:         robot,
		environment:      environment,
		experimental:     experimental,
		httpClient:       httpClient,
		httpClientNoAuth: &http.Client{Timeout: time.Hour},
		logger:           appVeselkaLogger.WithFields(logrus.Fields{"module": "VeselkaClient"}),
	}
}

func (v *Client) getApiBaseURL() *url.URL {
	u := *v.apiBase
	return &u
}

func (v *Client) setRequestCommonHeaders(req *http.Request) {
	req.Header.Set("User-Agent", fmt.Sprintf("%s/%s", userAgent, v.robotEnv.MakaRobotName))
	req.Header.Set(environment.RobotHTTPHeader, v.robotEnv.MakaRobotName)
	req.Header.Set(environment.GenerationHTTPHeader, v.robotEnv.MakaGen)
}

func (v *Client) SetLogLevel(level logrus.Level) {
	appVeselkaLogger.SetLevel(level)
}

func (v *Client) SetLogFormatter(formatter *logrus.TextFormatter) {
	appVeselkaLogger.SetFormatter(formatter)
}

func (v *Client) GetModelInfo(ctx context.Context, id string) (*Model, error) {
	model := &Model{}

	models, err := v.GetModelInfos(ctx, []string{id})
	if err != nil || len(models) < 1 {
		return nil, err
	}
	for _, currentModel := range models {
		model = currentModel
		break
	}
	return model, nil
}

func (v *Client) GetModelInfos(ctx context.Context, ids []string) (map[string]*Model, error) {
	tm := timer()
	v.logger.Debugln("retrieving modelInfos:", ids)
	models := make(map[string]*Model)
	if len(ids) == 0 {
		return models, nil
	}

	u := v.getApiBaseURL()
	u.Path = "/modelsvc/" + strings.Join(ids, ",")

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, err
	}
	v.setRequestCommonHeaders(req)
	response, err := v.httpClient.Do(req)
	if err != nil {
		v.logger.Info("failed to send get request")
		return nil, err
	}
	defer response.Body.Close()

	v.logger.Debugln("get model infos response:", response.Status, "duration:", tm())
	switch response.StatusCode {
	case http.StatusOK:
	case http.StatusNotFound:
		return nil, ErrModelNotFound
	default:
		return nil, fmt.Errorf("failed to get model info for %v", ids)
	}

	decoder := json.NewDecoder(response.Body)
	if len(ids) == 1 { // TODO:(smt) fix this veselka side
		model := &Model{}
		err = decoder.Decode(&model)
		models[model.ID] = model
	} else {
		err = decoder.Decode(&models)
	}
	if err != nil {
		v.logger.WithError(err).Error("failed to decode response")
		return nil, err
	}

	return models, nil
}

func (v *Client) GetModelArtifact(ctx context.Context, computeCapability string, modelID string, tensorRTVersion string, startingSize int64) (*http.Response, error) {
	tm := timer()
	v.logger.Debugln("retrieving artifact for model:", modelID, "startingSize", startingSize)

	params := url.Values{}
	params.Add("compute_capability", computeCapability)
	params.Add("model_id", modelID)
	params.Add("tensorrt_version", tensorRTVersion)

	u := v.getApiBaseURL()
	u.Path = "/modelsvc/artifact/download"
	u.RawQuery = params.Encode()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, err
	}
	v.setRequestCommonHeaders(req)

	resp, err := v.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode == http.StatusTemporaryRedirect {
		defer resp.Body.Close() // close previous response body if we are redirecting

		v.logger.Debugln("get model was redirected:", resp.Status, "duration:", tm())
		redirectTarget := resp.Header.Get("Location")
		subReq, err := http.NewRequestWithContext(ctx, http.MethodGet, redirectTarget, nil)
		if err != nil {
			return nil, err
		}
		v.setRequestCommonHeaders(req)
		subReq.Header.Set("Range", fmt.Sprintf("bytes=%d-", startingSize))
		redirectResp, err := v.httpClientNoAuth.Do(subReq)
		if err != nil {
			return nil, err
		}
		//defer redirectResp.Body.Close() //TODO:(smt) this gets called downstream but should be cleaned up
		switch redirectResp.StatusCode {
		case http.StatusOK, http.StatusPartialContent:
			return redirectResp, nil
		default:
			redirectResp.Body.Close()
			v.logger.Warnf("download model %v bad response %v duration: %s", modelID, redirectResp.StatusCode, tm())
			return nil, fmt.Errorf("failed to download model %s sm %s trt %s: %s", modelID, computeCapability, tensorRTVersion, redirectResp.Status)
		}
	}

	// we only get here if there is no redirect
	// because we want to return a response with an open body for downloading
	// we do a few hoops to only close the response body on failure
	// should clean this up in the future by moving file download guts here
	switch resp.StatusCode {
	case http.StatusOK:
		return resp, nil
	default:
		defer resp.Body.Close()
		v.logger.Warnf("download model %v bad response %v duration: %s", modelID, resp.StatusCode, tm())
		return nil, fmt.Errorf("failed to download model %s sm %s trt %s: %s", modelID, computeCapability, tensorRTVersion, resp.Status)
	}
}

func (v *Client) GetAllCrops(ctx context.Context) ([]*Crop, error) {
	tm := timer()
	v.logger.Debugln("retrieving all crops")

	u := v.getApiBaseURL()
	u.Path = "/cropsvc/crops"

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, err
	}

	v.setRequestCommonHeaders(req)
	resp, err := v.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	v.logger.Debugln("get all crops response:", resp.Status, "duration:", tm())
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("invalid response: %s", resp.Status)
	}
	crops := make([]*Crop, 0)
	if err := json.NewDecoder(resp.Body).Decode(&crops); err != nil {
		return nil, err
	}
	return crops, nil
}

func (v *Client) GetCrop(ctx context.Context, id string) (*Crop, error) {
	tm := timer()
	v.logger.Debugln("retrieving cropID:", id)

	params := url.Values{}
	params.Add("id", id)

	u := v.getApiBaseURL()
	u.Path = "/cropsvc/crop"
	u.RawQuery = params.Encode()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, err
	}

	v.setRequestCommonHeaders(req)
	resp, err := v.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	v.logger.Debugln("get crop response:", resp.Status, "duration:", tm())
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("invalid response: %s", resp.Status)
	}
	crop := new(Crop)
	if err := json.NewDecoder(resp.Body).Decode(crop); err != nil {
		return nil, err
	}
	return crop, nil
}

func (v *Client) GetDefaultP2PModel(ctx context.Context, currentModelVersion int) (*Model, error) {
	v.logger.Debugln("requesting default p2p model")
	params := GetRecommendedModelParams{
		CurrentModelVersion: currentModelVersion,
		ModelType:           ModelTypeP2P,
	}
	return v.getRecommendedModel(ctx, params)
}

func (v *Client) GetModelForCropID(ctx context.Context, params GetRecommendedModelParams) (*Model, error) {
	v.logger.Debugf("requesting model for %+v", params)
	params.ModelType = ModelTypeDeepweed
	return v.getRecommendedModel(ctx, params)
}

type GetRecommendedModelParams struct {
	CropID              string          `url:"crop_id" json:"crop_id"`
	SubType             string          `url:"sub_type,omitempty" json:"sub_type"`
	RobotName           string          `url:"robot_name" json:"robot_name"`
	Geohash             string          `url:"geohash" json:"gohash"`
	CurrentModelVersion int             `url:"current_model_version" json:"current_model_version"`
	ModelType           ModelType       `url:"model_type" json:"model_type"`
	FeatureFlags        map[string]bool `url:"-" json:"feature_flags"`
}

func (params GetRecommendedModelParams) Validate() error {
	switch params.ModelType {
	case ModelTypeDeepweed:
		if params.CropID == "" || params.RobotName == "" || params.Geohash == "" {
			return fmt.Errorf("invalid GetRecommendedModelParams %+v", params)
		}
	case ModelTypeP2P:
	default:
		return fmt.Errorf("invalid GetRecommendedModelParams model type is required")
	}
	return nil
}

func (v *Client) getRecommendedModel(ctx context.Context, parameters GetRecommendedModelParams) (*Model, error) {
	tm := timer()
	if err := parameters.Validate(); err != nil {
		return nil, err
	}
	params, err := query.Values(parameters)
	if err != nil {
		return nil, err
	}
	v.setFeatureFlags(params, parameters.FeatureFlags)

	u := v.getApiBaseURL()
	u.Path = "/modelsvc/model"
	u.RawQuery = params.Encode()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, err
	}
	v.setRequestCommonHeaders(req)
	resp, err := v.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	v.logger.Debugln("get model for crop id response:", resp.Status, "duration:", tm())
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("invalid response: %s", resp.Status)
	}
	model := new(Model)
	if err := json.NewDecoder(resp.Body).Decode(model); err != nil {
		return nil, err
	}
	return model, nil
}

type ModelParameterRequest struct {
	ModelID string `json:"model_id"`
	CropID  string `json:"crop_id"`
	RobotID string `json:"robot_id"`
	Geohash string `json:"geohash"`
}

func (v *Client) GetModelParameters(ctx context.Context, parametersRequest []ModelParameterRequest) ([]*almanac.ModelinatorConfig, error) {
	tm := timer()
	v.logger.Debugf("requesting model params for: %v", parametersRequest)

	u := v.getApiBaseURL()
	u.Path = "/modelsvc/parameters"

	body, err := json.Marshal(parametersRequest)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, u.String(), bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	v.setRequestCommonHeaders(req)
	req.Header.Set(HttpContentType, HttpApplicationJSON)
	resp, err := v.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	v.logger.Debugln("get params response:", resp.Status, "duration:", tm())
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("invalid response: %s", resp.Status)
	}
	cropModelParameters := make([]*almanac.ModelinatorConfig, 0)
	if err := json.NewDecoder(resp.Body).Decode(&cropModelParameters); err != nil {
		return nil, err
	}
	return cropModelParameters, nil
}

func (v *Client) setFeatureFlags(params url.Values, featureFlags map[string]bool) {
	for name, value := range featureFlags {
		params.Set(name, strconv.FormatBool(value))
	}
}

func (v *Client) RecommendModel(ctx context.Context, plantCaptchaResults *frontend.PlantCaptchaResults, robotName string) (*almanac.ModelinatorConfig, bool, error) {
	tm := timer()
	m := protojson.MarshalOptions{UseProtoNames: true, UseEnumNumbers: true, EmitUnpopulated: true}

	params := url.Values{}
	params.Add("robot_id", robotName)

	u := v.getApiBaseURL()
	u.Path = "/modelsvc/captcha_recommend"
	u.RawQuery = params.Encode()

	body, err := m.Marshal(plantCaptchaResults)
	if err != nil {
		return nil, false, err
	}
	v.logger.Debugf("PlantCaptcha: request: %v", string(body))
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, u.String(), bytes.NewReader(body))
	if err != nil {
		return nil, false, err
	}
	v.setRequestCommonHeaders(req)
	req.Header.Set(HttpContentType, HttpApplicationJSON)
	resp, err := v.httpClient.Do(req)
	if err != nil {
		return nil, false, err
	}
	defer resp.Body.Close()

	v.logger.Debugln("PlantCaptcha: response:", resp.Status, "duration:", tm())
	if resp.StatusCode != http.StatusOK {
		return nil, false, fmt.Errorf("invalid response: %s", resp.Status)
	}

	veselkaResponse := &frontend.VeselkaPlantCaptchaResponse{}
	if err := json.NewDecoder(resp.Body).Decode(&veselkaResponse); err != nil {
		v.logger.WithError(err).Error("Could not decode response body")
		return nil, false, err
	}
	return veselkaResponse.NewModelParameters, veselkaResponse.Succeeded, nil
}

func (v *Client) GetAllPointCategories(ctx context.Context) ([]PointCategory, error) {
	tm := timer()
	u := v.getApiBaseURL()
	u.Path = "/modelsvc/point_categories"

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, err
	}
	v.setRequestCommonHeaders(req)
	resp, err := v.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	v.logger.Debugln("PointCategories: response:", resp.Status, "duration:", tm())
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("invalid response: %s", resp.Status)
	}

	pointCats := make([]PointCategory, 0)
	if err := json.NewDecoder(resp.Body).Decode(&pointCats); err != nil {
		return nil, err
	}
	return pointCats, nil
}

func timer() func() time.Duration {
	start := time.Now()
	return func() time.Duration {
		return time.Since(start)
	}
}
