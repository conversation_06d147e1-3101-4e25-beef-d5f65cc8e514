import asyncio
from time import time
from typing import Callable, List, Optional

from hw_client_gps_distributor.pybind.hw_client_gps_distributor_python import RawGpsData, get_next
from lib.common.geo.geojson import Feature, LineString, Position
from lib.common.logging import get_logger
from lib.common.messaging.message import msg_decoder
from lib.common.units.angle import Angle
from lib.rtc.data_provider.data_bus_client import AUTH_READ_REQ, AUTH_WRITE_REQ, DATA_BUS
from lib.rtc.messaging.message import Message
from proj_utils.pybind.proj_utils_python import Geod
from tractor_ctl.messages import CurrHeadingResp, ErrorMsg, MessageType, SetHeadingReq, empty_sender, msg_sender

LOG = get_logger(__name__)


async def _get_recent_gps() -> Optional[RawGpsData]:
    recent = int((time() - 1) * 1000)  # recent time in ms
    return await asyncio.get_event_loop().run_in_executor(None, lambda: get_next(recent, 5))


class HeadingHandler:
    def __init__(self) -> None:

        DATA_BUS.register(MessageType.GET_CURRENT_HEADING_REQ, self._curr_heading_req, AUTH_READ_REQ)
        DATA_BUS.register(MessageType.SET_CURRENT_HEADING_REQ, self._set_heading_req, AUTH_WRITE_REQ)

        self._current_heading: Optional[Angle] = None
        self._curr_path: Optional[Feature] = None
        self._cbs: List[Callable[[], None]] = []
        self._geod = Geod("WGS84")

    @staticmethod
    async def build() -> "HeadingHandler":
        hh = HeadingHandler()
        await hh.__async_init()
        return hh

    async def __async_init(self) -> None:
        pass

    @msg_sender
    async def _curr_heading_req(self, _: Message) -> CurrHeadingResp:
        resp = CurrHeadingResp(heading_deg=None, start_point=None)
        if self._current_heading is not None:
            resp.heading_deg = self._current_heading.degrees
        if self._curr_path is not None:
            ls = self._curr_path.geometry
            assert isinstance(ls, LineString) and len(ls.coordinates) > 0
            resp.start_point = Position.from_list(ls.coordinates[0])
        return resp

    @empty_sender
    @msg_decoder(SetHeadingReq)
    async def _set_heading_req(self, req: SetHeadingReq) -> Optional[ErrorMsg]:
        gps_pos = await _get_recent_gps()
        if gps_pos is None:
            return ErrorMsg("Could not get valid GPS data")
        if req.heading_deg is not None:
            self._current_heading = Angle.from_degrees(req.heading_deg)
        else:
            assert gps_pos.dual is not None
            self._current_heading = Angle.from_degrees(gps_pos.dual.heading_deg.value)
        end = self._geod.direct(
            gps_pos.longitude, gps_pos.latitude, self._current_heading.degrees, 5000
        )  # 3.1miles away
        self._curr_path = Feature(
            geometry=LineString(coordinates=[[gps_pos.longitude, gps_pos.latitude], [end.lon, end.lat]])
        )
        for cb in self._cbs:
            cb()
        return None

    def get_current_path(self) -> Optional[Feature]:
        return self._curr_path

    def on_path_change(self, cb: Callable[[], None]) -> None:
        self._cbs.append(cb)
