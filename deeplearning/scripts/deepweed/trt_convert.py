import argparse
import logging
import os
import sys
from typing import Optional


def main() -> None:
    logging.basicConfig()
    logging.getLogger().setLevel("INFO")

    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", required=True)
    parser.add_argument("--segmentation-model-id", type=str, default=None)
    parser.add_argument("--tensorrt-version", choices=["8.0.1.6", "10.0.1"], required=True)
    parser.add_argument("--dataset-id", type=str, default=None)
    parser.add_argument("--fixed-crop-id", type=str, default=None)
    parser.add_argument("--num-evaluation-samples", type=int, default=500)
    args, _ = parser.parse_known_args()

    sys.path = [x for x in sys.path if not x.startswith("/opt/TensorRT")]
    sys.path.insert(0, f"/opt/TensorRT-{args.tensorrt_version}/python/site-packages")

    # only load deeplearning code after updating sys.path
    from deeplearning.deepweed.trt_convert import TrtConvert
    from deeplearning.scripts.utils.utils import TrainingInfo
    from deeplearning.utils.trainer import (
        S3_BUCKET,
        compute_md5sum,
        get_compute_capability,
        get_tensorrt_version,
        upload_directory,
        get_tensorrt_file_name,
    )
    from lib.common.veselka.client import VeselkaClient

    training_info = TrainingInfo(args.model_id)
    assert training_info.dataset_id is not None, f"No dataset_id recorded for {args.model_id}"
    deepweed_config = training_info.deepweed_config
    assert deepweed_config is not None, "Deepweed DL config is missing"
    trt_file_name = get_tensorrt_file_name(deepweed_config.convert_int8)
    trt_file_path = os.path.join(training_info.data_dir, trt_file_name)

    segmentation_checkpoint: Optional[str] = None
    if args.segmentation_model_id is not None:
        segmentation_training_info = TrainingInfo(args.segmentation_model_id)
        segmentation_checkpoint = segmentation_training_info.best_model_weights

    trt_converter = TrtConvert()
    trt_converter.load_model(
        training_info.best_model_weights,
        deepweed_config,
        args.fixed_crop_id,
        segmentation_checkpoint=segmentation_checkpoint,
    )
    trt_converter.load_datasets(args.dataset_id if args.dataset_id is not None else training_info.dataset_id)
    test_results = trt_converter.convert(
        max_batch_size=deepweed_config.evaluation_batch_size,
        save_to=trt_file_path,
        int8=deepweed_config.convert_int8,
        fp16=deepweed_config.convert_fp16,
        calibration_batch_size=16,
        calibration_only=False,
        error_metrics=True,
        config=deepweed_config,
        num_evaluation_samples=args.num_evaluation_samples,
    )

    upload_directory(training_info.data_dir)

    veselka_client = VeselkaClient()
    veselka_client.post_model_artifact(
        model_id=args.model_id,
        tensorrt_version=get_tensorrt_version(),
        compute_capability=get_compute_capability(),
        url=f"s3://{S3_BUCKET}/models/{args.model_id}/{trt_file_name}",
        checksum=compute_md5sum(trt_file_path),
        test_results=test_results,
    )


if __name__ == "__main__":
    main()
