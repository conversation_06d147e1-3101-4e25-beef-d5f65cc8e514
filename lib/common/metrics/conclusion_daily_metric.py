from typing import Dict

from lib.common.logging import get_logger
from lib.common.metrics.conclusion_checker import DailyCon<PERSON>lusionChecker
from lib.common.metrics.daily_metric import DailyMetric
from metrics.pybind.metrics_python import DailyTimezone

LOG = get_logger(__name__)


class ConclusionDailyMetric(DailyMetric):
    def __init__(self, dtz: DailyTimezone) -> None:
        super().__init__("Conclusions")
        self._dtz = dtz

    async def _add_weed_metrics(self, key_map: Dict[str, str]) -> None:
        info = await self._checker.check_weed_counters()

        key_map["killed_weeds"] = str(info.killed)
        key_map["missed_weeds"] = str(info.missed)
        key_map["skipped_weeds"] = str(info.skipped)
        key_map["not_weeding"] = str(info.not_shooting)
        key_map["total_weeds"] = str(info.count)
        key_map["avg_weed_size_mm"] = str(info.avg_size())
        key_map["avg_targetable_req_laser_time"] = str(info.avg_targetable_time())
        key_map["avg_untargetable_req_laser_time"] = str(info.avg_untargetable_time())
        for weed_type, val in info.type_counts.items():
            key_map[weed_type] = str(val)

    async def _add_crop_metrics(self, key_map: Dict[str, str]) -> None:
        info = await self._checker.check_crop_counters()

        key_map["thinned_crops"] = str(info.killed)
        key_map["missed_crops"] = str(info.missed)
        key_map["skipped_crops"] = str(info.skipped)
        key_map["not_thinning"] = str(info.not_shooting)
        key_map["kept_crops"] = str(info.kept)
        key_map["total_crops"] = str(info.count)
        key_map["avg_crop_size_mm"] = str(info.avg_size())
        key_map["valid_crops"] = str(info.valid_crops)

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        await self._checker.migrate()
        await self._add_weed_metrics(key_map)
        await self._add_crop_metrics(key_map)

    async def start(self) -> None:
        self._checker = await DailyConclusionChecker.build(self._dtz)

    async def stop(self) -> None:
        pass

    async def _loop(self) -> None:
        pass
