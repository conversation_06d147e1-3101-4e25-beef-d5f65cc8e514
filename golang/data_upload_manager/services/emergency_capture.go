package services

import (
	"context"
	"fmt"
	"math"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	pb "github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
)

const MmInFeet float64 = 304.8

type LatestCaptureParameters struct {
	rowId uint32
	camId string
}

type EmergencyCaptureSession struct {
	stopCtx                 context.Context
	sessionStopChan         chan bool
	sessionStopCtx          context.Context
	dataCaptureState        *state.DataCaptureState
	rowClients              map[int]*rows.RowClients
	robotName               string
	emergencyCaptureRate    float64
	emergencySessionName    string
	emergencyCaptureTarget  *config.ConfigTree
	crop                    string
	cropID                  string
	admin                   *config.ConfigTree
	enableEmergencyPriority *config.ConfigTree
	hardwareManagerClient   *hardware_manager.HardwareManagerClient
	uploadProportions       *config.ConfigTree
	useLatest               bool
	latestCaptureParams     LatestCaptureParameters
	predictAperture         *config.ConfigTree
	softwareVersion         string
	snapCapture             bool
	redis                   *redis.Client
}

func NewEmergencyCaptureSession(stopCtx context.Context, rowClients map[int]*rows.RowClients, robotName string, emergencyCaptureRate float64, emergencySessionName string, emergencyCaptureTarget *config.ConfigTree, crop, cropID string, admin *config.ConfigTree, enableEmergencyPriority *config.ConfigTree, hardwareManagerClient *hardware_manager.HardwareManagerClient, uploadProportions *config.ConfigTree, useLatest bool, latestCaptureParams LatestCaptureParameters, predictAperture *config.ConfigTree, softwareVersion string, snapCapture bool, redis *redis.Client) *EmergencyCaptureSession {
	dataCaptureState := state.NewDataCaptureState()
	state := EmergencyCaptureSession{
		stopCtx:                 stopCtx,
		sessionStopChan:         make(chan bool, 1),
		dataCaptureState:        &dataCaptureState,
		rowClients:              rowClients,
		robotName:               robotName,
		emergencyCaptureRate:    emergencyCaptureRate,
		emergencySessionName:    emergencySessionName,
		emergencyCaptureTarget:  emergencyCaptureTarget,
		crop:                    crop,
		cropID:                  cropID,
		admin:                   admin,
		enableEmergencyPriority: enableEmergencyPriority,
		hardwareManagerClient:   hardwareManagerClient,
		uploadProportions:       uploadProportions,
		useLatest:               useLatest,
		latestCaptureParams:     latestCaptureParams,
		predictAperture:         predictAperture,
		softwareVersion:         softwareVersion,
		snapCapture:             snapCapture,
		redis:                   redis,
	}
	return &state
}

func getEmergencyDir() string {
	return fmt.Sprintf("%v/data_upload_manager/emergency", getDataDir())
}

func getSessionDir(sessionName string) string {
	return fmt.Sprintf("%v/%v", getEmergencyDir(), sessionName)
}

func flushQueues(client pb.CVRuntimeServiceClient) error {
	emergencyScoreTypes := []string{"emergency_weed_tracking", "emergency_recency", "emergency_weed_margin_max", "emergency_crop_margin_max", "emergency_ambiguous_weed_count", "emergency_ambiguous_crop_count", "emergency_driptape"}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	_, err := client.FlushQueues(ctx, &pb.FlushQueuesRequest{ScoreType: emergencyScoreTypes})

	return err
}

func (s *EmergencyCaptureSession) saveBestImage(robotName string, sessionDirectory string) {
	var scores map[int]ScorePair
	var scoreType string

	validScore := false
	for {
		scoreType = selectScoreType(s.uploadProportions.GetNode("weed_tracking").GetFloatValue(), s.uploadProportions.GetNode("recency").GetFloatValue(), s.uploadProportions.GetNode("weed_margin_max").GetFloatValue(), s.uploadProportions.GetNode("crop_margin_max").GetFloatValue(), s.uploadProportions.GetNode("ambiguous_weed_count").GetFloatValue(), s.uploadProportions.GetNode("ambiguous_crop_count").GetFloatValue(), s.uploadProportions.GetNode("driptape").GetFloatValue(), s.uploadProportions.GetNode("unknown_plant").GetFloatValue())
		scoreType = fmt.Sprintf("emergency_%v", scoreType)

		scores, validScore = getMaxScores(s.rowClients, scoreType)

		if !validScore {
			logrus.Errorf("No valid scores")
			continue
		} else {
			break
		}
	}

	indexPair := getInd(scores, 0.0)

	if indexPair.rowID < 0 || indexPair.pcID == 0 {
		// This should never happen because of the validScore check, but just to be safe
		indexPair.rowID = getRandomMapKey(s.rowClients)
		indexPair.pcID = getRandomMapKey(s.rowClients[indexPair.rowID].CVRuntimeClients)
	}

	cvClient := s.rowClients[indexPair.rowID].CVRuntimeClients[indexPair.pcID]
	client, err := cvClient.GetRawClient()
	if err != nil {
		logrus.Errorf("Failed to get raw client: %v", err)
		return
	}
	imageAndMetadata, err_get_max := getMaxImage(client, scoreType)
	if err_get_max != nil {
		logrus.Errorf("Couldn't get best image: %v", err_get_max)
		return
	}

	job_id, job_name := getCurrentJobIdAndName(s.redis)
	priority := UploadPriorityEmergency
	if !s.enableEmergencyPriority.GetBoolValue() {
		priority = UploadPriorityNormal
	}
	common_filename, err := writeImageAndMetadata(imageAndMetadata, indexPair.rowID, scoreType, robotName, sessionDirectory, s.crop, s.cropID, int(s.admin.GetIntValue()), priority, "predict", s.emergencySessionName, s.predictAperture.GetStringValue(), s.softwareVersion, job_id, job_name, s.redis, true)
	if err != nil {
		logrus.Errorf("Couldn't write best image in image capture mode: %v", err)
		return
	}

	for _, rowClient := range s.rowClients {
		for _, cvClient := range rowClient.CVRuntimeClients {
			client, err := cvClient.GetRawClient()
			if err != nil {
				logrus.Errorf("Failed to get raw client: %v", err)
				continue
			}
			flushQueues(client)
		}
	}

	s.dataCaptureState.AppendFile(common_filename)
}

func (s *EmergencyCaptureSession) saveLatestImage(robotName string, sessionDirectory string, cvClient *cv_runtime_client.CVRuntimeClient, camId string, rowInd uint32, scoreType string, wg *sync.WaitGroup) {
	client, err := cvClient.GetRawClient()
	if err != nil {
		logrus.Errorf("Failed to get raw client: %v", err)
		wg.Done()
		return
	}
	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)
	imageAndMetadata, err := client.GetLatestImage(ctx, &pb.GetLatestImageRequest{CamId: camId})
	if err != nil {
		logrus.Errorf("Couldn't get latest image: %v", err)
		wg.Done()
		return
	}
	job_id, job_name := getCurrentJobIdAndName(s.redis)
	priority := UploadPriorityEmergency
	if !s.enableEmergencyPriority.GetBoolValue() {
		priority = UploadPriorityNormal
	}
	commonFilename, errWriting := writeImageAndMetadata(imageAndMetadata, int(rowInd), scoreType, robotName, sessionDirectory, s.crop, s.cropID, int(s.admin.GetIntValue()), priority, "predict", s.emergencySessionName, s.predictAperture.GetStringValue(), s.softwareVersion, job_id, job_name, s.redis, true)

	if errWriting != nil {
		logrus.Errorf("Couldn't write latest image in image capture mode: %v", errWriting)
		wg.Done()
		return
	}

	s.dataCaptureState.AppendFile(commonFilename)
	wg.Done()
}

func (s *EmergencyCaptureSession) Start() {
	s.dataCaptureState.SetCapturing(true)
	go s.runEmergencyCaptureSession()
}

func (s *EmergencyCaptureSession) Pause() {
	s.dataCaptureState.SetCapturing(false)
}

func (s *EmergencyCaptureSession) Resume() {
	s.dataCaptureState.SetCapturing(true)
}

func (s *EmergencyCaptureSession) Stop() {
	s.dataCaptureState.SetCapturing(false)
	// TODO clean files
	s.dataCaptureState.EmptyFiles()
	s.sessionStopChan <- true
}

func (s *EmergencyCaptureSession) Complete() {
	s.dataCaptureState.SetCapturing(false)
	s.dataCaptureState.SetCompleted(true)
	s.sessionStopChan <- true
}

func (s *EmergencyCaptureSession) snapCaptureImages(robotName string, sessionDirectory string, rowClients map[int]*rows.RowClients) {
	var wg sync.WaitGroup
	for rowID, rowClient := range rowClients {
		for _, cvClient := range rowClient.CVRuntimeClients {

			client, err := cvClient.GetRawClient()
			if err != nil {
				logrus.Errorf("Failed to get raw client: %v", err)
				continue
			}

			var camIds []string
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()
			cams, err := client.GetCameraInfo(ctx, &pb.GetCameraInfoRequest{})
			if err != nil {
				logrus.Errorf("Failed to get camera info: %v", err)
			}
			camInfo := cams.GetCameraInfo()

			for _, cInfo := range camInfo {
				id := cInfo.GetCamId()
				if id != "" && strings.HasPrefix(id, "predict") && cInfo.GetConnected() {
					camIds = append(camIds, id)
				}
			}

			for _, camId := range camIds {
				wg.Add(1)
				go s.saveLatestImage(robotName, sessionDirectory, cvClient, camId, uint32(rowID), "emergency_snap", &wg)
			}
		}
	}
	wg.Wait()
}

func (s *EmergencyCaptureSession) runEmergencyCaptureSession() error {
	sessionDirectory := getSessionDir(s.emergencySessionName)
	logrus.Infof("Running the emergency capture session: %v", sessionDirectory)

	if err := os.MkdirAll(sessionDirectory, 0777); err != nil {
		return err
	}

	previousDistance, _, err := s.hardwareManagerClient.GetNextDistance()

	if err != nil {
		logrus.Errorf("Error getting distance %v", err)
	}

	if s.snapCapture {
		s.snapCaptureImages(s.robotName, sessionDirectory, s.rowClients)
		s.dataCaptureState.SetCapturing(false)
		s.dataCaptureState.SetCompleted(true)
		return nil
	}

	var wg sync.WaitGroup
	var cvClient *cv_runtime_client.CVRuntimeClient

	for {
		select {
		case <-s.stopCtx.Done():
			return nil
		case <-s.sessionStopChan:
			return nil
		case <-time.After(time.Duration(250) * time.Millisecond):
			if uint64(s.dataCaptureState.ImagesCaptured()) >= s.emergencyCaptureTarget.GetUIntValue() {
				s.dataCaptureState.SetCapturing(false)
				s.dataCaptureState.SetCompleted(true)
				return nil
			}

			if !s.dataCaptureState.IsCapturing() {
				continue
			}

			newDistance, _, err := s.hardwareManagerClient.GetNextDistance()
			if err != nil {
				logrus.Errorf("Error getting distance %v", err)
				return err
			}

			if math.Abs((newDistance-previousDistance)/MmInFeet) < s.emergencyCaptureRate {
				continue
			}
			previousDistance = newDistance

			if s.useLatest {
				if cvClient == nil {
					cvClient, err = GetCVClientForCamId(s.rowClients[int(s.latestCaptureParams.rowId)], s.latestCaptureParams.camId)
					if err != nil {
						logrus.Errorf("Failed to get cv client for cam id %v: %v", s.latestCaptureParams.camId, err)
						continue
					}
				}
				go s.saveLatestImage(s.robotName, sessionDirectory, cvClient, s.latestCaptureParams.camId, s.latestCaptureParams.rowId, "emergency_latest", &wg)
			} else {
				go s.saveBestImage(s.robotName, sessionDirectory)
			}
		}
	}
}
