add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(boundary_checker SHARED ${SOURCES})
target_compile_definitions(boundary_checker PRIVATE -DSPDLOG_FMT_EXTERNAL=1 -DUSE_UNSTABLE_GEOS_CPP_API=1)
target_link_libraries(boundary_checker PRIVATE m stdc++fs pthread rt geos fmt config_tree_lib config_client_lib utils bot_stop proj_utils hw_client_gps_distributor)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(boundary_checker_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(boundary_checker_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 -DUSE_UNSTABLE_GEOS_CPP_API=1 PYBIND)
target_link_libraries(boundary_checker_python PUBLIC boundary_checker)
target_link_libraries(boundary_checker_python PRIVATE utils)
set_target_properties(boundary_checker_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
target_compile_options(boundary_checker_python PRIVATE -fvisibility=hidden)