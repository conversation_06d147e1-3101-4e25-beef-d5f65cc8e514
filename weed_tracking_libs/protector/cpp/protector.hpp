#pragma once
#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>

#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <lib/common/cpp/utils/thread_pool.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>
#include <trajectory/cpp/trajectory.hpp>

#include <atomic>
#include <memory>
#include <mutex>
#include <thread>
#include <unordered_map>

namespace carbon::protector {
class Protector : public arbiter::ArbiterClient<> {
public:
  virtual void trajectory_request(const arbiter::Request<> &req) override;
  static std::shared_ptr<Protector> get();
  ~Protector();

private:
  struct Config {
    float crop_protect_radius_mm;
    float reverse_crop_protect_radius_mm;
    float crop_protect_radius_multiplier;
    float speculative_crop_protect_radius_mm;
    bool allow_unprotect;
    float speculative_crop_protect_min_radius_mm;
    float speculative_crop_protect_radius_multiplier;
  };
  Protector();
  config::AtomicFlagConfigScopedCallback scoped_tree_;
  Config cfg_;
  common::ThreadSafeQueue<arbiter::Request<>> new_requests_;
  std::unordered_map<trajectory::Trajectory::ID, std::shared_ptr<trajectory::Trajectory>> crops_;
  std::unordered_map<trajectory::Trajectory::ID, std::shared_ptr<trajectory::Trajectory>> weeds_;
  common::ThreadPool pool_;

  // Keep thread at end to make sure all data is initialized first
  std::thread thread_;

  void work_loop();
  void reload();
};

} // namespace carbon::protector