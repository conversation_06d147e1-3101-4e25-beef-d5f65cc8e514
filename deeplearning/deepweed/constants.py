REMOTE_VESELKA_DATASET_SERVER_PORT = 8000

DATASET_SAMPLING_ALGORITHM_DEFAULT = "default"
DATASET_SAMPLING_ALGORITHM_DRIPTAPE = "driptape"
DATASET_SAMPLING_ALGORITHM_GEOHASH_DATE_CATEGORY_BUCKETS = "geohash-date-category-buckets"
DATASET_SAMPLING_ALGORITHM_GEOHASH_MONTH = "geohash-month"
DATASET_SAMPLING_ALGORITHM_GEOHASH_MONTH_HIT = "geohash-month-hit"
DATASET_SAMPLING_ALGORITHM_EMBEDDINGS = "embeddings"
DATASET_SAMPLING_ALGORITHM_CATEGORY_EMBEDDINGS = "category_embeddings"
DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS = "embedding_clusters"
DATASET_SAMPLING_ALGORITHM_UNIFORM = "uniform"


EMBEDDING_LOSS_SIZE_DIFFERENTIATION_THRESHOLD_DEFAULT = (
    1.35  # 1.35 comes from the limits we use when generating comparison image pairs for training
)
