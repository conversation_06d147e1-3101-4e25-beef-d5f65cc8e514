import asyncio
from contextlib import AbstractAsyncContextManager
from functools import partial
from types import TracebackType
from typing import TYPE_CHECKING, Dict, Optional, Type

from prometheus_client import Gauge

from config.client.cpp.config_client_python import ConfigTree
from lib.cloud.auth import TokenStore
from lib.common.config.accessor import TypedConfigAccessor
from lib.common.logging import get_logger
from lib.common.messaging.message import ErrorMsg, ErrorMsgException, msg_decoder
from lib.common.units.speed import Speed
from lib.rtc.data_provider.data_bus_client import AUTH_WRITE_REQ, DATA_BUS
from lib.rtc.messaging.message import Message
from tractor_ctl.atim_server import AtimController
from tractor_ctl.autonomy.autonomous_task_runner import AUTONOMOUS_TASK_RUNNER_STOPPED, AutonomousTaskRunner
from tractor_ctl.autonomy.autonomy import AutonomyMode, SteeringAssistAlgo
from tractor_ctl.autonomy.steering_assist import STEERING_ASSIST_STOPPED, SteeringEstimatorAlgo
from tractor_ctl.bypassable_safety import BYPASS_STATE, BYPASS_UPDATED
from tractor_ctl.constants import SERVICE_NAME
from tractor_ctl.event_bus import (
    AUTONOMY_STOPPED_EVENT,
    ON_PEER_DISCONNECT,
    REMOTE_INTERACTION,
    TRACTOR_EVENT_BUS,
    TRACTOR_STATE_CHANGE,
)
from tractor_ctl.messages import AutonomyMsg, ControlState, HHState, MessageType, RemoteAssistanceReq, empty_sender
from tractor_ctl.state.ff_stateful_emitter import FFStatefulEmitter
from tractor_ctl.state.global_states import AUTONOMOUS_STATE
from tractor_ctl.tractor_if import TractorIF, TractorInteractions
from tractor_ctl.tractor_state_provider import TractorStateProvider

LOG = get_logger(__name__)
if TYPE_CHECKING:
    ACM = AbstractAsyncContextManager["AutonomousController"]
else:
    ACM = AbstractAsyncContextManager


class AutonomousController(TractorStateProvider, ACM):
    def __init__(self, addl_bypasses: Dict[str, FFStatefulEmitter[bool]] = {}) -> None:
        super().__init__()
        self._addl_bypasses = addl_bypasses
        self._mode = AutonomyMode.REMOTE_DRIVER
        self._mode_metric = Gauge(
            subsystem=SERVICE_NAME, name="autonomy_mode", documentation="What mode is currently being used",
        )
        self._steering_assist_metric = Gauge(
            subsystem=SERVICE_NAME,
            name="steering_assist_algo",
            documentation="What algo is currently being used for steering assist",
        )
        self._assists: Dict[SteeringAssistAlgo, SteeringEstimatorAlgo] = {}
        self._active_assist = SteeringAssistAlgo.NONE
        self._mode = AutonomyMode.REMOTE_DRIVER
        self._mode_metric.set(self._mode)
        self._steering_assist_metric.set(self._active_assist)
        self._lock = asyncio.Lock()
        DATA_BUS.register(MessageType.SET_AUTONOMY_REQ, self._set_autonomy_req, AUTH_WRITE_REQ)

        TRACTOR_EVENT_BUS.add_listener(ON_PEER_DISCONNECT, self._on_disconnect)
        TRACTOR_EVENT_BUS.add_listener(STEERING_ASSIST_STOPPED, self._on_assist_stop)
        TRACTOR_EVENT_BUS.add_listener(AUTONOMOUS_TASK_RUNNER_STOPPED, self._on_auto_task_stop)
        TRACTOR_EVENT_BUS.add_listener(TRACTOR_STATE_CHANGE, self._on_tractor_state_change)
        TRACTOR_EVENT_BUS.add_listener(REMOTE_INTERACTION, self._on_remote_interaction)
        TRACTOR_EVENT_BUS.add_listener(BYPASS_UPDATED, self._on_bypass_updated)
        for name, bypass in self._addl_bypasses.items():
            bypass.emitter.watch(partial(self._on_addl_bypass_update, name))

    async def ainit(
        self,
        tractor: TractorIF,
        atim: AtimController,
        ts: TokenStore,
        tree: ConfigTree,
        min_steering_speed: TypedConfigAccessor[float, Speed],
    ) -> None:
        self._tractor = tractor
        self._task_runner = await AutonomousTaskRunner.build(tractor, atim, ts, tree, min_steering_speed)

    def add_assist(self, algo: SteeringEstimatorAlgo) -> None:
        assert algo.algo_id not in self._assists
        self._assists[algo.algo_id] = algo

    def __set_mode(self, mode: AutonomyMode) -> None:
        self._mode = mode
        self._mode_metric.set(self._mode)

    def __set_active_assist(self, assist: SteeringAssistAlgo) -> None:
        self._active_assist = assist
        self._steering_assist_metric.set(self._active_assist)

    @property
    def is_active(self) -> bool:
        return self._mode != AutonomyMode.REMOTE_DRIVER or self._active_assist != SteeringAssistAlgo.NONE

    async def _on_assist_stop(self, msg: str) -> None:
        async with self._lock:
            assert self._mode == AutonomyMode.REMOTE_DRIVER
            self.__set_active_assist(SteeringAssistAlgo.NONE)
        await self._send_autonomy_state()
        await DATA_BUS.broadcast_msg(Message.build(MessageType.REMOTE_ASSIST_REQUIRED, RemoteAssistanceReq(msg=msg)))
        TRACTOR_EVENT_BUS.emit(AUTONOMY_STOPPED_EVENT)

    async def _on_auto_task_stop(self) -> None:
        async with self._lock:
            assert self._mode == AutonomyMode.TASK_AUTONOMY
            self.__set_active_assist(SteeringAssistAlgo.NONE)
            self.__set_mode(AutonomyMode.REMOTE_DRIVER)
        await self._send_autonomy_state()
        msg = "autonomy stopped, intervention is required to continue"
        await DATA_BUS.broadcast_msg(Message.build(MessageType.REMOTE_ASSIST_REQUIRED, RemoteAssistanceReq(msg=msg)))
        TRACTOR_EVENT_BUS.emit(AUTONOMY_STOPPED_EVENT)

    async def _do_disable(self) -> bool:
        changed = False
        async with self._lock:
            if self._mode == AutonomyMode.TASK_AUTONOMY:
                await self._task_runner.disable()
                self.__set_mode(AutonomyMode.REMOTE_DRIVER)
                changed = True
            elif self._mode == AutonomyMode.REMOTE_DRIVER and self._active_assist != SteeringAssistAlgo.NONE:
                await self._assists[self._active_assist].disable()
                self.__set_active_assist(SteeringAssistAlgo.NONE)
                changed = True
            if changed:
                await self._send_autonomy_state()
        return changed

    async def _on_remote_interaction(self, interaction: TractorInteractions) -> None:
        do_disable = False
        async with self._lock:
            if self._mode == AutonomyMode.TASK_AUTONOMY:
                do_disable = True
            elif (
                self._mode == AutonomyMode.REMOTE_DRIVER
                and self._active_assist != SteeringAssistAlgo.NONE
                and TractorInteractions.STEERING in interaction
            ):
                do_disable = True
        if do_disable:
            await self._do_disable()

    async def _on_bypass_updated(self, bypassed: bool) -> None:
        if bypassed:
            if await self._do_disable():
                msg = "a safety system has been bypassed, all forms of autonomy have been disabled"
                await DATA_BUS.broadcast_msg(
                    Message.build(MessageType.REMOTE_ASSIST_REQUIRED, RemoteAssistanceReq(msg=msg))
                )

    async def _on_addl_bypass_update(self, name: str, bypassed: bool) -> None:
        if bypassed and self._addl_bypasses[name].feature_enabled:
            if await self._do_disable():
                msg = f"{name} system has been bypassed, all forms of autonomy have been disabled"
                await DATA_BUS.broadcast_msg(
                    Message.build(MessageType.REMOTE_ASSIST_REQUIRED, RemoteAssistanceReq(msg=msg))
                )

    async def _send_autonomy_state(self) -> None:
        state = ControlState()
        await self.__fill_autonomy_state(state)
        await self.send_update(state)
        assert state.autonomous_state is not None
        AUTONOMOUS_STATE.set(state.autonomous_state)

    async def __fill_autonomy_state(self, states: ControlState) -> None:
        states.autonomous_state = AutonomyMsg(mode=self._mode, steering_assist_algo=self._active_assist)

    async def fill_states(self, states: ControlState) -> None:
        await self.__fill_autonomy_state(states)

    async def _autonomy_change(self, msg: AutonomyMsg) -> Optional[ErrorMsg]:
        try:
            async with self._lock:
                try:
                    if msg.mode == AutonomyMode.TASK_AUTONOMY:
                        if self._active_assist != SteeringAssistAlgo.NONE:
                            await self._assists[self._active_assist].disable()
                            self.__set_active_assist(SteeringAssistAlgo.NONE)
                        self.__set_mode(msg.mode)
                        await self._task_runner.enable()
                    elif msg.mode == AutonomyMode.REMOTE_DRIVER:
                        if self._active_assist != SteeringAssistAlgo.NONE:
                            await self._assists[self._active_assist].disable()
                        self.__set_active_assist(msg.steering_assist_algo)
                        if self._active_assist != SteeringAssistAlgo.NONE:
                            await self._assists[self._active_assist].enable()
                    else:
                        return ErrorMsg("Unknown autonomy mode")
                except Exception as ex:
                    LOG.exception("failed to set new active algo")
                    if isinstance(ex, ErrorMsgException):
                        return ex.error_msg
                    else:
                        return ErrorMsg("Unknown error occurred setting autonomy state")
        finally:
            await self._send_autonomy_state()
        return None

    @empty_sender
    @msg_decoder(AutonomyMsg)
    async def _set_autonomy_req(self, msg: AutonomyMsg) -> Optional[ErrorMsg]:
        if self._tractor.get_state() != HHState.OPERATIONAL:
            return ErrorMsg("Cannot set any form of autonomy when tractor is not in operational state.")
        if msg.mode == AutonomyMode.TASK_AUTONOMY:
            msg.steering_assist_algo = SteeringAssistAlgo.NONE

        if msg.steering_assist_algo != SteeringAssistAlgo.NONE and msg.steering_assist_algo not in self._assists:
            return ErrorMsg(f"Invalid algorithm selection {msg.steering_assist_algo} is not currently supported")
        if msg.mode == AutonomyMode.TASK_AUTONOMY or msg.steering_assist_algo != SteeringAssistAlgo.NONE:
            if BYPASS_STATE.is_bypassed():
                return ErrorMsg("Cannot enable any form of autonomy when a safety system is bypassed.")
            for name, bypass in self._addl_bypasses.items():
                if bypass.feature_enabled and bypass.emitter.current:
                    return ErrorMsg(f"Cannot enable any form of autonomy when a {name} system is bypassed.")
        if msg.mode == self._mode and msg.steering_assist_algo == self._active_assist:
            # No change needed
            return None
        return await self._autonomy_change(msg)

    async def disable(self) -> None:
        changed = False
        try:
            async with self._lock:
                if self._mode == AutonomyMode.TASK_AUTONOMY:
                    await self._task_runner.disable()
                    self.__set_mode(AutonomyMode.REMOTE_DRIVER)
                    changed = True
                if self._active_assist != SteeringAssistAlgo.NONE:
                    changed = True
                    await self._assists[self._active_assist].disable()
                    self.__set_active_assist(SteeringAssistAlgo.NONE)
        finally:
            if changed:
                await self._send_autonomy_state()

    def is_autonomous(self) -> bool:
        return self._mode == AutonomyMode.TASK_AUTONOMY

    async def _on_disconnect(self) -> None:
        await self.disable()

    async def _on_tractor_state_change(self, state: HHState) -> None:
        if state != HHState.OPERATIONAL:
            await self.disable()

    async def __aenter__(self) -> "AutonomousController":
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        pass
