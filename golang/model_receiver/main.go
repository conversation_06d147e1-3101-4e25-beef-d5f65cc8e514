package main

import (
	"context"
	"fmt"
	"net"
	"os/signal"
	"sync"
	"syscall"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/sentry_reporter"
	"github.com/carbonrobotics/robot/golang/model_receiver/services"
	"github.com/carbonrobotics/robot/golang/model_receiver/state"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

func main() {
	customFormatter := new(logrus.TextFormatter)
	customFormatter.TimestampFormat = "2006-01-02 15:04:05.000"
	customFormatter.FullTimestamp = true
	logrus.SetFormatter(customFormatter)

	logrus.Infof("Starting Model Receiver")

	stopCtx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	env, err := environment.GetRobot()
	if err != nil {
		logrus.Fatalln("failed to parse environment:", err)
	}

	configSubscriber := config.NewConfigSubscriber(config.MakeRobotLocalAddr(61001))
	configSubscriber.AddConfigTree("common", "common", "services/common.yaml")
	configSubscriber.Start()
	configSubscriber.WaitUntilReady()

	if configSubscriber.GetConfigNode("common", "environment").GetStringValue() == "production" {
		sentry_reporter.InitializeSentry()
		defer sentry_reporter.HandlePanic()
	}

	// Create GRPC Server
	var opts []grpc.ServerOption
	opts = append(opts, grpc.UnaryInterceptor(sentry_reporter.PanicInterceptor))
	maxMessageBytes := int(1e9) // 1 GB
	opts = append(opts, grpc.MaxRecvMsgSize(maxMessageBytes))
	grpcServer := grpc.NewServer(opts...)

	redisClient := redis.New(env)
	_ = logging.NewLoggingService(grpcServer, redisClient, "model_receiver", &env)

	modelStatus := state.NewModelStatus(env)
	chipStatus := state.NewChipStatus(env)
	modelReceiver := services.NewModelReceiverService(grpcServer, modelStatus, chipStatus)

	// Get model state before starting grpc server
	modelStatus.UpdateModels()
	chipStatus.UpdateChips()

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		modelStatus.RunModelWatcher(stopCtx, modelReceiver.TriggerChannel())
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		chipStatus.RunChipWatcher(stopCtx, modelReceiver.ChipTriggerChannel())
	}()

	addr := fmt.Sprintf("0.0.0.0:%d", 61004)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		logrus.Fatalf("failed to listen: %v", err)
	}

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil {
			logrus.Fatalf("Failed to Serve GRPC Server: %v", err)
		}
	}()

	<-stopCtx.Done()
	grpcServer.Stop()
	wg.Wait()
}
