package web_server

import (
	"context"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/sim_UI"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/generate"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/simulator_controller"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/types"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const streamMaxIdle = 300 // 5 mins

type ServerState struct {
	velocityMPH float64

	idVel int
	idPre int

	velocityCV *sync.Cond
	presetsCV  *sync.Cond

	lock sync.Mutex
}

func NewServerState() *ServerState {
	return &ServerState{
		velocityMPH: 0,
		idVel:       0,
		idPre:       0,
		velocityCV:  sync.NewCond(&sync.Mutex{}),
		presetsCV:   sync.NewCond(&sync.Mutex{}),
	}
}

type SimulatorUIService struct {
	sim_UI.UnimplementedSimulatorUIServiceServer

	simState *simulator_controller.SimulatorState

	state *ServerState

	configNode *config.ConfigTree

	TerminateChannel1 chan bool
	StoppedChannel1   chan bool
}

func NewSimulatorUIService(grpcServer *grpc.Server, configSubscriber *config.ConfigSubscriber,
	simState *simulator_controller.SimulatorState) *SimulatorUIService {
	service := &SimulatorUIService{
		state:    NewServerState(),
		simState: simState,

		TerminateChannel1: make(chan bool, 1),
		StoppedChannel1:   make(chan bool, 1),
		configNode:        configSubscriber.GetConfigNode("simulator", ""),
	}
	sim_UI.RegisterSimulatorUIServiceServer(grpcServer, service)
	service.configNode.RegisterCallback(service.readConfig)
	service.readConfig()
	return service
}

func (s *SimulatorUIService) readConfig() {
	s.notifyVelocity(
		func() {
			s.state.velocityMPH = s.configNode.GetNode("velocity_mph").GetFloatValue()
		},
	)
}

func (s *SimulatorUIService) Start() {
	logrus.Info("gRPC webserver started")
	for {
		select {
		case <-s.TerminateChannel1:
			logrus.Infof("Web Server: Received request to terminate")
			s.StoppedChannel1 <- true
			return
		case <-time.After(time.Duration(streamMaxIdle) * time.Second):
		}
		s.notifyVelocity(func() {})
		s.notifyPresets(func() {})
	}
}

func (s *SimulatorUIService) Stop() {
	s.TerminateChannel1 <- true
	<-s.StoppedChannel1
	logrus.Infof("Web Server: Stopped")
}

func (s *SimulatorUIService) notifyVelocity(updateFunc func()) {
	s.state.velocityCV.L.Lock()
	updateFunc()
	s.state.velocityCV.Broadcast()
	s.state.velocityCV.L.Unlock()
}

func (s *SimulatorUIService) GetVelocityStream(req *sim_UI.GetVelocityRequest,
	stream sim_UI.SimulatorUIService_GetVelocityStreamServer) error {
	s.state.lock.Lock()
	id := s.state.idVel
	s.state.idVel += 1
	s.state.lock.Unlock()
	logrus.Infof("gRPC webserver: New GetVelocity stream with ID: %v", id)
	s.state.velocityCV.L.Lock()
	velocity := s.state.velocityMPH
	s.state.velocityCV.L.Unlock()
	lastUpdate := timeNowSec()
	for {
		err := stream.Send(&sim_UI.GetVelocityResponse{Velocity: float32(velocity)})
		if err != nil {
			stat, _ := status.FromError(err)
			switch stat.Code() {
			case codes.Canceled:
				logrus.Infof("Frontend closed GetVelocity stream with ID: %v", id)
				return nil
			default:
				logrus.Errorf("GetVelocity stream with ID: %v, Error sending metric message %v", id, err)
				return err
			}
		}
		logrus.Infof("GetVelocity stream with ID: %v, sent: %.2v", id, velocity)
		lastUpdate = timeNowSec()
		s.state.velocityCV.L.Lock()
		for velocity == s.state.velocityMPH && timeNowSec()-lastUpdate < streamMaxIdle {
			s.state.velocityCV.Wait()
		}
		velocity = s.state.velocityMPH
		s.state.velocityCV.L.Unlock()
	}
}

func (s *SimulatorUIService) SetVelocity(ctx context.Context, req *sim_UI.SetVelocityRequest) (*sim_UI.SetVelocityResponse, error) {
	if s.simState.Field == nil {
		logrus.Errorf("gRPC webserver: SetVelocity failed (field is nil): %v", req)
		return nil, status.Error(codes.FailedPrecondition, "field is nil")
	}
	velMPH := float64(req.Velocity)
	logrus.Infof("gRPC webserver: SetVelocity: %.2v", velMPH)
	s.notifyVelocity(
		func() {
			s.state.velocityMPH = velMPH
			s.simState.Field.SetVelocity(velMPH)
			s.simState.Gps.SetVelocity(velMPH)
			s.simState.Nofx.SetVelocity(velMPH)
		},
	)
	return &sim_UI.SetVelocityResponse{}, nil
}

func (s *SimulatorUIService) notifyPresets(updateFunc func()) {
	s.state.presetsCV.L.Lock()
	updateFunc()
	s.state.presetsCV.Broadcast()
	s.state.presetsCV.L.Unlock()
}

func (s *SimulatorUIService) GetPresetStream(req *sim_UI.GetPresetsRequest,
	stream sim_UI.SimulatorUIService_GetPresetStreamServer) error {
	if s.simState.Field == nil {
		logrus.Errorf("gRPC webserver: GetPresetStream failed (field is nil): %v", req)
		return status.Error(codes.FailedPrecondition, "field is nil")
	}
	s.state.lock.Lock()
	id := s.state.idPre
	s.state.idPre += 1
	s.state.lock.Unlock()
	logrus.Infof("gRPC webserver: New GetPreset stream with ID: %v", id)
	presetMap := generate.NewPresetMap()
	s.state.presetsCV.L.Lock()
	presetMap.Sync(s.simState.Field.PresetMap)
	s.state.presetsCV.L.Unlock()
	lastUpdate := timeNowSec()
	for {
		resp := &sim_UI.GetPresetsResponse{
			Settings: presetMap.ToMessage(),
		}
		err := stream.Send(resp)
		if err != nil {
			stat, _ := status.FromError(err)
			switch stat.Code() {
			case codes.Canceled:
				logrus.Infof("Frontend closed GetPreset stream with ID: %v", id)
				return nil
			default:
				logrus.Errorf("GetPreset stream with ID: %v, Error sending metric message %v", id, err)
				return err
			}
		}
		logrus.Infof("GetPreset stream with ID: %v, sent presets.", id)
		lastUpdate = timeNowSec()
		s.state.presetsCV.L.Lock()
		for presetMap.Equal(s.simState.Field.PresetMap) && timeNowSec()-lastUpdate < streamMaxIdle {
			s.state.presetsCV.Wait()
		}
		presetMap.Sync(s.simState.Field.PresetMap)
		s.state.presetsCV.L.Unlock()
	}
}

func (s *SimulatorUIService) SetPreset(ctx context.Context, req *sim_UI.SetPresetRequest) (*sim_UI.SetPresetResponse, error) {
	if s.simState.Field == nil {
		logrus.Errorf("gRPC webserver: SetPreset failed (field is nil): %v", req)
		return nil, status.Error(codes.FailedPrecondition, "field is nil")
	}
	logrus.Infof("gRPC webserver: SetPreset: %v", req)
	path := req.Setting.Path
	value := req.Setting.Value
	var err error
	s.notifyPresets(
		func() {
			err = s.simState.Field.SetPreset(path, value)
		},
	)
	if err != nil {
		logrus.WithError(err).Errorf("gRPC webserver: SetPreset failed: %v", req)
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	return &sim_UI.SetPresetResponse{}, nil
}

func (s *SimulatorUIService) GetDiagnosticSettings(ctx context.Context, req *sim_UI.GetDiagnosticSettingsRequest) (*sim_UI.GetDiagnosticSettingsResponse, error) {
	if s.simState.Mode.Load() != types.Replay {
		logrus.Errorf("gRPC webserver: GetDiagnosticSettings failed (mode is not Replay): %v", req)
		return nil, status.Error(codes.FailedPrecondition, "Mode is not Replay")
	}
	settings, err := s.simState.Replayer.GetNextDiagnosticState(ctx, req)
	if err != nil {
		logrus.WithError(err).Errorf("gRPC webserver: GetDiagnosticSettings failed: %v", req)
		if err == context.DeadlineExceeded {
			return nil, status.Error(codes.Canceled, "Timeout or terminated")
		}
		return nil, err
	}
	logrus.Infof("gRPC webserver: GetDiagnosticSettings: %v", settings)

	return &sim_UI.GetDiagnosticSettingsResponse{Settings: settings}, nil
}

func (s *SimulatorUIService) SetDiagnosticSettings(ctx context.Context, req *sim_UI.SetDiagnosticSettingsRequest) (*sim_UI.SetDiagnosticSettingsResponse, error) {
	if s.simState.Mode.Load() != types.Replay {
		logrus.Errorf("gRPC webserver: SetDiagnosticSettings failed (mode is not Replay): %v", req)
		return nil, status.Error(codes.FailedPrecondition, "Mode is not Replay")
	}
	row := int(req.Row)
	currentDiagnostic := req.CurrentDiagnostic
	logrus.Infof("gRPC webserver: SetDiagnosticSettings: %v", req)
	s.simState.Replayer.UpdateDiagnosticState()
	// reload the replay
	s.simState.ReloadWithParams(row, currentDiagnostic)
	return &sim_UI.SetDiagnosticSettingsResponse{}, nil
}

func timeNowSec() int64 {
	return time.Now().UnixMilli() / 1000
}
