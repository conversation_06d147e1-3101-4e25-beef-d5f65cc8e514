import concurrent
import concurrent.futures
import json
import logging
import os
import threading
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, cast

import numpy as np
import pandas
import torch
from tqdm import tqdm

from deeplearning.comparison.data_utils import (
    get_comparison_evaluation_prefix,
    get_images_with_comparison_embeddings,
    load_embeddings_from_torch,
)
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import (
    DATASET_SAMPLING_ALGORITHM_CATEGORY_EMBEDDINGS,
    DATASET_SAMPLING_ALGORITHM_DEFAULT,
    DATASET_SAMPLING_ALGORITHM_DRIPTAPE,
    DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS,
    DATASET_SAMPLING_ALG<PERSON>ITHM_EMBEDDINGS,
    DATASET_SAMPLING_ALGORITHM_GEOHASH_DATE_CATEGORY_BUCKETS,
    DATASET_SAMPLING_ALGORITHM_GEOHASH_MONTH,
    DATASET_SAMPLING_ALGORITHM_GEOHASH_MONTH_HIT,
    DATASET_SAMPLING_ALGORITHM_UNIFORM,
)
from deeplearning.deepweed.datapoint_timestamps import DatapointTimestamps
from deeplearning.deepweed.dataset_utils import (
    EmbeddingSampler,
    GeohashMonthBucket,
    GeohashMonthCategoryBucket,
    get_embedding_index,
)
from deeplearning.dl_metrics.dl_metrics.points_db import EmbeddingType
from deeplearning.utils.dataset import DatasetType
from deeplearning.utils.trainer import Environment
from lib.common.perf.perf_tracker import (
    duration_perf_recorder_decorator,
    set_autowrite_filename,
    set_autowrite_frequency,
    set_verbosity,
)
from lib.common.veselka.client import VeselkaClient

set_verbosity(False)
LOG = logging.getLogger(__name__)


def create_hash_key_for_embedding_lookup(x: float, y: float, round_digits: Optional[int] = 3) -> Tuple[float, float]:
    # The hash_key is used for efficiently retrieving specific points.
    if round_digits is not None:
        hash_key = (
            round(x, round_digits),
            round(y, round_digits),
        )
    else:
        hash_key = (x, y)

    return hash_key


class RemoteVeselkaDataset:
    @duration_perf_recorder_decorator("Training")
    def __init__(  # noqa: C901
        self,
        config: DeepweedConfig,
        filepath: str,
        mode: DatasetType,
        segm_classes: Optional[Tuple[str, ...]] = None,
        weed_classes: Optional[Tuple[str, ...]] = None,
        crop_classes: Optional[Tuple[str, ...]] = None,
        num_samples: Optional[int] = None,
        camera: Optional[str] = None,
        train_ppi: Optional[int] = 200,
        dilate_mask: int = 0,
        keep_low_confidence: bool = True,
        seed: int = 1,
        new_loss_multiplier: float = 1.0,
        goal_percentage_new: Optional[float] = None,
        positive_sample_percentage: float = 10 / 11,
        weed_sample_percentage: float = 1 / 2,
        recency_split: Optional[int] = None,
        recency_split_age: Optional[int] = None,
        is_new_func: Optional[Callable[[Dict[str, Any]], bool]] = None,
        duplicate_new_data: Optional[float] = None,
        geohash_max_precision: int = 0,
        geohash_min_precision: int = 0,
        use_date_groups: bool = False,
        discard_points_border_in: float = 0.5,
        embedding_balancing_evaluation_path: Optional[str] = None,
        embedding_type: EmbeddingType = EmbeddingType.REDUCED_SCALED,
        embedding_balancing_on_clusters: bool = False,
    ):
        self._config = config
        self._filepath = filepath
        self._mode = mode
        self._segm_classes = set(segm_classes or ())
        self._weed_classes = set(weed_classes or ())
        self._crop_classes = set(crop_classes or ())
        self._num_samples = num_samples
        self._camera = camera
        self._train_ppi = train_ppi
        self._dilate_mask = dilate_mask
        self._keep_low_confidence = keep_low_confidence
        self._seed = seed
        self._new_loss_multiplier = new_loss_multiplier
        self._goal_percentage_new = goal_percentage_new
        self._positive_sample_percentage = positive_sample_percentage
        self._new_data_captured_ats: List[DatapointTimestamps] = []
        self._recency_split = recency_split
        self._recency_split_age = recency_split_age
        self._geohash_max_precision = geohash_max_precision
        self._geohash_min_precision = geohash_min_precision
        assert self._geohash_min_precision <= self._geohash_max_precision
        self._weed_sample_percentage = weed_sample_percentage
        self._discard_points_border_px = (self._train_ppi or 0) * discard_points_border_in
        self._embedding_type = embedding_type
        self._embedding_balancing_evaluation_path = embedding_balancing_evaluation_path
        self._embedding_balancing_on_clusters = embedding_balancing_on_clusters
        self._client = VeselkaClient()  # Just for getting point categories and crop IDs

        if self._embedding_balancing_evaluation_path is not None and not embedding_balancing_on_clusters:
            self._embedding_balancing_evaluation_path = os.path.join(
                self._embedding_balancing_evaluation_path, self._embedding_type.name.lower()
            )

        self._images_with_comparison_embeddings: Set[str] = set()

        self.comparison_embeddings_loaded = False
        self.sampling_embeddings_loaded = False

        self.comparison_embeddings_loaded = False
        self.sampling_embeddings_loaded = False

        self._segm_classes = set(tuple(category.upper() for category in self._segm_classes))
        self._weed_classes = set(tuple(category.upper() for category in self._weed_classes))
        self._crop_classes = set(tuple(category.upper() for category in self._crop_classes))
        self._enabled_classes = list(
            set(list(self._weed_classes) + list(self._crop_classes) + list(self._segm_classes))
        )

        set_autowrite_filename(f"{CARBON_DATA_DIR}/deeplearning/models/{config.model_id}/timing/dataset_{os.getpid()}")
        set_autowrite_frequency(60000)

        self._x_buckets = config.embedding_x_buckets
        self._y_buckets = config.embedding_y_buckets

        self.carbon_cache_host: Optional[str] = None

        if os.getenv("DISABLE_S3_CACHE_PROXY"):
            LOG.warning("Running without s3 cache proxy!")
        else:
            self.carbon_cache_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")

        if self.carbon_cache_host is None:
            self._tmp_data = "/data/temp_stream_for_datasets"
            try:
                os.mkdir(self._tmp_data)
            except FileExistsError:
                pass

        # Instantiate a random number generator based on the dataset seed.
        self._rng = np.random.default_rng(self._seed)

        self.image_id2annotations: Dict[str, List[Any]] = {}
        self.image_id2last_updated_annotation_time: Dict[str, Optional[int]] = {}
        self.image_url2id: Dict[str, str] = {}
        self._image_id_to_embedding_indices: Dict[str, Dict[Tuple[float, float], Dict[str, Any]]] = defaultdict(dict)
        geohash_month_buckets_positive_categories: Dict[GeohashMonthBucket, List[str]] = {}

        # For dataset V2 sampling
        self._certified_crop_ids = set()
        self._image_id2crop_id: Dict[str, str] = {}

        if self._config.dataset_version == 2:
            with open(self._filepath) as f:
                self._data2: List[Dict[str, Any]] = [json.loads(line) for line in f]
            self.image_url2id = {image["uri"]: image["image_id"] for image in self._data2}
            self.image_id2annotations = {image["image_id"]: image["points"] for image in self._data2}
            self._image_id2crop_id = {image["image_id"]: image["crop_id"] for image in self._data2}

            pc_metadata = self._client.get_point_categories()

            self._point_category_to_name = {pc["id"]: pc["name"].upper() for pc in pc_metadata}
            self._point_category_to_name.update({k.upper(): v for k, v in self._point_category_to_name.items()})

            self._name_to_point_category = {v: k for k, v in self._point_category_to_name.items()}

            crop_metadata = self._client.get_all_crop_metadata()
            self._crop_id_to_name = {crop["id"]: crop["carbon_name"] for crop in crop_metadata}

            self._categories = tuple(
                set(sorted(list(self._weed_classes) + list(self._crop_classes) + list(self._segm_classes)))
            )
            num_images = len(self._data2)

            self.image_id2last_updated_annotation_time = {  # Just captured at time for now
                image["image_id"]: image["captured_at"] for image in self._data2
            }

            LOG.info(f"Initializing dataset with the following categories: {json.dumps(self._categories, indent=4)}")
            LOG.info(f"Dataset contains {num_images} datapoints")

            all_certified_categories = set()
            for index, datapoint in enumerate(self._data2):
                catdata = json.loads(datapoint["categories"]["data"])
                for category, info in catdata["crownLabels"].items():
                    if "crop" in info:
                        all_certified_categories.add("CROP")
                        self._certified_crop_ids.add(datapoint["crop_id"])
                    else:
                        all_certified_categories.add(category.upper())
                if "polygonLabels" in catdata:
                    for polygon_category in catdata["polygonLabels"].keys():
                        all_certified_categories.add(polygon_category.upper())

            metadata = []
            self._new_datapoints = []
            self._old_datapoints = []
            self.is_new_func = is_new_func
            self._use_date_groups = use_date_groups

            geohash_month_category_buckets = []
            geohash_month_buckets = []
            geohash_month_buckets_positive_categories = {}

            for index, datapoint in enumerate(self._data2):
                if is_new_func is not None:
                    is_new = is_new_func(datapoint)
                else:
                    is_new = datapoint["is_new"]

                datapoint["crop"] = self._crop_id_to_name.get(datapoint["crop_id"], "UNKNOWN")

                datapoint_metadata = {
                    "url": datapoint["uri"],
                    "crop": self._crop_id_to_name.get(datapoint["crop_id"], "UNKNOWN"),
                    "timestamp": datapoint["captured_at"],
                    "date": pandas.to_datetime(datapoint["captured_at"], unit="ms").date,
                    "month": pandas.to_datetime(datapoint["captured_at"], unit="ms").month,
                    "city": datapoint["geohash"][:4],
                    "is_new": is_new,
                    "index": index,
                    "crop_id": datapoint["crop_id"],
                    "geohash": datapoint["geohash"],
                    "date_group_id": datapoint.get("date_group_id"),
                    "geohash_bucket": datapoint["geohash"][: self._config.sampling_geohash_precision],
                }

                self._use_date_groups = self._use_date_groups and datapoint.get("date_group_id", None) is not None

                if self._geohash_max_precision > 0:
                    if datapoint["geohash"] is not None:
                        datapoint_metadata[f"geohash_{self._geohash_min_precision}"] = datapoint["geohash"][
                            : self._geohash_min_precision
                        ]
                    else:
                        datapoint_metadata[f"geohash_{self._geohash_min_precision}"] = None
                    for i in range(self._geohash_min_precision, self._geohash_max_precision):
                        if datapoint["geohash"] is not None:
                            datapoint_metadata[f"geohash_{i+1}"] = datapoint["geohash"][i]
                        else:
                            datapoint_metadata[f"geohash_{i+1}"] = None

                if is_new:
                    self._new_datapoints.append(datapoint["uri"])
                else:
                    self._old_datapoints.append(datapoint["uri"])

                certified_categories = []
                catdata = json.loads(datapoint["categories"]["data"])
                if "polygonLabels" in catdata:
                    for polygon_category in catdata["polygonLabels"].keys():
                        certified_categories.append(polygon_category.upper())
                for point_category, d in catdata["crownLabels"].items():
                    certified_categories.append(point_category.upper() if "crop" not in d else "CROP")
                certified_categories = list(set(certified_categories))

                crop_sizes = []
                present_categories = []
                for annotation in datapoint["points"]:
                    annotation["relocation_info"] = {
                        "image_s3_url": datapoint["uri"],
                        "image_id": datapoint["image_id"],
                        "x": annotation["x"],
                        "y": annotation["y"],
                    }

                    if annotation.get("confidence", 2) == 0:
                        continue

                    annotation["class"] = self._point_category_to_name.get(annotation["point_category_id"])

                    present_categories.append(annotation["class"])
                    crop_sizes.append(annotation["radius"])

                for polygon in datapoint["polygons"]:
                    present_categories.append(polygon["label"].upper())

                if len(crop_sizes) == 0:
                    average_crop_size = 0
                else:
                    average_crop_size = np.array(crop_sizes).mean() * (25.4 / 200.0)

                (a, b) = self._config.size_balancing_partitions
                if average_crop_size < a:
                    size = "small"
                elif average_crop_size > a and average_crop_size < b:
                    size = "medium"
                else:
                    size = "large"

                datapoint_metadata["size"] = size
                present_categories = list(set(present_categories))

                for category in all_certified_categories:
                    if category in certified_categories:
                        if category in present_categories:
                            datapoint_metadata[category] = True
                            if (
                                category in self._crop_classes
                                and datapoint_metadata["crop_id"] in self._certified_crop_ids
                            ):
                                category = datapoint_metadata["crop_id"]
                            geohash_month_category_buckets.append(
                                GeohashMonthCategoryBucket(
                                    datapoint_metadata["geohash_bucket"], datapoint_metadata["month"], category,
                                )
                            )

                            geohash_month_bucket = GeohashMonthBucket(
                                datapoint_metadata["geohash_bucket"], datapoint_metadata["month"]
                            )

                            geohash_month_buckets.append(geohash_month_bucket)

                            if geohash_month_bucket in geohash_month_buckets_positive_categories:
                                geohash_month_buckets_positive_categories[geohash_month_bucket].append(category)
                            else:
                                geohash_month_buckets_positive_categories[geohash_month_bucket] = [category]

                        else:
                            datapoint_metadata[category] = False
                    else:
                        datapoint_metadata[category] = None

                metadata.append(datapoint_metadata)

                if is_new:
                    datapoint_timestamps = DatapointTimestamps(
                        image_captured_timestamp_ms=datapoint["captured_at"],
                        label_updated_timestamp_ms=self.image_id2last_updated_annotation_time[datapoint["image_id"]],
                    )
                    self._new_data_captured_ats.append(datapoint_timestamps)

            if self._use_date_groups:
                LOG.info("Using date groups for grouping.")
            else:
                LOG.info("Not using date groups for grouping.")

            self._geohash_indices = []
            if self._geohash_max_precision > 0:
                self._geohash_indices = [f"geohash_{self._geohash_min_precision}"]
                for i in range(self._geohash_min_precision, self._geohash_max_precision):
                    self._geohash_indices.append(f"geohash_{i + 1}")

            columns = (
                [
                    "url",
                    "crop",
                    "timestamp",
                    "date",
                    "month",
                    "city",
                    "is_new",
                    "index",
                    "crop_id",
                    "geohash",
                    "date_group_id",
                    "size",
                    "geohash_bucket",
                ]
                + self._geohash_indices
                + list(all_certified_categories)
            )
            self._metadata = pandas.DataFrame(metadata, columns=columns)

        elif self._config.dataset_version == 1:
            # Read in the dataset json object
            with open(self._filepath) as f:
                self._data: Dict[str, Any] = json.load(f)

            # Add a class key to all points. It seems that sometimes we use class instead of label. I think this change is
            # made in the metadata file.
            for annotation in self._data["annotations"]:
                if annotation["annotation_type"] == "point":
                    annotation["class"] = annotation["label"]

            self.image_id2annotations = {image["id"]: [] for image in self._data["images"]}
            self.image_id2last_updated_annotation_time = {image["id"]: None for image in self._data["images"]}

            image_id2url: Dict[str, str] = {image["id"]: image["uri"] for image in self._data["images"]}
            self.image_url2id = {image["uri"]: image["id"] for image in self._data["images"]}

            total_points = 0
            for annotation in self._data["annotations"]:
                if annotation["image_id"] in self.image_id2annotations:
                    # Get embedding if it exists for this annotation
                    if annotation["annotation_type"] == "point":
                        total_points += 1
                        annotation["relocation_info"] = {
                            "image_s3_url": image_id2url[annotation["image_id"]],
                            "image_id": annotation["image_id"],
                            "x": annotation["x"],
                            "y": annotation["y"],
                        }

                    self.image_id2annotations[annotation["image_id"]].append(annotation)
                    if "last_updated" in annotation:
                        if self.image_id2last_updated_annotation_time[annotation["image_id"]] is None:
                            self.image_id2last_updated_annotation_time[annotation["image_id"]] = cast(
                                int, annotation["last_updated"]
                            )
                        else:
                            self.image_id2last_updated_annotation_time[annotation["image_id"]] = max(
                                cast(int, self.image_id2last_updated_annotation_time[annotation["image_id"]]),
                                cast(int, annotation["last_updated"]),
                            )

            # Build metadata dataframe
            # This dataframe will have the s3 url, the timestamp, the crop, boolean category columns, the city, and the
            # index in the data list.
            categories = []
            for category in self._segm_classes:
                categories.append(category.upper())
            for category in self._weed_classes:
                categories.append(category.upper())
            for category in self._crop_classes:
                categories.append(category.upper())

            self._categories = tuple(set(sorted(categories)))

            num_images = len(self._data["images"])
            LOG.info(f"Initializing dataset with the following categories: {json.dumps(self._categories, indent=4)}")
            LOG.info(f"Dataset contains {num_images} datapoints")

            all_certified_categories = set()
            for index, datapoint in enumerate(self._data["images"]):
                for polygon_category in datapoint["polygon_categories"]:
                    all_certified_categories.add(polygon_category.upper())
                for point_category in datapoint["point_categories"]:
                    all_certified_categories.add(point_category.upper())
            for category in self._categories:
                all_certified_categories.add(category.upper())

            metadata = []
            self._new_datapoints = []
            self._old_datapoints = []
            self.is_new_func = is_new_func
            self._use_date_groups = use_date_groups

            geohash_month_category_buckets = []
            geohash_month_buckets = []
            geohash_month_buckets_positive_categories = {}

            num_images = len(self._data["images"])

            for index, datapoint in enumerate(self._data["images"]):
                if is_new_func is not None:
                    is_new = is_new_func(datapoint)
                else:
                    is_new = datapoint["is_new"]
                datapoint_metadata = {
                    "url": datapoint["uri"],
                    "crop": datapoint["crop"],
                    "timestamp": datapoint["captured_at"],
                    "date": pandas.to_datetime(datapoint["captured_at"], unit="ms").date,
                    "month": pandas.to_datetime(datapoint["captured_at"], unit="ms").month,
                    "city": datapoint["city"],
                    "is_new": is_new,
                    "index": index,
                    "crop_id": datapoint["crop_id"],
                    "geohash": datapoint["geohash"],
                    "date_group_id": datapoint.get("date_group_id"),
                    "geohash_bucket": datapoint["geohash"][: self._config.sampling_geohash_precision],
                }

                # If groups don't exist, don't use date groups
                self._use_date_groups = self._use_date_groups and datapoint.get("date_group_id") is not None

                if self._geohash_max_precision > 0:
                    if datapoint["geohash"] is not None:
                        datapoint_metadata[f"geohash_{self._geohash_min_precision}"] = datapoint["geohash"][
                            : self._geohash_min_precision
                        ]
                    else:
                        datapoint_metadata[f"geohash_{self._geohash_min_precision}"] = None
                    for i in range(self._geohash_min_precision, self._geohash_max_precision):
                        if datapoint["geohash"] is not None:
                            datapoint_metadata[f"geohash_{i+1}"] = datapoint["geohash"][i]
                        else:
                            datapoint_metadata[f"geohash_{i+1}"] = None

                if is_new:
                    self._new_datapoints.append(datapoint["uri"])
                else:
                    self._old_datapoints.append(datapoint["uri"])

                certified_categories = []
                for polygon_category in datapoint["polygon_categories"]:
                    certified_categories.append(polygon_category.upper())
                for point_category in datapoint["point_categories"]:
                    certified_categories.append(point_category.upper())
                certified_categories = list(set(certified_categories))

                crop_sizes = []
                present_categories = []
                for annotation in self.image_id2annotations[datapoint["id"]]:
                    if annotation.get("confidence", 2) == 0:
                        continue

                    present_categories.append(annotation["label"].upper())

                    if annotation["annotation_type"] == "point":
                        crop_sizes.append(annotation["radius"])

                if len(crop_sizes) == 0:
                    average_crop_size = 0
                else:
                    average_crop_size = np.array(crop_sizes).mean() * (25.4 / 200.0)

                (a, b) = self._config.size_balancing_partitions
                if average_crop_size < a:
                    size = "small"
                elif average_crop_size > a and average_crop_size < b:
                    size = "medium"
                else:
                    size = "large"

                datapoint_metadata["size"] = size
                present_categories = list(set(present_categories))

                for category in all_certified_categories:
                    if category in certified_categories:
                        if category in present_categories:
                            if category in self._crop_classes:
                                datapoint_metadata["CROP"] = True
                            else:
                                datapoint_metadata[category] = True
                            geohash_month_category_buckets.append(
                                GeohashMonthCategoryBucket(
                                    datapoint_metadata["geohash_bucket"], datapoint_metadata["month"], category,
                                )
                            )

                            geohash_month_bucket = GeohashMonthBucket(
                                datapoint_metadata["geohash_bucket"], datapoint_metadata["month"]
                            )

                            geohash_month_buckets.append(geohash_month_bucket)

                            if geohash_month_bucket in geohash_month_buckets_positive_categories.keys():
                                geohash_month_buckets_positive_categories[geohash_month_bucket].append(category)
                            else:
                                geohash_month_buckets_positive_categories[geohash_month_bucket] = [category]
                        else:
                            if category in self._crop_classes:
                                if "CROP" in datapoint_metadata:
                                    continue
                                datapoint_metadata["CROP"] = False
                            else:
                                datapoint_metadata[category] = False
                    else:
                        if category in self._crop_classes:
                            if "CROP" in datapoint_metadata:
                                continue
                            datapoint_metadata["CROP"] = None
                        else:
                            datapoint_metadata[category] = None
                metadata.append(datapoint_metadata)
                if is_new:
                    datapoint_timestamps = DatapointTimestamps(
                        image_captured_timestamp_ms=datapoint["captured_at"],
                        label_updated_timestamp_ms=self.image_id2last_updated_annotation_time[datapoint["id"]],
                    )
                    self._new_data_captured_ats.append(datapoint_timestamps)

            if self._use_date_groups:
                LOG.info("Using date groups for grouping.")
            else:
                LOG.info("Not using date groups for grouping.")

            self._geohash_indices = []
            if self._geohash_max_precision > 0:
                self._geohash_indices = [f"geohash_{self._geohash_min_precision}"]
                for i in range(self._geohash_min_precision, self._geohash_max_precision):
                    self._geohash_indices.append(f"geohash_{i + 1}")

            all_certified_categories = set(
                [category for category in all_certified_categories if category not in self._crop_classes] + ["CROP"]
            )
            self._certified_crop_ids = set(
                [category for category in all_certified_categories if category in self._crop_classes]
            )

            columns = (
                [
                    "url",
                    "crop",
                    "timestamp",
                    "date",
                    "month",
                    "city",
                    "is_new",
                    "index",
                    "crop_id",
                    "geohash",
                    "date_group_id",
                    "size",
                    "geohash_bucket",
                ]
                + self._geohash_indices
                + list(all_certified_categories)
            )
            self._metadata = pandas.DataFrame(metadata, columns=columns)

        LOG.info(f"Metadata crop count: {self._metadata.CROP.value_counts()}")

        # Generate positive and negative category lists
        self._positive_categories = []
        self._negative_categories = []
        self._positive_categories_in_new = []
        self._negative_categories_in_new = []
        self._positive_categories_in_old = []
        self._negative_categories_in_old = []
        categories_set = set(
            [category for category in self._categories if category not in self._crop_classes] + ["CROP"]
        )

        for category in all_certified_categories:
            # only use categories we are training on for positive categories
            if category in categories_set:

                positive_examples = self._metadata[self._metadata[category].isin([True])]
                positive_count = positive_examples.shape[0]
                positive_in_new_count = positive_examples[positive_examples["is_new"].isin([True])].shape[0]
                positive_in_old_count = positive_examples[positive_examples["is_new"].isin([False])].shape[0]

                if positive_count > 0:
                    self._positive_categories.append(category)

                if positive_in_new_count > 0:
                    self._positive_categories_in_new.append(category)

                if positive_in_old_count > 0:
                    self._positive_categories_in_old.append(category)

            # All certified classes can be negative categories to sample over
            negative_examples = self._metadata[self._metadata[category].isin([False])]
            negative_count = negative_examples.shape[0]
            negative_in_new_count = negative_examples[negative_examples["is_new"].isin([True])].shape[0]
            negative_in_old_count = negative_examples[negative_examples["is_new"].isin([False])].shape[0]

            if negative_count > 0:
                self._negative_categories.append(category)

            if negative_in_new_count > 0:
                self._negative_categories_in_new.append(category)

            if negative_in_old_count > 0:
                self._negative_categories_in_old.append(category)

        if duplicate_new_data is not None:
            percentage_new_data = len(self._new_datapoints) / (len(self._new_datapoints) + len(self._old_datapoints))
            self._goal_percentage_new = min(1.0, duplicate_new_data * percentage_new_data)
            LOG.info(
                f"Duplicating new data. New datapoints: {len(self._new_datapoints)}, old datapoints: {len(self._old_datapoints)}. Percentage of new data: {percentage_new_data}, goal percentage new: {self._goal_percentage_new}"
            )

        if self._config.size_balancing:
            small = self._metadata[self._metadata["size"] == "small"].shape[0]
            medium = self._metadata[self._metadata["size"] == "medium"].shape[0]
            large = self._metadata[self._metadata["size"] == "large"].shape[0]
            LOG.info(f"Images per size group: small={small}, medium={medium}, large={large}")

        self._geohash_month_category_buckets = list(set(geohash_month_category_buckets))

        row_category_counts = self._metadata[all_certified_categories].sum(axis=1)
        self._sample_probs = row_category_counts / row_category_counts.sum()
        self._all_certified_categories = all_certified_categories
        self._geohash_month_buckets_with_dupes = geohash_month_buckets
        self._geohash_month_buckets = list(set(geohash_month_buckets))
        self._geohash_month_buckets_positive_categories = {
            key: list(set(value)) for key, value in geohash_month_buckets_positive_categories.items()
        }
        self._embedding_sampler: EmbeddingSampler = EmbeddingSampler(self._goal_percentage_new)

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def load_comparison_evaluations(self) -> None:
        # This call will be used to set comparison embedding information. It intentionally uses the config comparison model id to prevent
        # anyone from accidentally mixing comparison models
        assert self._config.comparison_model_id is not None
        self._images_with_comparison_embeddings = set()
        if not self._config.fast_run:
            self._images_with_comparison_embeddings, _ = get_images_with_comparison_embeddings(
                self._config.comparison_model_id
            )

        for image_id, annotations in self.image_id2annotations.items():
            if image_id not in self._images_with_comparison_embeddings:
                continue

            for annotation in annotations:
                annotation[
                    "comparison_embedding_key"
                ] = f"{get_comparison_evaluation_prefix(self._config.comparison_model_id, Environment.PRODUCTION)}/{image_id}.pt"

        self.comparison_embeddings_loaded = True

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def load_sampling_embeddings(self) -> None:
        assert self._embedding_balancing_evaluation_path is not None
        url_to_metadata = defaultdict(list)
        index_to_metadata: Dict[int, pandas.DataFrame] = {}

        for row in self._metadata.itertuples():
            image_id = self.image_url2id[row.url]
            url_to_metadata[row.url].append(row._asdict())
            index_to_metadata[row.index] = row
            embedding_filepath = os.path.join(self._embedding_balancing_evaluation_path, f"{image_id}.pt")
            if os.path.exists(embedding_filepath):
                embs = load_embeddings_from_torch(embedding_filepath)
                if self._embedding_balancing_on_clusters:
                    for i in range(embs["embeddings"].shape[0]):
                        embedding_meta = embs["embeddings_data"][i]
                        embedding_cluster_index = embs["embeddings"][i]
                        hash_key = create_hash_key_for_embedding_lookup(x=embedding_meta["x"], y=embedding_meta["y"])
                        self._image_id_to_embedding_indices[image_id][hash_key] = {
                            "row": row.index,
                            "x": embedding_meta["x"],
                            "y": embedding_meta["y"],
                            "embedding_ind": (embedding_cluster_index.item(),),
                        }
                else:
                    bounded_embeddings = torch.clamp(embs["embeddings"], min=0.0, max=1.0)
                    for i in range(bounded_embeddings.shape[0]):
                        embedding = bounded_embeddings[i]
                        embedding_meta = embs["embeddings_data"][i]
                        emb_ind = get_embedding_index(
                            x=embedding[0].item(),
                            y=embedding[1].item(),
                            x_buckets=self._x_buckets,
                            y_buckets=self._y_buckets,
                        )
                        hash_key = create_hash_key_for_embedding_lookup(x=embedding_meta["x"], y=embedding_meta["y"])
                        self._image_id_to_embedding_indices[image_id][hash_key] = {
                            "row": row.index,
                            "x": embedding_meta["x"],
                            "y": embedding_meta["y"],
                            "embedding_ind": emb_ind,
                        }
        last_bucket: Tuple[int, ...] = (-1,)
        if not self._embedding_balancing_on_clusters:
            last_bucket = tuple(x * y for x, y in zip(self._x_buckets, self._y_buckets))

        url_to_metadata_combined = {url: pandas.DataFrame(records) for url, records in url_to_metadata.items()}
        logging.info(f"url_to_metadata_combined with size: {len(url_to_metadata_combined)}.")

        lock = threading.Lock()
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(32, (os.cpu_count() or 1) * 5)) as executor:
            futures = [
                executor.submit(
                    self.process_image_annotations,
                    im_id=im_id,
                    anns=anns,
                    url_to_metadata_combined=url_to_metadata_combined,
                    last_bucket=last_bucket,
                    index_to_metadata=index_to_metadata,
                )
                for im_id, anns in self.image_id2annotations.items()
            ]
            with tqdm(total=len(futures), desc="Parallel annotation processing") as pbar:
                for future in concurrent.futures.as_completed(futures):
                    results = future.result()
                    with lock:
                        for result in results:
                            self._embedding_sampler.add(
                                embedding_bucket_index=result["embedding_ind"],
                                row_index=result["row"],
                                category=result["category"],
                                is_emphasized=result["is_new"],
                                use_category_as_key=self._config.sampling_algorithm == "category_embeddings",
                            )
                    pbar.update(1)

        self._embedding_sampler.convert_items_container()
        self.sampling_embeddings_loaded = True

        LOG.info(
            f"self._metadata_index_embedding_indices_to_categories: "
            f"{len(self._embedding_sampler._metadata_index_embedding_indices_to_categories)}"
        )

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def process_image_annotations(
        self,
        im_id: str,
        anns: List[Any],
        url_to_metadata_combined: Dict[str, Any],
        last_bucket: Tuple[int, ...],
        index_to_metadata: Dict[int, pandas.DataFrame],
    ) -> List[Dict[str, Any]]:
        updates = []
        for ann in anns:
            if self._config.dataset_version == 1 and ann["annotation_type"] != "point":
                continue

            embedding_index_items = self._image_id_to_embedding_indices.get(im_id, {})
            matched = False

            category = (
                ann["label"]
                if self._config.dataset_version == 1
                else self._point_category_to_name[ann["point_category_id"]]
            )

            if self._config.dataset_version == 2 and category == "CROP":
                if self._image_id2crop_id[im_id] in self._certified_crop_ids:
                    category = self._image_id2crop_id[im_id]
                else:
                    continue

            for hash_key in [
                create_hash_key_for_embedding_lookup(x=ann["x"], y=ann["y"]),
                create_hash_key_for_embedding_lookup(x=ann["y"], y=ann["x"]),
            ]:
                if embedding_index_items.get(hash_key, None) is not None:
                    embedding_item = embedding_index_items[hash_key]
                    assert embedding_item["embedding_ind"] is not None
                    ann["deepweed_embedding_bucket"] = embedding_item["embedding_ind"]

                    updates.append(
                        {
                            "embedding_ind": embedding_item["embedding_ind"],
                            "row": embedding_item["row"],
                            "category": category,
                            "is_new": self._metadata.iloc[embedding_item["row"]]["is_new"]
                            if self._goal_percentage_new
                            else None,
                        }
                    )
                    matched = True
                    break

            if not matched:
                url_match_data = url_to_metadata_combined[ann["relocation_info"]["image_s3_url"]]
                assert url_match_data.shape[0] <= 1, f"{url_match_data.shape[0]}, {url_match_data['index']}"
                index = url_match_data["index"].iloc[0]

                updates.append(
                    {
                        "embedding_ind": last_bucket,
                        "row": index,
                        "category": category,
                        "is_new": index_to_metadata[index]["is_new"] if self._goal_percentage_new else None,
                    }
                )
                ann["deepweed_embedding_bucket"] = last_bucket

        return updates

    @duration_perf_recorder_decorator("Training")
    def set_seed(self, seed: int) -> None:
        self._seed = seed
        self._rng = np.random.default_rng(self._seed)

    @property
    def recency_split_age(self) -> Optional[int]:
        if self._recency_split_age is None:
            return None

        return self._recency_split_age

    @property
    def recency_split(self) -> Optional[int]:
        if self._recency_split is None:
            return None

        return self._recency_split

    @duration_perf_recorder_decorator("Training")
    def __len__(self) -> int:
        if self._num_samples is not None:
            length = self._num_samples
        else:
            if self._config.dataset_version == 1:
                length = len(self._data["images"])
            else:
                length = len(self._data2)
        return length

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def __getitem__(self, index: int) -> Dict[str, Any]:
        selected_category = None
        selected_embedding = None
        embeddings_avail = None

        if self._num_samples is not None:
            index, selected_category, selected_embedding = self._sample_index()

        if self._config.dataset_version == 1:
            datapoint_metadata = self._data["images"][index]
            metadata = self._metadata[self._metadata["index"] == index]
            if self._embedding_balancing_evaluation_path:
                embeddings_avail = []
                image_id = self.image_url2id[metadata.iloc[0]["url"]]
                for annotation in self.image_id2annotations[image_id]:
                    if annotation.get("deepweed_embedding_bucket"):
                        embeddings_avail.append(annotation["deepweed_embedding_bucket"])

            annotations = []
            for ann in self.image_id2annotations[datapoint_metadata["id"]]:
                annotations.append(ann)

            certified_classes = []
            for class_name in self._categories:
                if class_name not in self._crop_classes and metadata[class_name].values[0] is not None:
                    certified_classes.append(class_name)
                elif class_name in self._crop_classes:
                    if datapoint_metadata["crop_id"].upper() == class_name and metadata["CROP"].values[0] is not None:
                        certified_classes.append(class_name)
                    continue
                else:
                    certified_classes.append(class_name)

            return {
                **datapoint_metadata,
                "annotations": annotations,
                "selected_category": selected_category,
                "selected_embedding": selected_embedding,
                "certified_classes": certified_classes,
                "is_new": datapoint_metadata["is_new"],
                "embeddings": embeddings_avail,
            }
        else:
            datapoint = self._data2[index]
            metadata = self._metadata[self._metadata["index"] == index]
            if self._embedding_balancing_evaluation_path:
                embeddings_avail = []

                for annotation in datapoint["points"]:
                    if annotation.get("deepweed_embedding_bucket"):
                        embeddings_avail.append(annotation["deepweed_embedding_bucket"])

            return {
                **datapoint,
                "selected_category": selected_category,
                "selected_embedding": selected_embedding,
                "certified_classes": [x for x in self._categories if metadata[x].values[0] is not None],
                "is_new": bool(metadata.iloc[0]["is_new"]),
                "embeddings": embeddings_avail,
            }

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def _sample_new(self, df: pandas.DataFrame) -> Tuple[pandas.DataFrame, List[str], List[str], bool]:
        assert self._goal_percentage_new is not None
        use_new = self._rng.uniform() < self._goal_percentage_new
        df = df[df["is_new"] == use_new]
        positive_categories = self._positive_categories_in_new if use_new else self._positive_categories_in_old
        negative_categories = self._negative_categories_in_new if use_new else self._negative_categories_in_old
        return df, positive_categories, negative_categories, use_new

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def _sample_positive_negative(
        self, df: pandas.DataFrame, positive_categories: List[str], negative_categories: List[str]
    ) -> Tuple[pandas.DataFrame, List[str], List[str], str]:
        is_weed = self._rng.uniform() < self._weed_sample_percentage or len(self._crop_classes) == 0
        if is_weed:
            positive_categories = [x for x in positive_categories if x in self._weed_classes or x in self._segm_classes]
            negative_categories = [x for x in negative_categories if x in self._weed_classes or x in self._segm_classes]
        else:
            positive_categories = [x for x in positive_categories if x == "CROP" or x in self._segm_classes]
            negative_categories = [x for x in negative_categories if x == "CROP" or x in self._segm_classes]

        positive = len(positive_categories) > 0 and self._rng.uniform() < self._positive_sample_percentage
        if positive or len(negative_categories) == 0:
            category = self._rng.choice(positive_categories)
            positive = True
        else:
            category = self._rng.choice(negative_categories)

        return df[df[category] == positive], positive_categories, negative_categories, category

    @duration_perf_recorder_decorator("Training")
    def _sample_size(self, df: pandas.DataFrame) -> pandas.DataFrame:
        size = self._rng.choice(df["size"].unique())
        return df[df["size"] == size]

    @duration_perf_recorder_decorator("Training")
    def _sample_crop_id(self, df: pandas.DataFrame) -> pandas.DataFrame:
        crop_id = self._rng.choice(df["crop_id"].unique())
        return df[df["crop_id"] == crop_id]

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def _sample_date(self, df: pandas.DataFrame) -> pandas.DataFrame:
        if self._use_date_groups:
            date_group_id = self._rng.choice(df["date_group_id"].unique())
            df = df[df["date_group_id"] == date_group_id]
            date = self._rng.choice(df["date"].unique())
        else:
            date = self._rng.choice(df["date"].unique())
        df = df[df["date"] == date]
        return df

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def _sample_location(self, df: pandas.DataFrame) -> pandas.DataFrame:
        if len(self._geohash_indices) > 0:
            for geo_ind in self._geohash_indices:
                val = self._rng.choice(df[geo_ind].unique())
                if val is None:
                    df = df[df[geo_ind].isna()]
                    break
                df = df[df[geo_ind] == val]
        else:
            city = self._rng.choice(df["city"].unique())
            df = df[df["city"] == city]
        return df

    @duration_perf_recorder_decorator("Training", no_thread=True)
    def _sample_index(self) -> Tuple[int, Optional[str], Optional[Tuple[int, ...]]]:  # noqa: C901
        """
        Samples a random index from the dataset using a the following procedure:
        - First optionally sample if datapoint is from the new set or not
        - Sample if the datapoint will be a positive or negative example (10:1 ratio)
        - Sample the category uniformly from available classes
        - Sample the date uniformly from available classes
        - Sample the city uniformly from available classes
        - Finally, uniformly sample the datapoint from the remaining datapoints once the selection criteria has been
        matched.
        """
        index: int
        category: Optional[str] = None
        embedding_ind: Optional[Tuple[int, ...]] = None
        if self._config.sampling_algorithm == DATASET_SAMPLING_ALGORITHM_DEFAULT:
            df = self._metadata
            positive_categories = self._positive_categories
            negative_categories = self._negative_categories

            if (
                self._goal_percentage_new is not None
                and self._positive_categories_in_old
                and self._negative_categories_in_old
            ):
                df, positive_categories, negative_categories, _ = self._sample_new(df)
                # self._positive_categories_in_old and self._negative_categories_in_old are both empty only if there's nothing in the unemphasized set.

            df, positive_categories, negative_categories, category = self._sample_positive_negative(
                df, positive_categories=positive_categories, negative_categories=negative_categories
            )

            if self._config.size_balancing:
                df = self._sample_size(df)

            df = self._sample_crop_id(df)
            df = self._sample_date(df)
            df = self._sample_location(df)

            index = self._rng.choice(df["index"])

            if category == "CROP" and self._config.dataset_version == 1:
                category = df.loc[index, "crop_id"].upper()
        elif self._config.sampling_algorithm == DATASET_SAMPLING_ALGORITHM_DRIPTAPE:
            df = self._metadata
            df, positive_categories, negative_categories, category = self._sample_positive_negative(
                df, positive_categories=self._positive_categories, negative_categories=self._negative_categories
            )
            df = self._sample_location(df)

            if category == "CROP" and self._config.dataset_version == 1:
                category = df.loc[index, "crop_id"].upper()
            index = self._rng.choice(df["index"])
        elif self._config.sampling_algorithm == DATASET_SAMPLING_ALGORITHM_GEOHASH_DATE_CATEGORY_BUCKETS:
            bucket_index = self._rng.integers(0, len(self._geohash_month_category_buckets))
            bucket = self._geohash_month_category_buckets[bucket_index]
            category = bucket.category

            df = self._metadata
            df = df[df["month"] == bucket.month]
            df = df[df["geohash_bucket"] == bucket.geohash]
            if self._config.dataset_version == 2 and category in self._certified_crop_ids:
                df = df[df["CROP"].isin([True])]
                df = df[df["crop_id"] == category]
                category = "CROP"
            else:
                df = df[df[category].isin([True])]

            df = df[df[bucket.category].isin([True])]

            index = self._rng.choice(df["index"])
        elif self._config.sampling_algorithm == DATASET_SAMPLING_ALGORITHM_GEOHASH_MONTH:
            bucket_index = self._rng.integers(0, len(self._geohash_month_buckets))
            geohash_month_bucket = self._geohash_month_buckets[bucket_index]
            categories = self._geohash_month_buckets_positive_categories[geohash_month_bucket]
            category_index = self._rng.choice(len(categories))
            category = categories[category_index]

            df = self._metadata
            df = df[df["month"] == geohash_month_bucket.month]
            df = df[df["geohash_bucket"] == geohash_month_bucket.geohash]
            if self._config.dataset_version == 2 and category in self._certified_crop_ids:
                df = df[df["CROP"].isin([True])]
                df = df[df["crop_id"] == category]
                category = "CROP"
            else:
                df = df[df[category].isin([True])]

            index = self._rng.choice(df["index"])

        elif self._config.sampling_algorithm == DATASET_SAMPLING_ALGORITHM_UNIFORM:
            df = self._metadata
            index = self._rng.choice(df.index, p=self._sample_probs)

            row = df.iloc[index]
            crop_id = row["crop_id"].upper()
            row = row[self._all_certified_categories]
            categories = row[row == True].index.tolist()  # noqa: E712
            category = self._rng.choice(categories)

            if self._config.dataset_version == 1 and category == "CROP":
                category = crop_id

        elif self._config.sampling_algorithm == DATASET_SAMPLING_ALGORITHM_GEOHASH_MONTH_HIT:
            bucket_index = self._rng.integers(0, len(self._geohash_month_buckets))
            geohash_month_bucket = self._geohash_month_buckets[bucket_index]
            categories = self._geohash_month_buckets_positive_categories[geohash_month_bucket]

            num_weed_categories = len([category for category in categories if category in self._weed_classes])
            num_crop_categories = len(categories) - num_weed_categories

            if num_crop_categories == 0 or num_weed_categories == 0:
                category_index = self._rng.integers(0, len(categories))
            else:
                weed_chance = 0.5 / num_weed_categories
                crop_chance = 0.5 / num_crop_categories
                probabilities = [
                    weed_chance if category in self._weed_classes else crop_chance for category in categories
                ]
                category_index = self._rng.choice(len(categories), p=probabilities)

            category = categories[category_index]
            df = self._metadata

            df = df[df["month"] == geohash_month_bucket.month]
            df = df[df["geohash_bucket"] == geohash_month_bucket.geohash]

            if self._config.dataset_version == 2 and category in self._certified_crop_ids:
                df = df[df["crop_id"] == category]
                df = df[df["CROP"].isin([True])]
                category = "CROP"
            elif self._config.dataset_version == 1 and category in self._crop_classes:
                df = df[df["crop_id"] == category.lower()]
                df = df[df["CROP"].isin([True])]
            else:
                df = df[df[category].isin([True])]
            index = self._rng.choice(df["index"])

        elif self._config.sampling_algorithm in [
            DATASET_SAMPLING_ALGORITHM_EMBEDDINGS,
            DATASET_SAMPLING_ALGORITHM_CATEGORY_EMBEDDINGS,
            DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS,
        ]:
            index, embedding_ind, category = self._embedding_sampler.sample()
            assert category is not None and embedding_ind is not None, f"{category} and {embedding_ind}"

        return index, category, embedding_ind

    def get_train_ppi(self) -> Optional[int]:
        return self._train_ppi

    @property
    def segm_classes(self) -> List[str]:
        return list(self._segm_classes)

    @property
    def weed_classes(self) -> List[str]:
        return list(self._weed_classes)

    @property
    def crop_classes(self) -> List[str]:
        return list(self._crop_classes)

    @property
    def distinct_examples(self) -> int:
        return int(self._metadata.shape[0])

    @property
    def keep_low_confidence(self) -> bool:
        return self._keep_low_confidence

    @property
    def enabled_classes(self) -> List[str]:
        return self._enabled_classes

    @property
    def distinct_new_examples(self) -> int:
        return int(self._metadata[self._metadata["is_new"].isin([True])].shape[0])

    @property
    def distinct_old_examples(self) -> int:
        return int(self._metadata[self._metadata["is_new"].isin([False])].shape[0])

    @property
    def positive_sample_percentage(self) -> float:
        return self._positive_sample_percentage

    def set_positive_percentage(self, percentage: float) -> None:
        self._positive_sample_percentage = percentage

    @property
    def new_data_captured_ats(self) -> List[DatapointTimestamps]:
        return self._new_data_captured_ats

    @property
    def new_datapoints(self) -> List[str]:
        return self._new_datapoints

    @property
    def old_datapoints(self) -> List[str]:
        return self._old_datapoints

    @property
    def filepath(self) -> str:
        return self._filepath

    @property
    def crop_ids(self) -> List[str]:
        return list(sorted(self._metadata["crop_id"].unique()))
