package state

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/metrics_aggregator"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/golang/protobuf/jsonpb"
	"github.com/sirupsen/logrus"
)

type JobsState struct {
	ManagedStateImpl

	AllJobs     map[string]*frontend.Job
	ActiveJobId string
	SyncTimes   map[string]int64

	redis     *redis.Client
	robotName string
}

func NewJobsState(redisClient *redis.Client, robotName string) *JobsState {
	s := &JobsState{
		AllJobs:   make(map[string]*frontend.Job),
		redis:     redisClient,
		SyncTimes: make(map[string]int64),
		robotName: robotName,
	}
	s.initialize()
	s.init()
	return s
}

func ReadActiveJobId(r *redis.Client) (string, error) {
	return r.ReadString(redis.JobActiveIDRedisKey, "")
}

func (s *JobsState) init() {
	all, err := s.redis.HGetAll(redis.JobDefsRedisKey)
	if err != nil {
		logrus.Infof("Jobs: could not get job ids: %v", err)
		return
	}

	active, err := ReadActiveJobId(s.redis)
	if err != nil {
		logrus.Infof("Jobs: Could not read active job id: %v", err)
		return
	}

	sync, err := s.redis.HGetAll(redis.JobSyncTimesRedisKey)
	if err != nil {
		logrus.Infof("Jobs: Could not read sync times from redis")
		return
	}

	s.WriteOnCurrent(func() {
		for _, jobStr := range all {
			var job *frontend.Job = &frontend.Job{}
			json.Unmarshal([]byte(jobStr), job)
			s.AllJobs[job.JobDescription.JobId] = job
		}

		if active != "" {
			s.ActiveJobId = active
		}

		for id, timeStr := range sync {
			ts, err := strconv.Atoi(timeStr)
			if err != nil {
				logrus.Infof("Jobs: Could not parse sync time %v", timeStr)
				continue
			}
			s.SyncTimes[id] = int64(ts)
		}
	})
}

func (s *JobsState) CreateJob(j *frontend.Job, active bool) error {
	var e error = nil
	s.WriteOnCurrent(func() {
		if active {
			s.stopActiveJob()
			j.StopTimeMs = 0
		} else {
			j.StopTimeMs = j.JobDescription.TimestampMs
		}

		j.LastUpdateTimeMs = time.Now().UnixMilli()

		err := s.redis.WriteJob(j)
		if err != nil {
			e = err
			return
		}

		s.AllJobs[j.JobDescription.JobId] = j

		if active {
			s.ActiveJobId = j.JobDescription.JobId
			err = s.redis.WriteString(redis.JobActiveIDRedisKey, j.JobDescription.JobId)
			if err != nil {
				e = err
				return
			}
		}
	})
	return e
}

func (s *JobsState) UpdateJob(req *frontend.UpdateJobRequest) error {
	var err error
	s.WriteOnCurrent(func() {
		if job, ok := s.AllJobs[req.JobDescription.JobId]; !ok {
			err = errors.New("Job not found")
			return
		} else {
			job.JobDescription.Name = req.JobDescription.Name
			job.JobDescription.TimestampMs = req.JobDescription.TimestampMs
			job.ExpectedAcreage = req.ExpectedAcreage
			job.LastUpdateTimeMs = time.Now().UnixMilli()

			err = s.redis.WriteJob(job)
		}
	})
	return err
}

func (s *JobsState) stopActiveJob() error {
	if s.ActiveJobId != "" {
		oldActiveJob := s.AllJobs[s.ActiveJobId]
		oldActiveJob.StopTimeMs = time.Now().UnixMilli()
		oldActiveJob.LastUpdateTimeMs = time.Now().UnixMilli()

		err := s.redis.WriteJob(oldActiveJob)
		if err != nil {
			return err
		}

		s.ActiveJobId = ""
		s.redis.Del(redis.JobActiveIDRedisKey)
	}
	return nil
}

func (s *JobsState) StopActiveJob() error {
	var e error = nil
	s.WriteOnCurrent(func() {
		err := s.stopActiveJob()
		if err != nil {
			e = err
		}
	})
	return e
}

func (s *JobsState) GetJob(jobId string) (*frontend.Job, error) {
	var job *frontend.Job
	var e error
	s.ReadOnCurrent(func() {
		if j, ok := s.AllJobs[jobId]; ok {
			job = j
		} else {
			e = fmt.Errorf("Jobs: Job %v not found", jobId)
		}
	})
	return job, e
}

func (s *JobsState) StartJob(jobId string) error {
	var e error = nil
	s.WriteOnCurrent(func() {
		var job *frontend.Job
		if j, ok := s.AllJobs[jobId]; !ok {
			e = fmt.Errorf("Jobs: Job %v not found", jobId)
			return
		} else {
			job = j
		}

		err := s.stopActiveJob()
		if err != nil {
			e = err
			return
		}

		job.StopTimeMs = 0
		job.Completed = false
		job.LastUpdateTimeMs = time.Now().UnixMilli()
		err = s.redis.WriteJob(job)
		if err != nil {
			e = err
			return
		}

		s.ActiveJobId = jobId
		err = s.redis.WriteString(redis.JobActiveIDRedisKey, jobId)
		if err != nil {
			e = err
			return
		}
	})
	return e
}

func (s *JobsState) UpdateActiveProfile(uuid string, name string, profileType frontend.ProfileType) error {
	return s.updateProfile(func(j *frontend.Job) {
		j.ActiveProfiles[int32(profileType)] = &frontend.ActiveProfile{
			ProfileType: profileType,
			Id:          uuid,
			Name:        name,
		}
		// Backwards compatability, remove once frontend no longer needs
		switch profileType {
		case frontend.ProfileType_ALMANAC:
			j.AlmanacProfileUUID = uuid
		case frontend.ProfileType_BANDING:
			j.BandingProfile = name
			j.BandingProfileUUID = uuid
		case frontend.ProfileType_DISCRIMINATOR:
			j.DiscriminatorProfileUUID = uuid
		case frontend.ProfileType_THINNING:
			j.ThinningProfile = name
			j.ThinningProfileUUID = uuid
		}
	})
}

func (s *JobsState) UpdateCropId(newCropId string) error {
	return s.updateProfile(func(j *frontend.Job) {
		j.CropId = newCropId
	})
}

func (s *JobsState) updateProfile(updateFunc func(job *frontend.Job)) error {
	var e error = nil
	s.WriteOnCurrent(func() {
		if s.ActiveJobId != "" {
			var job *frontend.Job
			if j, ok := s.AllJobs[s.ActiveJobId]; !ok {
				e = fmt.Errorf("Jobs: Job %v not found", s.ActiveJobId)
				return
			} else {
				job = j
			}
			if job.ActiveProfiles == nil {
				job.ActiveProfiles = make(map[int32]*frontend.ActiveProfile)
			}

			updateFunc(job)
			job.LastUpdateTimeMs = time.Now().UnixMilli()
			err := s.redis.WriteJob(job)
			if err != nil {
				e = err
				return
			}
		}
	})
	return e
}

func (s *JobsState) WriteConfigs(jobId string, root *frontend.ConfigNodeSnapshot) error {
	m := jsonpb.Marshaler{}
	str, err := m.MarshalToString(root)
	if err != nil {
		return err
	}
	if err := s.redis.HSet(redis.JobConfigsRedisKey, jobId, str); err != nil {
		return err
	}
	return nil
}

func (s *JobsState) ReadConfigs(jobId string) (*frontend.ConfigNodeSnapshot, error) {
	str, err := s.redis.HReadStringSafe(redis.JobConfigsRedisKey, jobId, "")
	if err != nil {
		return nil, err
	}
	if str == "" {
		return nil, fmt.Errorf("Jobs: config dump for %v not found", jobId)
	}
	root := &frontend.ConfigNodeSnapshot{}
	err = jsonpb.UnmarshalString(str, root)
	if err != nil {
		return nil, err
	}
	return root, nil
}

func (s *JobsState) ReadMetrics(jobId string) (*metrics_aggregator.Metrics, error) {
	m, err := s.redis.HGetAll("jobs/metrics/" + jobId)
	if err != nil {
		return nil, err
	}

	return &metrics_aggregator.Metrics{Metrics: m}, nil
}

func (s *JobsState) DeleteJob(jobId string) {
	s.WriteOnCurrent(func() {
		if s.ActiveJobId == jobId {
			s.stopActiveJob()
		}
		s.redis.HDel(redis.JobDefsRedisKey, jobId)
		s.redis.HDel(redis.JobConfigsRedisKey, jobId)
		s.redis.HDel(redis.JobSyncTimesRedisKey, jobId)
		delete(s.AllJobs, jobId)
		delete(s.SyncTimes, jobId)
		s.redis.Del("jobs/metrics/" + jobId)
		s.redis.Del(fmt.Sprintf("/jobs/%v/weed_tracking/combined/counts", jobId))
	})
}

func (s *JobsState) MarkJobCompleted(jobId string) {
	s.WriteOnCurrent(func() {
		if s.ActiveJobId == jobId {
			s.stopActiveJob()
		}
		s.AllJobs[jobId].Completed = true
		s.AllJobs[jobId].LastUpdateTimeMs = time.Now().UnixMilli()
		s.redis.WriteJob(s.AllJobs[jobId])
	})
}

func (s *JobsState) MarkJobIncomplete(jobId string) {
	s.WriteOnCurrent(func() {
		s.AllJobs[jobId].Completed = false
		s.AllJobs[jobId].LastUpdateTimeMs = time.Now().UnixMilli()
		s.redis.WriteJob(s.AllJobs[jobId])
	})
}

func (s *JobsState) GetActiveId() string {
	id := ""
	s.ReadOnCurrent(func() {
		id = s.ActiveJobId
	})
	return id
}

type JobsWatcher struct {
	EventTrigger

	jobsState  *JobsState
	jobsClient *portal_clients.Client

	lastJobSyncTimeSec int64
}

func NewJobsWatcher(jobsClient *portal_clients.Client, jobsState *JobsState) *JobsWatcher {
	w := &JobsWatcher{
		jobsState:          jobsState,
		jobsClient:         jobsClient,
		lastJobSyncTimeSec: time.Now().Unix(),
	}

	w.triggerChannel = make(chan bool)
	return w
}

func copyJob(j *frontend.Job) *frontend.Job {
	newJob := &frontend.Job{
		JobDescription: &frontend.JobDescription{
			JobId:       j.JobDescription.JobId,
			Name:        j.JobDescription.Name,
			TimestampMs: j.JobDescription.TimestampMs,
		},
		BandingProfile:           j.BandingProfile,
		BandingProfileUUID:       j.BandingProfileUUID,
		ThinningProfile:          j.ThinningProfile,
		ThinningProfileUUID:      j.ThinningProfileUUID,
		StopTimeMs:               j.StopTimeMs,
		LastUpdateTimeMs:         j.LastUpdateTimeMs,
		ExpectedAcreage:          j.ExpectedAcreage,
		Completed:                j.Completed,
		Almanac:                  j.Almanac,
		Discriminator:            j.Discriminator,
		CropId:                   j.CropId,
		AlmanacProfileUUID:       j.AlmanacProfileUUID,
		DiscriminatorProfileUUID: j.DiscriminatorProfileUUID,
		ActiveProfiles:           make(map[int32]*frontend.ActiveProfile),
	}
	for k, v := range j.ActiveProfiles {
		newJob.ActiveProfiles[k] = v
	}
	return newJob
}

func (w *JobsWatcher) uploadJob(j *frontend.Job) {
	logrus.Infof("Jobs: Uploading %v", j.JobDescription.JobId)
	err := w.jobsClient.UploadJob(j, w.jobsState.robotName)
	if err != nil {
		logrus.WithError(err).Errorf("Jobs: error uploading job %s to portal", j.JobDescription.JobId)
		return
	}
	if j.StopTimeMs == 0 {
		err = w.uploadJobConfigDump(j.JobDescription.JobId)
		if err != nil {
			return
		}
	}
	ts := time.Now().UnixMilli()
	w.jobsState.redis.HSet(redis.JobSyncTimesRedisKey, j.JobDescription.JobId, fmt.Sprintf("%v", ts))
	w.jobsState.WriteOnCurrent(func() {
		w.jobsState.SyncTimes[j.JobDescription.JobId] = ts
	})
}

func (w *JobsWatcher) uploadJobConfigDump(jobId string) error {
	logrus.Infof("Jobs: uploading config dump for job %v", jobId)
	rootConfig, err := w.jobsState.ReadConfigs(jobId)
	if err != nil {
		logrus.Errorf("Jobs: could not read configs for job %v: %v", jobId, err)
		return err
	}
	err = w.jobsClient.UploadJobConfigDump(jobId, rootConfig)
	if err != nil {
		logrus.WithError(err).Errorf("Jobs: could not upload config for job %v to portal", jobId)
		return err
	}
	return nil
}

func (w *JobsWatcher) Action() {
	activeId := ""
	w.jobsState.WriteOnCurrent(func() {
		activeId = w.jobsState.ActiveJobId
		if activeId != "" {
			if j, ok := w.jobsState.AllJobs[activeId]; ok {
				j.LastUpdateTimeMs = time.Now().UnixMilli()
				j.LastUsedTimeMs = j.LastUpdateTimeMs
				w.jobsState.redis.WriteJob(j)
			}
		}
	}) // update jobs state timestamp so that updated metrics will be read from redis in GetNextJob(s)

	if time.Now().Unix()-w.lastJobSyncTimeSec < 60 {
		return
	}
	w.lastJobSyncTimeSec = time.Now().Unix()

	updatedJobs := make([]*frontend.Job, 0)
	w.jobsState.ReadOnCurrent(func() {
		activeId = w.jobsState.ActiveJobId
		for id, job := range w.jobsState.AllJobs {
			if syncTs, ok := w.jobsState.SyncTimes[id]; ok {
				if syncTs < job.LastUpdateTimeMs {
					updatedJobs = append(updatedJobs, copyJob(job))
				}
			} else {
				updatedJobs = append(updatedJobs, copyJob(job))
			}
		}
	})

	for _, j := range updatedJobs {
		w.uploadJob(j)
	}

	if activeId != "" {
		m, err := w.jobsState.ReadMetrics(activeId)
		if err == nil {
			if len(m.Metrics) > 0 {
				w.jobsClient.UploadJobMetrics(activeId, m)
			}
		} else {
			logrus.Errorf("Jobs: error reading metrics for job %v: %v", activeId, err)
		}
	}
}
