# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: cv/runtime/proto/cv_runtime.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.common.camera.proto import camera_pb2 as lib_dot_common_dot_camera_dot_proto_dot_camera__pb2
from generated.proto.cv import cv_pb2 as proto_dot_cv_dot_cv__pb2
from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='cv/runtime/proto/cv_runtime.proto',
  package='cv.runtime.proto',
  syntax='proto3',
  serialized_options=b'Z\010proto/cv',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n!cv/runtime/proto/cv_runtime.proto\x12\x10\x63v.runtime.proto\x1a$lib/common/camera/proto/camera.proto\x1a\x11proto/cv/cv.proto\x1a\'weed_tracking/proto/weed_tracking.proto\"I\n\x10TargetSafetyZone\x12\n\n\x02up\x18\x01 \x01(\x02\x12\x0c\n\x04\x64own\x18\x02 \x01(\x02\x12\x0c\n\x04left\x18\x03 \x01(\x02\x12\r\n\x05right\x18\x04 \x01(\x02\"t\n\nP2PContext\x12\x16\n\x0epredict_cam_id\x18\x01 \x01(\t\x12\x1c\n\x14predict_timestamp_ms\x18\x02 \x01(\x03\x12\x17\n\x0fpredict_coord_x\x18\x03 \x01(\x02\x12\x17\n\x0fpredict_coord_y\x18\x04 \x01(\x02\"\x84\x02\n\x14SetP2PContextRequest\x12\x15\n\rtarget_cam_id\x18\x01 \x01(\t\x12\x35\n\x0fprimary_context\x18\x02 \x01(\x0b\x32\x1c.cv.runtime.proto.P2PContext\x12<\n\x11secondary_context\x18\x03 \x01(\x0b\x32\x1c.cv.runtime.proto.P2PContextH\x00\x88\x01\x01\x12\x37\n\x0bsafety_zone\x18\x04 \x01(\x0b\x32\".cv.runtime.proto.TargetSafetyZone\x12\x11\n\ttarget_id\x18\x05 \x01(\x03\x42\x14\n\x12_secondary_context\"\x17\n\x15SetP2PContextResponse\",\n\x1aGetCameraDimensionsRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"O\n\x1bGetCameraDimensionsResponse\x12\r\n\x05width\x18\x01 \x01(\x03\x12\x0e\n\x06height\x18\x02 \x01(\x03\x12\x11\n\ttranspose\x18\x03 \x01(\x08\"\xb7\x01\n\x1aStartP2PDataCaptureRequest\x12\x15\n\rtarget_cam_id\x18\x01 \x01(\t\x12\x19\n\x11\x63\x61pture_miss_rate\x18\x02 \x01(\x02\x12\x1c\n\x14\x63\x61pture_success_rate\x18\x03 \x01(\x02\x12\x17\n\x0f\x63\x61pture_enabled\x18\x04 \x01(\x08\x12\x14\n\x0c\x63\x61pture_path\x18\x05 \x01(\t\x12\x1a\n\x12\x61\x66ter_timestamp_ms\x18\x06 \x01(\x03\"=\n\x16PointDetectionCategory\x12\x11\n\tthreshold\x18\x01 \x01(\x02\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\t\"^\n\x1dSegmentationDetectionCategory\x12\x11\n\tthreshold\x18\x01 \x01(\x02\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\t\x12\x18\n\x10safety_radius_in\x18\x03 \x01(\x02\"\xf4\x01\n DeepweedDetectionCriteriaSetting\x12\x42\n\x10point_categories\x18\x01 \x03(\x0b\x32(.cv.runtime.proto.PointDetectionCategory\x12\x1c\n\x14weed_point_threshold\x18\x02 \x01(\x02\x12\x1c\n\x14\x63rop_point_threshold\x18\x03 \x01(\x02\x12P\n\x17segmentation_categories\x18\x04 \x03(\x0b\x32/.cv.runtime.proto.SegmentationDetectionCategory\"\xf7\x01\n#SetDeepweedDetectionCriteriaRequest\x12\x1c\n\x14weed_point_threshold\x18\x01 \x01(\x02\x12\x1c\n\x14\x63rop_point_threshold\x18\x02 \x01(\x02\x12\x42\n\x10point_categories\x18\x03 \x03(\x0b\x32(.cv.runtime.proto.PointDetectionCategory\x12P\n\x17segmentation_categories\x18\x04 \x03(\x0b\x32/.cv.runtime.proto.SegmentationDetectionCategory\"&\n$SetDeepweedDetectionCriteriaResponse\"%\n#GetDeepweedDetectionCriteriaRequest\"\xf8\x01\n$GetDeepweedDetectionCriteriaResponse\x12\x1c\n\x14weed_point_threshold\x18\x01 \x01(\x02\x12\x1c\n\x14\x63rop_point_threshold\x18\x02 \x01(\x02\x12\x42\n\x10point_categories\x18\x03 \x03(\x0b\x32(.cv.runtime.proto.PointDetectionCategory\x12P\n\x17segmentation_categories\x18\x04 \x03(\x0b\x32/.cv.runtime.proto.SegmentationDetectionCategory\"G\n%GetDeepweedSupportedCategoriesRequest\x12\x13\n\x06\x63\x61m_id\x18\x01 \x01(\tH\x00\x88\x01\x01\x42\t\n\x07_cam_id\"c\n&GetDeepweedSupportedCategoriesResponse\x12\x1f\n\x17segmentation_categories\x18\x01 \x03(\t\x12\x18\n\x10point_categories\x18\x02 \x03(\t\"3\n!GetDeepweedIndexToCategoryRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"\xdd\x04\n\"GetDeepweedIndexToCategoryResponse\x12x\n\x1cweed_point_index_to_category\x18\x01 \x03(\x0b\x32R.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry\x12x\n\x1c\x63rop_point_index_to_category\x18\x02 \x03(\x0b\x32R.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry\x12}\n\x1eintersection_index_to_category\x18\x03 \x03(\x0b\x32U.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry\x1a?\n\x1dWeedPointIndexToCategoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a?\n\x1d\x43ropPointIndexToCategoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x42\n IntersectionIndexToCategoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"4\n\x1aGetPredictCamMatrixRequest\x12\x16\n\x0epredict_cam_id\x18\x01 \x01(\t\"D\n*GetPredictCamDistortionCoefficientsRequest\x12\x16\n\x0epredict_cam_id\x18\x01 \x01(\t\"\x87\x05\n\x18SetCameraSettingsRequest\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\x12\x18\n\x0b\x65xposure_us\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x12\n\x05gamma\x18\x03 \x01(\x02H\x01\x88\x01\x01\x12\x14\n\x07gain_db\x18\x04 \x01(\x02H\x02\x88\x01\x01\x12\x46\n\x13light_source_preset\x18\x05 \x01(\x0e\x32$.lib.common.camera.LightSourcePresetH\x03\x88\x01\x01\x12\x19\n\x0cwb_ratio_red\x18\x06 \x01(\x02H\x04\x88\x01\x01\x12\x1b\n\x0ewb_ratio_green\x18\x07 \x01(\x02H\x05\x88\x01\x01\x12\x1a\n\rwb_ratio_blue\x18\x08 \x01(\x02H\x06\x88\x01\x01\x12\x19\n\x0croi_offset_x\x18\t \x01(\x03H\x07\x88\x01\x01\x12\x19\n\x0croi_offset_y\x18\n \x01(\x03H\x08\x88\x01\x01\x12\x13\n\x06mirror\x18\x0b \x01(\x08H\t\x88\x01\x01\x12\x11\n\x04\x66lip\x18\x0c \x01(\x08H\n\x88\x01\x01\x12\x15\n\x08strobing\x18\r \x01(\x08H\x0b\x88\x01\x01\x12\x10\n\x03ptp\x18\x0e \x01(\x08H\x0c\x88\x01\x01\x12\x1e\n\x11\x61uto_whitebalance\x18\x0f \x01(\x08H\r\x88\x01\x01\x42\x0e\n\x0c_exposure_usB\x08\n\x06_gammaB\n\n\x08_gain_dbB\x16\n\x14_light_source_presetB\x0f\n\r_wb_ratio_redB\x11\n\x0f_wb_ratio_greenB\x10\n\x0e_wb_ratio_blueB\x0f\n\r_roi_offset_xB\x0f\n\r_roi_offset_yB\t\n\x07_mirrorB\x07\n\x05_flipB\x0b\n\t_strobingB\x06\n\x04_ptpB\x14\n\x12_auto_whitebalance\",\n\x19SetCameraSettingsResponse\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\"=\n\x1aSetAutoWhitebalanceRequest\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\x0f\n\x07\x63\x61m_ids\x18\x02 \x03(\t\"\x1d\n\x1bSetAutoWhitebalanceResponse\"+\n\x18GetCameraSettingsRequest\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\"\xb5\x05\n\x16\x43\x61meraSettingsResponse\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x18\n\x0b\x65xposure_us\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x12\n\x05gamma\x18\x03 \x01(\x02H\x01\x88\x01\x01\x12\x14\n\x07gain_db\x18\x04 \x01(\x02H\x02\x88\x01\x01\x12\x46\n\x13light_source_preset\x18\x05 \x01(\x0e\x32$.lib.common.camera.LightSourcePresetH\x03\x88\x01\x01\x12\x19\n\x0cwb_ratio_red\x18\x06 \x01(\x02H\x04\x88\x01\x01\x12\x1b\n\x0ewb_ratio_green\x18\x07 \x01(\x02H\x05\x88\x01\x01\x12\x1a\n\rwb_ratio_blue\x18\x08 \x01(\x02H\x06\x88\x01\x01\x12\x16\n\troi_width\x18\t \x01(\x03H\x07\x88\x01\x01\x12\x17\n\nroi_height\x18\n \x01(\x03H\x08\x88\x01\x01\x12\x19\n\x0croi_offset_x\x18\x0b \x01(\x03H\t\x88\x01\x01\x12\x19\n\x0croi_offset_y\x18\x0c \x01(\x03H\n\x88\x01\x01\x12\x13\n\x06gpu_id\x18\r \x01(\x03H\x0b\x88\x01\x01\x12\x0e\n\x06mirror\x18\x0e \x01(\x08\x12\x0c\n\x04\x66lip\x18\x0f \x01(\x08\x12\x10\n\x08strobing\x18\x10 \x01(\x08\x12\x0b\n\x03ptp\x18\x11 \x01(\x08\x12\x1e\n\x11\x61uto_whitebalance\x18\x12 \x01(\x08H\x0c\x88\x01\x01\x42\x0e\n\x0c_exposure_usB\x08\n\x06_gammaB\n\n\x08_gain_dbB\x16\n\x14_light_source_presetB\x0f\n\r_wb_ratio_redB\x11\n\x0f_wb_ratio_greenB\x10\n\x0e_wb_ratio_blueB\x0c\n\n_roi_widthB\r\n\x0b_roi_heightB\x0f\n\r_roi_offset_xB\x0f\n\r_roi_offset_yB\t\n\x07_gpu_idB\x14\n\x12_auto_whitebalance\"g\n\x19GetCameraSettingsResponse\x12J\n\x18\x63\x61mera_settings_response\x18\x01 \x03(\x0b\x32(.cv.runtime.proto.CameraSettingsResponse\"\x91\x01\n\x1dStartBurstRecordFramesRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x13\n\x0b\x64uration_ms\x18\x02 \x01(\x03\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\"\n\x1a\x64ont_capture_predict_image\x18\x04 \x01(\x08\x12\x19\n\x11\x64ownsample_factor\x18\x05 \x01(\x05\" \n\x1eStartBurstRecordFramesResponse\"p\n\x1cStopBurstRecordFramesRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12$\n\x17last_frame_timestamp_ms\x18\x02 \x01(\x03H\x00\x88\x01\x01\x42\x1a\n\x18_last_frame_timestamp_ms\"\x1f\n\x1dStopBurstRecordFramesResponse\"\xe1\x01\n\x0eP2POutputProto\x12\x0f\n\x07matched\x18\x01 \x01(\x08\x12\x16\n\x0etarget_coord_x\x18\x02 \x01(\x02\x12\x16\n\x0etarget_coord_y\x18\x03 \x01(\x02\x12\x1b\n\x13target_timestamp_ms\x18\x04 \x01(\x03\x12\x1c\n\x14predict_timestamp_ms\x18\x05 \x01(\x03\x12\x0c\n\x04safe\x18\x06 \x01(\x08\x12\x17\n\x0fpredict_coord_x\x18\x07 \x01(\x02\x12\x17\n\x0fpredict_coord_y\x18\x08 \x01(\x02\x12\x13\n\x0bpredict_cam\x18\t \x01(\t\"S\n\x17GetNextP2POutputRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x12\n\ntimeout_ms\x18\x03 \x01(\x03\">\n\x14GetConnectorsRequest\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\x12\x15\n\rconnector_ids\x18\x02 \x03(\t\"f\n\x11\x43onnectorResponse\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0c\x63onnector_id\x18\x02 \x01(\t\x12\x12\n\nis_enabled\x18\x03 \x01(\x08\x12\x17\n\x0freduction_ratio\x18\x04 \x01(\x03\"X\n\x15GetConnectorsResponse\x12?\n\x12\x63onnector_response\x18\x01 \x03(\x0b\x32#.cv.runtime.proto.ConnectorResponse\"\x98\x01\n\x14SetConnectorsRequest\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\x12\x15\n\rconnector_ids\x18\x02 \x03(\t\x12\x17\n\nis_enabled\x18\x03 \x01(\x08H\x00\x88\x01\x01\x12\x1c\n\x0freduction_ratio\x18\x04 \x01(\x03H\x01\x88\x01\x01\x42\r\n\x0b_is_enabledB\x12\n\x10_reduction_ratio\"\x17\n\x15SetConnectorsResponse\"\xfd\x01\n\nNodeTiming\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08\x66ps_mean\x18\x02 \x01(\x02\x12\x11\n\tfps_99pct\x18\x03 \x01(\x02\x12\x17\n\x0flatency_ms_mean\x18\x04 \x01(\x02\x12\x18\n\x10latency_ms_99pct\x18\x05 \x01(\x02\x12\r\n\x05state\x18\x06 \x01(\t\x12\x45\n\rstate_timings\x18\x07 \x03(\x0b\x32..cv.runtime.proto.NodeTiming.StateTimingsEntry\x1a\x33\n\x11StateTimingsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\x12\n\x10GetTimingRequest\"F\n\x11GetTimingResponse\x12\x31\n\x0bnode_timing\x18\x01 \x03(\x0b\x32\x1c.cv.runtime.proto.NodeTiming\"K\n\x0ePredictRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x12\n\nfile_paths\x18\x02 \x03(\t\x12\x15\n\rtimestamps_ms\x18\x03 \x03(\x03\"\x11\n\x0fPredictResponse\"P\n\x13LoadAndQueueRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x12\n\nfile_paths\x18\x02 \x03(\t\x12\x15\n\rtimestamps_ms\x18\x03 \x03(\x03\"\x16\n\x14LoadAndQueueResponse\"4\n\x0fSetImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x11\n\tfile_path\x18\x02 \x01(\t\"\x12\n\x10SetImageResponse\"#\n\x11UnsetImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"\x14\n\x12UnsetImageResponse\"\x16\n\x14GetModelPathsRequest\"w\n\x15GetModelPathsResponse\x12\x10\n\x03p2p\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x15\n\x08\x64\x65\x65pweed\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x14\n\x07\x66urrows\x18\x03 \x01(\tH\x02\x88\x01\x01\x42\x06\n\x04_p2pB\x0b\n\t_deepweedB\n\n\x08_furrows\"\x1e\n\x1cGetCameraTemperaturesRequest\"8\n\x11\x43\x61meraTemperature\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x13\n\x0btemperature\x18\x02 \x01(\x01\"Y\n\x1dGetCameraTemperaturesResponse\x12\x38\n\x0btemperature\x18\x01 \x03(\x0b\x32#.cv.runtime.proto.CameraTemperature\"\x82\x01\n\x06GeoLLA\x12\x10\n\x03lat\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x10\n\x03lng\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x10\n\x03\x61lt\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x19\n\x0ctimestamp_ms\x18\x04 \x01(\x03H\x03\x88\x01\x01\x42\x06\n\x04_latB\x06\n\x04_lngB\x06\n\x04_altB\x0f\n\r_timestamp_ms\"w\n\x07GeoECEF\x12\x0e\n\x01x\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x0e\n\x01y\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x0e\n\x01z\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x19\n\x0ctimestamp_ms\x18\x04 \x01(\x03H\x03\x88\x01\x01\x42\x04\n\x02_xB\x04\n\x02_yB\x04\n\x02_zB\x0f\n\r_timestamp_ms\"g\n\x15SetGPSLocationRequest\x12%\n\x03lla\x18\x01 \x01(\x0b\x32\x18.cv.runtime.proto.GeoLLA\x12\'\n\x04\x65\x63\x65\x66\x18\x02 \x01(\x0b\x32\x19.cv.runtime.proto.GeoECEF\"\x18\n\x16SetGPSLocationResponse\"=\n\x19SetImplementStatusRequest\x12\x0e\n\x06lifted\x18\x01 \x01(\x08\x12\x10\n\x08\x65stopped\x18\x02 \x01(\x08\"\x1c\n\x1aSetImplementStatusResponse\"\x86\x01\n\x14SetImageScoreRequest\x12\r\n\x05score\x18\x01 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\t\x12\x39\n\x0f\x64\x65\x65pweed_output\x18\x04 \x01(\x0b\x32 .cv.runtime.proto.DeepweedOutput\"\x17\n\x15SetImageScoreResponse\"*\n\x14GetScoreQueueRequest\x12\x12\n\nscore_type\x18\x01 \x01(\t\"B\n\x0bScoreObject\x12\r\n\x05score\x18\x01 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\t\"L\n\x15GetScoreQueueResponse\x12\x33\n\x0cscore_object\x18\x01 \x03(\x0b\x32\x1d.cv.runtime.proto.ScoreObject\"-\n\x17GetMaxImageScoreRequest\x12\x12\n\nscore_type\x18\x01 \x01(\t\"7\n\x18GetMaxImageScoreResponse\x12\r\n\x05score\x18\x01 \x01(\x01\x12\x0c\n\x04type\x18\x02 \x01(\t\".\n\x18GetMaxScoredImageRequest\x12\x12\n\nscore_type\x18\x01 \x01(\t\"f\n\x18GetLatestP2PImageRequest\x12\x16\n\nscore_type\x18\x01 \x01(\tB\x02\x18\x01\x12\x32\n\x06reason\x18\x02 \x01(\x0e\x32\".carbon.aimbot.cv.P2PCaptureReason\"\'\n\x15GetLatestImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"D\n\x1cGetImageNearTimestampRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\")\n\x13GetChipImageRequest\x12\x12\n\nscore_type\x18\x01 \x01(\t\"~\n\x12\x46lushQueuesRequest\x12\x12\n\nscore_type\x18\x01 \x03(\t\x12?\n\x10score_queue_type\x18\x02 \x01(\x0e\x32 .cv.runtime.proto.ScoreQueueTypeH\x00\x88\x01\x01\x42\x13\n\x11_score_queue_type\"\x15\n\x13\x46lushQueuesResponse\"\xb0\x06\n\x18ImageAndMetadataResponse\x12\r\n\x05\x62ytes\x18\x01 \x01(\x0c\x12\r\n\x05width\x18\x02 \x01(\x05\x12\x0e\n\x06height\x18\x03 \x01(\x05\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x03\x12\r\n\x05score\x18\x05 \x01(\x01\x12\x0e\n\x06\x63\x61m_id\x18\x06 \x01(\t\x12\x1a\n\x12iso_formatted_time\x18\x07 \x01(\t\x12\x0f\n\x07lla_lat\x18\x08 \x01(\x01\x12\x0f\n\x07lla_lng\x18\t \x01(\x01\x12\x0f\n\x07lla_alt\x18\n \x01(\x01\x12\x18\n\x10lla_timestamp_ms\x18\x0b \x01(\x03\x12\x0e\n\x06\x65\x63\x65\x66_x\x18\x0c \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_y\x18\r \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_z\x18\x0e \x01(\x01\x12\x19\n\x11\x65\x63\x65\x66_timestamp_ms\x18\x0f \x01(\x03\x12\x0b\n\x03ppi\x18\x10 \x01(\x02\x12\x17\n\nscore_type\x18\x11 \x01(\tH\x00\x88\x01\x01\x12\x12\n\nimage_type\x18\x12 \x01(\t\x12\x11\n\tmodel_url\x18\x13 \x01(\t\x12\x0c\n\x04\x63rop\x18\x14 \x01(\t\x12\x1b\n\x13weed_height_columns\x18\x15 \x03(\x01\x12\x1b\n\x13\x63rop_height_columns\x18\x16 \x03(\x01\x12\x15\n\rbbh_offset_mm\x18\x17 \x01(\x01\x12\x14\n\x0c\x66ocus_metric\x18\x18 \x01(\x01\x12\x13\n\x0b\x65xposure_us\x18\x19 \x01(\x01\x12\x1c\n\x14\x63rop_point_threshold\x18\x1a \x01(\x01\x12\x1c\n\x14weed_point_threshold\x18\x1b \x01(\x01\x12\x17\n\x0fweeding_enabled\x18\x1c \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x1d \x01(\x08\x12\x13\n\x0b\x64\x65\x65pweed_id\x18\x1e \x01(\t\x12\x0e\n\x06p2p_id\x18\x1f \x01(\t\x12\x35\n\x13\x64\x65\x65pweed_detections\x18  \x03(\x0b\x32\x18.weed_tracking.Detection\x12\x1e\n\x16segmentation_threshold\x18! \x01(\x01\x12\x1b\n\x13simulator_generated\x18\" \x01(\x08\x12\x0f\n\x07gain_db\x18# \x01(\x01\x42\r\n\x0b_score_type\"5\n\x12\x43\x61tegoryPrediction\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x01\"\xd4\x01\n\x0e\x43hipPrediction\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\x0e\n\x06radius\x18\x03 \x01(\x01\x12\x10\n\x08model_id\x18\x04 \x01(\t\x12\x33\n\x05score\x18\x05 \x03(\x0b\x32$.cv.runtime.proto.CategoryPrediction\x12@\n\x12\x65mbedding_distance\x18\x06 \x03(\x0b\x32$.cv.runtime.proto.CategoryPrediction\x12\x13\n\x0b\x62\x61nd_status\x18\x07 \x01(\t\"\xa5\x01\n\x1c\x43hipImageAndMetadataResponse\x12\x46\n\x12image_and_metadata\x18\x01 \x01(\x0b\x32*.cv.runtime.proto.ImageAndMetadataResponse\x12=\n\x13prediction_metadata\x18\x02 \x01(\x0b\x32 .cv.runtime.proto.ChipPrediction\"\x1d\n\x1b\x43hipQueueInformationRequest\"Y\n\x1c\x43hipQueueInformationResponse\x12\x39\n\x0bqueue_score\x18\x01 \x03(\x0b\x32$.cv.runtime.proto.CategoryPrediction\"\xda\x05\n\x1bP2PImageAndMetadataResponse\x12\x14\n\x0ctarget_bytes\x18\x01 \x01(\x0c\x12\x14\n\x0ctarget_width\x18\x02 \x01(\x05\x12\x15\n\rtarget_height\x18\x03 \x01(\x05\x12\x19\n\x11perspective_bytes\x18\x04 \x01(\x0c\x12\x19\n\x11perspective_width\x18\x05 \x01(\x05\x12\x1a\n\x12perspective_height\x18\x06 \x01(\x05\x12\x1e\n\x16\x61nnotated_target_bytes\x18\x07 \x01(\x0c\x12\x1e\n\x16\x61nnotated_target_width\x18\x08 \x01(\x05\x12\x1f\n\x17\x61nnotated_target_height\x18\t \x01(\x05\x12\x14\n\x0ctimestamp_ms\x18\n \x01(\x03\x12\r\n\x05score\x18\x0b \x01(\x01\x12\x0e\n\x06\x63\x61m_id\x18\x0c \x01(\t\x12\x1a\n\x12iso_formatted_time\x18\r \x01(\t\x12\x0f\n\x07lla_lat\x18\x0e \x01(\x01\x12\x0f\n\x07lla_lng\x18\x0f \x01(\x01\x12\x0f\n\x07lla_alt\x18\x10 \x01(\x01\x12\x18\n\x10lla_timestamp_ms\x18\x11 \x01(\x03\x12\x0e\n\x06\x65\x63\x65\x66_x\x18\x12 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_y\x18\x13 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_z\x18\x14 \x01(\x01\x12\x19\n\x11\x65\x63\x65\x66_timestamp_ms\x18\x15 \x01(\x03\x12\x0b\n\x03ppi\x18\x16 \x01(\x02\x12\x17\n\x0fperspective_ppi\x18\x17 \x01(\x02\x12\x12\n\nimage_type\x18\x19 \x01(\t\x12\x11\n\tmodel_url\x18\x1a \x01(\t\x12\x0c\n\x04\x63rop\x18\x1b \x01(\t\x12\x14\n\x0c\x66ocus_metric\x18\x1c \x01(\x01\x12\x13\n\x0b\x65xposure_us\x18\x1d \x01(\x01\x12\x17\n\x0fweeding_enabled\x18\x1e \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x1f \x01(\x08\x12\x13\n\x0b\x64\x65\x65pweed_id\x18  \x01(\t\x12\x0e\n\x06p2p_id\x18! \x01(\t\"\x16\n\x14GetCameraInfoRequest\"\x84\x03\n\nCameraInfo\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x17\n\nip_address\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x1a\n\rserial_number\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\r\n\x05model\x18\x04 \x01(\t\x12\r\n\x05width\x18\x05 \x01(\r\x12\x0e\n\x06height\x18\x06 \x01(\r\x12\x11\n\tconnected\x18\x07 \x01(\x08\x12\x12\n\nlink_speed\x18\x08 \x01(\x04\x12/\n\nerror_type\x18\t \x01(\x0e\x32\x1b.cv.runtime.proto.ErrorType\x12\x1b\n\x0ev4l2_device_id\x18\n \x01(\tH\x02\x88\x01\x01\x12\x18\n\x10\x66irmware_version\x18\x0b \x01(\t\x12$\n\x17latest_firmware_version\x18\x0c \x01(\tH\x03\x88\x01\x01\x42\r\n\x0b_ip_addressB\x10\n\x0e_serial_numberB\x11\n\x0f_v4l2_device_idB\x1a\n\x18_latest_firmware_version\"J\n\x15GetCameraInfoResponse\x12\x31\n\x0b\x63\x61mera_info\x18\x01 \x03(\x0b\x32\x1c.cv.runtime.proto.CameraInfo\"\"\n GetLightweightBurstRecordRequest\"L\n!GetLightweightBurstRecordResponse\x12\x10\n\x08zip_file\x18\x01 \x01(\x0c\x12\x15\n\rmetadata_file\x18\x02 \x01(\x0c\"\x12\n\x10GetBootedRequest\"#\n\x11GetBootedResponse\x12\x0e\n\x06\x62ooted\x18\x01 \x01(\x08\"\x11\n\x0fGetReadyRequest\"\xcc\x02\n\x10GetReadyResponse\x12\r\n\x05ready\x18\x01 \x01(\x08\x12X\n\x14\x64\x65\x65pweed_ready_state\x18\x02 \x03(\x0b\x32:.cv.runtime.proto.GetReadyResponse.DeepweedReadyStateEntry\x12N\n\x0fp2p_ready_state\x18\x03 \x03(\x0b\x32\x35.cv.runtime.proto.GetReadyResponse.P2pReadyStateEntry\x12\x0e\n\x06\x62ooted\x18\x04 \x01(\x08\x1a\x39\n\x17\x44\x65\x65pweedReadyStateEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\x1a\x34\n\x12P2pReadyStateEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"Z\n\x15GetErrorStateResponse\x12\x1b\n\x13plant_profile_error\x18\x01 \x01(\x08\x12$\n\x1cmodel_unsupported_embeddings\x18\x02 \x01(\x08\"\xc2\x02\n\x11\x44\x65\x65pweedDetection\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x0c\n\x04size\x18\x03 \x01(\x02\x12\r\n\x05score\x18\x04 \x01(\x02\x12-\n\thit_class\x18\x06 \x01(\x0e\x32\x1a.cv.runtime.proto.HitClass\x12\x1a\n\x12mask_intersections\x18\x07 \x03(\r\x12\x12\n\nweed_score\x18\x08 \x01(\x02\x12\x12\n\ncrop_score\x18\t \x01(\x02\x12#\n\x1bweed_detection_class_scores\x18\n \x03(\x02\x12\x11\n\tembedding\x18\x0b \x03(\x02\x12\x13\n\x0bplant_score\x18\x0c \x01(\x02\x12$\n\x1c\x65mbedding_category_distances\x18\r \x03(\x02\x12\x14\n\x0c\x64\x65tection_id\x18\x0e \x01(\r\"\xd1\x02\n\x0e\x44\x65\x65pweedOutput\x12\x37\n\ndetections\x18\x01 \x03(\x0b\x32#.cv.runtime.proto.DeepweedDetection\x12\x12\n\nmask_width\x18\x02 \x01(\r\x12\x13\n\x0bmask_height\x18\x03 \x01(\r\x12\x15\n\rmask_channels\x18\x04 \x01(\r\x12\x0c\n\x04mask\x18\x05 \x01(\x0c\x12\x1c\n\x14mask_channel_classes\x18\x06 \x03(\t\x12\"\n\x1apredict_in_distance_buffer\x18\x07 \x01(\x08\x12\x14\n\x0ctimestamp_ms\x18\x08 \x01(\x03\x12\x1e\n\x16weed_detection_classes\x18\t \x03(\t\x12\x1c\n\x14\x65mbedding_categories\x18\n \x03(\t\x12\"\n\x1a\x61vailable_for_snapshotting\x18\x0b \x01(\x08\"K\n#GetDeepweedOutputByTimestampRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"%\n#GetRecommendedStrobeSettingsRequest\"d\n$GetRecommendedStrobeSettingsResponse\x12\x19\n\x11target_camera_fps\x18\x01 \x01(\x02\x12!\n\x19targets_per_predict_ratio\x18\x02 \x01(\x05\"/\n\x19StartP2PBufferringRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t:\x02\x18\x01\" \n\x1aStartP2PBufferringResponse:\x02\x18\x01\"\xaa\x01\n\x18StopP2PBufferringRequest\x12\x12\n\nsave_burst\x18\x01 \x01(\x08\x12\x0e\n\x06\x63\x61m_id\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\"\n\x1a\x64ont_capture_predict_image\x18\x04 \x01(\x08\x12\x1a\n\x12start_timestamp_ms\x18\x05 \x01(\x03\x12\x18\n\x10\x65nd_timestamp_ms\x18\x06 \x01(\x03:\x02\x18\x01\"\x1f\n\x19StopP2PBufferringResponse:\x02\x18\x01\"\x92\x01\n\x11P2PCaptureRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x03\x12\x15\n\rwrite_to_disk\x18\x04 \x01(\x08\x12\x32\n\x06reason\x18\x05 \x01(\x0e\x32\".carbon.aimbot.cv.P2PCaptureReason\"\x14\n\x12P2PCaptureResponse\"9\n P2PBufferingBurstPredictMetadata\x12\x15\n\rplant_size_px\x18\x01 \x01(\x02\"\xd0\x02\n P2PBufferringBurstCaptureRequest\x12\x0e\n\x06\x63\x61m_id\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\"\n\x1a\x64ont_capture_predict_image\x18\x04 \x01(\x08\x12\x1a\n\x12start_timestamp_ms\x18\x05 \x01(\x03\x12\x18\n\x10\x65nd_timestamp_ms\x18\x06 \x01(\x03\x12\x19\n\x0cpredict_path\x18\x07 \x01(\tH\x00\x88\x01\x01\x12\x1b\n\x13predict_path_exists\x18\x08 \x01(\x08\x12\x1d\n\x15save_predict_metadata\x18\t \x01(\x08\x12L\n\x10predict_metadata\x18\n \x01(\x0b\x32\x32.cv.runtime.proto.P2PBufferingBurstPredictMetadataB\x0f\n\r_predict_path\"#\n!P2PBufferringBurstCaptureResponse\"X\n\x1cGetNextDeepweedOutputRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x12\n\ntimeout_ms\x18\x02 \x01(\x03\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\t\"M\n\x18SetTargetingStateRequest\x12\x17\n\x0fweeding_enabled\x18\x01 \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x02 \x01(\x08\"\x1b\n\x19SetTargetingStateResponse\"A\n\x19GetNextFocusMetricRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"H\n\x1aGetNextFocusMetricResponse\x12\x14\n\x0c\x66ocus_metric\x18\x01 \x01(\x02\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"$\n\x14RemoveDataDirRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\"\x17\n\x15RemoveDataDirResponse\"7\n\x11LastNImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x12\n\nnum_images\x18\x02 \x01(\x04\"3\n\x1b\x43omputeCapabilitiesResponse\x12\x14\n\x0c\x63\x61pabilities\x18\x01 \x03(\t\"5\n!SupportedTensorRTVersionsResponse\x12\x10\n\x08versions\x18\x01 \x03(\t\"\x07\n\x05\x45mpty\"<\n\x12ScoreQueueAndCount\x12\x13\n\x0bscore_queue\x18\x01 \x01(\t\x12\x11\n\tnum_items\x18\x02 \x01(\x04\"T\n\x17ListScoreQueuesResponse\x12\x39\n\x0bscore_queue\x18\x01 \x03(\x0b\x32$.cv.runtime.proto.ScoreQueueAndCount\"b\n\x1dGetCategoryCollectionResponse\x12\x1e\n\x16\x63\x61tegory_collection_id\x18\x01 \x01(\t\x12!\n\x19last_updated_timestamp_ms\x18\x02 \x01(\x03\"\x1e\n\x1cSnapshotPredictImagesRequest\"5\n\x0cPcamSnapshot\x12\x0f\n\x07pcam_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"R\n\x1dSnapshotPredictImagesResponse\x12\x31\n\tsnapshots\x18\x01 \x03(\x0b\x32\x1e.cv.runtime.proto.PcamSnapshot\"p\n\x1dGetChipForPredictImageRequest\x12\x0f\n\x07pcam_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x13\n\x0b\x63\x65nter_x_px\x18\x03 \x01(\x05\x12\x13\n\x0b\x63\x65nter_y_px\x18\x04 \x01(\x05\"h\n\x1eGetChipForPredictImageResponse\x12\x46\n\x12image_and_metadata\x18\x01 \x01(\x0b\x32*.cv.runtime.proto.ImageAndMetadataResponse*A\n\rBufferUseCase\x12\x07\n\x03P2P\x10\x00\x12\x0f\n\x0bOpticalFlow\x10\x01\x12\x0b\n\x07Predict\x10\x02\x12\t\n\x05\x44rive\x10\x03*)\n\x08HitClass\x12\x08\n\x04WEED\x10\x00\x12\x08\n\x04\x43ROP\x10\x01\x12\t\n\x05PLANT\x10\x02*\'\n\x0eScoreQueueType\x12\x0b\n\x07PREDICT\x10\x00\x12\x08\n\x04\x43HIP\x10\x01*c\n\tErrorType\x12\x08\n\x04NONE\x10\x00\x12\x08\n\x04GRAB\x10\x01\x12\x0e\n\nCONNECTION\x10\x02\x12\x15\n\x11NO_IMPLEMENTATION\x10\x03\x12\x1b\n\x17NO_IMAGE_IN_LAST_MINUTE\x10\x04\x32\xb6.\n\x10\x43VRuntimeService\x12\x62\n\rSetP2PContext\x12&.cv.runtime.proto.SetP2PContextRequest\x1a\'.cv.runtime.proto.SetP2PContextResponse\"\x00\x12t\n\x13GetCameraDimensions\x12,.cv.runtime.proto.GetCameraDimensionsRequest\x1a-.cv.runtime.proto.GetCameraDimensionsResponse\"\x00\x12\x62\n\rGetCameraInfo\x12&.cv.runtime.proto.GetCameraInfoRequest\x1a\'.cv.runtime.proto.GetCameraInfoResponse\"\x00\x12\x89\x01\n\x1aGetDeepweedIndexToCategory\x12\x33.cv.runtime.proto.GetDeepweedIndexToCategoryRequest\x1a\x34.cv.runtime.proto.GetDeepweedIndexToCategoryResponse\"\x00\x12\x8f\x01\n\x1cSetDeepweedDetectionCriteria\x12\x35.cv.runtime.proto.SetDeepweedDetectionCriteriaRequest\x1a\x36.cv.runtime.proto.SetDeepweedDetectionCriteriaResponse\"\x00\x12\x8f\x01\n\x1cGetDeepweedDetectionCriteria\x12\x35.cv.runtime.proto.GetDeepweedDetectionCriteriaRequest\x1a\x36.cv.runtime.proto.GetDeepweedDetectionCriteriaResponse\"\x00\x12\x95\x01\n\x1eGetDeepweedSupportedCategories\x12\x37.cv.runtime.proto.GetDeepweedSupportedCategoriesRequest\x1a\x38.cv.runtime.proto.GetDeepweedSupportedCategoriesResponse\"\x00\x12z\n\x15GetCameraTemperatures\x12..cv.runtime.proto.GetCameraTemperaturesRequest\x1a/.cv.runtime.proto.GetCameraTemperaturesResponse\"\x00\x12n\n\x11SetCameraSettings\x12*.cv.runtime.proto.SetCameraSettingsRequest\x1a+.cv.runtime.proto.SetCameraSettingsResponse\"\x00\x12n\n\x11GetCameraSettings\x12*.cv.runtime.proto.GetCameraSettingsRequest\x1a+.cv.runtime.proto.GetCameraSettingsResponse\"\x00\x12}\n\x16StartBurstRecordFrames\x12/.cv.runtime.proto.StartBurstRecordFramesRequest\x1a\x30.cv.runtime.proto.StartBurstRecordFramesResponse\"\x00\x12z\n\x15StopBurstRecordFrames\x12..cv.runtime.proto.StopBurstRecordFramesRequest\x1a/.cv.runtime.proto.StopBurstRecordFramesResponse\"\x00\x12\x62\n\rGetConnectors\x12&.cv.runtime.proto.GetConnectorsRequest\x1a\'.cv.runtime.proto.GetConnectorsResponse\"\x00\x12\x62\n\rSetConnectors\x12&.cv.runtime.proto.SetConnectorsRequest\x1a\'.cv.runtime.proto.SetConnectorsResponse\"\x00\x12V\n\tGetTiming\x12\".cv.runtime.proto.GetTimingRequest\x1a#.cv.runtime.proto.GetTimingResponse\"\x00\x12P\n\x07Predict\x12 .cv.runtime.proto.PredictRequest\x1a!.cv.runtime.proto.PredictResponse\"\x00\x12_\n\x0cLoadAndQueue\x12%.cv.runtime.proto.LoadAndQueueRequest\x1a&.cv.runtime.proto.LoadAndQueueResponse\"\x00\x12S\n\x08SetImage\x12!.cv.runtime.proto.SetImageRequest\x1a\".cv.runtime.proto.SetImageResponse\"\x00\x12Y\n\nUnsetImage\x12#.cv.runtime.proto.UnsetImageRequest\x1a$.cv.runtime.proto.UnsetImageResponse\"\x00\x12\x62\n\rGetModelPaths\x12&.cv.runtime.proto.GetModelPathsRequest\x1a\'.cv.runtime.proto.GetModelPathsResponse\"\x00\x12\x65\n\x0eSetGPSLocation\x12\'.cv.runtime.proto.SetGPSLocationRequest\x1a(.cv.runtime.proto.SetGPSLocationResponse\"\x00\x12q\n\x12SetImplementStatus\x12+.cv.runtime.proto.SetImplementStatusRequest\x1a,.cv.runtime.proto.SetImplementStatusResponse\"\x00\x12\x62\n\rSetImageScore\x12&.cv.runtime.proto.SetImageScoreRequest\x1a\'.cv.runtime.proto.SetImageScoreResponse\"\x00\x12\x62\n\rGetScoreQueue\x12&.cv.runtime.proto.GetScoreQueueRequest\x1a\'.cv.runtime.proto.GetScoreQueueResponse\"\x00\x12W\n\x0fListScoreQueues\x12\x17.cv.runtime.proto.Empty\x1a).cv.runtime.proto.ListScoreQueuesResponse\"\x00\x12k\n\x10GetMaxImageScore\x12).cv.runtime.proto.GetMaxImageScoreRequest\x1a*.cv.runtime.proto.GetMaxImageScoreResponse\"\x00\x12m\n\x11GetMaxScoredImage\x12*.cv.runtime.proto.GetMaxScoredImageRequest\x1a*.cv.runtime.proto.ImageAndMetadataResponse\"\x00\x12p\n\x11GetLatestP2PImage\x12*.cv.runtime.proto.GetLatestP2PImageRequest\x1a-.cv.runtime.proto.P2PImageAndMetadataResponse\"\x00\x12g\n\x0cGetChipImage\x12%.cv.runtime.proto.GetChipImageRequest\x1a..cv.runtime.proto.ChipImageAndMetadataResponse\"\x00\x12z\n\x17GetChipQueueInformation\x12-.cv.runtime.proto.ChipQueueInformationRequest\x1a..cv.runtime.proto.ChipQueueInformationResponse\"\x00\x12\\\n\x0b\x46lushQueues\x12$.cv.runtime.proto.FlushQueuesRequest\x1a%.cv.runtime.proto.FlushQueuesResponse\"\x00\x12g\n\x0eGetLatestImage\x12\'.cv.runtime.proto.GetLatestImageRequest\x1a*.cv.runtime.proto.ImageAndMetadataResponse\"\x00\x12u\n\x15GetImageNearTimestamp\x12..cv.runtime.proto.GetImageNearTimestampRequest\x1a*.cv.runtime.proto.ImageAndMetadataResponse\"\x00\x12\x86\x01\n\x19GetLightweightBurstRecord\x12\x32.cv.runtime.proto.GetLightweightBurstRecordRequest\x1a\x33.cv.runtime.proto.GetLightweightBurstRecordResponse\"\x00\x12V\n\tGetBooted\x12\".cv.runtime.proto.GetBootedRequest\x1a#.cv.runtime.proto.GetBootedResponse\"\x00\x12S\n\x08GetReady\x12!.cv.runtime.proto.GetReadyRequest\x1a\".cv.runtime.proto.GetReadyResponse\"\x00\x12y\n\x1cGetDeepweedOutputByTimestamp\x12\x35.cv.runtime.proto.GetDeepweedOutputByTimestampRequest\x1a .cv.runtime.proto.DeepweedOutput\"\x00\x12\x8f\x01\n\x1cGetRecommendedStrobeSettings\x12\x35.cv.runtime.proto.GetRecommendedStrobeSettingsRequest\x1a\x36.cv.runtime.proto.GetRecommendedStrobeSettingsResponse\"\x00\x12Y\n\nP2PCapture\x12#.cv.runtime.proto.P2PCaptureRequest\x1a$.cv.runtime.proto.P2PCaptureResponse\"\x00\x12t\n\x13SetAutoWhitebalance\x12,.cv.runtime.proto.SetAutoWhitebalanceRequest\x1a-.cv.runtime.proto.SetAutoWhitebalanceResponse\"\x00\x12k\n\x15GetNextDeepweedOutput\x12..cv.runtime.proto.GetNextDeepweedOutputRequest\x1a .cv.runtime.proto.DeepweedOutput\"\x00\x12\x61\n\x10GetNextP2POutput\x12).cv.runtime.proto.GetNextP2POutputRequest\x1a .cv.runtime.proto.P2POutputProto\"\x00\x12n\n\x11SetTargetingState\x12*.cv.runtime.proto.SetTargetingStateRequest\x1a+.cv.runtime.proto.SetTargetingStateResponse\"\x00\x12\x86\x01\n\x19P2PBufferringBurstCapture\x12\x32.cv.runtime.proto.P2PBufferringBurstCaptureRequest\x1a\x33.cv.runtime.proto.P2PBufferringBurstCaptureResponse\"\x00\x12q\n\x12GetNextFocusMetric\x12+.cv.runtime.proto.GetNextFocusMetricRequest\x1a,.cv.runtime.proto.GetNextFocusMetricResponse\"\x00\x12\x62\n\rRemoveDataDir\x12&.cv.runtime.proto.RemoveDataDirRequest\x1a\'.cv.runtime.proto.RemoveDataDirResponse\"\x00\x12\x65\n\x0eGetLastNImages\x12#.cv.runtime.proto.LastNImageRequest\x1a*.cv.runtime.proto.ImageAndMetadataResponse\"\x00\x30\x01\x12\x62\n\x16GetComputeCapabilities\x12\x17.cv.runtime.proto.Empty\x1a-.cv.runtime.proto.ComputeCapabilitiesResponse\"\x00\x12n\n\x1cGetSupportedTensorRTVersions\x12\x17.cv.runtime.proto.Empty\x1a\x33.cv.runtime.proto.SupportedTensorRTVersionsResponse\"\x00\x12N\n\x18ReloadCategoryCollection\x12\x17.cv.runtime.proto.Empty\x1a\x17.cv.runtime.proto.Empty\"\x00\x12\x63\n\x15GetCategoryCollection\x12\x17.cv.runtime.proto.Empty\x1a/.cv.runtime.proto.GetCategoryCollectionResponse\"\x00\x12S\n\rGetErrorState\x12\x17.cv.runtime.proto.Empty\x1a\'.cv.runtime.proto.GetErrorStateResponse\"\x00\x12z\n\x15SnapshotPredictImages\x12..cv.runtime.proto.SnapshotPredictImagesRequest\x1a/.cv.runtime.proto.SnapshotPredictImagesResponse\"\x00\x12}\n\x16GetChipForPredictImage\x12/.cv.runtime.proto.GetChipForPredictImageRequest\x1a\x30.cv.runtime.proto.GetChipForPredictImageResponse\"\x00\x42\nZ\x08proto/cvb\x06proto3'
  ,
  dependencies=[lib_dot_common_dot_camera_dot_proto_dot_camera__pb2.DESCRIPTOR,proto_dot_cv_dot_cv__pb2.DESCRIPTOR,weed__tracking_dot_proto_dot_weed__tracking__pb2.DESCRIPTOR,])

_BUFFERUSECASE = _descriptor.EnumDescriptor(
  name='BufferUseCase',
  full_name='cv.runtime.proto.BufferUseCase',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='P2P', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='OpticalFlow', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Predict', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Drive', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14221,
  serialized_end=14286,
)
_sym_db.RegisterEnumDescriptor(_BUFFERUSECASE)

BufferUseCase = enum_type_wrapper.EnumTypeWrapper(_BUFFERUSECASE)
_HITCLASS = _descriptor.EnumDescriptor(
  name='HitClass',
  full_name='cv.runtime.proto.HitClass',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WEED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CROP', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PLANT', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14288,
  serialized_end=14329,
)
_sym_db.RegisterEnumDescriptor(_HITCLASS)

HitClass = enum_type_wrapper.EnumTypeWrapper(_HITCLASS)
_SCOREQUEUETYPE = _descriptor.EnumDescriptor(
  name='ScoreQueueType',
  full_name='cv.runtime.proto.ScoreQueueType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PREDICT', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CHIP', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14331,
  serialized_end=14370,
)
_sym_db.RegisterEnumDescriptor(_SCOREQUEUETYPE)

ScoreQueueType = enum_type_wrapper.EnumTypeWrapper(_SCOREQUEUETYPE)
_ERRORTYPE = _descriptor.EnumDescriptor(
  name='ErrorType',
  full_name='cv.runtime.proto.ErrorType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GRAB', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CONNECTION', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NO_IMPLEMENTATION', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NO_IMAGE_IN_LAST_MINUTE', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14372,
  serialized_end=14471,
)
_sym_db.RegisterEnumDescriptor(_ERRORTYPE)

ErrorType = enum_type_wrapper.EnumTypeWrapper(_ERRORTYPE)
P2P = 0
OpticalFlow = 1
Predict = 2
Drive = 3
WEED = 0
CROP = 1
PLANT = 2
PREDICT = 0
CHIP = 1
NONE = 0
GRAB = 1
CONNECTION = 2
NO_IMPLEMENTATION = 3
NO_IMAGE_IN_LAST_MINUTE = 4



_TARGETSAFETYZONE = _descriptor.Descriptor(
  name='TargetSafetyZone',
  full_name='cv.runtime.proto.TargetSafetyZone',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='up', full_name='cv.runtime.proto.TargetSafetyZone.up', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='down', full_name='cv.runtime.proto.TargetSafetyZone.down', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='left', full_name='cv.runtime.proto.TargetSafetyZone.left', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='right', full_name='cv.runtime.proto.TargetSafetyZone.right', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=153,
  serialized_end=226,
)


_P2PCONTEXT = _descriptor.Descriptor(
  name='P2PContext',
  full_name='cv.runtime.proto.P2PContext',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='predict_cam_id', full_name='cv.runtime.proto.P2PContext.predict_cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_timestamp_ms', full_name='cv.runtime.proto.P2PContext.predict_timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_coord_x', full_name='cv.runtime.proto.P2PContext.predict_coord_x', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_coord_y', full_name='cv.runtime.proto.P2PContext.predict_coord_y', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=228,
  serialized_end=344,
)


_SETP2PCONTEXTREQUEST = _descriptor.Descriptor(
  name='SetP2PContextRequest',
  full_name='cv.runtime.proto.SetP2PContextRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_cam_id', full_name='cv.runtime.proto.SetP2PContextRequest.target_cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='primary_context', full_name='cv.runtime.proto.SetP2PContextRequest.primary_context', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='secondary_context', full_name='cv.runtime.proto.SetP2PContextRequest.secondary_context', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_zone', full_name='cv.runtime.proto.SetP2PContextRequest.safety_zone', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_id', full_name='cv.runtime.proto.SetP2PContextRequest.target_id', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_secondary_context', full_name='cv.runtime.proto.SetP2PContextRequest._secondary_context',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=347,
  serialized_end=607,
)


_SETP2PCONTEXTRESPONSE = _descriptor.Descriptor(
  name='SetP2PContextResponse',
  full_name='cv.runtime.proto.SetP2PContextResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=609,
  serialized_end=632,
)


_GETCAMERADIMENSIONSREQUEST = _descriptor.Descriptor(
  name='GetCameraDimensionsRequest',
  full_name='cv.runtime.proto.GetCameraDimensionsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetCameraDimensionsRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=634,
  serialized_end=678,
)


_GETCAMERADIMENSIONSRESPONSE = _descriptor.Descriptor(
  name='GetCameraDimensionsResponse',
  full_name='cv.runtime.proto.GetCameraDimensionsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='width', full_name='cv.runtime.proto.GetCameraDimensionsResponse.width', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='cv.runtime.proto.GetCameraDimensionsResponse.height', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='transpose', full_name='cv.runtime.proto.GetCameraDimensionsResponse.transpose', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=680,
  serialized_end=759,
)


_STARTP2PDATACAPTUREREQUEST = _descriptor.Descriptor(
  name='StartP2PDataCaptureRequest',
  full_name='cv.runtime.proto.StartP2PDataCaptureRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_cam_id', full_name='cv.runtime.proto.StartP2PDataCaptureRequest.target_cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='capture_miss_rate', full_name='cv.runtime.proto.StartP2PDataCaptureRequest.capture_miss_rate', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='capture_success_rate', full_name='cv.runtime.proto.StartP2PDataCaptureRequest.capture_success_rate', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='capture_enabled', full_name='cv.runtime.proto.StartP2PDataCaptureRequest.capture_enabled', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='capture_path', full_name='cv.runtime.proto.StartP2PDataCaptureRequest.capture_path', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='after_timestamp_ms', full_name='cv.runtime.proto.StartP2PDataCaptureRequest.after_timestamp_ms', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=762,
  serialized_end=945,
)


_POINTDETECTIONCATEGORY = _descriptor.Descriptor(
  name='PointDetectionCategory',
  full_name='cv.runtime.proto.PointDetectionCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='threshold', full_name='cv.runtime.proto.PointDetectionCategory.threshold', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='category', full_name='cv.runtime.proto.PointDetectionCategory.category', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=947,
  serialized_end=1008,
)


_SEGMENTATIONDETECTIONCATEGORY = _descriptor.Descriptor(
  name='SegmentationDetectionCategory',
  full_name='cv.runtime.proto.SegmentationDetectionCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='threshold', full_name='cv.runtime.proto.SegmentationDetectionCategory.threshold', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='category', full_name='cv.runtime.proto.SegmentationDetectionCategory.category', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_radius_in', full_name='cv.runtime.proto.SegmentationDetectionCategory.safety_radius_in', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1010,
  serialized_end=1104,
)


_DEEPWEEDDETECTIONCRITERIASETTING = _descriptor.Descriptor(
  name='DeepweedDetectionCriteriaSetting',
  full_name='cv.runtime.proto.DeepweedDetectionCriteriaSetting',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='point_categories', full_name='cv.runtime.proto.DeepweedDetectionCriteriaSetting.point_categories', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_point_threshold', full_name='cv.runtime.proto.DeepweedDetectionCriteriaSetting.weed_point_threshold', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_point_threshold', full_name='cv.runtime.proto.DeepweedDetectionCriteriaSetting.crop_point_threshold', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='segmentation_categories', full_name='cv.runtime.proto.DeepweedDetectionCriteriaSetting.segmentation_categories', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1107,
  serialized_end=1351,
)


_SETDEEPWEEDDETECTIONCRITERIAREQUEST = _descriptor.Descriptor(
  name='SetDeepweedDetectionCriteriaRequest',
  full_name='cv.runtime.proto.SetDeepweedDetectionCriteriaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weed_point_threshold', full_name='cv.runtime.proto.SetDeepweedDetectionCriteriaRequest.weed_point_threshold', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_point_threshold', full_name='cv.runtime.proto.SetDeepweedDetectionCriteriaRequest.crop_point_threshold', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='point_categories', full_name='cv.runtime.proto.SetDeepweedDetectionCriteriaRequest.point_categories', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='segmentation_categories', full_name='cv.runtime.proto.SetDeepweedDetectionCriteriaRequest.segmentation_categories', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1354,
  serialized_end=1601,
)


_SETDEEPWEEDDETECTIONCRITERIARESPONSE = _descriptor.Descriptor(
  name='SetDeepweedDetectionCriteriaResponse',
  full_name='cv.runtime.proto.SetDeepweedDetectionCriteriaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1603,
  serialized_end=1641,
)


_GETDEEPWEEDDETECTIONCRITERIAREQUEST = _descriptor.Descriptor(
  name='GetDeepweedDetectionCriteriaRequest',
  full_name='cv.runtime.proto.GetDeepweedDetectionCriteriaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1643,
  serialized_end=1680,
)


_GETDEEPWEEDDETECTIONCRITERIARESPONSE = _descriptor.Descriptor(
  name='GetDeepweedDetectionCriteriaResponse',
  full_name='cv.runtime.proto.GetDeepweedDetectionCriteriaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weed_point_threshold', full_name='cv.runtime.proto.GetDeepweedDetectionCriteriaResponse.weed_point_threshold', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_point_threshold', full_name='cv.runtime.proto.GetDeepweedDetectionCriteriaResponse.crop_point_threshold', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='point_categories', full_name='cv.runtime.proto.GetDeepweedDetectionCriteriaResponse.point_categories', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='segmentation_categories', full_name='cv.runtime.proto.GetDeepweedDetectionCriteriaResponse.segmentation_categories', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1683,
  serialized_end=1931,
)


_GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST = _descriptor.Descriptor(
  name='GetDeepweedSupportedCategoriesRequest',
  full_name='cv.runtime.proto.GetDeepweedSupportedCategoriesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetDeepweedSupportedCategoriesRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_cam_id', full_name='cv.runtime.proto.GetDeepweedSupportedCategoriesRequest._cam_id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1933,
  serialized_end=2004,
)


_GETDEEPWEEDSUPPORTEDCATEGORIESRESPONSE = _descriptor.Descriptor(
  name='GetDeepweedSupportedCategoriesResponse',
  full_name='cv.runtime.proto.GetDeepweedSupportedCategoriesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='segmentation_categories', full_name='cv.runtime.proto.GetDeepweedSupportedCategoriesResponse.segmentation_categories', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='point_categories', full_name='cv.runtime.proto.GetDeepweedSupportedCategoriesResponse.point_categories', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2006,
  serialized_end=2105,
)


_GETDEEPWEEDINDEXTOCATEGORYREQUEST = _descriptor.Descriptor(
  name='GetDeepweedIndexToCategoryRequest',
  full_name='cv.runtime.proto.GetDeepweedIndexToCategoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2107,
  serialized_end=2158,
)


_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY = _descriptor.Descriptor(
  name='WeedPointIndexToCategoryEntry',
  full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2570,
  serialized_end=2633,
)

_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY = _descriptor.Descriptor(
  name='CropPointIndexToCategoryEntry',
  full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2635,
  serialized_end=2698,
)

_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY = _descriptor.Descriptor(
  name='IntersectionIndexToCategoryEntry',
  full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2700,
  serialized_end=2766,
)

_GETDEEPWEEDINDEXTOCATEGORYRESPONSE = _descriptor.Descriptor(
  name='GetDeepweedIndexToCategoryResponse',
  full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weed_point_index_to_category', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.weed_point_index_to_category', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_point_index_to_category', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.crop_point_index_to_category', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intersection_index_to_category', full_name='cv.runtime.proto.GetDeepweedIndexToCategoryResponse.intersection_index_to_category', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY, _GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY, _GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2161,
  serialized_end=2766,
)


_GETPREDICTCAMMATRIXREQUEST = _descriptor.Descriptor(
  name='GetPredictCamMatrixRequest',
  full_name='cv.runtime.proto.GetPredictCamMatrixRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='predict_cam_id', full_name='cv.runtime.proto.GetPredictCamMatrixRequest.predict_cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2768,
  serialized_end=2820,
)


_GETPREDICTCAMDISTORTIONCOEFFICIENTSREQUEST = _descriptor.Descriptor(
  name='GetPredictCamDistortionCoefficientsRequest',
  full_name='cv.runtime.proto.GetPredictCamDistortionCoefficientsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='predict_cam_id', full_name='cv.runtime.proto.GetPredictCamDistortionCoefficientsRequest.predict_cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2822,
  serialized_end=2890,
)


_SETCAMERASETTINGSREQUEST = _descriptor.Descriptor(
  name='SetCameraSettingsRequest',
  full_name='cv.runtime.proto.SetCameraSettingsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_ids', full_name='cv.runtime.proto.SetCameraSettingsRequest.cam_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='cv.runtime.proto.SetCameraSettingsRequest.exposure_us', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gamma', full_name='cv.runtime.proto.SetCameraSettingsRequest.gamma', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_db', full_name='cv.runtime.proto.SetCameraSettingsRequest.gain_db', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='light_source_preset', full_name='cv.runtime.proto.SetCameraSettingsRequest.light_source_preset', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_red', full_name='cv.runtime.proto.SetCameraSettingsRequest.wb_ratio_red', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_green', full_name='cv.runtime.proto.SetCameraSettingsRequest.wb_ratio_green', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_blue', full_name='cv.runtime.proto.SetCameraSettingsRequest.wb_ratio_blue', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='roi_offset_x', full_name='cv.runtime.proto.SetCameraSettingsRequest.roi_offset_x', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='roi_offset_y', full_name='cv.runtime.proto.SetCameraSettingsRequest.roi_offset_y', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mirror', full_name='cv.runtime.proto.SetCameraSettingsRequest.mirror', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='flip', full_name='cv.runtime.proto.SetCameraSettingsRequest.flip', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobing', full_name='cv.runtime.proto.SetCameraSettingsRequest.strobing', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ptp', full_name='cv.runtime.proto.SetCameraSettingsRequest.ptp', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='auto_whitebalance', full_name='cv.runtime.proto.SetCameraSettingsRequest.auto_whitebalance', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_exposure_us', full_name='cv.runtime.proto.SetCameraSettingsRequest._exposure_us',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_gamma', full_name='cv.runtime.proto.SetCameraSettingsRequest._gamma',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_gain_db', full_name='cv.runtime.proto.SetCameraSettingsRequest._gain_db',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_light_source_preset', full_name='cv.runtime.proto.SetCameraSettingsRequest._light_source_preset',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_wb_ratio_red', full_name='cv.runtime.proto.SetCameraSettingsRequest._wb_ratio_red',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_wb_ratio_green', full_name='cv.runtime.proto.SetCameraSettingsRequest._wb_ratio_green',
      index=5, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_wb_ratio_blue', full_name='cv.runtime.proto.SetCameraSettingsRequest._wb_ratio_blue',
      index=6, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_roi_offset_x', full_name='cv.runtime.proto.SetCameraSettingsRequest._roi_offset_x',
      index=7, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_roi_offset_y', full_name='cv.runtime.proto.SetCameraSettingsRequest._roi_offset_y',
      index=8, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_mirror', full_name='cv.runtime.proto.SetCameraSettingsRequest._mirror',
      index=9, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_flip', full_name='cv.runtime.proto.SetCameraSettingsRequest._flip',
      index=10, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_strobing', full_name='cv.runtime.proto.SetCameraSettingsRequest._strobing',
      index=11, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_ptp', full_name='cv.runtime.proto.SetCameraSettingsRequest._ptp',
      index=12, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_auto_whitebalance', full_name='cv.runtime.proto.SetCameraSettingsRequest._auto_whitebalance',
      index=13, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2893,
  serialized_end=3540,
)


_SETCAMERASETTINGSRESPONSE = _descriptor.Descriptor(
  name='SetCameraSettingsResponse',
  full_name='cv.runtime.proto.SetCameraSettingsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_ids', full_name='cv.runtime.proto.SetCameraSettingsResponse.cam_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3542,
  serialized_end=3586,
)


_SETAUTOWHITEBALANCEREQUEST = _descriptor.Descriptor(
  name='SetAutoWhitebalanceRequest',
  full_name='cv.runtime.proto.SetAutoWhitebalanceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enable', full_name='cv.runtime.proto.SetAutoWhitebalanceRequest.enable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_ids', full_name='cv.runtime.proto.SetAutoWhitebalanceRequest.cam_ids', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3588,
  serialized_end=3649,
)


_SETAUTOWHITEBALANCERESPONSE = _descriptor.Descriptor(
  name='SetAutoWhitebalanceResponse',
  full_name='cv.runtime.proto.SetAutoWhitebalanceResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3651,
  serialized_end=3680,
)


_GETCAMERASETTINGSREQUEST = _descriptor.Descriptor(
  name='GetCameraSettingsRequest',
  full_name='cv.runtime.proto.GetCameraSettingsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_ids', full_name='cv.runtime.proto.GetCameraSettingsRequest.cam_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3682,
  serialized_end=3725,
)


_CAMERASETTINGSRESPONSE = _descriptor.Descriptor(
  name='CameraSettingsResponse',
  full_name='cv.runtime.proto.CameraSettingsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.CameraSettingsResponse.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='cv.runtime.proto.CameraSettingsResponse.exposure_us', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gamma', full_name='cv.runtime.proto.CameraSettingsResponse.gamma', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_db', full_name='cv.runtime.proto.CameraSettingsResponse.gain_db', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='light_source_preset', full_name='cv.runtime.proto.CameraSettingsResponse.light_source_preset', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_red', full_name='cv.runtime.proto.CameraSettingsResponse.wb_ratio_red', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_green', full_name='cv.runtime.proto.CameraSettingsResponse.wb_ratio_green', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_blue', full_name='cv.runtime.proto.CameraSettingsResponse.wb_ratio_blue', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='roi_width', full_name='cv.runtime.proto.CameraSettingsResponse.roi_width', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='roi_height', full_name='cv.runtime.proto.CameraSettingsResponse.roi_height', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='roi_offset_x', full_name='cv.runtime.proto.CameraSettingsResponse.roi_offset_x', index=10,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='roi_offset_y', full_name='cv.runtime.proto.CameraSettingsResponse.roi_offset_y', index=11,
      number=12, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gpu_id', full_name='cv.runtime.proto.CameraSettingsResponse.gpu_id', index=12,
      number=13, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mirror', full_name='cv.runtime.proto.CameraSettingsResponse.mirror', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='flip', full_name='cv.runtime.proto.CameraSettingsResponse.flip', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobing', full_name='cv.runtime.proto.CameraSettingsResponse.strobing', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ptp', full_name='cv.runtime.proto.CameraSettingsResponse.ptp', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='auto_whitebalance', full_name='cv.runtime.proto.CameraSettingsResponse.auto_whitebalance', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_exposure_us', full_name='cv.runtime.proto.CameraSettingsResponse._exposure_us',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_gamma', full_name='cv.runtime.proto.CameraSettingsResponse._gamma',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_gain_db', full_name='cv.runtime.proto.CameraSettingsResponse._gain_db',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_light_source_preset', full_name='cv.runtime.proto.CameraSettingsResponse._light_source_preset',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_wb_ratio_red', full_name='cv.runtime.proto.CameraSettingsResponse._wb_ratio_red',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_wb_ratio_green', full_name='cv.runtime.proto.CameraSettingsResponse._wb_ratio_green',
      index=5, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_wb_ratio_blue', full_name='cv.runtime.proto.CameraSettingsResponse._wb_ratio_blue',
      index=6, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_roi_width', full_name='cv.runtime.proto.CameraSettingsResponse._roi_width',
      index=7, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_roi_height', full_name='cv.runtime.proto.CameraSettingsResponse._roi_height',
      index=8, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_roi_offset_x', full_name='cv.runtime.proto.CameraSettingsResponse._roi_offset_x',
      index=9, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_roi_offset_y', full_name='cv.runtime.proto.CameraSettingsResponse._roi_offset_y',
      index=10, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_gpu_id', full_name='cv.runtime.proto.CameraSettingsResponse._gpu_id',
      index=11, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_auto_whitebalance', full_name='cv.runtime.proto.CameraSettingsResponse._auto_whitebalance',
      index=12, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3728,
  serialized_end=4421,
)


_GETCAMERASETTINGSRESPONSE = _descriptor.Descriptor(
  name='GetCameraSettingsResponse',
  full_name='cv.runtime.proto.GetCameraSettingsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='camera_settings_response', full_name='cv.runtime.proto.GetCameraSettingsResponse.camera_settings_response', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4423,
  serialized_end=4526,
)


_STARTBURSTRECORDFRAMESREQUEST = _descriptor.Descriptor(
  name='StartBurstRecordFramesRequest',
  full_name='cv.runtime.proto.StartBurstRecordFramesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.StartBurstRecordFramesRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_ms', full_name='cv.runtime.proto.StartBurstRecordFramesRequest.duration_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='path', full_name='cv.runtime.proto.StartBurstRecordFramesRequest.path', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dont_capture_predict_image', full_name='cv.runtime.proto.StartBurstRecordFramesRequest.dont_capture_predict_image', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='downsample_factor', full_name='cv.runtime.proto.StartBurstRecordFramesRequest.downsample_factor', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4529,
  serialized_end=4674,
)


_STARTBURSTRECORDFRAMESRESPONSE = _descriptor.Descriptor(
  name='StartBurstRecordFramesResponse',
  full_name='cv.runtime.proto.StartBurstRecordFramesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4676,
  serialized_end=4708,
)


_STOPBURSTRECORDFRAMESREQUEST = _descriptor.Descriptor(
  name='StopBurstRecordFramesRequest',
  full_name='cv.runtime.proto.StopBurstRecordFramesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.StopBurstRecordFramesRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_frame_timestamp_ms', full_name='cv.runtime.proto.StopBurstRecordFramesRequest.last_frame_timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_last_frame_timestamp_ms', full_name='cv.runtime.proto.StopBurstRecordFramesRequest._last_frame_timestamp_ms',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=4710,
  serialized_end=4822,
)


_STOPBURSTRECORDFRAMESRESPONSE = _descriptor.Descriptor(
  name='StopBurstRecordFramesResponse',
  full_name='cv.runtime.proto.StopBurstRecordFramesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4824,
  serialized_end=4855,
)


_P2POUTPUTPROTO = _descriptor.Descriptor(
  name='P2POutputProto',
  full_name='cv.runtime.proto.P2POutputProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='matched', full_name='cv.runtime.proto.P2POutputProto.matched', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_coord_x', full_name='cv.runtime.proto.P2POutputProto.target_coord_x', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_coord_y', full_name='cv.runtime.proto.P2POutputProto.target_coord_y', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_timestamp_ms', full_name='cv.runtime.proto.P2POutputProto.target_timestamp_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_timestamp_ms', full_name='cv.runtime.proto.P2POutputProto.predict_timestamp_ms', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safe', full_name='cv.runtime.proto.P2POutputProto.safe', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_coord_x', full_name='cv.runtime.proto.P2POutputProto.predict_coord_x', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_coord_y', full_name='cv.runtime.proto.P2POutputProto.predict_coord_y', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_cam', full_name='cv.runtime.proto.P2POutputProto.predict_cam', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4858,
  serialized_end=5083,
)


_GETNEXTP2POUTPUTREQUEST = _descriptor.Descriptor(
  name='GetNextP2POutputRequest',
  full_name='cv.runtime.proto.GetNextP2POutputRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetNextP2POutputRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GetNextP2POutputRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='cv.runtime.proto.GetNextP2POutputRequest.timeout_ms', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5085,
  serialized_end=5168,
)


_GETCONNECTORSREQUEST = _descriptor.Descriptor(
  name='GetConnectorsRequest',
  full_name='cv.runtime.proto.GetConnectorsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_ids', full_name='cv.runtime.proto.GetConnectorsRequest.cam_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connector_ids', full_name='cv.runtime.proto.GetConnectorsRequest.connector_ids', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5170,
  serialized_end=5232,
)


_CONNECTORRESPONSE = _descriptor.Descriptor(
  name='ConnectorResponse',
  full_name='cv.runtime.proto.ConnectorResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.ConnectorResponse.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connector_id', full_name='cv.runtime.proto.ConnectorResponse.connector_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_enabled', full_name='cv.runtime.proto.ConnectorResponse.is_enabled', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reduction_ratio', full_name='cv.runtime.proto.ConnectorResponse.reduction_ratio', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5234,
  serialized_end=5336,
)


_GETCONNECTORSRESPONSE = _descriptor.Descriptor(
  name='GetConnectorsResponse',
  full_name='cv.runtime.proto.GetConnectorsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='connector_response', full_name='cv.runtime.proto.GetConnectorsResponse.connector_response', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5338,
  serialized_end=5426,
)


_SETCONNECTORSREQUEST = _descriptor.Descriptor(
  name='SetConnectorsRequest',
  full_name='cv.runtime.proto.SetConnectorsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_ids', full_name='cv.runtime.proto.SetConnectorsRequest.cam_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connector_ids', full_name='cv.runtime.proto.SetConnectorsRequest.connector_ids', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_enabled', full_name='cv.runtime.proto.SetConnectorsRequest.is_enabled', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reduction_ratio', full_name='cv.runtime.proto.SetConnectorsRequest.reduction_ratio', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_is_enabled', full_name='cv.runtime.proto.SetConnectorsRequest._is_enabled',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_reduction_ratio', full_name='cv.runtime.proto.SetConnectorsRequest._reduction_ratio',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=5429,
  serialized_end=5581,
)


_SETCONNECTORSRESPONSE = _descriptor.Descriptor(
  name='SetConnectorsResponse',
  full_name='cv.runtime.proto.SetConnectorsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5583,
  serialized_end=5606,
)


_NODETIMING_STATETIMINGSENTRY = _descriptor.Descriptor(
  name='StateTimingsEntry',
  full_name='cv.runtime.proto.NodeTiming.StateTimingsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='cv.runtime.proto.NodeTiming.StateTimingsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='cv.runtime.proto.NodeTiming.StateTimingsEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5811,
  serialized_end=5862,
)

_NODETIMING = _descriptor.Descriptor(
  name='NodeTiming',
  full_name='cv.runtime.proto.NodeTiming',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='cv.runtime.proto.NodeTiming.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fps_mean', full_name='cv.runtime.proto.NodeTiming.fps_mean', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fps_99pct', full_name='cv.runtime.proto.NodeTiming.fps_99pct', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latency_ms_mean', full_name='cv.runtime.proto.NodeTiming.latency_ms_mean', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latency_ms_99pct', full_name='cv.runtime.proto.NodeTiming.latency_ms_99pct', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='cv.runtime.proto.NodeTiming.state', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state_timings', full_name='cv.runtime.proto.NodeTiming.state_timings', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_NODETIMING_STATETIMINGSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5609,
  serialized_end=5862,
)


_GETTIMINGREQUEST = _descriptor.Descriptor(
  name='GetTimingRequest',
  full_name='cv.runtime.proto.GetTimingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5864,
  serialized_end=5882,
)


_GETTIMINGRESPONSE = _descriptor.Descriptor(
  name='GetTimingResponse',
  full_name='cv.runtime.proto.GetTimingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='node_timing', full_name='cv.runtime.proto.GetTimingResponse.node_timing', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5884,
  serialized_end=5954,
)


_PREDICTREQUEST = _descriptor.Descriptor(
  name='PredictRequest',
  full_name='cv.runtime.proto.PredictRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.PredictRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='file_paths', full_name='cv.runtime.proto.PredictRequest.file_paths', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamps_ms', full_name='cv.runtime.proto.PredictRequest.timestamps_ms', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5956,
  serialized_end=6031,
)


_PREDICTRESPONSE = _descriptor.Descriptor(
  name='PredictResponse',
  full_name='cv.runtime.proto.PredictResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6033,
  serialized_end=6050,
)


_LOADANDQUEUEREQUEST = _descriptor.Descriptor(
  name='LoadAndQueueRequest',
  full_name='cv.runtime.proto.LoadAndQueueRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.LoadAndQueueRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='file_paths', full_name='cv.runtime.proto.LoadAndQueueRequest.file_paths', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamps_ms', full_name='cv.runtime.proto.LoadAndQueueRequest.timestamps_ms', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6052,
  serialized_end=6132,
)


_LOADANDQUEUERESPONSE = _descriptor.Descriptor(
  name='LoadAndQueueResponse',
  full_name='cv.runtime.proto.LoadAndQueueResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6134,
  serialized_end=6156,
)


_SETIMAGEREQUEST = _descriptor.Descriptor(
  name='SetImageRequest',
  full_name='cv.runtime.proto.SetImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.SetImageRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='file_path', full_name='cv.runtime.proto.SetImageRequest.file_path', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6158,
  serialized_end=6210,
)


_SETIMAGERESPONSE = _descriptor.Descriptor(
  name='SetImageResponse',
  full_name='cv.runtime.proto.SetImageResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6212,
  serialized_end=6230,
)


_UNSETIMAGEREQUEST = _descriptor.Descriptor(
  name='UnsetImageRequest',
  full_name='cv.runtime.proto.UnsetImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.UnsetImageRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6232,
  serialized_end=6267,
)


_UNSETIMAGERESPONSE = _descriptor.Descriptor(
  name='UnsetImageResponse',
  full_name='cv.runtime.proto.UnsetImageResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6269,
  serialized_end=6289,
)


_GETMODELPATHSREQUEST = _descriptor.Descriptor(
  name='GetModelPathsRequest',
  full_name='cv.runtime.proto.GetModelPathsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6291,
  serialized_end=6313,
)


_GETMODELPATHSRESPONSE = _descriptor.Descriptor(
  name='GetModelPathsResponse',
  full_name='cv.runtime.proto.GetModelPathsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='p2p', full_name='cv.runtime.proto.GetModelPathsResponse.p2p', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed', full_name='cv.runtime.proto.GetModelPathsResponse.deepweed', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='furrows', full_name='cv.runtime.proto.GetModelPathsResponse.furrows', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_p2p', full_name='cv.runtime.proto.GetModelPathsResponse._p2p',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_deepweed', full_name='cv.runtime.proto.GetModelPathsResponse._deepweed',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_furrows', full_name='cv.runtime.proto.GetModelPathsResponse._furrows',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=6315,
  serialized_end=6434,
)


_GETCAMERATEMPERATURESREQUEST = _descriptor.Descriptor(
  name='GetCameraTemperaturesRequest',
  full_name='cv.runtime.proto.GetCameraTemperaturesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6436,
  serialized_end=6466,
)


_CAMERATEMPERATURE = _descriptor.Descriptor(
  name='CameraTemperature',
  full_name='cv.runtime.proto.CameraTemperature',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.CameraTemperature.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature', full_name='cv.runtime.proto.CameraTemperature.temperature', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6468,
  serialized_end=6524,
)


_GETCAMERATEMPERATURESRESPONSE = _descriptor.Descriptor(
  name='GetCameraTemperaturesResponse',
  full_name='cv.runtime.proto.GetCameraTemperaturesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='temperature', full_name='cv.runtime.proto.GetCameraTemperaturesResponse.temperature', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6526,
  serialized_end=6615,
)


_GEOLLA = _descriptor.Descriptor(
  name='GeoLLA',
  full_name='cv.runtime.proto.GeoLLA',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lat', full_name='cv.runtime.proto.GeoLLA.lat', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lng', full_name='cv.runtime.proto.GeoLLA.lng', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alt', full_name='cv.runtime.proto.GeoLLA.alt', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GeoLLA.timestamp_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_lat', full_name='cv.runtime.proto.GeoLLA._lat',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_lng', full_name='cv.runtime.proto.GeoLLA._lng',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_alt', full_name='cv.runtime.proto.GeoLLA._alt',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_timestamp_ms', full_name='cv.runtime.proto.GeoLLA._timestamp_ms',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=6618,
  serialized_end=6748,
)


_GEOECEF = _descriptor.Descriptor(
  name='GeoECEF',
  full_name='cv.runtime.proto.GeoECEF',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='cv.runtime.proto.GeoECEF.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='cv.runtime.proto.GeoECEF.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='cv.runtime.proto.GeoECEF.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GeoECEF.timestamp_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_x', full_name='cv.runtime.proto.GeoECEF._x',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_y', full_name='cv.runtime.proto.GeoECEF._y',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_z', full_name='cv.runtime.proto.GeoECEF._z',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_timestamp_ms', full_name='cv.runtime.proto.GeoECEF._timestamp_ms',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=6750,
  serialized_end=6869,
)


_SETGPSLOCATIONREQUEST = _descriptor.Descriptor(
  name='SetGPSLocationRequest',
  full_name='cv.runtime.proto.SetGPSLocationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lla', full_name='cv.runtime.proto.SetGPSLocationRequest.lla', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef', full_name='cv.runtime.proto.SetGPSLocationRequest.ecef', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6871,
  serialized_end=6974,
)


_SETGPSLOCATIONRESPONSE = _descriptor.Descriptor(
  name='SetGPSLocationResponse',
  full_name='cv.runtime.proto.SetGPSLocationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6976,
  serialized_end=7000,
)


_SETIMPLEMENTSTATUSREQUEST = _descriptor.Descriptor(
  name='SetImplementStatusRequest',
  full_name='cv.runtime.proto.SetImplementStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lifted', full_name='cv.runtime.proto.SetImplementStatusRequest.lifted', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='estopped', full_name='cv.runtime.proto.SetImplementStatusRequest.estopped', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7002,
  serialized_end=7063,
)


_SETIMPLEMENTSTATUSRESPONSE = _descriptor.Descriptor(
  name='SetImplementStatusResponse',
  full_name='cv.runtime.proto.SetImplementStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7065,
  serialized_end=7093,
)


_SETIMAGESCOREREQUEST = _descriptor.Descriptor(
  name='SetImageScoreRequest',
  full_name='cv.runtime.proto.SetImageScoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score', full_name='cv.runtime.proto.SetImageScoreRequest.score', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.SetImageScoreRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.SetImageScoreRequest.cam_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed_output', full_name='cv.runtime.proto.SetImageScoreRequest.deepweed_output', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7096,
  serialized_end=7230,
)


_SETIMAGESCORERESPONSE = _descriptor.Descriptor(
  name='SetImageScoreResponse',
  full_name='cv.runtime.proto.SetImageScoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7232,
  serialized_end=7255,
)


_GETSCOREQUEUEREQUEST = _descriptor.Descriptor(
  name='GetScoreQueueRequest',
  full_name='cv.runtime.proto.GetScoreQueueRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_type', full_name='cv.runtime.proto.GetScoreQueueRequest.score_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7257,
  serialized_end=7299,
)


_SCOREOBJECT = _descriptor.Descriptor(
  name='ScoreObject',
  full_name='cv.runtime.proto.ScoreObject',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score', full_name='cv.runtime.proto.ScoreObject.score', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.ScoreObject.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.ScoreObject.cam_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7301,
  serialized_end=7367,
)


_GETSCOREQUEUERESPONSE = _descriptor.Descriptor(
  name='GetScoreQueueResponse',
  full_name='cv.runtime.proto.GetScoreQueueResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_object', full_name='cv.runtime.proto.GetScoreQueueResponse.score_object', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7369,
  serialized_end=7445,
)


_GETMAXIMAGESCOREREQUEST = _descriptor.Descriptor(
  name='GetMaxImageScoreRequest',
  full_name='cv.runtime.proto.GetMaxImageScoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_type', full_name='cv.runtime.proto.GetMaxImageScoreRequest.score_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7447,
  serialized_end=7492,
)


_GETMAXIMAGESCORERESPONSE = _descriptor.Descriptor(
  name='GetMaxImageScoreResponse',
  full_name='cv.runtime.proto.GetMaxImageScoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score', full_name='cv.runtime.proto.GetMaxImageScoreResponse.score', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='cv.runtime.proto.GetMaxImageScoreResponse.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7494,
  serialized_end=7549,
)


_GETMAXSCOREDIMAGEREQUEST = _descriptor.Descriptor(
  name='GetMaxScoredImageRequest',
  full_name='cv.runtime.proto.GetMaxScoredImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_type', full_name='cv.runtime.proto.GetMaxScoredImageRequest.score_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7551,
  serialized_end=7597,
)


_GETLATESTP2PIMAGEREQUEST = _descriptor.Descriptor(
  name='GetLatestP2PImageRequest',
  full_name='cv.runtime.proto.GetLatestP2PImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_type', full_name='cv.runtime.proto.GetLatestP2PImageRequest.score_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reason', full_name='cv.runtime.proto.GetLatestP2PImageRequest.reason', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7599,
  serialized_end=7701,
)


_GETLATESTIMAGEREQUEST = _descriptor.Descriptor(
  name='GetLatestImageRequest',
  full_name='cv.runtime.proto.GetLatestImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetLatestImageRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7703,
  serialized_end=7742,
)


_GETIMAGENEARTIMESTAMPREQUEST = _descriptor.Descriptor(
  name='GetImageNearTimestampRequest',
  full_name='cv.runtime.proto.GetImageNearTimestampRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetImageNearTimestampRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GetImageNearTimestampRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7744,
  serialized_end=7812,
)


_GETCHIPIMAGEREQUEST = _descriptor.Descriptor(
  name='GetChipImageRequest',
  full_name='cv.runtime.proto.GetChipImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_type', full_name='cv.runtime.proto.GetChipImageRequest.score_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7814,
  serialized_end=7855,
)


_FLUSHQUEUESREQUEST = _descriptor.Descriptor(
  name='FlushQueuesRequest',
  full_name='cv.runtime.proto.FlushQueuesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_type', full_name='cv.runtime.proto.FlushQueuesRequest.score_type', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_queue_type', full_name='cv.runtime.proto.FlushQueuesRequest.score_queue_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_score_queue_type', full_name='cv.runtime.proto.FlushQueuesRequest._score_queue_type',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=7857,
  serialized_end=7983,
)


_FLUSHQUEUESRESPONSE = _descriptor.Descriptor(
  name='FlushQueuesResponse',
  full_name='cv.runtime.proto.FlushQueuesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7985,
  serialized_end=8006,
)


_IMAGEANDMETADATARESPONSE = _descriptor.Descriptor(
  name='ImageAndMetadataResponse',
  full_name='cv.runtime.proto.ImageAndMetadataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bytes', full_name='cv.runtime.proto.ImageAndMetadataResponse.bytes', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width', full_name='cv.runtime.proto.ImageAndMetadataResponse.width', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='cv.runtime.proto.ImageAndMetadataResponse.height', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.ImageAndMetadataResponse.timestamp_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='cv.runtime.proto.ImageAndMetadataResponse.score', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.ImageAndMetadataResponse.cam_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='iso_formatted_time', full_name='cv.runtime.proto.ImageAndMetadataResponse.iso_formatted_time', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lla_lat', full_name='cv.runtime.proto.ImageAndMetadataResponse.lla_lat', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lla_lng', full_name='cv.runtime.proto.ImageAndMetadataResponse.lla_lng', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lla_alt', full_name='cv.runtime.proto.ImageAndMetadataResponse.lla_alt', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lla_timestamp_ms', full_name='cv.runtime.proto.ImageAndMetadataResponse.lla_timestamp_ms', index=10,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_x', full_name='cv.runtime.proto.ImageAndMetadataResponse.ecef_x', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_y', full_name='cv.runtime.proto.ImageAndMetadataResponse.ecef_y', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_z', full_name='cv.runtime.proto.ImageAndMetadataResponse.ecef_z', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_timestamp_ms', full_name='cv.runtime.proto.ImageAndMetadataResponse.ecef_timestamp_ms', index=14,
      number=15, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ppi', full_name='cv.runtime.proto.ImageAndMetadataResponse.ppi', index=15,
      number=16, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score_type', full_name='cv.runtime.proto.ImageAndMetadataResponse.score_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='image_type', full_name='cv.runtime.proto.ImageAndMetadataResponse.image_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_url', full_name='cv.runtime.proto.ImageAndMetadataResponse.model_url', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='cv.runtime.proto.ImageAndMetadataResponse.crop', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_height_columns', full_name='cv.runtime.proto.ImageAndMetadataResponse.weed_height_columns', index=20,
      number=21, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_height_columns', full_name='cv.runtime.proto.ImageAndMetadataResponse.crop_height_columns', index=21,
      number=22, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bbh_offset_mm', full_name='cv.runtime.proto.ImageAndMetadataResponse.bbh_offset_mm', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focus_metric', full_name='cv.runtime.proto.ImageAndMetadataResponse.focus_metric', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='cv.runtime.proto.ImageAndMetadataResponse.exposure_us', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_point_threshold', full_name='cv.runtime.proto.ImageAndMetadataResponse.crop_point_threshold', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_point_threshold', full_name='cv.runtime.proto.ImageAndMetadataResponse.weed_point_threshold', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeding_enabled', full_name='cv.runtime.proto.ImageAndMetadataResponse.weeding_enabled', index=27,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_enabled', full_name='cv.runtime.proto.ImageAndMetadataResponse.thinning_enabled', index=28,
      number=29, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed_id', full_name='cv.runtime.proto.ImageAndMetadataResponse.deepweed_id', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p_id', full_name='cv.runtime.proto.ImageAndMetadataResponse.p2p_id', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed_detections', full_name='cv.runtime.proto.ImageAndMetadataResponse.deepweed_detections', index=31,
      number=32, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='segmentation_threshold', full_name='cv.runtime.proto.ImageAndMetadataResponse.segmentation_threshold', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='simulator_generated', full_name='cv.runtime.proto.ImageAndMetadataResponse.simulator_generated', index=33,
      number=34, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_db', full_name='cv.runtime.proto.ImageAndMetadataResponse.gain_db', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_score_type', full_name='cv.runtime.proto.ImageAndMetadataResponse._score_type',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=8009,
  serialized_end=8825,
)


_CATEGORYPREDICTION = _descriptor.Descriptor(
  name='CategoryPrediction',
  full_name='cv.runtime.proto.CategoryPrediction',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='category', full_name='cv.runtime.proto.CategoryPrediction.category', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='cv.runtime.proto.CategoryPrediction.score', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8827,
  serialized_end=8880,
)


_CHIPPREDICTION = _descriptor.Descriptor(
  name='ChipPrediction',
  full_name='cv.runtime.proto.ChipPrediction',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='cv.runtime.proto.ChipPrediction.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='cv.runtime.proto.ChipPrediction.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='radius', full_name='cv.runtime.proto.ChipPrediction.radius', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_id', full_name='cv.runtime.proto.ChipPrediction.model_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='cv.runtime.proto.ChipPrediction.score', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding_distance', full_name='cv.runtime.proto.ChipPrediction.embedding_distance', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='band_status', full_name='cv.runtime.proto.ChipPrediction.band_status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8883,
  serialized_end=9095,
)


_CHIPIMAGEANDMETADATARESPONSE = _descriptor.Descriptor(
  name='ChipImageAndMetadataResponse',
  full_name='cv.runtime.proto.ChipImageAndMetadataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='image_and_metadata', full_name='cv.runtime.proto.ChipImageAndMetadataResponse.image_and_metadata', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='prediction_metadata', full_name='cv.runtime.proto.ChipImageAndMetadataResponse.prediction_metadata', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9098,
  serialized_end=9263,
)


_CHIPQUEUEINFORMATIONREQUEST = _descriptor.Descriptor(
  name='ChipQueueInformationRequest',
  full_name='cv.runtime.proto.ChipQueueInformationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9265,
  serialized_end=9294,
)


_CHIPQUEUEINFORMATIONRESPONSE = _descriptor.Descriptor(
  name='ChipQueueInformationResponse',
  full_name='cv.runtime.proto.ChipQueueInformationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='queue_score', full_name='cv.runtime.proto.ChipQueueInformationResponse.queue_score', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9296,
  serialized_end=9385,
)


_P2PIMAGEANDMETADATARESPONSE = _descriptor.Descriptor(
  name='P2PImageAndMetadataResponse',
  full_name='cv.runtime.proto.P2PImageAndMetadataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_bytes', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.target_bytes', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_width', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.target_width', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_height', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.target_height', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='perspective_bytes', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.perspective_bytes', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='perspective_width', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.perspective_width', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='perspective_height', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.perspective_height', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='annotated_target_bytes', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.annotated_target_bytes', index=6,
      number=7, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='annotated_target_width', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.annotated_target_width', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='annotated_target_height', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.annotated_target_height', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.timestamp_ms', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.score', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.cam_id', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='iso_formatted_time', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.iso_formatted_time', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lla_lat', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.lla_lat', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lla_lng', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.lla_lng', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lla_alt', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.lla_alt', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lla_timestamp_ms', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.lla_timestamp_ms', index=16,
      number=17, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_x', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.ecef_x', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_y', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.ecef_y', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_z', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.ecef_z', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_timestamp_ms', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.ecef_timestamp_ms', index=20,
      number=21, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ppi', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.ppi', index=21,
      number=22, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='perspective_ppi', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.perspective_ppi', index=22,
      number=23, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='image_type', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.image_type', index=23,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_url', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.model_url', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.crop', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focus_metric', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.focus_metric', index=26,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.exposure_us', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeding_enabled', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.weeding_enabled', index=28,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_enabled', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.thinning_enabled', index=29,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed_id', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.deepweed_id', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p_id', full_name='cv.runtime.proto.P2PImageAndMetadataResponse.p2p_id', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9388,
  serialized_end=10118,
)


_GETCAMERAINFOREQUEST = _descriptor.Descriptor(
  name='GetCameraInfoRequest',
  full_name='cv.runtime.proto.GetCameraInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10120,
  serialized_end=10142,
)


_CAMERAINFO = _descriptor.Descriptor(
  name='CameraInfo',
  full_name='cv.runtime.proto.CameraInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.CameraInfo.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ip_address', full_name='cv.runtime.proto.CameraInfo.ip_address', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='serial_number', full_name='cv.runtime.proto.CameraInfo.serial_number', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model', full_name='cv.runtime.proto.CameraInfo.model', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width', full_name='cv.runtime.proto.CameraInfo.width', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='cv.runtime.proto.CameraInfo.height', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connected', full_name='cv.runtime.proto.CameraInfo.connected', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='link_speed', full_name='cv.runtime.proto.CameraInfo.link_speed', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_type', full_name='cv.runtime.proto.CameraInfo.error_type', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='v4l2_device_id', full_name='cv.runtime.proto.CameraInfo.v4l2_device_id', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firmware_version', full_name='cv.runtime.proto.CameraInfo.firmware_version', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latest_firmware_version', full_name='cv.runtime.proto.CameraInfo.latest_firmware_version', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_ip_address', full_name='cv.runtime.proto.CameraInfo._ip_address',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_serial_number', full_name='cv.runtime.proto.CameraInfo._serial_number',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_v4l2_device_id', full_name='cv.runtime.proto.CameraInfo._v4l2_device_id',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_latest_firmware_version', full_name='cv.runtime.proto.CameraInfo._latest_firmware_version',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=10145,
  serialized_end=10533,
)


_GETCAMERAINFORESPONSE = _descriptor.Descriptor(
  name='GetCameraInfoResponse',
  full_name='cv.runtime.proto.GetCameraInfoResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='camera_info', full_name='cv.runtime.proto.GetCameraInfoResponse.camera_info', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10535,
  serialized_end=10609,
)


_GETLIGHTWEIGHTBURSTRECORDREQUEST = _descriptor.Descriptor(
  name='GetLightweightBurstRecordRequest',
  full_name='cv.runtime.proto.GetLightweightBurstRecordRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10611,
  serialized_end=10645,
)


_GETLIGHTWEIGHTBURSTRECORDRESPONSE = _descriptor.Descriptor(
  name='GetLightweightBurstRecordResponse',
  full_name='cv.runtime.proto.GetLightweightBurstRecordResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='zip_file', full_name='cv.runtime.proto.GetLightweightBurstRecordResponse.zip_file', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metadata_file', full_name='cv.runtime.proto.GetLightweightBurstRecordResponse.metadata_file', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10647,
  serialized_end=10723,
)


_GETBOOTEDREQUEST = _descriptor.Descriptor(
  name='GetBootedRequest',
  full_name='cv.runtime.proto.GetBootedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10725,
  serialized_end=10743,
)


_GETBOOTEDRESPONSE = _descriptor.Descriptor(
  name='GetBootedResponse',
  full_name='cv.runtime.proto.GetBootedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booted', full_name='cv.runtime.proto.GetBootedResponse.booted', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10745,
  serialized_end=10780,
)


_GETREADYREQUEST = _descriptor.Descriptor(
  name='GetReadyRequest',
  full_name='cv.runtime.proto.GetReadyRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10782,
  serialized_end=10799,
)


_GETREADYRESPONSE_DEEPWEEDREADYSTATEENTRY = _descriptor.Descriptor(
  name='DeepweedReadyStateEntry',
  full_name='cv.runtime.proto.GetReadyResponse.DeepweedReadyStateEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='cv.runtime.proto.GetReadyResponse.DeepweedReadyStateEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='cv.runtime.proto.GetReadyResponse.DeepweedReadyStateEntry.value', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11023,
  serialized_end=11080,
)

_GETREADYRESPONSE_P2PREADYSTATEENTRY = _descriptor.Descriptor(
  name='P2pReadyStateEntry',
  full_name='cv.runtime.proto.GetReadyResponse.P2pReadyStateEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='cv.runtime.proto.GetReadyResponse.P2pReadyStateEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='cv.runtime.proto.GetReadyResponse.P2pReadyStateEntry.value', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11082,
  serialized_end=11134,
)

_GETREADYRESPONSE = _descriptor.Descriptor(
  name='GetReadyResponse',
  full_name='cv.runtime.proto.GetReadyResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ready', full_name='cv.runtime.proto.GetReadyResponse.ready', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed_ready_state', full_name='cv.runtime.proto.GetReadyResponse.deepweed_ready_state', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p_ready_state', full_name='cv.runtime.proto.GetReadyResponse.p2p_ready_state', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booted', full_name='cv.runtime.proto.GetReadyResponse.booted', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETREADYRESPONSE_DEEPWEEDREADYSTATEENTRY, _GETREADYRESPONSE_P2PREADYSTATEENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10802,
  serialized_end=11134,
)


_GETERRORSTATERESPONSE = _descriptor.Descriptor(
  name='GetErrorStateResponse',
  full_name='cv.runtime.proto.GetErrorStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='plant_profile_error', full_name='cv.runtime.proto.GetErrorStateResponse.plant_profile_error', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_unsupported_embeddings', full_name='cv.runtime.proto.GetErrorStateResponse.model_unsupported_embeddings', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11136,
  serialized_end=11226,
)


_DEEPWEEDDETECTION = _descriptor.Descriptor(
  name='DeepweedDetection',
  full_name='cv.runtime.proto.DeepweedDetection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='cv.runtime.proto.DeepweedDetection.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='cv.runtime.proto.DeepweedDetection.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size', full_name='cv.runtime.proto.DeepweedDetection.size', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='cv.runtime.proto.DeepweedDetection.score', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hit_class', full_name='cv.runtime.proto.DeepweedDetection.hit_class', index=4,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mask_intersections', full_name='cv.runtime.proto.DeepweedDetection.mask_intersections', index=5,
      number=7, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_score', full_name='cv.runtime.proto.DeepweedDetection.weed_score', index=6,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_score', full_name='cv.runtime.proto.DeepweedDetection.crop_score', index=7,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_detection_class_scores', full_name='cv.runtime.proto.DeepweedDetection.weed_detection_class_scores', index=8,
      number=10, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding', full_name='cv.runtime.proto.DeepweedDetection.embedding', index=9,
      number=11, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_score', full_name='cv.runtime.proto.DeepweedDetection.plant_score', index=10,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding_category_distances', full_name='cv.runtime.proto.DeepweedDetection.embedding_category_distances', index=11,
      number=13, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='detection_id', full_name='cv.runtime.proto.DeepweedDetection.detection_id', index=12,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11229,
  serialized_end=11551,
)


_DEEPWEEDOUTPUT = _descriptor.Descriptor(
  name='DeepweedOutput',
  full_name='cv.runtime.proto.DeepweedOutput',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='detections', full_name='cv.runtime.proto.DeepweedOutput.detections', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mask_width', full_name='cv.runtime.proto.DeepweedOutput.mask_width', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mask_height', full_name='cv.runtime.proto.DeepweedOutput.mask_height', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mask_channels', full_name='cv.runtime.proto.DeepweedOutput.mask_channels', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mask', full_name='cv.runtime.proto.DeepweedOutput.mask', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mask_channel_classes', full_name='cv.runtime.proto.DeepweedOutput.mask_channel_classes', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_in_distance_buffer', full_name='cv.runtime.proto.DeepweedOutput.predict_in_distance_buffer', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.DeepweedOutput.timestamp_ms', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_detection_classes', full_name='cv.runtime.proto.DeepweedOutput.weed_detection_classes', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding_categories', full_name='cv.runtime.proto.DeepweedOutput.embedding_categories', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='available_for_snapshotting', full_name='cv.runtime.proto.DeepweedOutput.available_for_snapshotting', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11554,
  serialized_end=11891,
)


_GETDEEPWEEDOUTPUTBYTIMESTAMPREQUEST = _descriptor.Descriptor(
  name='GetDeepweedOutputByTimestampRequest',
  full_name='cv.runtime.proto.GetDeepweedOutputByTimestampRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetDeepweedOutputByTimestampRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GetDeepweedOutputByTimestampRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11893,
  serialized_end=11968,
)


_GETRECOMMENDEDSTROBESETTINGSREQUEST = _descriptor.Descriptor(
  name='GetRecommendedStrobeSettingsRequest',
  full_name='cv.runtime.proto.GetRecommendedStrobeSettingsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11970,
  serialized_end=12007,
)


_GETRECOMMENDEDSTROBESETTINGSRESPONSE = _descriptor.Descriptor(
  name='GetRecommendedStrobeSettingsResponse',
  full_name='cv.runtime.proto.GetRecommendedStrobeSettingsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_camera_fps', full_name='cv.runtime.proto.GetRecommendedStrobeSettingsResponse.target_camera_fps', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='targets_per_predict_ratio', full_name='cv.runtime.proto.GetRecommendedStrobeSettingsResponse.targets_per_predict_ratio', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12009,
  serialized_end=12109,
)


_STARTP2PBUFFERRINGREQUEST = _descriptor.Descriptor(
  name='StartP2PBufferringRequest',
  full_name='cv.runtime.proto.StartP2PBufferringRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.StartP2PBufferringRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12111,
  serialized_end=12158,
)


_STARTP2PBUFFERRINGRESPONSE = _descriptor.Descriptor(
  name='StartP2PBufferringResponse',
  full_name='cv.runtime.proto.StartP2PBufferringResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12160,
  serialized_end=12192,
)


_STOPP2PBUFFERRINGREQUEST = _descriptor.Descriptor(
  name='StopP2PBufferringRequest',
  full_name='cv.runtime.proto.StopP2PBufferringRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='save_burst', full_name='cv.runtime.proto.StopP2PBufferringRequest.save_burst', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.StopP2PBufferringRequest.cam_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='path', full_name='cv.runtime.proto.StopP2PBufferringRequest.path', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dont_capture_predict_image', full_name='cv.runtime.proto.StopP2PBufferringRequest.dont_capture_predict_image', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_timestamp_ms', full_name='cv.runtime.proto.StopP2PBufferringRequest.start_timestamp_ms', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_timestamp_ms', full_name='cv.runtime.proto.StopP2PBufferringRequest.end_timestamp_ms', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12195,
  serialized_end=12365,
)


_STOPP2PBUFFERRINGRESPONSE = _descriptor.Descriptor(
  name='StopP2PBufferringResponse',
  full_name='cv.runtime.proto.StopP2PBufferringResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12367,
  serialized_end=12398,
)


_P2PCAPTUREREQUEST = _descriptor.Descriptor(
  name='P2PCaptureRequest',
  full_name='cv.runtime.proto.P2PCaptureRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.P2PCaptureRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='cv.runtime.proto.P2PCaptureRequest.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.P2PCaptureRequest.timestamp_ms', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='write_to_disk', full_name='cv.runtime.proto.P2PCaptureRequest.write_to_disk', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reason', full_name='cv.runtime.proto.P2PCaptureRequest.reason', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12401,
  serialized_end=12547,
)


_P2PCAPTURERESPONSE = _descriptor.Descriptor(
  name='P2PCaptureResponse',
  full_name='cv.runtime.proto.P2PCaptureResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12549,
  serialized_end=12569,
)


_P2PBUFFERINGBURSTPREDICTMETADATA = _descriptor.Descriptor(
  name='P2PBufferingBurstPredictMetadata',
  full_name='cv.runtime.proto.P2PBufferingBurstPredictMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='plant_size_px', full_name='cv.runtime.proto.P2PBufferingBurstPredictMetadata.plant_size_px', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12571,
  serialized_end=12628,
)


_P2PBUFFERRINGBURSTCAPTUREREQUEST = _descriptor.Descriptor(
  name='P2PBufferringBurstCaptureRequest',
  full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.cam_id', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='path', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.path', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dont_capture_predict_image', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.dont_capture_predict_image', index=2,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_timestamp_ms', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.start_timestamp_ms', index=3,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_timestamp_ms', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.end_timestamp_ms', index=4,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_path', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.predict_path', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_path_exists', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.predict_path_exists', index=6,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='save_predict_metadata', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.save_predict_metadata', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_metadata', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest.predict_metadata', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_predict_path', full_name='cv.runtime.proto.P2PBufferringBurstCaptureRequest._predict_path',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=12631,
  serialized_end=12967,
)


_P2PBUFFERRINGBURSTCAPTURERESPONSE = _descriptor.Descriptor(
  name='P2PBufferringBurstCaptureResponse',
  full_name='cv.runtime.proto.P2PBufferringBurstCaptureResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12969,
  serialized_end=13004,
)


_GETNEXTDEEPWEEDOUTPUTREQUEST = _descriptor.Descriptor(
  name='GetNextDeepweedOutputRequest',
  full_name='cv.runtime.proto.GetNextDeepweedOutputRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GetNextDeepweedOutputRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='cv.runtime.proto.GetNextDeepweedOutputRequest.timeout_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetNextDeepweedOutputRequest.cam_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13006,
  serialized_end=13094,
)


_SETTARGETINGSTATEREQUEST = _descriptor.Descriptor(
  name='SetTargetingStateRequest',
  full_name='cv.runtime.proto.SetTargetingStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weeding_enabled', full_name='cv.runtime.proto.SetTargetingStateRequest.weeding_enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_enabled', full_name='cv.runtime.proto.SetTargetingStateRequest.thinning_enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13096,
  serialized_end=13173,
)


_SETTARGETINGSTATERESPONSE = _descriptor.Descriptor(
  name='SetTargetingStateResponse',
  full_name='cv.runtime.proto.SetTargetingStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13175,
  serialized_end=13202,
)


_GETNEXTFOCUSMETRICREQUEST = _descriptor.Descriptor(
  name='GetNextFocusMetricRequest',
  full_name='cv.runtime.proto.GetNextFocusMetricRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.GetNextFocusMetricRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GetNextFocusMetricRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13204,
  serialized_end=13269,
)


_GETNEXTFOCUSMETRICRESPONSE = _descriptor.Descriptor(
  name='GetNextFocusMetricResponse',
  full_name='cv.runtime.proto.GetNextFocusMetricResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='focus_metric', full_name='cv.runtime.proto.GetNextFocusMetricResponse.focus_metric', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GetNextFocusMetricResponse.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13271,
  serialized_end=13343,
)


_REMOVEDATADIRREQUEST = _descriptor.Descriptor(
  name='RemoveDataDirRequest',
  full_name='cv.runtime.proto.RemoveDataDirRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='path', full_name='cv.runtime.proto.RemoveDataDirRequest.path', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13345,
  serialized_end=13381,
)


_REMOVEDATADIRRESPONSE = _descriptor.Descriptor(
  name='RemoveDataDirResponse',
  full_name='cv.runtime.proto.RemoveDataDirResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13383,
  serialized_end=13406,
)


_LASTNIMAGEREQUEST = _descriptor.Descriptor(
  name='LastNImageRequest',
  full_name='cv.runtime.proto.LastNImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='cv.runtime.proto.LastNImageRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_images', full_name='cv.runtime.proto.LastNImageRequest.num_images', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13408,
  serialized_end=13463,
)


_COMPUTECAPABILITIESRESPONSE = _descriptor.Descriptor(
  name='ComputeCapabilitiesResponse',
  full_name='cv.runtime.proto.ComputeCapabilitiesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='capabilities', full_name='cv.runtime.proto.ComputeCapabilitiesResponse.capabilities', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13465,
  serialized_end=13516,
)


_SUPPORTEDTENSORRTVERSIONSRESPONSE = _descriptor.Descriptor(
  name='SupportedTensorRTVersionsResponse',
  full_name='cv.runtime.proto.SupportedTensorRTVersionsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='versions', full_name='cv.runtime.proto.SupportedTensorRTVersionsResponse.versions', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13518,
  serialized_end=13571,
)


_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='cv.runtime.proto.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13573,
  serialized_end=13580,
)


_SCOREQUEUEANDCOUNT = _descriptor.Descriptor(
  name='ScoreQueueAndCount',
  full_name='cv.runtime.proto.ScoreQueueAndCount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_queue', full_name='cv.runtime.proto.ScoreQueueAndCount.score_queue', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_items', full_name='cv.runtime.proto.ScoreQueueAndCount.num_items', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13582,
  serialized_end=13642,
)


_LISTSCOREQUEUESRESPONSE = _descriptor.Descriptor(
  name='ListScoreQueuesResponse',
  full_name='cv.runtime.proto.ListScoreQueuesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='score_queue', full_name='cv.runtime.proto.ListScoreQueuesResponse.score_queue', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13644,
  serialized_end=13728,
)


_GETCATEGORYCOLLECTIONRESPONSE = _descriptor.Descriptor(
  name='GetCategoryCollectionResponse',
  full_name='cv.runtime.proto.GetCategoryCollectionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_collection_id', full_name='cv.runtime.proto.GetCategoryCollectionResponse.category_collection_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_updated_timestamp_ms', full_name='cv.runtime.proto.GetCategoryCollectionResponse.last_updated_timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13730,
  serialized_end=13828,
)


_SNAPSHOTPREDICTIMAGESREQUEST = _descriptor.Descriptor(
  name='SnapshotPredictImagesRequest',
  full_name='cv.runtime.proto.SnapshotPredictImagesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13830,
  serialized_end=13860,
)


_PCAMSNAPSHOT = _descriptor.Descriptor(
  name='PcamSnapshot',
  full_name='cv.runtime.proto.PcamSnapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pcam_id', full_name='cv.runtime.proto.PcamSnapshot.pcam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.PcamSnapshot.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13862,
  serialized_end=13915,
)


_SNAPSHOTPREDICTIMAGESRESPONSE = _descriptor.Descriptor(
  name='SnapshotPredictImagesResponse',
  full_name='cv.runtime.proto.SnapshotPredictImagesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='snapshots', full_name='cv.runtime.proto.SnapshotPredictImagesResponse.snapshots', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13917,
  serialized_end=13999,
)


_GETCHIPFORPREDICTIMAGEREQUEST = _descriptor.Descriptor(
  name='GetChipForPredictImageRequest',
  full_name='cv.runtime.proto.GetChipForPredictImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pcam_id', full_name='cv.runtime.proto.GetChipForPredictImageRequest.pcam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.runtime.proto.GetChipForPredictImageRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_x_px', full_name='cv.runtime.proto.GetChipForPredictImageRequest.center_x_px', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_y_px', full_name='cv.runtime.proto.GetChipForPredictImageRequest.center_y_px', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14001,
  serialized_end=14113,
)


_GETCHIPFORPREDICTIMAGERESPONSE = _descriptor.Descriptor(
  name='GetChipForPredictImageResponse',
  full_name='cv.runtime.proto.GetChipForPredictImageResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='image_and_metadata', full_name='cv.runtime.proto.GetChipForPredictImageResponse.image_and_metadata', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14115,
  serialized_end=14219,
)

_SETP2PCONTEXTREQUEST.fields_by_name['primary_context'].message_type = _P2PCONTEXT
_SETP2PCONTEXTREQUEST.fields_by_name['secondary_context'].message_type = _P2PCONTEXT
_SETP2PCONTEXTREQUEST.fields_by_name['safety_zone'].message_type = _TARGETSAFETYZONE
_SETP2PCONTEXTREQUEST.oneofs_by_name['_secondary_context'].fields.append(
  _SETP2PCONTEXTREQUEST.fields_by_name['secondary_context'])
_SETP2PCONTEXTREQUEST.fields_by_name['secondary_context'].containing_oneof = _SETP2PCONTEXTREQUEST.oneofs_by_name['_secondary_context']
_DEEPWEEDDETECTIONCRITERIASETTING.fields_by_name['point_categories'].message_type = _POINTDETECTIONCATEGORY
_DEEPWEEDDETECTIONCRITERIASETTING.fields_by_name['segmentation_categories'].message_type = _SEGMENTATIONDETECTIONCATEGORY
_SETDEEPWEEDDETECTIONCRITERIAREQUEST.fields_by_name['point_categories'].message_type = _POINTDETECTIONCATEGORY
_SETDEEPWEEDDETECTIONCRITERIAREQUEST.fields_by_name['segmentation_categories'].message_type = _SEGMENTATIONDETECTIONCATEGORY
_GETDEEPWEEDDETECTIONCRITERIARESPONSE.fields_by_name['point_categories'].message_type = _POINTDETECTIONCATEGORY
_GETDEEPWEEDDETECTIONCRITERIARESPONSE.fields_by_name['segmentation_categories'].message_type = _SEGMENTATIONDETECTIONCATEGORY
_GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST.oneofs_by_name['_cam_id'].fields.append(
  _GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST.fields_by_name['cam_id'])
_GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST.fields_by_name['cam_id'].containing_oneof = _GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST.oneofs_by_name['_cam_id']
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY.containing_type = _GETDEEPWEEDINDEXTOCATEGORYRESPONSE
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY.containing_type = _GETDEEPWEEDINDEXTOCATEGORYRESPONSE
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY.containing_type = _GETDEEPWEEDINDEXTOCATEGORYRESPONSE
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE.fields_by_name['weed_point_index_to_category'].message_type = _GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE.fields_by_name['crop_point_index_to_category'].message_type = _GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE.fields_by_name['intersection_index_to_category'].message_type = _GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY
_SETCAMERASETTINGSREQUEST.fields_by_name['light_source_preset'].enum_type = lib_dot_common_dot_camera_dot_proto_dot_camera__pb2._LIGHTSOURCEPRESET
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_exposure_us'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['exposure_us'])
_SETCAMERASETTINGSREQUEST.fields_by_name['exposure_us'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_exposure_us']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_gamma'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['gamma'])
_SETCAMERASETTINGSREQUEST.fields_by_name['gamma'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_gamma']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_gain_db'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['gain_db'])
_SETCAMERASETTINGSREQUEST.fields_by_name['gain_db'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_gain_db']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_light_source_preset'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['light_source_preset'])
_SETCAMERASETTINGSREQUEST.fields_by_name['light_source_preset'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_light_source_preset']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_wb_ratio_red'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['wb_ratio_red'])
_SETCAMERASETTINGSREQUEST.fields_by_name['wb_ratio_red'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_wb_ratio_red']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_wb_ratio_green'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['wb_ratio_green'])
_SETCAMERASETTINGSREQUEST.fields_by_name['wb_ratio_green'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_wb_ratio_green']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_wb_ratio_blue'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['wb_ratio_blue'])
_SETCAMERASETTINGSREQUEST.fields_by_name['wb_ratio_blue'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_wb_ratio_blue']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_roi_offset_x'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['roi_offset_x'])
_SETCAMERASETTINGSREQUEST.fields_by_name['roi_offset_x'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_roi_offset_x']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_roi_offset_y'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['roi_offset_y'])
_SETCAMERASETTINGSREQUEST.fields_by_name['roi_offset_y'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_roi_offset_y']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_mirror'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['mirror'])
_SETCAMERASETTINGSREQUEST.fields_by_name['mirror'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_mirror']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_flip'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['flip'])
_SETCAMERASETTINGSREQUEST.fields_by_name['flip'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_flip']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_strobing'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['strobing'])
_SETCAMERASETTINGSREQUEST.fields_by_name['strobing'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_strobing']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_ptp'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['ptp'])
_SETCAMERASETTINGSREQUEST.fields_by_name['ptp'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_ptp']
_SETCAMERASETTINGSREQUEST.oneofs_by_name['_auto_whitebalance'].fields.append(
  _SETCAMERASETTINGSREQUEST.fields_by_name['auto_whitebalance'])
_SETCAMERASETTINGSREQUEST.fields_by_name['auto_whitebalance'].containing_oneof = _SETCAMERASETTINGSREQUEST.oneofs_by_name['_auto_whitebalance']
_CAMERASETTINGSRESPONSE.fields_by_name['light_source_preset'].enum_type = lib_dot_common_dot_camera_dot_proto_dot_camera__pb2._LIGHTSOURCEPRESET
_CAMERASETTINGSRESPONSE.oneofs_by_name['_exposure_us'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['exposure_us'])
_CAMERASETTINGSRESPONSE.fields_by_name['exposure_us'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_exposure_us']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_gamma'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['gamma'])
_CAMERASETTINGSRESPONSE.fields_by_name['gamma'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_gamma']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_gain_db'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['gain_db'])
_CAMERASETTINGSRESPONSE.fields_by_name['gain_db'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_gain_db']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_light_source_preset'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['light_source_preset'])
_CAMERASETTINGSRESPONSE.fields_by_name['light_source_preset'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_light_source_preset']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_wb_ratio_red'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['wb_ratio_red'])
_CAMERASETTINGSRESPONSE.fields_by_name['wb_ratio_red'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_wb_ratio_red']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_wb_ratio_green'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['wb_ratio_green'])
_CAMERASETTINGSRESPONSE.fields_by_name['wb_ratio_green'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_wb_ratio_green']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_wb_ratio_blue'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['wb_ratio_blue'])
_CAMERASETTINGSRESPONSE.fields_by_name['wb_ratio_blue'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_wb_ratio_blue']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_roi_width'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['roi_width'])
_CAMERASETTINGSRESPONSE.fields_by_name['roi_width'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_roi_width']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_roi_height'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['roi_height'])
_CAMERASETTINGSRESPONSE.fields_by_name['roi_height'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_roi_height']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_roi_offset_x'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['roi_offset_x'])
_CAMERASETTINGSRESPONSE.fields_by_name['roi_offset_x'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_roi_offset_x']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_roi_offset_y'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['roi_offset_y'])
_CAMERASETTINGSRESPONSE.fields_by_name['roi_offset_y'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_roi_offset_y']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_gpu_id'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['gpu_id'])
_CAMERASETTINGSRESPONSE.fields_by_name['gpu_id'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_gpu_id']
_CAMERASETTINGSRESPONSE.oneofs_by_name['_auto_whitebalance'].fields.append(
  _CAMERASETTINGSRESPONSE.fields_by_name['auto_whitebalance'])
_CAMERASETTINGSRESPONSE.fields_by_name['auto_whitebalance'].containing_oneof = _CAMERASETTINGSRESPONSE.oneofs_by_name['_auto_whitebalance']
_GETCAMERASETTINGSRESPONSE.fields_by_name['camera_settings_response'].message_type = _CAMERASETTINGSRESPONSE
_STOPBURSTRECORDFRAMESREQUEST.oneofs_by_name['_last_frame_timestamp_ms'].fields.append(
  _STOPBURSTRECORDFRAMESREQUEST.fields_by_name['last_frame_timestamp_ms'])
_STOPBURSTRECORDFRAMESREQUEST.fields_by_name['last_frame_timestamp_ms'].containing_oneof = _STOPBURSTRECORDFRAMESREQUEST.oneofs_by_name['_last_frame_timestamp_ms']
_GETCONNECTORSRESPONSE.fields_by_name['connector_response'].message_type = _CONNECTORRESPONSE
_SETCONNECTORSREQUEST.oneofs_by_name['_is_enabled'].fields.append(
  _SETCONNECTORSREQUEST.fields_by_name['is_enabled'])
_SETCONNECTORSREQUEST.fields_by_name['is_enabled'].containing_oneof = _SETCONNECTORSREQUEST.oneofs_by_name['_is_enabled']
_SETCONNECTORSREQUEST.oneofs_by_name['_reduction_ratio'].fields.append(
  _SETCONNECTORSREQUEST.fields_by_name['reduction_ratio'])
_SETCONNECTORSREQUEST.fields_by_name['reduction_ratio'].containing_oneof = _SETCONNECTORSREQUEST.oneofs_by_name['_reduction_ratio']
_NODETIMING_STATETIMINGSENTRY.containing_type = _NODETIMING
_NODETIMING.fields_by_name['state_timings'].message_type = _NODETIMING_STATETIMINGSENTRY
_GETTIMINGRESPONSE.fields_by_name['node_timing'].message_type = _NODETIMING
_GETMODELPATHSRESPONSE.oneofs_by_name['_p2p'].fields.append(
  _GETMODELPATHSRESPONSE.fields_by_name['p2p'])
_GETMODELPATHSRESPONSE.fields_by_name['p2p'].containing_oneof = _GETMODELPATHSRESPONSE.oneofs_by_name['_p2p']
_GETMODELPATHSRESPONSE.oneofs_by_name['_deepweed'].fields.append(
  _GETMODELPATHSRESPONSE.fields_by_name['deepweed'])
_GETMODELPATHSRESPONSE.fields_by_name['deepweed'].containing_oneof = _GETMODELPATHSRESPONSE.oneofs_by_name['_deepweed']
_GETMODELPATHSRESPONSE.oneofs_by_name['_furrows'].fields.append(
  _GETMODELPATHSRESPONSE.fields_by_name['furrows'])
_GETMODELPATHSRESPONSE.fields_by_name['furrows'].containing_oneof = _GETMODELPATHSRESPONSE.oneofs_by_name['_furrows']
_GETCAMERATEMPERATURESRESPONSE.fields_by_name['temperature'].message_type = _CAMERATEMPERATURE
_GEOLLA.oneofs_by_name['_lat'].fields.append(
  _GEOLLA.fields_by_name['lat'])
_GEOLLA.fields_by_name['lat'].containing_oneof = _GEOLLA.oneofs_by_name['_lat']
_GEOLLA.oneofs_by_name['_lng'].fields.append(
  _GEOLLA.fields_by_name['lng'])
_GEOLLA.fields_by_name['lng'].containing_oneof = _GEOLLA.oneofs_by_name['_lng']
_GEOLLA.oneofs_by_name['_alt'].fields.append(
  _GEOLLA.fields_by_name['alt'])
_GEOLLA.fields_by_name['alt'].containing_oneof = _GEOLLA.oneofs_by_name['_alt']
_GEOLLA.oneofs_by_name['_timestamp_ms'].fields.append(
  _GEOLLA.fields_by_name['timestamp_ms'])
_GEOLLA.fields_by_name['timestamp_ms'].containing_oneof = _GEOLLA.oneofs_by_name['_timestamp_ms']
_GEOECEF.oneofs_by_name['_x'].fields.append(
  _GEOECEF.fields_by_name['x'])
_GEOECEF.fields_by_name['x'].containing_oneof = _GEOECEF.oneofs_by_name['_x']
_GEOECEF.oneofs_by_name['_y'].fields.append(
  _GEOECEF.fields_by_name['y'])
_GEOECEF.fields_by_name['y'].containing_oneof = _GEOECEF.oneofs_by_name['_y']
_GEOECEF.oneofs_by_name['_z'].fields.append(
  _GEOECEF.fields_by_name['z'])
_GEOECEF.fields_by_name['z'].containing_oneof = _GEOECEF.oneofs_by_name['_z']
_GEOECEF.oneofs_by_name['_timestamp_ms'].fields.append(
  _GEOECEF.fields_by_name['timestamp_ms'])
_GEOECEF.fields_by_name['timestamp_ms'].containing_oneof = _GEOECEF.oneofs_by_name['_timestamp_ms']
_SETGPSLOCATIONREQUEST.fields_by_name['lla'].message_type = _GEOLLA
_SETGPSLOCATIONREQUEST.fields_by_name['ecef'].message_type = _GEOECEF
_SETIMAGESCOREREQUEST.fields_by_name['deepweed_output'].message_type = _DEEPWEEDOUTPUT
_GETSCOREQUEUERESPONSE.fields_by_name['score_object'].message_type = _SCOREOBJECT
_GETLATESTP2PIMAGEREQUEST.fields_by_name['reason'].enum_type = proto_dot_cv_dot_cv__pb2._P2PCAPTUREREASON
_FLUSHQUEUESREQUEST.fields_by_name['score_queue_type'].enum_type = _SCOREQUEUETYPE
_FLUSHQUEUESREQUEST.oneofs_by_name['_score_queue_type'].fields.append(
  _FLUSHQUEUESREQUEST.fields_by_name['score_queue_type'])
_FLUSHQUEUESREQUEST.fields_by_name['score_queue_type'].containing_oneof = _FLUSHQUEUESREQUEST.oneofs_by_name['_score_queue_type']
_IMAGEANDMETADATARESPONSE.fields_by_name['deepweed_detections'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._DETECTION
_IMAGEANDMETADATARESPONSE.oneofs_by_name['_score_type'].fields.append(
  _IMAGEANDMETADATARESPONSE.fields_by_name['score_type'])
_IMAGEANDMETADATARESPONSE.fields_by_name['score_type'].containing_oneof = _IMAGEANDMETADATARESPONSE.oneofs_by_name['_score_type']
_CHIPPREDICTION.fields_by_name['score'].message_type = _CATEGORYPREDICTION
_CHIPPREDICTION.fields_by_name['embedding_distance'].message_type = _CATEGORYPREDICTION
_CHIPIMAGEANDMETADATARESPONSE.fields_by_name['image_and_metadata'].message_type = _IMAGEANDMETADATARESPONSE
_CHIPIMAGEANDMETADATARESPONSE.fields_by_name['prediction_metadata'].message_type = _CHIPPREDICTION
_CHIPQUEUEINFORMATIONRESPONSE.fields_by_name['queue_score'].message_type = _CATEGORYPREDICTION
_CAMERAINFO.fields_by_name['error_type'].enum_type = _ERRORTYPE
_CAMERAINFO.oneofs_by_name['_ip_address'].fields.append(
  _CAMERAINFO.fields_by_name['ip_address'])
_CAMERAINFO.fields_by_name['ip_address'].containing_oneof = _CAMERAINFO.oneofs_by_name['_ip_address']
_CAMERAINFO.oneofs_by_name['_serial_number'].fields.append(
  _CAMERAINFO.fields_by_name['serial_number'])
_CAMERAINFO.fields_by_name['serial_number'].containing_oneof = _CAMERAINFO.oneofs_by_name['_serial_number']
_CAMERAINFO.oneofs_by_name['_v4l2_device_id'].fields.append(
  _CAMERAINFO.fields_by_name['v4l2_device_id'])
_CAMERAINFO.fields_by_name['v4l2_device_id'].containing_oneof = _CAMERAINFO.oneofs_by_name['_v4l2_device_id']
_CAMERAINFO.oneofs_by_name['_latest_firmware_version'].fields.append(
  _CAMERAINFO.fields_by_name['latest_firmware_version'])
_CAMERAINFO.fields_by_name['latest_firmware_version'].containing_oneof = _CAMERAINFO.oneofs_by_name['_latest_firmware_version']
_GETCAMERAINFORESPONSE.fields_by_name['camera_info'].message_type = _CAMERAINFO
_GETREADYRESPONSE_DEEPWEEDREADYSTATEENTRY.containing_type = _GETREADYRESPONSE
_GETREADYRESPONSE_P2PREADYSTATEENTRY.containing_type = _GETREADYRESPONSE
_GETREADYRESPONSE.fields_by_name['deepweed_ready_state'].message_type = _GETREADYRESPONSE_DEEPWEEDREADYSTATEENTRY
_GETREADYRESPONSE.fields_by_name['p2p_ready_state'].message_type = _GETREADYRESPONSE_P2PREADYSTATEENTRY
_DEEPWEEDDETECTION.fields_by_name['hit_class'].enum_type = _HITCLASS
_DEEPWEEDOUTPUT.fields_by_name['detections'].message_type = _DEEPWEEDDETECTION
_P2PCAPTUREREQUEST.fields_by_name['reason'].enum_type = proto_dot_cv_dot_cv__pb2._P2PCAPTUREREASON
_P2PBUFFERRINGBURSTCAPTUREREQUEST.fields_by_name['predict_metadata'].message_type = _P2PBUFFERINGBURSTPREDICTMETADATA
_P2PBUFFERRINGBURSTCAPTUREREQUEST.oneofs_by_name['_predict_path'].fields.append(
  _P2PBUFFERRINGBURSTCAPTUREREQUEST.fields_by_name['predict_path'])
_P2PBUFFERRINGBURSTCAPTUREREQUEST.fields_by_name['predict_path'].containing_oneof = _P2PBUFFERRINGBURSTCAPTUREREQUEST.oneofs_by_name['_predict_path']
_LISTSCOREQUEUESRESPONSE.fields_by_name['score_queue'].message_type = _SCOREQUEUEANDCOUNT
_SNAPSHOTPREDICTIMAGESRESPONSE.fields_by_name['snapshots'].message_type = _PCAMSNAPSHOT
_GETCHIPFORPREDICTIMAGERESPONSE.fields_by_name['image_and_metadata'].message_type = _IMAGEANDMETADATARESPONSE
DESCRIPTOR.message_types_by_name['TargetSafetyZone'] = _TARGETSAFETYZONE
DESCRIPTOR.message_types_by_name['P2PContext'] = _P2PCONTEXT
DESCRIPTOR.message_types_by_name['SetP2PContextRequest'] = _SETP2PCONTEXTREQUEST
DESCRIPTOR.message_types_by_name['SetP2PContextResponse'] = _SETP2PCONTEXTRESPONSE
DESCRIPTOR.message_types_by_name['GetCameraDimensionsRequest'] = _GETCAMERADIMENSIONSREQUEST
DESCRIPTOR.message_types_by_name['GetCameraDimensionsResponse'] = _GETCAMERADIMENSIONSRESPONSE
DESCRIPTOR.message_types_by_name['StartP2PDataCaptureRequest'] = _STARTP2PDATACAPTUREREQUEST
DESCRIPTOR.message_types_by_name['PointDetectionCategory'] = _POINTDETECTIONCATEGORY
DESCRIPTOR.message_types_by_name['SegmentationDetectionCategory'] = _SEGMENTATIONDETECTIONCATEGORY
DESCRIPTOR.message_types_by_name['DeepweedDetectionCriteriaSetting'] = _DEEPWEEDDETECTIONCRITERIASETTING
DESCRIPTOR.message_types_by_name['SetDeepweedDetectionCriteriaRequest'] = _SETDEEPWEEDDETECTIONCRITERIAREQUEST
DESCRIPTOR.message_types_by_name['SetDeepweedDetectionCriteriaResponse'] = _SETDEEPWEEDDETECTIONCRITERIARESPONSE
DESCRIPTOR.message_types_by_name['GetDeepweedDetectionCriteriaRequest'] = _GETDEEPWEEDDETECTIONCRITERIAREQUEST
DESCRIPTOR.message_types_by_name['GetDeepweedDetectionCriteriaResponse'] = _GETDEEPWEEDDETECTIONCRITERIARESPONSE
DESCRIPTOR.message_types_by_name['GetDeepweedSupportedCategoriesRequest'] = _GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST
DESCRIPTOR.message_types_by_name['GetDeepweedSupportedCategoriesResponse'] = _GETDEEPWEEDSUPPORTEDCATEGORIESRESPONSE
DESCRIPTOR.message_types_by_name['GetDeepweedIndexToCategoryRequest'] = _GETDEEPWEEDINDEXTOCATEGORYREQUEST
DESCRIPTOR.message_types_by_name['GetDeepweedIndexToCategoryResponse'] = _GETDEEPWEEDINDEXTOCATEGORYRESPONSE
DESCRIPTOR.message_types_by_name['GetPredictCamMatrixRequest'] = _GETPREDICTCAMMATRIXREQUEST
DESCRIPTOR.message_types_by_name['GetPredictCamDistortionCoefficientsRequest'] = _GETPREDICTCAMDISTORTIONCOEFFICIENTSREQUEST
DESCRIPTOR.message_types_by_name['SetCameraSettingsRequest'] = _SETCAMERASETTINGSREQUEST
DESCRIPTOR.message_types_by_name['SetCameraSettingsResponse'] = _SETCAMERASETTINGSRESPONSE
DESCRIPTOR.message_types_by_name['SetAutoWhitebalanceRequest'] = _SETAUTOWHITEBALANCEREQUEST
DESCRIPTOR.message_types_by_name['SetAutoWhitebalanceResponse'] = _SETAUTOWHITEBALANCERESPONSE
DESCRIPTOR.message_types_by_name['GetCameraSettingsRequest'] = _GETCAMERASETTINGSREQUEST
DESCRIPTOR.message_types_by_name['CameraSettingsResponse'] = _CAMERASETTINGSRESPONSE
DESCRIPTOR.message_types_by_name['GetCameraSettingsResponse'] = _GETCAMERASETTINGSRESPONSE
DESCRIPTOR.message_types_by_name['StartBurstRecordFramesRequest'] = _STARTBURSTRECORDFRAMESREQUEST
DESCRIPTOR.message_types_by_name['StartBurstRecordFramesResponse'] = _STARTBURSTRECORDFRAMESRESPONSE
DESCRIPTOR.message_types_by_name['StopBurstRecordFramesRequest'] = _STOPBURSTRECORDFRAMESREQUEST
DESCRIPTOR.message_types_by_name['StopBurstRecordFramesResponse'] = _STOPBURSTRECORDFRAMESRESPONSE
DESCRIPTOR.message_types_by_name['P2POutputProto'] = _P2POUTPUTPROTO
DESCRIPTOR.message_types_by_name['GetNextP2POutputRequest'] = _GETNEXTP2POUTPUTREQUEST
DESCRIPTOR.message_types_by_name['GetConnectorsRequest'] = _GETCONNECTORSREQUEST
DESCRIPTOR.message_types_by_name['ConnectorResponse'] = _CONNECTORRESPONSE
DESCRIPTOR.message_types_by_name['GetConnectorsResponse'] = _GETCONNECTORSRESPONSE
DESCRIPTOR.message_types_by_name['SetConnectorsRequest'] = _SETCONNECTORSREQUEST
DESCRIPTOR.message_types_by_name['SetConnectorsResponse'] = _SETCONNECTORSRESPONSE
DESCRIPTOR.message_types_by_name['NodeTiming'] = _NODETIMING
DESCRIPTOR.message_types_by_name['GetTimingRequest'] = _GETTIMINGREQUEST
DESCRIPTOR.message_types_by_name['GetTimingResponse'] = _GETTIMINGRESPONSE
DESCRIPTOR.message_types_by_name['PredictRequest'] = _PREDICTREQUEST
DESCRIPTOR.message_types_by_name['PredictResponse'] = _PREDICTRESPONSE
DESCRIPTOR.message_types_by_name['LoadAndQueueRequest'] = _LOADANDQUEUEREQUEST
DESCRIPTOR.message_types_by_name['LoadAndQueueResponse'] = _LOADANDQUEUERESPONSE
DESCRIPTOR.message_types_by_name['SetImageRequest'] = _SETIMAGEREQUEST
DESCRIPTOR.message_types_by_name['SetImageResponse'] = _SETIMAGERESPONSE
DESCRIPTOR.message_types_by_name['UnsetImageRequest'] = _UNSETIMAGEREQUEST
DESCRIPTOR.message_types_by_name['UnsetImageResponse'] = _UNSETIMAGERESPONSE
DESCRIPTOR.message_types_by_name['GetModelPathsRequest'] = _GETMODELPATHSREQUEST
DESCRIPTOR.message_types_by_name['GetModelPathsResponse'] = _GETMODELPATHSRESPONSE
DESCRIPTOR.message_types_by_name['GetCameraTemperaturesRequest'] = _GETCAMERATEMPERATURESREQUEST
DESCRIPTOR.message_types_by_name['CameraTemperature'] = _CAMERATEMPERATURE
DESCRIPTOR.message_types_by_name['GetCameraTemperaturesResponse'] = _GETCAMERATEMPERATURESRESPONSE
DESCRIPTOR.message_types_by_name['GeoLLA'] = _GEOLLA
DESCRIPTOR.message_types_by_name['GeoECEF'] = _GEOECEF
DESCRIPTOR.message_types_by_name['SetGPSLocationRequest'] = _SETGPSLOCATIONREQUEST
DESCRIPTOR.message_types_by_name['SetGPSLocationResponse'] = _SETGPSLOCATIONRESPONSE
DESCRIPTOR.message_types_by_name['SetImplementStatusRequest'] = _SETIMPLEMENTSTATUSREQUEST
DESCRIPTOR.message_types_by_name['SetImplementStatusResponse'] = _SETIMPLEMENTSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['SetImageScoreRequest'] = _SETIMAGESCOREREQUEST
DESCRIPTOR.message_types_by_name['SetImageScoreResponse'] = _SETIMAGESCORERESPONSE
DESCRIPTOR.message_types_by_name['GetScoreQueueRequest'] = _GETSCOREQUEUEREQUEST
DESCRIPTOR.message_types_by_name['ScoreObject'] = _SCOREOBJECT
DESCRIPTOR.message_types_by_name['GetScoreQueueResponse'] = _GETSCOREQUEUERESPONSE
DESCRIPTOR.message_types_by_name['GetMaxImageScoreRequest'] = _GETMAXIMAGESCOREREQUEST
DESCRIPTOR.message_types_by_name['GetMaxImageScoreResponse'] = _GETMAXIMAGESCORERESPONSE
DESCRIPTOR.message_types_by_name['GetMaxScoredImageRequest'] = _GETMAXSCOREDIMAGEREQUEST
DESCRIPTOR.message_types_by_name['GetLatestP2PImageRequest'] = _GETLATESTP2PIMAGEREQUEST
DESCRIPTOR.message_types_by_name['GetLatestImageRequest'] = _GETLATESTIMAGEREQUEST
DESCRIPTOR.message_types_by_name['GetImageNearTimestampRequest'] = _GETIMAGENEARTIMESTAMPREQUEST
DESCRIPTOR.message_types_by_name['GetChipImageRequest'] = _GETCHIPIMAGEREQUEST
DESCRIPTOR.message_types_by_name['FlushQueuesRequest'] = _FLUSHQUEUESREQUEST
DESCRIPTOR.message_types_by_name['FlushQueuesResponse'] = _FLUSHQUEUESRESPONSE
DESCRIPTOR.message_types_by_name['ImageAndMetadataResponse'] = _IMAGEANDMETADATARESPONSE
DESCRIPTOR.message_types_by_name['CategoryPrediction'] = _CATEGORYPREDICTION
DESCRIPTOR.message_types_by_name['ChipPrediction'] = _CHIPPREDICTION
DESCRIPTOR.message_types_by_name['ChipImageAndMetadataResponse'] = _CHIPIMAGEANDMETADATARESPONSE
DESCRIPTOR.message_types_by_name['ChipQueueInformationRequest'] = _CHIPQUEUEINFORMATIONREQUEST
DESCRIPTOR.message_types_by_name['ChipQueueInformationResponse'] = _CHIPQUEUEINFORMATIONRESPONSE
DESCRIPTOR.message_types_by_name['P2PImageAndMetadataResponse'] = _P2PIMAGEANDMETADATARESPONSE
DESCRIPTOR.message_types_by_name['GetCameraInfoRequest'] = _GETCAMERAINFOREQUEST
DESCRIPTOR.message_types_by_name['CameraInfo'] = _CAMERAINFO
DESCRIPTOR.message_types_by_name['GetCameraInfoResponse'] = _GETCAMERAINFORESPONSE
DESCRIPTOR.message_types_by_name['GetLightweightBurstRecordRequest'] = _GETLIGHTWEIGHTBURSTRECORDREQUEST
DESCRIPTOR.message_types_by_name['GetLightweightBurstRecordResponse'] = _GETLIGHTWEIGHTBURSTRECORDRESPONSE
DESCRIPTOR.message_types_by_name['GetBootedRequest'] = _GETBOOTEDREQUEST
DESCRIPTOR.message_types_by_name['GetBootedResponse'] = _GETBOOTEDRESPONSE
DESCRIPTOR.message_types_by_name['GetReadyRequest'] = _GETREADYREQUEST
DESCRIPTOR.message_types_by_name['GetReadyResponse'] = _GETREADYRESPONSE
DESCRIPTOR.message_types_by_name['GetErrorStateResponse'] = _GETERRORSTATERESPONSE
DESCRIPTOR.message_types_by_name['DeepweedDetection'] = _DEEPWEEDDETECTION
DESCRIPTOR.message_types_by_name['DeepweedOutput'] = _DEEPWEEDOUTPUT
DESCRIPTOR.message_types_by_name['GetDeepweedOutputByTimestampRequest'] = _GETDEEPWEEDOUTPUTBYTIMESTAMPREQUEST
DESCRIPTOR.message_types_by_name['GetRecommendedStrobeSettingsRequest'] = _GETRECOMMENDEDSTROBESETTINGSREQUEST
DESCRIPTOR.message_types_by_name['GetRecommendedStrobeSettingsResponse'] = _GETRECOMMENDEDSTROBESETTINGSRESPONSE
DESCRIPTOR.message_types_by_name['StartP2PBufferringRequest'] = _STARTP2PBUFFERRINGREQUEST
DESCRIPTOR.message_types_by_name['StartP2PBufferringResponse'] = _STARTP2PBUFFERRINGRESPONSE
DESCRIPTOR.message_types_by_name['StopP2PBufferringRequest'] = _STOPP2PBUFFERRINGREQUEST
DESCRIPTOR.message_types_by_name['StopP2PBufferringResponse'] = _STOPP2PBUFFERRINGRESPONSE
DESCRIPTOR.message_types_by_name['P2PCaptureRequest'] = _P2PCAPTUREREQUEST
DESCRIPTOR.message_types_by_name['P2PCaptureResponse'] = _P2PCAPTURERESPONSE
DESCRIPTOR.message_types_by_name['P2PBufferingBurstPredictMetadata'] = _P2PBUFFERINGBURSTPREDICTMETADATA
DESCRIPTOR.message_types_by_name['P2PBufferringBurstCaptureRequest'] = _P2PBUFFERRINGBURSTCAPTUREREQUEST
DESCRIPTOR.message_types_by_name['P2PBufferringBurstCaptureResponse'] = _P2PBUFFERRINGBURSTCAPTURERESPONSE
DESCRIPTOR.message_types_by_name['GetNextDeepweedOutputRequest'] = _GETNEXTDEEPWEEDOUTPUTREQUEST
DESCRIPTOR.message_types_by_name['SetTargetingStateRequest'] = _SETTARGETINGSTATEREQUEST
DESCRIPTOR.message_types_by_name['SetTargetingStateResponse'] = _SETTARGETINGSTATERESPONSE
DESCRIPTOR.message_types_by_name['GetNextFocusMetricRequest'] = _GETNEXTFOCUSMETRICREQUEST
DESCRIPTOR.message_types_by_name['GetNextFocusMetricResponse'] = _GETNEXTFOCUSMETRICRESPONSE
DESCRIPTOR.message_types_by_name['RemoveDataDirRequest'] = _REMOVEDATADIRREQUEST
DESCRIPTOR.message_types_by_name['RemoveDataDirResponse'] = _REMOVEDATADIRRESPONSE
DESCRIPTOR.message_types_by_name['LastNImageRequest'] = _LASTNIMAGEREQUEST
DESCRIPTOR.message_types_by_name['ComputeCapabilitiesResponse'] = _COMPUTECAPABILITIESRESPONSE
DESCRIPTOR.message_types_by_name['SupportedTensorRTVersionsResponse'] = _SUPPORTEDTENSORRTVERSIONSRESPONSE
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['ScoreQueueAndCount'] = _SCOREQUEUEANDCOUNT
DESCRIPTOR.message_types_by_name['ListScoreQueuesResponse'] = _LISTSCOREQUEUESRESPONSE
DESCRIPTOR.message_types_by_name['GetCategoryCollectionResponse'] = _GETCATEGORYCOLLECTIONRESPONSE
DESCRIPTOR.message_types_by_name['SnapshotPredictImagesRequest'] = _SNAPSHOTPREDICTIMAGESREQUEST
DESCRIPTOR.message_types_by_name['PcamSnapshot'] = _PCAMSNAPSHOT
DESCRIPTOR.message_types_by_name['SnapshotPredictImagesResponse'] = _SNAPSHOTPREDICTIMAGESRESPONSE
DESCRIPTOR.message_types_by_name['GetChipForPredictImageRequest'] = _GETCHIPFORPREDICTIMAGEREQUEST
DESCRIPTOR.message_types_by_name['GetChipForPredictImageResponse'] = _GETCHIPFORPREDICTIMAGERESPONSE
DESCRIPTOR.enum_types_by_name['BufferUseCase'] = _BUFFERUSECASE
DESCRIPTOR.enum_types_by_name['HitClass'] = _HITCLASS
DESCRIPTOR.enum_types_by_name['ScoreQueueType'] = _SCOREQUEUETYPE
DESCRIPTOR.enum_types_by_name['ErrorType'] = _ERRORTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TargetSafetyZone = _reflection.GeneratedProtocolMessageType('TargetSafetyZone', (_message.Message,), {
  'DESCRIPTOR' : _TARGETSAFETYZONE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.TargetSafetyZone)
  })
_sym_db.RegisterMessage(TargetSafetyZone)

P2PContext = _reflection.GeneratedProtocolMessageType('P2PContext', (_message.Message,), {
  'DESCRIPTOR' : _P2PCONTEXT,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.P2PContext)
  })
_sym_db.RegisterMessage(P2PContext)

SetP2PContextRequest = _reflection.GeneratedProtocolMessageType('SetP2PContextRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETP2PCONTEXTREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetP2PContextRequest)
  })
_sym_db.RegisterMessage(SetP2PContextRequest)

SetP2PContextResponse = _reflection.GeneratedProtocolMessageType('SetP2PContextResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETP2PCONTEXTRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetP2PContextResponse)
  })
_sym_db.RegisterMessage(SetP2PContextResponse)

GetCameraDimensionsRequest = _reflection.GeneratedProtocolMessageType('GetCameraDimensionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCAMERADIMENSIONSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCameraDimensionsRequest)
  })
_sym_db.RegisterMessage(GetCameraDimensionsRequest)

GetCameraDimensionsResponse = _reflection.GeneratedProtocolMessageType('GetCameraDimensionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCAMERADIMENSIONSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCameraDimensionsResponse)
  })
_sym_db.RegisterMessage(GetCameraDimensionsResponse)

StartP2PDataCaptureRequest = _reflection.GeneratedProtocolMessageType('StartP2PDataCaptureRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTP2PDATACAPTUREREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StartP2PDataCaptureRequest)
  })
_sym_db.RegisterMessage(StartP2PDataCaptureRequest)

PointDetectionCategory = _reflection.GeneratedProtocolMessageType('PointDetectionCategory', (_message.Message,), {
  'DESCRIPTOR' : _POINTDETECTIONCATEGORY,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.PointDetectionCategory)
  })
_sym_db.RegisterMessage(PointDetectionCategory)

SegmentationDetectionCategory = _reflection.GeneratedProtocolMessageType('SegmentationDetectionCategory', (_message.Message,), {
  'DESCRIPTOR' : _SEGMENTATIONDETECTIONCATEGORY,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SegmentationDetectionCategory)
  })
_sym_db.RegisterMessage(SegmentationDetectionCategory)

DeepweedDetectionCriteriaSetting = _reflection.GeneratedProtocolMessageType('DeepweedDetectionCriteriaSetting', (_message.Message,), {
  'DESCRIPTOR' : _DEEPWEEDDETECTIONCRITERIASETTING,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.DeepweedDetectionCriteriaSetting)
  })
_sym_db.RegisterMessage(DeepweedDetectionCriteriaSetting)

SetDeepweedDetectionCriteriaRequest = _reflection.GeneratedProtocolMessageType('SetDeepweedDetectionCriteriaRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETDEEPWEEDDETECTIONCRITERIAREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetDeepweedDetectionCriteriaRequest)
  })
_sym_db.RegisterMessage(SetDeepweedDetectionCriteriaRequest)

SetDeepweedDetectionCriteriaResponse = _reflection.GeneratedProtocolMessageType('SetDeepweedDetectionCriteriaResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETDEEPWEEDDETECTIONCRITERIARESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetDeepweedDetectionCriteriaResponse)
  })
_sym_db.RegisterMessage(SetDeepweedDetectionCriteriaResponse)

GetDeepweedDetectionCriteriaRequest = _reflection.GeneratedProtocolMessageType('GetDeepweedDetectionCriteriaRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDDETECTIONCRITERIAREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedDetectionCriteriaRequest)
  })
_sym_db.RegisterMessage(GetDeepweedDetectionCriteriaRequest)

GetDeepweedDetectionCriteriaResponse = _reflection.GeneratedProtocolMessageType('GetDeepweedDetectionCriteriaResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDDETECTIONCRITERIARESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedDetectionCriteriaResponse)
  })
_sym_db.RegisterMessage(GetDeepweedDetectionCriteriaResponse)

GetDeepweedSupportedCategoriesRequest = _reflection.GeneratedProtocolMessageType('GetDeepweedSupportedCategoriesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedSupportedCategoriesRequest)
  })
_sym_db.RegisterMessage(GetDeepweedSupportedCategoriesRequest)

GetDeepweedSupportedCategoriesResponse = _reflection.GeneratedProtocolMessageType('GetDeepweedSupportedCategoriesResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDSUPPORTEDCATEGORIESRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedSupportedCategoriesResponse)
  })
_sym_db.RegisterMessage(GetDeepweedSupportedCategoriesResponse)

GetDeepweedIndexToCategoryRequest = _reflection.GeneratedProtocolMessageType('GetDeepweedIndexToCategoryRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDINDEXTOCATEGORYREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedIndexToCategoryRequest)
  })
_sym_db.RegisterMessage(GetDeepweedIndexToCategoryRequest)

GetDeepweedIndexToCategoryResponse = _reflection.GeneratedProtocolMessageType('GetDeepweedIndexToCategoryResponse', (_message.Message,), {

  'WeedPointIndexToCategoryEntry' : _reflection.GeneratedProtocolMessageType('WeedPointIndexToCategoryEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY,
    '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
    # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry)
    })
  ,

  'CropPointIndexToCategoryEntry' : _reflection.GeneratedProtocolMessageType('CropPointIndexToCategoryEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY,
    '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
    # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry)
    })
  ,

  'IntersectionIndexToCategoryEntry' : _reflection.GeneratedProtocolMessageType('IntersectionIndexToCategoryEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY,
    '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
    # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry)
    })
  ,
  'DESCRIPTOR' : _GETDEEPWEEDINDEXTOCATEGORYRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedIndexToCategoryResponse)
  })
_sym_db.RegisterMessage(GetDeepweedIndexToCategoryResponse)
_sym_db.RegisterMessage(GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry)
_sym_db.RegisterMessage(GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry)
_sym_db.RegisterMessage(GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry)

GetPredictCamMatrixRequest = _reflection.GeneratedProtocolMessageType('GetPredictCamMatrixRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPREDICTCAMMATRIXREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetPredictCamMatrixRequest)
  })
_sym_db.RegisterMessage(GetPredictCamMatrixRequest)

GetPredictCamDistortionCoefficientsRequest = _reflection.GeneratedProtocolMessageType('GetPredictCamDistortionCoefficientsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPREDICTCAMDISTORTIONCOEFFICIENTSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetPredictCamDistortionCoefficientsRequest)
  })
_sym_db.RegisterMessage(GetPredictCamDistortionCoefficientsRequest)

SetCameraSettingsRequest = _reflection.GeneratedProtocolMessageType('SetCameraSettingsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETCAMERASETTINGSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetCameraSettingsRequest)
  })
_sym_db.RegisterMessage(SetCameraSettingsRequest)

SetCameraSettingsResponse = _reflection.GeneratedProtocolMessageType('SetCameraSettingsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETCAMERASETTINGSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetCameraSettingsResponse)
  })
_sym_db.RegisterMessage(SetCameraSettingsResponse)

SetAutoWhitebalanceRequest = _reflection.GeneratedProtocolMessageType('SetAutoWhitebalanceRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETAUTOWHITEBALANCEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetAutoWhitebalanceRequest)
  })
_sym_db.RegisterMessage(SetAutoWhitebalanceRequest)

SetAutoWhitebalanceResponse = _reflection.GeneratedProtocolMessageType('SetAutoWhitebalanceResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETAUTOWHITEBALANCERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetAutoWhitebalanceResponse)
  })
_sym_db.RegisterMessage(SetAutoWhitebalanceResponse)

GetCameraSettingsRequest = _reflection.GeneratedProtocolMessageType('GetCameraSettingsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCAMERASETTINGSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCameraSettingsRequest)
  })
_sym_db.RegisterMessage(GetCameraSettingsRequest)

CameraSettingsResponse = _reflection.GeneratedProtocolMessageType('CameraSettingsResponse', (_message.Message,), {
  'DESCRIPTOR' : _CAMERASETTINGSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.CameraSettingsResponse)
  })
_sym_db.RegisterMessage(CameraSettingsResponse)

GetCameraSettingsResponse = _reflection.GeneratedProtocolMessageType('GetCameraSettingsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCAMERASETTINGSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCameraSettingsResponse)
  })
_sym_db.RegisterMessage(GetCameraSettingsResponse)

StartBurstRecordFramesRequest = _reflection.GeneratedProtocolMessageType('StartBurstRecordFramesRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTBURSTRECORDFRAMESREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StartBurstRecordFramesRequest)
  })
_sym_db.RegisterMessage(StartBurstRecordFramesRequest)

StartBurstRecordFramesResponse = _reflection.GeneratedProtocolMessageType('StartBurstRecordFramesResponse', (_message.Message,), {
  'DESCRIPTOR' : _STARTBURSTRECORDFRAMESRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StartBurstRecordFramesResponse)
  })
_sym_db.RegisterMessage(StartBurstRecordFramesResponse)

StopBurstRecordFramesRequest = _reflection.GeneratedProtocolMessageType('StopBurstRecordFramesRequest', (_message.Message,), {
  'DESCRIPTOR' : _STOPBURSTRECORDFRAMESREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StopBurstRecordFramesRequest)
  })
_sym_db.RegisterMessage(StopBurstRecordFramesRequest)

StopBurstRecordFramesResponse = _reflection.GeneratedProtocolMessageType('StopBurstRecordFramesResponse', (_message.Message,), {
  'DESCRIPTOR' : _STOPBURSTRECORDFRAMESRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StopBurstRecordFramesResponse)
  })
_sym_db.RegisterMessage(StopBurstRecordFramesResponse)

P2POutputProto = _reflection.GeneratedProtocolMessageType('P2POutputProto', (_message.Message,), {
  'DESCRIPTOR' : _P2POUTPUTPROTO,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.P2POutputProto)
  })
_sym_db.RegisterMessage(P2POutputProto)

GetNextP2POutputRequest = _reflection.GeneratedProtocolMessageType('GetNextP2POutputRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTP2POUTPUTREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetNextP2POutputRequest)
  })
_sym_db.RegisterMessage(GetNextP2POutputRequest)

GetConnectorsRequest = _reflection.GeneratedProtocolMessageType('GetConnectorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCONNECTORSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetConnectorsRequest)
  })
_sym_db.RegisterMessage(GetConnectorsRequest)

ConnectorResponse = _reflection.GeneratedProtocolMessageType('ConnectorResponse', (_message.Message,), {
  'DESCRIPTOR' : _CONNECTORRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ConnectorResponse)
  })
_sym_db.RegisterMessage(ConnectorResponse)

GetConnectorsResponse = _reflection.GeneratedProtocolMessageType('GetConnectorsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCONNECTORSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetConnectorsResponse)
  })
_sym_db.RegisterMessage(GetConnectorsResponse)

SetConnectorsRequest = _reflection.GeneratedProtocolMessageType('SetConnectorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETCONNECTORSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetConnectorsRequest)
  })
_sym_db.RegisterMessage(SetConnectorsRequest)

SetConnectorsResponse = _reflection.GeneratedProtocolMessageType('SetConnectorsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETCONNECTORSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetConnectorsResponse)
  })
_sym_db.RegisterMessage(SetConnectorsResponse)

NodeTiming = _reflection.GeneratedProtocolMessageType('NodeTiming', (_message.Message,), {

  'StateTimingsEntry' : _reflection.GeneratedProtocolMessageType('StateTimingsEntry', (_message.Message,), {
    'DESCRIPTOR' : _NODETIMING_STATETIMINGSENTRY,
    '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
    # @@protoc_insertion_point(class_scope:cv.runtime.proto.NodeTiming.StateTimingsEntry)
    })
  ,
  'DESCRIPTOR' : _NODETIMING,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.NodeTiming)
  })
_sym_db.RegisterMessage(NodeTiming)
_sym_db.RegisterMessage(NodeTiming.StateTimingsEntry)

GetTimingRequest = _reflection.GeneratedProtocolMessageType('GetTimingRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTIMINGREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetTimingRequest)
  })
_sym_db.RegisterMessage(GetTimingRequest)

GetTimingResponse = _reflection.GeneratedProtocolMessageType('GetTimingResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETTIMINGRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetTimingResponse)
  })
_sym_db.RegisterMessage(GetTimingResponse)

PredictRequest = _reflection.GeneratedProtocolMessageType('PredictRequest', (_message.Message,), {
  'DESCRIPTOR' : _PREDICTREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.PredictRequest)
  })
_sym_db.RegisterMessage(PredictRequest)

PredictResponse = _reflection.GeneratedProtocolMessageType('PredictResponse', (_message.Message,), {
  'DESCRIPTOR' : _PREDICTRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.PredictResponse)
  })
_sym_db.RegisterMessage(PredictResponse)

LoadAndQueueRequest = _reflection.GeneratedProtocolMessageType('LoadAndQueueRequest', (_message.Message,), {
  'DESCRIPTOR' : _LOADANDQUEUEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.LoadAndQueueRequest)
  })
_sym_db.RegisterMessage(LoadAndQueueRequest)

LoadAndQueueResponse = _reflection.GeneratedProtocolMessageType('LoadAndQueueResponse', (_message.Message,), {
  'DESCRIPTOR' : _LOADANDQUEUERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.LoadAndQueueResponse)
  })
_sym_db.RegisterMessage(LoadAndQueueResponse)

SetImageRequest = _reflection.GeneratedProtocolMessageType('SetImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETIMAGEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetImageRequest)
  })
_sym_db.RegisterMessage(SetImageRequest)

SetImageResponse = _reflection.GeneratedProtocolMessageType('SetImageResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETIMAGERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetImageResponse)
  })
_sym_db.RegisterMessage(SetImageResponse)

UnsetImageRequest = _reflection.GeneratedProtocolMessageType('UnsetImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _UNSETIMAGEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.UnsetImageRequest)
  })
_sym_db.RegisterMessage(UnsetImageRequest)

UnsetImageResponse = _reflection.GeneratedProtocolMessageType('UnsetImageResponse', (_message.Message,), {
  'DESCRIPTOR' : _UNSETIMAGERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.UnsetImageResponse)
  })
_sym_db.RegisterMessage(UnsetImageResponse)

GetModelPathsRequest = _reflection.GeneratedProtocolMessageType('GetModelPathsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETMODELPATHSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetModelPathsRequest)
  })
_sym_db.RegisterMessage(GetModelPathsRequest)

GetModelPathsResponse = _reflection.GeneratedProtocolMessageType('GetModelPathsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETMODELPATHSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetModelPathsResponse)
  })
_sym_db.RegisterMessage(GetModelPathsResponse)

GetCameraTemperaturesRequest = _reflection.GeneratedProtocolMessageType('GetCameraTemperaturesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCAMERATEMPERATURESREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCameraTemperaturesRequest)
  })
_sym_db.RegisterMessage(GetCameraTemperaturesRequest)

CameraTemperature = _reflection.GeneratedProtocolMessageType('CameraTemperature', (_message.Message,), {
  'DESCRIPTOR' : _CAMERATEMPERATURE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.CameraTemperature)
  })
_sym_db.RegisterMessage(CameraTemperature)

GetCameraTemperaturesResponse = _reflection.GeneratedProtocolMessageType('GetCameraTemperaturesResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCAMERATEMPERATURESRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCameraTemperaturesResponse)
  })
_sym_db.RegisterMessage(GetCameraTemperaturesResponse)

GeoLLA = _reflection.GeneratedProtocolMessageType('GeoLLA', (_message.Message,), {
  'DESCRIPTOR' : _GEOLLA,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GeoLLA)
  })
_sym_db.RegisterMessage(GeoLLA)

GeoECEF = _reflection.GeneratedProtocolMessageType('GeoECEF', (_message.Message,), {
  'DESCRIPTOR' : _GEOECEF,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GeoECEF)
  })
_sym_db.RegisterMessage(GeoECEF)

SetGPSLocationRequest = _reflection.GeneratedProtocolMessageType('SetGPSLocationRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETGPSLOCATIONREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetGPSLocationRequest)
  })
_sym_db.RegisterMessage(SetGPSLocationRequest)

SetGPSLocationResponse = _reflection.GeneratedProtocolMessageType('SetGPSLocationResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETGPSLOCATIONRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetGPSLocationResponse)
  })
_sym_db.RegisterMessage(SetGPSLocationResponse)

SetImplementStatusRequest = _reflection.GeneratedProtocolMessageType('SetImplementStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETIMPLEMENTSTATUSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetImplementStatusRequest)
  })
_sym_db.RegisterMessage(SetImplementStatusRequest)

SetImplementStatusResponse = _reflection.GeneratedProtocolMessageType('SetImplementStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETIMPLEMENTSTATUSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetImplementStatusResponse)
  })
_sym_db.RegisterMessage(SetImplementStatusResponse)

SetImageScoreRequest = _reflection.GeneratedProtocolMessageType('SetImageScoreRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETIMAGESCOREREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetImageScoreRequest)
  })
_sym_db.RegisterMessage(SetImageScoreRequest)

SetImageScoreResponse = _reflection.GeneratedProtocolMessageType('SetImageScoreResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETIMAGESCORERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetImageScoreResponse)
  })
_sym_db.RegisterMessage(SetImageScoreResponse)

GetScoreQueueRequest = _reflection.GeneratedProtocolMessageType('GetScoreQueueRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSCOREQUEUEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetScoreQueueRequest)
  })
_sym_db.RegisterMessage(GetScoreQueueRequest)

ScoreObject = _reflection.GeneratedProtocolMessageType('ScoreObject', (_message.Message,), {
  'DESCRIPTOR' : _SCOREOBJECT,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ScoreObject)
  })
_sym_db.RegisterMessage(ScoreObject)

GetScoreQueueResponse = _reflection.GeneratedProtocolMessageType('GetScoreQueueResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSCOREQUEUERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetScoreQueueResponse)
  })
_sym_db.RegisterMessage(GetScoreQueueResponse)

GetMaxImageScoreRequest = _reflection.GeneratedProtocolMessageType('GetMaxImageScoreRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETMAXIMAGESCOREREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetMaxImageScoreRequest)
  })
_sym_db.RegisterMessage(GetMaxImageScoreRequest)

GetMaxImageScoreResponse = _reflection.GeneratedProtocolMessageType('GetMaxImageScoreResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETMAXIMAGESCORERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetMaxImageScoreResponse)
  })
_sym_db.RegisterMessage(GetMaxImageScoreResponse)

GetMaxScoredImageRequest = _reflection.GeneratedProtocolMessageType('GetMaxScoredImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETMAXSCOREDIMAGEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetMaxScoredImageRequest)
  })
_sym_db.RegisterMessage(GetMaxScoredImageRequest)

GetLatestP2PImageRequest = _reflection.GeneratedProtocolMessageType('GetLatestP2PImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETLATESTP2PIMAGEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetLatestP2PImageRequest)
  })
_sym_db.RegisterMessage(GetLatestP2PImageRequest)

GetLatestImageRequest = _reflection.GeneratedProtocolMessageType('GetLatestImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETLATESTIMAGEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetLatestImageRequest)
  })
_sym_db.RegisterMessage(GetLatestImageRequest)

GetImageNearTimestampRequest = _reflection.GeneratedProtocolMessageType('GetImageNearTimestampRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETIMAGENEARTIMESTAMPREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetImageNearTimestampRequest)
  })
_sym_db.RegisterMessage(GetImageNearTimestampRequest)

GetChipImageRequest = _reflection.GeneratedProtocolMessageType('GetChipImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCHIPIMAGEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetChipImageRequest)
  })
_sym_db.RegisterMessage(GetChipImageRequest)

FlushQueuesRequest = _reflection.GeneratedProtocolMessageType('FlushQueuesRequest', (_message.Message,), {
  'DESCRIPTOR' : _FLUSHQUEUESREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.FlushQueuesRequest)
  })
_sym_db.RegisterMessage(FlushQueuesRequest)

FlushQueuesResponse = _reflection.GeneratedProtocolMessageType('FlushQueuesResponse', (_message.Message,), {
  'DESCRIPTOR' : _FLUSHQUEUESRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.FlushQueuesResponse)
  })
_sym_db.RegisterMessage(FlushQueuesResponse)

ImageAndMetadataResponse = _reflection.GeneratedProtocolMessageType('ImageAndMetadataResponse', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEANDMETADATARESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ImageAndMetadataResponse)
  })
_sym_db.RegisterMessage(ImageAndMetadataResponse)

CategoryPrediction = _reflection.GeneratedProtocolMessageType('CategoryPrediction', (_message.Message,), {
  'DESCRIPTOR' : _CATEGORYPREDICTION,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.CategoryPrediction)
  })
_sym_db.RegisterMessage(CategoryPrediction)

ChipPrediction = _reflection.GeneratedProtocolMessageType('ChipPrediction', (_message.Message,), {
  'DESCRIPTOR' : _CHIPPREDICTION,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ChipPrediction)
  })
_sym_db.RegisterMessage(ChipPrediction)

ChipImageAndMetadataResponse = _reflection.GeneratedProtocolMessageType('ChipImageAndMetadataResponse', (_message.Message,), {
  'DESCRIPTOR' : _CHIPIMAGEANDMETADATARESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ChipImageAndMetadataResponse)
  })
_sym_db.RegisterMessage(ChipImageAndMetadataResponse)

ChipQueueInformationRequest = _reflection.GeneratedProtocolMessageType('ChipQueueInformationRequest', (_message.Message,), {
  'DESCRIPTOR' : _CHIPQUEUEINFORMATIONREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ChipQueueInformationRequest)
  })
_sym_db.RegisterMessage(ChipQueueInformationRequest)

ChipQueueInformationResponse = _reflection.GeneratedProtocolMessageType('ChipQueueInformationResponse', (_message.Message,), {
  'DESCRIPTOR' : _CHIPQUEUEINFORMATIONRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ChipQueueInformationResponse)
  })
_sym_db.RegisterMessage(ChipQueueInformationResponse)

P2PImageAndMetadataResponse = _reflection.GeneratedProtocolMessageType('P2PImageAndMetadataResponse', (_message.Message,), {
  'DESCRIPTOR' : _P2PIMAGEANDMETADATARESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.P2PImageAndMetadataResponse)
  })
_sym_db.RegisterMessage(P2PImageAndMetadataResponse)

GetCameraInfoRequest = _reflection.GeneratedProtocolMessageType('GetCameraInfoRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCAMERAINFOREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCameraInfoRequest)
  })
_sym_db.RegisterMessage(GetCameraInfoRequest)

CameraInfo = _reflection.GeneratedProtocolMessageType('CameraInfo', (_message.Message,), {
  'DESCRIPTOR' : _CAMERAINFO,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.CameraInfo)
  })
_sym_db.RegisterMessage(CameraInfo)

GetCameraInfoResponse = _reflection.GeneratedProtocolMessageType('GetCameraInfoResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCAMERAINFORESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCameraInfoResponse)
  })
_sym_db.RegisterMessage(GetCameraInfoResponse)

GetLightweightBurstRecordRequest = _reflection.GeneratedProtocolMessageType('GetLightweightBurstRecordRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETLIGHTWEIGHTBURSTRECORDREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetLightweightBurstRecordRequest)
  })
_sym_db.RegisterMessage(GetLightweightBurstRecordRequest)

GetLightweightBurstRecordResponse = _reflection.GeneratedProtocolMessageType('GetLightweightBurstRecordResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETLIGHTWEIGHTBURSTRECORDRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetLightweightBurstRecordResponse)
  })
_sym_db.RegisterMessage(GetLightweightBurstRecordResponse)

GetBootedRequest = _reflection.GeneratedProtocolMessageType('GetBootedRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETBOOTEDREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetBootedRequest)
  })
_sym_db.RegisterMessage(GetBootedRequest)

GetBootedResponse = _reflection.GeneratedProtocolMessageType('GetBootedResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETBOOTEDRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetBootedResponse)
  })
_sym_db.RegisterMessage(GetBootedResponse)

GetReadyRequest = _reflection.GeneratedProtocolMessageType('GetReadyRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETREADYREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetReadyRequest)
  })
_sym_db.RegisterMessage(GetReadyRequest)

GetReadyResponse = _reflection.GeneratedProtocolMessageType('GetReadyResponse', (_message.Message,), {

  'DeepweedReadyStateEntry' : _reflection.GeneratedProtocolMessageType('DeepweedReadyStateEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETREADYRESPONSE_DEEPWEEDREADYSTATEENTRY,
    '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
    # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetReadyResponse.DeepweedReadyStateEntry)
    })
  ,

  'P2pReadyStateEntry' : _reflection.GeneratedProtocolMessageType('P2pReadyStateEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETREADYRESPONSE_P2PREADYSTATEENTRY,
    '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
    # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetReadyResponse.P2pReadyStateEntry)
    })
  ,
  'DESCRIPTOR' : _GETREADYRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetReadyResponse)
  })
_sym_db.RegisterMessage(GetReadyResponse)
_sym_db.RegisterMessage(GetReadyResponse.DeepweedReadyStateEntry)
_sym_db.RegisterMessage(GetReadyResponse.P2pReadyStateEntry)

GetErrorStateResponse = _reflection.GeneratedProtocolMessageType('GetErrorStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETERRORSTATERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetErrorStateResponse)
  })
_sym_db.RegisterMessage(GetErrorStateResponse)

DeepweedDetection = _reflection.GeneratedProtocolMessageType('DeepweedDetection', (_message.Message,), {
  'DESCRIPTOR' : _DEEPWEEDDETECTION,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.DeepweedDetection)
  })
_sym_db.RegisterMessage(DeepweedDetection)

DeepweedOutput = _reflection.GeneratedProtocolMessageType('DeepweedOutput', (_message.Message,), {
  'DESCRIPTOR' : _DEEPWEEDOUTPUT,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.DeepweedOutput)
  })
_sym_db.RegisterMessage(DeepweedOutput)

GetDeepweedOutputByTimestampRequest = _reflection.GeneratedProtocolMessageType('GetDeepweedOutputByTimestampRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDEEPWEEDOUTPUTBYTIMESTAMPREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetDeepweedOutputByTimestampRequest)
  })
_sym_db.RegisterMessage(GetDeepweedOutputByTimestampRequest)

GetRecommendedStrobeSettingsRequest = _reflection.GeneratedProtocolMessageType('GetRecommendedStrobeSettingsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETRECOMMENDEDSTROBESETTINGSREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetRecommendedStrobeSettingsRequest)
  })
_sym_db.RegisterMessage(GetRecommendedStrobeSettingsRequest)

GetRecommendedStrobeSettingsResponse = _reflection.GeneratedProtocolMessageType('GetRecommendedStrobeSettingsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETRECOMMENDEDSTROBESETTINGSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetRecommendedStrobeSettingsResponse)
  })
_sym_db.RegisterMessage(GetRecommendedStrobeSettingsResponse)

StartP2PBufferringRequest = _reflection.GeneratedProtocolMessageType('StartP2PBufferringRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTP2PBUFFERRINGREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StartP2PBufferringRequest)
  })
_sym_db.RegisterMessage(StartP2PBufferringRequest)

StartP2PBufferringResponse = _reflection.GeneratedProtocolMessageType('StartP2PBufferringResponse', (_message.Message,), {
  'DESCRIPTOR' : _STARTP2PBUFFERRINGRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StartP2PBufferringResponse)
  })
_sym_db.RegisterMessage(StartP2PBufferringResponse)

StopP2PBufferringRequest = _reflection.GeneratedProtocolMessageType('StopP2PBufferringRequest', (_message.Message,), {
  'DESCRIPTOR' : _STOPP2PBUFFERRINGREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StopP2PBufferringRequest)
  })
_sym_db.RegisterMessage(StopP2PBufferringRequest)

StopP2PBufferringResponse = _reflection.GeneratedProtocolMessageType('StopP2PBufferringResponse', (_message.Message,), {
  'DESCRIPTOR' : _STOPP2PBUFFERRINGRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.StopP2PBufferringResponse)
  })
_sym_db.RegisterMessage(StopP2PBufferringResponse)

P2PCaptureRequest = _reflection.GeneratedProtocolMessageType('P2PCaptureRequest', (_message.Message,), {
  'DESCRIPTOR' : _P2PCAPTUREREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.P2PCaptureRequest)
  })
_sym_db.RegisterMessage(P2PCaptureRequest)

P2PCaptureResponse = _reflection.GeneratedProtocolMessageType('P2PCaptureResponse', (_message.Message,), {
  'DESCRIPTOR' : _P2PCAPTURERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.P2PCaptureResponse)
  })
_sym_db.RegisterMessage(P2PCaptureResponse)

P2PBufferingBurstPredictMetadata = _reflection.GeneratedProtocolMessageType('P2PBufferingBurstPredictMetadata', (_message.Message,), {
  'DESCRIPTOR' : _P2PBUFFERINGBURSTPREDICTMETADATA,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.P2PBufferingBurstPredictMetadata)
  })
_sym_db.RegisterMessage(P2PBufferingBurstPredictMetadata)

P2PBufferringBurstCaptureRequest = _reflection.GeneratedProtocolMessageType('P2PBufferringBurstCaptureRequest', (_message.Message,), {
  'DESCRIPTOR' : _P2PBUFFERRINGBURSTCAPTUREREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.P2PBufferringBurstCaptureRequest)
  })
_sym_db.RegisterMessage(P2PBufferringBurstCaptureRequest)

P2PBufferringBurstCaptureResponse = _reflection.GeneratedProtocolMessageType('P2PBufferringBurstCaptureResponse', (_message.Message,), {
  'DESCRIPTOR' : _P2PBUFFERRINGBURSTCAPTURERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.P2PBufferringBurstCaptureResponse)
  })
_sym_db.RegisterMessage(P2PBufferringBurstCaptureResponse)

GetNextDeepweedOutputRequest = _reflection.GeneratedProtocolMessageType('GetNextDeepweedOutputRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTDEEPWEEDOUTPUTREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetNextDeepweedOutputRequest)
  })
_sym_db.RegisterMessage(GetNextDeepweedOutputRequest)

SetTargetingStateRequest = _reflection.GeneratedProtocolMessageType('SetTargetingStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETTARGETINGSTATEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetTargetingStateRequest)
  })
_sym_db.RegisterMessage(SetTargetingStateRequest)

SetTargetingStateResponse = _reflection.GeneratedProtocolMessageType('SetTargetingStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETTARGETINGSTATERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SetTargetingStateResponse)
  })
_sym_db.RegisterMessage(SetTargetingStateResponse)

GetNextFocusMetricRequest = _reflection.GeneratedProtocolMessageType('GetNextFocusMetricRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTFOCUSMETRICREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetNextFocusMetricRequest)
  })
_sym_db.RegisterMessage(GetNextFocusMetricRequest)

GetNextFocusMetricResponse = _reflection.GeneratedProtocolMessageType('GetNextFocusMetricResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTFOCUSMETRICRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetNextFocusMetricResponse)
  })
_sym_db.RegisterMessage(GetNextFocusMetricResponse)

RemoveDataDirRequest = _reflection.GeneratedProtocolMessageType('RemoveDataDirRequest', (_message.Message,), {
  'DESCRIPTOR' : _REMOVEDATADIRREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.RemoveDataDirRequest)
  })
_sym_db.RegisterMessage(RemoveDataDirRequest)

RemoveDataDirResponse = _reflection.GeneratedProtocolMessageType('RemoveDataDirResponse', (_message.Message,), {
  'DESCRIPTOR' : _REMOVEDATADIRRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.RemoveDataDirResponse)
  })
_sym_db.RegisterMessage(RemoveDataDirResponse)

LastNImageRequest = _reflection.GeneratedProtocolMessageType('LastNImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _LASTNIMAGEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.LastNImageRequest)
  })
_sym_db.RegisterMessage(LastNImageRequest)

ComputeCapabilitiesResponse = _reflection.GeneratedProtocolMessageType('ComputeCapabilitiesResponse', (_message.Message,), {
  'DESCRIPTOR' : _COMPUTECAPABILITIESRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ComputeCapabilitiesResponse)
  })
_sym_db.RegisterMessage(ComputeCapabilitiesResponse)

SupportedTensorRTVersionsResponse = _reflection.GeneratedProtocolMessageType('SupportedTensorRTVersionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SUPPORTEDTENSORRTVERSIONSRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SupportedTensorRTVersionsResponse)
  })
_sym_db.RegisterMessage(SupportedTensorRTVersionsResponse)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.Empty)
  })
_sym_db.RegisterMessage(Empty)

ScoreQueueAndCount = _reflection.GeneratedProtocolMessageType('ScoreQueueAndCount', (_message.Message,), {
  'DESCRIPTOR' : _SCOREQUEUEANDCOUNT,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ScoreQueueAndCount)
  })
_sym_db.RegisterMessage(ScoreQueueAndCount)

ListScoreQueuesResponse = _reflection.GeneratedProtocolMessageType('ListScoreQueuesResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTSCOREQUEUESRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.ListScoreQueuesResponse)
  })
_sym_db.RegisterMessage(ListScoreQueuesResponse)

GetCategoryCollectionResponse = _reflection.GeneratedProtocolMessageType('GetCategoryCollectionResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCATEGORYCOLLECTIONRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetCategoryCollectionResponse)
  })
_sym_db.RegisterMessage(GetCategoryCollectionResponse)

SnapshotPredictImagesRequest = _reflection.GeneratedProtocolMessageType('SnapshotPredictImagesRequest', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPREDICTIMAGESREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SnapshotPredictImagesRequest)
  })
_sym_db.RegisterMessage(SnapshotPredictImagesRequest)

PcamSnapshot = _reflection.GeneratedProtocolMessageType('PcamSnapshot', (_message.Message,), {
  'DESCRIPTOR' : _PCAMSNAPSHOT,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.PcamSnapshot)
  })
_sym_db.RegisterMessage(PcamSnapshot)

SnapshotPredictImagesResponse = _reflection.GeneratedProtocolMessageType('SnapshotPredictImagesResponse', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPREDICTIMAGESRESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.SnapshotPredictImagesResponse)
  })
_sym_db.RegisterMessage(SnapshotPredictImagesResponse)

GetChipForPredictImageRequest = _reflection.GeneratedProtocolMessageType('GetChipForPredictImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCHIPFORPREDICTIMAGEREQUEST,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetChipForPredictImageRequest)
  })
_sym_db.RegisterMessage(GetChipForPredictImageRequest)

GetChipForPredictImageResponse = _reflection.GeneratedProtocolMessageType('GetChipForPredictImageResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCHIPFORPREDICTIMAGERESPONSE,
  '__module__' : 'cv.runtime.proto.cv_runtime_pb2'
  # @@protoc_insertion_point(class_scope:cv.runtime.proto.GetChipForPredictImageResponse)
  })
_sym_db.RegisterMessage(GetChipForPredictImageResponse)


DESCRIPTOR._options = None
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY._options = None
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY._options = None
_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY._options = None
_NODETIMING_STATETIMINGSENTRY._options = None
_GETLATESTP2PIMAGEREQUEST.fields_by_name['score_type']._options = None
_GETREADYRESPONSE_DEEPWEEDREADYSTATEENTRY._options = None
_GETREADYRESPONSE_P2PREADYSTATEENTRY._options = None
_STARTP2PBUFFERRINGREQUEST._options = None
_STARTP2PBUFFERRINGRESPONSE._options = None
_STOPP2PBUFFERRINGREQUEST._options = None
_STOPP2PBUFFERRINGRESPONSE._options = None

_CVRUNTIMESERVICE = _descriptor.ServiceDescriptor(
  name='CVRuntimeService',
  full_name='cv.runtime.proto.CVRuntimeService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=14474,
  serialized_end=20416,
  methods=[
  _descriptor.MethodDescriptor(
    name='SetP2PContext',
    full_name='cv.runtime.proto.CVRuntimeService.SetP2PContext',
    index=0,
    containing_service=None,
    input_type=_SETP2PCONTEXTREQUEST,
    output_type=_SETP2PCONTEXTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCameraDimensions',
    full_name='cv.runtime.proto.CVRuntimeService.GetCameraDimensions',
    index=1,
    containing_service=None,
    input_type=_GETCAMERADIMENSIONSREQUEST,
    output_type=_GETCAMERADIMENSIONSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCameraInfo',
    full_name='cv.runtime.proto.CVRuntimeService.GetCameraInfo',
    index=2,
    containing_service=None,
    input_type=_GETCAMERAINFOREQUEST,
    output_type=_GETCAMERAINFORESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDeepweedIndexToCategory',
    full_name='cv.runtime.proto.CVRuntimeService.GetDeepweedIndexToCategory',
    index=3,
    containing_service=None,
    input_type=_GETDEEPWEEDINDEXTOCATEGORYREQUEST,
    output_type=_GETDEEPWEEDINDEXTOCATEGORYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetDeepweedDetectionCriteria',
    full_name='cv.runtime.proto.CVRuntimeService.SetDeepweedDetectionCriteria',
    index=4,
    containing_service=None,
    input_type=_SETDEEPWEEDDETECTIONCRITERIAREQUEST,
    output_type=_SETDEEPWEEDDETECTIONCRITERIARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDeepweedDetectionCriteria',
    full_name='cv.runtime.proto.CVRuntimeService.GetDeepweedDetectionCriteria',
    index=5,
    containing_service=None,
    input_type=_GETDEEPWEEDDETECTIONCRITERIAREQUEST,
    output_type=_GETDEEPWEEDDETECTIONCRITERIARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDeepweedSupportedCategories',
    full_name='cv.runtime.proto.CVRuntimeService.GetDeepweedSupportedCategories',
    index=6,
    containing_service=None,
    input_type=_GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST,
    output_type=_GETDEEPWEEDSUPPORTEDCATEGORIESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCameraTemperatures',
    full_name='cv.runtime.proto.CVRuntimeService.GetCameraTemperatures',
    index=7,
    containing_service=None,
    input_type=_GETCAMERATEMPERATURESREQUEST,
    output_type=_GETCAMERATEMPERATURESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetCameraSettings',
    full_name='cv.runtime.proto.CVRuntimeService.SetCameraSettings',
    index=8,
    containing_service=None,
    input_type=_SETCAMERASETTINGSREQUEST,
    output_type=_SETCAMERASETTINGSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCameraSettings',
    full_name='cv.runtime.proto.CVRuntimeService.GetCameraSettings',
    index=9,
    containing_service=None,
    input_type=_GETCAMERASETTINGSREQUEST,
    output_type=_GETCAMERASETTINGSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartBurstRecordFrames',
    full_name='cv.runtime.proto.CVRuntimeService.StartBurstRecordFrames',
    index=10,
    containing_service=None,
    input_type=_STARTBURSTRECORDFRAMESREQUEST,
    output_type=_STARTBURSTRECORDFRAMESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopBurstRecordFrames',
    full_name='cv.runtime.proto.CVRuntimeService.StopBurstRecordFrames',
    index=11,
    containing_service=None,
    input_type=_STOPBURSTRECORDFRAMESREQUEST,
    output_type=_STOPBURSTRECORDFRAMESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetConnectors',
    full_name='cv.runtime.proto.CVRuntimeService.GetConnectors',
    index=12,
    containing_service=None,
    input_type=_GETCONNECTORSREQUEST,
    output_type=_GETCONNECTORSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetConnectors',
    full_name='cv.runtime.proto.CVRuntimeService.SetConnectors',
    index=13,
    containing_service=None,
    input_type=_SETCONNECTORSREQUEST,
    output_type=_SETCONNECTORSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTiming',
    full_name='cv.runtime.proto.CVRuntimeService.GetTiming',
    index=14,
    containing_service=None,
    input_type=_GETTIMINGREQUEST,
    output_type=_GETTIMINGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Predict',
    full_name='cv.runtime.proto.CVRuntimeService.Predict',
    index=15,
    containing_service=None,
    input_type=_PREDICTREQUEST,
    output_type=_PREDICTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LoadAndQueue',
    full_name='cv.runtime.proto.CVRuntimeService.LoadAndQueue',
    index=16,
    containing_service=None,
    input_type=_LOADANDQUEUEREQUEST,
    output_type=_LOADANDQUEUERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetImage',
    full_name='cv.runtime.proto.CVRuntimeService.SetImage',
    index=17,
    containing_service=None,
    input_type=_SETIMAGEREQUEST,
    output_type=_SETIMAGERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UnsetImage',
    full_name='cv.runtime.proto.CVRuntimeService.UnsetImage',
    index=18,
    containing_service=None,
    input_type=_UNSETIMAGEREQUEST,
    output_type=_UNSETIMAGERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetModelPaths',
    full_name='cv.runtime.proto.CVRuntimeService.GetModelPaths',
    index=19,
    containing_service=None,
    input_type=_GETMODELPATHSREQUEST,
    output_type=_GETMODELPATHSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetGPSLocation',
    full_name='cv.runtime.proto.CVRuntimeService.SetGPSLocation',
    index=20,
    containing_service=None,
    input_type=_SETGPSLOCATIONREQUEST,
    output_type=_SETGPSLOCATIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetImplementStatus',
    full_name='cv.runtime.proto.CVRuntimeService.SetImplementStatus',
    index=21,
    containing_service=None,
    input_type=_SETIMPLEMENTSTATUSREQUEST,
    output_type=_SETIMPLEMENTSTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetImageScore',
    full_name='cv.runtime.proto.CVRuntimeService.SetImageScore',
    index=22,
    containing_service=None,
    input_type=_SETIMAGESCOREREQUEST,
    output_type=_SETIMAGESCORERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetScoreQueue',
    full_name='cv.runtime.proto.CVRuntimeService.GetScoreQueue',
    index=23,
    containing_service=None,
    input_type=_GETSCOREQUEUEREQUEST,
    output_type=_GETSCOREQUEUERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ListScoreQueues',
    full_name='cv.runtime.proto.CVRuntimeService.ListScoreQueues',
    index=24,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_LISTSCOREQUEUESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetMaxImageScore',
    full_name='cv.runtime.proto.CVRuntimeService.GetMaxImageScore',
    index=25,
    containing_service=None,
    input_type=_GETMAXIMAGESCOREREQUEST,
    output_type=_GETMAXIMAGESCORERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetMaxScoredImage',
    full_name='cv.runtime.proto.CVRuntimeService.GetMaxScoredImage',
    index=26,
    containing_service=None,
    input_type=_GETMAXSCOREDIMAGEREQUEST,
    output_type=_IMAGEANDMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetLatestP2PImage',
    full_name='cv.runtime.proto.CVRuntimeService.GetLatestP2PImage',
    index=27,
    containing_service=None,
    input_type=_GETLATESTP2PIMAGEREQUEST,
    output_type=_P2PIMAGEANDMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetChipImage',
    full_name='cv.runtime.proto.CVRuntimeService.GetChipImage',
    index=28,
    containing_service=None,
    input_type=_GETCHIPIMAGEREQUEST,
    output_type=_CHIPIMAGEANDMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetChipQueueInformation',
    full_name='cv.runtime.proto.CVRuntimeService.GetChipQueueInformation',
    index=29,
    containing_service=None,
    input_type=_CHIPQUEUEINFORMATIONREQUEST,
    output_type=_CHIPQUEUEINFORMATIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FlushQueues',
    full_name='cv.runtime.proto.CVRuntimeService.FlushQueues',
    index=30,
    containing_service=None,
    input_type=_FLUSHQUEUESREQUEST,
    output_type=_FLUSHQUEUESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetLatestImage',
    full_name='cv.runtime.proto.CVRuntimeService.GetLatestImage',
    index=31,
    containing_service=None,
    input_type=_GETLATESTIMAGEREQUEST,
    output_type=_IMAGEANDMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetImageNearTimestamp',
    full_name='cv.runtime.proto.CVRuntimeService.GetImageNearTimestamp',
    index=32,
    containing_service=None,
    input_type=_GETIMAGENEARTIMESTAMPREQUEST,
    output_type=_IMAGEANDMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetLightweightBurstRecord',
    full_name='cv.runtime.proto.CVRuntimeService.GetLightweightBurstRecord',
    index=33,
    containing_service=None,
    input_type=_GETLIGHTWEIGHTBURSTRECORDREQUEST,
    output_type=_GETLIGHTWEIGHTBURSTRECORDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetBooted',
    full_name='cv.runtime.proto.CVRuntimeService.GetBooted',
    index=34,
    containing_service=None,
    input_type=_GETBOOTEDREQUEST,
    output_type=_GETBOOTEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetReady',
    full_name='cv.runtime.proto.CVRuntimeService.GetReady',
    index=35,
    containing_service=None,
    input_type=_GETREADYREQUEST,
    output_type=_GETREADYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDeepweedOutputByTimestamp',
    full_name='cv.runtime.proto.CVRuntimeService.GetDeepweedOutputByTimestamp',
    index=36,
    containing_service=None,
    input_type=_GETDEEPWEEDOUTPUTBYTIMESTAMPREQUEST,
    output_type=_DEEPWEEDOUTPUT,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRecommendedStrobeSettings',
    full_name='cv.runtime.proto.CVRuntimeService.GetRecommendedStrobeSettings',
    index=37,
    containing_service=None,
    input_type=_GETRECOMMENDEDSTROBESETTINGSREQUEST,
    output_type=_GETRECOMMENDEDSTROBESETTINGSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='P2PCapture',
    full_name='cv.runtime.proto.CVRuntimeService.P2PCapture',
    index=38,
    containing_service=None,
    input_type=_P2PCAPTUREREQUEST,
    output_type=_P2PCAPTURERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetAutoWhitebalance',
    full_name='cv.runtime.proto.CVRuntimeService.SetAutoWhitebalance',
    index=39,
    containing_service=None,
    input_type=_SETAUTOWHITEBALANCEREQUEST,
    output_type=_SETAUTOWHITEBALANCERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextDeepweedOutput',
    full_name='cv.runtime.proto.CVRuntimeService.GetNextDeepweedOutput',
    index=40,
    containing_service=None,
    input_type=_GETNEXTDEEPWEEDOUTPUTREQUEST,
    output_type=_DEEPWEEDOUTPUT,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextP2POutput',
    full_name='cv.runtime.proto.CVRuntimeService.GetNextP2POutput',
    index=41,
    containing_service=None,
    input_type=_GETNEXTP2POUTPUTREQUEST,
    output_type=_P2POUTPUTPROTO,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetTargetingState',
    full_name='cv.runtime.proto.CVRuntimeService.SetTargetingState',
    index=42,
    containing_service=None,
    input_type=_SETTARGETINGSTATEREQUEST,
    output_type=_SETTARGETINGSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='P2PBufferringBurstCapture',
    full_name='cv.runtime.proto.CVRuntimeService.P2PBufferringBurstCapture',
    index=43,
    containing_service=None,
    input_type=_P2PBUFFERRINGBURSTCAPTUREREQUEST,
    output_type=_P2PBUFFERRINGBURSTCAPTURERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextFocusMetric',
    full_name='cv.runtime.proto.CVRuntimeService.GetNextFocusMetric',
    index=44,
    containing_service=None,
    input_type=_GETNEXTFOCUSMETRICREQUEST,
    output_type=_GETNEXTFOCUSMETRICRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='RemoveDataDir',
    full_name='cv.runtime.proto.CVRuntimeService.RemoveDataDir',
    index=45,
    containing_service=None,
    input_type=_REMOVEDATADIRREQUEST,
    output_type=_REMOVEDATADIRRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetLastNImages',
    full_name='cv.runtime.proto.CVRuntimeService.GetLastNImages',
    index=46,
    containing_service=None,
    input_type=_LASTNIMAGEREQUEST,
    output_type=_IMAGEANDMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetComputeCapabilities',
    full_name='cv.runtime.proto.CVRuntimeService.GetComputeCapabilities',
    index=47,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_COMPUTECAPABILITIESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSupportedTensorRTVersions',
    full_name='cv.runtime.proto.CVRuntimeService.GetSupportedTensorRTVersions',
    index=48,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_SUPPORTEDTENSORRTVERSIONSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReloadCategoryCollection',
    full_name='cv.runtime.proto.CVRuntimeService.ReloadCategoryCollection',
    index=49,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCategoryCollection',
    full_name='cv.runtime.proto.CVRuntimeService.GetCategoryCollection',
    index=50,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_GETCATEGORYCOLLECTIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetErrorState',
    full_name='cv.runtime.proto.CVRuntimeService.GetErrorState',
    index=51,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_GETERRORSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SnapshotPredictImages',
    full_name='cv.runtime.proto.CVRuntimeService.SnapshotPredictImages',
    index=52,
    containing_service=None,
    input_type=_SNAPSHOTPREDICTIMAGESREQUEST,
    output_type=_SNAPSHOTPREDICTIMAGESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetChipForPredictImage',
    full_name='cv.runtime.proto.CVRuntimeService.GetChipForPredictImage',
    index=53,
    containing_service=None,
    input_type=_GETCHIPFORPREDICTIMAGEREQUEST,
    output_type=_GETCHIPFORPREDICTIMAGERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CVRUNTIMESERVICE)

DESCRIPTOR.services_by_name['CVRuntimeService'] = _CVRUNTIMESERVICE

# @@protoc_insertion_point(module_scope)
