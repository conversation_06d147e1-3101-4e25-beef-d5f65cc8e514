from typing import Any, Iterator

import numpy as np
import torch


# For if we ever want to use custom samplers
class BaseSampler(torch.utils.data.Sampler[int]):
    def __init__(self, dataset: torch.utils.data.Dataset[Any], num_samples: int, seed: int = 1000,) -> None:
        super().__init__()
        self._dataset = dataset
        self._num_samples = num_samples
        self._rng = np.random.RandomState(seed)

    @property
    def num_samples(self) -> int:
        return self._num_samples

    def __len__(self) -> int:
        return self._num_samples

    def __iter__(self) -> Iterator[int]:
        for _ in range(self._num_samples):
            yield self._rng.randint(0, len(self._dataset))  # type: ignore
