// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/weeding_diagnostics.proto

#include "frontend/proto/weeding_diagnostics.pb.h"
#include "frontend/proto/weeding_diagnostics.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace weeding_diagnostics {

static const char* WeedingDiagnosticsService_method_names[] = {
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/RecordWeedingDiagnostics",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetCurrentTrajectories",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRecordingsList",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/OpenRecording",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetSnapshot",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/DeleteRecording",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryData",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryPredictImage",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryTargetImage",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImageMetadata",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImage",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/StartUpload",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetNextUploadState",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictionsCount",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictions",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/FindTrajectory",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRotaryTicks",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/SnapshotPredictImages",
  "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetChipForPredictImage",
};

std::unique_ptr< WeedingDiagnosticsService::Stub> WeedingDiagnosticsService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< WeedingDiagnosticsService::Stub> stub(new WeedingDiagnosticsService::Stub(channel, options));
  return stub;
}

WeedingDiagnosticsService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_RecordWeedingDiagnostics_(WeedingDiagnosticsService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCurrentTrajectories_(WeedingDiagnosticsService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetRecordingsList_(WeedingDiagnosticsService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_OpenRecording_(WeedingDiagnosticsService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSnapshot_(WeedingDiagnosticsService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteRecording_(WeedingDiagnosticsService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTrajectoryData_(WeedingDiagnosticsService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTrajectoryPredictImage_(WeedingDiagnosticsService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_GetTrajectoryTargetImage_(WeedingDiagnosticsService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_GetPredictImageMetadata_(WeedingDiagnosticsService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetPredictImage_(WeedingDiagnosticsService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_StartUpload_(WeedingDiagnosticsService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextUploadState_(WeedingDiagnosticsService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDeepweedPredictionsCount_(WeedingDiagnosticsService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDeepweedPredictions_(WeedingDiagnosticsService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_FindTrajectory_(WeedingDiagnosticsService_method_names[15], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetRotaryTicks_(WeedingDiagnosticsService_method_names[16], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SnapshotPredictImages_(WeedingDiagnosticsService_method_names[17], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetChipForPredictImage_(WeedingDiagnosticsService_method_names[18], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status WeedingDiagnosticsService::Stub::RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_RecordWeedingDiagnostics_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RecordWeedingDiagnostics_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RecordWeedingDiagnostics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* WeedingDiagnosticsService::Stub::PrepareAsyncRecordWeedingDiagnosticsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_RecordWeedingDiagnostics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* WeedingDiagnosticsService::Stub::AsyncRecordWeedingDiagnosticsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncRecordWeedingDiagnosticsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::weed_tracking::DiagnosticsSnapshot* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::weed_tracking::DiagnosticsSnapshot, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCurrentTrajectories_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::weed_tracking::DiagnosticsSnapshot, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCurrentTrajectories_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCurrentTrajectories_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>* WeedingDiagnosticsService::Stub::PrepareAsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::DiagnosticsSnapshot, ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCurrentTrajectories_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>* WeedingDiagnosticsService::Stub::AsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCurrentTrajectoriesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRecordingsList_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRecordingsList_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRecordingsList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncGetRecordingsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse, ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRecordingsList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* WeedingDiagnosticsService::Stub::AsyncGetRecordingsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRecordingsListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_OpenRecording_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_OpenRecording_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_OpenRecording_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncOpenRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse, ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_OpenRecording_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* WeedingDiagnosticsService::Stub::AsyncOpenRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncOpenRecordingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSnapshot_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSnapshot_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSnapshot_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncGetSnapshotRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse, ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSnapshot_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* WeedingDiagnosticsService::Stub::AsyncGetSnapshotRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSnapshotRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteRecording_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteRecording_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteRecording_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* WeedingDiagnosticsService::Stub::PrepareAsyncDeleteRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteRecording_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* WeedingDiagnosticsService::Stub::AsyncDeleteRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteRecordingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::carbon::frontend::weeding_diagnostics::TrajectoryData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTrajectoryData_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::carbon::frontend::weeding_diagnostics::TrajectoryData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTrajectoryData_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTrajectoryData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::TrajectoryData>* WeedingDiagnosticsService::Stub::PrepareAsyncGetTrajectoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::TrajectoryData, ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTrajectoryData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::TrajectoryData>* WeedingDiagnosticsService::Stub::AsyncGetTrajectoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTrajectoryDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::GetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), rpcmethod_GetTrajectoryPredictImage_, context, request);
}

void WeedingDiagnosticsService::Stub::async::GetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) {
  ::grpc::internal::ClientCallbackReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(stub_->channel_.get(), stub_->rpcmethod_GetTrajectoryPredictImage_, context, request, reactor);
}

::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::AsyncGetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), cq, rpcmethod_GetTrajectoryPredictImage_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::PrepareAsyncGetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), cq, rpcmethod_GetTrajectoryPredictImage_, context, request, false, nullptr);
}

::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::GetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), rpcmethod_GetTrajectoryTargetImage_, context, request);
}

void WeedingDiagnosticsService::Stub::async::GetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) {
  ::grpc::internal::ClientCallbackReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(stub_->channel_.get(), stub_->rpcmethod_GetTrajectoryTargetImage_, context, request, reactor);
}

::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::AsyncGetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), cq, rpcmethod_GetTrajectoryTargetImage_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::PrepareAsyncGetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), cq, rpcmethod_GetTrajectoryTargetImage_, context, request, false, nullptr);
}

::grpc::Status WeedingDiagnosticsService::Stub::GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetPredictImageMetadata_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPredictImageMetadata_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPredictImageMetadata_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncGetPredictImageMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetPredictImageMetadata_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* WeedingDiagnosticsService::Stub::AsyncGetPredictImageMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetPredictImageMetadataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::GetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), rpcmethod_GetPredictImage_, context, request);
}

void WeedingDiagnosticsService::Stub::async::GetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) {
  ::grpc::internal::ClientCallbackReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(stub_->channel_.get(), stub_->rpcmethod_GetPredictImage_, context, request, reactor);
}

::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::AsyncGetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), cq, rpcmethod_GetPredictImage_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* WeedingDiagnosticsService::Stub::PrepareAsyncGetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::frontend::weeding_diagnostics::ImageChunk>::Create(channel_.get(), cq, rpcmethod_GetPredictImage_, context, request, false, nullptr);
}

::grpc::Status WeedingDiagnosticsService::Stub::StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartUpload_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartUpload_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* WeedingDiagnosticsService::Stub::PrepareAsyncStartUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* WeedingDiagnosticsService::Stub::AsyncStartUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextUploadState_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextUploadState_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextUploadState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncGetNextUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextUploadState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* WeedingDiagnosticsService::Stub::AsyncGetNextUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextUploadStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDeepweedPredictionsCount_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedPredictionsCount_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedPredictionsCount_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncGetDeepweedPredictionsCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDeepweedPredictionsCount_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* WeedingDiagnosticsService::Stub::AsyncGetDeepweedPredictionsCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDeepweedPredictionsCountRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDeepweedPredictions_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedPredictions_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeepweedPredictions_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncGetDeepweedPredictionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDeepweedPredictions_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* WeedingDiagnosticsService::Stub::AsyncGetDeepweedPredictionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDeepweedPredictionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_FindTrajectory_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FindTrajectory_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FindTrajectory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncFindTrajectoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse, ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_FindTrajectory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* WeedingDiagnosticsService::Stub::AsyncFindTrajectoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncFindTrajectoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRotaryTicks_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRotaryTicks_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRotaryTicks_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncGetRotaryTicksRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRotaryTicks_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* WeedingDiagnosticsService::Stub::AsyncGetRotaryTicksRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRotaryTicksRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SnapshotPredictImages_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SnapshotPredictImages_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SnapshotPredictImages_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncSnapshotPredictImagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SnapshotPredictImages_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* WeedingDiagnosticsService::Stub::AsyncSnapshotPredictImagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSnapshotPredictImagesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedingDiagnosticsService::Stub::GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetChipForPredictImage_, context, request, response);
}

void WeedingDiagnosticsService::Stub::async::GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipForPredictImage_, context, request, response, std::move(f));
}

void WeedingDiagnosticsService::Stub::async::GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipForPredictImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* WeedingDiagnosticsService::Stub::PrepareAsyncGetChipForPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetChipForPredictImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* WeedingDiagnosticsService::Stub::AsyncGetChipForPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetChipForPredictImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

WeedingDiagnosticsService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->RecordWeedingDiagnostics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::weed_tracking::DiagnosticsSnapshot, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* req,
             ::weed_tracking::DiagnosticsSnapshot* resp) {
               return service->GetCurrentTrajectories(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* req,
             ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* resp) {
               return service->GetRecordingsList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* req,
             ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* resp) {
               return service->OpenRecording(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* req,
             ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* resp) {
               return service->GetSnapshot(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DeleteRecording(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::carbon::frontend::weeding_diagnostics::TrajectoryData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* req,
             ::carbon::frontend::weeding_diagnostics::TrajectoryData* resp) {
               return service->GetTrajectoryData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[7],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* req,
             ::grpc::ServerWriter<::carbon::frontend::weeding_diagnostics::ImageChunk>* writer) {
               return service->GetTrajectoryPredictImage(ctx, req, writer);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[8],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* req,
             ::grpc::ServerWriter<::carbon::frontend::weeding_diagnostics::ImageChunk>* writer) {
               return service->GetTrajectoryTargetImage(ctx, req, writer);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* req,
             ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* resp) {
               return service->GetPredictImageMetadata(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[10],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* req,
             ::grpc::ServerWriter<::carbon::frontend::weeding_diagnostics::ImageChunk>* writer) {
               return service->GetPredictImage(ctx, req, writer);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* req,
             ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* resp) {
               return service->GetNextUploadState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* req,
             ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* resp) {
               return service->GetDeepweedPredictionsCount(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* req,
             ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* resp) {
               return service->GetDeepweedPredictions(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* req,
             ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* resp) {
               return service->FindTrajectory(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[16],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* req,
             ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* resp) {
               return service->GetRotaryTicks(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[17],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* req,
             ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* resp) {
               return service->SnapshotPredictImages(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedingDiagnosticsService_method_names[18],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedingDiagnosticsService::Service, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedingDiagnosticsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* req,
             ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* resp) {
               return service->GetChipForPredictImage(ctx, req, resp);
             }, this)));
}

WeedingDiagnosticsService::Service::~Service() {
}

::grpc::Status WeedingDiagnosticsService::Service::RecordWeedingDiagnostics(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetCurrentTrajectories(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetRecordingsList(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::OpenRecording(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetSnapshot(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::DeleteRecording(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetTrajectoryData(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetTrajectoryPredictImage(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* request, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetTrajectoryTargetImage(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* request, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetPredictImageMetadata(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetPredictImage(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* request, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::StartUpload(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetNextUploadState(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetDeepweedPredictionsCount(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetDeepweedPredictions(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::FindTrajectory(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetRotaryTicks(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::SnapshotPredictImages(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedingDiagnosticsService::Service::GetChipForPredictImage(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace weeding_diagnostics

