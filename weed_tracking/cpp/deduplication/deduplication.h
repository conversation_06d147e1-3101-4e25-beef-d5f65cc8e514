#pragma once

#include <cmath>
#include <nanoflann.hpp>
#include <optional>
#include <set>
#include <spdlog/spdlog.h>
#include <tuple>

#include <lib/common/cpp/exceptions.h>
#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <trajectory/cpp/trajectory.hpp>
#include <weed_tracking/cpp/tracking/tracker.h>
#include <generated/recorder/proto/recorder.pb.h>

constexpr float kPixelsPerMM = (float)(200 / 25.4);

namespace carbon {

namespace kd_tree {

template <class IndexTypeT, class ItemTypeT, int DIM = 2, class Distance = nanoflann::metric_L2>
class KDTreeAdaptorBase {
public:
  typedef KDTreeAdaptorBase<IndexTypeT, ItemTypeT, DIM, Distance> self_t;
  typedef typename Distance::template traits<float, self_t>::distance_t metric_t;
  typedef nanoflann::KDTreeSingleIndexAdaptor<metric_t, self_t, DIM> index_t;
  KDTreeAdaptorBase(const std::vector<std::pair<IndexTypeT, ItemTypeT>> &items, const size_t leaf_max_size = 10)
      : items_(items), index_(new index_t(2, *this, nanoflann::KDTreeSingleIndexAdaptorParams(leaf_max_size))) {
    index_->buildIndex();
  }

  inline size_t kdtree_get_point_count() const { return items_.size(); }

  inline float kdtree_get_pt(const size_t idx, const size_t dim) const {
    if (dim == 0) {
      return items_[idx].second.get_x();
    } else if (dim == 1) {
      return items_[idx].second.get_y();
    } else {
      throw maka_error("unsupported");
    }
  }

  template <class BBOX>
  bool kdtree_get_bbox(BBOX &) const {
    return false;
  }

  virtual inline size_t query_radius(float query_point_x, float query_point_y, float radius,
                                     std::vector<std::pair<size_t, float>> &out_points) const {
    std::vector<float> query_point = {query_point_x, query_point_y};
    return index_->radiusSearch(query_point.data(), radius, out_points, nanoflann::SearchParams());
  }

  virtual inline size_t query_nn(float query_point_x, float query_point_y, int num_items,
                                 std::vector<size_t> &out_indices, std::vector<float> &out_dist_sqr) const {
    std::vector<float> query_point = {query_point_x, query_point_y};
    return index_->knnSearch(query_point.data(), num_items, &out_indices[0], &out_dist_sqr[0]);
  }

protected:
  std::vector<std::pair<IndexTypeT, ItemTypeT>> items_;
  std::unique_ptr<index_t> index_;
};

template class KDTreeAdaptorBase<size_t, carbon::trajectory::TrackedItemCentroid>;
template class KDTreeAdaptorBase<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>;
} // namespace kd_tree

namespace weed_tracking {

constexpr size_t max_neighbor_testing = 3; // cap nearest neighbor search to 3
using PType = std::pair<size_t, float>;
struct NN_data {
  size_t index;
  std::vector<PType> neighbors;
  NN_data() : index(0) {}
  NN_data(const NN_data &rhs) : index(rhs.index), neighbors(rhs.neighbors) {}
  void sort() {
    std::sort(neighbors.begin(), neighbors.end(),
              [](const PType &lhs, const PType &rhs) { return lhs.second < rhs.second; });
  }
  inline bool valid() const { return index < neighbors.size(); }
  const PType &get() const { return neighbors[index]; }
  float get_dist(float def) const {
    if (valid()) {
      return neighbors[index].second;
    }
    return def;
  }
  NN_data &operator++() {
    ++index;
    return *this;
  }
  NN_data &operator--() {
    --index;
    return *this;
  }
};

struct CandidateMatch {
  double error;
  float shift_x;
  float shift_y;
  size_t centroid_id;
  trajectory::Trajectory::ID trajectory_id;
  std::vector<NN_data> matches;
  CandidateMatch(size_t track_size, float _shift_x, float _shift_y, size_t _centroid_id, trajectory::Trajectory::ID _trajectory_id)
      : error(0.0), shift_x(_shift_x), shift_y(_shift_y), centroid_id(_centroid_id), trajectory_id(_trajectory_id), matches(track_size) {}
};

// Even though the distance metric is L2 here. The actual distance returned is the squared L2 norm.
template <int DIM = 2, class Distance = nanoflann::metric_L2>
class KDTreeAdaptor : public carbon::kd_tree::KDTreeAdaptorBase<size_t, trajectory::TrackedItemCentroid> {
public:
  typedef KDTreeAdaptor<DIM, Distance> self_t;
  typedef typename Distance::template traits<float, self_t>::distance_t metric_t;
  typedef nanoflann::KDTreeSingleIndexAdaptor<metric_t, self_t, DIM> index_t;

  KDTreeAdaptor(const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &centroids,
                const size_t leaf_max_size = 10)
      : KDTreeAdaptorBase<size_t, trajectory::TrackedItemCentroid>(centroids, leaf_max_size) {}
};

void undistort_model_centroids(
    std::vector<trajectory::TrackedItemCentroid> &model_centroids, torch::Tensor d2u_mapx, torch::Tensor d2u_mapy,
    std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &plant_centroids_with_keys);

std::tuple<std::map<uint32_t, std::pair<size_t, trajectory::TrackedItemCentroid>>,
           std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>>,
           std::map<uint32_t, trajectory::TrackedItemCentroid>, bool>
deduplicate_ransac(Tracker *tracker, std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam,
                   int64_t timestamp_ms, float plant_dedup_radius,
                   const KDTreeAdaptor<2, nanoflann::metric_L2> &kd_tree_plant_centroids,
                   const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &plant_centroids_with_keys,
                   std::vector<trajectory::Trajectory::ID> tracked_plant_ids,
                   std::vector<std::optional<std::tuple<float, float>>> &tracked_plant_xy,
                   ::recorder::CandidateShift &best_candidate_match,
                   std::vector<::recorder::CandidateShift> &all_candidate_matches,
                   std::map<size_t, std::optional<std::pair<bool, ::recorder::TrackedItemCoordinates>>> &tracked_items_coordinates
                  );

void calc_final_matches(CandidateMatch *candidate, size_t max_matches, double dedup_radius);
void calc_final_matches_recurse(CandidateMatch *candidate, std::unordered_map<size_t, size_t> &model_to_track,
                                size_t i);

template <typename IndexTypeT, typename ItemTypeT>
void shift_all_tracked_objects_by_vector_and_get_distances(
    CandidateMatch *candidate, const std::vector<std::optional<std::tuple<float, float>>> &tracked_items_xy,
    const carbon::kd_tree::KDTreeAdaptorBase<IndexTypeT, ItemTypeT> &kd_tree_centroids, size_t max_matches,
    double dedup_radius);


class OptimalMatcher {
  public:
    OptimalMatcher(const std::vector<std::optional<std::tuple<float, float>>> &tracked_items_xy,
                   const std::vector<trajectory::Trajectory::ID> &tracked_ids, Tracker *tracker,
                   const KDTreeAdaptor<2, nanoflann::metric_L2> &kd_tree_centroids,
                   const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &centroids_with_keys,
                   float dedup_radius);
    void find_optimal_matches();
    bool valid() const;
    const CandidateMatch &best() const;
    const std::forward_list<CandidateMatch> &get_options() const { return options_; }

  private:
    const std::vector<std::optional<std::tuple<float, float>>> &tracked_items_xy_;
    const std::vector<trajectory::Trajectory::ID> &tracked_ids_;
    std::forward_list<CandidateMatch> options_;
    Tracker *tracker_;
    const KDTreeAdaptor<2, nanoflann::metric_L2> &kd_tree_centroids_;
    const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &centroids_with_keys_;
    std::forward_list<CandidateMatch>::iterator best_;
    float dedup_radius_;
    float dedup_radius_sqrd_;
    bool valid_;
    size_t max_matches_;
};
} // namespace weed_tracking
} // namespace carbon
