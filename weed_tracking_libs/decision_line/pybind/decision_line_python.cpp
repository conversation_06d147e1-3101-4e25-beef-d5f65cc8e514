#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include <weed_tracking_libs/decision_line/cpp/decision_line.hpp>

#include <spdlog/spdlog.h>

namespace py = pybind11;

namespace carbon::decision_line {

void init_scanner_decision_line(float middle_of_predict_space_mm) {
  decision_line::DecisionLine::set(std::make_shared<decision_line::ScannerDecisionLine>(middle_of_predict_space_mm));
  spdlog::info("Decisionline set to ScannerDecisionLine");
}

PYBIND11_MODULE(decision_line_python, m) {
  m.def("init_scanner_decision_line", &init_scanner_decision_line, py::arg("middle_of_predict_space_mm"),
        py::call_guard<py::gil_scoped_release>());
}

} // namespace carbon::decision_line