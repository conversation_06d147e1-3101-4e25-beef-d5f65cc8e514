#pragma once

#include <c10/cuda/CUDAGuard.h>

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/distance_status.h"
#include "lib/common/cpp/fixed_buffer.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class DistanceBuffer : public CVNodeImpl {
public:
  DistanceBuffer(camera::Camera camera, Input<camera::CameraImage> input, int max_items, std::optional<int> gpu_id,
                 float distance_interval_mm, std::shared_ptr<DistanceStatus> distance_status, bool simulator)
      : CVNodeImpl(fmt::format("/distance_buffer/{}", camera.get_info().camera_id)), input_(input), gpu_id_(gpu_id),
        buffer_(new FixedBuffer<int64_t, camera::CameraImage>((size_t)max_items)),
        snapshot_buffer_(new FixedBuffer<int64_t, camera::CameraImage>((size_t)max_items)), last_distance_(0.0),
        distance_interval_mm_(distance_interval_mm), distance_status_(distance_status), simulator_(simulator),
        last_snapshot_distance_(0.0) {}

  std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>> get_buffer() const { return buffer_; }
  std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>> get_snapshot_buffer() const { return snapshot_buffer_; }
  Output<camera::CameraImage> &get_output() { return output_; }

  std::vector<std::pair<std::string, int64_t>> snapshot_and_clear() {
    snapshot_buffer_->clear();
    std::vector<std::pair<std::string, int64_t>> snapshots;

    buffer_->apply([&](const int64_t &timestamp, const camera::CameraImage &image) {
      if (image.available_for_snapshot) {
        snapshot_buffer_->push(timestamp, image);
        snapshots.emplace_back(image.camera_id, timestamp);
      }
    });

    return snapshots;
  }

protected:
  int64_t tick() override {
    c10::cuda::OptionalCUDAStreamGuard stream_guard;
    set_state(CVNodeState::kPulling);
    camera::CameraImage cam_image = input_.pop();
    set_state(CVNodeState::kProcessing);
    double distance = distance_status_->retrieve();

    if (!buffer_->contains(cam_image.timestamp_ms) && abs(last_distance_ - distance) > distance_interval_mm_) {
      // Add image to distance buffer
      if (gpu_id_) {
        if (!cuda_stream_) {
          cuda_stream_.reset(
              new c10::cuda::CUDAStream(c10::cuda::getStreamFromPool(true, cam_image.image.device().index())));
        }
        stream_guard.reset_stream(*cuda_stream_);
        cam_image = cam_image.cuda(*gpu_id_);
        cuda_stream_->synchronize();
      }

      // Mark one image as available for snapshot every 10 inches (254 mm)
      if (abs(last_snapshot_distance_ - distance) >= 254.0) {
        cam_image.available_for_snapshot = true;
        last_snapshot_distance_ = distance;
      }

      buffer_->push(cam_image.timestamp_ms, cam_image);
      output_.push(cam_image);
      last_distance_ = distance;
    }

    return cam_image.timestamp_ms;
  }

  void close_connectors() override {
    input_.close();
    output_.close();
  }

private:
  Input<camera::CameraImage> input_;
  Output<camera::CameraImage> output_;

  std::optional<int> gpu_id_;
  std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>> buffer_;
  std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>> snapshot_buffer_;
  std::unique_ptr<c10::cuda::CUDAStream> cuda_stream_;
  double last_distance_;
  double distance_interval_mm_;
  std::shared_ptr<DistanceStatus> distance_status_;
  bool simulator_;
  double last_snapshot_distance_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
