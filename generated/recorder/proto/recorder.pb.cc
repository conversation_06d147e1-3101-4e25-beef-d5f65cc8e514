// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: recorder/proto/recorder.proto

#include "recorder/proto/recorder.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace recorder {
constexpr DetectionClass::DetectionClass(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : class__(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , score_(0){}
struct DetectionClassDefaultTypeInternal {
  constexpr DetectionClassDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DetectionClassDefaultTypeInternal() {}
  union {
    DetectionClass _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DetectionClassDefaultTypeInternal _DetectionClass_default_instance_;
constexpr DeepweedDetection::DeepweedDetection(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : detection_classes_()
  , mask_intersections_()
  , embedding_category_distances_()
  , x_(0)
  , y_(0)
  , size_(0)
  , score_(0)
  , hit_class_(0)

  , trajectory_id_(0u)
  , weed_score_(0)
  , crop_score_(0)
  , plant_score_(0)
  , x_undistorted_(0)
  , y_undistorted_(0){}
struct DeepweedDetectionDefaultTypeInternal {
  constexpr DeepweedDetectionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeepweedDetectionDefaultTypeInternal() {}
  union {
    DeepweedDetection _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeepweedDetectionDefaultTypeInternal _DeepweedDetection_default_instance_;
constexpr TrackedItemCoordinates::TrackedItemCoordinates(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_mm_(0)
  , y_mm_(0)
  , z_mm_(0)
  , x_px_(0)
  , y_px_(0){}
struct TrackedItemCoordinatesDefaultTypeInternal {
  constexpr TrackedItemCoordinatesDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TrackedItemCoordinatesDefaultTypeInternal() {}
  union {
    TrackedItemCoordinates _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TrackedItemCoordinatesDefaultTypeInternal _TrackedItemCoordinates_default_instance_;
constexpr EmbeddingCategory::EmbeddingCategory(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : category_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , distance_(0){}
struct EmbeddingCategoryDefaultTypeInternal {
  constexpr EmbeddingCategoryDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmbeddingCategoryDefaultTypeInternal() {}
  union {
    EmbeddingCategory _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmbeddingCategoryDefaultTypeInternal _EmbeddingCategory_default_instance_;
constexpr TrackedItem::TrackedItem(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : coordinates_before_(nullptr)
  , coordinates_after_(nullptr)
  , trajectory_id_(0u)
  , deduplicated_(false)
  , in_camera_(false){}
struct TrackedItemDefaultTypeInternal {
  constexpr TrackedItemDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TrackedItemDefaultTypeInternal() {}
  union {
    TrackedItem _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TrackedItemDefaultTypeInternal _TrackedItem_default_instance_;
constexpr CandidateShift::CandidateShift(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_shift_(0)
  , y_shift_(0)
  , centroid_id_(0u)
  , trajectory_id_(0u){}
struct CandidateShiftDefaultTypeInternal {
  constexpr CandidateShiftDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CandidateShiftDefaultTypeInternal() {}
  union {
    CandidateShift _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CandidateShiftDefaultTypeInternal _CandidateShift_default_instance_;
constexpr DeepweedPredictionFrame::DeepweedPredictionFrame(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : detections_()
  , embedding_categories_()
  , tracked_items_()
  , candidate_shifts_()
  , best_shift_(nullptr)
  , timestamp_ms_(int64_t{0})
  , plant_matcher_valid_(false){}
struct DeepweedPredictionFrameDefaultTypeInternal {
  constexpr DeepweedPredictionFrameDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeepweedPredictionFrameDefaultTypeInternal() {}
  union {
    DeepweedPredictionFrame _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeepweedPredictionFrameDefaultTypeInternal _DeepweedPredictionFrame_default_instance_;
constexpr DeepweedPredictionRecord::DeepweedPredictionRecord(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : frame_(nullptr)
  , record_timestamp_ms_(uint64_t{0u}){}
struct DeepweedPredictionRecordDefaultTypeInternal {
  constexpr DeepweedPredictionRecordDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeepweedPredictionRecordDefaultTypeInternal() {}
  union {
    DeepweedPredictionRecord _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeepweedPredictionRecordDefaultTypeInternal _DeepweedPredictionRecord_default_instance_;
constexpr LaneHeightSnapshot::LaneHeightSnapshot(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : weed_height_()
  , crop_height_(){}
struct LaneHeightSnapshotDefaultTypeInternal {
  constexpr LaneHeightSnapshotDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaneHeightSnapshotDefaultTypeInternal() {}
  union {
    LaneHeightSnapshot _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaneHeightSnapshotDefaultTypeInternal _LaneHeightSnapshot_default_instance_;
constexpr LaneHeightRecord::LaneHeightRecord(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : snapshot_(nullptr)
  , record_timestamp_ms_(uint64_t{0u}){}
struct LaneHeightRecordDefaultTypeInternal {
  constexpr LaneHeightRecordDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaneHeightRecordDefaultTypeInternal() {}
  union {
    LaneHeightRecord _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaneHeightRecordDefaultTypeInternal _LaneHeightRecord_default_instance_;
constexpr RotaryTicksSnapshot::RotaryTicksSnapshot(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_us_(uint64_t{0u})
  , fl_(0)
  , fr_(0)
  , bl_(0)
  , br_(0)
  , fl_enabled_(false)
  , fr_enabled_(false)
  , bl_enabled_(false)
  , br_enabled_(false){}
struct RotaryTicksSnapshotDefaultTypeInternal {
  constexpr RotaryTicksSnapshotDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RotaryTicksSnapshotDefaultTypeInternal() {}
  union {
    RotaryTicksSnapshot _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RotaryTicksSnapshotDefaultTypeInternal _RotaryTicksSnapshot_default_instance_;
constexpr RotaryTicksRecord::RotaryTicksRecord(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : snapshot_(nullptr)
  , record_timestamp_ms_(uint64_t{0u}){}
struct RotaryTicksRecordDefaultTypeInternal {
  constexpr RotaryTicksRecordDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RotaryTicksRecordDefaultTypeInternal() {}
  union {
    RotaryTicksRecord _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RotaryTicksRecordDefaultTypeInternal _RotaryTicksRecord_default_instance_;
}  // namespace recorder
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_recorder_2fproto_2frecorder_2eproto[12];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_recorder_2fproto_2frecorder_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_recorder_2fproto_2frecorder_2eproto = nullptr;

const uint32_t TableStruct_recorder_2fproto_2frecorder_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::DetectionClass, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::DetectionClass, class__),
  PROTOBUF_FIELD_OFFSET(::recorder::DetectionClass, score_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, x_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, y_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, size_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, score_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, hit_class_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, detection_classes_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, mask_intersections_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, trajectory_id_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, weed_score_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, crop_score_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, plant_score_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, embedding_category_distances_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, x_undistorted_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, y_undistorted_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItemCoordinates, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItemCoordinates, x_mm_),
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItemCoordinates, y_mm_),
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItemCoordinates, z_mm_),
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItemCoordinates, x_px_),
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItemCoordinates, y_px_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::EmbeddingCategory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::EmbeddingCategory, category_),
  PROTOBUF_FIELD_OFFSET(::recorder::EmbeddingCategory, distance_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItem, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItem, trajectory_id_),
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItem, deduplicated_),
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItem, coordinates_before_),
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItem, coordinates_after_),
  PROTOBUF_FIELD_OFFSET(::recorder::TrackedItem, in_camera_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::CandidateShift, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::CandidateShift, x_shift_),
  PROTOBUF_FIELD_OFFSET(::recorder::CandidateShift, y_shift_),
  PROTOBUF_FIELD_OFFSET(::recorder::CandidateShift, centroid_id_),
  PROTOBUF_FIELD_OFFSET(::recorder::CandidateShift, trajectory_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, detections_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, embedding_categories_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, tracked_items_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, best_shift_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, candidate_shifts_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, plant_matcher_valid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionRecord, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionRecord, record_timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionRecord, frame_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightSnapshot, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightSnapshot, weed_height_),
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightSnapshot, crop_height_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightRecord, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightRecord, record_timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightRecord, snapshot_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, timestamp_us_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, fl_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, fr_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, bl_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, br_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, fl_enabled_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, fr_enabled_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, bl_enabled_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, br_enabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksRecord, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksRecord, record_timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksRecord, snapshot_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::recorder::DetectionClass)},
  { 8, -1, -1, sizeof(::recorder::DeepweedDetection)},
  { 28, -1, -1, sizeof(::recorder::TrackedItemCoordinates)},
  { 39, -1, -1, sizeof(::recorder::EmbeddingCategory)},
  { 47, -1, -1, sizeof(::recorder::TrackedItem)},
  { 58, -1, -1, sizeof(::recorder::CandidateShift)},
  { 68, -1, -1, sizeof(::recorder::DeepweedPredictionFrame)},
  { 81, -1, -1, sizeof(::recorder::DeepweedPredictionRecord)},
  { 89, -1, -1, sizeof(::recorder::LaneHeightSnapshot)},
  { 97, -1, -1, sizeof(::recorder::LaneHeightRecord)},
  { 105, -1, -1, sizeof(::recorder::RotaryTicksSnapshot)},
  { 120, -1, -1, sizeof(::recorder::RotaryTicksRecord)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_DetectionClass_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_DeepweedDetection_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_TrackedItemCoordinates_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_EmbeddingCategory_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_TrackedItem_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_CandidateShift_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_DeepweedPredictionFrame_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_DeepweedPredictionRecord_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_LaneHeightSnapshot_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_LaneHeightRecord_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_RotaryTicksSnapshot_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_RotaryTicksRecord_default_instance_),
};

const char descriptor_table_protodef_recorder_2fproto_2frecorder_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035recorder/proto/recorder.proto\022\010recorde"
  "r\".\n\016DetectionClass\022\r\n\005class\030\001 \001(\t\022\r\n\005sc"
  "ore\030\002 \001(\002\"\230\003\n\021DeepweedDetection\022\t\n\001x\030\001 \001"
  "(\002\022\t\n\001y\030\002 \001(\002\022\014\n\004size\030\003 \001(\002\022\r\n\005score\030\004 \001"
  "(\002\0227\n\thit_class\030\005 \001(\0162$.recorder.Deepwee"
  "dDetection.HitClass\0223\n\021detection_classes"
  "\030\006 \003(\0132\030.recorder.DetectionClass\022\032\n\022mask"
  "_intersections\030\007 \003(\014\022\025\n\rtrajectory_id\030\010 "
  "\001(\r\022\022\n\nweed_score\030\t \001(\002\022\022\n\ncrop_score\030\n "
  "\001(\002\022\023\n\013plant_score\030\013 \001(\002\022$\n\034embedding_ca"
  "tegory_distances\030\014 \003(\002\022\025\n\rx_undistorted\030"
  "\r \001(\002\022\025\n\ry_undistorted\030\016 \001(\002\"\036\n\010HitClass"
  "\022\010\n\004WEED\020\000\022\010\n\004CROP\020\001\"^\n\026TrackedItemCoord"
  "inates\022\014\n\004x_mm\030\001 \001(\002\022\014\n\004y_mm\030\002 \001(\002\022\014\n\004z_"
  "mm\030\003 \001(\002\022\014\n\004x_px\030\004 \001(\002\022\014\n\004y_px\030\005 \001(\002\"7\n\021"
  "EmbeddingCategory\022\020\n\010category\030\001 \001(\t\022\020\n\010d"
  "istance\030\002 \001(\002\"\310\001\n\013TrackedItem\022\025\n\rtraject"
  "ory_id\030\001 \001(\r\022\024\n\014deduplicated\030\002 \001(\010\022<\n\022co"
  "ordinates_before\030\003 \001(\0132 .recorder.Tracke"
  "dItemCoordinates\022;\n\021coordinates_after\030\004 "
  "\001(\0132 .recorder.TrackedItemCoordinates\022\021\n"
  "\tin_camera\030\005 \001(\010\"^\n\016CandidateShift\022\017\n\007x_"
  "shift\030\001 \001(\002\022\017\n\007y_shift\030\002 \001(\002\022\023\n\013centroid"
  "_id\030\003 \001(\r\022\025\n\rtrajectory_id\030\004 \001(\r\"\253\002\n\027Dee"
  "pweedPredictionFrame\022\024\n\014timestamp_ms\030\001 \001"
  "(\003\022/\n\ndetections\030\002 \003(\0132\033.recorder.Deepwe"
  "edDetection\022\034\n\024embedding_categories\030\003 \003("
  "\t\022,\n\rtracked_items\030\004 \003(\0132\025.recorder.Trac"
  "kedItem\022,\n\nbest_shift\030\005 \001(\0132\030.recorder.C"
  "andidateShift\0222\n\020candidate_shifts\030\006 \003(\0132"
  "\030.recorder.CandidateShift\022\033\n\023plant_match"
  "er_valid\030\007 \001(\010\"i\n\030DeepweedPredictionReco"
  "rd\022\033\n\023record_timestamp_ms\030\001 \001(\004\0220\n\005frame"
  "\030\002 \001(\0132!.recorder.DeepweedPredictionFram"
  "e\">\n\022LaneHeightSnapshot\022\023\n\013weed_height\030\001"
  " \003(\001\022\023\n\013crop_height\030\002 \003(\001\"_\n\020LaneHeightR"
  "ecord\022\033\n\023record_timestamp_ms\030\001 \001(\004\022.\n\010sn"
  "apshot\030\002 \001(\0132\034.recorder.LaneHeightSnapsh"
  "ot\"\253\001\n\023RotaryTicksSnapshot\022\024\n\014timestamp_"
  "us\030\001 \001(\004\022\n\n\002fl\030\002 \001(\005\022\n\n\002fr\030\003 \001(\005\022\n\n\002bl\030\004"
  " \001(\005\022\n\n\002br\030\005 \001(\005\022\022\n\nfl_enabled\030\006 \001(\010\022\022\n\n"
  "fr_enabled\030\007 \001(\010\022\022\n\nbl_enabled\030\010 \001(\010\022\022\n\n"
  "br_enabled\030\t \001(\010\"a\n\021RotaryTicksRecord\022\033\n"
  "\023record_timestamp_ms\030\001 \001(\004\022/\n\010snapshot\030\002"
  " \001(\0132\035.recorder.RotaryTicksSnapshotB\020Z\016p"
  "roto/recorderb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_recorder_2fproto_2frecorder_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_recorder_2fproto_2frecorder_2eproto = {
  false, false, 1821, descriptor_table_protodef_recorder_2fproto_2frecorder_2eproto, "recorder/proto/recorder.proto", 
  &descriptor_table_recorder_2fproto_2frecorder_2eproto_once, nullptr, 0, 12,
  schemas, file_default_instances, TableStruct_recorder_2fproto_2frecorder_2eproto::offsets,
  file_level_metadata_recorder_2fproto_2frecorder_2eproto, file_level_enum_descriptors_recorder_2fproto_2frecorder_2eproto, file_level_service_descriptors_recorder_2fproto_2frecorder_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_recorder_2fproto_2frecorder_2eproto_getter() {
  return &descriptor_table_recorder_2fproto_2frecorder_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_recorder_2fproto_2frecorder_2eproto(&descriptor_table_recorder_2fproto_2frecorder_2eproto);
namespace recorder {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DeepweedDetection_HitClass_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_recorder_2fproto_2frecorder_2eproto);
  return file_level_enum_descriptors_recorder_2fproto_2frecorder_2eproto[0];
}
bool DeepweedDetection_HitClass_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr DeepweedDetection_HitClass DeepweedDetection::WEED;
constexpr DeepweedDetection_HitClass DeepweedDetection::CROP;
constexpr DeepweedDetection_HitClass DeepweedDetection::HitClass_MIN;
constexpr DeepweedDetection_HitClass DeepweedDetection::HitClass_MAX;
constexpr int DeepweedDetection::HitClass_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

// ===================================================================

class DetectionClass::_Internal {
 public:
};

DetectionClass::DetectionClass(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.DetectionClass)
}
DetectionClass::DetectionClass(const DetectionClass& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  class__.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    class__.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_class_().empty()) {
    class__.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_class_(), 
      GetArenaForAllocation());
  }
  score_ = from.score_;
  // @@protoc_insertion_point(copy_constructor:recorder.DetectionClass)
}

inline void DetectionClass::SharedCtor() {
class__.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  class__.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
score_ = 0;
}

DetectionClass::~DetectionClass() {
  // @@protoc_insertion_point(destructor:recorder.DetectionClass)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DetectionClass::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  class__.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DetectionClass::ArenaDtor(void* object) {
  DetectionClass* _this = reinterpret_cast< DetectionClass* >(object);
  (void)_this;
}
void DetectionClass::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DetectionClass::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DetectionClass::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.DetectionClass)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  class__.ClearToEmpty();
  score_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DetectionClass::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string class = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_class_();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "recorder.DetectionClass.class"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float score = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DetectionClass::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.DetectionClass)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string class = 1;
  if (!this->_internal_class_().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_class_().data(), static_cast<int>(this->_internal_class_().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "recorder.DetectionClass.class");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_class_(), target);
  }

  // float score = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_score(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.DetectionClass)
  return target;
}

size_t DetectionClass::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.DetectionClass)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string class = 1;
  if (!this->_internal_class_().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_class_());
  }

  // float score = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DetectionClass::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DetectionClass::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DetectionClass::GetClassData() const { return &_class_data_; }

void DetectionClass::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DetectionClass *>(to)->MergeFrom(
      static_cast<const DetectionClass &>(from));
}


void DetectionClass::MergeFrom(const DetectionClass& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.DetectionClass)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_class_().empty()) {
    _internal_set_class_(from._internal_class_());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = from._internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    _internal_set_score(from._internal_score());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DetectionClass::CopyFrom(const DetectionClass& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.DetectionClass)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DetectionClass::IsInitialized() const {
  return true;
}

void DetectionClass::InternalSwap(DetectionClass* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &class__, lhs_arena,
      &other->class__, rhs_arena
  );
  swap(score_, other->score_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DetectionClass::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[0]);
}

// ===================================================================

class DeepweedDetection::_Internal {
 public:
};

DeepweedDetection::DeepweedDetection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  detection_classes_(arena),
  mask_intersections_(arena),
  embedding_category_distances_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.DeepweedDetection)
}
DeepweedDetection::DeepweedDetection(const DeepweedDetection& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      detection_classes_(from.detection_classes_),
      mask_intersections_(from.mask_intersections_),
      embedding_category_distances_(from.embedding_category_distances_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&y_undistorted_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_undistorted_));
  // @@protoc_insertion_point(copy_constructor:recorder.DeepweedDetection)
}

inline void DeepweedDetection::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&y_undistorted_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_undistorted_));
}

DeepweedDetection::~DeepweedDetection() {
  // @@protoc_insertion_point(destructor:recorder.DeepweedDetection)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeepweedDetection::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DeepweedDetection::ArenaDtor(void* object) {
  DeepweedDetection* _this = reinterpret_cast< DeepweedDetection* >(object);
  (void)_this;
}
void DeepweedDetection::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeepweedDetection::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeepweedDetection::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.DeepweedDetection)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  detection_classes_.Clear();
  mask_intersections_.Clear();
  embedding_category_distances_.Clear();
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_undistorted_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_undistorted_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeepweedDetection::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float size = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          size_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float score = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .recorder.DeepweedDetection.HitClass hit_class = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_hit_class(static_cast<::recorder::DeepweedDetection_HitClass>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .recorder.DetectionClass detection_classes = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_detection_classes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated bytes mask_intersections = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_mask_intersections();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // uint32 trajectory_id = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          trajectory_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float weed_score = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          weed_score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float crop_score = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          crop_score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float plant_score = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 93)) {
          plant_score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated float embedding_category_distances = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_embedding_category_distances(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 101) {
          _internal_add_embedding_category_distances(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float x_undistorted = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 109)) {
          x_undistorted_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y_undistorted = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 117)) {
          y_undistorted_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeepweedDetection::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.DeepweedDetection)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  // float size = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_size = this->_internal_size();
  uint32_t raw_size;
  memcpy(&raw_size, &tmp_size, sizeof(tmp_size));
  if (raw_size != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_size(), target);
  }

  // float score = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_score(), target);
  }

  // .recorder.DeepweedDetection.HitClass hit_class = 5;
  if (this->_internal_hit_class() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->_internal_hit_class(), target);
  }

  // repeated .recorder.DetectionClass detection_classes = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_detection_classes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_detection_classes(i), target, stream);
  }

  // repeated bytes mask_intersections = 7;
  for (int i = 0, n = this->_internal_mask_intersections_size(); i < n; i++) {
    const auto& s = this->_internal_mask_intersections(i);
    target = stream->WriteBytes(7, s, target);
  }

  // uint32 trajectory_id = 8;
  if (this->_internal_trajectory_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_trajectory_id(), target);
  }

  // float weed_score = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = this->_internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_weed_score(), target);
  }

  // float crop_score = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = this->_internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_crop_score(), target);
  }

  // float plant_score = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_plant_score = this->_internal_plant_score();
  uint32_t raw_plant_score;
  memcpy(&raw_plant_score, &tmp_plant_score, sizeof(tmp_plant_score));
  if (raw_plant_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(11, this->_internal_plant_score(), target);
  }

  // repeated float embedding_category_distances = 12;
  if (this->_internal_embedding_category_distances_size() > 0) {
    target = stream->WriteFixedPacked(12, _internal_embedding_category_distances(), target);
  }

  // float x_undistorted = 13;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_undistorted = this->_internal_x_undistorted();
  uint32_t raw_x_undistorted;
  memcpy(&raw_x_undistorted, &tmp_x_undistorted, sizeof(tmp_x_undistorted));
  if (raw_x_undistorted != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(13, this->_internal_x_undistorted(), target);
  }

  // float y_undistorted = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_undistorted = this->_internal_y_undistorted();
  uint32_t raw_y_undistorted;
  memcpy(&raw_y_undistorted, &tmp_y_undistorted, sizeof(tmp_y_undistorted));
  if (raw_y_undistorted != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(14, this->_internal_y_undistorted(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.DeepweedDetection)
  return target;
}

size_t DeepweedDetection::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.DeepweedDetection)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .recorder.DetectionClass detection_classes = 6;
  total_size += 1UL * this->_internal_detection_classes_size();
  for (const auto& msg : this->detection_classes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated bytes mask_intersections = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(mask_intersections_.size());
  for (int i = 0, n = mask_intersections_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      mask_intersections_.Get(i));
  }

  // repeated float embedding_category_distances = 12;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_embedding_category_distances_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  // float size = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_size = this->_internal_size();
  uint32_t raw_size;
  memcpy(&raw_size, &tmp_size, sizeof(tmp_size));
  if (raw_size != 0) {
    total_size += 1 + 4;
  }

  // float score = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    total_size += 1 + 4;
  }

  // .recorder.DeepweedDetection.HitClass hit_class = 5;
  if (this->_internal_hit_class() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_hit_class());
  }

  // uint32 trajectory_id = 8;
  if (this->_internal_trajectory_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_trajectory_id());
  }

  // float weed_score = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = this->_internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    total_size += 1 + 4;
  }

  // float crop_score = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = this->_internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    total_size += 1 + 4;
  }

  // float plant_score = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_plant_score = this->_internal_plant_score();
  uint32_t raw_plant_score;
  memcpy(&raw_plant_score, &tmp_plant_score, sizeof(tmp_plant_score));
  if (raw_plant_score != 0) {
    total_size += 1 + 4;
  }

  // float x_undistorted = 13;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_undistorted = this->_internal_x_undistorted();
  uint32_t raw_x_undistorted;
  memcpy(&raw_x_undistorted, &tmp_x_undistorted, sizeof(tmp_x_undistorted));
  if (raw_x_undistorted != 0) {
    total_size += 1 + 4;
  }

  // float y_undistorted = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_undistorted = this->_internal_y_undistorted();
  uint32_t raw_y_undistorted;
  memcpy(&raw_y_undistorted, &tmp_y_undistorted, sizeof(tmp_y_undistorted));
  if (raw_y_undistorted != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeepweedDetection::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeepweedDetection::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeepweedDetection::GetClassData() const { return &_class_data_; }

void DeepweedDetection::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeepweedDetection *>(to)->MergeFrom(
      static_cast<const DeepweedDetection &>(from));
}


void DeepweedDetection::MergeFrom(const DeepweedDetection& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.DeepweedDetection)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  detection_classes_.MergeFrom(from.detection_classes_);
  mask_intersections_.MergeFrom(from.mask_intersections_);
  embedding_category_distances_.MergeFrom(from.embedding_category_distances_);
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_size = from._internal_size();
  uint32_t raw_size;
  memcpy(&raw_size, &tmp_size, sizeof(tmp_size));
  if (raw_size != 0) {
    _internal_set_size(from._internal_size());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = from._internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    _internal_set_score(from._internal_score());
  }
  if (from._internal_hit_class() != 0) {
    _internal_set_hit_class(from._internal_hit_class());
  }
  if (from._internal_trajectory_id() != 0) {
    _internal_set_trajectory_id(from._internal_trajectory_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = from._internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    _internal_set_weed_score(from._internal_weed_score());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = from._internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    _internal_set_crop_score(from._internal_crop_score());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_plant_score = from._internal_plant_score();
  uint32_t raw_plant_score;
  memcpy(&raw_plant_score, &tmp_plant_score, sizeof(tmp_plant_score));
  if (raw_plant_score != 0) {
    _internal_set_plant_score(from._internal_plant_score());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_undistorted = from._internal_x_undistorted();
  uint32_t raw_x_undistorted;
  memcpy(&raw_x_undistorted, &tmp_x_undistorted, sizeof(tmp_x_undistorted));
  if (raw_x_undistorted != 0) {
    _internal_set_x_undistorted(from._internal_x_undistorted());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_undistorted = from._internal_y_undistorted();
  uint32_t raw_y_undistorted;
  memcpy(&raw_y_undistorted, &tmp_y_undistorted, sizeof(tmp_y_undistorted));
  if (raw_y_undistorted != 0) {
    _internal_set_y_undistorted(from._internal_y_undistorted());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeepweedDetection::CopyFrom(const DeepweedDetection& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.DeepweedDetection)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeepweedDetection::IsInitialized() const {
  return true;
}

void DeepweedDetection::InternalSwap(DeepweedDetection* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  detection_classes_.InternalSwap(&other->detection_classes_);
  mask_intersections_.InternalSwap(&other->mask_intersections_);
  embedding_category_distances_.InternalSwap(&other->embedding_category_distances_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DeepweedDetection, y_undistorted_)
      + sizeof(DeepweedDetection::y_undistorted_)
      - PROTOBUF_FIELD_OFFSET(DeepweedDetection, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DeepweedDetection::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[1]);
}

// ===================================================================

class TrackedItemCoordinates::_Internal {
 public:
};

TrackedItemCoordinates::TrackedItemCoordinates(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.TrackedItemCoordinates)
}
TrackedItemCoordinates::TrackedItemCoordinates(const TrackedItemCoordinates& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_mm_, &from.x_mm_,
    static_cast<size_t>(reinterpret_cast<char*>(&y_px_) -
    reinterpret_cast<char*>(&x_mm_)) + sizeof(y_px_));
  // @@protoc_insertion_point(copy_constructor:recorder.TrackedItemCoordinates)
}

inline void TrackedItemCoordinates::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_mm_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&y_px_) -
    reinterpret_cast<char*>(&x_mm_)) + sizeof(y_px_));
}

TrackedItemCoordinates::~TrackedItemCoordinates() {
  // @@protoc_insertion_point(destructor:recorder.TrackedItemCoordinates)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TrackedItemCoordinates::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TrackedItemCoordinates::ArenaDtor(void* object) {
  TrackedItemCoordinates* _this = reinterpret_cast< TrackedItemCoordinates* >(object);
  (void)_this;
}
void TrackedItemCoordinates::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TrackedItemCoordinates::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TrackedItemCoordinates::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.TrackedItemCoordinates)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_mm_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_px_) -
      reinterpret_cast<char*>(&x_mm_)) + sizeof(y_px_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TrackedItemCoordinates::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x_mm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          x_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y_mm = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          y_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float z_mm = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          z_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float x_px = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          x_px_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y_px = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          y_px_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TrackedItemCoordinates::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.TrackedItemCoordinates)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x_mm = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_mm = this->_internal_x_mm();
  uint32_t raw_x_mm;
  memcpy(&raw_x_mm, &tmp_x_mm, sizeof(tmp_x_mm));
  if (raw_x_mm != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_x_mm(), target);
  }

  // float y_mm = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_mm = this->_internal_y_mm();
  uint32_t raw_y_mm;
  memcpy(&raw_y_mm, &tmp_y_mm, sizeof(tmp_y_mm));
  if (raw_y_mm != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_y_mm(), target);
  }

  // float z_mm = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z_mm = this->_internal_z_mm();
  uint32_t raw_z_mm;
  memcpy(&raw_z_mm, &tmp_z_mm, sizeof(tmp_z_mm));
  if (raw_z_mm != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_z_mm(), target);
  }

  // float x_px = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_px = this->_internal_x_px();
  uint32_t raw_x_px;
  memcpy(&raw_x_px, &tmp_x_px, sizeof(tmp_x_px));
  if (raw_x_px != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_x_px(), target);
  }

  // float y_px = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_px = this->_internal_y_px();
  uint32_t raw_y_px;
  memcpy(&raw_y_px, &tmp_y_px, sizeof(tmp_y_px));
  if (raw_y_px != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_y_px(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.TrackedItemCoordinates)
  return target;
}

size_t TrackedItemCoordinates::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.TrackedItemCoordinates)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x_mm = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_mm = this->_internal_x_mm();
  uint32_t raw_x_mm;
  memcpy(&raw_x_mm, &tmp_x_mm, sizeof(tmp_x_mm));
  if (raw_x_mm != 0) {
    total_size += 1 + 4;
  }

  // float y_mm = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_mm = this->_internal_y_mm();
  uint32_t raw_y_mm;
  memcpy(&raw_y_mm, &tmp_y_mm, sizeof(tmp_y_mm));
  if (raw_y_mm != 0) {
    total_size += 1 + 4;
  }

  // float z_mm = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z_mm = this->_internal_z_mm();
  uint32_t raw_z_mm;
  memcpy(&raw_z_mm, &tmp_z_mm, sizeof(tmp_z_mm));
  if (raw_z_mm != 0) {
    total_size += 1 + 4;
  }

  // float x_px = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_px = this->_internal_x_px();
  uint32_t raw_x_px;
  memcpy(&raw_x_px, &tmp_x_px, sizeof(tmp_x_px));
  if (raw_x_px != 0) {
    total_size += 1 + 4;
  }

  // float y_px = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_px = this->_internal_y_px();
  uint32_t raw_y_px;
  memcpy(&raw_y_px, &tmp_y_px, sizeof(tmp_y_px));
  if (raw_y_px != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TrackedItemCoordinates::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TrackedItemCoordinates::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TrackedItemCoordinates::GetClassData() const { return &_class_data_; }

void TrackedItemCoordinates::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TrackedItemCoordinates *>(to)->MergeFrom(
      static_cast<const TrackedItemCoordinates &>(from));
}


void TrackedItemCoordinates::MergeFrom(const TrackedItemCoordinates& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.TrackedItemCoordinates)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_mm = from._internal_x_mm();
  uint32_t raw_x_mm;
  memcpy(&raw_x_mm, &tmp_x_mm, sizeof(tmp_x_mm));
  if (raw_x_mm != 0) {
    _internal_set_x_mm(from._internal_x_mm());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_mm = from._internal_y_mm();
  uint32_t raw_y_mm;
  memcpy(&raw_y_mm, &tmp_y_mm, sizeof(tmp_y_mm));
  if (raw_y_mm != 0) {
    _internal_set_y_mm(from._internal_y_mm());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z_mm = from._internal_z_mm();
  uint32_t raw_z_mm;
  memcpy(&raw_z_mm, &tmp_z_mm, sizeof(tmp_z_mm));
  if (raw_z_mm != 0) {
    _internal_set_z_mm(from._internal_z_mm());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_px = from._internal_x_px();
  uint32_t raw_x_px;
  memcpy(&raw_x_px, &tmp_x_px, sizeof(tmp_x_px));
  if (raw_x_px != 0) {
    _internal_set_x_px(from._internal_x_px());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_px = from._internal_y_px();
  uint32_t raw_y_px;
  memcpy(&raw_y_px, &tmp_y_px, sizeof(tmp_y_px));
  if (raw_y_px != 0) {
    _internal_set_y_px(from._internal_y_px());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TrackedItemCoordinates::CopyFrom(const TrackedItemCoordinates& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.TrackedItemCoordinates)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackedItemCoordinates::IsInitialized() const {
  return true;
}

void TrackedItemCoordinates::InternalSwap(TrackedItemCoordinates* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TrackedItemCoordinates, y_px_)
      + sizeof(TrackedItemCoordinates::y_px_)
      - PROTOBUF_FIELD_OFFSET(TrackedItemCoordinates, x_mm_)>(
          reinterpret_cast<char*>(&x_mm_),
          reinterpret_cast<char*>(&other->x_mm_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TrackedItemCoordinates::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[2]);
}

// ===================================================================

class EmbeddingCategory::_Internal {
 public:
};

EmbeddingCategory::EmbeddingCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.EmbeddingCategory)
}
EmbeddingCategory::EmbeddingCategory(const EmbeddingCategory& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  category_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_category().empty()) {
    category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_category(), 
      GetArenaForAllocation());
  }
  distance_ = from.distance_;
  // @@protoc_insertion_point(copy_constructor:recorder.EmbeddingCategory)
}

inline void EmbeddingCategory::SharedCtor() {
category_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
distance_ = 0;
}

EmbeddingCategory::~EmbeddingCategory() {
  // @@protoc_insertion_point(destructor:recorder.EmbeddingCategory)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EmbeddingCategory::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  category_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void EmbeddingCategory::ArenaDtor(void* object) {
  EmbeddingCategory* _this = reinterpret_cast< EmbeddingCategory* >(object);
  (void)_this;
}
void EmbeddingCategory::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EmbeddingCategory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EmbeddingCategory::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.EmbeddingCategory)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  category_.ClearToEmpty();
  distance_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EmbeddingCategory::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string category = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_category();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "recorder.EmbeddingCategory.category"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float distance = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          distance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EmbeddingCategory::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.EmbeddingCategory)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string category = 1;
  if (!this->_internal_category().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_category().data(), static_cast<int>(this->_internal_category().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "recorder.EmbeddingCategory.category");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_category(), target);
  }

  // float distance = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = this->_internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_distance(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.EmbeddingCategory)
  return target;
}

size_t EmbeddingCategory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.EmbeddingCategory)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string category = 1;
  if (!this->_internal_category().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());
  }

  // float distance = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = this->_internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EmbeddingCategory::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EmbeddingCategory::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EmbeddingCategory::GetClassData() const { return &_class_data_; }

void EmbeddingCategory::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<EmbeddingCategory *>(to)->MergeFrom(
      static_cast<const EmbeddingCategory &>(from));
}


void EmbeddingCategory::MergeFrom(const EmbeddingCategory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.EmbeddingCategory)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_category().empty()) {
    _internal_set_category(from._internal_category());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = from._internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    _internal_set_distance(from._internal_distance());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EmbeddingCategory::CopyFrom(const EmbeddingCategory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.EmbeddingCategory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EmbeddingCategory::IsInitialized() const {
  return true;
}

void EmbeddingCategory::InternalSwap(EmbeddingCategory* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &category_, lhs_arena,
      &other->category_, rhs_arena
  );
  swap(distance_, other->distance_);
}

::PROTOBUF_NAMESPACE_ID::Metadata EmbeddingCategory::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[3]);
}

// ===================================================================

class TrackedItem::_Internal {
 public:
  static const ::recorder::TrackedItemCoordinates& coordinates_before(const TrackedItem* msg);
  static const ::recorder::TrackedItemCoordinates& coordinates_after(const TrackedItem* msg);
};

const ::recorder::TrackedItemCoordinates&
TrackedItem::_Internal::coordinates_before(const TrackedItem* msg) {
  return *msg->coordinates_before_;
}
const ::recorder::TrackedItemCoordinates&
TrackedItem::_Internal::coordinates_after(const TrackedItem* msg) {
  return *msg->coordinates_after_;
}
TrackedItem::TrackedItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.TrackedItem)
}
TrackedItem::TrackedItem(const TrackedItem& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_coordinates_before()) {
    coordinates_before_ = new ::recorder::TrackedItemCoordinates(*from.coordinates_before_);
  } else {
    coordinates_before_ = nullptr;
  }
  if (from._internal_has_coordinates_after()) {
    coordinates_after_ = new ::recorder::TrackedItemCoordinates(*from.coordinates_after_);
  } else {
    coordinates_after_ = nullptr;
  }
  ::memcpy(&trajectory_id_, &from.trajectory_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&in_camera_) -
    reinterpret_cast<char*>(&trajectory_id_)) + sizeof(in_camera_));
  // @@protoc_insertion_point(copy_constructor:recorder.TrackedItem)
}

inline void TrackedItem::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&coordinates_before_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&in_camera_) -
    reinterpret_cast<char*>(&coordinates_before_)) + sizeof(in_camera_));
}

TrackedItem::~TrackedItem() {
  // @@protoc_insertion_point(destructor:recorder.TrackedItem)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TrackedItem::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete coordinates_before_;
  if (this != internal_default_instance()) delete coordinates_after_;
}

void TrackedItem::ArenaDtor(void* object) {
  TrackedItem* _this = reinterpret_cast< TrackedItem* >(object);
  (void)_this;
}
void TrackedItem::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TrackedItem::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TrackedItem::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.TrackedItem)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && coordinates_before_ != nullptr) {
    delete coordinates_before_;
  }
  coordinates_before_ = nullptr;
  if (GetArenaForAllocation() == nullptr && coordinates_after_ != nullptr) {
    delete coordinates_after_;
  }
  coordinates_after_ = nullptr;
  ::memset(&trajectory_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&in_camera_) -
      reinterpret_cast<char*>(&trajectory_id_)) + sizeof(in_camera_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TrackedItem::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 trajectory_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          trajectory_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool deduplicated = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          deduplicated_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .recorder.TrackedItemCoordinates coordinates_before = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_coordinates_before(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .recorder.TrackedItemCoordinates coordinates_after = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_coordinates_after(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool in_camera = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          in_camera_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TrackedItem::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.TrackedItem)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 trajectory_id = 1;
  if (this->_internal_trajectory_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_trajectory_id(), target);
  }

  // bool deduplicated = 2;
  if (this->_internal_deduplicated() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_deduplicated(), target);
  }

  // .recorder.TrackedItemCoordinates coordinates_before = 3;
  if (this->_internal_has_coordinates_before()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::coordinates_before(this), target, stream);
  }

  // .recorder.TrackedItemCoordinates coordinates_after = 4;
  if (this->_internal_has_coordinates_after()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::coordinates_after(this), target, stream);
  }

  // bool in_camera = 5;
  if (this->_internal_in_camera() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_in_camera(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.TrackedItem)
  return target;
}

size_t TrackedItem::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.TrackedItem)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .recorder.TrackedItemCoordinates coordinates_before = 3;
  if (this->_internal_has_coordinates_before()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *coordinates_before_);
  }

  // .recorder.TrackedItemCoordinates coordinates_after = 4;
  if (this->_internal_has_coordinates_after()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *coordinates_after_);
  }

  // uint32 trajectory_id = 1;
  if (this->_internal_trajectory_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_trajectory_id());
  }

  // bool deduplicated = 2;
  if (this->_internal_deduplicated() != 0) {
    total_size += 1 + 1;
  }

  // bool in_camera = 5;
  if (this->_internal_in_camera() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TrackedItem::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TrackedItem::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TrackedItem::GetClassData() const { return &_class_data_; }

void TrackedItem::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TrackedItem *>(to)->MergeFrom(
      static_cast<const TrackedItem &>(from));
}


void TrackedItem::MergeFrom(const TrackedItem& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.TrackedItem)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_coordinates_before()) {
    _internal_mutable_coordinates_before()->::recorder::TrackedItemCoordinates::MergeFrom(from._internal_coordinates_before());
  }
  if (from._internal_has_coordinates_after()) {
    _internal_mutable_coordinates_after()->::recorder::TrackedItemCoordinates::MergeFrom(from._internal_coordinates_after());
  }
  if (from._internal_trajectory_id() != 0) {
    _internal_set_trajectory_id(from._internal_trajectory_id());
  }
  if (from._internal_deduplicated() != 0) {
    _internal_set_deduplicated(from._internal_deduplicated());
  }
  if (from._internal_in_camera() != 0) {
    _internal_set_in_camera(from._internal_in_camera());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TrackedItem::CopyFrom(const TrackedItem& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.TrackedItem)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackedItem::IsInitialized() const {
  return true;
}

void TrackedItem::InternalSwap(TrackedItem* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TrackedItem, in_camera_)
      + sizeof(TrackedItem::in_camera_)
      - PROTOBUF_FIELD_OFFSET(TrackedItem, coordinates_before_)>(
          reinterpret_cast<char*>(&coordinates_before_),
          reinterpret_cast<char*>(&other->coordinates_before_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TrackedItem::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[4]);
}

// ===================================================================

class CandidateShift::_Internal {
 public:
};

CandidateShift::CandidateShift(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.CandidateShift)
}
CandidateShift::CandidateShift(const CandidateShift& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_shift_, &from.x_shift_,
    static_cast<size_t>(reinterpret_cast<char*>(&trajectory_id_) -
    reinterpret_cast<char*>(&x_shift_)) + sizeof(trajectory_id_));
  // @@protoc_insertion_point(copy_constructor:recorder.CandidateShift)
}

inline void CandidateShift::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_shift_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&trajectory_id_) -
    reinterpret_cast<char*>(&x_shift_)) + sizeof(trajectory_id_));
}

CandidateShift::~CandidateShift() {
  // @@protoc_insertion_point(destructor:recorder.CandidateShift)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CandidateShift::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CandidateShift::ArenaDtor(void* object) {
  CandidateShift* _this = reinterpret_cast< CandidateShift* >(object);
  (void)_this;
}
void CandidateShift::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CandidateShift::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CandidateShift::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.CandidateShift)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_shift_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&trajectory_id_) -
      reinterpret_cast<char*>(&x_shift_)) + sizeof(trajectory_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CandidateShift::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x_shift = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          x_shift_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y_shift = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          y_shift_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // uint32 centroid_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          centroid_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 trajectory_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          trajectory_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CandidateShift::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.CandidateShift)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x_shift = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_shift = this->_internal_x_shift();
  uint32_t raw_x_shift;
  memcpy(&raw_x_shift, &tmp_x_shift, sizeof(tmp_x_shift));
  if (raw_x_shift != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_x_shift(), target);
  }

  // float y_shift = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_shift = this->_internal_y_shift();
  uint32_t raw_y_shift;
  memcpy(&raw_y_shift, &tmp_y_shift, sizeof(tmp_y_shift));
  if (raw_y_shift != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_y_shift(), target);
  }

  // uint32 centroid_id = 3;
  if (this->_internal_centroid_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_centroid_id(), target);
  }

  // uint32 trajectory_id = 4;
  if (this->_internal_trajectory_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_trajectory_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.CandidateShift)
  return target;
}

size_t CandidateShift::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.CandidateShift)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x_shift = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_shift = this->_internal_x_shift();
  uint32_t raw_x_shift;
  memcpy(&raw_x_shift, &tmp_x_shift, sizeof(tmp_x_shift));
  if (raw_x_shift != 0) {
    total_size += 1 + 4;
  }

  // float y_shift = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_shift = this->_internal_y_shift();
  uint32_t raw_y_shift;
  memcpy(&raw_y_shift, &tmp_y_shift, sizeof(tmp_y_shift));
  if (raw_y_shift != 0) {
    total_size += 1 + 4;
  }

  // uint32 centroid_id = 3;
  if (this->_internal_centroid_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_centroid_id());
  }

  // uint32 trajectory_id = 4;
  if (this->_internal_trajectory_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_trajectory_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CandidateShift::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CandidateShift::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CandidateShift::GetClassData() const { return &_class_data_; }

void CandidateShift::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CandidateShift *>(to)->MergeFrom(
      static_cast<const CandidateShift &>(from));
}


void CandidateShift::MergeFrom(const CandidateShift& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.CandidateShift)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x_shift = from._internal_x_shift();
  uint32_t raw_x_shift;
  memcpy(&raw_x_shift, &tmp_x_shift, sizeof(tmp_x_shift));
  if (raw_x_shift != 0) {
    _internal_set_x_shift(from._internal_x_shift());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y_shift = from._internal_y_shift();
  uint32_t raw_y_shift;
  memcpy(&raw_y_shift, &tmp_y_shift, sizeof(tmp_y_shift));
  if (raw_y_shift != 0) {
    _internal_set_y_shift(from._internal_y_shift());
  }
  if (from._internal_centroid_id() != 0) {
    _internal_set_centroid_id(from._internal_centroid_id());
  }
  if (from._internal_trajectory_id() != 0) {
    _internal_set_trajectory_id(from._internal_trajectory_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CandidateShift::CopyFrom(const CandidateShift& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.CandidateShift)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CandidateShift::IsInitialized() const {
  return true;
}

void CandidateShift::InternalSwap(CandidateShift* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CandidateShift, trajectory_id_)
      + sizeof(CandidateShift::trajectory_id_)
      - PROTOBUF_FIELD_OFFSET(CandidateShift, x_shift_)>(
          reinterpret_cast<char*>(&x_shift_),
          reinterpret_cast<char*>(&other->x_shift_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CandidateShift::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[5]);
}

// ===================================================================

class DeepweedPredictionFrame::_Internal {
 public:
  static const ::recorder::CandidateShift& best_shift(const DeepweedPredictionFrame* msg);
};

const ::recorder::CandidateShift&
DeepweedPredictionFrame::_Internal::best_shift(const DeepweedPredictionFrame* msg) {
  return *msg->best_shift_;
}
DeepweedPredictionFrame::DeepweedPredictionFrame(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  detections_(arena),
  embedding_categories_(arena),
  tracked_items_(arena),
  candidate_shifts_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.DeepweedPredictionFrame)
}
DeepweedPredictionFrame::DeepweedPredictionFrame(const DeepweedPredictionFrame& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      detections_(from.detections_),
      embedding_categories_(from.embedding_categories_),
      tracked_items_(from.tracked_items_),
      candidate_shifts_(from.candidate_shifts_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_best_shift()) {
    best_shift_ = new ::recorder::CandidateShift(*from.best_shift_);
  } else {
    best_shift_ = nullptr;
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&plant_matcher_valid_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(plant_matcher_valid_));
  // @@protoc_insertion_point(copy_constructor:recorder.DeepweedPredictionFrame)
}

inline void DeepweedPredictionFrame::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&best_shift_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&plant_matcher_valid_) -
    reinterpret_cast<char*>(&best_shift_)) + sizeof(plant_matcher_valid_));
}

DeepweedPredictionFrame::~DeepweedPredictionFrame() {
  // @@protoc_insertion_point(destructor:recorder.DeepweedPredictionFrame)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeepweedPredictionFrame::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete best_shift_;
}

void DeepweedPredictionFrame::ArenaDtor(void* object) {
  DeepweedPredictionFrame* _this = reinterpret_cast< DeepweedPredictionFrame* >(object);
  (void)_this;
}
void DeepweedPredictionFrame::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeepweedPredictionFrame::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeepweedPredictionFrame::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.DeepweedPredictionFrame)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  detections_.Clear();
  embedding_categories_.Clear();
  tracked_items_.Clear();
  candidate_shifts_.Clear();
  if (GetArenaForAllocation() == nullptr && best_shift_ != nullptr) {
    delete best_shift_;
  }
  best_shift_ = nullptr;
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&plant_matcher_valid_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(plant_matcher_valid_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeepweedPredictionFrame::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .recorder.DeepweedDetection detections = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_detections(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string embedding_categories = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_embedding_categories();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "recorder.DeepweedPredictionFrame.embedding_categories"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .recorder.TrackedItem tracked_items = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_tracked_items(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .recorder.CandidateShift best_shift = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_best_shift(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .recorder.CandidateShift candidate_shifts = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_candidate_shifts(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool plant_matcher_valid = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          plant_matcher_valid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeepweedPredictionFrame::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.DeepweedPredictionFrame)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // repeated .recorder.DeepweedDetection detections = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_detections_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_detections(i), target, stream);
  }

  // repeated string embedding_categories = 3;
  for (int i = 0, n = this->_internal_embedding_categories_size(); i < n; i++) {
    const auto& s = this->_internal_embedding_categories(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "recorder.DeepweedPredictionFrame.embedding_categories");
    target = stream->WriteString(3, s, target);
  }

  // repeated .recorder.TrackedItem tracked_items = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_tracked_items_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_tracked_items(i), target, stream);
  }

  // .recorder.CandidateShift best_shift = 5;
  if (this->_internal_has_best_shift()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::best_shift(this), target, stream);
  }

  // repeated .recorder.CandidateShift candidate_shifts = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_candidate_shifts_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_candidate_shifts(i), target, stream);
  }

  // bool plant_matcher_valid = 7;
  if (this->_internal_plant_matcher_valid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_plant_matcher_valid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.DeepweedPredictionFrame)
  return target;
}

size_t DeepweedPredictionFrame::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.DeepweedPredictionFrame)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .recorder.DeepweedDetection detections = 2;
  total_size += 1UL * this->_internal_detections_size();
  for (const auto& msg : this->detections_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated string embedding_categories = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(embedding_categories_.size());
  for (int i = 0, n = embedding_categories_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      embedding_categories_.Get(i));
  }

  // repeated .recorder.TrackedItem tracked_items = 4;
  total_size += 1UL * this->_internal_tracked_items_size();
  for (const auto& msg : this->tracked_items_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .recorder.CandidateShift candidate_shifts = 6;
  total_size += 1UL * this->_internal_candidate_shifts_size();
  for (const auto& msg : this->candidate_shifts_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .recorder.CandidateShift best_shift = 5;
  if (this->_internal_has_best_shift()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *best_shift_);
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // bool plant_matcher_valid = 7;
  if (this->_internal_plant_matcher_valid() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeepweedPredictionFrame::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeepweedPredictionFrame::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeepweedPredictionFrame::GetClassData() const { return &_class_data_; }

void DeepweedPredictionFrame::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeepweedPredictionFrame *>(to)->MergeFrom(
      static_cast<const DeepweedPredictionFrame &>(from));
}


void DeepweedPredictionFrame::MergeFrom(const DeepweedPredictionFrame& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.DeepweedPredictionFrame)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  detections_.MergeFrom(from.detections_);
  embedding_categories_.MergeFrom(from.embedding_categories_);
  tracked_items_.MergeFrom(from.tracked_items_);
  candidate_shifts_.MergeFrom(from.candidate_shifts_);
  if (from._internal_has_best_shift()) {
    _internal_mutable_best_shift()->::recorder::CandidateShift::MergeFrom(from._internal_best_shift());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_plant_matcher_valid() != 0) {
    _internal_set_plant_matcher_valid(from._internal_plant_matcher_valid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeepweedPredictionFrame::CopyFrom(const DeepweedPredictionFrame& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.DeepweedPredictionFrame)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeepweedPredictionFrame::IsInitialized() const {
  return true;
}

void DeepweedPredictionFrame::InternalSwap(DeepweedPredictionFrame* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  detections_.InternalSwap(&other->detections_);
  embedding_categories_.InternalSwap(&other->embedding_categories_);
  tracked_items_.InternalSwap(&other->tracked_items_);
  candidate_shifts_.InternalSwap(&other->candidate_shifts_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DeepweedPredictionFrame, plant_matcher_valid_)
      + sizeof(DeepweedPredictionFrame::plant_matcher_valid_)
      - PROTOBUF_FIELD_OFFSET(DeepweedPredictionFrame, best_shift_)>(
          reinterpret_cast<char*>(&best_shift_),
          reinterpret_cast<char*>(&other->best_shift_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DeepweedPredictionFrame::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[6]);
}

// ===================================================================

class DeepweedPredictionRecord::_Internal {
 public:
  static const ::recorder::DeepweedPredictionFrame& frame(const DeepweedPredictionRecord* msg);
};

const ::recorder::DeepweedPredictionFrame&
DeepweedPredictionRecord::_Internal::frame(const DeepweedPredictionRecord* msg) {
  return *msg->frame_;
}
DeepweedPredictionRecord::DeepweedPredictionRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.DeepweedPredictionRecord)
}
DeepweedPredictionRecord::DeepweedPredictionRecord(const DeepweedPredictionRecord& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_frame()) {
    frame_ = new ::recorder::DeepweedPredictionFrame(*from.frame_);
  } else {
    frame_ = nullptr;
  }
  record_timestamp_ms_ = from.record_timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:recorder.DeepweedPredictionRecord)
}

inline void DeepweedPredictionRecord::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&frame_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&record_timestamp_ms_) -
    reinterpret_cast<char*>(&frame_)) + sizeof(record_timestamp_ms_));
}

DeepweedPredictionRecord::~DeepweedPredictionRecord() {
  // @@protoc_insertion_point(destructor:recorder.DeepweedPredictionRecord)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeepweedPredictionRecord::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete frame_;
}

void DeepweedPredictionRecord::ArenaDtor(void* object) {
  DeepweedPredictionRecord* _this = reinterpret_cast< DeepweedPredictionRecord* >(object);
  (void)_this;
}
void DeepweedPredictionRecord::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeepweedPredictionRecord::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeepweedPredictionRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.DeepweedPredictionRecord)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && frame_ != nullptr) {
    delete frame_;
  }
  frame_ = nullptr;
  record_timestamp_ms_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeepweedPredictionRecord::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 record_timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          record_timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .recorder.DeepweedPredictionFrame frame = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_frame(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeepweedPredictionRecord::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.DeepweedPredictionRecord)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_record_timestamp_ms(), target);
  }

  // .recorder.DeepweedPredictionFrame frame = 2;
  if (this->_internal_has_frame()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::frame(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.DeepweedPredictionRecord)
  return target;
}

size_t DeepweedPredictionRecord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.DeepweedPredictionRecord)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .recorder.DeepweedPredictionFrame frame = 2;
  if (this->_internal_has_frame()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *frame_);
  }

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_record_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeepweedPredictionRecord::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeepweedPredictionRecord::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeepweedPredictionRecord::GetClassData() const { return &_class_data_; }

void DeepweedPredictionRecord::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeepweedPredictionRecord *>(to)->MergeFrom(
      static_cast<const DeepweedPredictionRecord &>(from));
}


void DeepweedPredictionRecord::MergeFrom(const DeepweedPredictionRecord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.DeepweedPredictionRecord)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_frame()) {
    _internal_mutable_frame()->::recorder::DeepweedPredictionFrame::MergeFrom(from._internal_frame());
  }
  if (from._internal_record_timestamp_ms() != 0) {
    _internal_set_record_timestamp_ms(from._internal_record_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeepweedPredictionRecord::CopyFrom(const DeepweedPredictionRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.DeepweedPredictionRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeepweedPredictionRecord::IsInitialized() const {
  return true;
}

void DeepweedPredictionRecord::InternalSwap(DeepweedPredictionRecord* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DeepweedPredictionRecord, record_timestamp_ms_)
      + sizeof(DeepweedPredictionRecord::record_timestamp_ms_)
      - PROTOBUF_FIELD_OFFSET(DeepweedPredictionRecord, frame_)>(
          reinterpret_cast<char*>(&frame_),
          reinterpret_cast<char*>(&other->frame_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DeepweedPredictionRecord::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[7]);
}

// ===================================================================

class LaneHeightSnapshot::_Internal {
 public:
};

LaneHeightSnapshot::LaneHeightSnapshot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  weed_height_(arena),
  crop_height_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.LaneHeightSnapshot)
}
LaneHeightSnapshot::LaneHeightSnapshot(const LaneHeightSnapshot& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      weed_height_(from.weed_height_),
      crop_height_(from.crop_height_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:recorder.LaneHeightSnapshot)
}

inline void LaneHeightSnapshot::SharedCtor() {
}

LaneHeightSnapshot::~LaneHeightSnapshot() {
  // @@protoc_insertion_point(destructor:recorder.LaneHeightSnapshot)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaneHeightSnapshot::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LaneHeightSnapshot::ArenaDtor(void* object) {
  LaneHeightSnapshot* _this = reinterpret_cast< LaneHeightSnapshot* >(object);
  (void)_this;
}
void LaneHeightSnapshot::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneHeightSnapshot::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaneHeightSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.LaneHeightSnapshot)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  weed_height_.Clear();
  crop_height_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneHeightSnapshot::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated double weed_height = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedDoubleParser(_internal_mutable_weed_height(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 9) {
          _internal_add_weed_height(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // repeated double crop_height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedDoubleParser(_internal_mutable_crop_height(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 17) {
          _internal_add_crop_height(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaneHeightSnapshot::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.LaneHeightSnapshot)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated double weed_height = 1;
  if (this->_internal_weed_height_size() > 0) {
    target = stream->WriteFixedPacked(1, _internal_weed_height(), target);
  }

  // repeated double crop_height = 2;
  if (this->_internal_crop_height_size() > 0) {
    target = stream->WriteFixedPacked(2, _internal_crop_height(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.LaneHeightSnapshot)
  return target;
}

size_t LaneHeightSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.LaneHeightSnapshot)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated double weed_height = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_weed_height_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated double crop_height = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_crop_height_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaneHeightSnapshot::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaneHeightSnapshot::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaneHeightSnapshot::GetClassData() const { return &_class_data_; }

void LaneHeightSnapshot::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaneHeightSnapshot *>(to)->MergeFrom(
      static_cast<const LaneHeightSnapshot &>(from));
}


void LaneHeightSnapshot::MergeFrom(const LaneHeightSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.LaneHeightSnapshot)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  weed_height_.MergeFrom(from.weed_height_);
  crop_height_.MergeFrom(from.crop_height_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaneHeightSnapshot::CopyFrom(const LaneHeightSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.LaneHeightSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneHeightSnapshot::IsInitialized() const {
  return true;
}

void LaneHeightSnapshot::InternalSwap(LaneHeightSnapshot* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  weed_height_.InternalSwap(&other->weed_height_);
  crop_height_.InternalSwap(&other->crop_height_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneHeightSnapshot::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[8]);
}

// ===================================================================

class LaneHeightRecord::_Internal {
 public:
  static const ::recorder::LaneHeightSnapshot& snapshot(const LaneHeightRecord* msg);
};

const ::recorder::LaneHeightSnapshot&
LaneHeightRecord::_Internal::snapshot(const LaneHeightRecord* msg) {
  return *msg->snapshot_;
}
LaneHeightRecord::LaneHeightRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.LaneHeightRecord)
}
LaneHeightRecord::LaneHeightRecord(const LaneHeightRecord& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_snapshot()) {
    snapshot_ = new ::recorder::LaneHeightSnapshot(*from.snapshot_);
  } else {
    snapshot_ = nullptr;
  }
  record_timestamp_ms_ = from.record_timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:recorder.LaneHeightRecord)
}

inline void LaneHeightRecord::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&snapshot_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&record_timestamp_ms_) -
    reinterpret_cast<char*>(&snapshot_)) + sizeof(record_timestamp_ms_));
}

LaneHeightRecord::~LaneHeightRecord() {
  // @@protoc_insertion_point(destructor:recorder.LaneHeightRecord)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaneHeightRecord::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete snapshot_;
}

void LaneHeightRecord::ArenaDtor(void* object) {
  LaneHeightRecord* _this = reinterpret_cast< LaneHeightRecord* >(object);
  (void)_this;
}
void LaneHeightRecord::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneHeightRecord::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaneHeightRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.LaneHeightRecord)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && snapshot_ != nullptr) {
    delete snapshot_;
  }
  snapshot_ = nullptr;
  record_timestamp_ms_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneHeightRecord::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 record_timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          record_timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .recorder.LaneHeightSnapshot snapshot = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_snapshot(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaneHeightRecord::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.LaneHeightRecord)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_record_timestamp_ms(), target);
  }

  // .recorder.LaneHeightSnapshot snapshot = 2;
  if (this->_internal_has_snapshot()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::snapshot(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.LaneHeightRecord)
  return target;
}

size_t LaneHeightRecord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.LaneHeightRecord)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .recorder.LaneHeightSnapshot snapshot = 2;
  if (this->_internal_has_snapshot()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *snapshot_);
  }

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_record_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaneHeightRecord::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaneHeightRecord::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaneHeightRecord::GetClassData() const { return &_class_data_; }

void LaneHeightRecord::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaneHeightRecord *>(to)->MergeFrom(
      static_cast<const LaneHeightRecord &>(from));
}


void LaneHeightRecord::MergeFrom(const LaneHeightRecord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.LaneHeightRecord)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_snapshot()) {
    _internal_mutable_snapshot()->::recorder::LaneHeightSnapshot::MergeFrom(from._internal_snapshot());
  }
  if (from._internal_record_timestamp_ms() != 0) {
    _internal_set_record_timestamp_ms(from._internal_record_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaneHeightRecord::CopyFrom(const LaneHeightRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.LaneHeightRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneHeightRecord::IsInitialized() const {
  return true;
}

void LaneHeightRecord::InternalSwap(LaneHeightRecord* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaneHeightRecord, record_timestamp_ms_)
      + sizeof(LaneHeightRecord::record_timestamp_ms_)
      - PROTOBUF_FIELD_OFFSET(LaneHeightRecord, snapshot_)>(
          reinterpret_cast<char*>(&snapshot_),
          reinterpret_cast<char*>(&other->snapshot_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneHeightRecord::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[9]);
}

// ===================================================================

class RotaryTicksSnapshot::_Internal {
 public:
};

RotaryTicksSnapshot::RotaryTicksSnapshot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.RotaryTicksSnapshot)
}
RotaryTicksSnapshot::RotaryTicksSnapshot(const RotaryTicksSnapshot& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&timestamp_us_, &from.timestamp_us_,
    static_cast<size_t>(reinterpret_cast<char*>(&br_enabled_) -
    reinterpret_cast<char*>(&timestamp_us_)) + sizeof(br_enabled_));
  // @@protoc_insertion_point(copy_constructor:recorder.RotaryTicksSnapshot)
}

inline void RotaryTicksSnapshot::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_us_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&br_enabled_) -
    reinterpret_cast<char*>(&timestamp_us_)) + sizeof(br_enabled_));
}

RotaryTicksSnapshot::~RotaryTicksSnapshot() {
  // @@protoc_insertion_point(destructor:recorder.RotaryTicksSnapshot)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RotaryTicksSnapshot::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RotaryTicksSnapshot::ArenaDtor(void* object) {
  RotaryTicksSnapshot* _this = reinterpret_cast< RotaryTicksSnapshot* >(object);
  (void)_this;
}
void RotaryTicksSnapshot::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RotaryTicksSnapshot::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RotaryTicksSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.RotaryTicksSnapshot)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&timestamp_us_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&br_enabled_) -
      reinterpret_cast<char*>(&timestamp_us_)) + sizeof(br_enabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RotaryTicksSnapshot::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 timestamp_us = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_us_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 fl = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          fl_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 fr = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          fr_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 bl = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          bl_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 br = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          br_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool fl_enabled = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          fl_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool fr_enabled = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          fr_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool bl_enabled = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          bl_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool br_enabled = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          br_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RotaryTicksSnapshot::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.RotaryTicksSnapshot)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 timestamp_us = 1;
  if (this->_internal_timestamp_us() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_timestamp_us(), target);
  }

  // int32 fl = 2;
  if (this->_internal_fl() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_fl(), target);
  }

  // int32 fr = 3;
  if (this->_internal_fr() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_fr(), target);
  }

  // int32 bl = 4;
  if (this->_internal_bl() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_bl(), target);
  }

  // int32 br = 5;
  if (this->_internal_br() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_br(), target);
  }

  // bool fl_enabled = 6;
  if (this->_internal_fl_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_fl_enabled(), target);
  }

  // bool fr_enabled = 7;
  if (this->_internal_fr_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_fr_enabled(), target);
  }

  // bool bl_enabled = 8;
  if (this->_internal_bl_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_bl_enabled(), target);
  }

  // bool br_enabled = 9;
  if (this->_internal_br_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(9, this->_internal_br_enabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.RotaryTicksSnapshot)
  return target;
}

size_t RotaryTicksSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.RotaryTicksSnapshot)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 timestamp_us = 1;
  if (this->_internal_timestamp_us() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_timestamp_us());
  }

  // int32 fl = 2;
  if (this->_internal_fl() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_fl());
  }

  // int32 fr = 3;
  if (this->_internal_fr() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_fr());
  }

  // int32 bl = 4;
  if (this->_internal_bl() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_bl());
  }

  // int32 br = 5;
  if (this->_internal_br() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_br());
  }

  // bool fl_enabled = 6;
  if (this->_internal_fl_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool fr_enabled = 7;
  if (this->_internal_fr_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool bl_enabled = 8;
  if (this->_internal_bl_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool br_enabled = 9;
  if (this->_internal_br_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RotaryTicksSnapshot::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RotaryTicksSnapshot::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RotaryTicksSnapshot::GetClassData() const { return &_class_data_; }

void RotaryTicksSnapshot::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RotaryTicksSnapshot *>(to)->MergeFrom(
      static_cast<const RotaryTicksSnapshot &>(from));
}


void RotaryTicksSnapshot::MergeFrom(const RotaryTicksSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.RotaryTicksSnapshot)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timestamp_us() != 0) {
    _internal_set_timestamp_us(from._internal_timestamp_us());
  }
  if (from._internal_fl() != 0) {
    _internal_set_fl(from._internal_fl());
  }
  if (from._internal_fr() != 0) {
    _internal_set_fr(from._internal_fr());
  }
  if (from._internal_bl() != 0) {
    _internal_set_bl(from._internal_bl());
  }
  if (from._internal_br() != 0) {
    _internal_set_br(from._internal_br());
  }
  if (from._internal_fl_enabled() != 0) {
    _internal_set_fl_enabled(from._internal_fl_enabled());
  }
  if (from._internal_fr_enabled() != 0) {
    _internal_set_fr_enabled(from._internal_fr_enabled());
  }
  if (from._internal_bl_enabled() != 0) {
    _internal_set_bl_enabled(from._internal_bl_enabled());
  }
  if (from._internal_br_enabled() != 0) {
    _internal_set_br_enabled(from._internal_br_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RotaryTicksSnapshot::CopyFrom(const RotaryTicksSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.RotaryTicksSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RotaryTicksSnapshot::IsInitialized() const {
  return true;
}

void RotaryTicksSnapshot::InternalSwap(RotaryTicksSnapshot* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RotaryTicksSnapshot, br_enabled_)
      + sizeof(RotaryTicksSnapshot::br_enabled_)
      - PROTOBUF_FIELD_OFFSET(RotaryTicksSnapshot, timestamp_us_)>(
          reinterpret_cast<char*>(&timestamp_us_),
          reinterpret_cast<char*>(&other->timestamp_us_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RotaryTicksSnapshot::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[10]);
}

// ===================================================================

class RotaryTicksRecord::_Internal {
 public:
  static const ::recorder::RotaryTicksSnapshot& snapshot(const RotaryTicksRecord* msg);
};

const ::recorder::RotaryTicksSnapshot&
RotaryTicksRecord::_Internal::snapshot(const RotaryTicksRecord* msg) {
  return *msg->snapshot_;
}
RotaryTicksRecord::RotaryTicksRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.RotaryTicksRecord)
}
RotaryTicksRecord::RotaryTicksRecord(const RotaryTicksRecord& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_snapshot()) {
    snapshot_ = new ::recorder::RotaryTicksSnapshot(*from.snapshot_);
  } else {
    snapshot_ = nullptr;
  }
  record_timestamp_ms_ = from.record_timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:recorder.RotaryTicksRecord)
}

inline void RotaryTicksRecord::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&snapshot_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&record_timestamp_ms_) -
    reinterpret_cast<char*>(&snapshot_)) + sizeof(record_timestamp_ms_));
}

RotaryTicksRecord::~RotaryTicksRecord() {
  // @@protoc_insertion_point(destructor:recorder.RotaryTicksRecord)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RotaryTicksRecord::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete snapshot_;
}

void RotaryTicksRecord::ArenaDtor(void* object) {
  RotaryTicksRecord* _this = reinterpret_cast< RotaryTicksRecord* >(object);
  (void)_this;
}
void RotaryTicksRecord::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RotaryTicksRecord::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RotaryTicksRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.RotaryTicksRecord)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && snapshot_ != nullptr) {
    delete snapshot_;
  }
  snapshot_ = nullptr;
  record_timestamp_ms_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RotaryTicksRecord::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 record_timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          record_timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .recorder.RotaryTicksSnapshot snapshot = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_snapshot(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RotaryTicksRecord::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.RotaryTicksRecord)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_record_timestamp_ms(), target);
  }

  // .recorder.RotaryTicksSnapshot snapshot = 2;
  if (this->_internal_has_snapshot()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::snapshot(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.RotaryTicksRecord)
  return target;
}

size_t RotaryTicksRecord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.RotaryTicksRecord)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .recorder.RotaryTicksSnapshot snapshot = 2;
  if (this->_internal_has_snapshot()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *snapshot_);
  }

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_record_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RotaryTicksRecord::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RotaryTicksRecord::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RotaryTicksRecord::GetClassData() const { return &_class_data_; }

void RotaryTicksRecord::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RotaryTicksRecord *>(to)->MergeFrom(
      static_cast<const RotaryTicksRecord &>(from));
}


void RotaryTicksRecord::MergeFrom(const RotaryTicksRecord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.RotaryTicksRecord)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_snapshot()) {
    _internal_mutable_snapshot()->::recorder::RotaryTicksSnapshot::MergeFrom(from._internal_snapshot());
  }
  if (from._internal_record_timestamp_ms() != 0) {
    _internal_set_record_timestamp_ms(from._internal_record_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RotaryTicksRecord::CopyFrom(const RotaryTicksRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.RotaryTicksRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RotaryTicksRecord::IsInitialized() const {
  return true;
}

void RotaryTicksRecord::InternalSwap(RotaryTicksRecord* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RotaryTicksRecord, record_timestamp_ms_)
      + sizeof(RotaryTicksRecord::record_timestamp_ms_)
      - PROTOBUF_FIELD_OFFSET(RotaryTicksRecord, snapshot_)>(
          reinterpret_cast<char*>(&snapshot_),
          reinterpret_cast<char*>(&other->snapshot_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RotaryTicksRecord::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[11]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace recorder
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::recorder::DetectionClass* Arena::CreateMaybeMessage< ::recorder::DetectionClass >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::DetectionClass >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::DeepweedDetection* Arena::CreateMaybeMessage< ::recorder::DeepweedDetection >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::DeepweedDetection >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::TrackedItemCoordinates* Arena::CreateMaybeMessage< ::recorder::TrackedItemCoordinates >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::TrackedItemCoordinates >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::EmbeddingCategory* Arena::CreateMaybeMessage< ::recorder::EmbeddingCategory >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::EmbeddingCategory >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::TrackedItem* Arena::CreateMaybeMessage< ::recorder::TrackedItem >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::TrackedItem >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::CandidateShift* Arena::CreateMaybeMessage< ::recorder::CandidateShift >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::CandidateShift >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::DeepweedPredictionFrame* Arena::CreateMaybeMessage< ::recorder::DeepweedPredictionFrame >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::DeepweedPredictionFrame >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::DeepweedPredictionRecord* Arena::CreateMaybeMessage< ::recorder::DeepweedPredictionRecord >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::DeepweedPredictionRecord >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::LaneHeightSnapshot* Arena::CreateMaybeMessage< ::recorder::LaneHeightSnapshot >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::LaneHeightSnapshot >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::LaneHeightRecord* Arena::CreateMaybeMessage< ::recorder::LaneHeightRecord >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::LaneHeightRecord >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::RotaryTicksSnapshot* Arena::CreateMaybeMessage< ::recorder::RotaryTicksSnapshot >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::RotaryTicksSnapshot >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::RotaryTicksRecord* Arena::CreateMaybeMessage< ::recorder::RotaryTicksRecord >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::RotaryTicksRecord >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
