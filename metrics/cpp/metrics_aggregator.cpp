#include "metrics/cpp/metrics_aggregator.hpp"

#include <config/client/cpp/config_subscriber.hpp>
#include <hardware_manager/cpp/grpc_client.h>
#include <hardware_manager/proto/hardware_manager_service.pb.h>
#include <lib/common/cpp/utils/call_reducer.hpp>
#include <lib/common/cpp/utils/default_unordered_map.hpp>
#include <lib/common/cpp/utils/generation.hpp>
#include <lib/common/cpp/utils/multi_string_hash.hpp>
#include <lib/common/geometric/cpp/global_height_estimator_collection.hpp>
#include <lib/common/libgeohash/geohash.hpp>
#include <lib/common/redis/redis_client.hpp>
#include <metrics/cpp/p2p_accuracy.hpp>
#include <metrics/cpp/spatial/spatial_weed_counter.hpp>
#include <scanner/cpp/scanner_wrapper.h>
#include <shooting/cpp/laser_ontime_tracker.hpp>
#include <targeting/cpp/targeting_mode_watcher.hpp>
#include <trajectory/cpp/decisions.hpp>
#include <trajectory/cpp/enums.hpp>
#include <trajectory/cpp/trajectory.hpp>
#include <weed_tracking/cpp/ingest_clients/banding.hpp>
#include <weed_tracking/cpp/ingest_clients/diagnostics.hpp>

#include <chrono>
#include <sstream>
#include <string>

#include <boost/circular_buffer.hpp>
#include <prometheus/counter.h>
#include <prometheus/exposer.h>
#include <prometheus/gauge.h>
#include <prometheus/histogram.h>
#include <prometheus/registry.h>
#include <spdlog/spdlog.h>

namespace carbon {
namespace metrics {

constexpr int kMetricsListenPort = 62108;
constexpr int geohashSize = 5;
constexpr std::string_view weed_kill_count_total_key = "weed_tracking/{}/weed_kill_count_total";
constexpr std::string_view crop_kill_count_total_key = "weed_tracking/{}/crop_kill_count_total";

class RollingWindowMetric {
private:
  prometheus::Gauge &gauge_;
  boost::circular_buffer<double> data_;
  double sum_;

public:
  RollingWindowMetric(prometheus::Gauge &gauge, size_t window_size) : gauge_(gauge), data_(window_size), sum_(0.0) {}
  void update_metric() { gauge_.Set(sum_ / ((double)data_.size())); }
  void add(double value, bool update = false) {
    if (data_.full()) {
      sum_ -= data_.front();
      data_.pop_front();
    }
    data_.push_back(value);
    sum_ += value;
    if (update) {
      update_metric();
    }
  }
};
class LaserTimeMetric {
private:
  prometheus::Family<prometheus::Gauge> &laser_time_family_;
  size_t window_size_;
  std::unordered_map<std::string, RollingWindowMetric> actual_shoot_times_;
  std::unordered_map<std::string, RollingWindowMetric> almanac_shoot_times_;

public:
  LaserTimeMetric(prometheus::Registry &registry, size_t window_size = 100u)
      : laser_time_family_(prometheus::BuildGauge()
                               .Name("laser_time_by_class")
                               .Help("Average laser time per category.")
                               .Register(registry)),
        window_size_(window_size) {}
  void add_almanac_time(const std::string &plant_id, double time) {
    auto it = almanac_shoot_times_.find(plant_id);
    if (it == almanac_shoot_times_.end()) {
      auto resp = almanac_shoot_times_.emplace(
          plant_id,
          RollingWindowMetric(laser_time_family_.Add({{"type", "almanac"}, {"class", plant_id}}), window_size_));
      if (!resp.second) {
        spdlog::error("Failed to add metric to map.");
        return;
      }
      it = resp.first;
    }
    it->second.add(time);
  }
  void add_actual_time(const std::string &plant_id, double time) {
    auto it = actual_shoot_times_.find(plant_id);
    if (it == actual_shoot_times_.end()) {
      auto resp = actual_shoot_times_.emplace(
          plant_id,
          RollingWindowMetric(laser_time_family_.Add({{"type", "actual"}, {"class", plant_id}}), window_size_));
      if (!resp.second) {
        spdlog::error("Failed to add metric to map.");
        return;
      }
      it = resp.first;
    }
    it->second.add(time);
  }
  void update() {
    for (auto &it : almanac_shoot_times_) {
      it.second.update_metric();
    }
    for (auto &it : actual_shoot_times_) {
      it.second.update_metric();
    }
  }
};

RollingConclusionCounter::RollingConclusionCounter(std::function<uint64_t()> rolling_duration_s_func)
    : rolling_duration_s_cache_(0), rolling_duration_cache_timestamp_ms_(0),
      rolling_duration_s_func_(rolling_duration_s_func), cached_counter_timestamp_ms_(0) {
  for (int i = 0; i < (int)ConclusionType::kConclusionTypeNumber; i++) {
    active_counter_.counters[i] = 0;
    cached_counter_.counters[i] = 0;
  }
};

ConclusionCounter RollingConclusionCounter::get_conclusion_counter() {
  std::lock_guard<std::mutex> lock(cached_counter_mutex_);
  return cached_counter_; // Intentional pass by copy
};

void RollingConclusionCounter::add_conclusion(ConclusionType type) {
  uint64_t timestamp_ms = maka_control_timestamp_ms();

  if (timestamp_ms - rolling_duration_cache_timestamp_ms_ > 60000) {
    rolling_duration_cache_timestamp_ms_ = timestamp_ms;
    rolling_duration_s_cache_ = rolling_duration_s_func_();
  }

  // Remove Old Items
  while (rolling_buffer_.size() > 0 &&
         (timestamp_ms - rolling_buffer_.front().timestamp_ms > (rolling_duration_s_cache_ * 1000))) {
    // The else of this should never happen, but just in case
    if (active_counter_.counters[(int)rolling_buffer_.front().type] != 0) {
      active_counter_.counters[(int)rolling_buffer_.front().type] -= 1;
    }
    rolling_buffer_.pop();
  }

  active_counter_.counters[(int)type] += 1;
  rolling_buffer_.push(TimestampedConclusion{type, timestamp_ms});

  if (timestamp_ms - cached_counter_timestamp_ms_ > 1000) {
    {
      std::lock_guard<std::mutex> lock(cached_counter_mutex_);
      cached_counter_ = active_counter_;
    }
    std::ostringstream ss;
    for (size_t i = 0; i < (size_t)ConclusionType::kConclusionTypeNumber; ++i) {
      ss << "\n" << conclusion_type_to_string((ConclusionType)i) << " " << active_counter_.counters[i];
    }
    spdlog::debug(ss.str());
    cached_counter_timestamp_ms_ = timestamp_ms;
  }
};

ConclusionCounter MetricsAggregator::get_conclusion_counter() { return conclusion_counter_->get_conclusion_counter(); }

MetricsAggregator::MetricsAggregator()
    : FilteredIngestClient(false, true), registry_(std::make_shared<prometheus::Registry>()),
      plant_conclusion_counter_(nullptr) {
  auto rolling_duration_node = carbon::config::get_global_config_subscriber()->get_config_node(
      "common", "metrics/conclusions/rolling_duration_s");
  conclusion_counter_ = std::make_unique<RollingConclusionCounter>(
      [rolling_duration_node]() { return rolling_duration_node->get_value<uint64_t>(); });

  for (auto &scanner : scanner::ScannerWrapperOwner::get().get_scanners()) {
    scheduling_failures_.try_emplace(scanner.first,
                                     static_cast<size_t>(SchedulingFailureType::kSchedulingFailureTypeCount));
    for (size_t i = 0; i < scheduling_failures_[scanner.first].size(); ++i) {
      scheduling_failures_[scanner.first][i] = 0;
    }
  }
}

MetricsAggregator::~MetricsAggregator() { metrics_report_thread_.join(); }

void MetricsAggregator::start() { metrics_report_thread_ = std::thread(&MetricsAggregator::report_loop, this); }

void MetricsAggregator::_trajectory_req(const ingest::Request<> &req) {
  // We only listen to deletes so don't need to know req type
  intake_.add(req.traj);
}

std::shared_ptr<MetricsAggregator> MetricsAggregator::get() {
  static std::shared_ptr<MetricsAggregator> inst{new MetricsAggregator()};
  return inst;
}

std::string weed_kill_count_by_class_key(const std::string &row_id, const std::string &clz) {
  return fmt::format("weed_tracking/{}/class/{}/weed_kill_count_total", row_id, clz);
}

void upgrade_metrics_fix(lib::common::RedisClient &redis, const std::string &row_id) {
  auto upgrade_done = redis.get_long_def(fmt::format("weed_tracking/{}/weed_kill_count_upgrade_1.9_done", row_id), 0);
  if (upgrade_done == 1) {
    return;
  }

  int64_t weed_kill_count_total = redis.get_long_def(fmt::format(weed_kill_count_total_key, row_id), 0);
  int64_t weed_kill_count_purslane = redis.get_long_def(weed_kill_count_by_class_key(row_id, "PURSLANE"), 0);
  int64_t weed_kill_count_broadleaf = redis.get_long_def(weed_kill_count_by_class_key(row_id, "BROADLEAF"), 0);
  int64_t weed_kill_count_grass = redis.get_long_def(weed_kill_count_by_class_key(row_id, "GRASS"), 0);
  int64_t weed_kill_count_offshoot = redis.get_long_def(weed_kill_count_by_class_key(row_id, "OFFSHOOT"), 0);
  int64_t weed_kill_count_crown = redis.get_long_def(weed_kill_count_by_class_key(row_id, "CROWN"), 0);

  if (weed_kill_count_total > 0 && weed_kill_count_crown == 0 && weed_kill_count_offshoot == 0 &&
      weed_kill_count_grass == 0 && weed_kill_count_broadleaf == 0 && weed_kill_count_purslane == 0) {
    // total reported so far = 10M for crown and 41m for everyone else, total=174M
    redis.set_long_safe(weed_kill_count_by_class_key(row_id, "PURSLANE"), (long)weed_kill_count_total * 41 / 174);
    redis.set_long_safe(weed_kill_count_by_class_key(row_id, "BROADLEAF"), (long)weed_kill_count_total * 41 / 174);
    redis.set_long_safe(weed_kill_count_by_class_key(row_id, "GRASS"), (long)weed_kill_count_total * 41 / 174);
    redis.set_long_safe(weed_kill_count_by_class_key(row_id, "OFFSHOOT"), (long)weed_kill_count_total * 41 / 174);
    redis.set_long_safe(weed_kill_count_by_class_key(row_id, "CROWN"), (long)weed_kill_count_total * 10 / 174);
  }

  redis.set_long_safe(fmt::format("weed_tracking/{}/weed_kill_count_upgrade_1.9_done", row_id), 1);
}
ConclusionType get_conclusion_type(trajectory::Trajectory *traj, bool is_weed, bool estopped) {
  if (!targeting::TargetingModeWatcher::get().targeting_enabled()) {
    if (!traj->is_reachable() && traj->timed_out()) {
      return ConclusionType::kFlicker; // since we filter flicker out still need even if not weeding
    }
    return ConclusionType::kNotWeeding;
  }
  if (estopped) {
    return ConclusionType::kNotWeeding;
  }
  ConclusionType type;
  switch (traj->kill_status()) {
  case trajectory::TrackedItemKillStatus::kNotShot:
    type = ConclusionType::kNotShot;
    break;
  case trajectory::TrackedItemKillStatus::kBeingShot:
  case trajectory::TrackedItemKillStatus::kPartiallyShot:
    type = ConclusionType::kPartiallyShot;
    break;
  case trajectory::TrackedItemKillStatus::kShot:
    type = ConclusionType::kShot;
    break;
  case trajectory::TrackedItemKillStatus::kP2PNotFound:
    type = ConclusionType::kP2PNotFound;
    break;
  case trajectory::TrackedItemKillStatus::kP2PMissingContext:
    type = ConclusionType::kP2PMissingContext;
    break;
  case trajectory::TrackedItemKillStatus::kError:
    type = ConclusionType::kError;
    break;
  default:
    type = ConclusionType::kError;
    break;
  }
  if (type != ConclusionType::kShot) {
    if (!is_weed) {
      if (traj->is_marked_for_thinning()) {
        if (!traj->is_reachable()) {
          type = traj->timed_out() ? ConclusionType::kFlicker : ConclusionType::kOutOfRange;
        } else {
          type = ConclusionType::kMarkedForThinning;
        }
      } else if (traj->banding_state() == weed_tracking::OutOfBand) {
        type = ConclusionType::kOutOfBand;
      } else {
        type = ConclusionType::kNotTargeted;
      }
    } else {
      if (!targeting::TargetingModeWatcher::get().weeding_enabled() || traj->avoid()) {
        type = ConclusionType::kNotTargeted;
      } else {
        if (!traj->is_reachable() && traj->timed_out()) {
          return ConclusionType::kFlicker;
        } else if (traj->banding_state() == weed_tracking::OutOfBand) {
          type = ConclusionType::kOutOfBand;
        } else if (traj->intersected_with_nonshootable()) {
          type = ConclusionType::kIntersectsWithNonShootable;
        } else if (!traj->is_reachable()) {
          type = ConclusionType::kOutOfRange;
        } else if (traj->ignorable()) {
          type = ConclusionType::kUnimportant;
        }
      }
    }
  } else {
    if (traj->intersected_with_nonshootable()) {
      spdlog::warn("Investigation required: Trajectory {} was shot but marked as unshootable.", traj->id());
    }
  }
  return type;
}
void MetricsAggregator::report_loop() {
  char *host_ptr = std::getenv("HOSTNAME");
  std::string host;
  if (host_ptr != NULL) {
    host += host_ptr;
  }
  if (host != "aimbot") {
    spdlog::info("MetricsAggregator running within {} which is not aimbot, exiting", host);
    return;
  }

  char *row_id_ptr = std::getenv("MAKA_ROW");
  std::string row_id = "row";
  if (row_id_ptr != NULL) {
    row_id += row_id_ptr;
  }

  char *gen_ptr = std::getenv("MAKA_GEN");
  std::string gen;
  if (gen_ptr != NULL) {
    gen += gen_ptr;
  }

  using namespace prometheus;
  using namespace carbon::config;
  using namespace std::chrono_literals;

  // wait until boards booted, command computer is up, etc
  std::this_thread::sleep_for(10000ms);

  std::string command_addr = make_robot_local_addr(61006);
  auto hw_client = hardware_manager::HardwareManagerClient(command_addr);
  // starts metrics server
  Exposer exposer(fmt::format("0.0.0.0:{}", kMetricsListenPort));
  P2PAccuracy::get().start(registry_);

  auto &crop_gps = prometheus::BuildGauge().Name("crop_gps").Help("crop count in given area").Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::array<std::string, 2>, std::reference_wrapper<prometheus::Gauge>,
                                      carbon::common::MultiStringHash<2>>
      crop_gps_labels_map(
          [&crop_gps](const std::array<std::string, 2> &keys) -> std::reference_wrapper<prometheus::Gauge> {
            return crop_gps.Add({{"geohash", keys[0]}, {"class", keys[1]}});
          });
  auto &weed_gps = prometheus::BuildGauge().Name("weed_gps").Help("weed count in given area").Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::array<std::string, 2>, std::reference_wrapper<prometheus::Gauge>,
                                      carbon::common::MultiStringHash<2>>
      weed_gps_labels_map(
          [&weed_gps](const std::array<std::string, 2> &keys) -> std::reference_wrapper<prometheus::Gauge> {
            return weed_gps.Add({{"geohash", keys[0]}, {"class", keys[1]}});
          });
  auto &weed_kill_count_gauge =
      prometheus::BuildGauge().Name("weed_kill_count").Help("num of weeds killed").Register(*registry_).Add({});
  auto &crop_kill_count_gauge =
      prometheus::BuildGauge().Name("crop_kill_count").Help("num of crops killed").Register(*registry_).Add({});
  auto &weed_kill_count_today_gauge = prometheus::BuildGauge()
                                          .Name("weed_kill_count_today")
                                          .Help("num of weeds killed per day")
                                          .Register(*registry_)
                                          .Add({});
  auto &weed_kill_count_by_class_gauge = prometheus::BuildGauge()
                                             .Name("weed_kill_count_by_class")
                                             .Help("num of weeds killed by weed type")
                                             .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::string, std::reference_wrapper<prometheus::Gauge>>
      weed_kill_count_by_class_labels_map([&weed_kill_count_by_class_gauge](const std::string &plant_class)
                                              -> std::reference_wrapper<prometheus::Gauge> {
        return weed_kill_count_by_class_gauge.Add({{"class", plant_class}});
      });
  auto redis = lib::common::RedisClient();
  carbon::common::DefaultUnorderedMap<std::string, uint64_t> weed_kill_count_by_class(
      [&redis, row_id](const std::string &plant_class) -> uint64_t {
        return redis.get_long_def(weed_kill_count_by_class_key(row_id, plant_class), 0);
      });

  auto &scanner_start_pos =
      prometheus::BuildGauge().Name("scanner_start_pos").Help("The tilt starting position 0-1").Register(*registry_);
  carbon::common::DefaultUnorderedMap<uint32_t, std::reference_wrapper<prometheus::Gauge>> scanner_start_labels_map(
      [&scanner_start_pos](uint32_t id) -> std::reference_wrapper<prometheus::Gauge> {
        return scanner_start_pos.Add({{"scanner", scanner::ScannerWrapper::make_identifier(id)}});
      });

  auto &scheduler_failures_family = prometheus::BuildGauge()
                                        .Name("scheduler_failures")
                                        .Help("Count of scheduler failures by type and scanner")
                                        .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::array<std::string, 2>, std::reference_wrapper<prometheus::Gauge>,
                                      carbon::common::MultiStringHash<2>>
      scheduler_failures_map([&scheduler_failures_family](
                                 const std::array<std::string, 2> &keys) -> std::reference_wrapper<prometheus::Gauge> {
        return scheduler_failures_family.Add({{"scanner", keys[0]}, {"error_type", keys[1]}});
      });

  auto &weed_category_switch_family = prometheus::BuildGauge()
                                          .Name("weed_category_switch")
                                          .Help("Count of weeds that switched categories")
                                          .Register(*registry_);
  carbon::common::DefaultUnorderedMap<bool, std::reference_wrapper<prometheus::Gauge>> weed_category_switch_map(
      [&weed_category_switch_family](bool switched) -> std::reference_wrapper<prometheus::Gauge> {
        return weed_category_switch_family.Add({{"switched", switched ? "true" : "false"}});
      });

  auto &decisions_set_family =
      prometheus::BuildCounter().Name("decision_flags_set").Help("Count of decision flags set").Register(*registry_);
  carbon::common::DefaultUnorderedMap<bool, std::reference_wrapper<prometheus::Counter>> decisions_set_map(
      [&decisions_set_family](bool set) -> std::reference_wrapper<prometheus::Counter> {
        return decisions_set_family.Add({{"set", set ? "true" : "false"}});
      });

  auto &decisions_count_family =
      prometheus::BuildCounter().Name("decision_flags_count").Help("Count of decision flags").Register(*registry_);
  carbon::common::DefaultUnorderedMap<trajectory::Decisions, std::reference_wrapper<prometheus::Counter>,
                                      trajectory::DecisionsHash>
      decisions_count_map(
          [&decisions_count_family](const trajectory::Decisions &flags) -> std::reference_wrapper<prometheus::Counter> {
            return decisions_count_family.Add(
                {{"weeding_weed", flags[trajectory::DecisionFlag::kWeedingWeed] ? "true" : "false"},
                 {"weeding_crop", flags[trajectory::DecisionFlag::kWeedingCrop] ? "true" : "false"},
                 {"thinning_weed", flags[trajectory::DecisionFlag::kThinningWeed] ? "true" : "false"},
                 {"thinning_crop", flags[trajectory::DecisionFlag::kThinningCrop] ? "true" : "false"},
                 {"keepable_crop", flags[trajectory::DecisionFlag::kKeepableCrop] ? "true" : "false"},
                 {"banding_crop", flags[trajectory::DecisionFlag::kBandingCrop] ? "true" : "false"}});
          });

  auto &diagnostics_latency_guage = prometheus::BuildGauge()
                                        .Name("diagnostics_latency_ms")
                                        .Help("Average latency of the diagnostics work loop")
                                        .Register(*registry_)
                                        .Add({});

  auto &diagnostics_snapshot_job_latency_gauge = prometheus::BuildGauge()
                                                     .Name("diagnostics_snapshot_job_latency_ns")
                                                     .Help("Average latency of the diagnostics snapshot jobs")
                                                     .Register(*registry_)
                                                     .Add({});

  auto config_subscriber = get_global_config_subscriber();
  auto bucket_node = config_subscriber->get_config_node("common", "almanac/size_categories");
  auto &weed_count =
      prometheus::BuildHistogram().Name("weed_count").Help("Total number of weeds seen").Register(*registry_);
  auto weed_count_bucket_boundaries = prometheus::Histogram::BucketBoundaries{};
  for (auto node : bucket_node->get_children_nodes()) {
    double b = (double)node->get_node("max")->get_value<float>();
    if (std::count(weed_count_bucket_boundaries.begin(), weed_count_bucket_boundaries.end(), b) == 0) {
      weed_count_bucket_boundaries.push_back(b);
    }
  }
  if (weed_count_bucket_boundaries.size() == 0) {
    weed_count_bucket_boundaries.push_back(1);
    weed_count_bucket_boundaries.push_back(3);
    weed_count_bucket_boundaries.push_back(5);
    weed_count_bucket_boundaries.push_back(10);
  } else {
    sort(weed_count_bucket_boundaries.begin(), weed_count_bucket_boundaries.end());
  }
  carbon::common::DefaultUnorderedMap<std::array<std::string, 2>, std::reference_wrapper<prometheus::Histogram>,
                                      carbon::common::MultiStringHash<2>>
      weed_count_labels_map([&weed_count, weed_count_bucket_boundaries](const std::array<std::string, 2> &keys)
                                -> std::reference_wrapper<prometheus::Histogram> {
        return weed_count.Add({{"kill_status", keys[0]}, {"class", keys[1]}}, weed_count_bucket_boundaries);
      });

  auto &item_height =
      prometheus::BuildHistogram().Name("item_height_mm").Help("Height of items seen").Register(*registry_);
  auto item_height_bucket_boundaries = prometheus::Histogram::BucketBoundaries{};
  // let's do 50 mm buckets between 550 and 1200 (covers 23 to 46 inches)
  for (int i = 550; i <= 1200; i += 50) {
    item_height_bucket_boundaries.push_back(i);
  }
  carbon::common::DefaultUnorderedMap<std::string, std::reference_wrapper<prometheus::Histogram>> item_height_map(
      [&item_height,
       item_height_bucket_boundaries](const std::string &hit_class) -> std::reference_wrapper<prometheus::Histogram> {
        return item_height.Add({{"hit_class", hit_class}}, item_height_bucket_boundaries);
      });

  auto &item_size_variation = prometheus::BuildHistogram()
                                  .Name("item_size_variation")
                                  .Help("Relative height variation (standard deviation)")
                                  .Register(*registry_);
  auto item_size_variation_boundaries = prometheus::Histogram::BucketBoundaries{};

  for (int i = 0; i <= 200; i += 10) {
    item_size_variation_boundaries.push_back(i);
  }
  carbon::common::DefaultUnorderedMap<std::string, std::reference_wrapper<prometheus::Histogram>>
      item_size_variation_map([&item_size_variation, item_size_variation_boundaries](
                                  const std::string &plant_id) -> std::reference_wrapper<prometheus::Histogram> {
        return item_size_variation.Add({{"plant_id", plant_id}}, item_size_variation_boundaries);
      });

  auto &detection_over_opportunity_percentage = prometheus::BuildHistogram()
                                                    .Name("item_detection_over_opportunity")
                                                    .Help("Number of detections over number of opportunities")
                                                    .Register(*registry_);
  auto detection_over_opportunity_percentage_bucket_boundaries = prometheus::Histogram::BucketBoundaries{};

  for (float i = 0.0f; i <= 1.0f; i += 0.05f) {
    detection_over_opportunity_percentage_bucket_boundaries.push_back(i);
  }
  carbon::common::DefaultUnorderedMap<std::string, std::reference_wrapper<prometheus::Histogram>>
      detection_over_opportunity_percentage_map(
          [&detection_over_opportunity_percentage, detection_over_opportunity_percentage_bucket_boundaries](
              const std::string &plant_id) -> std::reference_wrapper<prometheus::Histogram> {
            return detection_over_opportunity_percentage.Add({{"plant_id", plant_id}},
                                                             detection_over_opportunity_percentage_bucket_boundaries);
          });

  auto &crops_in_band_percent = prometheus::BuildGauge()
                                    .Name("crops_in_band_percent")
                                    .Help("crops in band percentage")
                                    .Register(*registry_)
                                    .Add({});
  auto crops_in_band_percent_metric = RollingWindowMetric(crops_in_band_percent, 100);

  auto &scheduler_frame_time = prometheus::BuildGauge()
                                   .Name("scheduler_frame_time")
                                   .Help("Avg time [ms] to run scheduler algorithm")
                                   .Register(*registry_)
                                   .Add({});

  auto &tracker_latency = prometheus::BuildGauge()
                              .Name("weed_tracker_latency")
                              .Help("Avg latency [ms] from image ts to getting deepweed in aimbot")
                              .Register(*registry_);
  auto &tracker_processing_time = prometheus::BuildGauge()
                                      .Name("weed_tracker_processing_time")
                                      .Help("Avg time [ms] to process a deepweed output")
                                      .Register(*registry_);
  auto &weed_tracker_deepweed_output_processed_count_family =
      prometheus::BuildCounter()
          .Name("weed_tracker_deepweed_output_processed_count")
          .Help("Number of deepweed outputs processed by weed tracker")
          .Register(*registry_);
  auto &weed_tracker_deepweed_output_timeout_count_family =
      prometheus::BuildCounter()
          .Name("weed_tracker_deepweed_output_timeout_count")
          .Help("Number of deepweed outputs that timed out in weed tracker")
          .Register(*registry_);
  auto &weed_tracker_deepweed_output_error_count_family =
      prometheus::BuildCounter()
          .Name("weed_tracker_deepweed_output_error_count")
          .Help("Number of deepweed outputs that errored in weed tracker")
          .Register(*registry_);
  auto &tracker_height_estimation_family =
      prometheus::BuildGauge()
          .Name("tracker_height_estimation")
          .Help("Height estimation per tracker, uning the global height estimators")
          .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::array<std::string, 3>, std::reference_wrapper<prometheus::Gauge>,
                                      carbon::common::MultiStringHash<3>>
      tracker_height_estimation_map([&tracker_height_estimation_family](const std::array<std::string, 3> &keys)
                                        -> std::reference_wrapper<prometheus::Gauge> {
        return tracker_height_estimation_family.Add({{"tracker_id", keys[0]}, {"bucket", keys[1]}, {"lane", keys[2]}});
      });

  auto failure_count_buckets = prometheus::Histogram::BucketBoundaries{};
  for (int i = 0; i < 11; ++i) {
    failure_count_buckets.push_back(i);
  }
  auto &extermination_failure_counts_family = prometheus::BuildHistogram()
                                                  .Name("extermination_failure_counts")
                                                  .Help("Histogram of number of extermination failures")
                                                  .Register(*registry_);
  carbon::common::DefaultUnorderedMap<ConclusionType, std::reference_wrapper<prometheus::Histogram>>
      extermination_failure_counts_map([&extermination_failure_counts_family, failure_count_buckets](
                                           ConclusionType type) -> std::reference_wrapper<prometheus::Histogram> {
        return extermination_failure_counts_family.Add({{"conclusion", conclusion_type_to_string(type)}},
                                                       failure_count_buckets);
      });

  auto &speculative_offset_family = prometheus::BuildGauge()
                                        .Name("speculative_offsets")
                                        .Help("Offsets calculated from p2p for speculative shooting")
                                        .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::array<std::string, 5>, std::reference_wrapper<prometheus::Gauge>,
                                      carbon::common::MultiStringHash<5>>
      speculative_offset_map([&speculative_offset_family](
                                 const std::array<std::string, 5> &keys) -> std::reference_wrapper<prometheus::Gauge> {
        return speculative_offset_family.Add(
            {{"scanner", keys[0]}, {"pcam", keys[1]}, {"grid_x", keys[2]}, {"grid_y", keys[3]}, {"axis", keys[4]}});
      });
  auto &speculative_percentile_family = prometheus::BuildGauge()
                                            .Name("speculative_percentile")
                                            .Help("What percent of previous first p2ps are in range")
                                            .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::array<std::string, 4>, std::reference_wrapper<prometheus::Gauge>,
                                      carbon::common::MultiStringHash<4>>
      speculative_percentile_map([&speculative_percentile_family](const std::array<std::string, 4> &keys)
                                     -> std::reference_wrapper<prometheus::Gauge> {
        return speculative_percentile_family.Add(
            {{"scanner", keys[0]}, {"pcam", keys[1]}, {"grid_x", keys[2]}, {"grid_y", keys[3]}});
      });

  auto &detection_to_trajectory_latency =
      prometheus::BuildGauge()
          .Name("detection_to_trajectory_latency_ms")
          .Help("Latency between when items are first detected by deepweed to when they're entered into the tracker")
          .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::string,
                                      std::pair<std::reference_wrapper<prometheus::Gauge>, RollingWindowMetric>>
      detection_to_trajectory_latency_map([&detection_to_trajectory_latency](const std::string &pcam)
                                              -> std::pair<std::reference_wrapper<prometheus::Gauge>,
                                                           RollingWindowMetric> {
        std::reference_wrapper<prometheus::Gauge> metric(detection_to_trajectory_latency.Add({{"tracker_id", pcam}}));
        return {metric, RollingWindowMetric(metric, 100)};
      });

  auto &first_to_last_detection_latency =
      prometheus::BuildGauge()
          .Name("first_detection_to_last_detection_ms")
          .Help("Latency between the first and last deepweed detection on a trajectory")
          .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::string,
                                      std::pair<std::reference_wrapper<prometheus::Gauge>, RollingWindowMetric>>
      first_to_last_detection_latency_map([&first_to_last_detection_latency](const std::string &pcam)
                                              -> std::pair<std::reference_wrapper<prometheus::Gauge>,
                                                           RollingWindowMetric> {
        std::reference_wrapper<prometheus::Gauge> metric(first_to_last_detection_latency.Add({{"tracker_id", pcam}}));
        return {metric, RollingWindowMetric(metric, 100)};
      });

  auto &trajectory_to_deduplication_latency =
      prometheus::BuildGauge()
          .Name("trajectory_creation_to_deduplication_latency_ms")
          .Help("Latency between trajectory creation and cross-cam deduplication")
          .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::string,
                                      std::pair<std::reference_wrapper<prometheus::Gauge>, RollingWindowMetric>>
      trajectory_to_deduplication_latency_map(
          [&trajectory_to_deduplication_latency](
              const std::string &pcam) -> std::pair<std::reference_wrapper<prometheus::Gauge>, RollingWindowMetric> {
            std::reference_wrapper<prometheus::Gauge> metric(
                trajectory_to_deduplication_latency.Add({{"tracker_id", pcam}}));
            return {metric, RollingWindowMetric(metric, 100)};
          });

  auto &trajectory_to_arbitration_latency = prometheus::BuildGauge()
                                                .Name("trajectory_creation_to_arbitration_latency_ms")
                                                .Help("Latency between trajectory creation and arbitration")
                                                .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::string,
                                      std::pair<std::reference_wrapper<prometheus::Gauge>, RollingWindowMetric>>
      trajectory_to_arbitration_latency_map([&trajectory_to_arbitration_latency](const std::string &pcam)
                                                -> std::pair<std::reference_wrapper<prometheus::Gauge>,
                                                             RollingWindowMetric> {
        std::reference_wrapper<prometheus::Gauge> metric(trajectory_to_arbitration_latency.Add({{"tracker_id", pcam}}));
        return {metric, RollingWindowMetric(metric, 100)};
      });

  auto &trajectory_to_thinning_latency = prometheus::BuildGauge()
                                             .Name("trajectory_creation_to_thinning_latency_ms")
                                             .Help("Latency between trajectory creation and thinning decision")
                                             .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::string,
                                      std::pair<std::reference_wrapper<prometheus::Gauge>, RollingWindowMetric>>
      trajectory_to_thinning_latency_map([&trajectory_to_thinning_latency](const std::string &pcam)
                                             -> std::pair<std::reference_wrapper<prometheus::Gauge>,
                                                          RollingWindowMetric> {
        std::reference_wrapper<prometheus::Gauge> metric(trajectory_to_thinning_latency.Add({{"tracker_id", pcam}}));
        return {metric, RollingWindowMetric(metric, 100)};
      });

  auto &arbiter_algorithm_gauge = prometheus::BuildGauge()
                                      .Name("aimbot_last_arbiter_algorithm_used_time")
                                      .Help("Time of the last use of each arbiter algorithm")
                                      .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::string, std::reference_wrapper<prometheus::Gauge>>
      arbiter_algorithm_gauge_map_([&arbiter_algorithm_gauge, &row_id](
                                       const std::string &algorithm) -> std::reference_wrapper<prometheus::Gauge> {
        return arbiter_algorithm_gauge.Add({{"row_id", row_id}, {"algorithm", algorithm}});
      });

  auto thinning_state_to_string = [](trajectory::ThinningState state) {
    switch (state) {
    case trajectory::ThinningState::kUnset:
      return "unset";
    case trajectory::ThinningState::kMarkedForThinning:
      return "marked_for_thinning";
    case trajectory::ThinningState::kKept:
      return "kept";
    case trajectory::ThinningState::kIgnored:
      return "ignored";
    default:
      return "unknown";
    }
  };
  auto &thinning_decision_gauge = prometheus::BuildCounter()
                                      .Name("aimbot_thinning_decisions")
                                      .Help("Number of thinning decisions by type")
                                      .Register(*registry_);
  carbon::common::DefaultUnorderedMap<std::string, std::reference_wrapper<prometheus::Counter>>
      thinning_decision_gauge_map_(
          [&thinning_decision_gauge](const std::string &state) -> std::reference_wrapper<prometheus::Counter> {
            return thinning_decision_gauge.Add({{"state", state}});
          });

  struct TrackerMetrics {
    std::shared_ptr<weed_tracking::AimbotWeedTracker> tracker;
    std::reference_wrapper<prometheus::Gauge> latency;
    std::reference_wrapper<prometheus::Gauge> processing_time;
    std::reference_wrapper<prometheus::Counter> deepweed_output_processed_count;
    std::reference_wrapper<prometheus::Counter> deepweed_output_timeout_count;
    std::reference_wrapper<prometheus::Counter> deepweed_output_error_count;
    TrackerMetrics(std::shared_ptr<weed_tracking::AimbotWeedTracker> _tracker, prometheus::Gauge &_latency,
                   prometheus::Gauge &_processing_time, prometheus::Counter &_deepweed_output_processed_count,
                   prometheus::Counter &_deepweed_output_timeout_count,
                   prometheus::Counter &_deepweed_output_error_count)
        : tracker(_tracker), latency(_latency), processing_time(_processing_time),
          deepweed_output_processed_count(_deepweed_output_processed_count),
          deepweed_output_timeout_count(_deepweed_output_timeout_count),
          deepweed_output_error_count(_deepweed_output_error_count) {}
  };
  std::vector<TrackerMetrics> tracker_metrics;
  for (const auto &pair : weed_tracking::AimbotTrackerOwner::get().get_trackers()) {
    tracker_metrics.emplace_back(
        pair.second, tracker_latency.Add({{"tracker_id", pair.second->get_pcam_id()}}),
        tracker_processing_time.Add({{"tracker_id", pair.second->get_pcam_id()}}),
        weed_tracker_deepweed_output_processed_count_family.Add({{"tracker_id", pair.second->get_pcam_id()}}),
        weed_tracker_deepweed_output_timeout_count_family.Add({{"tracker_id", pair.second->get_pcam_id()}}),
        weed_tracker_deepweed_output_error_count_family.Add({{"tracker_id", pair.second->get_pcam_id()}}));
  }
  redis.wait_until_ready();
  upgrade_metrics_fix(redis, row_id);
  int64_t weed_kill_count_total = redis.get_long_def(fmt::format(weed_kill_count_total_key, row_id), 0);
  int64_t crop_kill_count_total = redis.get_long_def(fmt::format(crop_kill_count_total_key, row_id), 0);

  spdlog::info("MetricsAggregator: read weed kill count total={}, crop kill count total = {} from redis",
               weed_kill_count_total, crop_kill_count_total);
  auto last_redis_report = std::chrono::system_clock::now();
  common::CallReducer<std::chrono::seconds> set_kill_count_gauge(std::chrono::seconds(15), [&]() {
    weed_kill_count_today_gauge.Set(plant_conclusion_counter_.get_weeds_shot_today());
  });
  set_kill_count_gauge();

  exposer.RegisterCollectable(registry_);

  std::string geohash;
  try {
    auto geo = hw_client.get_gps_data();
    auto geo_lla = geo.lla();
    cgeohash::encode(geo_lla.lat(), geo_lla.lng(), geohashSize, geohash);
  } catch (const std::exception &e) {
    spdlog::warn("MetricsAggregator: can't get GPS data, assuming America/Los_Angeles, error: {}", e.what());
    cgeohash::encode(47.62684451147215, -122.34409499118749, geohashSize, geohash);
  }

  LaserTimeMetric laser_time_metric(*registry_);
  int64_t last_estop_check_ts = 0;
  int64_t estop_check_interval_ms = 1000;
  bool estopped = false;
  std::string arbiter_algorithm = "";
  while (true) {
    try {
      auto redis_report_interval_sec =
          config_subscriber->get_config_node("common", "weeding_metrics/kill_count_persist_interval_sec")
              ->get_value<uint32_t>();

      for (auto traj : intake_.wait_pop_all(1000)) { // updates will happen every 1000ms or when new removes come in
        if (traj->duplicate_status() == trajectory::DuplicateStatus::kDuplicate) {
          traj->mark_concluded();
          continue;
        }

        decisions_set_map[traj->decisions_set()].get().Increment();

        if (!traj->decisions_set()) {
          continue;
        }

        arbiter_algorithm = traj->decision_algorithm();

        decisions_count_map[traj->get_decisions()].get().Increment();

        // TODO(jfroel) metrics for upt weirdness
        bool is_weed = traj->get_decision(trajectory::DecisionFlag::kWeedingWeed) ||
                       traj->get_decision(trajectory::DecisionFlag::kThinningWeed);
        bool is_crop = traj->get_decision(trajectory::DecisionFlag::kWeedingCrop) ||
                       traj->get_decision(trajectory::DecisionFlag::kThinningCrop) ||
                       traj->get_decision(trajectory::DecisionFlag::kKeepableCrop) ||
                       traj->get_decision(trajectory::DecisionFlag::kBandingCrop);

        if (!is_weed && !is_crop) {
          continue;
        }

        if (maka_control_timestamp_ms() - last_estop_check_ts > estop_check_interval_ms) {
          try {
            auto safety = hw_client.get_implement_status();
            estopped =
                safety.estopped() || safety.in_cab_estopped() || safety.left_estopped() || safety.right_estopped();
          } catch (std::exception &e) {
            spdlog::warn("Error getting implement status, {}", e.what());
          }
          last_estop_check_ts = maka_control_timestamp_ms();
        }

        auto centroid = traj->get_last_centroid_with_perspective();
        auto weed_id = traj->get_decision_weed_class();
        auto crop_id = traj->get_decision_crop_class();
        ConclusionType weed_conclusion_type = get_conclusion_type(traj.get(), true, estopped);
        ConclusionType crop_conclusion_type = get_conclusion_type(traj.get(), false, estopped);

        bool armed = targeting::TargetingModeWatcher::get().armed();

        if (weed_conclusion_type != ConclusionType::kFlicker) {
          weed_category_switch_map[traj->changed_classes()].get().Increment();
        }

        auto req_laser_time = traj->total_laser_power_required_at_power();
        bool valid(true); // todo(jfroel) check if we should be using this, rn we are just rejecting everthing that
                          // doesn't have a flag
        if (is_weed) {
          plant_conclusion_counter_.increment(weed_conclusion_type, armed, true, traj->size_mm(), weed_id,
                                              req_laser_time, valid, false);
          SpatialWeedCounter::get().increment(traj->spatial_block(), weed_conclusion_type, armed, true, traj->size_mm(),
                                              weed_id, req_laser_time, valid, false);
        }
        if (is_crop) {
          plant_conclusion_counter_.increment(crop_conclusion_type, armed, false, traj->size_mm(), crop_id,
                                              req_laser_time, valid, traj->is_marked_for_thinning());
          SpatialWeedCounter::get().increment(traj->spatial_block(), crop_conclusion_type, armed, false,
                                              traj->size_mm(), crop_id, req_laser_time, valid,
                                              traj->is_marked_for_thinning());
        }

        if (armed) {
          if (is_weed && weed_conclusion_type == ConclusionType::kShot) {
            ++weed_kill_count_total;
            weed_kill_count_gauge.Set(static_cast<double>(weed_kill_count_total));
            weed_kill_count_by_class[weed_id] += 1;
            weed_kill_count_by_class_labels_map[weed_id].get().Set((double)weed_kill_count_by_class[weed_id]);
          }
          if (is_crop && crop_conclusion_type == ConclusionType::kShot && traj->is_marked_for_thinning()) {
            ++crop_kill_count_total;
            crop_kill_count_gauge.Set(static_cast<double>(crop_kill_count_total));
          }
        }

        if (is_crop && crop_conclusion_type == ConclusionType::kNotShot) {
          crop_gps_labels_map[{geohash, crop_id}].get().Increment();
        }

        if (is_weed && targeting::TargetingModeWatcher::get().weeding_enabled()) {
          conclusion_counter_->add_conclusion(weed_conclusion_type);
        }
        if (is_crop && targeting::TargetingModeWatcher::get().thinning_enabled()) {
          conclusion_counter_->add_conclusion(crop_conclusion_type);
        }

        if (is_weed) {
          weed_gps_labels_map[{geohash, weed_id}].get().Increment();
          auto kill_status = conclusion_type_to_string(weed_conclusion_type);
          weed_count_labels_map[{kill_status, weed_id}].get().Observe(centroid.get_size());
        }

        if (is_weed) {
          item_height_map["weed"].get().Observe(centroid.get_z_mm());
        }
        if (is_crop) {
          item_height_map["crop"].get().Observe(centroid.get_z_mm());
        }

        if (traj->len_sizes() > 1) {
          if (is_weed) {
            item_size_variation_map[weed_id].get().Observe(traj->size_standard_deviation());
          }
          if (is_crop) {
            item_size_variation_map[crop_id].get().Observe(traj->size_standard_deviation());
          }
        }

        if (traj->determined_initial_instances()) {
          if (is_weed) {
            detection_over_opportunity_percentage_map[weed_id].get().Observe(traj->get_detections_over_opportunities());
          }
          if (is_crop) {
            detection_over_opportunity_percentage_map[crop_id].get().Observe(traj->get_detections_over_opportunities());
          }
        }

        if (is_weed) {
          laser_time_metric.add_almanac_time(weed_id, traj->total_laser_power_required_at_power());
          if (weed_conclusion_type == ConclusionType::kShot) {
            laser_time_metric.add_actual_time(weed_id, traj->total_laser_power_received_at_power());
          }
        }
        if (is_crop) {
          laser_time_metric.add_almanac_time(crop_id, traj->total_laser_power_required_at_power());
          if (crop_conclusion_type == ConclusionType::kShot) {
            laser_time_metric.add_actual_time(crop_id, traj->total_laser_power_received_at_power());
          }
        }

        if (is_crop) {
          // for crops, trajectory->banding_state() is always InBand, so we need to check it for real here
          auto banding_state = weed_tracking::Banding::instance()->in_band((float)centroid.get_x_mm());
          if (banding_state == weed_tracking::OutOfBand) {
            crops_in_band_percent_metric.add(0);
          } else {
            crops_in_band_percent_metric.add(100);
          }
        }
        if (weed_conclusion_type != ConclusionType::kFlicker && weed_conclusion_type != ConclusionType::kNotWeeding) {
          extermination_failure_counts_map[weed_conclusion_type].get().Observe(traj->extermination_failures());
        }
        if (crop_conclusion_type != ConclusionType::kFlicker && crop_conclusion_type != ConclusionType::kNotWeeding) {
          extermination_failure_counts_map[crop_conclusion_type].get().Observe(traj->extermination_failures());
        }

        // Trajectory latency

        auto event_timestamps = traj->get_event_timestamps();

        if (event_timestamps.final_detection_time && event_timestamps.first_detection_time) {
          first_to_last_detection_latency_map[traj->get_cam_name()].second.add(
              (double)(event_timestamps.final_detection_time - event_timestamps.first_detection_time));
          first_to_last_detection_latency_map[traj->get_cam_name()].second.update_metric();
        }

        if (event_timestamps.creation_time) {
          if (event_timestamps.first_detection_time) {
            detection_to_trajectory_latency_map[traj->get_cam_name()].second.add(
                (double)(event_timestamps.creation_time - event_timestamps.first_detection_time));
            detection_to_trajectory_latency_map[traj->get_cam_name()].second.update_metric();
          }

          if (event_timestamps.deduplication_time) {
            trajectory_to_deduplication_latency_map[traj->get_cam_name()].second.add(
                (double)(event_timestamps.deduplication_time - event_timestamps.creation_time));
            trajectory_to_deduplication_latency_map[traj->get_cam_name()].second.update_metric();
          }

          if (event_timestamps.arbitration_time) {
            trajectory_to_arbitration_latency_map[traj->get_cam_name()].second.add(
                (double)(event_timestamps.arbitration_time - event_timestamps.creation_time));
            trajectory_to_arbitration_latency_map[traj->get_cam_name()].second.update_metric();
          }

          if (event_timestamps.thinning_time) {
            trajectory_to_thinning_latency_map[traj->get_cam_name()].second.add(
                (double)(event_timestamps.thinning_time - event_timestamps.creation_time));
            trajectory_to_thinning_latency_map[traj->get_cam_name()].second.update_metric();
          }
        }

        if (traj->get_decision(trajectory::DecisionFlag::kThinningCrop) &&
            targeting::TargetingModeWatcher::get().thinning_enabled()) {
          thinning_decision_gauge_map_[thinning_state_to_string(traj->thinning_state())].get().Increment();
        }

        traj->mark_concluded();
      }

      if (arbiter_algorithm != "") {
        arbiter_algorithm_gauge_map_[arbiter_algorithm].get().SetToCurrentTime();
      }

      crops_in_band_percent_metric.update_metric();
      set_kill_count_gauge();
      bool grid_data_enabled =
          config_subscriber->get_config_node("common", "weeding_metrics/enable_speculative_grid_metrics")
              ->get_value<bool>();
      // scanner metrics
      for (auto &scanner : scanner::ScannerWrapperOwner::get().get_scanners()) {
        if (!scanner.second->enabled()) {
          continue;
        }
        auto id = scanner.first;
        const std::string &sid(scanner.second->identifier());
        float avg_pos = scanner.second->avg_tilt_start_point();
        scanner_start_labels_map[id].get().Set(avg_pos);

        std::vector<lib::common::geometric::GeometricScannerOffset::PercentileTuple> percentiles;
        std::vector<lib::common::geometric::GeometricScannerOffset::OffsetsTuple> offsets;
        if (grid_data_enabled) {
          scanner.second->geo_scanner()->percentiles(&percentiles);
          scanner.second->geo_scanner()->offsets(&offsets);
          for (auto &percentile : percentiles) {
            speculative_percentile_map[std::get<0>(percentile)].get().Set(std::get<1>(percentile));
          }
          for (auto &offset : offsets) {
            speculative_offset_map[std::get<0>(offset)].get().Set(std::get<1>(offset));
          }
        }
        for (size_t i = 0; i < scheduling_failures_.at(id).size(); ++i) {
          scheduler_failures_map[{sid, scheduling_failure_to_string(static_cast<SchedulingFailureType>(i))}].get().Set(
              scheduling_failures_.at(id)[i]);
        }
      }

      laser_time_metric.update();
      scheduler_frame_time.Set(scheduler_frame_time_);
      auto estimators = lib::common::geometric::GlobalHeightEstimatorCollection::instance().get_all_height_estimators();
      for (auto &tm : tracker_metrics) {
        tm.latency.get().Set(tm.tracker->latency());
        tm.processing_time.get().Set(tm.tracker->processing_time());
        tm.deepweed_output_processed_count.get().Increment(tm.tracker->get_and_reset_deepweed_output_processed_count());
        tm.deepweed_output_timeout_count.get().Increment(tm.tracker->get_and_reset_deepweed_output_timeout_count());
        tm.deepweed_output_error_count.get().Increment(tm.tracker->get_and_reset_deepweed_output_error_count());
        auto tracker_id = tm.tracker->get_pcam_id();
        for (auto &[bucket, estimator] : estimators) {
          auto geo_cam = tm.tracker->get_geo_cam();
          if (!geo_cam) {
            continue;
          }
          auto lane_heights = estimator->get_averages_for_all_columns(*geo_cam);
          for (size_t i = 0; i < lane_heights.size(); ++i) {
            auto lane_str = std::to_string(i);
            tracker_height_estimation_map[{tracker_id, bucket, lane_str}].get().Set(lane_heights[i]);
          }
        }
      }
      if (std::chrono::system_clock::now() - last_redis_report > std::chrono::seconds(redis_report_interval_sec)) {
        redis.set_long_safe(fmt::format(weed_kill_count_total_key, row_id), weed_kill_count_total);
        redis.set_long_safe(fmt::format(crop_kill_count_total_key, row_id), crop_kill_count_total);
        if (std::rand() % 120 == 0) {
          // dont spam the logs
          spdlog::info("MetricsAggregator: reported weed kill count total={}, crop kill count total={} to redis",
                       weed_kill_count_total, crop_kill_count_total);
        }
        last_redis_report = std::chrono::system_clock::now();
        for (auto &it : weed_kill_count_by_class) {
          auto &clz = it.first;
          auto cnt = it.second;
          redis.set_long_safe(weed_kill_count_by_class_key(row_id, clz), cnt);
        }
      }

      diagnostics_latency_guage.Set(weed_tracking::WeedingDiagnostics::get()->work_loop_latency_ms());
      diagnostics_snapshot_job_latency_gauge.Set(weed_tracking::WeedingDiagnostics::get()->snapshot_job_latency_ns());

    } catch (const std::exception &ex) {
      spdlog::error("Exception in weed_tracking_metrics, {}", ex.what());
    }
  }
}
} // namespace metrics
} // namespace carbon
