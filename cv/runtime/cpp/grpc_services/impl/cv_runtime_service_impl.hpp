#pragma once

#include "cv/deepweed/output.h"
#include "cv/embeddings/manager.h"
#include "cv/p2p/output.h"
#include "cv/runtime/cpp/connector.h"
#include "cv/runtime/cpp/error_state.h"
#include "cv/runtime/cpp/image_score_queue.h"
#include "cv/runtime/cpp/latest_internal_buffer.h"
#include "cv/runtime/cpp/node.h"
#include "cv/runtime/cpp/node_registry.h"
#include "cv/runtime/cpp/nodes/buffer.h"
#include "cv/runtime/cpp/nodes/camera_grabber.h"
#include "cv/runtime/cpp/nodes/distance_buffer.h"
#include "cv/runtime/cpp/nodes/latest_image.h"
#include "cv/runtime/proto/cv_runtime.grpc.pb.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/cpp/geo_data.h"
#include "lib/common/cpp/implement_status.h"
#include "lib/common/model/cpp/model_registry.h"

#include <chrono>
#include <functional>
#include <map>
#include <memory>
#include <optional>
#include <string>
#include <unordered_map>

namespace cv {
namespace runtime {

// Common implementation class for CV runtime services
// This class contains shared implementation functions used by both
// the CVRuntimeServiceImplSync and the asynchronous CVRuntimeServiceImplAsync
class CVRuntimeServiceImplCommon {
public:
  CVRuntimeServiceImplCommon(
      std::shared_ptr<NodeRegistry> node_registry,
      std::shared_ptr<std::map<std::string, std::map<proto::BufferUseCase, std::string>>> camera_to_buffer,
      std::shared_ptr<std::map<std::string, lib::common::camera::Camera>> camera_id_to_camera,
      std::shared_ptr<std::map<std::string, bool>> camera_id_to_transpose,
      std::shared_ptr<std::map<std::string, std::pair<std::string, lib::common::camera::Camera>>> sim_cameras,
      std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry,
      std::shared_ptr<lib::common::GeoData> geo_data, std::shared_ptr<lib::common::ImplementStatus> implement_status,
      std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues,
      std::shared_ptr<carbon::config::ConfigTree> deepweed_config, std::string kLightweightBurstDirectory,
      std::string kFullBurstDirectory,
      const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues,
      std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>>>>
          deepweed_buffers,
      std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>>
          p2p_buffers,
      std::string role,
      std::shared_ptr<
          std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, lib::common::camera::CameraImage>>>>
          distance_camera_buffer,
      std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> chip_score_queues,
      std::function<void()> reload_category_collection, std::shared_ptr<cv::embeddings::Manager> embeddings_manager,
      std::shared_ptr<cv::runtime::ErrorState> error_state);
  virtual ~CVRuntimeServiceImplCommon();

  virtual void Build(grpc::ServerBuilder &builder) = 0;
  virtual void Start() = 0;
  virtual void Shutdown() = 0;
  virtual void Check() = 0;
  virtual void ReportMetrics() = 0;

  grpc::Status processGetBooted(const proto::GetBootedRequest *request, proto::GetBootedResponse *);
  grpc::Status processGetReady(const proto::GetReadyRequest *request, proto::GetReadyResponse *);
  grpc::Status processSetP2PContext(const proto::SetP2PContextRequest *request, proto::SetP2PContextResponse *);
  grpc::Status processGetCameraDimensions(const proto::GetCameraDimensionsRequest *request,
                                          proto::GetCameraDimensionsResponse *);
  grpc::Status processGetCameraInfo(const proto::GetCameraInfoRequest *request, proto::GetCameraInfoResponse *);
  grpc::Status processGetDeepweedIndexToCategory(const proto::GetDeepweedIndexToCategoryRequest *request,
                                                 proto::GetDeepweedIndexToCategoryResponse *);
  grpc::Status processSetDeepweedDetectionCriteria(const proto::SetDeepweedDetectionCriteriaRequest *request,
                                                   proto::SetDeepweedDetectionCriteriaResponse *);
  grpc::Status processGetDeepweedDetectionCriteria(const proto::GetDeepweedDetectionCriteriaRequest *request,
                                                   proto::GetDeepweedDetectionCriteriaResponse *);
  grpc::Status processGetDeepweedSupportedCategories(const proto::GetDeepweedSupportedCategoriesRequest *request,
                                                     proto::GetDeepweedSupportedCategoriesResponse *);
  grpc::Status processSetCameraSettings(const proto::SetCameraSettingsRequest *request,
                                        proto::SetCameraSettingsResponse *);
  grpc::Status processGetCameraSettings(const proto::GetCameraSettingsRequest *request,
                                        proto::GetCameraSettingsResponse *);
  grpc::Status processStartBurstRecordFrames(const proto::StartBurstRecordFramesRequest *request,
                                             proto::StartBurstRecordFramesResponse *);
  grpc::Status processStopBurstRecordFrames(const proto::StopBurstRecordFramesRequest *request,
                                            proto::StopBurstRecordFramesResponse *);
  grpc::Status processGetConnectors(const proto::GetConnectorsRequest *request, proto::GetConnectorsResponse *);
  grpc::Status processSetConnectors(const proto::SetConnectorsRequest *request, proto::SetConnectorsResponse *);
  grpc::Status processGetTiming(const proto::GetTimingRequest *, proto::GetTimingResponse *response);
  grpc::Status processPredict(const proto::PredictRequest *request, proto::PredictResponse *);
  grpc::Status processLoadAndQueue(const proto::LoadAndQueueRequest *request, proto::LoadAndQueueResponse *);
  grpc::Status processSetImage(const proto::SetImageRequest *request, proto::SetImageResponse *);
  grpc::Status processUnsetImage(const proto::UnsetImageRequest *request, proto::UnsetImageResponse *);
  grpc::Status processGetModelPaths(const proto::GetModelPathsRequest *, proto::GetModelPathsResponse *response);
  grpc::Status processGetCameraTemperatures(const proto::GetCameraTemperaturesRequest *,
                                            proto::GetCameraTemperaturesResponse *response);
  grpc::Status processSetImageScore(const proto::SetImageScoreRequest *, proto::SetImageScoreResponse *response);
  grpc::Status processGetScoreQueue(const proto::GetScoreQueueRequest *, proto::GetScoreQueueResponse *response);
  grpc::Status processListScoreQueues(const proto::Empty *, proto::ListScoreQueuesResponse *response);
  grpc::Status processGetMaxImageScore(const proto::GetMaxImageScoreRequest *,
                                       proto::GetMaxImageScoreResponse *response);
  grpc::Status processGetMaxScoredImage(const proto::GetMaxScoredImageRequest *,
                                        proto::ImageAndMetadataResponse *response);
  grpc::Status processGetChipImage(const proto::GetChipImageRequest *, proto::ChipImageAndMetadataResponse *response);
  grpc::Status processGetChipQueueInformation(const proto::ChipQueueInformationRequest *,
                                              proto::ChipQueueInformationResponse *response);
  grpc::Status processGetLatestP2PImage(const proto::GetLatestP2PImageRequest *request,
                                        proto::P2PImageAndMetadataResponse *response);
  grpc::Status processFlushQueues(const proto::FlushQueuesRequest *, proto::FlushQueuesResponse *);
  grpc::Status processGetLatestImage(const proto::GetLatestImageRequest *, proto::ImageAndMetadataResponse *response);
  grpc::Status processGetImageNearTimestamp(const proto::GetImageNearTimestampRequest *,
                                            proto::ImageAndMetadataResponse *response);
  grpc::Status processGetLightweightBurstRecord(const proto::GetLightweightBurstRecordRequest *,
                                                proto::GetLightweightBurstRecordResponse *response);
  grpc::Status processGetDeepweedOutputByTimestamp(const proto::GetDeepweedOutputByTimestampRequest *request,
                                                   proto::DeepweedOutput *response);
  grpc::Status processP2PBufferringBurstCapture(const proto::P2PBufferringBurstCaptureRequest *request,
                                                proto::P2PBufferringBurstCaptureResponse *response);
  grpc::Status processP2PCapture(const proto::P2PCaptureRequest *request, proto::P2PCaptureResponse *response);
  grpc::Status processSetAutoWhitebalance(const proto::SetAutoWhitebalanceRequest *request,
                                          proto::SetAutoWhitebalanceResponse *);
  grpc::Status processGetNextDeepweedOutput(const proto::GetNextDeepweedOutputRequest *request,
                                            proto::DeepweedOutput *response);
  grpc::Status processGetNextP2POutput(const proto::GetNextP2POutputRequest *request, proto::P2POutputProto *response);
  grpc::Status processSetTargetingState(const proto::SetTargetingStateRequest *request,
                                        proto::SetTargetingStateResponse *response);
  void set_booted(bool booted);
  grpc::Status processGetRecommendedStrobeSettings(const proto::GetRecommendedStrobeSettingsRequest *request,
                                                   proto::GetRecommendedStrobeSettingsResponse *response);
  grpc::Status processGetNextFocusMetric(const proto::GetNextFocusMetricRequest *request,
                                         proto::GetNextFocusMetricResponse *response);

  grpc::Status processRemoveDataDir(const proto::RemoveDataDirRequest *request, proto::RemoveDataDirResponse *response);

  grpc::Status processGetLastNImages(const proto::LastNImageRequest *request,
                                     grpc::ServerWriter<proto::ImageAndMetadataResponse> *writer);

  grpc::Status processGetComputeCapabilities(const proto::Empty *request, proto::ComputeCapabilitiesResponse *response);

  grpc::Status processGetSupportedTensorRTVersions(const proto::Empty *request,
                                                   proto::SupportedTensorRTVersionsResponse *response);

  grpc::Status processReloadCategoryCollection(const proto::Empty *request, proto::Empty *response);

  grpc::Status processGetCategoryCollection(const proto::Empty *request,
                                            proto::GetCategoryCollectionResponse *response);
  grpc::Status processGetErrorState(const proto::Empty *request, proto::GetErrorStateResponse *response);
  grpc::Status processSnapshotPredictImages(const proto::SnapshotPredictImagesRequest *request,
                                            proto::SnapshotPredictImagesResponse *response);
  grpc::Status processGetChipForPredictImage(const proto::GetChipForPredictImageRequest *request,
                                             proto::GetChipForPredictImageResponse *response);

protected:
  constexpr static auto kCameraTimeout = std::chrono::milliseconds(0);
  void setImageAndMetadataResponse(lib::common::camera::CameraImage cam_image,
                                   proto::ImageAndMetadataResponse *response);
  void setDetectionsMetadataResponse(const proto::DeepweedOutput &deepweed_output,
                                     proto::ImageAndMetadataResponse *response);
  void setP2PImageAndMetadataResponse(P2PScoreQueueObject p2p_score_object,
                                      proto::P2PImageAndMetadataResponse *response);
  void setChipPredictionMetadata(ChipScoreMetadata metadata, proto::ChipPrediction *prediction_metadata);
  std::optional<std::string> cache_camera_model_firmware_version(const std::string &model);

  std::shared_ptr<NodeRegistry> node_registry_;
  std::shared_ptr<std::map<std::string, std::map<proto::BufferUseCase, std::string>>> camera_to_buffer_;
  std::shared_ptr<std::map<std::string, lib::common::camera::Camera>> camera_id_to_camera_;
  std::shared_ptr<std::map<std::string, bool>> camera_id_to_transpose_;
  std::shared_ptr<std::map<std::string, std::pair<std::string, lib::common::camera::Camera>>> sim_cameras_;
  std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry_;
  std::shared_ptr<lib::common::GeoData> geo_data_;
  std::shared_ptr<lib::common::ImplementStatus> implement_status_;
  std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues_;
  std::shared_ptr<carbon::config::ConfigTree> deepweed_config_;
  std::shared_ptr<carbon::config::ConfigTree> common_config_;
  std::string kLightweightBurstDirectory_;
  std::string kFullBurstDirectory_;
  std::atomic<bool> booted_;
  const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues_;
  std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>>>>
      deepweed_buffers_;
  std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>>
      p2p_buffers_;
  std::string role_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
      distance_camera_buffer_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> chip_score_queues_;
  std::function<void()> reload_category_collection_;
  std::map<std::string, std::optional<std::string>> camera_model_to_firmware_version_;
  std::shared_ptr<cv::embeddings::Manager> embeddings_manager_;
  std::shared_ptr<cv::runtime::ErrorState> error_state_;

  std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
      snapshot_buffer_;
};

} // namespace runtime
} // namespace cv
