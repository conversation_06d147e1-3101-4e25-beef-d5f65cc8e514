#!/usr/bin/python

import argparse
import json
import os
import subprocess
import traceback
from typing import Any, Dict, Optional

from generation import GENERATION
from role import ROLE, is_numeric_role

BASE_PREFIX = os.environ.get("CARBON_HOST_BASE_DIR", "/data/carbon")


def setup_dirs(prefix: str) -> None:
    os.makedirs(prefix, exist_ok=True)
    os.makedirs(f"{prefix}/bot", exist_ok=True)
    os.makedirs(f"{prefix}/bot/versions", exist_ok=True)
    os.makedirs(f"{prefix}/bot/envs", exist_ok=True)
    os.makedirs(f"{prefix}/calibration", exist_ok=True)
    os.makedirs(f"{prefix}/config", exist_ok=True)
    os.makedirs(f"{prefix}/logs", exist_ok=True)
    os.makedirs(f"{prefix}/models", exist_ok=True)
    os.makedirs(f"{prefix}/data", exist_ok=True)
    os.makedirs(f"{prefix}/data/logs/", exist_ok=True)
    os.makedirs(f"{prefix}/data/diagnostics/", exist_ok=True)


def setup_user_dirs(prefix: str) -> None:
    os.makedirs(prefix, exist_ok=True)
    os.makedirs(f"{prefix}/calibration", exist_ok=True)
    os.makedirs(f"{prefix}/config", exist_ok=True)
    os.makedirs(f"{prefix}/logs", exist_ok=True)
    os.makedirs(f"{prefix}/data", exist_ok=True)
    os.makedirs(f"{prefix}/data/logs/", exist_ok=True)
    os.makedirs(f"{prefix}/data/diagnostics/", exist_ok=True)
    os.makedirs(f"{prefix}/robot", exist_ok=True)


class ComputerDef:
    def __init__(self, role: str, row: int = 0) -> None:
        self.role = role
        self.row = row


class RobotDef:
    def __init__(
        self,
        name: str,
        generation: str,
        computer: Dict[str, Any],
        robot_username: str,
        robot_password: str,
        environment: str = "production",
    ) -> None:
        self.name = name
        self.generation = generation
        self.computer = ComputerDef(**computer)
        self.robot_username = robot_username
        self.robot_password = robot_password
        self.environment = environment

    def save(self, test: bool = False) -> None:
        file_path = f"{BASE_PREFIX}/bot/robot.json"
        if test is False:
            setup_dirs(BASE_PREFIX)
        data = {
            "name": self.name,
            "generation": self.generation,
            "computer": {"role": self.computer.role, "row": f"{self.computer.row}"},
            "robot_username": self.robot_username,
            "robot_password": self.robot_password,
        }
        if self.environment is not None and self.environment != "production" and self.environment != "":
            data["environment"] = self.environment
        if test:
            print("!!Test Mode!! - printing instead of writing files")
            print(f"file: {file_path}")
            print(f"data: {json.dumps(data, indent=2)}")
            return

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

    def validate(self) -> "RobotDef":
        assert self.name
        gen = GENERATION.from_str(self.generation)
        assert gen is not None, f"{self.generation} is not a valid generation"

        role = ROLE.from_str(self.computer.role)
        row = self.computer.row
        assert role is not None, f"{self.computer.role} is not a valid role"
        assert (is_numeric_role(role) and row > 0) or ((not is_numeric_role(role) or role == ROLE.MODULE) and row == 0)

        return self

    @staticmethod
    def from_input() -> "RobotDef":
        name = input("Robot Name: ")
        generation = input("Robot Generation [" + ", ".join(GENERATION.generation_strs()) + "]: ")
        role = input("Robot Computer Role [" + ", ".join(ROLE.role_strs()) + "]: ")
        opt_role = ROLE.from_str(role)
        row = int(input("Row/Module Number: ")) if opt_role is not None and is_numeric_role(opt_role) else 0
        environment = input("Environment (defaults to production): ")
        robot_username = input("Robot Username: ")
        robot_password = input("Robot Password: ")
        return RobotDef(
            name=name,
            generation=generation,
            computer={"role": role, "row": row},
            environment=environment,
            robot_username=robot_username,
            robot_password=robot_password,
        ).validate()

    @staticmethod
    def from_namespace(args: argparse.Namespace) -> "RobotDef":
        name = args.name
        generation = args.generation
        role = args.role
        opt_role = ROLE.from_str(role)
        row = args.row if opt_role is not None and is_numeric_role(opt_role) else 0
        environment = args.environment
        robot_username = args.robot_username
        robot_password = args.robot_password
        return RobotDef(
            name=name,
            generation=generation,
            computer={"role": role, "row": row},
            environment=environment,
            robot_username=robot_username,
            robot_password=robot_password,
        ).validate()

    @staticmethod
    def read() -> "RobotDef":
        with open(f"{BASE_PREFIX}/bot/robot.json", "r") as f:
            return RobotDef(**json.load(f))


class CheckoutDef:
    def __init__(self, mode: str, user: str) -> None:
        self.mode = mode
        self.user = user

    def save(self, test: bool = False) -> None:
        if test is False:
            setup_dirs(BASE_PREFIX)
        data = {
            "mode": self.mode,
            "user": self.user,
        }
        file_path = f"{BASE_PREFIX}/bot/checkout.json"
        if test:
            print("!!Test Mode!! - printing instead of writing files")
            print(f"file: {file_path}")
            print(f"data: {json.dumps(data, indent=2)}")
            return

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

    def get_user(self) -> str:
        return self.user

    @staticmethod
    def default() -> "CheckoutDef":
        return CheckoutDef(mode="prod", user="carbon")

    @staticmethod
    def user_manual(user: str) -> "CheckoutDef":
        return CheckoutDef(mode="user", user=user)

    @staticmethod
    def user_auto() -> "CheckoutDef":
        return CheckoutDef.user_manual(os.getlogin())

    @staticmethod
    def read() -> "CheckoutDef":
        with open(f"{BASE_PREFIX}/bot/checkout.json", "r") as f:
            return CheckoutDef(**json.load(f))


class EnvDef:
    def __init__(self, bot: str, robot: str, data: str, config: str, calibration: str, logs: str, models: str) -> None:
        self.bot = bot
        self.robot = robot
        self.data = data
        self.config = config
        self.calibration = calibration
        self.logs = logs
        self.models = models

    def _save(self, path: str, test: bool = False) -> None:
        if test is False:
            setup_dirs(BASE_PREFIX)
        data = {
            "bot": self.bot,
            "robot": self.robot,
            "data": self.data,
            "config": self.config,
            "calibration": self.calibration,
            "logs": self.logs,
            "models": self.models,
        }

        if test:
            print("!!Test Mode!! - printing instead of writing files")
            print(f"file: {path}")
            print(f"data: {json.dumps(data, indent=2)}")
            return

        with open(path, "w") as f:
            json.dump(data, f, indent=2)

    def save_default(self, test: bool = False) -> None:
        self._save(f"{BASE_PREFIX}/bot/env.json", test)

    def save_user(self, user: str, test: bool = False) -> None:
        self._save(f"{BASE_PREFIX}/bot/envs/{user}.json", test)

    def save_auto_user(self, test: bool = False) -> None:
        self.save_user(os.getlogin(), test)

    @staticmethod
    def default() -> "EnvDef":
        return EnvDef(
            bot=f"{BASE_PREFIX}/bot",
            robot=f"{BASE_PREFIX}/robot",
            data=f"{BASE_PREFIX}/data",
            config=f"{BASE_PREFIX}/config",
            calibration=f"{BASE_PREFIX}/calibration",
            logs=f"{BASE_PREFIX}/logs",
            models=f"{BASE_PREFIX}/models",
        )

    @staticmethod
    def user(base: str) -> "EnvDef":
        base_dir = base
        if not base_dir:
            base_dir = input("Base User Dir: ")
        setup_user_dirs(base_dir)
        return EnvDef(
            bot=f"{BASE_PREFIX}/bot",
            robot=f"{base_dir}/robot",
            data=f"{base_dir}/data",
            config=f"{base_dir}/config",
            calibration=f"{base_dir}/calibration",
            logs=f"{base_dir}/logs",
            models=f"{BASE_PREFIX}/models",
        )

    @staticmethod
    def read(checkout: CheckoutDef) -> "EnvDef":
        data = {}
        with open(f"{BASE_PREFIX}/bot/env.json", "r") as f:
            data.update(json.load(f))

        if checkout.mode == "user" and os.path.exists(f"{BASE_PREFIX}/bot/envs/{checkout.get_user()}.json"):
            with open(f"{BASE_PREFIX}/bot/envs/{checkout.get_user()}.json", "r") as f:
                data.update(json.load(f))

        return EnvDef(**data)


class VersionDef:
    def __init__(self, tag: str) -> None:
        self.tag = tag

    @staticmethod
    def read(prod: bool, system_version: Optional[str] = None) -> "VersionDef":
        if system_version is None:
            if os.path.exists("/carbon/sbin/carbon-upgrade"):
                try:
                    process = subprocess.Popen(
                        ["/carbon/sbin/carbon-upgrade", "info", "--old", "/"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                    )
                    out, err = process.communicate()
                    if "unrecognized arguments: --old" not in str(err):
                        info = json.loads(out)
                        system_version = info["versions"]["current"]
                except Exception:
                    traceback.print_exc()
                    print(
                        "Warning: /carbon/sbin/carbon-upgrade threw an exception, could be out of date. Assuming pre-production system version s1.0.0"
                    )
            if system_version is None or system_version == "":
                system_version = "s1.0.0"
        if prod:
            if os.path.exists(f"{BASE_PREFIX}/bot/versions/{system_version}.json"):
                with open(f"{BASE_PREFIX}/bot/versions/{system_version}.json", "r") as f:
                    return VersionDef(**json.load(f))
            else:
                # TODO Consider if we want to try to automatically figure out the best available version
                raise Exception(f"No Software Version defined for system version: {system_version}")

        return VersionDef(tag="latest")


def get_username() -> str:
    try:
        return os.getlogin()
    except Exception:
        pass
    return "invalid"
