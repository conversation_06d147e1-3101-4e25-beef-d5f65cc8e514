"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

InvalidScoreReasonValue = typing___NewType('InvalidScoreReasonValue', builtin___int)
type___InvalidScoreReasonValue = InvalidScoreReasonValue
InvalidScoreReason: _InvalidScoreReason
class _InvalidScoreReason(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[InvalidScoreReasonValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NONE = typing___cast(InvalidScoreReasonValue, 0)
    ANOTHER_LASER_SHOOTING = typing___cast(InvalidScoreReasonValue, 1)
    SCANNER_POSITION_INVALID = typing___cast(InvalidScoreReasonValue, 2)
    WEED_ALREADY_KILLED = typing___cast(InvalidScoreReasonValue, 3)
    WEED_SHOT_BY_ANOTHER_LASER = typing___cast(InvalidScoreReasonValue, 4)
    WEED_OUT_OF_BAND = typing___cast(InvalidScoreReasonValue, 5)
    NOT_A_WEED = typing___cast(InvalidScoreReasonValue, 6)
    SCORE_NEGATIVE = typing___cast(InvalidScoreReasonValue, 7)
    INTERSECTED_WITH_NONSHOOTABLE = typing___cast(InvalidScoreReasonValue, 8)
    EXTERMINATION_FAILURES_EXCEEDED_MAX = typing___cast(InvalidScoreReasonValue, 9)
    DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN = typing___cast(InvalidScoreReasonValue, 10)
NONE = typing___cast(InvalidScoreReasonValue, 0)
ANOTHER_LASER_SHOOTING = typing___cast(InvalidScoreReasonValue, 1)
SCANNER_POSITION_INVALID = typing___cast(InvalidScoreReasonValue, 2)
WEED_ALREADY_KILLED = typing___cast(InvalidScoreReasonValue, 3)
WEED_SHOT_BY_ANOTHER_LASER = typing___cast(InvalidScoreReasonValue, 4)
WEED_OUT_OF_BAND = typing___cast(InvalidScoreReasonValue, 5)
NOT_A_WEED = typing___cast(InvalidScoreReasonValue, 6)
SCORE_NEGATIVE = typing___cast(InvalidScoreReasonValue, 7)
INTERSECTED_WITH_NONSHOOTABLE = typing___cast(InvalidScoreReasonValue, 8)
EXTERMINATION_FAILURES_EXCEEDED_MAX = typing___cast(InvalidScoreReasonValue, 9)
DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN = typing___cast(InvalidScoreReasonValue, 10)

KillStatusValue = typing___NewType('KillStatusValue', builtin___int)
type___KillStatusValue = KillStatusValue
KillStatus: _KillStatus
class _KillStatus(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[KillStatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    STATUS_NOT_SHOT = typing___cast(KillStatusValue, 0)
    STATUS_BEING_SHOT = typing___cast(KillStatusValue, 1)
    STATUS_SHOT = typing___cast(KillStatusValue, 2)
    STATUS_PARTIALLY_SHOT = typing___cast(KillStatusValue, 3)
    STATUS_P2P_NOT_FOUND = typing___cast(KillStatusValue, 4)
    STATUS_ERROR = typing___cast(KillStatusValue, 5)
    STATUS_P2P_MISSING_CONTEXT = typing___cast(KillStatusValue, 6)
STATUS_NOT_SHOT = typing___cast(KillStatusValue, 0)
STATUS_BEING_SHOT = typing___cast(KillStatusValue, 1)
STATUS_SHOT = typing___cast(KillStatusValue, 2)
STATUS_PARTIALLY_SHOT = typing___cast(KillStatusValue, 3)
STATUS_P2P_NOT_FOUND = typing___cast(KillStatusValue, 4)
STATUS_ERROR = typing___cast(KillStatusValue, 5)
STATUS_P2P_MISSING_CONTEXT = typing___cast(KillStatusValue, 6)

DuplicateStatusValue = typing___NewType('DuplicateStatusValue', builtin___int)
type___DuplicateStatusValue = DuplicateStatusValue
DuplicateStatus: _DuplicateStatus
class _DuplicateStatus(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[DuplicateStatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    UNIQUE = typing___cast(DuplicateStatusValue, 0)
    PRIMARY = typing___cast(DuplicateStatusValue, 1)
    DUPLICATE = typing___cast(DuplicateStatusValue, 2)
UNIQUE = typing___cast(DuplicateStatusValue, 0)
PRIMARY = typing___cast(DuplicateStatusValue, 1)
DUPLICATE = typing___cast(DuplicateStatusValue, 2)

ThinningStateValue = typing___NewType('ThinningStateValue', builtin___int)
type___ThinningStateValue = ThinningStateValue
ThinningState: _ThinningState
class _ThinningState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ThinningStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    THINNING_UNSET = typing___cast(ThinningStateValue, 0)
    THINNING_MARKED_FOR_THINNING = typing___cast(ThinningStateValue, 1)
    THINNING_KEPT = typing___cast(ThinningStateValue, 2)
    THINNING_IGNORED = typing___cast(ThinningStateValue, 3)
THINNING_UNSET = typing___cast(ThinningStateValue, 0)
THINNING_MARKED_FOR_THINNING = typing___cast(ThinningStateValue, 1)
THINNING_KEPT = typing___cast(ThinningStateValue, 2)
THINNING_IGNORED = typing___cast(ThinningStateValue, 3)

TargetableStateValue = typing___NewType('TargetableStateValue', builtin___int)
type___TargetableStateValue = TargetableStateValue
TargetableState: _TargetableState
class _TargetableState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[TargetableStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    TARGET_NOT_IN_SCHEDULER = typing___cast(TargetableStateValue, 0)
    TARGET_SCORED = typing___cast(TargetableStateValue, 1)
    TARGET_INTERSECTS_NON_SHOOTABLE = typing___cast(TargetableStateValue, 2)
    TARGET_TOO_MANY_FAILURES = typing___cast(TargetableStateValue, 3)
    TARGET_DOO_TOO_LOW = typing___cast(TargetableStateValue, 4)
    TARGET_IGNORED_FROM_ALMANAC = typing___cast(TargetableStateValue, 5)
    TARGET_OUT_OF_BAND = typing___cast(TargetableStateValue, 6)
    TARGET_AVOID_FROM_ALMANAC = typing___cast(TargetableStateValue, 7)
TARGET_NOT_IN_SCHEDULER = typing___cast(TargetableStateValue, 0)
TARGET_SCORED = typing___cast(TargetableStateValue, 1)
TARGET_INTERSECTS_NON_SHOOTABLE = typing___cast(TargetableStateValue, 2)
TARGET_TOO_MANY_FAILURES = typing___cast(TargetableStateValue, 3)
TARGET_DOO_TOO_LOW = typing___cast(TargetableStateValue, 4)
TARGET_IGNORED_FROM_ALMANAC = typing___cast(TargetableStateValue, 5)
TARGET_OUT_OF_BAND = typing___cast(TargetableStateValue, 6)
TARGET_AVOID_FROM_ALMANAC = typing___cast(TargetableStateValue, 7)

ClassificationValue = typing___NewType('ClassificationValue', builtin___int)
type___ClassificationValue = ClassificationValue
Classification: _Classification
class _Classification(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ClassificationValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    CLASS_UNDECIDED = typing___cast(ClassificationValue, 0)
    CLASS_WEED = typing___cast(ClassificationValue, 1)
    CLASS_CROP = typing___cast(ClassificationValue, 2)
    CLASS_BOTH = typing___cast(ClassificationValue, 3)
    CLASS_UNKNOWN = typing___cast(ClassificationValue, 4)
CLASS_UNDECIDED = typing___cast(ClassificationValue, 0)
CLASS_WEED = typing___cast(ClassificationValue, 1)
CLASS_CROP = typing___cast(ClassificationValue, 2)
CLASS_BOTH = typing___cast(ClassificationValue, 3)
CLASS_UNKNOWN = typing___cast(ClassificationValue, 4)

RecordingStatusValue = typing___NewType('RecordingStatusValue', builtin___int)
type___RecordingStatusValue = RecordingStatusValue
RecordingStatus: _RecordingStatus
class _RecordingStatus(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[RecordingStatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NOT_RECORDING = typing___cast(RecordingStatusValue, 0)
    RECORDING_STARTED = typing___cast(RecordingStatusValue, 1)
    RECORDING_FINISHED = typing___cast(RecordingStatusValue, 2)
    RECORDING_FAILED = typing___cast(RecordingStatusValue, 3)
NOT_RECORDING = typing___cast(RecordingStatusValue, 0)
RECORDING_STARTED = typing___cast(RecordingStatusValue, 1)
RECORDING_FINISHED = typing___cast(RecordingStatusValue, 2)
RECORDING_FAILED = typing___cast(RecordingStatusValue, 3)

ConclusionTypeValue = typing___NewType('ConclusionTypeValue', builtin___int)
type___ConclusionTypeValue = ConclusionTypeValue
ConclusionType: _ConclusionType
class _ConclusionType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ConclusionTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NOT_WEEDING = typing___cast(ConclusionTypeValue, 0)
    OUT_OF_BAND = typing___cast(ConclusionTypeValue, 1)
    INTERSECTS_WITH_NON_SHOOTABLE = typing___cast(ConclusionTypeValue, 2)
    OUT_OF_RANGE = typing___cast(ConclusionTypeValue, 3)
    UNIMPORTANT = typing___cast(ConclusionTypeValue, 4)
    NOT_SHOT = typing___cast(ConclusionTypeValue, 5)
    PARTIALLY_SHOT = typing___cast(ConclusionTypeValue, 6)
    SHOT = typing___cast(ConclusionTypeValue, 7)
    P2P_NOT_FOUND = typing___cast(ConclusionTypeValue, 8)
    ERROR = typing___cast(ConclusionTypeValue, 9)
    FLICKER = typing___cast(ConclusionTypeValue, 10)
    MARKED_FOR_THINNING = typing___cast(ConclusionTypeValue, 11)
    NOT_TARGETED = typing___cast(ConclusionTypeValue, 12)
    P2P_MISSING_CONTEXT = typing___cast(ConclusionTypeValue, 13)
NOT_WEEDING = typing___cast(ConclusionTypeValue, 0)
OUT_OF_BAND = typing___cast(ConclusionTypeValue, 1)
INTERSECTS_WITH_NON_SHOOTABLE = typing___cast(ConclusionTypeValue, 2)
OUT_OF_RANGE = typing___cast(ConclusionTypeValue, 3)
UNIMPORTANT = typing___cast(ConclusionTypeValue, 4)
NOT_SHOT = typing___cast(ConclusionTypeValue, 5)
PARTIALLY_SHOT = typing___cast(ConclusionTypeValue, 6)
SHOT = typing___cast(ConclusionTypeValue, 7)
P2P_NOT_FOUND = typing___cast(ConclusionTypeValue, 8)
ERROR = typing___cast(ConclusionTypeValue, 9)
FLICKER = typing___cast(ConclusionTypeValue, 10)
MARKED_FOR_THINNING = typing___cast(ConclusionTypeValue, 11)
NOT_TARGETED = typing___cast(ConclusionTypeValue, 12)
P2P_MISSING_CONTEXT = typing___cast(ConclusionTypeValue, 13)

PlantCaptchaStatusValue = typing___NewType('PlantCaptchaStatusValue', builtin___int)
type___PlantCaptchaStatusValue = PlantCaptchaStatusValue
PlantCaptchaStatus: _PlantCaptchaStatus
class _PlantCaptchaStatus(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[PlantCaptchaStatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NOT_STARTED = typing___cast(PlantCaptchaStatusValue, 0)
    CAPTCHA_STARTED = typing___cast(PlantCaptchaStatusValue, 1)
    CAPTCHA_FINISHED = typing___cast(PlantCaptchaStatusValue, 2)
    CAPTCHA_FAILED = typing___cast(PlantCaptchaStatusValue, 3)
    CAPTCHA_CANCELLED = typing___cast(PlantCaptchaStatusValue, 4)
NOT_STARTED = typing___cast(PlantCaptchaStatusValue, 0)
CAPTCHA_STARTED = typing___cast(PlantCaptchaStatusValue, 1)
CAPTCHA_FINISHED = typing___cast(PlantCaptchaStatusValue, 2)
CAPTCHA_FAILED = typing___cast(PlantCaptchaStatusValue, 3)
CAPTCHA_CANCELLED = typing___cast(PlantCaptchaStatusValue, 4)

PlantCaptchaUserPredictionValue = typing___NewType('PlantCaptchaUserPredictionValue', builtin___int)
type___PlantCaptchaUserPredictionValue = PlantCaptchaUserPredictionValue
PlantCaptchaUserPrediction: _PlantCaptchaUserPrediction
class _PlantCaptchaUserPrediction(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[PlantCaptchaUserPredictionValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    WEED = typing___cast(PlantCaptchaUserPredictionValue, 0)
    CROP = typing___cast(PlantCaptchaUserPredictionValue, 1)
    UNKNOWN = typing___cast(PlantCaptchaUserPredictionValue, 2)
    OTHER = typing___cast(PlantCaptchaUserPredictionValue, 3)
    IGNORE = typing___cast(PlantCaptchaUserPredictionValue, 4)
    VOLUNTEER = typing___cast(PlantCaptchaUserPredictionValue, 5)
    BENEFICIAL = typing___cast(PlantCaptchaUserPredictionValue, 6)
    DEBRIS = typing___cast(PlantCaptchaUserPredictionValue, 7)
WEED = typing___cast(PlantCaptchaUserPredictionValue, 0)
CROP = typing___cast(PlantCaptchaUserPredictionValue, 1)
UNKNOWN = typing___cast(PlantCaptchaUserPredictionValue, 2)
OTHER = typing___cast(PlantCaptchaUserPredictionValue, 3)
IGNORE = typing___cast(PlantCaptchaUserPredictionValue, 4)
VOLUNTEER = typing___cast(PlantCaptchaUserPredictionValue, 5)
BENEFICIAL = typing___cast(PlantCaptchaUserPredictionValue, 6)
DEBRIS = typing___cast(PlantCaptchaUserPredictionValue, 7)

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class Trajectory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    tracker_id: builtin___int = ...
    status: builtin___int = ...
    is_weed: builtin___bool = ...
    x_mm: builtin___float = ...
    y_mm: builtin___float = ...
    z_mm: builtin___float = ...
    intersected_with_nonshootable: builtin___bool = ...
    nonshootable_type_string: typing___Text = ...
    deduplicated_across_tracker: builtin___bool = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        tracker_id : typing___Optional[builtin___int] = None,
        status : typing___Optional[builtin___int] = None,
        is_weed : typing___Optional[builtin___bool] = None,
        x_mm : typing___Optional[builtin___float] = None,
        y_mm : typing___Optional[builtin___float] = None,
        z_mm : typing___Optional[builtin___float] = None,
        intersected_with_nonshootable : typing___Optional[builtin___bool] = None,
        nonshootable_type_string : typing___Optional[typing___Text] = None,
        deduplicated_across_tracker : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"deduplicated_across_tracker",b"deduplicated_across_tracker",u"id",b"id",u"intersected_with_nonshootable",b"intersected_with_nonshootable",u"is_weed",b"is_weed",u"nonshootable_type_string",b"nonshootable_type_string",u"status",b"status",u"tracker_id",b"tracker_id",u"x_mm",b"x_mm",u"y_mm",b"y_mm",u"z_mm",b"z_mm"]) -> None: ...
type___Trajectory = Trajectory

class Target(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    trajectory_id: builtin___int = ...
    next_trajectory_id: builtin___int = ...
    starting_pos_y: builtin___float = ...
    ending_pos_y: builtin___float = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        trajectory_id : typing___Optional[builtin___int] = None,
        next_trajectory_id : typing___Optional[builtin___int] = None,
        starting_pos_y : typing___Optional[builtin___float] = None,
        ending_pos_y : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ending_pos_y",b"ending_pos_y",u"next_trajectory_id",b"next_trajectory_id",u"scanner_id",b"scanner_id",u"starting_pos_y",b"starting_pos_y",u"trajectory_id",b"trajectory_id"]) -> None: ...
type___Target = Target

class Bounds(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    tracker_id: typing___Text = ...
    max_pos_mm_y: builtin___float = ...

    def __init__(self,
        *,
        tracker_id : typing___Optional[typing___Text] = None,
        max_pos_mm_y : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"max_pos_mm_y",b"max_pos_mm_y",u"tracker_id",b"tracker_id"]) -> None: ...
type___Bounds = Bounds

class TrackingStatusReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def targets(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Target]: ...

    @property
    def trajectories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Trajectory]: ...

    @property
    def bounds(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Bounds]: ...

    def __init__(self,
        *,
        targets : typing___Optional[typing___Iterable[type___Target]] = None,
        trajectories : typing___Optional[typing___Iterable[type___Trajectory]] = None,
        bounds : typing___Optional[typing___Iterable[type___Bounds]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bounds",b"bounds",u"targets",b"targets",u"trajectories",b"trajectories"]) -> None: ...
type___TrackingStatusReply = TrackingStatusReply

class GetDetectionsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...
    max_x: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        max_x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"max_x",b"max_x",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetDetectionsRequest = GetDetectionsRequest

class Detection(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___float = ...
    y: builtin___float = ...
    size: builtin___float = ...
    clz: typing___Text = ...
    is_weed: builtin___bool = ...
    out_of_band: builtin___bool = ...
    id: builtin___int = ...
    score: builtin___float = ...
    weed_score: builtin___float = ...
    crop_score: builtin___float = ...
    embedding: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    plant_score: builtin___float = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        size : typing___Optional[builtin___float] = None,
        clz : typing___Optional[typing___Text] = None,
        is_weed : typing___Optional[builtin___bool] = None,
        out_of_band : typing___Optional[builtin___bool] = None,
        id : typing___Optional[builtin___int] = None,
        score : typing___Optional[builtin___float] = None,
        weed_score : typing___Optional[builtin___float] = None,
        crop_score : typing___Optional[builtin___float] = None,
        embedding : typing___Optional[typing___Iterable[builtin___float]] = None,
        plant_score : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"clz",b"clz",u"crop_score",b"crop_score",u"embedding",b"embedding",u"id",b"id",u"is_weed",b"is_weed",u"out_of_band",b"out_of_band",u"plant_score",b"plant_score",u"score",b"score",u"size",b"size",u"weed_score",b"weed_score",u"x",b"x",u"y",b"y"]) -> None: ...
type___Detection = Detection

class Detections(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    @property
    def detections(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Detection]: ...

    def __init__(self,
        *,
        detections : typing___Optional[typing___Iterable[type___Detection]] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"detections",b"detections",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___Detections = Detections

class Band(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    start_x_px: builtin___float = ...
    end_x_px: builtin___float = ...

    def __init__(self,
        *,
        start_x_px : typing___Optional[builtin___float] = None,
        end_x_px : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"end_x_px",b"end_x_px",u"start_x_px",b"start_x_px"]) -> None: ...
type___Band = Band

class Bands(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    banding_enabled: builtin___bool = ...
    row_has_bands_defined: builtin___bool = ...

    @property
    def band(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Band]: ...

    def __init__(self,
        *,
        band : typing___Optional[typing___Iterable[type___Band]] = None,
        banding_enabled : typing___Optional[builtin___bool] = None,
        row_has_bands_defined : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"band",b"band",u"banding_enabled",b"banding_enabled",u"row_has_bands_defined",b"row_has_bands_defined"]) -> None: ...
type___Bands = Bands

class GetDetectionsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def detections(self) -> type___Detections: ...

    @property
    def bands(self) -> type___Bands: ...

    def __init__(self,
        *,
        detections : typing___Optional[type___Detections] = None,
        bands : typing___Optional[type___Bands] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bands",b"bands",u"detections",b"detections"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bands",b"bands",u"detections",b"detections"]) -> None: ...
type___GetDetectionsResponse = GetDetectionsResponse

class GetTrajectoryMetadataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetTrajectoryMetadataRequest = GetTrajectoryMetadataRequest

class TrackedItemMetadata(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    detection_id: builtin___int = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        detection_id : typing___Optional[builtin___int] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"detection_id",b"detection_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___TrackedItemMetadata = TrackedItemMetadata

class TrajectoryMetadata(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    trajectory_id: builtin___int = ...
    cam_id: typing___Text = ...
    band_status: typing___Text = ...

    @property
    def tracked_item_metadata(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TrackedItemMetadata]: ...

    def __init__(self,
        *,
        trajectory_id : typing___Optional[builtin___int] = None,
        cam_id : typing___Optional[typing___Text] = None,
        tracked_item_metadata : typing___Optional[typing___Iterable[type___TrackedItemMetadata]] = None,
        band_status : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"band_status",b"band_status",u"cam_id",b"cam_id",u"tracked_item_metadata",b"tracked_item_metadata",u"trajectory_id",b"trajectory_id"]) -> None: ...
type___TrajectoryMetadata = TrajectoryMetadata

class GetTrajectoryMetadataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def metadata(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TrajectoryMetadata]: ...

    def __init__(self,
        *,
        metadata : typing___Optional[typing___Iterable[type___TrajectoryMetadata]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"metadata",b"metadata"]) -> None: ...
type___GetTrajectoryMetadataResponse = GetTrajectoryMetadataResponse

class PingRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PingRequest = PingRequest

class PongReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PongReply = PongReply

class TrajectoryScore(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score: builtin___int = ...
    score_tilt: builtin___float = ...
    score_pan: builtin___float = ...
    score_busy: builtin___float = ...
    score_in_range: builtin___float = ...
    score_importance: builtin___float = ...
    score_size: builtin___float = ...

    def __init__(self,
        *,
        score : typing___Optional[builtin___int] = None,
        score_tilt : typing___Optional[builtin___float] = None,
        score_pan : typing___Optional[builtin___float] = None,
        score_busy : typing___Optional[builtin___float] = None,
        score_in_range : typing___Optional[builtin___float] = None,
        score_importance : typing___Optional[builtin___float] = None,
        score_size : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"score",b"score",u"score_busy",b"score_busy",u"score_importance",b"score_importance",u"score_in_range",b"score_in_range",u"score_pan",b"score_pan",u"score_size",b"score_size",u"score_tilt",b"score_tilt"]) -> None: ...
type___TrajectoryScore = TrajectoryScore

class PerScannerScore(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    score: builtin___int = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        score : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_id",b"scanner_id",u"score",b"score"]) -> None: ...
type___PerScannerScore = PerScannerScore

class ScoreState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    target_state: type___TargetableStateValue = ...

    @property
    def scores(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PerScannerScore]: ...

    def __init__(self,
        *,
        target_state : typing___Optional[type___TargetableStateValue] = None,
        scores : typing___Optional[typing___Iterable[type___PerScannerScore]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scores",b"scores",u"target_state",b"target_state"]) -> None: ...
type___ScoreState = ScoreState

class Pos2D(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___float = ...
    y: builtin___float = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x",u"y",b"y"]) -> None: ...
type___Pos2D = Pos2D

class KillBox(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...

    @property
    def top_left(self) -> type___Pos2D: ...

    @property
    def bottom_right(self) -> type___Pos2D: ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        top_left : typing___Optional[type___Pos2D] = None,
        bottom_right : typing___Optional[type___Pos2D] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bottom_right",b"bottom_right",u"top_left",b"top_left"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bottom_right",b"bottom_right",u"scanner_id",b"scanner_id",u"top_left",b"top_left"]) -> None: ...
type___KillBox = KillBox

class Thresholds(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weeding: builtin___float = ...
    thinning: builtin___float = ...
    banding: builtin___float = ...
    passed_weeding: builtin___bool = ...
    passed_thinning: builtin___bool = ...
    passed_banding: builtin___bool = ...

    def __init__(self,
        *,
        weeding : typing___Optional[builtin___float] = None,
        thinning : typing___Optional[builtin___float] = None,
        banding : typing___Optional[builtin___float] = None,
        passed_weeding : typing___Optional[builtin___bool] = None,
        passed_thinning : typing___Optional[builtin___bool] = None,
        passed_banding : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"banding",b"banding",u"passed_banding",b"passed_banding",u"passed_thinning",b"passed_thinning",u"passed_weeding",b"passed_weeding",u"thinning",b"thinning",u"weeding",b"weeding"]) -> None: ...
type___Thresholds = Thresholds

class Decisions(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weeding_weed: builtin___bool = ...
    weeding_crop: builtin___bool = ...
    thinning_weed: builtin___bool = ...
    thinning_crop: builtin___bool = ...
    keepable_crop: builtin___bool = ...
    banding_crop: builtin___bool = ...

    def __init__(self,
        *,
        weeding_weed : typing___Optional[builtin___bool] = None,
        weeding_crop : typing___Optional[builtin___bool] = None,
        thinning_weed : typing___Optional[builtin___bool] = None,
        thinning_crop : typing___Optional[builtin___bool] = None,
        keepable_crop : typing___Optional[builtin___bool] = None,
        banding_crop : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"banding_crop",b"banding_crop",u"keepable_crop",b"keepable_crop",u"thinning_crop",b"thinning_crop",u"thinning_weed",b"thinning_weed",u"weeding_crop",b"weeding_crop",u"weeding_weed",b"weeding_weed"]) -> None: ...
type___Decisions = Decisions

class TrajectorySnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class DetectionClassesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___DetectionClassesEntry = DetectionClassesEntry

    id: builtin___int = ...
    kill_status: type___KillStatusValue = ...
    is_weed: builtin___bool = ...
    x_mm: builtin___float = ...
    y_mm: builtin___float = ...
    z_mm: builtin___float = ...
    invalid_score: type___InvalidScoreReasonValue = ...
    radius_mm: builtin___float = ...
    category: typing___Text = ...
    shoot_time_requested_ms: builtin___int = ...
    shoot_time_actual_ms: builtin___int = ...
    marked_for_thinning: builtin___bool = ...
    tracker_id: builtin___int = ...
    duplicate_status: type___DuplicateStatusValue = ...
    duplicate_trajectory_id: builtin___int = ...
    assigned_lasers: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    out_of_band: builtin___bool = ...
    intersected_with_nonshootable: builtin___bool = ...
    confidence: builtin___float = ...
    thinning_state: type___ThinningStateValue = ...
    global_pos: builtin___float = ...
    doo: builtin___float = ...
    distance_perspectives_count: builtin___int = ...
    size_category_index: builtin___int = ...
    speculative_allowed: builtin___bool = ...
    protected_by_traj: builtin___bool = ...
    crop_score: builtin___float = ...
    weed_score: builtin___float = ...
    plant_score: builtin___float = ...
    num_detections_used_for_decision: builtin___int = ...
    classification: type___ClassificationValue = ...
    speculative_shoot_time_actual_ms: builtin___int = ...

    @property
    def score(self) -> type___TrajectoryScore: ...

    @property
    def detection_classes(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    @property
    def score_state(self) -> type___ScoreState: ...

    @property
    def thresholds(self) -> type___Thresholds: ...

    @property
    def decisions(self) -> type___Decisions: ...

    @property
    def snapshot_metadata(self) -> type___SnapshotMetadata: ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        kill_status : typing___Optional[type___KillStatusValue] = None,
        is_weed : typing___Optional[builtin___bool] = None,
        x_mm : typing___Optional[builtin___float] = None,
        y_mm : typing___Optional[builtin___float] = None,
        z_mm : typing___Optional[builtin___float] = None,
        score : typing___Optional[type___TrajectoryScore] = None,
        invalid_score : typing___Optional[type___InvalidScoreReasonValue] = None,
        radius_mm : typing___Optional[builtin___float] = None,
        category : typing___Optional[typing___Text] = None,
        shoot_time_requested_ms : typing___Optional[builtin___int] = None,
        shoot_time_actual_ms : typing___Optional[builtin___int] = None,
        marked_for_thinning : typing___Optional[builtin___bool] = None,
        tracker_id : typing___Optional[builtin___int] = None,
        duplicate_status : typing___Optional[type___DuplicateStatusValue] = None,
        duplicate_trajectory_id : typing___Optional[builtin___int] = None,
        assigned_lasers : typing___Optional[typing___Iterable[builtin___int]] = None,
        out_of_band : typing___Optional[builtin___bool] = None,
        intersected_with_nonshootable : typing___Optional[builtin___bool] = None,
        detection_classes : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        confidence : typing___Optional[builtin___float] = None,
        thinning_state : typing___Optional[type___ThinningStateValue] = None,
        global_pos : typing___Optional[builtin___float] = None,
        score_state : typing___Optional[type___ScoreState] = None,
        doo : typing___Optional[builtin___float] = None,
        distance_perspectives_count : typing___Optional[builtin___int] = None,
        size_category_index : typing___Optional[builtin___int] = None,
        speculative_allowed : typing___Optional[builtin___bool] = None,
        protected_by_traj : typing___Optional[builtin___bool] = None,
        thresholds : typing___Optional[type___Thresholds] = None,
        decisions : typing___Optional[type___Decisions] = None,
        crop_score : typing___Optional[builtin___float] = None,
        weed_score : typing___Optional[builtin___float] = None,
        plant_score : typing___Optional[builtin___float] = None,
        num_detections_used_for_decision : typing___Optional[builtin___int] = None,
        classification : typing___Optional[type___ClassificationValue] = None,
        speculative_shoot_time_actual_ms : typing___Optional[builtin___int] = None,
        snapshot_metadata : typing___Optional[type___SnapshotMetadata] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"decisions",b"decisions",u"invalid_score",b"invalid_score",u"score",b"score",u"score_details",b"score_details",u"score_state",b"score_state",u"snapshot_metadata",b"snapshot_metadata",u"thresholds",b"thresholds"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"assigned_lasers",b"assigned_lasers",u"category",b"category",u"classification",b"classification",u"confidence",b"confidence",u"crop_score",b"crop_score",u"decisions",b"decisions",u"detection_classes",b"detection_classes",u"distance_perspectives_count",b"distance_perspectives_count",u"doo",b"doo",u"duplicate_status",b"duplicate_status",u"duplicate_trajectory_id",b"duplicate_trajectory_id",u"global_pos",b"global_pos",u"id",b"id",u"intersected_with_nonshootable",b"intersected_with_nonshootable",u"invalid_score",b"invalid_score",u"is_weed",b"is_weed",u"kill_status",b"kill_status",u"marked_for_thinning",b"marked_for_thinning",u"num_detections_used_for_decision",b"num_detections_used_for_decision",u"out_of_band",b"out_of_band",u"plant_score",b"plant_score",u"protected_by_traj",b"protected_by_traj",u"radius_mm",b"radius_mm",u"score",b"score",u"score_details",b"score_details",u"score_state",b"score_state",u"shoot_time_actual_ms",b"shoot_time_actual_ms",u"shoot_time_requested_ms",b"shoot_time_requested_ms",u"size_category_index",b"size_category_index",u"snapshot_metadata",b"snapshot_metadata",u"speculative_allowed",b"speculative_allowed",u"speculative_shoot_time_actual_ms",b"speculative_shoot_time_actual_ms",u"thinning_state",b"thinning_state",u"thresholds",b"thresholds",u"tracker_id",b"tracker_id",u"weed_score",b"weed_score",u"x_mm",b"x_mm",u"y_mm",b"y_mm",u"z_mm",b"z_mm"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"score_details",b"score_details"]) -> typing_extensions___Literal["score","invalid_score"]: ...
type___TrajectorySnapshot = TrajectorySnapshot

class SnapshotMetadata(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pcam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...
    center_x_px: builtin___float = ...
    center_y_px: builtin___float = ...

    def __init__(self,
        *,
        pcam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        center_x_px : typing___Optional[builtin___float] = None,
        center_y_px : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_x_px",b"center_x_px",u"center_y_px",b"center_y_px",u"pcam_id",b"pcam_id",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___SnapshotMetadata = SnapshotMetadata

class BandDefinition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    offset_mm: builtin___float = ...
    width_mm: builtin___float = ...
    id: builtin___int = ...

    def __init__(self,
        *,
        offset_mm : typing___Optional[builtin___float] = None,
        width_mm : typing___Optional[builtin___float] = None,
        id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"offset_mm",b"offset_mm",u"width_mm",b"width_mm"]) -> None: ...
type___BandDefinition = BandDefinition

class CLDAlgorithmSnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    graph_points_x: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    graph_points_y: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    minimas_x: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    minimas_y: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    lines: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        graph_points_x : typing___Optional[typing___Iterable[builtin___float]] = None,
        graph_points_y : typing___Optional[typing___Iterable[builtin___float]] = None,
        minimas_x : typing___Optional[typing___Iterable[builtin___float]] = None,
        minimas_y : typing___Optional[typing___Iterable[builtin___float]] = None,
        lines : typing___Optional[typing___Iterable[builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"graph_points_x",b"graph_points_x",u"graph_points_y",b"graph_points_y",u"lines",b"lines",u"minimas_x",b"minimas_x",u"minimas_y",b"minimas_y",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___CLDAlgorithmSnapshot = CLDAlgorithmSnapshot

class DiagnosticsSnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    @property
    def trajectories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TrajectorySnapshot]: ...

    @property
    def bands(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___BandDefinition]: ...

    @property
    def kill_boxes(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___KillBox]: ...

    @property
    def banding_algorithm_snapshot(self) -> type___CLDAlgorithmSnapshot: ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        trajectories : typing___Optional[typing___Iterable[type___TrajectorySnapshot]] = None,
        bands : typing___Optional[typing___Iterable[type___BandDefinition]] = None,
        kill_boxes : typing___Optional[typing___Iterable[type___KillBox]] = None,
        banding_algorithm_snapshot : typing___Optional[type___CLDAlgorithmSnapshot] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_banding_algorithm_snapshot",b"_banding_algorithm_snapshot",u"banding_algorithm_snapshot",b"banding_algorithm_snapshot"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_banding_algorithm_snapshot",b"_banding_algorithm_snapshot",u"banding_algorithm_snapshot",b"banding_algorithm_snapshot",u"bands",b"bands",u"kill_boxes",b"kill_boxes",u"timestamp_ms",b"timestamp_ms",u"trajectories",b"trajectories"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_banding_algorithm_snapshot",b"_banding_algorithm_snapshot"]) -> typing_extensions___Literal["banding_algorithm_snapshot"]: ...
type___DiagnosticsSnapshot = DiagnosticsSnapshot

class RecordDiagnosticsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ttl_sec: builtin___int = ...
    crop_images_per_sec: builtin___float = ...
    weed_images_per_sec: builtin___float = ...

    def __init__(self,
        *,
        ttl_sec : typing___Optional[builtin___int] = None,
        crop_images_per_sec : typing___Optional[builtin___float] = None,
        weed_images_per_sec : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_images_per_sec",b"crop_images_per_sec",u"ttl_sec",b"ttl_sec",u"weed_images_per_sec",b"weed_images_per_sec"]) -> None: ...
type___RecordDiagnosticsRequest = RecordDiagnosticsRequest

class GetRecordingStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    status: type___RecordingStatusValue = ...

    def __init__(self,
        *,
        status : typing___Optional[type___RecordingStatusValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"status",b"status"]) -> None: ...
type___GetRecordingStatusResponse = GetRecordingStatusResponse

class StartSavingCropLineDetectionReplayRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    filename: typing___Text = ...
    ttl_ms: builtin___int = ...

    def __init__(self,
        *,
        filename : typing___Optional[typing___Text] = None,
        ttl_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"filename",b"filename",u"ttl_ms",b"ttl_ms"]) -> None: ...
type___StartSavingCropLineDetectionReplayRequest = StartSavingCropLineDetectionReplayRequest

class RecordAimbotInputRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    ttl_ms: builtin___int = ...
    rotary_ticks: builtin___bool = ...
    deepweed: builtin___bool = ...
    lane_heights: builtin___bool = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ttl_ms : typing___Optional[builtin___int] = None,
        rotary_ticks : typing___Optional[builtin___bool] = None,
        deepweed : typing___Optional[builtin___bool] = None,
        lane_heights : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_deepweed",b"_deepweed",u"_lane_heights",b"_lane_heights",u"_rotary_ticks",b"_rotary_ticks",u"deepweed",b"deepweed",u"lane_heights",b"lane_heights",u"rotary_ticks",b"rotary_ticks"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_deepweed",b"_deepweed",u"_lane_heights",b"_lane_heights",u"_rotary_ticks",b"_rotary_ticks",u"deepweed",b"deepweed",u"lane_heights",b"lane_heights",u"name",b"name",u"rotary_ticks",b"rotary_ticks",u"ttl_ms",b"ttl_ms"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_deepweed",b"_deepweed"]) -> typing_extensions___Literal["deepweed"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_lane_heights",b"_lane_heights"]) -> typing_extensions___Literal["lane_heights"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_rotary_ticks",b"_rotary_ticks"]) -> typing_extensions___Literal["rotary_ticks"]: ...
type___RecordAimbotInputRequest = RecordAimbotInputRequest

class ConclusionCount(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: type___ConclusionTypeValue = ...
    count: builtin___int = ...

    def __init__(self,
        *,
        type : typing___Optional[type___ConclusionTypeValue] = None,
        count : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"count",b"count",u"type",b"type"]) -> None: ...
type___ConclusionCount = ConclusionCount

class ConclusionCounter(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def counts(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ConclusionCount]: ...

    def __init__(self,
        *,
        counts : typing___Optional[typing___Iterable[type___ConclusionCount]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"counts",b"counts"]) -> None: ...
type___ConclusionCounter = ConclusionCounter

class BandDefinitions(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    banding_enabled: builtin___bool = ...
    row_has_bands_defined: builtin___bool = ...

    @property
    def bands(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___BandDefinition]: ...

    def __init__(self,
        *,
        bands : typing___Optional[typing___Iterable[type___BandDefinition]] = None,
        banding_enabled : typing___Optional[builtin___bool] = None,
        row_has_bands_defined : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"banding_enabled",b"banding_enabled",u"bands",b"bands",u"row_has_bands_defined",b"row_has_bands_defined"]) -> None: ...
type___BandDefinitions = BandDefinitions

class PlantCaptchaStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ExemplarCountsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___int = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___int] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ExemplarCountsEntry = ExemplarCountsEntry

    status: type___PlantCaptchaStatusValue = ...
    total_images: builtin___int = ...
    images_taken: builtin___int = ...
    metadata_taken: builtin___int = ...

    @property
    def exemplar_counts(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___int]: ...

    def __init__(self,
        *,
        status : typing___Optional[type___PlantCaptchaStatusValue] = None,
        total_images : typing___Optional[builtin___int] = None,
        images_taken : typing___Optional[builtin___int] = None,
        metadata_taken : typing___Optional[builtin___int] = None,
        exemplar_counts : typing___Optional[typing___Mapping[typing___Text, builtin___int]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"exemplar_counts",b"exemplar_counts",u"images_taken",b"images_taken",u"metadata_taken",b"metadata_taken",u"status",b"status",u"total_images",b"total_images"]) -> None: ...
type___PlantCaptchaStatusResponse = PlantCaptchaStatusResponse

class Embedding(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    elements: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...

    def __init__(self,
        *,
        elements : typing___Optional[typing___Iterable[builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"elements",b"elements"]) -> None: ...
type___Embedding = Embedding

class WeedClasses(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ClassesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ClassesEntry = ClassesEntry


    @property
    def classes(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    def __init__(self,
        *,
        classes : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"classes",b"classes"]) -> None: ...
type___WeedClasses = WeedClasses

class PlantCaptchaItemMetadata(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class CategoriesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___CategoriesEntry = CategoriesEntry

    class WeedCategoriesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___WeedCategoriesEntry = WeedCategoriesEntry

    class EmbeddingDistancesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___EmbeddingDistancesEntry = EmbeddingDistancesEntry

    confidence: builtin___float = ...
    x_px: builtin___int = ...
    y_px: builtin___int = ...
    x_mm: builtin___float = ...
    y_mm: builtin___float = ...
    z_mm: builtin___float = ...
    size_mm: builtin___float = ...
    doo: builtin___float = ...
    is_weed: builtin___bool = ...
    intersected_with_nonshootable: builtin___bool = ...
    confidence_history: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    is_in_band: builtin___bool = ...
    id: typing___Text = ...
    size_px: builtin___float = ...
    shoot_time_ms: builtin___int = ...
    weed_confidence_history: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    crop_confidence_history: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    size_category_index: builtin___int = ...
    initial_label: type___PlantCaptchaUserPredictionValue = ...
    plant_confidence_history: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    size_mm_history: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    num_detections_used_for_decision: builtin___int = ...

    @property
    def categories(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    @property
    def weed_categories(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    @property
    def embedding_history(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Embedding]: ...

    @property
    def weed_classes_history(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___WeedClasses]: ...

    @property
    def decisions(self) -> type___Decisions: ...

    @property
    def embedding_distances(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    def __init__(self,
        *,
        confidence : typing___Optional[builtin___float] = None,
        x_px : typing___Optional[builtin___int] = None,
        y_px : typing___Optional[builtin___int] = None,
        x_mm : typing___Optional[builtin___float] = None,
        y_mm : typing___Optional[builtin___float] = None,
        z_mm : typing___Optional[builtin___float] = None,
        size_mm : typing___Optional[builtin___float] = None,
        categories : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        doo : typing___Optional[builtin___float] = None,
        is_weed : typing___Optional[builtin___bool] = None,
        intersected_with_nonshootable : typing___Optional[builtin___bool] = None,
        confidence_history : typing___Optional[typing___Iterable[builtin___float]] = None,
        is_in_band : typing___Optional[builtin___bool] = None,
        id : typing___Optional[typing___Text] = None,
        size_px : typing___Optional[builtin___float] = None,
        shoot_time_ms : typing___Optional[builtin___int] = None,
        weed_confidence_history : typing___Optional[typing___Iterable[builtin___float]] = None,
        crop_confidence_history : typing___Optional[typing___Iterable[builtin___float]] = None,
        size_category_index : typing___Optional[builtin___int] = None,
        weed_categories : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        embedding_history : typing___Optional[typing___Iterable[type___Embedding]] = None,
        initial_label : typing___Optional[type___PlantCaptchaUserPredictionValue] = None,
        plant_confidence_history : typing___Optional[typing___Iterable[builtin___float]] = None,
        weed_classes_history : typing___Optional[typing___Iterable[type___WeedClasses]] = None,
        size_mm_history : typing___Optional[typing___Iterable[builtin___float]] = None,
        decisions : typing___Optional[type___Decisions] = None,
        num_detections_used_for_decision : typing___Optional[builtin___int] = None,
        embedding_distances : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"decisions",b"decisions"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"categories",b"categories",u"confidence",b"confidence",u"confidence_history",b"confidence_history",u"crop_confidence_history",b"crop_confidence_history",u"decisions",b"decisions",u"doo",b"doo",u"embedding_distances",b"embedding_distances",u"embedding_history",b"embedding_history",u"id",b"id",u"initial_label",b"initial_label",u"intersected_with_nonshootable",b"intersected_with_nonshootable",u"is_in_band",b"is_in_band",u"is_weed",b"is_weed",u"num_detections_used_for_decision",b"num_detections_used_for_decision",u"plant_confidence_history",b"plant_confidence_history",u"shoot_time_ms",b"shoot_time_ms",u"size_category_index",b"size_category_index",u"size_mm",b"size_mm",u"size_mm_history",b"size_mm_history",u"size_px",b"size_px",u"weed_categories",b"weed_categories",u"weed_classes_history",b"weed_classes_history",u"weed_confidence_history",b"weed_confidence_history",u"x_mm",b"x_mm",u"x_px",b"x_px",u"y_mm",b"y_mm",u"y_px",b"y_px",u"z_mm",b"z_mm"]) -> None: ...
type___PlantCaptchaItemMetadata = PlantCaptchaItemMetadata

class GetTargetingEnabledRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetTargetingEnabledRequest = GetTargetingEnabledRequest

class GetTargetingEnabledResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___GetTargetingEnabledResponse = GetTargetingEnabledResponse

class GetBootedRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetBootedRequest = GetBootedRequest

class GetBootedResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    booted: builtin___bool = ...

    def __init__(self,
        *,
        booted : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"booted",b"booted"]) -> None: ...
type___GetBootedResponse = GetBootedResponse
