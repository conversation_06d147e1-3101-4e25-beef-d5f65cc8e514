// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: cv/runtime/proto/cv_runtime.proto

package cv

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	weed_tracking "github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BufferUseCase int32

const (
	BufferUseCase_P2P         BufferUseCase = 0
	BufferUseCase_OpticalFlow BufferUseCase = 1 // Deprecated
	BufferUseCase_Predict     BufferUseCase = 2
	BufferUseCase_Drive       BufferUseCase = 3
)

// Enum value maps for BufferUseCase.
var (
	BufferUseCase_name = map[int32]string{
		0: "P2P",
		1: "OpticalFlow",
		2: "Predict",
		3: "Drive",
	}
	BufferUseCase_value = map[string]int32{
		"P2P":         0,
		"OpticalFlow": 1,
		"Predict":     2,
		"Drive":       3,
	}
)

func (x BufferUseCase) Enum() *BufferUseCase {
	p := new(BufferUseCase)
	*p = x
	return p
}

func (x BufferUseCase) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BufferUseCase) Descriptor() protoreflect.EnumDescriptor {
	return file_cv_runtime_proto_cv_runtime_proto_enumTypes[0].Descriptor()
}

func (BufferUseCase) Type() protoreflect.EnumType {
	return &file_cv_runtime_proto_cv_runtime_proto_enumTypes[0]
}

func (x BufferUseCase) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BufferUseCase.Descriptor instead.
func (BufferUseCase) EnumDescriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{0}
}

type HitClass int32

const (
	HitClass_WEED  HitClass = 0
	HitClass_CROP  HitClass = 1
	HitClass_PLANT HitClass = 2
)

// Enum value maps for HitClass.
var (
	HitClass_name = map[int32]string{
		0: "WEED",
		1: "CROP",
		2: "PLANT",
	}
	HitClass_value = map[string]int32{
		"WEED":  0,
		"CROP":  1,
		"PLANT": 2,
	}
)

func (x HitClass) Enum() *HitClass {
	p := new(HitClass)
	*p = x
	return p
}

func (x HitClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HitClass) Descriptor() protoreflect.EnumDescriptor {
	return file_cv_runtime_proto_cv_runtime_proto_enumTypes[1].Descriptor()
}

func (HitClass) Type() protoreflect.EnumType {
	return &file_cv_runtime_proto_cv_runtime_proto_enumTypes[1]
}

func (x HitClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HitClass.Descriptor instead.
func (HitClass) EnumDescriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{1}
}

type ScoreQueueType int32

const (
	ScoreQueueType_PREDICT ScoreQueueType = 0
	ScoreQueueType_CHIP    ScoreQueueType = 1
)

// Enum value maps for ScoreQueueType.
var (
	ScoreQueueType_name = map[int32]string{
		0: "PREDICT",
		1: "CHIP",
	}
	ScoreQueueType_value = map[string]int32{
		"PREDICT": 0,
		"CHIP":    1,
	}
)

func (x ScoreQueueType) Enum() *ScoreQueueType {
	p := new(ScoreQueueType)
	*p = x
	return p
}

func (x ScoreQueueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScoreQueueType) Descriptor() protoreflect.EnumDescriptor {
	return file_cv_runtime_proto_cv_runtime_proto_enumTypes[2].Descriptor()
}

func (ScoreQueueType) Type() protoreflect.EnumType {
	return &file_cv_runtime_proto_cv_runtime_proto_enumTypes[2]
}

func (x ScoreQueueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScoreQueueType.Descriptor instead.
func (ScoreQueueType) EnumDescriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{2}
}

type ErrorType int32

const (
	ErrorType_NONE                    ErrorType = 0
	ErrorType_GRAB                    ErrorType = 1
	ErrorType_CONNECTION              ErrorType = 2
	ErrorType_NO_IMPLEMENTATION       ErrorType = 3
	ErrorType_NO_IMAGE_IN_LAST_MINUTE ErrorType = 4
)

// Enum value maps for ErrorType.
var (
	ErrorType_name = map[int32]string{
		0: "NONE",
		1: "GRAB",
		2: "CONNECTION",
		3: "NO_IMPLEMENTATION",
		4: "NO_IMAGE_IN_LAST_MINUTE",
	}
	ErrorType_value = map[string]int32{
		"NONE":                    0,
		"GRAB":                    1,
		"CONNECTION":              2,
		"NO_IMPLEMENTATION":       3,
		"NO_IMAGE_IN_LAST_MINUTE": 4,
	}
)

func (x ErrorType) Enum() *ErrorType {
	p := new(ErrorType)
	*p = x
	return p
}

func (x ErrorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorType) Descriptor() protoreflect.EnumDescriptor {
	return file_cv_runtime_proto_cv_runtime_proto_enumTypes[3].Descriptor()
}

func (ErrorType) Type() protoreflect.EnumType {
	return &file_cv_runtime_proto_cv_runtime_proto_enumTypes[3]
}

func (x ErrorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorType.Descriptor instead.
func (ErrorType) EnumDescriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{3}
}

type TargetSafetyZone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Up    float32 `protobuf:"fixed32,1,opt,name=up,proto3" json:"up,omitempty"`
	Down  float32 `protobuf:"fixed32,2,opt,name=down,proto3" json:"down,omitempty"`
	Left  float32 `protobuf:"fixed32,3,opt,name=left,proto3" json:"left,omitempty"`
	Right float32 `protobuf:"fixed32,4,opt,name=right,proto3" json:"right,omitempty"`
}

func (x *TargetSafetyZone) Reset() {
	*x = TargetSafetyZone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetSafetyZone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetSafetyZone) ProtoMessage() {}

func (x *TargetSafetyZone) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetSafetyZone.ProtoReflect.Descriptor instead.
func (*TargetSafetyZone) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{0}
}

func (x *TargetSafetyZone) GetUp() float32 {
	if x != nil {
		return x.Up
	}
	return 0
}

func (x *TargetSafetyZone) GetDown() float32 {
	if x != nil {
		return x.Down
	}
	return 0
}

func (x *TargetSafetyZone) GetLeft() float32 {
	if x != nil {
		return x.Left
	}
	return 0
}

func (x *TargetSafetyZone) GetRight() float32 {
	if x != nil {
		return x.Right
	}
	return 0
}

type P2PContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PredictCamId       string  `protobuf:"bytes,1,opt,name=predict_cam_id,json=predictCamId,proto3" json:"predict_cam_id,omitempty"`
	PredictTimestampMs int64   `protobuf:"varint,2,opt,name=predict_timestamp_ms,json=predictTimestampMs,proto3" json:"predict_timestamp_ms,omitempty"`
	PredictCoordX      float32 `protobuf:"fixed32,3,opt,name=predict_coord_x,json=predictCoordX,proto3" json:"predict_coord_x,omitempty"`
	PredictCoordY      float32 `protobuf:"fixed32,4,opt,name=predict_coord_y,json=predictCoordY,proto3" json:"predict_coord_y,omitempty"`
}

func (x *P2PContext) Reset() {
	*x = P2PContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PContext) ProtoMessage() {}

func (x *P2PContext) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PContext.ProtoReflect.Descriptor instead.
func (*P2PContext) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{1}
}

func (x *P2PContext) GetPredictCamId() string {
	if x != nil {
		return x.PredictCamId
	}
	return ""
}

func (x *P2PContext) GetPredictTimestampMs() int64 {
	if x != nil {
		return x.PredictTimestampMs
	}
	return 0
}

func (x *P2PContext) GetPredictCoordX() float32 {
	if x != nil {
		return x.PredictCoordX
	}
	return 0
}

func (x *P2PContext) GetPredictCoordY() float32 {
	if x != nil {
		return x.PredictCoordY
	}
	return 0
}

type SetP2PContextRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetCamId      string            `protobuf:"bytes,1,opt,name=target_cam_id,json=targetCamId,proto3" json:"target_cam_id,omitempty"`
	PrimaryContext   *P2PContext       `protobuf:"bytes,2,opt,name=primary_context,json=primaryContext,proto3" json:"primary_context,omitempty"`
	SecondaryContext *P2PContext       `protobuf:"bytes,3,opt,name=secondary_context,json=secondaryContext,proto3,oneof" json:"secondary_context,omitempty"`
	SafetyZone       *TargetSafetyZone `protobuf:"bytes,4,opt,name=safety_zone,json=safetyZone,proto3" json:"safety_zone,omitempty"`
	TargetId         int64             `protobuf:"varint,5,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
}

func (x *SetP2PContextRequest) Reset() {
	*x = SetP2PContextRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetP2PContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetP2PContextRequest) ProtoMessage() {}

func (x *SetP2PContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetP2PContextRequest.ProtoReflect.Descriptor instead.
func (*SetP2PContextRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{2}
}

func (x *SetP2PContextRequest) GetTargetCamId() string {
	if x != nil {
		return x.TargetCamId
	}
	return ""
}

func (x *SetP2PContextRequest) GetPrimaryContext() *P2PContext {
	if x != nil {
		return x.PrimaryContext
	}
	return nil
}

func (x *SetP2PContextRequest) GetSecondaryContext() *P2PContext {
	if x != nil {
		return x.SecondaryContext
	}
	return nil
}

func (x *SetP2PContextRequest) GetSafetyZone() *TargetSafetyZone {
	if x != nil {
		return x.SafetyZone
	}
	return nil
}

func (x *SetP2PContextRequest) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

type SetP2PContextResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetP2PContextResponse) Reset() {
	*x = SetP2PContextResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetP2PContextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetP2PContextResponse) ProtoMessage() {}

func (x *SetP2PContextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetP2PContextResponse.ProtoReflect.Descriptor instead.
func (*SetP2PContextResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{3}
}

type GetCameraDimensionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *GetCameraDimensionsRequest) Reset() {
	*x = GetCameraDimensionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraDimensionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraDimensionsRequest) ProtoMessage() {}

func (x *GetCameraDimensionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraDimensionsRequest.ProtoReflect.Descriptor instead.
func (*GetCameraDimensionsRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{4}
}

func (x *GetCameraDimensionsRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

type GetCameraDimensionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width     int64 `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Height    int64 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	Transpose bool  `protobuf:"varint,3,opt,name=transpose,proto3" json:"transpose,omitempty"`
}

func (x *GetCameraDimensionsResponse) Reset() {
	*x = GetCameraDimensionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraDimensionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraDimensionsResponse) ProtoMessage() {}

func (x *GetCameraDimensionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraDimensionsResponse.ProtoReflect.Descriptor instead.
func (*GetCameraDimensionsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{5}
}

func (x *GetCameraDimensionsResponse) GetWidth() int64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *GetCameraDimensionsResponse) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *GetCameraDimensionsResponse) GetTranspose() bool {
	if x != nil {
		return x.Transpose
	}
	return false
}

type StartP2PDataCaptureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetCamId        string  `protobuf:"bytes,1,opt,name=target_cam_id,json=targetCamId,proto3" json:"target_cam_id,omitempty"`
	CaptureMissRate    float32 `protobuf:"fixed32,2,opt,name=capture_miss_rate,json=captureMissRate,proto3" json:"capture_miss_rate,omitempty"`
	CaptureSuccessRate float32 `protobuf:"fixed32,3,opt,name=capture_success_rate,json=captureSuccessRate,proto3" json:"capture_success_rate,omitempty"`
	CaptureEnabled     bool    `protobuf:"varint,4,opt,name=capture_enabled,json=captureEnabled,proto3" json:"capture_enabled,omitempty"`
	CapturePath        string  `protobuf:"bytes,5,opt,name=capture_path,json=capturePath,proto3" json:"capture_path,omitempty"`
	AfterTimestampMs   int64   `protobuf:"varint,6,opt,name=after_timestamp_ms,json=afterTimestampMs,proto3" json:"after_timestamp_ms,omitempty"`
}

func (x *StartP2PDataCaptureRequest) Reset() {
	*x = StartP2PDataCaptureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartP2PDataCaptureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartP2PDataCaptureRequest) ProtoMessage() {}

func (x *StartP2PDataCaptureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartP2PDataCaptureRequest.ProtoReflect.Descriptor instead.
func (*StartP2PDataCaptureRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{6}
}

func (x *StartP2PDataCaptureRequest) GetTargetCamId() string {
	if x != nil {
		return x.TargetCamId
	}
	return ""
}

func (x *StartP2PDataCaptureRequest) GetCaptureMissRate() float32 {
	if x != nil {
		return x.CaptureMissRate
	}
	return 0
}

func (x *StartP2PDataCaptureRequest) GetCaptureSuccessRate() float32 {
	if x != nil {
		return x.CaptureSuccessRate
	}
	return 0
}

func (x *StartP2PDataCaptureRequest) GetCaptureEnabled() bool {
	if x != nil {
		return x.CaptureEnabled
	}
	return false
}

func (x *StartP2PDataCaptureRequest) GetCapturePath() string {
	if x != nil {
		return x.CapturePath
	}
	return ""
}

func (x *StartP2PDataCaptureRequest) GetAfterTimestampMs() int64 {
	if x != nil {
		return x.AfterTimestampMs
	}
	return 0
}

type PointDetectionCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Threshold float32 `protobuf:"fixed32,1,opt,name=threshold,proto3" json:"threshold,omitempty"`
	Category  string  `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
}

func (x *PointDetectionCategory) Reset() {
	*x = PointDetectionCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PointDetectionCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PointDetectionCategory) ProtoMessage() {}

func (x *PointDetectionCategory) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PointDetectionCategory.ProtoReflect.Descriptor instead.
func (*PointDetectionCategory) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{7}
}

func (x *PointDetectionCategory) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *PointDetectionCategory) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type SegmentationDetectionCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Threshold      float32 `protobuf:"fixed32,1,opt,name=threshold,proto3" json:"threshold,omitempty"`
	Category       string  `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
	SafetyRadiusIn float32 `protobuf:"fixed32,3,opt,name=safety_radius_in,json=safetyRadiusIn,proto3" json:"safety_radius_in,omitempty"`
}

func (x *SegmentationDetectionCategory) Reset() {
	*x = SegmentationDetectionCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SegmentationDetectionCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SegmentationDetectionCategory) ProtoMessage() {}

func (x *SegmentationDetectionCategory) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SegmentationDetectionCategory.ProtoReflect.Descriptor instead.
func (*SegmentationDetectionCategory) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{8}
}

func (x *SegmentationDetectionCategory) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *SegmentationDetectionCategory) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *SegmentationDetectionCategory) GetSafetyRadiusIn() float32 {
	if x != nil {
		return x.SafetyRadiusIn
	}
	return 0
}

type DeepweedDetectionCriteriaSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// v1
	PointCategories []*PointDetectionCategory `protobuf:"bytes,1,rep,name=point_categories,json=pointCategories,proto3" json:"point_categories,omitempty"`
	// v2
	WeedPointThreshold     float32                          `protobuf:"fixed32,2,opt,name=weed_point_threshold,json=weedPointThreshold,proto3" json:"weed_point_threshold,omitempty"`
	CropPointThreshold     float32                          `protobuf:"fixed32,3,opt,name=crop_point_threshold,json=cropPointThreshold,proto3" json:"crop_point_threshold,omitempty"`
	SegmentationCategories []*SegmentationDetectionCategory `protobuf:"bytes,4,rep,name=segmentation_categories,json=segmentationCategories,proto3" json:"segmentation_categories,omitempty"`
}

func (x *DeepweedDetectionCriteriaSetting) Reset() {
	*x = DeepweedDetectionCriteriaSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedDetectionCriteriaSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedDetectionCriteriaSetting) ProtoMessage() {}

func (x *DeepweedDetectionCriteriaSetting) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedDetectionCriteriaSetting.ProtoReflect.Descriptor instead.
func (*DeepweedDetectionCriteriaSetting) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{9}
}

func (x *DeepweedDetectionCriteriaSetting) GetPointCategories() []*PointDetectionCategory {
	if x != nil {
		return x.PointCategories
	}
	return nil
}

func (x *DeepweedDetectionCriteriaSetting) GetWeedPointThreshold() float32 {
	if x != nil {
		return x.WeedPointThreshold
	}
	return 0
}

func (x *DeepweedDetectionCriteriaSetting) GetCropPointThreshold() float32 {
	if x != nil {
		return x.CropPointThreshold
	}
	return 0
}

func (x *DeepweedDetectionCriteriaSetting) GetSegmentationCategories() []*SegmentationDetectionCategory {
	if x != nil {
		return x.SegmentationCategories
	}
	return nil
}

type SetDeepweedDetectionCriteriaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedPointThreshold     float32                          `protobuf:"fixed32,1,opt,name=weed_point_threshold,json=weedPointThreshold,proto3" json:"weed_point_threshold,omitempty"`
	CropPointThreshold     float32                          `protobuf:"fixed32,2,opt,name=crop_point_threshold,json=cropPointThreshold,proto3" json:"crop_point_threshold,omitempty"`
	PointCategories        []*PointDetectionCategory        `protobuf:"bytes,3,rep,name=point_categories,json=pointCategories,proto3" json:"point_categories,omitempty"`
	SegmentationCategories []*SegmentationDetectionCategory `protobuf:"bytes,4,rep,name=segmentation_categories,json=segmentationCategories,proto3" json:"segmentation_categories,omitempty"`
}

func (x *SetDeepweedDetectionCriteriaRequest) Reset() {
	*x = SetDeepweedDetectionCriteriaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDeepweedDetectionCriteriaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDeepweedDetectionCriteriaRequest) ProtoMessage() {}

func (x *SetDeepweedDetectionCriteriaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDeepweedDetectionCriteriaRequest.ProtoReflect.Descriptor instead.
func (*SetDeepweedDetectionCriteriaRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{10}
}

func (x *SetDeepweedDetectionCriteriaRequest) GetWeedPointThreshold() float32 {
	if x != nil {
		return x.WeedPointThreshold
	}
	return 0
}

func (x *SetDeepweedDetectionCriteriaRequest) GetCropPointThreshold() float32 {
	if x != nil {
		return x.CropPointThreshold
	}
	return 0
}

func (x *SetDeepweedDetectionCriteriaRequest) GetPointCategories() []*PointDetectionCategory {
	if x != nil {
		return x.PointCategories
	}
	return nil
}

func (x *SetDeepweedDetectionCriteriaRequest) GetSegmentationCategories() []*SegmentationDetectionCategory {
	if x != nil {
		return x.SegmentationCategories
	}
	return nil
}

type SetDeepweedDetectionCriteriaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetDeepweedDetectionCriteriaResponse) Reset() {
	*x = SetDeepweedDetectionCriteriaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDeepweedDetectionCriteriaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDeepweedDetectionCriteriaResponse) ProtoMessage() {}

func (x *SetDeepweedDetectionCriteriaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDeepweedDetectionCriteriaResponse.ProtoReflect.Descriptor instead.
func (*SetDeepweedDetectionCriteriaResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{11}
}

type GetDeepweedDetectionCriteriaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetDeepweedDetectionCriteriaRequest) Reset() {
	*x = GetDeepweedDetectionCriteriaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedDetectionCriteriaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedDetectionCriteriaRequest) ProtoMessage() {}

func (x *GetDeepweedDetectionCriteriaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedDetectionCriteriaRequest.ProtoReflect.Descriptor instead.
func (*GetDeepweedDetectionCriteriaRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{12}
}

type GetDeepweedDetectionCriteriaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedPointThreshold     float32                          `protobuf:"fixed32,1,opt,name=weed_point_threshold,json=weedPointThreshold,proto3" json:"weed_point_threshold,omitempty"`
	CropPointThreshold     float32                          `protobuf:"fixed32,2,opt,name=crop_point_threshold,json=cropPointThreshold,proto3" json:"crop_point_threshold,omitempty"`
	PointCategories        []*PointDetectionCategory        `protobuf:"bytes,3,rep,name=point_categories,json=pointCategories,proto3" json:"point_categories,omitempty"`
	SegmentationCategories []*SegmentationDetectionCategory `protobuf:"bytes,4,rep,name=segmentation_categories,json=segmentationCategories,proto3" json:"segmentation_categories,omitempty"`
}

func (x *GetDeepweedDetectionCriteriaResponse) Reset() {
	*x = GetDeepweedDetectionCriteriaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedDetectionCriteriaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedDetectionCriteriaResponse) ProtoMessage() {}

func (x *GetDeepweedDetectionCriteriaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedDetectionCriteriaResponse.ProtoReflect.Descriptor instead.
func (*GetDeepweedDetectionCriteriaResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{13}
}

func (x *GetDeepweedDetectionCriteriaResponse) GetWeedPointThreshold() float32 {
	if x != nil {
		return x.WeedPointThreshold
	}
	return 0
}

func (x *GetDeepweedDetectionCriteriaResponse) GetCropPointThreshold() float32 {
	if x != nil {
		return x.CropPointThreshold
	}
	return 0
}

func (x *GetDeepweedDetectionCriteriaResponse) GetPointCategories() []*PointDetectionCategory {
	if x != nil {
		return x.PointCategories
	}
	return nil
}

func (x *GetDeepweedDetectionCriteriaResponse) GetSegmentationCategories() []*SegmentationDetectionCategory {
	if x != nil {
		return x.SegmentationCategories
	}
	return nil
}

type GetDeepweedSupportedCategoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId *string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3,oneof" json:"cam_id,omitempty"`
}

func (x *GetDeepweedSupportedCategoriesRequest) Reset() {
	*x = GetDeepweedSupportedCategoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedSupportedCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedSupportedCategoriesRequest) ProtoMessage() {}

func (x *GetDeepweedSupportedCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedSupportedCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetDeepweedSupportedCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{14}
}

func (x *GetDeepweedSupportedCategoriesRequest) GetCamId() string {
	if x != nil && x.CamId != nil {
		return *x.CamId
	}
	return ""
}

type GetDeepweedSupportedCategoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentationCategories []string `protobuf:"bytes,1,rep,name=segmentation_categories,json=segmentationCategories,proto3" json:"segmentation_categories,omitempty"`
	PointCategories        []string `protobuf:"bytes,2,rep,name=point_categories,json=pointCategories,proto3" json:"point_categories,omitempty"`
}

func (x *GetDeepweedSupportedCategoriesResponse) Reset() {
	*x = GetDeepweedSupportedCategoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedSupportedCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedSupportedCategoriesResponse) ProtoMessage() {}

func (x *GetDeepweedSupportedCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedSupportedCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetDeepweedSupportedCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{15}
}

func (x *GetDeepweedSupportedCategoriesResponse) GetSegmentationCategories() []string {
	if x != nil {
		return x.SegmentationCategories
	}
	return nil
}

func (x *GetDeepweedSupportedCategoriesResponse) GetPointCategories() []string {
	if x != nil {
		return x.PointCategories
	}
	return nil
}

type GetDeepweedIndexToCategoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *GetDeepweedIndexToCategoryRequest) Reset() {
	*x = GetDeepweedIndexToCategoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedIndexToCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedIndexToCategoryRequest) ProtoMessage() {}

func (x *GetDeepweedIndexToCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedIndexToCategoryRequest.ProtoReflect.Descriptor instead.
func (*GetDeepweedIndexToCategoryRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{16}
}

func (x *GetDeepweedIndexToCategoryRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

type GetDeepweedIndexToCategoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedPointIndexToCategory    map[int32]string `protobuf:"bytes,1,rep,name=weed_point_index_to_category,json=weedPointIndexToCategory,proto3" json:"weed_point_index_to_category,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CropPointIndexToCategory    map[int32]string `protobuf:"bytes,2,rep,name=crop_point_index_to_category,json=cropPointIndexToCategory,proto3" json:"crop_point_index_to_category,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IntersectionIndexToCategory map[int32]string `protobuf:"bytes,3,rep,name=intersection_index_to_category,json=intersectionIndexToCategory,proto3" json:"intersection_index_to_category,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetDeepweedIndexToCategoryResponse) Reset() {
	*x = GetDeepweedIndexToCategoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedIndexToCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedIndexToCategoryResponse) ProtoMessage() {}

func (x *GetDeepweedIndexToCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedIndexToCategoryResponse.ProtoReflect.Descriptor instead.
func (*GetDeepweedIndexToCategoryResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{17}
}

func (x *GetDeepweedIndexToCategoryResponse) GetWeedPointIndexToCategory() map[int32]string {
	if x != nil {
		return x.WeedPointIndexToCategory
	}
	return nil
}

func (x *GetDeepweedIndexToCategoryResponse) GetCropPointIndexToCategory() map[int32]string {
	if x != nil {
		return x.CropPointIndexToCategory
	}
	return nil
}

func (x *GetDeepweedIndexToCategoryResponse) GetIntersectionIndexToCategory() map[int32]string {
	if x != nil {
		return x.IntersectionIndexToCategory
	}
	return nil
}

type GetPredictCamMatrixRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PredictCamId string `protobuf:"bytes,1,opt,name=predict_cam_id,json=predictCamId,proto3" json:"predict_cam_id,omitempty"`
}

func (x *GetPredictCamMatrixRequest) Reset() {
	*x = GetPredictCamMatrixRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPredictCamMatrixRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPredictCamMatrixRequest) ProtoMessage() {}

func (x *GetPredictCamMatrixRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPredictCamMatrixRequest.ProtoReflect.Descriptor instead.
func (*GetPredictCamMatrixRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{18}
}

func (x *GetPredictCamMatrixRequest) GetPredictCamId() string {
	if x != nil {
		return x.PredictCamId
	}
	return ""
}

type GetPredictCamDistortionCoefficientsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PredictCamId string `protobuf:"bytes,1,opt,name=predict_cam_id,json=predictCamId,proto3" json:"predict_cam_id,omitempty"`
}

func (x *GetPredictCamDistortionCoefficientsRequest) Reset() {
	*x = GetPredictCamDistortionCoefficientsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPredictCamDistortionCoefficientsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPredictCamDistortionCoefficientsRequest) ProtoMessage() {}

func (x *GetPredictCamDistortionCoefficientsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPredictCamDistortionCoefficientsRequest.ProtoReflect.Descriptor instead.
func (*GetPredictCamDistortionCoefficientsRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{19}
}

func (x *GetPredictCamDistortionCoefficientsRequest) GetPredictCamId() string {
	if x != nil {
		return x.PredictCamId
	}
	return ""
}

type SetCameraSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamIds            []string           `protobuf:"bytes,1,rep,name=cam_ids,json=camIds,proto3" json:"cam_ids,omitempty"`
	ExposureUs        *float32           `protobuf:"fixed32,2,opt,name=exposure_us,json=exposureUs,proto3,oneof" json:"exposure_us,omitempty"`
	Gamma             *float32           `protobuf:"fixed32,3,opt,name=gamma,proto3,oneof" json:"gamma,omitempty"`
	GainDb            *float32           `protobuf:"fixed32,4,opt,name=gain_db,json=gainDb,proto3,oneof" json:"gain_db,omitempty"`
	LightSourcePreset *LightSourcePreset `protobuf:"varint,5,opt,name=light_source_preset,json=lightSourcePreset,proto3,enum=lib.common.camera.LightSourcePreset,oneof" json:"light_source_preset,omitempty"`
	WbRatioRed        *float32           `protobuf:"fixed32,6,opt,name=wb_ratio_red,json=wbRatioRed,proto3,oneof" json:"wb_ratio_red,omitempty"`
	WbRatioGreen      *float32           `protobuf:"fixed32,7,opt,name=wb_ratio_green,json=wbRatioGreen,proto3,oneof" json:"wb_ratio_green,omitempty"`
	WbRatioBlue       *float32           `protobuf:"fixed32,8,opt,name=wb_ratio_blue,json=wbRatioBlue,proto3,oneof" json:"wb_ratio_blue,omitempty"`
	RoiOffsetX        *int64             `protobuf:"varint,9,opt,name=roi_offset_x,json=roiOffsetX,proto3,oneof" json:"roi_offset_x,omitempty"`
	RoiOffsetY        *int64             `protobuf:"varint,10,opt,name=roi_offset_y,json=roiOffsetY,proto3,oneof" json:"roi_offset_y,omitempty"`
	Mirror            *bool              `protobuf:"varint,11,opt,name=mirror,proto3,oneof" json:"mirror,omitempty"`
	Flip              *bool              `protobuf:"varint,12,opt,name=flip,proto3,oneof" json:"flip,omitempty"`
	Strobing          *bool              `protobuf:"varint,13,opt,name=strobing,proto3,oneof" json:"strobing,omitempty"`
	Ptp               *bool              `protobuf:"varint,14,opt,name=ptp,proto3,oneof" json:"ptp,omitempty"`
	AutoWhitebalance  *bool              `protobuf:"varint,15,opt,name=auto_whitebalance,json=autoWhitebalance,proto3,oneof" json:"auto_whitebalance,omitempty"`
}

func (x *SetCameraSettingsRequest) Reset() {
	*x = SetCameraSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCameraSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCameraSettingsRequest) ProtoMessage() {}

func (x *SetCameraSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCameraSettingsRequest.ProtoReflect.Descriptor instead.
func (*SetCameraSettingsRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{20}
}

func (x *SetCameraSettingsRequest) GetCamIds() []string {
	if x != nil {
		return x.CamIds
	}
	return nil
}

func (x *SetCameraSettingsRequest) GetExposureUs() float32 {
	if x != nil && x.ExposureUs != nil {
		return *x.ExposureUs
	}
	return 0
}

func (x *SetCameraSettingsRequest) GetGamma() float32 {
	if x != nil && x.Gamma != nil {
		return *x.Gamma
	}
	return 0
}

func (x *SetCameraSettingsRequest) GetGainDb() float32 {
	if x != nil && x.GainDb != nil {
		return *x.GainDb
	}
	return 0
}

func (x *SetCameraSettingsRequest) GetLightSourcePreset() LightSourcePreset {
	if x != nil && x.LightSourcePreset != nil {
		return *x.LightSourcePreset
	}
	return LightSourcePreset_kOff
}

func (x *SetCameraSettingsRequest) GetWbRatioRed() float32 {
	if x != nil && x.WbRatioRed != nil {
		return *x.WbRatioRed
	}
	return 0
}

func (x *SetCameraSettingsRequest) GetWbRatioGreen() float32 {
	if x != nil && x.WbRatioGreen != nil {
		return *x.WbRatioGreen
	}
	return 0
}

func (x *SetCameraSettingsRequest) GetWbRatioBlue() float32 {
	if x != nil && x.WbRatioBlue != nil {
		return *x.WbRatioBlue
	}
	return 0
}

func (x *SetCameraSettingsRequest) GetRoiOffsetX() int64 {
	if x != nil && x.RoiOffsetX != nil {
		return *x.RoiOffsetX
	}
	return 0
}

func (x *SetCameraSettingsRequest) GetRoiOffsetY() int64 {
	if x != nil && x.RoiOffsetY != nil {
		return *x.RoiOffsetY
	}
	return 0
}

func (x *SetCameraSettingsRequest) GetMirror() bool {
	if x != nil && x.Mirror != nil {
		return *x.Mirror
	}
	return false
}

func (x *SetCameraSettingsRequest) GetFlip() bool {
	if x != nil && x.Flip != nil {
		return *x.Flip
	}
	return false
}

func (x *SetCameraSettingsRequest) GetStrobing() bool {
	if x != nil && x.Strobing != nil {
		return *x.Strobing
	}
	return false
}

func (x *SetCameraSettingsRequest) GetPtp() bool {
	if x != nil && x.Ptp != nil {
		return *x.Ptp
	}
	return false
}

func (x *SetCameraSettingsRequest) GetAutoWhitebalance() bool {
	if x != nil && x.AutoWhitebalance != nil {
		return *x.AutoWhitebalance
	}
	return false
}

type SetCameraSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamIds []string `protobuf:"bytes,1,rep,name=cam_ids,json=camIds,proto3" json:"cam_ids,omitempty"`
}

func (x *SetCameraSettingsResponse) Reset() {
	*x = SetCameraSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCameraSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCameraSettingsResponse) ProtoMessage() {}

func (x *SetCameraSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCameraSettingsResponse.ProtoReflect.Descriptor instead.
func (*SetCameraSettingsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{21}
}

func (x *SetCameraSettingsResponse) GetCamIds() []string {
	if x != nil {
		return x.CamIds
	}
	return nil
}

type SetAutoWhitebalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable bool     `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	CamIds []string `protobuf:"bytes,2,rep,name=cam_ids,json=camIds,proto3" json:"cam_ids,omitempty"`
}

func (x *SetAutoWhitebalanceRequest) Reset() {
	*x = SetAutoWhitebalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAutoWhitebalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAutoWhitebalanceRequest) ProtoMessage() {}

func (x *SetAutoWhitebalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAutoWhitebalanceRequest.ProtoReflect.Descriptor instead.
func (*SetAutoWhitebalanceRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{22}
}

func (x *SetAutoWhitebalanceRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *SetAutoWhitebalanceRequest) GetCamIds() []string {
	if x != nil {
		return x.CamIds
	}
	return nil
}

type SetAutoWhitebalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetAutoWhitebalanceResponse) Reset() {
	*x = SetAutoWhitebalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAutoWhitebalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAutoWhitebalanceResponse) ProtoMessage() {}

func (x *SetAutoWhitebalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAutoWhitebalanceResponse.ProtoReflect.Descriptor instead.
func (*SetAutoWhitebalanceResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{23}
}

type GetCameraSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamIds []string `protobuf:"bytes,1,rep,name=cam_ids,json=camIds,proto3" json:"cam_ids,omitempty"`
}

func (x *GetCameraSettingsRequest) Reset() {
	*x = GetCameraSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraSettingsRequest) ProtoMessage() {}

func (x *GetCameraSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetCameraSettingsRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{24}
}

func (x *GetCameraSettingsRequest) GetCamIds() []string {
	if x != nil {
		return x.CamIds
	}
	return nil
}

type CameraSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId             string             `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	ExposureUs        *float32           `protobuf:"fixed32,2,opt,name=exposure_us,json=exposureUs,proto3,oneof" json:"exposure_us,omitempty"`
	Gamma             *float32           `protobuf:"fixed32,3,opt,name=gamma,proto3,oneof" json:"gamma,omitempty"`
	GainDb            *float32           `protobuf:"fixed32,4,opt,name=gain_db,json=gainDb,proto3,oneof" json:"gain_db,omitempty"`
	LightSourcePreset *LightSourcePreset `protobuf:"varint,5,opt,name=light_source_preset,json=lightSourcePreset,proto3,enum=lib.common.camera.LightSourcePreset,oneof" json:"light_source_preset,omitempty"`
	WbRatioRed        *float32           `protobuf:"fixed32,6,opt,name=wb_ratio_red,json=wbRatioRed,proto3,oneof" json:"wb_ratio_red,omitempty"`
	WbRatioGreen      *float32           `protobuf:"fixed32,7,opt,name=wb_ratio_green,json=wbRatioGreen,proto3,oneof" json:"wb_ratio_green,omitempty"`
	WbRatioBlue       *float32           `protobuf:"fixed32,8,opt,name=wb_ratio_blue,json=wbRatioBlue,proto3,oneof" json:"wb_ratio_blue,omitempty"`
	RoiWidth          *int64             `protobuf:"varint,9,opt,name=roi_width,json=roiWidth,proto3,oneof" json:"roi_width,omitempty"`
	RoiHeight         *int64             `protobuf:"varint,10,opt,name=roi_height,json=roiHeight,proto3,oneof" json:"roi_height,omitempty"`
	RoiOffsetX        *int64             `protobuf:"varint,11,opt,name=roi_offset_x,json=roiOffsetX,proto3,oneof" json:"roi_offset_x,omitempty"`
	RoiOffsetY        *int64             `protobuf:"varint,12,opt,name=roi_offset_y,json=roiOffsetY,proto3,oneof" json:"roi_offset_y,omitempty"`
	GpuId             *int64             `protobuf:"varint,13,opt,name=gpu_id,json=gpuId,proto3,oneof" json:"gpu_id,omitempty"`
	Mirror            bool               `protobuf:"varint,14,opt,name=mirror,proto3" json:"mirror,omitempty"`
	Flip              bool               `protobuf:"varint,15,opt,name=flip,proto3" json:"flip,omitempty"`
	Strobing          bool               `protobuf:"varint,16,opt,name=strobing,proto3" json:"strobing,omitempty"`
	Ptp               bool               `protobuf:"varint,17,opt,name=ptp,proto3" json:"ptp,omitempty"`
	AutoWhitebalance  *bool              `protobuf:"varint,18,opt,name=auto_whitebalance,json=autoWhitebalance,proto3,oneof" json:"auto_whitebalance,omitempty"`
}

func (x *CameraSettingsResponse) Reset() {
	*x = CameraSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraSettingsResponse) ProtoMessage() {}

func (x *CameraSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraSettingsResponse.ProtoReflect.Descriptor instead.
func (*CameraSettingsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{25}
}

func (x *CameraSettingsResponse) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *CameraSettingsResponse) GetExposureUs() float32 {
	if x != nil && x.ExposureUs != nil {
		return *x.ExposureUs
	}
	return 0
}

func (x *CameraSettingsResponse) GetGamma() float32 {
	if x != nil && x.Gamma != nil {
		return *x.Gamma
	}
	return 0
}

func (x *CameraSettingsResponse) GetGainDb() float32 {
	if x != nil && x.GainDb != nil {
		return *x.GainDb
	}
	return 0
}

func (x *CameraSettingsResponse) GetLightSourcePreset() LightSourcePreset {
	if x != nil && x.LightSourcePreset != nil {
		return *x.LightSourcePreset
	}
	return LightSourcePreset_kOff
}

func (x *CameraSettingsResponse) GetWbRatioRed() float32 {
	if x != nil && x.WbRatioRed != nil {
		return *x.WbRatioRed
	}
	return 0
}

func (x *CameraSettingsResponse) GetWbRatioGreen() float32 {
	if x != nil && x.WbRatioGreen != nil {
		return *x.WbRatioGreen
	}
	return 0
}

func (x *CameraSettingsResponse) GetWbRatioBlue() float32 {
	if x != nil && x.WbRatioBlue != nil {
		return *x.WbRatioBlue
	}
	return 0
}

func (x *CameraSettingsResponse) GetRoiWidth() int64 {
	if x != nil && x.RoiWidth != nil {
		return *x.RoiWidth
	}
	return 0
}

func (x *CameraSettingsResponse) GetRoiHeight() int64 {
	if x != nil && x.RoiHeight != nil {
		return *x.RoiHeight
	}
	return 0
}

func (x *CameraSettingsResponse) GetRoiOffsetX() int64 {
	if x != nil && x.RoiOffsetX != nil {
		return *x.RoiOffsetX
	}
	return 0
}

func (x *CameraSettingsResponse) GetRoiOffsetY() int64 {
	if x != nil && x.RoiOffsetY != nil {
		return *x.RoiOffsetY
	}
	return 0
}

func (x *CameraSettingsResponse) GetGpuId() int64 {
	if x != nil && x.GpuId != nil {
		return *x.GpuId
	}
	return 0
}

func (x *CameraSettingsResponse) GetMirror() bool {
	if x != nil {
		return x.Mirror
	}
	return false
}

func (x *CameraSettingsResponse) GetFlip() bool {
	if x != nil {
		return x.Flip
	}
	return false
}

func (x *CameraSettingsResponse) GetStrobing() bool {
	if x != nil {
		return x.Strobing
	}
	return false
}

func (x *CameraSettingsResponse) GetPtp() bool {
	if x != nil {
		return x.Ptp
	}
	return false
}

func (x *CameraSettingsResponse) GetAutoWhitebalance() bool {
	if x != nil && x.AutoWhitebalance != nil {
		return *x.AutoWhitebalance
	}
	return false
}

type GetCameraSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CameraSettingsResponse []*CameraSettingsResponse `protobuf:"bytes,1,rep,name=camera_settings_response,json=cameraSettingsResponse,proto3" json:"camera_settings_response,omitempty"`
}

func (x *GetCameraSettingsResponse) Reset() {
	*x = GetCameraSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraSettingsResponse) ProtoMessage() {}

func (x *GetCameraSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraSettingsResponse.ProtoReflect.Descriptor instead.
func (*GetCameraSettingsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{26}
}

func (x *GetCameraSettingsResponse) GetCameraSettingsResponse() []*CameraSettingsResponse {
	if x != nil {
		return x.CameraSettingsResponse
	}
	return nil
}

type StartBurstRecordFramesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId                   string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	DurationMs              int64  `protobuf:"varint,2,opt,name=duration_ms,json=durationMs,proto3" json:"duration_ms,omitempty"`
	Path                    string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	DontCapturePredictImage bool   `protobuf:"varint,4,opt,name=dont_capture_predict_image,json=dontCapturePredictImage,proto3" json:"dont_capture_predict_image,omitempty"`
	DownsampleFactor        int32  `protobuf:"varint,5,opt,name=downsample_factor,json=downsampleFactor,proto3" json:"downsample_factor,omitempty"`
}

func (x *StartBurstRecordFramesRequest) Reset() {
	*x = StartBurstRecordFramesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartBurstRecordFramesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartBurstRecordFramesRequest) ProtoMessage() {}

func (x *StartBurstRecordFramesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartBurstRecordFramesRequest.ProtoReflect.Descriptor instead.
func (*StartBurstRecordFramesRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{27}
}

func (x *StartBurstRecordFramesRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *StartBurstRecordFramesRequest) GetDurationMs() int64 {
	if x != nil {
		return x.DurationMs
	}
	return 0
}

func (x *StartBurstRecordFramesRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *StartBurstRecordFramesRequest) GetDontCapturePredictImage() bool {
	if x != nil {
		return x.DontCapturePredictImage
	}
	return false
}

func (x *StartBurstRecordFramesRequest) GetDownsampleFactor() int32 {
	if x != nil {
		return x.DownsampleFactor
	}
	return 0
}

type StartBurstRecordFramesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartBurstRecordFramesResponse) Reset() {
	*x = StartBurstRecordFramesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartBurstRecordFramesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartBurstRecordFramesResponse) ProtoMessage() {}

func (x *StartBurstRecordFramesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartBurstRecordFramesResponse.ProtoReflect.Descriptor instead.
func (*StartBurstRecordFramesResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{28}
}

type StopBurstRecordFramesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId                string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	LastFrameTimestampMs *int64 `protobuf:"varint,2,opt,name=last_frame_timestamp_ms,json=lastFrameTimestampMs,proto3,oneof" json:"last_frame_timestamp_ms,omitempty"`
}

func (x *StopBurstRecordFramesRequest) Reset() {
	*x = StopBurstRecordFramesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopBurstRecordFramesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopBurstRecordFramesRequest) ProtoMessage() {}

func (x *StopBurstRecordFramesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopBurstRecordFramesRequest.ProtoReflect.Descriptor instead.
func (*StopBurstRecordFramesRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{29}
}

func (x *StopBurstRecordFramesRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *StopBurstRecordFramesRequest) GetLastFrameTimestampMs() int64 {
	if x != nil && x.LastFrameTimestampMs != nil {
		return *x.LastFrameTimestampMs
	}
	return 0
}

type StopBurstRecordFramesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopBurstRecordFramesResponse) Reset() {
	*x = StopBurstRecordFramesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopBurstRecordFramesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopBurstRecordFramesResponse) ProtoMessage() {}

func (x *StopBurstRecordFramesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopBurstRecordFramesResponse.ProtoReflect.Descriptor instead.
func (*StopBurstRecordFramesResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{30}
}

type P2POutputProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Matched            bool    `protobuf:"varint,1,opt,name=matched,proto3" json:"matched,omitempty"`
	TargetCoordX       float32 `protobuf:"fixed32,2,opt,name=target_coord_x,json=targetCoordX,proto3" json:"target_coord_x,omitempty"`
	TargetCoordY       float32 `protobuf:"fixed32,3,opt,name=target_coord_y,json=targetCoordY,proto3" json:"target_coord_y,omitempty"`
	TargetTimestampMs  int64   `protobuf:"varint,4,opt,name=target_timestamp_ms,json=targetTimestampMs,proto3" json:"target_timestamp_ms,omitempty"`
	PredictTimestampMs int64   `protobuf:"varint,5,opt,name=predict_timestamp_ms,json=predictTimestampMs,proto3" json:"predict_timestamp_ms,omitempty"`
	Safe               bool    `protobuf:"varint,6,opt,name=safe,proto3" json:"safe,omitempty"`
	PredictCoordX      float32 `protobuf:"fixed32,7,opt,name=predict_coord_x,json=predictCoordX,proto3" json:"predict_coord_x,omitempty"`
	PredictCoordY      float32 `protobuf:"fixed32,8,opt,name=predict_coord_y,json=predictCoordY,proto3" json:"predict_coord_y,omitempty"`
	PredictCam         string  `protobuf:"bytes,9,opt,name=predict_cam,json=predictCam,proto3" json:"predict_cam,omitempty"`
}

func (x *P2POutputProto) Reset() {
	*x = P2POutputProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2POutputProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2POutputProto) ProtoMessage() {}

func (x *P2POutputProto) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2POutputProto.ProtoReflect.Descriptor instead.
func (*P2POutputProto) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{31}
}

func (x *P2POutputProto) GetMatched() bool {
	if x != nil {
		return x.Matched
	}
	return false
}

func (x *P2POutputProto) GetTargetCoordX() float32 {
	if x != nil {
		return x.TargetCoordX
	}
	return 0
}

func (x *P2POutputProto) GetTargetCoordY() float32 {
	if x != nil {
		return x.TargetCoordY
	}
	return 0
}

func (x *P2POutputProto) GetTargetTimestampMs() int64 {
	if x != nil {
		return x.TargetTimestampMs
	}
	return 0
}

func (x *P2POutputProto) GetPredictTimestampMs() int64 {
	if x != nil {
		return x.PredictTimestampMs
	}
	return 0
}

func (x *P2POutputProto) GetSafe() bool {
	if x != nil {
		return x.Safe
	}
	return false
}

func (x *P2POutputProto) GetPredictCoordX() float32 {
	if x != nil {
		return x.PredictCoordX
	}
	return 0
}

func (x *P2POutputProto) GetPredictCoordY() float32 {
	if x != nil {
		return x.PredictCoordY
	}
	return 0
}

func (x *P2POutputProto) GetPredictCam() string {
	if x != nil {
		return x.PredictCam
	}
	return ""
}

type GetNextP2POutputRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId       string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	TimeoutMs   int64  `protobuf:"varint,3,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
}

func (x *GetNextP2POutputRequest) Reset() {
	*x = GetNextP2POutputRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextP2POutputRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextP2POutputRequest) ProtoMessage() {}

func (x *GetNextP2POutputRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextP2POutputRequest.ProtoReflect.Descriptor instead.
func (*GetNextP2POutputRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{32}
}

func (x *GetNextP2POutputRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *GetNextP2POutputRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetNextP2POutputRequest) GetTimeoutMs() int64 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

type GetConnectorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamIds       []string `protobuf:"bytes,1,rep,name=cam_ids,json=camIds,proto3" json:"cam_ids,omitempty"`
	ConnectorIds []string `protobuf:"bytes,2,rep,name=connector_ids,json=connectorIds,proto3" json:"connector_ids,omitempty"`
}

func (x *GetConnectorsRequest) Reset() {
	*x = GetConnectorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectorsRequest) ProtoMessage() {}

func (x *GetConnectorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectorsRequest.ProtoReflect.Descriptor instead.
func (*GetConnectorsRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{33}
}

func (x *GetConnectorsRequest) GetCamIds() []string {
	if x != nil {
		return x.CamIds
	}
	return nil
}

func (x *GetConnectorsRequest) GetConnectorIds() []string {
	if x != nil {
		return x.ConnectorIds
	}
	return nil
}

type ConnectorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId          string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	ConnectorId    string `protobuf:"bytes,2,opt,name=connector_id,json=connectorId,proto3" json:"connector_id,omitempty"`
	IsEnabled      bool   `protobuf:"varint,3,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	ReductionRatio int64  `protobuf:"varint,4,opt,name=reduction_ratio,json=reductionRatio,proto3" json:"reduction_ratio,omitempty"`
}

func (x *ConnectorResponse) Reset() {
	*x = ConnectorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectorResponse) ProtoMessage() {}

func (x *ConnectorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectorResponse.ProtoReflect.Descriptor instead.
func (*ConnectorResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{34}
}

func (x *ConnectorResponse) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *ConnectorResponse) GetConnectorId() string {
	if x != nil {
		return x.ConnectorId
	}
	return ""
}

func (x *ConnectorResponse) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *ConnectorResponse) GetReductionRatio() int64 {
	if x != nil {
		return x.ReductionRatio
	}
	return 0
}

type GetConnectorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectorResponse []*ConnectorResponse `protobuf:"bytes,1,rep,name=connector_response,json=connectorResponse,proto3" json:"connector_response,omitempty"`
}

func (x *GetConnectorsResponse) Reset() {
	*x = GetConnectorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectorsResponse) ProtoMessage() {}

func (x *GetConnectorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectorsResponse.ProtoReflect.Descriptor instead.
func (*GetConnectorsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{35}
}

func (x *GetConnectorsResponse) GetConnectorResponse() []*ConnectorResponse {
	if x != nil {
		return x.ConnectorResponse
	}
	return nil
}

type SetConnectorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamIds         []string `protobuf:"bytes,1,rep,name=cam_ids,json=camIds,proto3" json:"cam_ids,omitempty"`
	ConnectorIds   []string `protobuf:"bytes,2,rep,name=connector_ids,json=connectorIds,proto3" json:"connector_ids,omitempty"`
	IsEnabled      *bool    `protobuf:"varint,3,opt,name=is_enabled,json=isEnabled,proto3,oneof" json:"is_enabled,omitempty"`
	ReductionRatio *int64   `protobuf:"varint,4,opt,name=reduction_ratio,json=reductionRatio,proto3,oneof" json:"reduction_ratio,omitempty"`
}

func (x *SetConnectorsRequest) Reset() {
	*x = SetConnectorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetConnectorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetConnectorsRequest) ProtoMessage() {}

func (x *SetConnectorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetConnectorsRequest.ProtoReflect.Descriptor instead.
func (*SetConnectorsRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{36}
}

func (x *SetConnectorsRequest) GetCamIds() []string {
	if x != nil {
		return x.CamIds
	}
	return nil
}

func (x *SetConnectorsRequest) GetConnectorIds() []string {
	if x != nil {
		return x.ConnectorIds
	}
	return nil
}

func (x *SetConnectorsRequest) GetIsEnabled() bool {
	if x != nil && x.IsEnabled != nil {
		return *x.IsEnabled
	}
	return false
}

func (x *SetConnectorsRequest) GetReductionRatio() int64 {
	if x != nil && x.ReductionRatio != nil {
		return *x.ReductionRatio
	}
	return 0
}

type SetConnectorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetConnectorsResponse) Reset() {
	*x = SetConnectorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetConnectorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetConnectorsResponse) ProtoMessage() {}

func (x *SetConnectorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetConnectorsResponse.ProtoReflect.Descriptor instead.
func (*SetConnectorsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{37}
}

type NodeTiming struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	FpsMean         float32            `protobuf:"fixed32,2,opt,name=fps_mean,json=fpsMean,proto3" json:"fps_mean,omitempty"`
	Fps_99Pct       float32            `protobuf:"fixed32,3,opt,name=fps_99pct,json=fps99pct,proto3" json:"fps_99pct,omitempty"`
	LatencyMsMean   float32            `protobuf:"fixed32,4,opt,name=latency_ms_mean,json=latencyMsMean,proto3" json:"latency_ms_mean,omitempty"`
	LatencyMs_99Pct float32            `protobuf:"fixed32,5,opt,name=latency_ms_99pct,json=latencyMs99pct,proto3" json:"latency_ms_99pct,omitempty"`
	State           string             `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	StateTimings    map[string]float32 `protobuf:"bytes,7,rep,name=state_timings,json=stateTimings,proto3" json:"state_timings,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *NodeTiming) Reset() {
	*x = NodeTiming{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeTiming) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeTiming) ProtoMessage() {}

func (x *NodeTiming) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeTiming.ProtoReflect.Descriptor instead.
func (*NodeTiming) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{38}
}

func (x *NodeTiming) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeTiming) GetFpsMean() float32 {
	if x != nil {
		return x.FpsMean
	}
	return 0
}

func (x *NodeTiming) GetFps_99Pct() float32 {
	if x != nil {
		return x.Fps_99Pct
	}
	return 0
}

func (x *NodeTiming) GetLatencyMsMean() float32 {
	if x != nil {
		return x.LatencyMsMean
	}
	return 0
}

func (x *NodeTiming) GetLatencyMs_99Pct() float32 {
	if x != nil {
		return x.LatencyMs_99Pct
	}
	return 0
}

func (x *NodeTiming) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *NodeTiming) GetStateTimings() map[string]float32 {
	if x != nil {
		return x.StateTimings
	}
	return nil
}

type GetTimingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetTimingRequest) Reset() {
	*x = GetTimingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTimingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTimingRequest) ProtoMessage() {}

func (x *GetTimingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTimingRequest.ProtoReflect.Descriptor instead.
func (*GetTimingRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{39}
}

type GetTimingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeTiming []*NodeTiming `protobuf:"bytes,1,rep,name=node_timing,json=nodeTiming,proto3" json:"node_timing,omitempty"`
}

func (x *GetTimingResponse) Reset() {
	*x = GetTimingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTimingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTimingResponse) ProtoMessage() {}

func (x *GetTimingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTimingResponse.ProtoReflect.Descriptor instead.
func (*GetTimingResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{40}
}

func (x *GetTimingResponse) GetNodeTiming() []*NodeTiming {
	if x != nil {
		return x.NodeTiming
	}
	return nil
}

type PredictRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId        string   `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	FilePaths    []string `protobuf:"bytes,2,rep,name=file_paths,json=filePaths,proto3" json:"file_paths,omitempty"`
	TimestampsMs []int64  `protobuf:"varint,3,rep,packed,name=timestamps_ms,json=timestampsMs,proto3" json:"timestamps_ms,omitempty"`
}

func (x *PredictRequest) Reset() {
	*x = PredictRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictRequest) ProtoMessage() {}

func (x *PredictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictRequest.ProtoReflect.Descriptor instead.
func (*PredictRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{41}
}

func (x *PredictRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *PredictRequest) GetFilePaths() []string {
	if x != nil {
		return x.FilePaths
	}
	return nil
}

func (x *PredictRequest) GetTimestampsMs() []int64 {
	if x != nil {
		return x.TimestampsMs
	}
	return nil
}

type PredictResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PredictResponse) Reset() {
	*x = PredictResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictResponse) ProtoMessage() {}

func (x *PredictResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictResponse.ProtoReflect.Descriptor instead.
func (*PredictResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{42}
}

type LoadAndQueueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId        string   `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	FilePaths    []string `protobuf:"bytes,2,rep,name=file_paths,json=filePaths,proto3" json:"file_paths,omitempty"`
	TimestampsMs []int64  `protobuf:"varint,3,rep,packed,name=timestamps_ms,json=timestampsMs,proto3" json:"timestamps_ms,omitempty"`
}

func (x *LoadAndQueueRequest) Reset() {
	*x = LoadAndQueueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadAndQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadAndQueueRequest) ProtoMessage() {}

func (x *LoadAndQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadAndQueueRequest.ProtoReflect.Descriptor instead.
func (*LoadAndQueueRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{43}
}

func (x *LoadAndQueueRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *LoadAndQueueRequest) GetFilePaths() []string {
	if x != nil {
		return x.FilePaths
	}
	return nil
}

func (x *LoadAndQueueRequest) GetTimestampsMs() []int64 {
	if x != nil {
		return x.TimestampsMs
	}
	return nil
}

type LoadAndQueueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LoadAndQueueResponse) Reset() {
	*x = LoadAndQueueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadAndQueueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadAndQueueResponse) ProtoMessage() {}

func (x *LoadAndQueueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadAndQueueResponse.ProtoReflect.Descriptor instead.
func (*LoadAndQueueResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{44}
}

type SetImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId    string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	FilePath string `protobuf:"bytes,2,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *SetImageRequest) Reset() {
	*x = SetImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImageRequest) ProtoMessage() {}

func (x *SetImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImageRequest.ProtoReflect.Descriptor instead.
func (*SetImageRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{45}
}

func (x *SetImageRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *SetImageRequest) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type SetImageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetImageResponse) Reset() {
	*x = SetImageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImageResponse) ProtoMessage() {}

func (x *SetImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImageResponse.ProtoReflect.Descriptor instead.
func (*SetImageResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{46}
}

type UnsetImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *UnsetImageRequest) Reset() {
	*x = UnsetImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnsetImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsetImageRequest) ProtoMessage() {}

func (x *UnsetImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsetImageRequest.ProtoReflect.Descriptor instead.
func (*UnsetImageRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{47}
}

func (x *UnsetImageRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

type UnsetImageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnsetImageResponse) Reset() {
	*x = UnsetImageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnsetImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsetImageResponse) ProtoMessage() {}

func (x *UnsetImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsetImageResponse.ProtoReflect.Descriptor instead.
func (*UnsetImageResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{48}
}

type GetModelPathsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetModelPathsRequest) Reset() {
	*x = GetModelPathsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelPathsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelPathsRequest) ProtoMessage() {}

func (x *GetModelPathsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelPathsRequest.ProtoReflect.Descriptor instead.
func (*GetModelPathsRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{49}
}

type GetModelPathsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	P2P      *string `protobuf:"bytes,1,opt,name=p2p,proto3,oneof" json:"p2p,omitempty"`
	Deepweed *string `protobuf:"bytes,2,opt,name=deepweed,proto3,oneof" json:"deepweed,omitempty"`
	Furrows  *string `protobuf:"bytes,3,opt,name=furrows,proto3,oneof" json:"furrows,omitempty"`
}

func (x *GetModelPathsResponse) Reset() {
	*x = GetModelPathsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelPathsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelPathsResponse) ProtoMessage() {}

func (x *GetModelPathsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelPathsResponse.ProtoReflect.Descriptor instead.
func (*GetModelPathsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{50}
}

func (x *GetModelPathsResponse) GetP2P() string {
	if x != nil && x.P2P != nil {
		return *x.P2P
	}
	return ""
}

func (x *GetModelPathsResponse) GetDeepweed() string {
	if x != nil && x.Deepweed != nil {
		return *x.Deepweed
	}
	return ""
}

func (x *GetModelPathsResponse) GetFurrows() string {
	if x != nil && x.Furrows != nil {
		return *x.Furrows
	}
	return ""
}

type GetCameraTemperaturesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCameraTemperaturesRequest) Reset() {
	*x = GetCameraTemperaturesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraTemperaturesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraTemperaturesRequest) ProtoMessage() {}

func (x *GetCameraTemperaturesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraTemperaturesRequest.ProtoReflect.Descriptor instead.
func (*GetCameraTemperaturesRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{51}
}

type CameraTemperature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId       string  `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Temperature float64 `protobuf:"fixed64,2,opt,name=temperature,proto3" json:"temperature,omitempty"`
}

func (x *CameraTemperature) Reset() {
	*x = CameraTemperature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraTemperature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraTemperature) ProtoMessage() {}

func (x *CameraTemperature) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraTemperature.ProtoReflect.Descriptor instead.
func (*CameraTemperature) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{52}
}

func (x *CameraTemperature) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *CameraTemperature) GetTemperature() float64 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

type GetCameraTemperaturesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Temperature []*CameraTemperature `protobuf:"bytes,1,rep,name=temperature,proto3" json:"temperature,omitempty"`
}

func (x *GetCameraTemperaturesResponse) Reset() {
	*x = GetCameraTemperaturesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraTemperaturesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraTemperaturesResponse) ProtoMessage() {}

func (x *GetCameraTemperaturesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraTemperaturesResponse.ProtoReflect.Descriptor instead.
func (*GetCameraTemperaturesResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{53}
}

func (x *GetCameraTemperaturesResponse) GetTemperature() []*CameraTemperature {
	if x != nil {
		return x.Temperature
	}
	return nil
}

type GeoLLA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat         *float64 `protobuf:"fixed64,1,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	Lng         *float64 `protobuf:"fixed64,2,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
	Alt         *float64 `protobuf:"fixed64,3,opt,name=alt,proto3,oneof" json:"alt,omitempty"`
	TimestampMs *int64   `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3,oneof" json:"timestamp_ms,omitempty"`
}

func (x *GeoLLA) Reset() {
	*x = GeoLLA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeoLLA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeoLLA) ProtoMessage() {}

func (x *GeoLLA) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeoLLA.ProtoReflect.Descriptor instead.
func (*GeoLLA) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{54}
}

func (x *GeoLLA) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *GeoLLA) GetLng() float64 {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return 0
}

func (x *GeoLLA) GetAlt() float64 {
	if x != nil && x.Alt != nil {
		return *x.Alt
	}
	return 0
}

func (x *GeoLLA) GetTimestampMs() int64 {
	if x != nil && x.TimestampMs != nil {
		return *x.TimestampMs
	}
	return 0
}

type GeoECEF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X           *float64 `protobuf:"fixed64,1,opt,name=x,proto3,oneof" json:"x,omitempty"`
	Y           *float64 `protobuf:"fixed64,2,opt,name=y,proto3,oneof" json:"y,omitempty"`
	Z           *float64 `protobuf:"fixed64,3,opt,name=z,proto3,oneof" json:"z,omitempty"`
	TimestampMs *int64   `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3,oneof" json:"timestamp_ms,omitempty"`
}

func (x *GeoECEF) Reset() {
	*x = GeoECEF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeoECEF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeoECEF) ProtoMessage() {}

func (x *GeoECEF) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeoECEF.ProtoReflect.Descriptor instead.
func (*GeoECEF) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{55}
}

func (x *GeoECEF) GetX() float64 {
	if x != nil && x.X != nil {
		return *x.X
	}
	return 0
}

func (x *GeoECEF) GetY() float64 {
	if x != nil && x.Y != nil {
		return *x.Y
	}
	return 0
}

func (x *GeoECEF) GetZ() float64 {
	if x != nil && x.Z != nil {
		return *x.Z
	}
	return 0
}

func (x *GeoECEF) GetTimestampMs() int64 {
	if x != nil && x.TimestampMs != nil {
		return *x.TimestampMs
	}
	return 0
}

type SetGPSLocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lla  *GeoLLA  `protobuf:"bytes,1,opt,name=lla,proto3" json:"lla,omitempty"`
	Ecef *GeoECEF `protobuf:"bytes,2,opt,name=ecef,proto3" json:"ecef,omitempty"`
}

func (x *SetGPSLocationRequest) Reset() {
	*x = SetGPSLocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetGPSLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGPSLocationRequest) ProtoMessage() {}

func (x *SetGPSLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGPSLocationRequest.ProtoReflect.Descriptor instead.
func (*SetGPSLocationRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{56}
}

func (x *SetGPSLocationRequest) GetLla() *GeoLLA {
	if x != nil {
		return x.Lla
	}
	return nil
}

func (x *SetGPSLocationRequest) GetEcef() *GeoECEF {
	if x != nil {
		return x.Ecef
	}
	return nil
}

type SetGPSLocationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetGPSLocationResponse) Reset() {
	*x = SetGPSLocationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetGPSLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGPSLocationResponse) ProtoMessage() {}

func (x *SetGPSLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGPSLocationResponse.ProtoReflect.Descriptor instead.
func (*SetGPSLocationResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{57}
}

type SetImplementStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lifted   bool `protobuf:"varint,1,opt,name=lifted,proto3" json:"lifted,omitempty"`
	Estopped bool `protobuf:"varint,2,opt,name=estopped,proto3" json:"estopped,omitempty"`
}

func (x *SetImplementStatusRequest) Reset() {
	*x = SetImplementStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetImplementStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImplementStatusRequest) ProtoMessage() {}

func (x *SetImplementStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImplementStatusRequest.ProtoReflect.Descriptor instead.
func (*SetImplementStatusRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{58}
}

func (x *SetImplementStatusRequest) GetLifted() bool {
	if x != nil {
		return x.Lifted
	}
	return false
}

func (x *SetImplementStatusRequest) GetEstopped() bool {
	if x != nil {
		return x.Estopped
	}
	return false
}

type SetImplementStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetImplementStatusResponse) Reset() {
	*x = SetImplementStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetImplementStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImplementStatusResponse) ProtoMessage() {}

func (x *SetImplementStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImplementStatusResponse.ProtoReflect.Descriptor instead.
func (*SetImplementStatusResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{59}
}

type SetImageScoreRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score          float64         `protobuf:"fixed64,1,opt,name=score,proto3" json:"score,omitempty"`
	TimestampMs    int64           `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	CamId          string          `protobuf:"bytes,3,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	DeepweedOutput *DeepweedOutput `protobuf:"bytes,4,opt,name=deepweed_output,json=deepweedOutput,proto3" json:"deepweed_output,omitempty"`
}

func (x *SetImageScoreRequest) Reset() {
	*x = SetImageScoreRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetImageScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImageScoreRequest) ProtoMessage() {}

func (x *SetImageScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImageScoreRequest.ProtoReflect.Descriptor instead.
func (*SetImageScoreRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{60}
}

func (x *SetImageScoreRequest) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *SetImageScoreRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *SetImageScoreRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *SetImageScoreRequest) GetDeepweedOutput() *DeepweedOutput {
	if x != nil {
		return x.DeepweedOutput
	}
	return nil
}

type SetImageScoreResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetImageScoreResponse) Reset() {
	*x = SetImageScoreResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetImageScoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImageScoreResponse) ProtoMessage() {}

func (x *SetImageScoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImageScoreResponse.ProtoReflect.Descriptor instead.
func (*SetImageScoreResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{61}
}

type GetScoreQueueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreType string `protobuf:"bytes,1,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
}

func (x *GetScoreQueueRequest) Reset() {
	*x = GetScoreQueueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScoreQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScoreQueueRequest) ProtoMessage() {}

func (x *GetScoreQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScoreQueueRequest.ProtoReflect.Descriptor instead.
func (*GetScoreQueueRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{62}
}

func (x *GetScoreQueueRequest) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

type ScoreObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score       float64 `protobuf:"fixed64,1,opt,name=score,proto3" json:"score,omitempty"`
	TimestampMs int64   `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	CamId       string  `protobuf:"bytes,3,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *ScoreObject) Reset() {
	*x = ScoreObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScoreObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScoreObject) ProtoMessage() {}

func (x *ScoreObject) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScoreObject.ProtoReflect.Descriptor instead.
func (*ScoreObject) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{63}
}

func (x *ScoreObject) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ScoreObject) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *ScoreObject) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

type GetScoreQueueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreObject []*ScoreObject `protobuf:"bytes,1,rep,name=score_object,json=scoreObject,proto3" json:"score_object,omitempty"`
}

func (x *GetScoreQueueResponse) Reset() {
	*x = GetScoreQueueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScoreQueueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScoreQueueResponse) ProtoMessage() {}

func (x *GetScoreQueueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScoreQueueResponse.ProtoReflect.Descriptor instead.
func (*GetScoreQueueResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{64}
}

func (x *GetScoreQueueResponse) GetScoreObject() []*ScoreObject {
	if x != nil {
		return x.ScoreObject
	}
	return nil
}

type GetMaxImageScoreRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreType string `protobuf:"bytes,1,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
}

func (x *GetMaxImageScoreRequest) Reset() {
	*x = GetMaxImageScoreRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaxImageScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaxImageScoreRequest) ProtoMessage() {}

func (x *GetMaxImageScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaxImageScoreRequest.ProtoReflect.Descriptor instead.
func (*GetMaxImageScoreRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{65}
}

func (x *GetMaxImageScoreRequest) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

type GetMaxImageScoreResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score float64 `protobuf:"fixed64,1,opt,name=score,proto3" json:"score,omitempty"`
	Type  string  `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *GetMaxImageScoreResponse) Reset() {
	*x = GetMaxImageScoreResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaxImageScoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaxImageScoreResponse) ProtoMessage() {}

func (x *GetMaxImageScoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaxImageScoreResponse.ProtoReflect.Descriptor instead.
func (*GetMaxImageScoreResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{66}
}

func (x *GetMaxImageScoreResponse) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *GetMaxImageScoreResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type GetMaxScoredImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreType string `protobuf:"bytes,1,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
}

func (x *GetMaxScoredImageRequest) Reset() {
	*x = GetMaxScoredImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaxScoredImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaxScoredImageRequest) ProtoMessage() {}

func (x *GetMaxScoredImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaxScoredImageRequest.ProtoReflect.Descriptor instead.
func (*GetMaxScoredImageRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{67}
}

func (x *GetMaxScoredImageRequest) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

type GetLatestP2PImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in cv/runtime/proto/cv_runtime.proto.
	ScoreType string           `protobuf:"bytes,1,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	Reason    P2PCaptureReason `protobuf:"varint,2,opt,name=reason,proto3,enum=carbon.aimbot.cv.P2PCaptureReason" json:"reason,omitempty"`
}

func (x *GetLatestP2PImageRequest) Reset() {
	*x = GetLatestP2PImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestP2PImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestP2PImageRequest) ProtoMessage() {}

func (x *GetLatestP2PImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestP2PImageRequest.ProtoReflect.Descriptor instead.
func (*GetLatestP2PImageRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{68}
}

// Deprecated: Marked as deprecated in cv/runtime/proto/cv_runtime.proto.
func (x *GetLatestP2PImageRequest) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *GetLatestP2PImageRequest) GetReason() P2PCaptureReason {
	if x != nil {
		return x.Reason
	}
	return P2PCaptureReason_P2PCaptureReason_MISS
}

type GetLatestImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *GetLatestImageRequest) Reset() {
	*x = GetLatestImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestImageRequest) ProtoMessage() {}

func (x *GetLatestImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestImageRequest.ProtoReflect.Descriptor instead.
func (*GetLatestImageRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{69}
}

func (x *GetLatestImageRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

type GetImageNearTimestampRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId       string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetImageNearTimestampRequest) Reset() {
	*x = GetImageNearTimestampRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetImageNearTimestampRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetImageNearTimestampRequest) ProtoMessage() {}

func (x *GetImageNearTimestampRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetImageNearTimestampRequest.ProtoReflect.Descriptor instead.
func (*GetImageNearTimestampRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{70}
}

func (x *GetImageNearTimestampRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *GetImageNearTimestampRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GetChipImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreType string `protobuf:"bytes,1,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
}

func (x *GetChipImageRequest) Reset() {
	*x = GetChipImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChipImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChipImageRequest) ProtoMessage() {}

func (x *GetChipImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChipImageRequest.ProtoReflect.Descriptor instead.
func (*GetChipImageRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{71}
}

func (x *GetChipImageRequest) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

type FlushQueuesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreType      []string        `protobuf:"bytes,1,rep,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	ScoreQueueType *ScoreQueueType `protobuf:"varint,2,opt,name=score_queue_type,json=scoreQueueType,proto3,enum=cv.runtime.proto.ScoreQueueType,oneof" json:"score_queue_type,omitempty"`
}

func (x *FlushQueuesRequest) Reset() {
	*x = FlushQueuesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlushQueuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushQueuesRequest) ProtoMessage() {}

func (x *FlushQueuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushQueuesRequest.ProtoReflect.Descriptor instead.
func (*FlushQueuesRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{72}
}

func (x *FlushQueuesRequest) GetScoreType() []string {
	if x != nil {
		return x.ScoreType
	}
	return nil
}

func (x *FlushQueuesRequest) GetScoreQueueType() ScoreQueueType {
	if x != nil && x.ScoreQueueType != nil {
		return *x.ScoreQueueType
	}
	return ScoreQueueType_PREDICT
}

type FlushQueuesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FlushQueuesResponse) Reset() {
	*x = FlushQueuesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlushQueuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushQueuesResponse) ProtoMessage() {}

func (x *FlushQueuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushQueuesResponse.ProtoReflect.Descriptor instead.
func (*FlushQueuesResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{73}
}

type ImageAndMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bytes                 []byte                     `protobuf:"bytes,1,opt,name=bytes,proto3" json:"bytes,omitempty"`
	Width                 int32                      `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	Height                int32                      `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	TimestampMs           int64                      `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	Score                 float64                    `protobuf:"fixed64,5,opt,name=score,proto3" json:"score,omitempty"`
	CamId                 string                     `protobuf:"bytes,6,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	IsoFormattedTime      string                     `protobuf:"bytes,7,opt,name=iso_formatted_time,json=isoFormattedTime,proto3" json:"iso_formatted_time,omitempty"`
	LlaLat                float64                    `protobuf:"fixed64,8,opt,name=lla_lat,json=llaLat,proto3" json:"lla_lat,omitempty"`
	LlaLng                float64                    `protobuf:"fixed64,9,opt,name=lla_lng,json=llaLng,proto3" json:"lla_lng,omitempty"`
	LlaAlt                float64                    `protobuf:"fixed64,10,opt,name=lla_alt,json=llaAlt,proto3" json:"lla_alt,omitempty"`
	LlaTimestampMs        int64                      `protobuf:"varint,11,opt,name=lla_timestamp_ms,json=llaTimestampMs,proto3" json:"lla_timestamp_ms,omitempty"`
	EcefX                 float64                    `protobuf:"fixed64,12,opt,name=ecef_x,json=ecefX,proto3" json:"ecef_x,omitempty"`
	EcefY                 float64                    `protobuf:"fixed64,13,opt,name=ecef_y,json=ecefY,proto3" json:"ecef_y,omitempty"`
	EcefZ                 float64                    `protobuf:"fixed64,14,opt,name=ecef_z,json=ecefZ,proto3" json:"ecef_z,omitempty"`
	EcefTimestampMs       int64                      `protobuf:"varint,15,opt,name=ecef_timestamp_ms,json=ecefTimestampMs,proto3" json:"ecef_timestamp_ms,omitempty"`
	Ppi                   float32                    `protobuf:"fixed32,16,opt,name=ppi,proto3" json:"ppi,omitempty"`
	ScoreType             *string                    `protobuf:"bytes,17,opt,name=score_type,json=scoreType,proto3,oneof" json:"score_type,omitempty"`
	ImageType             string                     `protobuf:"bytes,18,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
	ModelUrl              string                     `protobuf:"bytes,19,opt,name=model_url,json=modelUrl,proto3" json:"model_url,omitempty"`
	Crop                  string                     `protobuf:"bytes,20,opt,name=crop,proto3" json:"crop,omitempty"`
	WeedHeightColumns     []float64                  `protobuf:"fixed64,21,rep,packed,name=weed_height_columns,json=weedHeightColumns,proto3" json:"weed_height_columns,omitempty"`
	CropHeightColumns     []float64                  `protobuf:"fixed64,22,rep,packed,name=crop_height_columns,json=cropHeightColumns,proto3" json:"crop_height_columns,omitempty"`
	BbhOffsetMm           float64                    `protobuf:"fixed64,23,opt,name=bbh_offset_mm,json=bbhOffsetMm,proto3" json:"bbh_offset_mm,omitempty"`
	FocusMetric           float64                    `protobuf:"fixed64,24,opt,name=focus_metric,json=focusMetric,proto3" json:"focus_metric,omitempty"`
	ExposureUs            float64                    `protobuf:"fixed64,25,opt,name=exposure_us,json=exposureUs,proto3" json:"exposure_us,omitempty"`
	CropPointThreshold    float64                    `protobuf:"fixed64,26,opt,name=crop_point_threshold,json=cropPointThreshold,proto3" json:"crop_point_threshold,omitempty"`
	WeedPointThreshold    float64                    `protobuf:"fixed64,27,opt,name=weed_point_threshold,json=weedPointThreshold,proto3" json:"weed_point_threshold,omitempty"`
	WeedingEnabled        bool                       `protobuf:"varint,28,opt,name=weeding_enabled,json=weedingEnabled,proto3" json:"weeding_enabled,omitempty"`
	ThinningEnabled       bool                       `protobuf:"varint,29,opt,name=thinning_enabled,json=thinningEnabled,proto3" json:"thinning_enabled,omitempty"`
	DeepweedId            string                     `protobuf:"bytes,30,opt,name=deepweed_id,json=deepweedId,proto3" json:"deepweed_id,omitempty"`
	P2PId                 string                     `protobuf:"bytes,31,opt,name=p2p_id,json=p2pId,proto3" json:"p2p_id,omitempty"`
	DeepweedDetections    []*weed_tracking.Detection `protobuf:"bytes,32,rep,name=deepweed_detections,json=deepweedDetections,proto3" json:"deepweed_detections,omitempty"`
	SegmentationThreshold float64                    `protobuf:"fixed64,33,opt,name=segmentation_threshold,json=segmentationThreshold,proto3" json:"segmentation_threshold,omitempty"`
	SimulatorGenerated    bool                       `protobuf:"varint,34,opt,name=simulator_generated,json=simulatorGenerated,proto3" json:"simulator_generated,omitempty"`
	GainDb                float64                    `protobuf:"fixed64,35,opt,name=gain_db,json=gainDb,proto3" json:"gain_db,omitempty"`
}

func (x *ImageAndMetadataResponse) Reset() {
	*x = ImageAndMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageAndMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageAndMetadataResponse) ProtoMessage() {}

func (x *ImageAndMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageAndMetadataResponse.ProtoReflect.Descriptor instead.
func (*ImageAndMetadataResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{74}
}

func (x *ImageAndMetadataResponse) GetBytes() []byte {
	if x != nil {
		return x.Bytes
	}
	return nil
}

func (x *ImageAndMetadataResponse) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *ImageAndMetadataResponse) GetIsoFormattedTime() string {
	if x != nil {
		return x.IsoFormattedTime
	}
	return ""
}

func (x *ImageAndMetadataResponse) GetLlaLat() float64 {
	if x != nil {
		return x.LlaLat
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetLlaLng() float64 {
	if x != nil {
		return x.LlaLng
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetLlaAlt() float64 {
	if x != nil {
		return x.LlaAlt
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetLlaTimestampMs() int64 {
	if x != nil {
		return x.LlaTimestampMs
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetEcefX() float64 {
	if x != nil {
		return x.EcefX
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetEcefY() float64 {
	if x != nil {
		return x.EcefY
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetEcefZ() float64 {
	if x != nil {
		return x.EcefZ
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetEcefTimestampMs() int64 {
	if x != nil {
		return x.EcefTimestampMs
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetPpi() float32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetScoreType() string {
	if x != nil && x.ScoreType != nil {
		return *x.ScoreType
	}
	return ""
}

func (x *ImageAndMetadataResponse) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *ImageAndMetadataResponse) GetModelUrl() string {
	if x != nil {
		return x.ModelUrl
	}
	return ""
}

func (x *ImageAndMetadataResponse) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *ImageAndMetadataResponse) GetWeedHeightColumns() []float64 {
	if x != nil {
		return x.WeedHeightColumns
	}
	return nil
}

func (x *ImageAndMetadataResponse) GetCropHeightColumns() []float64 {
	if x != nil {
		return x.CropHeightColumns
	}
	return nil
}

func (x *ImageAndMetadataResponse) GetBbhOffsetMm() float64 {
	if x != nil {
		return x.BbhOffsetMm
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetFocusMetric() float64 {
	if x != nil {
		return x.FocusMetric
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetExposureUs() float64 {
	if x != nil {
		return x.ExposureUs
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetCropPointThreshold() float64 {
	if x != nil {
		return x.CropPointThreshold
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetWeedPointThreshold() float64 {
	if x != nil {
		return x.WeedPointThreshold
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetWeedingEnabled() bool {
	if x != nil {
		return x.WeedingEnabled
	}
	return false
}

func (x *ImageAndMetadataResponse) GetThinningEnabled() bool {
	if x != nil {
		return x.ThinningEnabled
	}
	return false
}

func (x *ImageAndMetadataResponse) GetDeepweedId() string {
	if x != nil {
		return x.DeepweedId
	}
	return ""
}

func (x *ImageAndMetadataResponse) GetP2PId() string {
	if x != nil {
		return x.P2PId
	}
	return ""
}

func (x *ImageAndMetadataResponse) GetDeepweedDetections() []*weed_tracking.Detection {
	if x != nil {
		return x.DeepweedDetections
	}
	return nil
}

func (x *ImageAndMetadataResponse) GetSegmentationThreshold() float64 {
	if x != nil {
		return x.SegmentationThreshold
	}
	return 0
}

func (x *ImageAndMetadataResponse) GetSimulatorGenerated() bool {
	if x != nil {
		return x.SimulatorGenerated
	}
	return false
}

func (x *ImageAndMetadataResponse) GetGainDb() float64 {
	if x != nil {
		return x.GainDb
	}
	return 0
}

type CategoryPrediction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category string  `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Score    float64 `protobuf:"fixed64,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *CategoryPrediction) Reset() {
	*x = CategoryPrediction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryPrediction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryPrediction) ProtoMessage() {}

func (x *CategoryPrediction) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryPrediction.ProtoReflect.Descriptor instead.
func (*CategoryPrediction) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{75}
}

func (x *CategoryPrediction) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *CategoryPrediction) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type ChipPrediction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X                 float64               `protobuf:"fixed64,1,opt,name=x,proto3" json:"x,omitempty"`
	Y                 float64               `protobuf:"fixed64,2,opt,name=y,proto3" json:"y,omitempty"`
	Radius            float64               `protobuf:"fixed64,3,opt,name=radius,proto3" json:"radius,omitempty"`
	ModelId           string                `protobuf:"bytes,4,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	Score             []*CategoryPrediction `protobuf:"bytes,5,rep,name=score,proto3" json:"score,omitempty"`
	EmbeddingDistance []*CategoryPrediction `protobuf:"bytes,6,rep,name=embedding_distance,json=embeddingDistance,proto3" json:"embedding_distance,omitempty"`
	BandStatus        string                `protobuf:"bytes,7,opt,name=band_status,json=bandStatus,proto3" json:"band_status,omitempty"`
}

func (x *ChipPrediction) Reset() {
	*x = ChipPrediction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChipPrediction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChipPrediction) ProtoMessage() {}

func (x *ChipPrediction) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChipPrediction.ProtoReflect.Descriptor instead.
func (*ChipPrediction) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{76}
}

func (x *ChipPrediction) GetX() float64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *ChipPrediction) GetY() float64 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *ChipPrediction) GetRadius() float64 {
	if x != nil {
		return x.Radius
	}
	return 0
}

func (x *ChipPrediction) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ChipPrediction) GetScore() []*CategoryPrediction {
	if x != nil {
		return x.Score
	}
	return nil
}

func (x *ChipPrediction) GetEmbeddingDistance() []*CategoryPrediction {
	if x != nil {
		return x.EmbeddingDistance
	}
	return nil
}

func (x *ChipPrediction) GetBandStatus() string {
	if x != nil {
		return x.BandStatus
	}
	return ""
}

type ChipImageAndMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageAndMetadata   *ImageAndMetadataResponse `protobuf:"bytes,1,opt,name=image_and_metadata,json=imageAndMetadata,proto3" json:"image_and_metadata,omitempty"`
	PredictionMetadata *ChipPrediction           `protobuf:"bytes,2,opt,name=prediction_metadata,json=predictionMetadata,proto3" json:"prediction_metadata,omitempty"`
}

func (x *ChipImageAndMetadataResponse) Reset() {
	*x = ChipImageAndMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChipImageAndMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChipImageAndMetadataResponse) ProtoMessage() {}

func (x *ChipImageAndMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChipImageAndMetadataResponse.ProtoReflect.Descriptor instead.
func (*ChipImageAndMetadataResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{77}
}

func (x *ChipImageAndMetadataResponse) GetImageAndMetadata() *ImageAndMetadataResponse {
	if x != nil {
		return x.ImageAndMetadata
	}
	return nil
}

func (x *ChipImageAndMetadataResponse) GetPredictionMetadata() *ChipPrediction {
	if x != nil {
		return x.PredictionMetadata
	}
	return nil
}

type ChipQueueInformationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ChipQueueInformationRequest) Reset() {
	*x = ChipQueueInformationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChipQueueInformationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChipQueueInformationRequest) ProtoMessage() {}

func (x *ChipQueueInformationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChipQueueInformationRequest.ProtoReflect.Descriptor instead.
func (*ChipQueueInformationRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{78}
}

type ChipQueueInformationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueueScore []*CategoryPrediction `protobuf:"bytes,1,rep,name=queue_score,json=queueScore,proto3" json:"queue_score,omitempty"`
}

func (x *ChipQueueInformationResponse) Reset() {
	*x = ChipQueueInformationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChipQueueInformationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChipQueueInformationResponse) ProtoMessage() {}

func (x *ChipQueueInformationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChipQueueInformationResponse.ProtoReflect.Descriptor instead.
func (*ChipQueueInformationResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{79}
}

func (x *ChipQueueInformationResponse) GetQueueScore() []*CategoryPrediction {
	if x != nil {
		return x.QueueScore
	}
	return nil
}

type P2PImageAndMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetBytes           []byte  `protobuf:"bytes,1,opt,name=target_bytes,json=targetBytes,proto3" json:"target_bytes,omitempty"`
	TargetWidth           int32   `protobuf:"varint,2,opt,name=target_width,json=targetWidth,proto3" json:"target_width,omitempty"`
	TargetHeight          int32   `protobuf:"varint,3,opt,name=target_height,json=targetHeight,proto3" json:"target_height,omitempty"`
	PerspectiveBytes      []byte  `protobuf:"bytes,4,opt,name=perspective_bytes,json=perspectiveBytes,proto3" json:"perspective_bytes,omitempty"`
	PerspectiveWidth      int32   `protobuf:"varint,5,opt,name=perspective_width,json=perspectiveWidth,proto3" json:"perspective_width,omitempty"`
	PerspectiveHeight     int32   `protobuf:"varint,6,opt,name=perspective_height,json=perspectiveHeight,proto3" json:"perspective_height,omitempty"`
	AnnotatedTargetBytes  []byte  `protobuf:"bytes,7,opt,name=annotated_target_bytes,json=annotatedTargetBytes,proto3" json:"annotated_target_bytes,omitempty"`
	AnnotatedTargetWidth  int32   `protobuf:"varint,8,opt,name=annotated_target_width,json=annotatedTargetWidth,proto3" json:"annotated_target_width,omitempty"`
	AnnotatedTargetHeight int32   `protobuf:"varint,9,opt,name=annotated_target_height,json=annotatedTargetHeight,proto3" json:"annotated_target_height,omitempty"`
	TimestampMs           int64   `protobuf:"varint,10,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	Score                 float64 `protobuf:"fixed64,11,opt,name=score,proto3" json:"score,omitempty"`
	CamId                 string  `protobuf:"bytes,12,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	IsoFormattedTime      string  `protobuf:"bytes,13,opt,name=iso_formatted_time,json=isoFormattedTime,proto3" json:"iso_formatted_time,omitempty"`
	LlaLat                float64 `protobuf:"fixed64,14,opt,name=lla_lat,json=llaLat,proto3" json:"lla_lat,omitempty"`
	LlaLng                float64 `protobuf:"fixed64,15,opt,name=lla_lng,json=llaLng,proto3" json:"lla_lng,omitempty"`
	LlaAlt                float64 `protobuf:"fixed64,16,opt,name=lla_alt,json=llaAlt,proto3" json:"lla_alt,omitempty"`
	LlaTimestampMs        int64   `protobuf:"varint,17,opt,name=lla_timestamp_ms,json=llaTimestampMs,proto3" json:"lla_timestamp_ms,omitempty"`
	EcefX                 float64 `protobuf:"fixed64,18,opt,name=ecef_x,json=ecefX,proto3" json:"ecef_x,omitempty"`
	EcefY                 float64 `protobuf:"fixed64,19,opt,name=ecef_y,json=ecefY,proto3" json:"ecef_y,omitempty"`
	EcefZ                 float64 `protobuf:"fixed64,20,opt,name=ecef_z,json=ecefZ,proto3" json:"ecef_z,omitempty"`
	EcefTimestampMs       int64   `protobuf:"varint,21,opt,name=ecef_timestamp_ms,json=ecefTimestampMs,proto3" json:"ecef_timestamp_ms,omitempty"`
	Ppi                   float32 `protobuf:"fixed32,22,opt,name=ppi,proto3" json:"ppi,omitempty"`
	PerspectivePpi        float32 `protobuf:"fixed32,23,opt,name=perspective_ppi,json=perspectivePpi,proto3" json:"perspective_ppi,omitempty"`
	ImageType             string  `protobuf:"bytes,25,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
	ModelUrl              string  `protobuf:"bytes,26,opt,name=model_url,json=modelUrl,proto3" json:"model_url,omitempty"`
	Crop                  string  `protobuf:"bytes,27,opt,name=crop,proto3" json:"crop,omitempty"`
	FocusMetric           float64 `protobuf:"fixed64,28,opt,name=focus_metric,json=focusMetric,proto3" json:"focus_metric,omitempty"`
	ExposureUs            float64 `protobuf:"fixed64,29,opt,name=exposure_us,json=exposureUs,proto3" json:"exposure_us,omitempty"`
	WeedingEnabled        bool    `protobuf:"varint,30,opt,name=weeding_enabled,json=weedingEnabled,proto3" json:"weeding_enabled,omitempty"`
	ThinningEnabled       bool    `protobuf:"varint,31,opt,name=thinning_enabled,json=thinningEnabled,proto3" json:"thinning_enabled,omitempty"`
	DeepweedId            string  `protobuf:"bytes,32,opt,name=deepweed_id,json=deepweedId,proto3" json:"deepweed_id,omitempty"`
	P2PId                 string  `protobuf:"bytes,33,opt,name=p2p_id,json=p2pId,proto3" json:"p2p_id,omitempty"`
}

func (x *P2PImageAndMetadataResponse) Reset() {
	*x = P2PImageAndMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PImageAndMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PImageAndMetadataResponse) ProtoMessage() {}

func (x *P2PImageAndMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PImageAndMetadataResponse.ProtoReflect.Descriptor instead.
func (*P2PImageAndMetadataResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{80}
}

func (x *P2PImageAndMetadataResponse) GetTargetBytes() []byte {
	if x != nil {
		return x.TargetBytes
	}
	return nil
}

func (x *P2PImageAndMetadataResponse) GetTargetWidth() int32 {
	if x != nil {
		return x.TargetWidth
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetTargetHeight() int32 {
	if x != nil {
		return x.TargetHeight
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetPerspectiveBytes() []byte {
	if x != nil {
		return x.PerspectiveBytes
	}
	return nil
}

func (x *P2PImageAndMetadataResponse) GetPerspectiveWidth() int32 {
	if x != nil {
		return x.PerspectiveWidth
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetPerspectiveHeight() int32 {
	if x != nil {
		return x.PerspectiveHeight
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetAnnotatedTargetBytes() []byte {
	if x != nil {
		return x.AnnotatedTargetBytes
	}
	return nil
}

func (x *P2PImageAndMetadataResponse) GetAnnotatedTargetWidth() int32 {
	if x != nil {
		return x.AnnotatedTargetWidth
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetAnnotatedTargetHeight() int32 {
	if x != nil {
		return x.AnnotatedTargetHeight
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *P2PImageAndMetadataResponse) GetIsoFormattedTime() string {
	if x != nil {
		return x.IsoFormattedTime
	}
	return ""
}

func (x *P2PImageAndMetadataResponse) GetLlaLat() float64 {
	if x != nil {
		return x.LlaLat
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetLlaLng() float64 {
	if x != nil {
		return x.LlaLng
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetLlaAlt() float64 {
	if x != nil {
		return x.LlaAlt
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetLlaTimestampMs() int64 {
	if x != nil {
		return x.LlaTimestampMs
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetEcefX() float64 {
	if x != nil {
		return x.EcefX
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetEcefY() float64 {
	if x != nil {
		return x.EcefY
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetEcefZ() float64 {
	if x != nil {
		return x.EcefZ
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetEcefTimestampMs() int64 {
	if x != nil {
		return x.EcefTimestampMs
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetPpi() float32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetPerspectivePpi() float32 {
	if x != nil {
		return x.PerspectivePpi
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *P2PImageAndMetadataResponse) GetModelUrl() string {
	if x != nil {
		return x.ModelUrl
	}
	return ""
}

func (x *P2PImageAndMetadataResponse) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *P2PImageAndMetadataResponse) GetFocusMetric() float64 {
	if x != nil {
		return x.FocusMetric
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetExposureUs() float64 {
	if x != nil {
		return x.ExposureUs
	}
	return 0
}

func (x *P2PImageAndMetadataResponse) GetWeedingEnabled() bool {
	if x != nil {
		return x.WeedingEnabled
	}
	return false
}

func (x *P2PImageAndMetadataResponse) GetThinningEnabled() bool {
	if x != nil {
		return x.ThinningEnabled
	}
	return false
}

func (x *P2PImageAndMetadataResponse) GetDeepweedId() string {
	if x != nil {
		return x.DeepweedId
	}
	return ""
}

func (x *P2PImageAndMetadataResponse) GetP2PId() string {
	if x != nil {
		return x.P2PId
	}
	return ""
}

type GetCameraInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCameraInfoRequest) Reset() {
	*x = GetCameraInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraInfoRequest) ProtoMessage() {}

func (x *GetCameraInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraInfoRequest.ProtoReflect.Descriptor instead.
func (*GetCameraInfoRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{81}
}

type CameraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId                 string    `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	IpAddress             *string   `protobuf:"bytes,2,opt,name=ip_address,json=ipAddress,proto3,oneof" json:"ip_address,omitempty"`
	SerialNumber          *string   `protobuf:"bytes,3,opt,name=serial_number,json=serialNumber,proto3,oneof" json:"serial_number,omitempty"`
	Model                 string    `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`
	Width                 uint32    `protobuf:"varint,5,opt,name=width,proto3" json:"width,omitempty"`
	Height                uint32    `protobuf:"varint,6,opt,name=height,proto3" json:"height,omitempty"`
	Connected             bool      `protobuf:"varint,7,opt,name=connected,proto3" json:"connected,omitempty"`
	LinkSpeed             uint64    `protobuf:"varint,8,opt,name=link_speed,json=linkSpeed,proto3" json:"link_speed,omitempty"`
	ErrorType             ErrorType `protobuf:"varint,9,opt,name=error_type,json=errorType,proto3,enum=cv.runtime.proto.ErrorType" json:"error_type,omitempty"`
	V4L2DeviceId          *string   `protobuf:"bytes,10,opt,name=v4l2_device_id,json=v4l2DeviceId,proto3,oneof" json:"v4l2_device_id,omitempty"`
	FirmwareVersion       string    `protobuf:"bytes,11,opt,name=firmware_version,json=firmwareVersion,proto3" json:"firmware_version,omitempty"`
	LatestFirmwareVersion *string   `protobuf:"bytes,12,opt,name=latest_firmware_version,json=latestFirmwareVersion,proto3,oneof" json:"latest_firmware_version,omitempty"`
}

func (x *CameraInfo) Reset() {
	*x = CameraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraInfo) ProtoMessage() {}

func (x *CameraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraInfo.ProtoReflect.Descriptor instead.
func (*CameraInfo) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{82}
}

func (x *CameraInfo) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *CameraInfo) GetIpAddress() string {
	if x != nil && x.IpAddress != nil {
		return *x.IpAddress
	}
	return ""
}

func (x *CameraInfo) GetSerialNumber() string {
	if x != nil && x.SerialNumber != nil {
		return *x.SerialNumber
	}
	return ""
}

func (x *CameraInfo) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *CameraInfo) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *CameraInfo) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *CameraInfo) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

func (x *CameraInfo) GetLinkSpeed() uint64 {
	if x != nil {
		return x.LinkSpeed
	}
	return 0
}

func (x *CameraInfo) GetErrorType() ErrorType {
	if x != nil {
		return x.ErrorType
	}
	return ErrorType_NONE
}

func (x *CameraInfo) GetV4L2DeviceId() string {
	if x != nil && x.V4L2DeviceId != nil {
		return *x.V4L2DeviceId
	}
	return ""
}

func (x *CameraInfo) GetFirmwareVersion() string {
	if x != nil {
		return x.FirmwareVersion
	}
	return ""
}

func (x *CameraInfo) GetLatestFirmwareVersion() string {
	if x != nil && x.LatestFirmwareVersion != nil {
		return *x.LatestFirmwareVersion
	}
	return ""
}

type GetCameraInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CameraInfo []*CameraInfo `protobuf:"bytes,1,rep,name=camera_info,json=cameraInfo,proto3" json:"camera_info,omitempty"`
}

func (x *GetCameraInfoResponse) Reset() {
	*x = GetCameraInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraInfoResponse) ProtoMessage() {}

func (x *GetCameraInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraInfoResponse.ProtoReflect.Descriptor instead.
func (*GetCameraInfoResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{83}
}

func (x *GetCameraInfoResponse) GetCameraInfo() []*CameraInfo {
	if x != nil {
		return x.CameraInfo
	}
	return nil
}

type GetLightweightBurstRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLightweightBurstRecordRequest) Reset() {
	*x = GetLightweightBurstRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLightweightBurstRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLightweightBurstRecordRequest) ProtoMessage() {}

func (x *GetLightweightBurstRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLightweightBurstRecordRequest.ProtoReflect.Descriptor instead.
func (*GetLightweightBurstRecordRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{84}
}

type GetLightweightBurstRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZipFile      []byte `protobuf:"bytes,1,opt,name=zip_file,json=zipFile,proto3" json:"zip_file,omitempty"`
	MetadataFile []byte `protobuf:"bytes,2,opt,name=metadata_file,json=metadataFile,proto3" json:"metadata_file,omitempty"`
}

func (x *GetLightweightBurstRecordResponse) Reset() {
	*x = GetLightweightBurstRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLightweightBurstRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLightweightBurstRecordResponse) ProtoMessage() {}

func (x *GetLightweightBurstRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLightweightBurstRecordResponse.ProtoReflect.Descriptor instead.
func (*GetLightweightBurstRecordResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{85}
}

func (x *GetLightweightBurstRecordResponse) GetZipFile() []byte {
	if x != nil {
		return x.ZipFile
	}
	return nil
}

func (x *GetLightweightBurstRecordResponse) GetMetadataFile() []byte {
	if x != nil {
		return x.MetadataFile
	}
	return nil
}

type GetBootedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetBootedRequest) Reset() {
	*x = GetBootedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBootedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBootedRequest) ProtoMessage() {}

func (x *GetBootedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBootedRequest.ProtoReflect.Descriptor instead.
func (*GetBootedRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{86}
}

type GetBootedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Booted bool `protobuf:"varint,1,opt,name=booted,proto3" json:"booted,omitempty"`
}

func (x *GetBootedResponse) Reset() {
	*x = GetBootedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBootedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBootedResponse) ProtoMessage() {}

func (x *GetBootedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBootedResponse.ProtoReflect.Descriptor instead.
func (*GetBootedResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{87}
}

func (x *GetBootedResponse) GetBooted() bool {
	if x != nil {
		return x.Booted
	}
	return false
}

type GetReadyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetReadyRequest) Reset() {
	*x = GetReadyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReadyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReadyRequest) ProtoMessage() {}

func (x *GetReadyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReadyRequest.ProtoReflect.Descriptor instead.
func (*GetReadyRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{88}
}

type GetReadyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ready              bool            `protobuf:"varint,1,opt,name=ready,proto3" json:"ready,omitempty"`
	DeepweedReadyState map[string]bool `protobuf:"bytes,2,rep,name=deepweed_ready_state,json=deepweedReadyState,proto3" json:"deepweed_ready_state,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // camera_id -> ready
	P2PReadyState      map[string]bool `protobuf:"bytes,3,rep,name=p2p_ready_state,json=p2pReadyState,proto3" json:"p2p_ready_state,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                // camera_id -> ready
	Booted             bool            `protobuf:"varint,4,opt,name=booted,proto3" json:"booted,omitempty"`
}

func (x *GetReadyResponse) Reset() {
	*x = GetReadyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReadyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReadyResponse) ProtoMessage() {}

func (x *GetReadyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReadyResponse.ProtoReflect.Descriptor instead.
func (*GetReadyResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{89}
}

func (x *GetReadyResponse) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *GetReadyResponse) GetDeepweedReadyState() map[string]bool {
	if x != nil {
		return x.DeepweedReadyState
	}
	return nil
}

func (x *GetReadyResponse) GetP2PReadyState() map[string]bool {
	if x != nil {
		return x.P2PReadyState
	}
	return nil
}

func (x *GetReadyResponse) GetBooted() bool {
	if x != nil {
		return x.Booted
	}
	return false
}

type GetErrorStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlantProfileError          bool `protobuf:"varint,1,opt,name=plant_profile_error,json=plantProfileError,proto3" json:"plant_profile_error,omitempty"`
	ModelUnsupportedEmbeddings bool `protobuf:"varint,2,opt,name=model_unsupported_embeddings,json=modelUnsupportedEmbeddings,proto3" json:"model_unsupported_embeddings,omitempty"`
}

func (x *GetErrorStateResponse) Reset() {
	*x = GetErrorStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetErrorStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetErrorStateResponse) ProtoMessage() {}

func (x *GetErrorStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetErrorStateResponse.ProtoReflect.Descriptor instead.
func (*GetErrorStateResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{90}
}

func (x *GetErrorStateResponse) GetPlantProfileError() bool {
	if x != nil {
		return x.PlantProfileError
	}
	return false
}

func (x *GetErrorStateResponse) GetModelUnsupportedEmbeddings() bool {
	if x != nil {
		return x.ModelUnsupportedEmbeddings
	}
	return false
}

type DeepweedDetection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X                          float32   `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y                          float32   `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	Size                       float32   `protobuf:"fixed32,3,opt,name=size,proto3" json:"size,omitempty"`
	Score                      float32   `protobuf:"fixed32,4,opt,name=score,proto3" json:"score,omitempty"`
	HitClass                   HitClass  `protobuf:"varint,6,opt,name=hit_class,json=hitClass,proto3,enum=cv.runtime.proto.HitClass" json:"hit_class,omitempty"`
	MaskIntersections          []uint32  `protobuf:"varint,7,rep,packed,name=mask_intersections,json=maskIntersections,proto3" json:"mask_intersections,omitempty"`
	WeedScore                  float32   `protobuf:"fixed32,8,opt,name=weed_score,json=weedScore,proto3" json:"weed_score,omitempty"`
	CropScore                  float32   `protobuf:"fixed32,9,opt,name=crop_score,json=cropScore,proto3" json:"crop_score,omitempty"`
	WeedDetectionClassScores   []float32 `protobuf:"fixed32,10,rep,packed,name=weed_detection_class_scores,json=weedDetectionClassScores,proto3" json:"weed_detection_class_scores,omitempty"`
	Embedding                  []float32 `protobuf:"fixed32,11,rep,packed,name=embedding,proto3" json:"embedding,omitempty"`
	PlantScore                 float32   `protobuf:"fixed32,12,opt,name=plant_score,json=plantScore,proto3" json:"plant_score,omitempty"`
	EmbeddingCategoryDistances []float32 `protobuf:"fixed32,13,rep,packed,name=embedding_category_distances,json=embeddingCategoryDistances,proto3" json:"embedding_category_distances,omitempty"`
	DetectionId                uint32    `protobuf:"varint,14,opt,name=detection_id,json=detectionId,proto3" json:"detection_id,omitempty"`
}

func (x *DeepweedDetection) Reset() {
	*x = DeepweedDetection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedDetection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedDetection) ProtoMessage() {}

func (x *DeepweedDetection) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedDetection.ProtoReflect.Descriptor instead.
func (*DeepweedDetection) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{91}
}

func (x *DeepweedDetection) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *DeepweedDetection) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *DeepweedDetection) GetSize() float32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DeepweedDetection) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DeepweedDetection) GetHitClass() HitClass {
	if x != nil {
		return x.HitClass
	}
	return HitClass_WEED
}

func (x *DeepweedDetection) GetMaskIntersections() []uint32 {
	if x != nil {
		return x.MaskIntersections
	}
	return nil
}

func (x *DeepweedDetection) GetWeedScore() float32 {
	if x != nil {
		return x.WeedScore
	}
	return 0
}

func (x *DeepweedDetection) GetCropScore() float32 {
	if x != nil {
		return x.CropScore
	}
	return 0
}

func (x *DeepweedDetection) GetWeedDetectionClassScores() []float32 {
	if x != nil {
		return x.WeedDetectionClassScores
	}
	return nil
}

func (x *DeepweedDetection) GetEmbedding() []float32 {
	if x != nil {
		return x.Embedding
	}
	return nil
}

func (x *DeepweedDetection) GetPlantScore() float32 {
	if x != nil {
		return x.PlantScore
	}
	return 0
}

func (x *DeepweedDetection) GetEmbeddingCategoryDistances() []float32 {
	if x != nil {
		return x.EmbeddingCategoryDistances
	}
	return nil
}

func (x *DeepweedDetection) GetDetectionId() uint32 {
	if x != nil {
		return x.DetectionId
	}
	return 0
}

type DeepweedOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Detections               []*DeepweedDetection `protobuf:"bytes,1,rep,name=detections,proto3" json:"detections,omitempty"`
	MaskWidth                uint32               `protobuf:"varint,2,opt,name=mask_width,json=maskWidth,proto3" json:"mask_width,omitempty"`
	MaskHeight               uint32               `protobuf:"varint,3,opt,name=mask_height,json=maskHeight,proto3" json:"mask_height,omitempty"`
	MaskChannels             uint32               `protobuf:"varint,4,opt,name=mask_channels,json=maskChannels,proto3" json:"mask_channels,omitempty"`
	Mask                     []byte               `protobuf:"bytes,5,opt,name=mask,proto3" json:"mask,omitempty"`
	MaskChannelClasses       []string             `protobuf:"bytes,6,rep,name=mask_channel_classes,json=maskChannelClasses,proto3" json:"mask_channel_classes,omitempty"`
	PredictInDistanceBuffer  bool                 `protobuf:"varint,7,opt,name=predict_in_distance_buffer,json=predictInDistanceBuffer,proto3" json:"predict_in_distance_buffer,omitempty"`
	TimestampMs              int64                `protobuf:"varint,8,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	WeedDetectionClasses     []string             `protobuf:"bytes,9,rep,name=weed_detection_classes,json=weedDetectionClasses,proto3" json:"weed_detection_classes,omitempty"`
	EmbeddingCategories      []string             `protobuf:"bytes,10,rep,name=embedding_categories,json=embeddingCategories,proto3" json:"embedding_categories,omitempty"`
	AvailableForSnapshotting bool                 `protobuf:"varint,11,opt,name=available_for_snapshotting,json=availableForSnapshotting,proto3" json:"available_for_snapshotting,omitempty"`
}

func (x *DeepweedOutput) Reset() {
	*x = DeepweedOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedOutput) ProtoMessage() {}

func (x *DeepweedOutput) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedOutput.ProtoReflect.Descriptor instead.
func (*DeepweedOutput) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{92}
}

func (x *DeepweedOutput) GetDetections() []*DeepweedDetection {
	if x != nil {
		return x.Detections
	}
	return nil
}

func (x *DeepweedOutput) GetMaskWidth() uint32 {
	if x != nil {
		return x.MaskWidth
	}
	return 0
}

func (x *DeepweedOutput) GetMaskHeight() uint32 {
	if x != nil {
		return x.MaskHeight
	}
	return 0
}

func (x *DeepweedOutput) GetMaskChannels() uint32 {
	if x != nil {
		return x.MaskChannels
	}
	return 0
}

func (x *DeepweedOutput) GetMask() []byte {
	if x != nil {
		return x.Mask
	}
	return nil
}

func (x *DeepweedOutput) GetMaskChannelClasses() []string {
	if x != nil {
		return x.MaskChannelClasses
	}
	return nil
}

func (x *DeepweedOutput) GetPredictInDistanceBuffer() bool {
	if x != nil {
		return x.PredictInDistanceBuffer
	}
	return false
}

func (x *DeepweedOutput) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *DeepweedOutput) GetWeedDetectionClasses() []string {
	if x != nil {
		return x.WeedDetectionClasses
	}
	return nil
}

func (x *DeepweedOutput) GetEmbeddingCategories() []string {
	if x != nil {
		return x.EmbeddingCategories
	}
	return nil
}

func (x *DeepweedOutput) GetAvailableForSnapshotting() bool {
	if x != nil {
		return x.AvailableForSnapshotting
	}
	return false
}

type GetDeepweedOutputByTimestampRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId       string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetDeepweedOutputByTimestampRequest) Reset() {
	*x = GetDeepweedOutputByTimestampRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedOutputByTimestampRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedOutputByTimestampRequest) ProtoMessage() {}

func (x *GetDeepweedOutputByTimestampRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedOutputByTimestampRequest.ProtoReflect.Descriptor instead.
func (*GetDeepweedOutputByTimestampRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{93}
}

func (x *GetDeepweedOutputByTimestampRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *GetDeepweedOutputByTimestampRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GetRecommendedStrobeSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRecommendedStrobeSettingsRequest) Reset() {
	*x = GetRecommendedStrobeSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommendedStrobeSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommendedStrobeSettingsRequest) ProtoMessage() {}

func (x *GetRecommendedStrobeSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommendedStrobeSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetRecommendedStrobeSettingsRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{94}
}

type GetRecommendedStrobeSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetCameraFps        float32 `protobuf:"fixed32,1,opt,name=target_camera_fps,json=targetCameraFps,proto3" json:"target_camera_fps,omitempty"`
	TargetsPerPredictRatio int32   `protobuf:"varint,2,opt,name=targets_per_predict_ratio,json=targetsPerPredictRatio,proto3" json:"targets_per_predict_ratio,omitempty"`
}

func (x *GetRecommendedStrobeSettingsResponse) Reset() {
	*x = GetRecommendedStrobeSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommendedStrobeSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommendedStrobeSettingsResponse) ProtoMessage() {}

func (x *GetRecommendedStrobeSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommendedStrobeSettingsResponse.ProtoReflect.Descriptor instead.
func (*GetRecommendedStrobeSettingsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{95}
}

func (x *GetRecommendedStrobeSettingsResponse) GetTargetCameraFps() float32 {
	if x != nil {
		return x.TargetCameraFps
	}
	return 0
}

func (x *GetRecommendedStrobeSettingsResponse) GetTargetsPerPredictRatio() int32 {
	if x != nil {
		return x.TargetsPerPredictRatio
	}
	return 0
}

// Deprecated: Marked as deprecated in cv/runtime/proto/cv_runtime.proto.
type StartP2PBufferringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *StartP2PBufferringRequest) Reset() {
	*x = StartP2PBufferringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartP2PBufferringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartP2PBufferringRequest) ProtoMessage() {}

func (x *StartP2PBufferringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartP2PBufferringRequest.ProtoReflect.Descriptor instead.
func (*StartP2PBufferringRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{96}
}

func (x *StartP2PBufferringRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

// Deprecated: Marked as deprecated in cv/runtime/proto/cv_runtime.proto.
type StartP2PBufferringResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartP2PBufferringResponse) Reset() {
	*x = StartP2PBufferringResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartP2PBufferringResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartP2PBufferringResponse) ProtoMessage() {}

func (x *StartP2PBufferringResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartP2PBufferringResponse.ProtoReflect.Descriptor instead.
func (*StartP2PBufferringResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{97}
}

// Deprecated: Marked as deprecated in cv/runtime/proto/cv_runtime.proto.
type StopP2PBufferringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SaveBurst               bool   `protobuf:"varint,1,opt,name=save_burst,json=saveBurst,proto3" json:"save_burst,omitempty"`
	CamId                   string `protobuf:"bytes,2,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Path                    string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	DontCapturePredictImage bool   `protobuf:"varint,4,opt,name=dont_capture_predict_image,json=dontCapturePredictImage,proto3" json:"dont_capture_predict_image,omitempty"`
	StartTimestampMs        int64  `protobuf:"varint,5,opt,name=start_timestamp_ms,json=startTimestampMs,proto3" json:"start_timestamp_ms,omitempty"`
	EndTimestampMs          int64  `protobuf:"varint,6,opt,name=end_timestamp_ms,json=endTimestampMs,proto3" json:"end_timestamp_ms,omitempty"`
}

func (x *StopP2PBufferringRequest) Reset() {
	*x = StopP2PBufferringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopP2PBufferringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopP2PBufferringRequest) ProtoMessage() {}

func (x *StopP2PBufferringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopP2PBufferringRequest.ProtoReflect.Descriptor instead.
func (*StopP2PBufferringRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{98}
}

func (x *StopP2PBufferringRequest) GetSaveBurst() bool {
	if x != nil {
		return x.SaveBurst
	}
	return false
}

func (x *StopP2PBufferringRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *StopP2PBufferringRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *StopP2PBufferringRequest) GetDontCapturePredictImage() bool {
	if x != nil {
		return x.DontCapturePredictImage
	}
	return false
}

func (x *StopP2PBufferringRequest) GetStartTimestampMs() int64 {
	if x != nil {
		return x.StartTimestampMs
	}
	return 0
}

func (x *StopP2PBufferringRequest) GetEndTimestampMs() int64 {
	if x != nil {
		return x.EndTimestampMs
	}
	return 0
}

// Deprecated: Marked as deprecated in cv/runtime/proto/cv_runtime.proto.
type StopP2PBufferringResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopP2PBufferringResponse) Reset() {
	*x = StopP2PBufferringResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopP2PBufferringResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopP2PBufferringResponse) ProtoMessage() {}

func (x *StopP2PBufferringResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopP2PBufferringResponse.ProtoReflect.Descriptor instead.
func (*StopP2PBufferringResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{99}
}

type P2PCaptureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId       string           `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Name        string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TimestampMs int64            `protobuf:"varint,3,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	WriteToDisk bool             `protobuf:"varint,4,opt,name=write_to_disk,json=writeToDisk,proto3" json:"write_to_disk,omitempty"`
	Reason      P2PCaptureReason `protobuf:"varint,5,opt,name=reason,proto3,enum=carbon.aimbot.cv.P2PCaptureReason" json:"reason,omitempty"`
}

func (x *P2PCaptureRequest) Reset() {
	*x = P2PCaptureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PCaptureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PCaptureRequest) ProtoMessage() {}

func (x *P2PCaptureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PCaptureRequest.ProtoReflect.Descriptor instead.
func (*P2PCaptureRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{100}
}

func (x *P2PCaptureRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *P2PCaptureRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *P2PCaptureRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *P2PCaptureRequest) GetWriteToDisk() bool {
	if x != nil {
		return x.WriteToDisk
	}
	return false
}

func (x *P2PCaptureRequest) GetReason() P2PCaptureReason {
	if x != nil {
		return x.Reason
	}
	return P2PCaptureReason_P2PCaptureReason_MISS
}

type P2PCaptureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *P2PCaptureResponse) Reset() {
	*x = P2PCaptureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PCaptureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PCaptureResponse) ProtoMessage() {}

func (x *P2PCaptureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PCaptureResponse.ProtoReflect.Descriptor instead.
func (*P2PCaptureResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{101}
}

type P2PBufferingBurstPredictMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlantSizePx float32 `protobuf:"fixed32,1,opt,name=plant_size_px,json=plantSizePx,proto3" json:"plant_size_px,omitempty"`
}

func (x *P2PBufferingBurstPredictMetadata) Reset() {
	*x = P2PBufferingBurstPredictMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PBufferingBurstPredictMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PBufferingBurstPredictMetadata) ProtoMessage() {}

func (x *P2PBufferingBurstPredictMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PBufferingBurstPredictMetadata.ProtoReflect.Descriptor instead.
func (*P2PBufferingBurstPredictMetadata) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{102}
}

func (x *P2PBufferingBurstPredictMetadata) GetPlantSizePx() float32 {
	if x != nil {
		return x.PlantSizePx
	}
	return 0
}

type P2PBufferringBurstCaptureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId                   string                            `protobuf:"bytes,2,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Path                    string                            `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	DontCapturePredictImage bool                              `protobuf:"varint,4,opt,name=dont_capture_predict_image,json=dontCapturePredictImage,proto3" json:"dont_capture_predict_image,omitempty"`
	StartTimestampMs        int64                             `protobuf:"varint,5,opt,name=start_timestamp_ms,json=startTimestampMs,proto3" json:"start_timestamp_ms,omitempty"`
	EndTimestampMs          int64                             `protobuf:"varint,6,opt,name=end_timestamp_ms,json=endTimestampMs,proto3" json:"end_timestamp_ms,omitempty"`
	PredictPath             *string                           `protobuf:"bytes,7,opt,name=predict_path,json=predictPath,proto3,oneof" json:"predict_path,omitempty"`
	PredictPathExists       bool                              `protobuf:"varint,8,opt,name=predict_path_exists,json=predictPathExists,proto3" json:"predict_path_exists,omitempty"`
	SavePredictMetadata     bool                              `protobuf:"varint,9,opt,name=save_predict_metadata,json=savePredictMetadata,proto3" json:"save_predict_metadata,omitempty"`
	PredictMetadata         *P2PBufferingBurstPredictMetadata `protobuf:"bytes,10,opt,name=predict_metadata,json=predictMetadata,proto3" json:"predict_metadata,omitempty"`
}

func (x *P2PBufferringBurstCaptureRequest) Reset() {
	*x = P2PBufferringBurstCaptureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PBufferringBurstCaptureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PBufferringBurstCaptureRequest) ProtoMessage() {}

func (x *P2PBufferringBurstCaptureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PBufferringBurstCaptureRequest.ProtoReflect.Descriptor instead.
func (*P2PBufferringBurstCaptureRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{103}
}

func (x *P2PBufferringBurstCaptureRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *P2PBufferringBurstCaptureRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *P2PBufferringBurstCaptureRequest) GetDontCapturePredictImage() bool {
	if x != nil {
		return x.DontCapturePredictImage
	}
	return false
}

func (x *P2PBufferringBurstCaptureRequest) GetStartTimestampMs() int64 {
	if x != nil {
		return x.StartTimestampMs
	}
	return 0
}

func (x *P2PBufferringBurstCaptureRequest) GetEndTimestampMs() int64 {
	if x != nil {
		return x.EndTimestampMs
	}
	return 0
}

func (x *P2PBufferringBurstCaptureRequest) GetPredictPath() string {
	if x != nil && x.PredictPath != nil {
		return *x.PredictPath
	}
	return ""
}

func (x *P2PBufferringBurstCaptureRequest) GetPredictPathExists() bool {
	if x != nil {
		return x.PredictPathExists
	}
	return false
}

func (x *P2PBufferringBurstCaptureRequest) GetSavePredictMetadata() bool {
	if x != nil {
		return x.SavePredictMetadata
	}
	return false
}

func (x *P2PBufferringBurstCaptureRequest) GetPredictMetadata() *P2PBufferingBurstPredictMetadata {
	if x != nil {
		return x.PredictMetadata
	}
	return nil
}

type P2PBufferringBurstCaptureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *P2PBufferringBurstCaptureResponse) Reset() {
	*x = P2PBufferringBurstCaptureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PBufferringBurstCaptureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PBufferringBurstCaptureResponse) ProtoMessage() {}

func (x *P2PBufferringBurstCaptureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PBufferringBurstCaptureResponse.ProtoReflect.Descriptor instead.
func (*P2PBufferringBurstCaptureResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{104}
}

type GetNextDeepweedOutputRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs int64  `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	TimeoutMs   int64  `protobuf:"varint,2,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
	CamId       string `protobuf:"bytes,3,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *GetNextDeepweedOutputRequest) Reset() {
	*x = GetNextDeepweedOutputRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextDeepweedOutputRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextDeepweedOutputRequest) ProtoMessage() {}

func (x *GetNextDeepweedOutputRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextDeepweedOutputRequest.ProtoReflect.Descriptor instead.
func (*GetNextDeepweedOutputRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{105}
}

func (x *GetNextDeepweedOutputRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetNextDeepweedOutputRequest) GetTimeoutMs() int64 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

func (x *GetNextDeepweedOutputRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

type SetTargetingStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedingEnabled  bool `protobuf:"varint,1,opt,name=weeding_enabled,json=weedingEnabled,proto3" json:"weeding_enabled,omitempty"`
	ThinningEnabled bool `protobuf:"varint,2,opt,name=thinning_enabled,json=thinningEnabled,proto3" json:"thinning_enabled,omitempty"`
}

func (x *SetTargetingStateRequest) Reset() {
	*x = SetTargetingStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTargetingStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTargetingStateRequest) ProtoMessage() {}

func (x *SetTargetingStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTargetingStateRequest.ProtoReflect.Descriptor instead.
func (*SetTargetingStateRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{106}
}

func (x *SetTargetingStateRequest) GetWeedingEnabled() bool {
	if x != nil {
		return x.WeedingEnabled
	}
	return false
}

func (x *SetTargetingStateRequest) GetThinningEnabled() bool {
	if x != nil {
		return x.ThinningEnabled
	}
	return false
}

type SetTargetingStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetTargetingStateResponse) Reset() {
	*x = SetTargetingStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTargetingStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTargetingStateResponse) ProtoMessage() {}

func (x *SetTargetingStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTargetingStateResponse.ProtoReflect.Descriptor instead.
func (*SetTargetingStateResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{107}
}

type GetNextFocusMetricRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId       string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetNextFocusMetricRequest) Reset() {
	*x = GetNextFocusMetricRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextFocusMetricRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextFocusMetricRequest) ProtoMessage() {}

func (x *GetNextFocusMetricRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextFocusMetricRequest.ProtoReflect.Descriptor instead.
func (*GetNextFocusMetricRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{108}
}

func (x *GetNextFocusMetricRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *GetNextFocusMetricRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GetNextFocusMetricResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FocusMetric float32 `protobuf:"fixed32,1,opt,name=focus_metric,json=focusMetric,proto3" json:"focus_metric,omitempty"`
	TimestampMs int64   `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetNextFocusMetricResponse) Reset() {
	*x = GetNextFocusMetricResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextFocusMetricResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextFocusMetricResponse) ProtoMessage() {}

func (x *GetNextFocusMetricResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextFocusMetricResponse.ProtoReflect.Descriptor instead.
func (*GetNextFocusMetricResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{109}
}

func (x *GetNextFocusMetricResponse) GetFocusMetric() float32 {
	if x != nil {
		return x.FocusMetric
	}
	return 0
}

func (x *GetNextFocusMetricResponse) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type RemoveDataDirRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *RemoveDataDirRequest) Reset() {
	*x = RemoveDataDirRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveDataDirRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveDataDirRequest) ProtoMessage() {}

func (x *RemoveDataDirRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveDataDirRequest.ProtoReflect.Descriptor instead.
func (*RemoveDataDirRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{110}
}

func (x *RemoveDataDirRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type RemoveDataDirResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveDataDirResponse) Reset() {
	*x = RemoveDataDirResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveDataDirResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveDataDirResponse) ProtoMessage() {}

func (x *RemoveDataDirResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveDataDirResponse.ProtoReflect.Descriptor instead.
func (*RemoveDataDirResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{111}
}

type LastNImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId     string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	NumImages uint64 `protobuf:"varint,2,opt,name=num_images,json=numImages,proto3" json:"num_images,omitempty"`
}

func (x *LastNImageRequest) Reset() {
	*x = LastNImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[112]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LastNImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LastNImageRequest) ProtoMessage() {}

func (x *LastNImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[112]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LastNImageRequest.ProtoReflect.Descriptor instead.
func (*LastNImageRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{112}
}

func (x *LastNImageRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *LastNImageRequest) GetNumImages() uint64 {
	if x != nil {
		return x.NumImages
	}
	return 0
}

type ComputeCapabilitiesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Capabilities []string `protobuf:"bytes,1,rep,name=capabilities,proto3" json:"capabilities,omitempty"`
}

func (x *ComputeCapabilitiesResponse) Reset() {
	*x = ComputeCapabilitiesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[113]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComputeCapabilitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComputeCapabilitiesResponse) ProtoMessage() {}

func (x *ComputeCapabilitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[113]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComputeCapabilitiesResponse.ProtoReflect.Descriptor instead.
func (*ComputeCapabilitiesResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{113}
}

func (x *ComputeCapabilitiesResponse) GetCapabilities() []string {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

type SupportedTensorRTVersionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Versions []string `protobuf:"bytes,1,rep,name=versions,proto3" json:"versions,omitempty"`
}

func (x *SupportedTensorRTVersionsResponse) Reset() {
	*x = SupportedTensorRTVersionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[114]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SupportedTensorRTVersionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportedTensorRTVersionsResponse) ProtoMessage() {}

func (x *SupportedTensorRTVersionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[114]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportedTensorRTVersionsResponse.ProtoReflect.Descriptor instead.
func (*SupportedTensorRTVersionsResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{114}
}

func (x *SupportedTensorRTVersionsResponse) GetVersions() []string {
	if x != nil {
		return x.Versions
	}
	return nil
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[115]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[115]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{115}
}

type ScoreQueueAndCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreQueue string `protobuf:"bytes,1,opt,name=score_queue,json=scoreQueue,proto3" json:"score_queue,omitempty"`
	NumItems   uint64 `protobuf:"varint,2,opt,name=num_items,json=numItems,proto3" json:"num_items,omitempty"`
}

func (x *ScoreQueueAndCount) Reset() {
	*x = ScoreQueueAndCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[116]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScoreQueueAndCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScoreQueueAndCount) ProtoMessage() {}

func (x *ScoreQueueAndCount) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[116]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScoreQueueAndCount.ProtoReflect.Descriptor instead.
func (*ScoreQueueAndCount) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{116}
}

func (x *ScoreQueueAndCount) GetScoreQueue() string {
	if x != nil {
		return x.ScoreQueue
	}
	return ""
}

func (x *ScoreQueueAndCount) GetNumItems() uint64 {
	if x != nil {
		return x.NumItems
	}
	return 0
}

type ListScoreQueuesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreQueue []*ScoreQueueAndCount `protobuf:"bytes,1,rep,name=score_queue,json=scoreQueue,proto3" json:"score_queue,omitempty"`
}

func (x *ListScoreQueuesResponse) Reset() {
	*x = ListScoreQueuesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[117]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListScoreQueuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListScoreQueuesResponse) ProtoMessage() {}

func (x *ListScoreQueuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[117]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListScoreQueuesResponse.ProtoReflect.Descriptor instead.
func (*ListScoreQueuesResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{117}
}

func (x *ListScoreQueuesResponse) GetScoreQueue() []*ScoreQueueAndCount {
	if x != nil {
		return x.ScoreQueue
	}
	return nil
}

type GetCategoryCollectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryCollectionId   string `protobuf:"bytes,1,opt,name=category_collection_id,json=categoryCollectionId,proto3" json:"category_collection_id,omitempty"`
	LastUpdatedTimestampMs int64  `protobuf:"varint,2,opt,name=last_updated_timestamp_ms,json=lastUpdatedTimestampMs,proto3" json:"last_updated_timestamp_ms,omitempty"`
}

func (x *GetCategoryCollectionResponse) Reset() {
	*x = GetCategoryCollectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[118]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryCollectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryCollectionResponse) ProtoMessage() {}

func (x *GetCategoryCollectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[118]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryCollectionResponse.ProtoReflect.Descriptor instead.
func (*GetCategoryCollectionResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{118}
}

func (x *GetCategoryCollectionResponse) GetCategoryCollectionId() string {
	if x != nil {
		return x.CategoryCollectionId
	}
	return ""
}

func (x *GetCategoryCollectionResponse) GetLastUpdatedTimestampMs() int64 {
	if x != nil {
		return x.LastUpdatedTimestampMs
	}
	return 0
}

type SnapshotPredictImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SnapshotPredictImagesRequest) Reset() {
	*x = SnapshotPredictImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[119]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotPredictImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotPredictImagesRequest) ProtoMessage() {}

func (x *SnapshotPredictImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[119]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotPredictImagesRequest.ProtoReflect.Descriptor instead.
func (*SnapshotPredictImagesRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{119}
}

type PcamSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PcamId      string `protobuf:"bytes,1,opt,name=pcam_id,json=pcamId,proto3" json:"pcam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *PcamSnapshot) Reset() {
	*x = PcamSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[120]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PcamSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PcamSnapshot) ProtoMessage() {}

func (x *PcamSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[120]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PcamSnapshot.ProtoReflect.Descriptor instead.
func (*PcamSnapshot) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{120}
}

func (x *PcamSnapshot) GetPcamId() string {
	if x != nil {
		return x.PcamId
	}
	return ""
}

func (x *PcamSnapshot) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type SnapshotPredictImagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Snapshots []*PcamSnapshot `protobuf:"bytes,1,rep,name=snapshots,proto3" json:"snapshots,omitempty"`
}

func (x *SnapshotPredictImagesResponse) Reset() {
	*x = SnapshotPredictImagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[121]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotPredictImagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotPredictImagesResponse) ProtoMessage() {}

func (x *SnapshotPredictImagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[121]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotPredictImagesResponse.ProtoReflect.Descriptor instead.
func (*SnapshotPredictImagesResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{121}
}

func (x *SnapshotPredictImagesResponse) GetSnapshots() []*PcamSnapshot {
	if x != nil {
		return x.Snapshots
	}
	return nil
}

type GetChipForPredictImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PcamId      string `protobuf:"bytes,1,opt,name=pcam_id,json=pcamId,proto3" json:"pcam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	CenterXPx   int32  `protobuf:"varint,3,opt,name=center_x_px,json=centerXPx,proto3" json:"center_x_px,omitempty"`
	CenterYPx   int32  `protobuf:"varint,4,opt,name=center_y_px,json=centerYPx,proto3" json:"center_y_px,omitempty"`
}

func (x *GetChipForPredictImageRequest) Reset() {
	*x = GetChipForPredictImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[122]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChipForPredictImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChipForPredictImageRequest) ProtoMessage() {}

func (x *GetChipForPredictImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[122]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChipForPredictImageRequest.ProtoReflect.Descriptor instead.
func (*GetChipForPredictImageRequest) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{122}
}

func (x *GetChipForPredictImageRequest) GetPcamId() string {
	if x != nil {
		return x.PcamId
	}
	return ""
}

func (x *GetChipForPredictImageRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetChipForPredictImageRequest) GetCenterXPx() int32 {
	if x != nil {
		return x.CenterXPx
	}
	return 0
}

func (x *GetChipForPredictImageRequest) GetCenterYPx() int32 {
	if x != nil {
		return x.CenterYPx
	}
	return 0
}

type GetChipForPredictImageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageAndMetadata *ImageAndMetadataResponse `protobuf:"bytes,1,opt,name=image_and_metadata,json=imageAndMetadata,proto3" json:"image_and_metadata,omitempty"`
}

func (x *GetChipForPredictImageResponse) Reset() {
	*x = GetChipForPredictImageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[123]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChipForPredictImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChipForPredictImageResponse) ProtoMessage() {}

func (x *GetChipForPredictImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cv_runtime_proto_cv_runtime_proto_msgTypes[123]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChipForPredictImageResponse.ProtoReflect.Descriptor instead.
func (*GetChipForPredictImageResponse) Descriptor() ([]byte, []int) {
	return file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP(), []int{123}
}

func (x *GetChipForPredictImageResponse) GetImageAndMetadata() *ImageAndMetadataResponse {
	if x != nil {
		return x.ImageAndMetadata
	}
	return nil
}

var File_cv_runtime_proto_cv_runtime_proto protoreflect.FileDescriptor

var file_cv_runtime_proto_cv_runtime_proto_rawDesc = []byte{
	0x0a, 0x21, 0x63, 0x76, 0x2f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x63, 0x76, 0x5f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x10, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x6c, 0x69, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x63, 0x76, 0x2f, 0x63, 0x76, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x60, 0x0a, 0x10, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x02, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x6f, 0x77, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x64, 0x6f, 0x77, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x6c,
	0x65, 0x66, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x22, 0xb4, 0x01, 0x0a, 0x0a, 0x50, 0x32,
	0x50, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x14, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6f, 0x72,
	0x64, 0x5f, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x58, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x5f, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x59,
	0x22, 0xc9, 0x02, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x50, 0x32, 0x50, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x45, 0x0a,
	0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x32, 0x50, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x4e, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x32, 0x50, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52,
	0x10, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x0b, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5f, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x0a, 0x73,
	0x61, 0x66, 0x65, 0x74, 0x79, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x17, 0x0a, 0x15,
	0x53, 0x65, 0x74, 0x50, 0x32, 0x50, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x69, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x70, 0x6f, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x70, 0x6f, 0x73, 0x65, 0x22, 0x98, 0x02, 0x0a, 0x1a, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50,
	0x32, 0x50, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x4d, 0x69, 0x73, 0x73,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x12, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x52, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10,
	0x61, 0x66, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73,
	0x22, 0x52, 0x0a, 0x16, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x22, 0x83, 0x01, 0x0a, 0x1d, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x28, 0x0a, 0x10, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x5f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x73, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x49, 0x6e, 0x22, 0xc5, 0x02, 0x0a, 0x20, 0x44,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x53, 0x0a, 0x10, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x0f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x12, 0x77, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x72, 0x6f, 0x70, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x68, 0x0a, 0x17, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x16, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x22, 0xc8, 0x02, 0x0a, 0x23, 0x53, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65,
	0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x77, 0x65, 0x65, 0x64, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x30, 0x0a, 0x14,
	0x63, 0x72, 0x6f, 0x70, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x72, 0x6f, 0x70,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x53,
	0x0a, 0x10, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x0f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x68, 0x0a, 0x17, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x16, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0x26, 0x0a,
	0x24, 0x53, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70,
	0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xc9, 0x02, 0x0a,
	0x24, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x12, 0x77, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x72, 0x6f, 0x70, 0x5f,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x72, 0x6f, 0x70, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x53, 0x0a, 0x10, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x68,
	0x0a, 0x17, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x16, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0x4e, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x17, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0x3a, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06,
	0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61,
	0x6d, 0x49, 0x64, 0x22, 0xd5, 0x05, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77,
	0x65, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x1c, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x74, 0x6f, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x52, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x18, 0x77, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x92, 0x01, 0x0a, 0x1c, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x5f, 0x74, 0x6f, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65,
	0x70, 0x77, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x6f,
	0x70, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x18, 0x63, 0x72, 0x6f, 0x70,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x9a, 0x01, 0x0a, 0x1e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x74, 0x6f, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x55, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x1b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x1a, 0x4b, 0x0a, 0x1d, 0x57, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4b,
	0x0a, 0x1d, 0x43, 0x72, 0x6f, 0x70, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4e, 0x0a, 0x20, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x42, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x4d, 0x61, 0x74, 0x72,
	0x69, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x49, 0x64, 0x22,
	0x52, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d,
	0x44, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x65, 0x66, 0x66, 0x69,
	0x63, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61,
	0x6d, 0x49, 0x64, 0x22, 0xab, 0x06, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00,
	0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x19, 0x0a, 0x05, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01,
	0x52, 0x05, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x67, 0x61,
	0x69, 0x6e, 0x5f, 0x64, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02, 0x52, 0x06, 0x67,
	0x61, 0x69, 0x6e, 0x44, 0x62, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x13, 0x6c, 0x69, 0x67, 0x68,
	0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6c, 0x69, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x48, 0x03, 0x52, 0x11, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0c, 0x77, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f,
	0x72, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x48, 0x04, 0x52, 0x0a, 0x77, 0x62, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e, 0x77, 0x62,
	0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x02, 0x48, 0x05, 0x52, 0x0c, 0x77, 0x62, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x47, 0x72, 0x65,
	0x65, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x77, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x5f, 0x62, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x48, 0x06, 0x52, 0x0b,
	0x77, 0x62, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x42, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25,
	0x0a, 0x0c, 0x72, 0x6f, 0x69, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x78, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x07, 0x52, 0x0a, 0x72, 0x6f, 0x69, 0x4f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x58, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0c, 0x72, 0x6f, 0x69, 0x5f, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x5f, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x08, 0x52, 0x0a, 0x72,
	0x6f, 0x69, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x59, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06,
	0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x48, 0x09, 0x52, 0x06,
	0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x66, 0x6c, 0x69,
	0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x48, 0x0a, 0x52, 0x04, 0x66, 0x6c, 0x69, 0x70, 0x88,
	0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x69, 0x6e, 0x67, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x0b, 0x52, 0x08, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x69, 0x6e, 0x67,
	0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x70, 0x74, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x0c, 0x52, 0x03, 0x70, 0x74, 0x70, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x08, 0x48, 0x0d, 0x52, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x42, 0x08, 0x0a, 0x06,
	0x5f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x67, 0x61, 0x69, 0x6e, 0x5f,
	0x64, 0x62, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x77,
	0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x72, 0x65, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x77, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x77, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x62, 0x6c, 0x75, 0x65,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x72, 0x6f, 0x69, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f,
	0x78, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x72, 0x6f, 0x69, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x5f, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x66, 0x6c, 0x69, 0x70, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x72, 0x6f, 0x62,
	0x69, 0x6e, 0x67, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x70, 0x74, 0x70, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x34, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x22, 0x4d, 0x0a, 0x1a, 0x53, 0x65, 0x74, 0x41, 0x75,
	0x74, 0x6f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x22, 0x1d, 0x0a, 0x1b, 0x53, 0x65, 0x74, 0x41, 0x75, 0x74,
	0x6f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x22, 0xf4, 0x06, 0x0a, 0x16, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x02, 0x48, 0x01, 0x52, 0x05, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a,
	0x07, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02,
	0x52, 0x06, 0x67, 0x61, 0x69, 0x6e, 0x44, 0x62, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x13, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6c, 0x69, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x4c, 0x69, 0x67,
	0x68, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x48, 0x03,
	0x52, 0x11, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x74, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0c, 0x77, 0x62, 0x5f, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x5f, 0x72, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x48, 0x04, 0x52, 0x0a,
	0x77, 0x62, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a,
	0x0e, 0x77, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x02, 0x48, 0x05, 0x52, 0x0c, 0x77, 0x62, 0x52, 0x61, 0x74, 0x69, 0x6f,
	0x47, 0x72, 0x65, 0x65, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x77, 0x62, 0x5f, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x5f, 0x62, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x48,
	0x06, 0x52, 0x0b, 0x77, 0x62, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x42, 0x6c, 0x75, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x20, 0x0a, 0x09, 0x72, 0x6f, 0x69, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x07, 0x52, 0x08, 0x72, 0x6f, 0x69, 0x57, 0x69, 0x64, 0x74, 0x68,
	0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x72, 0x6f, 0x69, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x08, 0x52, 0x09, 0x72, 0x6f, 0x69, 0x48, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0c, 0x72, 0x6f, 0x69, 0x5f, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x48, 0x09, 0x52,
	0x0a, 0x72, 0x6f, 0x69, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x58, 0x88, 0x01, 0x01, 0x12, 0x25,
	0x0a, 0x0c, 0x72, 0x6f, 0x69, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x79, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x0a, 0x52, 0x0a, 0x72, 0x6f, 0x69, 0x4f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x59, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x67, 0x70, 0x75, 0x5f, 0x69, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x48, 0x0b, 0x52, 0x05, 0x67, 0x70, 0x75, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6c, 0x69,
	0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x66, 0x6c, 0x69, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x69, 0x6e, 0x67, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x69, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x74, 0x70,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x70, 0x74, 0x70, 0x12, 0x30, 0x0a, 0x11, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x48, 0x0c, 0x52, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x57, 0x68,
	0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x67, 0x61, 0x69, 0x6e,
	0x5f, 0x64, 0x62, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x77, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x72, 0x65, 0x64, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x77, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x77, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x62, 0x6c, 0x75,
	0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x6f, 0x69, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x72, 0x6f, 0x69, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x72, 0x6f, 0x69, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x78, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x72, 0x6f, 0x69, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x79,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x7f, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62,
	0x0a, 0x18, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x16, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0xd5, 0x01, 0x0a, 0x1d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x75, 0x72, 0x73,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x12, 0x3b, 0x0a, 0x1a, 0x64, 0x6f, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x64, 0x6f, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a,
	0x11, 0x64, 0x6f, 0x77, 0x6e, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x64, 0x6f, 0x77, 0x6e, 0x73, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x20, 0x0a, 0x1e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46, 0x72,
	0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8d, 0x01, 0x0a,
	0x1c, 0x53, 0x74, 0x6f, 0x70, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x88, 0x01, 0x01,
	0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x22, 0x1f, 0x0a, 0x1d,
	0x53, 0x74, 0x6f, 0x70, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46,
	0x72, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xdd, 0x02,
	0x0a, 0x0e, 0x50, 0x32, 0x50, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x5f, 0x78, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x58,
	0x12, 0x24, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64,
	0x5f, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x43, 0x6f, 0x6f, 0x72, 0x64, 0x59, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x61, 0x66, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x61, 0x66, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x5f, 0x78, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x6f,
	0x6f, 0x72, 0x64, 0x58, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f,
	0x63, 0x6f, 0x6f, 0x72, 0x64, 0x5f, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x59, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x22, 0x72, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x32, 0x50, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x4d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d,
	0x73, 0x22, 0x54, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6d,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x49,
	0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x64, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x72, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22,
	0x6b, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xc9, 0x01, 0x0a,
	0x14, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x72, 0x65, 0x64, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74,
	0x69, 0x6f, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x72, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x17, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xd6, 0x02, 0x0a, 0x0a, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x70, 0x73, 0x5f, 0x6d, 0x65, 0x61, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x66, 0x70, 0x73, 0x4d, 0x65, 0x61, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x70, 0x73, 0x5f, 0x39, 0x39, 0x70, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x08, 0x66, 0x70, 0x73, 0x39, 0x39, 0x70, 0x63, 0x74, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6d, 0x73, 0x5f, 0x6d, 0x65, 0x61, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4d, 0x73,
	0x4d, 0x65, 0x61, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f,
	0x6d, 0x73, 0x5f, 0x39, 0x39, 0x70, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e,
	0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4d, 0x73, 0x39, 0x39, 0x70, 0x63, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x53, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x3f, 0x0a, 0x11, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x12, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x52,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x69,
	0x6e, 0x67, 0x22, 0x6b, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x73, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x73, 0x4d, 0x73, 0x22,
	0x11, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x70, 0x0a, 0x13, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6e, 0x64, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x73, 0x5f, 0x6d, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x73, 0x4d, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6e, 0x64, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x45, 0x0a, 0x0f,
	0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x0a, 0x11, 0x55, 0x6e, 0x73, 0x65, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06,
	0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61,
	0x6d, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x55, 0x6e, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x8f, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61,
	0x74, 0x68, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x03, 0x70,
	0x32, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x70, 0x32, 0x70, 0x88,
	0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x66, 0x75, 0x72, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x07, 0x66, 0x75, 0x72, 0x72, 0x6f, 0x77, 0x73, 0x88,
	0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x70, 0x32, 0x70, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66, 0x75, 0x72, 0x72,
	0x6f, 0x77, 0x73, 0x22, 0x1e, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x4c, 0x0a, 0x11, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x54, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x22, 0x66, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x54, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x0b, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x06, 0x47, 0x65,
	0x6f, 0x4c, 0x4c, 0x41, 0x12, 0x15, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x00, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x6c,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x88,
	0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x61, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48,
	0x02, 0x52, 0x03, 0x61, 0x6c, 0x74, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x88, 0x01,
	0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x61, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x6e,
	0x67, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x61, 0x6c, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x07, 0x47,
	0x65, 0x6f, 0x45, 0x43, 0x45, 0x46, 0x12, 0x11, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x00, 0x52, 0x01, 0x78, 0x88, 0x01, 0x01, 0x12, 0x11, 0x0a, 0x01, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52, 0x01, 0x79, 0x88, 0x01, 0x01, 0x12, 0x11, 0x0a, 0x01,
	0x7a, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x01, 0x7a, 0x88, 0x01, 0x01, 0x12,
	0x26, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x4d, 0x73, 0x88, 0x01, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x5f, 0x78, 0x42, 0x04, 0x0a,
	0x02, 0x5f, 0x79, 0x42, 0x04, 0x0a, 0x02, 0x5f, 0x7a, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x22, 0x72, 0x0a, 0x15, 0x53, 0x65,
	0x74, 0x47, 0x50, 0x53, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x03, 0x6c, 0x6c, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x6f, 0x4c, 0x4c, 0x41, 0x52, 0x03, 0x6c, 0x6c, 0x61, 0x12,
	0x2d, 0x0a, 0x04, 0x65, 0x63, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x6f, 0x45, 0x43, 0x45, 0x46, 0x52, 0x04, 0x65, 0x63, 0x65, 0x66, 0x22, 0x18,
	0x0a, 0x16, 0x53, 0x65, 0x74, 0x47, 0x50, 0x53, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4f, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x49,
	0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x22, 0x1c, 0x0a, 0x1a, 0x53, 0x65, 0x74,
	0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x49, 0x0a, 0x0f, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x6f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0e, 0x64, 0x65, 0x65,
	0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x22, 0x17, 0x0a, 0x15, 0x53,
	0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x35, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5d, 0x0a, 0x0b, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x4d, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0b, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x22, 0x38, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x44, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x39, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x79, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x50, 0x32, 0x50,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0a,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3a, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x63, 0x76, 0x2e, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x2e, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x65, 0x61, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x34, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x12,
	0x46, 0x6c, 0x75, 0x73, 0x68, 0x51, 0x75, 0x65, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x4f, 0x0a, 0x10, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52,
	0x0e, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x46, 0x6c, 0x75, 0x73, 0x68,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xdb,
	0x09, 0x0a, 0x18, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x4d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x69, 0x73, 0x6f, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x73, 0x6f,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x6c, 0x6c, 0x61, 0x5f, 0x6c, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06,
	0x6c, 0x6c, 0x61, 0x4c, 0x61, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6c, 0x61, 0x5f, 0x6c, 0x6e,
	0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6c, 0x61, 0x4c, 0x6e, 0x67, 0x12,
	0x17, 0x0a, 0x07, 0x6c, 0x6c, 0x61, 0x5f, 0x61, 0x6c, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x06, 0x6c, 0x6c, 0x61, 0x41, 0x6c, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6c, 0x61, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x6c, 0x6c, 0x61, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x4d, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f, 0x78, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x05, 0x65, 0x63, 0x65, 0x66, 0x58, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x63, 0x65,
	0x66, 0x5f, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x65, 0x63, 0x65, 0x66, 0x59,
	0x12, 0x15, 0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f, 0x7a, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x05, 0x65, 0x63, 0x65, 0x66, 0x5a, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x63, 0x65, 0x66, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x65, 0x63, 0x65, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x4d, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x70, 0x69, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73,
	0x18, 0x15, 0x20, 0x03, 0x28, 0x01, 0x52, 0x11, 0x77, 0x65, 0x65, 0x64, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73,
	0x18, 0x16, 0x20, 0x03, 0x28, 0x01, 0x52, 0x11, 0x63, 0x72, 0x6f, 0x70, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x62, 0x68,
	0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x62, 0x62, 0x68, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x4d, 0x6d, 0x12, 0x21, 0x0a,
	0x0c, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55,
	0x73, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x12, 0x63, 0x72, 0x6f, 0x70, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x12, 0x77, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x29,
	0x0a, 0x10, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x65,
	0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x32,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x32, 0x70, 0x49,
	0x64, 0x12, 0x49, 0x0a, 0x13, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x64, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x20, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65,
	0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x16,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x12, 0x2f, 0x0a, 0x13, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x22, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x12, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x62, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x67, 0x61, 0x69, 0x6e, 0x44, 0x62, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x46, 0x0a, 0x12,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x22, 0x91, 0x02, 0x0a, 0x0e, 0x43, 0x68, 0x69, 0x70, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x01, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x53, 0x0a, 0x12, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x61, 0x6e, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x61,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xcb, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x69,
	0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x12, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e,
	0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x10, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x51, 0x0a, 0x13, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x69, 0x70, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x12, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x1d, 0x0a, 0x1b, 0x43, 0x68, 0x69, 0x70, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x65, 0x0a, 0x1c, 0x43, 0x68, 0x69, 0x70, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x76, 0x2e,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x71, 0x75, 0x65, 0x75, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xf4, 0x08, 0x0a,
	0x1b, 0x50, 0x32, 0x50, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x57, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x10, 0x70, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x70, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x57, 0x69, 0x64, 0x74,
	0x68, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x70,
	0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x34, 0x0a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x14, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x36, 0x0a, 0x17,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x48, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x5f, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x6f, 0x5f, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x69, 0x73, 0x6f, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6c, 0x61, 0x5f, 0x6c, 0x61, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6c, 0x61, 0x4c, 0x61, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6c,
	0x6c, 0x61, 0x5f, 0x6c, 0x6e, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6c,
	0x61, 0x4c, 0x6e, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6c, 0x61, 0x5f, 0x61, 0x6c, 0x74, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x6c, 0x6c, 0x61, 0x41, 0x6c, 0x74, 0x12, 0x28, 0x0a,
	0x10, 0x6c, 0x6c, 0x61, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6c, 0x6c, 0x61, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f,
	0x78, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x65, 0x63, 0x65, 0x66, 0x58, 0x12, 0x15,
	0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x65, 0x63, 0x65, 0x66, 0x59, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f, 0x7a, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x65, 0x63, 0x65, 0x66, 0x5a, 0x12, 0x2a, 0x0a, 0x11,
	0x65, 0x63, 0x65, 0x66, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d,
	0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x65, 0x63, 0x65, 0x66, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x70, 0x69, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65,
	0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x70, 0x69, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x50, 0x70, 0x69, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x72, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x72, 0x6f, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x66, 0x6f, 0x63, 0x75, 0x73,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x5f, 0x75, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x29, 0x0a, 0x10, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x74, 0x68, 0x69, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x32, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x32,
	0x70, 0x49, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x91, 0x04, 0x0a, 0x0a,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0c,
	0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x12, 0x3a, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x0e,
	0x76, 0x34, 0x6c, 0x32, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0c, 0x76, 0x34, 0x6c, 0x32, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x6d, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x17, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x72,
	0x6d, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x15, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x46, 0x69, 0x72,
	0x6d, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x76, 0x34, 0x6c, 0x32, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66,
	0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x56, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x22, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4c, 0x69,
	0x67, 0x68, 0x74, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x63, 0x0a, 0x21, 0x47,
	0x65, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x75, 0x72,
	0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x7a, 0x69, 0x70, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65,
	0x22, 0x12, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x2b, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x74, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x6f, 0x6f,
	0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x74, 0x65,
	0x64, 0x22, 0x11, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x96, 0x03, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61,
	0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x12,
	0x6c, 0x0a, 0x14, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x64,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x64, 0x65, 0x65, 0x70, 0x77,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x5d, 0x0a,
	0x0f, 0x70, 0x32, 0x70, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61,
	0x64, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x32, 0x70, 0x52, 0x65,
	0x61, 0x64, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x70,
	0x32, 0x70, 0x52, 0x65, 0x61, 0x64, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x62, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x62, 0x6f,
	0x6f, 0x74, 0x65, 0x64, 0x1a, 0x45, 0x0a, 0x17, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64,
	0x52, 0x65, 0x61, 0x64, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x50,
	0x32, 0x70, 0x52, 0x65, 0x61, 0x64, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x89, 0x01,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x40, 0x0a, 0x1c, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x75, 0x6e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x55, 0x6e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x45,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x22, 0xe2, 0x03, 0x0a, 0x11, 0x44, 0x65,
	0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a,
	0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x5f, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x74, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x52, 0x08, 0x68, 0x69, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x2d,
	0x0a, 0x12, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x11, 0x6d, 0x61, 0x73, 0x6b,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x77, 0x65, 0x65, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x09, 0x63, 0x72, 0x6f, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x02,
	0x52, 0x18, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6d,
	0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x02, 0x52, 0x09, 0x65,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x6e,
	0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70,
	0x6c, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x40, 0x0a, 0x1c, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x02, 0x52,
	0x1a, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x87,
	0x04, 0x0a, 0x0e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x12, 0x43, 0x0a, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65,
	0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x61, 0x73, 0x6b,
	0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6d, 0x61, 0x73, 0x6b,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6d,
	0x61, 0x73, 0x6b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x61, 0x73, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x12,
	0x30, 0x0a, 0x14, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x6d,
	0x61, 0x73, 0x6b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65,
	0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x5f,
	0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6e,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d,
	0x73, 0x12, 0x34, 0x0a, 0x16, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x14, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x1a, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x73, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x5f, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x42, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x25, 0x0a, 0x23, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x53, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x8d, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x66, 0x70, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x46, 0x70, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73,
	0x5f, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x73, 0x50, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f,
	0x22, 0x36, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66,
	0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x61, 0x6d, 0x49, 0x64, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x20, 0x0a, 0x1a, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x3a, 0x02, 0x18, 0x01, 0x22, 0xfd, 0x01, 0x0a, 0x18, 0x53,
	0x74, 0x6f, 0x70, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x61, 0x76, 0x65, 0x5f,
	0x62, 0x75, 0x72, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x61, 0x76,
	0x65, 0x42, 0x75, 0x72, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x12, 0x3b, 0x0a, 0x1a, 0x64, 0x6f, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x64, 0x6f, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x28, 0x0a, 0x10,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x1f, 0x0a, 0x19, 0x53, 0x74,
	0x6f, 0x70, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x3a, 0x02, 0x18, 0x01, 0x22, 0xc1, 0x01, 0x0a, 0x11,
	0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12,
	0x22, 0x0a, 0x0d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x69, 0x73, 0x6b,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x77, 0x72, 0x69, 0x74, 0x65, 0x54, 0x6f, 0x44,
	0x69, 0x73, 0x6b, 0x12, 0x3a, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x63, 0x76, 0x2e, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22,
	0x14, 0x0a, 0x12, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x46, 0x0a, 0x20, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x72, 0x73, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x6c, 0x61,
	0x6e, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x70, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0b, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x50, 0x78, 0x22, 0xde, 0x03,
	0x0a, 0x20, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x42,
	0x75, 0x72, 0x73, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x3b, 0x0a,
	0x1a, 0x64, 0x6f, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x17, 0x64, 0x6f, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x4d, 0x73, 0x12, 0x26, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x50, 0x61, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x50, 0x61, 0x74, 0x68, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x61,
	0x76, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x73, 0x61, 0x76, 0x65, 0x50,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x5d,
	0x0a, 0x10, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x32, 0x50, 0x42,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x72, 0x73, 0x74, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x0f, 0x0a,
	0x0d, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x22, 0x23,
	0x0a, 0x21, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x42,
	0x75, 0x72, 0x73, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x77, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x65,
	0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x4d, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x6e, 0x0a, 0x18,
	0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x74, 0x68, 0x69,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x1b, 0x0a, 0x19,
	0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x55, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73,
	0x22, 0x62, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x63, 0x75, 0x73,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x4d, 0x73, 0x22, 0x2a, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x44, 0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x22, 0x17, 0x0a, 0x15, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x49, 0x0a, 0x11, 0x4c, 0x61, 0x73,
	0x74, 0x4e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15,
	0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x73, 0x22, 0x41, 0x0a, 0x1b, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x43,
	0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x70, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x22, 0x3f, 0x0a, 0x21, 0x53, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x54, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x52, 0x0a, 0x12, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x41,
	0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x6d, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6e, 0x75, 0x6d,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x60, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x45, 0x0a, 0x0b, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x41, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0a, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x22, 0x90, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x39, 0x0a, 0x19, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x1e, 0x0a, 0x1c, 0x53, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x4a, 0x0a, 0x0c, 0x50, 0x63,
	0x61, 0x6d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x63,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x61,
	0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x5d, 0x0a, 0x1d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x73, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x76, 0x2e,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x63,
	0x61, 0x6d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x09, 0x73, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69,
	0x70, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x63, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x4d, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x78, 0x5f,
	0x70, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x58, 0x50, 0x78, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x79, 0x5f,
	0x70, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x59, 0x50, 0x78, 0x22, 0x7a, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x46, 0x6f,
	0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x12, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x61,
	0x6e, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x10, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2a,
	0x41, 0x0a, 0x0d, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x55, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x07, 0x0a, 0x03, 0x50, 0x32, 0x50, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x70, 0x74,
	0x69, 0x63, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x77, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x10, 0x03, 0x2a, 0x29, 0x0a, 0x08, 0x48, 0x69, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x08,
	0x0a, 0x04, 0x57, 0x45, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x52, 0x4f, 0x50,
	0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x4c, 0x41, 0x4e, 0x54, 0x10, 0x02, 0x2a, 0x27, 0x0a,
	0x0e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0b, 0x0a, 0x07, 0x50, 0x52, 0x45, 0x44, 0x49, 0x43, 0x54, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04,
	0x43, 0x48, 0x49, 0x50, 0x10, 0x01, 0x2a, 0x63, 0x0a, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x47, 0x52, 0x41, 0x42, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4f, 0x4e, 0x4e, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x4e, 0x4f, 0x5f, 0x49, 0x4d,
	0x50, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x1b,
	0x0a, 0x17, 0x4e, 0x4f, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x4c, 0x41,
	0x53, 0x54, 0x5f, 0x4d, 0x49, 0x4e, 0x55, 0x54, 0x45, 0x10, 0x04, 0x32, 0xb6, 0x2e, 0x0a, 0x10,
	0x43, 0x56, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x62, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x50, 0x32, 0x50, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x26, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x32, 0x50, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74,
	0x50, 0x32, 0x50, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x74, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x89,
	0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x33, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65,
	0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x6f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8f, 0x01, 0x0a, 0x1c, 0x53,
	0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x35, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53,
	0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65,
	0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8f, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x35, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77,
	0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x69, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x95,
	0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x12, 0x37, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64,
	0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x76, 0x2e,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12,
	0x2e, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x54, 0x65, 0x6d, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x54, 0x65, 0x6d, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x6e, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x2a, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x6e, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x2a, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x7d, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x75, 0x72, 0x73, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x63,
	0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x7a, 0x0a, 0x15, 0x53, 0x74, 0x6f, 0x70, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x2e, 0x2e, 0x63, 0x76, 0x2e,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74,
	0x6f, 0x70, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x76, 0x2e,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74,
	0x6f, 0x70, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x26,
	0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x62, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x12, 0x26, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x76, 0x2e,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x69,
	0x6e, 0x67, 0x12, 0x22, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a,
	0x07, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x12, 0x20, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x76, 0x2e,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x5f, 0x0a, 0x0c, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6e, 0x64, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12,
	0x25, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6e, 0x64, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6e,
	0x64, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x53, 0x0a, 0x08, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x21, 0x2e, 0x63,
	0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0a, 0x55, 0x6e, 0x73, 0x65, 0x74, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x23, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x6e, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x6e, 0x73, 0x65,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x62, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x74, 0x68,
	0x73, 0x12, 0x26, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x74,
	0x68, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x47, 0x50, 0x53, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x47, 0x50, 0x53,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x47, 0x50, 0x53, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x12, 0x53,
	0x65, 0x74, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2b, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62,
	0x0a, 0x0d, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x26, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x62, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x12, 0x26, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x75, 0x65, 0x73, 0x12, 0x17, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x29, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x6b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x29, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x64, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x2a, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x50, 0x32, 0x50, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x2a, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x50, 0x32, 0x50,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63,
	0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x50, 0x32, 0x50, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x67, 0x0a,
	0x0c, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x25, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x69, 0x70, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69,
	0x70, 0x51, 0x75, 0x65, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2d, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x69, 0x70, 0x51, 0x75, 0x65, 0x75, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2e, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x69, 0x70, 0x51, 0x75, 0x65, 0x75, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x5c, 0x0a, 0x0b, 0x46, 0x6c, 0x75, 0x73, 0x68, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x73, 0x12, 0x24, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x6c, 0x75, 0x73, 0x68, 0x51, 0x75, 0x65, 0x75, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x6c, 0x75, 0x73, 0x68,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x67, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x27, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x65, 0x61, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x2e, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x65,
	0x61, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x86, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x32,
	0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x42, 0x75, 0x72, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x09, 0x47, 0x65, 0x74,
	0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x12, 0x22, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x76, 0x2e,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65,
	0x74, 0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x53, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x21, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65,
	0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x42, 0x79, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x35, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65,
	0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x42, 0x79, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x22,
	0x00, 0x12, 0x8f, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x35, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x53, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0a, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x23, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x74,
	0x0a, 0x13, 0x53, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2c, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f,
	0x57, 0x68, 0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x57, 0x68,
	0x69, 0x74, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x2e, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x22,
	0x00, 0x12, 0x61, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x32, 0x50, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x29, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x50, 0x32, 0x50, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x32, 0x50, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x00, 0x12, 0x6e, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x19, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66,
	0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x72, 0x73, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x12, 0x32, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x42, 0x75, 0x72, 0x73, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x32, 0x50, 0x42, 0x75, 0x66,
	0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x72, 0x73, 0x74, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x71, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x12, 0x2b, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x46, 0x6f,
	0x63, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x63, 0x75, 0x73,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x62, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69,
	0x72, 0x12, 0x26, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44,
	0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x76, 0x2e, 0x72,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x4e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x62, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x17, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2d,
	0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x6e, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x54,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x54, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x17, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x33, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x54, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x4e, 0x0a, 0x18, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x2e, 0x63, 0x76,
	0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x17, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x63, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x2f, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x27,
	0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x73, 0x12, 0x2e, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70,
	0x46, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x2f, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x46, 0x6f, 0x72, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x0a, 0x5a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x76,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cv_runtime_proto_cv_runtime_proto_rawDescOnce sync.Once
	file_cv_runtime_proto_cv_runtime_proto_rawDescData = file_cv_runtime_proto_cv_runtime_proto_rawDesc
)

func file_cv_runtime_proto_cv_runtime_proto_rawDescGZIP() []byte {
	file_cv_runtime_proto_cv_runtime_proto_rawDescOnce.Do(func() {
		file_cv_runtime_proto_cv_runtime_proto_rawDescData = protoimpl.X.CompressGZIP(file_cv_runtime_proto_cv_runtime_proto_rawDescData)
	})
	return file_cv_runtime_proto_cv_runtime_proto_rawDescData
}

var file_cv_runtime_proto_cv_runtime_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_cv_runtime_proto_cv_runtime_proto_msgTypes = make([]protoimpl.MessageInfo, 130)
var file_cv_runtime_proto_cv_runtime_proto_goTypes = []interface{}{
	(BufferUseCase)(0),                                 // 0: cv.runtime.proto.BufferUseCase
	(HitClass)(0),                                      // 1: cv.runtime.proto.HitClass
	(ScoreQueueType)(0),                                // 2: cv.runtime.proto.ScoreQueueType
	(ErrorType)(0),                                     // 3: cv.runtime.proto.ErrorType
	(*TargetSafetyZone)(nil),                           // 4: cv.runtime.proto.TargetSafetyZone
	(*P2PContext)(nil),                                 // 5: cv.runtime.proto.P2PContext
	(*SetP2PContextRequest)(nil),                       // 6: cv.runtime.proto.SetP2PContextRequest
	(*SetP2PContextResponse)(nil),                      // 7: cv.runtime.proto.SetP2PContextResponse
	(*GetCameraDimensionsRequest)(nil),                 // 8: cv.runtime.proto.GetCameraDimensionsRequest
	(*GetCameraDimensionsResponse)(nil),                // 9: cv.runtime.proto.GetCameraDimensionsResponse
	(*StartP2PDataCaptureRequest)(nil),                 // 10: cv.runtime.proto.StartP2PDataCaptureRequest
	(*PointDetectionCategory)(nil),                     // 11: cv.runtime.proto.PointDetectionCategory
	(*SegmentationDetectionCategory)(nil),              // 12: cv.runtime.proto.SegmentationDetectionCategory
	(*DeepweedDetectionCriteriaSetting)(nil),           // 13: cv.runtime.proto.DeepweedDetectionCriteriaSetting
	(*SetDeepweedDetectionCriteriaRequest)(nil),        // 14: cv.runtime.proto.SetDeepweedDetectionCriteriaRequest
	(*SetDeepweedDetectionCriteriaResponse)(nil),       // 15: cv.runtime.proto.SetDeepweedDetectionCriteriaResponse
	(*GetDeepweedDetectionCriteriaRequest)(nil),        // 16: cv.runtime.proto.GetDeepweedDetectionCriteriaRequest
	(*GetDeepweedDetectionCriteriaResponse)(nil),       // 17: cv.runtime.proto.GetDeepweedDetectionCriteriaResponse
	(*GetDeepweedSupportedCategoriesRequest)(nil),      // 18: cv.runtime.proto.GetDeepweedSupportedCategoriesRequest
	(*GetDeepweedSupportedCategoriesResponse)(nil),     // 19: cv.runtime.proto.GetDeepweedSupportedCategoriesResponse
	(*GetDeepweedIndexToCategoryRequest)(nil),          // 20: cv.runtime.proto.GetDeepweedIndexToCategoryRequest
	(*GetDeepweedIndexToCategoryResponse)(nil),         // 21: cv.runtime.proto.GetDeepweedIndexToCategoryResponse
	(*GetPredictCamMatrixRequest)(nil),                 // 22: cv.runtime.proto.GetPredictCamMatrixRequest
	(*GetPredictCamDistortionCoefficientsRequest)(nil), // 23: cv.runtime.proto.GetPredictCamDistortionCoefficientsRequest
	(*SetCameraSettingsRequest)(nil),                   // 24: cv.runtime.proto.SetCameraSettingsRequest
	(*SetCameraSettingsResponse)(nil),                  // 25: cv.runtime.proto.SetCameraSettingsResponse
	(*SetAutoWhitebalanceRequest)(nil),                 // 26: cv.runtime.proto.SetAutoWhitebalanceRequest
	(*SetAutoWhitebalanceResponse)(nil),                // 27: cv.runtime.proto.SetAutoWhitebalanceResponse
	(*GetCameraSettingsRequest)(nil),                   // 28: cv.runtime.proto.GetCameraSettingsRequest
	(*CameraSettingsResponse)(nil),                     // 29: cv.runtime.proto.CameraSettingsResponse
	(*GetCameraSettingsResponse)(nil),                  // 30: cv.runtime.proto.GetCameraSettingsResponse
	(*StartBurstRecordFramesRequest)(nil),              // 31: cv.runtime.proto.StartBurstRecordFramesRequest
	(*StartBurstRecordFramesResponse)(nil),             // 32: cv.runtime.proto.StartBurstRecordFramesResponse
	(*StopBurstRecordFramesRequest)(nil),               // 33: cv.runtime.proto.StopBurstRecordFramesRequest
	(*StopBurstRecordFramesResponse)(nil),              // 34: cv.runtime.proto.StopBurstRecordFramesResponse
	(*P2POutputProto)(nil),                             // 35: cv.runtime.proto.P2POutputProto
	(*GetNextP2POutputRequest)(nil),                    // 36: cv.runtime.proto.GetNextP2POutputRequest
	(*GetConnectorsRequest)(nil),                       // 37: cv.runtime.proto.GetConnectorsRequest
	(*ConnectorResponse)(nil),                          // 38: cv.runtime.proto.ConnectorResponse
	(*GetConnectorsResponse)(nil),                      // 39: cv.runtime.proto.GetConnectorsResponse
	(*SetConnectorsRequest)(nil),                       // 40: cv.runtime.proto.SetConnectorsRequest
	(*SetConnectorsResponse)(nil),                      // 41: cv.runtime.proto.SetConnectorsResponse
	(*NodeTiming)(nil),                                 // 42: cv.runtime.proto.NodeTiming
	(*GetTimingRequest)(nil),                           // 43: cv.runtime.proto.GetTimingRequest
	(*GetTimingResponse)(nil),                          // 44: cv.runtime.proto.GetTimingResponse
	(*PredictRequest)(nil),                             // 45: cv.runtime.proto.PredictRequest
	(*PredictResponse)(nil),                            // 46: cv.runtime.proto.PredictResponse
	(*LoadAndQueueRequest)(nil),                        // 47: cv.runtime.proto.LoadAndQueueRequest
	(*LoadAndQueueResponse)(nil),                       // 48: cv.runtime.proto.LoadAndQueueResponse
	(*SetImageRequest)(nil),                            // 49: cv.runtime.proto.SetImageRequest
	(*SetImageResponse)(nil),                           // 50: cv.runtime.proto.SetImageResponse
	(*UnsetImageRequest)(nil),                          // 51: cv.runtime.proto.UnsetImageRequest
	(*UnsetImageResponse)(nil),                         // 52: cv.runtime.proto.UnsetImageResponse
	(*GetModelPathsRequest)(nil),                       // 53: cv.runtime.proto.GetModelPathsRequest
	(*GetModelPathsResponse)(nil),                      // 54: cv.runtime.proto.GetModelPathsResponse
	(*GetCameraTemperaturesRequest)(nil),               // 55: cv.runtime.proto.GetCameraTemperaturesRequest
	(*CameraTemperature)(nil),                          // 56: cv.runtime.proto.CameraTemperature
	(*GetCameraTemperaturesResponse)(nil),              // 57: cv.runtime.proto.GetCameraTemperaturesResponse
	(*GeoLLA)(nil),                                     // 58: cv.runtime.proto.GeoLLA
	(*GeoECEF)(nil),                                    // 59: cv.runtime.proto.GeoECEF
	(*SetGPSLocationRequest)(nil),                      // 60: cv.runtime.proto.SetGPSLocationRequest
	(*SetGPSLocationResponse)(nil),                     // 61: cv.runtime.proto.SetGPSLocationResponse
	(*SetImplementStatusRequest)(nil),                  // 62: cv.runtime.proto.SetImplementStatusRequest
	(*SetImplementStatusResponse)(nil),                 // 63: cv.runtime.proto.SetImplementStatusResponse
	(*SetImageScoreRequest)(nil),                       // 64: cv.runtime.proto.SetImageScoreRequest
	(*SetImageScoreResponse)(nil),                      // 65: cv.runtime.proto.SetImageScoreResponse
	(*GetScoreQueueRequest)(nil),                       // 66: cv.runtime.proto.GetScoreQueueRequest
	(*ScoreObject)(nil),                                // 67: cv.runtime.proto.ScoreObject
	(*GetScoreQueueResponse)(nil),                      // 68: cv.runtime.proto.GetScoreQueueResponse
	(*GetMaxImageScoreRequest)(nil),                    // 69: cv.runtime.proto.GetMaxImageScoreRequest
	(*GetMaxImageScoreResponse)(nil),                   // 70: cv.runtime.proto.GetMaxImageScoreResponse
	(*GetMaxScoredImageRequest)(nil),                   // 71: cv.runtime.proto.GetMaxScoredImageRequest
	(*GetLatestP2PImageRequest)(nil),                   // 72: cv.runtime.proto.GetLatestP2PImageRequest
	(*GetLatestImageRequest)(nil),                      // 73: cv.runtime.proto.GetLatestImageRequest
	(*GetImageNearTimestampRequest)(nil),               // 74: cv.runtime.proto.GetImageNearTimestampRequest
	(*GetChipImageRequest)(nil),                        // 75: cv.runtime.proto.GetChipImageRequest
	(*FlushQueuesRequest)(nil),                         // 76: cv.runtime.proto.FlushQueuesRequest
	(*FlushQueuesResponse)(nil),                        // 77: cv.runtime.proto.FlushQueuesResponse
	(*ImageAndMetadataResponse)(nil),                   // 78: cv.runtime.proto.ImageAndMetadataResponse
	(*CategoryPrediction)(nil),                         // 79: cv.runtime.proto.CategoryPrediction
	(*ChipPrediction)(nil),                             // 80: cv.runtime.proto.ChipPrediction
	(*ChipImageAndMetadataResponse)(nil),               // 81: cv.runtime.proto.ChipImageAndMetadataResponse
	(*ChipQueueInformationRequest)(nil),                // 82: cv.runtime.proto.ChipQueueInformationRequest
	(*ChipQueueInformationResponse)(nil),               // 83: cv.runtime.proto.ChipQueueInformationResponse
	(*P2PImageAndMetadataResponse)(nil),                // 84: cv.runtime.proto.P2PImageAndMetadataResponse
	(*GetCameraInfoRequest)(nil),                       // 85: cv.runtime.proto.GetCameraInfoRequest
	(*CameraInfo)(nil),                                 // 86: cv.runtime.proto.CameraInfo
	(*GetCameraInfoResponse)(nil),                      // 87: cv.runtime.proto.GetCameraInfoResponse
	(*GetLightweightBurstRecordRequest)(nil),           // 88: cv.runtime.proto.GetLightweightBurstRecordRequest
	(*GetLightweightBurstRecordResponse)(nil),          // 89: cv.runtime.proto.GetLightweightBurstRecordResponse
	(*GetBootedRequest)(nil),                           // 90: cv.runtime.proto.GetBootedRequest
	(*GetBootedResponse)(nil),                          // 91: cv.runtime.proto.GetBootedResponse
	(*GetReadyRequest)(nil),                            // 92: cv.runtime.proto.GetReadyRequest
	(*GetReadyResponse)(nil),                           // 93: cv.runtime.proto.GetReadyResponse
	(*GetErrorStateResponse)(nil),                      // 94: cv.runtime.proto.GetErrorStateResponse
	(*DeepweedDetection)(nil),                          // 95: cv.runtime.proto.DeepweedDetection
	(*DeepweedOutput)(nil),                             // 96: cv.runtime.proto.DeepweedOutput
	(*GetDeepweedOutputByTimestampRequest)(nil),        // 97: cv.runtime.proto.GetDeepweedOutputByTimestampRequest
	(*GetRecommendedStrobeSettingsRequest)(nil),        // 98: cv.runtime.proto.GetRecommendedStrobeSettingsRequest
	(*GetRecommendedStrobeSettingsResponse)(nil),       // 99: cv.runtime.proto.GetRecommendedStrobeSettingsResponse
	(*StartP2PBufferringRequest)(nil),                  // 100: cv.runtime.proto.StartP2PBufferringRequest
	(*StartP2PBufferringResponse)(nil),                 // 101: cv.runtime.proto.StartP2PBufferringResponse
	(*StopP2PBufferringRequest)(nil),                   // 102: cv.runtime.proto.StopP2PBufferringRequest
	(*StopP2PBufferringResponse)(nil),                  // 103: cv.runtime.proto.StopP2PBufferringResponse
	(*P2PCaptureRequest)(nil),                          // 104: cv.runtime.proto.P2PCaptureRequest
	(*P2PCaptureResponse)(nil),                         // 105: cv.runtime.proto.P2PCaptureResponse
	(*P2PBufferingBurstPredictMetadata)(nil),           // 106: cv.runtime.proto.P2PBufferingBurstPredictMetadata
	(*P2PBufferringBurstCaptureRequest)(nil),           // 107: cv.runtime.proto.P2PBufferringBurstCaptureRequest
	(*P2PBufferringBurstCaptureResponse)(nil),          // 108: cv.runtime.proto.P2PBufferringBurstCaptureResponse
	(*GetNextDeepweedOutputRequest)(nil),               // 109: cv.runtime.proto.GetNextDeepweedOutputRequest
	(*SetTargetingStateRequest)(nil),                   // 110: cv.runtime.proto.SetTargetingStateRequest
	(*SetTargetingStateResponse)(nil),                  // 111: cv.runtime.proto.SetTargetingStateResponse
	(*GetNextFocusMetricRequest)(nil),                  // 112: cv.runtime.proto.GetNextFocusMetricRequest
	(*GetNextFocusMetricResponse)(nil),                 // 113: cv.runtime.proto.GetNextFocusMetricResponse
	(*RemoveDataDirRequest)(nil),                       // 114: cv.runtime.proto.RemoveDataDirRequest
	(*RemoveDataDirResponse)(nil),                      // 115: cv.runtime.proto.RemoveDataDirResponse
	(*LastNImageRequest)(nil),                          // 116: cv.runtime.proto.LastNImageRequest
	(*ComputeCapabilitiesResponse)(nil),                // 117: cv.runtime.proto.ComputeCapabilitiesResponse
	(*SupportedTensorRTVersionsResponse)(nil),          // 118: cv.runtime.proto.SupportedTensorRTVersionsResponse
	(*Empty)(nil),                                      // 119: cv.runtime.proto.Empty
	(*ScoreQueueAndCount)(nil),                         // 120: cv.runtime.proto.ScoreQueueAndCount
	(*ListScoreQueuesResponse)(nil),                    // 121: cv.runtime.proto.ListScoreQueuesResponse
	(*GetCategoryCollectionResponse)(nil),              // 122: cv.runtime.proto.GetCategoryCollectionResponse
	(*SnapshotPredictImagesRequest)(nil),               // 123: cv.runtime.proto.SnapshotPredictImagesRequest
	(*PcamSnapshot)(nil),                               // 124: cv.runtime.proto.PcamSnapshot
	(*SnapshotPredictImagesResponse)(nil),              // 125: cv.runtime.proto.SnapshotPredictImagesResponse
	(*GetChipForPredictImageRequest)(nil),              // 126: cv.runtime.proto.GetChipForPredictImageRequest
	(*GetChipForPredictImageResponse)(nil),             // 127: cv.runtime.proto.GetChipForPredictImageResponse
	nil,                                                // 128: cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry
	nil,                                                // 129: cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry
	nil,                                                // 130: cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry
	nil,                                                // 131: cv.runtime.proto.NodeTiming.StateTimingsEntry
	nil,                                                // 132: cv.runtime.proto.GetReadyResponse.DeepweedReadyStateEntry
	nil,                                                // 133: cv.runtime.proto.GetReadyResponse.P2pReadyStateEntry
	(LightSourcePreset)(0),                             // 134: lib.common.camera.LightSourcePreset
	(P2PCaptureReason)(0),                              // 135: carbon.aimbot.cv.P2PCaptureReason
	(*weed_tracking.Detection)(nil),                    // 136: weed_tracking.Detection
}
var file_cv_runtime_proto_cv_runtime_proto_depIdxs = []int32{
	5,   // 0: cv.runtime.proto.SetP2PContextRequest.primary_context:type_name -> cv.runtime.proto.P2PContext
	5,   // 1: cv.runtime.proto.SetP2PContextRequest.secondary_context:type_name -> cv.runtime.proto.P2PContext
	4,   // 2: cv.runtime.proto.SetP2PContextRequest.safety_zone:type_name -> cv.runtime.proto.TargetSafetyZone
	11,  // 3: cv.runtime.proto.DeepweedDetectionCriteriaSetting.point_categories:type_name -> cv.runtime.proto.PointDetectionCategory
	12,  // 4: cv.runtime.proto.DeepweedDetectionCriteriaSetting.segmentation_categories:type_name -> cv.runtime.proto.SegmentationDetectionCategory
	11,  // 5: cv.runtime.proto.SetDeepweedDetectionCriteriaRequest.point_categories:type_name -> cv.runtime.proto.PointDetectionCategory
	12,  // 6: cv.runtime.proto.SetDeepweedDetectionCriteriaRequest.segmentation_categories:type_name -> cv.runtime.proto.SegmentationDetectionCategory
	11,  // 7: cv.runtime.proto.GetDeepweedDetectionCriteriaResponse.point_categories:type_name -> cv.runtime.proto.PointDetectionCategory
	12,  // 8: cv.runtime.proto.GetDeepweedDetectionCriteriaResponse.segmentation_categories:type_name -> cv.runtime.proto.SegmentationDetectionCategory
	128, // 9: cv.runtime.proto.GetDeepweedIndexToCategoryResponse.weed_point_index_to_category:type_name -> cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry
	129, // 10: cv.runtime.proto.GetDeepweedIndexToCategoryResponse.crop_point_index_to_category:type_name -> cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry
	130, // 11: cv.runtime.proto.GetDeepweedIndexToCategoryResponse.intersection_index_to_category:type_name -> cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry
	134, // 12: cv.runtime.proto.SetCameraSettingsRequest.light_source_preset:type_name -> lib.common.camera.LightSourcePreset
	134, // 13: cv.runtime.proto.CameraSettingsResponse.light_source_preset:type_name -> lib.common.camera.LightSourcePreset
	29,  // 14: cv.runtime.proto.GetCameraSettingsResponse.camera_settings_response:type_name -> cv.runtime.proto.CameraSettingsResponse
	38,  // 15: cv.runtime.proto.GetConnectorsResponse.connector_response:type_name -> cv.runtime.proto.ConnectorResponse
	131, // 16: cv.runtime.proto.NodeTiming.state_timings:type_name -> cv.runtime.proto.NodeTiming.StateTimingsEntry
	42,  // 17: cv.runtime.proto.GetTimingResponse.node_timing:type_name -> cv.runtime.proto.NodeTiming
	56,  // 18: cv.runtime.proto.GetCameraTemperaturesResponse.temperature:type_name -> cv.runtime.proto.CameraTemperature
	58,  // 19: cv.runtime.proto.SetGPSLocationRequest.lla:type_name -> cv.runtime.proto.GeoLLA
	59,  // 20: cv.runtime.proto.SetGPSLocationRequest.ecef:type_name -> cv.runtime.proto.GeoECEF
	96,  // 21: cv.runtime.proto.SetImageScoreRequest.deepweed_output:type_name -> cv.runtime.proto.DeepweedOutput
	67,  // 22: cv.runtime.proto.GetScoreQueueResponse.score_object:type_name -> cv.runtime.proto.ScoreObject
	135, // 23: cv.runtime.proto.GetLatestP2PImageRequest.reason:type_name -> carbon.aimbot.cv.P2PCaptureReason
	2,   // 24: cv.runtime.proto.FlushQueuesRequest.score_queue_type:type_name -> cv.runtime.proto.ScoreQueueType
	136, // 25: cv.runtime.proto.ImageAndMetadataResponse.deepweed_detections:type_name -> weed_tracking.Detection
	79,  // 26: cv.runtime.proto.ChipPrediction.score:type_name -> cv.runtime.proto.CategoryPrediction
	79,  // 27: cv.runtime.proto.ChipPrediction.embedding_distance:type_name -> cv.runtime.proto.CategoryPrediction
	78,  // 28: cv.runtime.proto.ChipImageAndMetadataResponse.image_and_metadata:type_name -> cv.runtime.proto.ImageAndMetadataResponse
	80,  // 29: cv.runtime.proto.ChipImageAndMetadataResponse.prediction_metadata:type_name -> cv.runtime.proto.ChipPrediction
	79,  // 30: cv.runtime.proto.ChipQueueInformationResponse.queue_score:type_name -> cv.runtime.proto.CategoryPrediction
	3,   // 31: cv.runtime.proto.CameraInfo.error_type:type_name -> cv.runtime.proto.ErrorType
	86,  // 32: cv.runtime.proto.GetCameraInfoResponse.camera_info:type_name -> cv.runtime.proto.CameraInfo
	132, // 33: cv.runtime.proto.GetReadyResponse.deepweed_ready_state:type_name -> cv.runtime.proto.GetReadyResponse.DeepweedReadyStateEntry
	133, // 34: cv.runtime.proto.GetReadyResponse.p2p_ready_state:type_name -> cv.runtime.proto.GetReadyResponse.P2pReadyStateEntry
	1,   // 35: cv.runtime.proto.DeepweedDetection.hit_class:type_name -> cv.runtime.proto.HitClass
	95,  // 36: cv.runtime.proto.DeepweedOutput.detections:type_name -> cv.runtime.proto.DeepweedDetection
	135, // 37: cv.runtime.proto.P2PCaptureRequest.reason:type_name -> carbon.aimbot.cv.P2PCaptureReason
	106, // 38: cv.runtime.proto.P2PBufferringBurstCaptureRequest.predict_metadata:type_name -> cv.runtime.proto.P2PBufferingBurstPredictMetadata
	120, // 39: cv.runtime.proto.ListScoreQueuesResponse.score_queue:type_name -> cv.runtime.proto.ScoreQueueAndCount
	124, // 40: cv.runtime.proto.SnapshotPredictImagesResponse.snapshots:type_name -> cv.runtime.proto.PcamSnapshot
	78,  // 41: cv.runtime.proto.GetChipForPredictImageResponse.image_and_metadata:type_name -> cv.runtime.proto.ImageAndMetadataResponse
	6,   // 42: cv.runtime.proto.CVRuntimeService.SetP2PContext:input_type -> cv.runtime.proto.SetP2PContextRequest
	8,   // 43: cv.runtime.proto.CVRuntimeService.GetCameraDimensions:input_type -> cv.runtime.proto.GetCameraDimensionsRequest
	85,  // 44: cv.runtime.proto.CVRuntimeService.GetCameraInfo:input_type -> cv.runtime.proto.GetCameraInfoRequest
	20,  // 45: cv.runtime.proto.CVRuntimeService.GetDeepweedIndexToCategory:input_type -> cv.runtime.proto.GetDeepweedIndexToCategoryRequest
	14,  // 46: cv.runtime.proto.CVRuntimeService.SetDeepweedDetectionCriteria:input_type -> cv.runtime.proto.SetDeepweedDetectionCriteriaRequest
	16,  // 47: cv.runtime.proto.CVRuntimeService.GetDeepweedDetectionCriteria:input_type -> cv.runtime.proto.GetDeepweedDetectionCriteriaRequest
	18,  // 48: cv.runtime.proto.CVRuntimeService.GetDeepweedSupportedCategories:input_type -> cv.runtime.proto.GetDeepweedSupportedCategoriesRequest
	55,  // 49: cv.runtime.proto.CVRuntimeService.GetCameraTemperatures:input_type -> cv.runtime.proto.GetCameraTemperaturesRequest
	24,  // 50: cv.runtime.proto.CVRuntimeService.SetCameraSettings:input_type -> cv.runtime.proto.SetCameraSettingsRequest
	28,  // 51: cv.runtime.proto.CVRuntimeService.GetCameraSettings:input_type -> cv.runtime.proto.GetCameraSettingsRequest
	31,  // 52: cv.runtime.proto.CVRuntimeService.StartBurstRecordFrames:input_type -> cv.runtime.proto.StartBurstRecordFramesRequest
	33,  // 53: cv.runtime.proto.CVRuntimeService.StopBurstRecordFrames:input_type -> cv.runtime.proto.StopBurstRecordFramesRequest
	37,  // 54: cv.runtime.proto.CVRuntimeService.GetConnectors:input_type -> cv.runtime.proto.GetConnectorsRequest
	40,  // 55: cv.runtime.proto.CVRuntimeService.SetConnectors:input_type -> cv.runtime.proto.SetConnectorsRequest
	43,  // 56: cv.runtime.proto.CVRuntimeService.GetTiming:input_type -> cv.runtime.proto.GetTimingRequest
	45,  // 57: cv.runtime.proto.CVRuntimeService.Predict:input_type -> cv.runtime.proto.PredictRequest
	47,  // 58: cv.runtime.proto.CVRuntimeService.LoadAndQueue:input_type -> cv.runtime.proto.LoadAndQueueRequest
	49,  // 59: cv.runtime.proto.CVRuntimeService.SetImage:input_type -> cv.runtime.proto.SetImageRequest
	51,  // 60: cv.runtime.proto.CVRuntimeService.UnsetImage:input_type -> cv.runtime.proto.UnsetImageRequest
	53,  // 61: cv.runtime.proto.CVRuntimeService.GetModelPaths:input_type -> cv.runtime.proto.GetModelPathsRequest
	60,  // 62: cv.runtime.proto.CVRuntimeService.SetGPSLocation:input_type -> cv.runtime.proto.SetGPSLocationRequest
	62,  // 63: cv.runtime.proto.CVRuntimeService.SetImplementStatus:input_type -> cv.runtime.proto.SetImplementStatusRequest
	64,  // 64: cv.runtime.proto.CVRuntimeService.SetImageScore:input_type -> cv.runtime.proto.SetImageScoreRequest
	66,  // 65: cv.runtime.proto.CVRuntimeService.GetScoreQueue:input_type -> cv.runtime.proto.GetScoreQueueRequest
	119, // 66: cv.runtime.proto.CVRuntimeService.ListScoreQueues:input_type -> cv.runtime.proto.Empty
	69,  // 67: cv.runtime.proto.CVRuntimeService.GetMaxImageScore:input_type -> cv.runtime.proto.GetMaxImageScoreRequest
	71,  // 68: cv.runtime.proto.CVRuntimeService.GetMaxScoredImage:input_type -> cv.runtime.proto.GetMaxScoredImageRequest
	72,  // 69: cv.runtime.proto.CVRuntimeService.GetLatestP2PImage:input_type -> cv.runtime.proto.GetLatestP2PImageRequest
	75,  // 70: cv.runtime.proto.CVRuntimeService.GetChipImage:input_type -> cv.runtime.proto.GetChipImageRequest
	82,  // 71: cv.runtime.proto.CVRuntimeService.GetChipQueueInformation:input_type -> cv.runtime.proto.ChipQueueInformationRequest
	76,  // 72: cv.runtime.proto.CVRuntimeService.FlushQueues:input_type -> cv.runtime.proto.FlushQueuesRequest
	73,  // 73: cv.runtime.proto.CVRuntimeService.GetLatestImage:input_type -> cv.runtime.proto.GetLatestImageRequest
	74,  // 74: cv.runtime.proto.CVRuntimeService.GetImageNearTimestamp:input_type -> cv.runtime.proto.GetImageNearTimestampRequest
	88,  // 75: cv.runtime.proto.CVRuntimeService.GetLightweightBurstRecord:input_type -> cv.runtime.proto.GetLightweightBurstRecordRequest
	90,  // 76: cv.runtime.proto.CVRuntimeService.GetBooted:input_type -> cv.runtime.proto.GetBootedRequest
	92,  // 77: cv.runtime.proto.CVRuntimeService.GetReady:input_type -> cv.runtime.proto.GetReadyRequest
	97,  // 78: cv.runtime.proto.CVRuntimeService.GetDeepweedOutputByTimestamp:input_type -> cv.runtime.proto.GetDeepweedOutputByTimestampRequest
	98,  // 79: cv.runtime.proto.CVRuntimeService.GetRecommendedStrobeSettings:input_type -> cv.runtime.proto.GetRecommendedStrobeSettingsRequest
	104, // 80: cv.runtime.proto.CVRuntimeService.P2PCapture:input_type -> cv.runtime.proto.P2PCaptureRequest
	26,  // 81: cv.runtime.proto.CVRuntimeService.SetAutoWhitebalance:input_type -> cv.runtime.proto.SetAutoWhitebalanceRequest
	109, // 82: cv.runtime.proto.CVRuntimeService.GetNextDeepweedOutput:input_type -> cv.runtime.proto.GetNextDeepweedOutputRequest
	36,  // 83: cv.runtime.proto.CVRuntimeService.GetNextP2POutput:input_type -> cv.runtime.proto.GetNextP2POutputRequest
	110, // 84: cv.runtime.proto.CVRuntimeService.SetTargetingState:input_type -> cv.runtime.proto.SetTargetingStateRequest
	107, // 85: cv.runtime.proto.CVRuntimeService.P2PBufferringBurstCapture:input_type -> cv.runtime.proto.P2PBufferringBurstCaptureRequest
	112, // 86: cv.runtime.proto.CVRuntimeService.GetNextFocusMetric:input_type -> cv.runtime.proto.GetNextFocusMetricRequest
	114, // 87: cv.runtime.proto.CVRuntimeService.RemoveDataDir:input_type -> cv.runtime.proto.RemoveDataDirRequest
	116, // 88: cv.runtime.proto.CVRuntimeService.GetLastNImages:input_type -> cv.runtime.proto.LastNImageRequest
	119, // 89: cv.runtime.proto.CVRuntimeService.GetComputeCapabilities:input_type -> cv.runtime.proto.Empty
	119, // 90: cv.runtime.proto.CVRuntimeService.GetSupportedTensorRTVersions:input_type -> cv.runtime.proto.Empty
	119, // 91: cv.runtime.proto.CVRuntimeService.ReloadCategoryCollection:input_type -> cv.runtime.proto.Empty
	119, // 92: cv.runtime.proto.CVRuntimeService.GetCategoryCollection:input_type -> cv.runtime.proto.Empty
	119, // 93: cv.runtime.proto.CVRuntimeService.GetErrorState:input_type -> cv.runtime.proto.Empty
	123, // 94: cv.runtime.proto.CVRuntimeService.SnapshotPredictImages:input_type -> cv.runtime.proto.SnapshotPredictImagesRequest
	126, // 95: cv.runtime.proto.CVRuntimeService.GetChipForPredictImage:input_type -> cv.runtime.proto.GetChipForPredictImageRequest
	7,   // 96: cv.runtime.proto.CVRuntimeService.SetP2PContext:output_type -> cv.runtime.proto.SetP2PContextResponse
	9,   // 97: cv.runtime.proto.CVRuntimeService.GetCameraDimensions:output_type -> cv.runtime.proto.GetCameraDimensionsResponse
	87,  // 98: cv.runtime.proto.CVRuntimeService.GetCameraInfo:output_type -> cv.runtime.proto.GetCameraInfoResponse
	21,  // 99: cv.runtime.proto.CVRuntimeService.GetDeepweedIndexToCategory:output_type -> cv.runtime.proto.GetDeepweedIndexToCategoryResponse
	15,  // 100: cv.runtime.proto.CVRuntimeService.SetDeepweedDetectionCriteria:output_type -> cv.runtime.proto.SetDeepweedDetectionCriteriaResponse
	17,  // 101: cv.runtime.proto.CVRuntimeService.GetDeepweedDetectionCriteria:output_type -> cv.runtime.proto.GetDeepweedDetectionCriteriaResponse
	19,  // 102: cv.runtime.proto.CVRuntimeService.GetDeepweedSupportedCategories:output_type -> cv.runtime.proto.GetDeepweedSupportedCategoriesResponse
	57,  // 103: cv.runtime.proto.CVRuntimeService.GetCameraTemperatures:output_type -> cv.runtime.proto.GetCameraTemperaturesResponse
	25,  // 104: cv.runtime.proto.CVRuntimeService.SetCameraSettings:output_type -> cv.runtime.proto.SetCameraSettingsResponse
	30,  // 105: cv.runtime.proto.CVRuntimeService.GetCameraSettings:output_type -> cv.runtime.proto.GetCameraSettingsResponse
	32,  // 106: cv.runtime.proto.CVRuntimeService.StartBurstRecordFrames:output_type -> cv.runtime.proto.StartBurstRecordFramesResponse
	34,  // 107: cv.runtime.proto.CVRuntimeService.StopBurstRecordFrames:output_type -> cv.runtime.proto.StopBurstRecordFramesResponse
	39,  // 108: cv.runtime.proto.CVRuntimeService.GetConnectors:output_type -> cv.runtime.proto.GetConnectorsResponse
	41,  // 109: cv.runtime.proto.CVRuntimeService.SetConnectors:output_type -> cv.runtime.proto.SetConnectorsResponse
	44,  // 110: cv.runtime.proto.CVRuntimeService.GetTiming:output_type -> cv.runtime.proto.GetTimingResponse
	46,  // 111: cv.runtime.proto.CVRuntimeService.Predict:output_type -> cv.runtime.proto.PredictResponse
	48,  // 112: cv.runtime.proto.CVRuntimeService.LoadAndQueue:output_type -> cv.runtime.proto.LoadAndQueueResponse
	50,  // 113: cv.runtime.proto.CVRuntimeService.SetImage:output_type -> cv.runtime.proto.SetImageResponse
	52,  // 114: cv.runtime.proto.CVRuntimeService.UnsetImage:output_type -> cv.runtime.proto.UnsetImageResponse
	54,  // 115: cv.runtime.proto.CVRuntimeService.GetModelPaths:output_type -> cv.runtime.proto.GetModelPathsResponse
	61,  // 116: cv.runtime.proto.CVRuntimeService.SetGPSLocation:output_type -> cv.runtime.proto.SetGPSLocationResponse
	63,  // 117: cv.runtime.proto.CVRuntimeService.SetImplementStatus:output_type -> cv.runtime.proto.SetImplementStatusResponse
	65,  // 118: cv.runtime.proto.CVRuntimeService.SetImageScore:output_type -> cv.runtime.proto.SetImageScoreResponse
	68,  // 119: cv.runtime.proto.CVRuntimeService.GetScoreQueue:output_type -> cv.runtime.proto.GetScoreQueueResponse
	121, // 120: cv.runtime.proto.CVRuntimeService.ListScoreQueues:output_type -> cv.runtime.proto.ListScoreQueuesResponse
	70,  // 121: cv.runtime.proto.CVRuntimeService.GetMaxImageScore:output_type -> cv.runtime.proto.GetMaxImageScoreResponse
	78,  // 122: cv.runtime.proto.CVRuntimeService.GetMaxScoredImage:output_type -> cv.runtime.proto.ImageAndMetadataResponse
	84,  // 123: cv.runtime.proto.CVRuntimeService.GetLatestP2PImage:output_type -> cv.runtime.proto.P2PImageAndMetadataResponse
	81,  // 124: cv.runtime.proto.CVRuntimeService.GetChipImage:output_type -> cv.runtime.proto.ChipImageAndMetadataResponse
	83,  // 125: cv.runtime.proto.CVRuntimeService.GetChipQueueInformation:output_type -> cv.runtime.proto.ChipQueueInformationResponse
	77,  // 126: cv.runtime.proto.CVRuntimeService.FlushQueues:output_type -> cv.runtime.proto.FlushQueuesResponse
	78,  // 127: cv.runtime.proto.CVRuntimeService.GetLatestImage:output_type -> cv.runtime.proto.ImageAndMetadataResponse
	78,  // 128: cv.runtime.proto.CVRuntimeService.GetImageNearTimestamp:output_type -> cv.runtime.proto.ImageAndMetadataResponse
	89,  // 129: cv.runtime.proto.CVRuntimeService.GetLightweightBurstRecord:output_type -> cv.runtime.proto.GetLightweightBurstRecordResponse
	91,  // 130: cv.runtime.proto.CVRuntimeService.GetBooted:output_type -> cv.runtime.proto.GetBootedResponse
	93,  // 131: cv.runtime.proto.CVRuntimeService.GetReady:output_type -> cv.runtime.proto.GetReadyResponse
	96,  // 132: cv.runtime.proto.CVRuntimeService.GetDeepweedOutputByTimestamp:output_type -> cv.runtime.proto.DeepweedOutput
	99,  // 133: cv.runtime.proto.CVRuntimeService.GetRecommendedStrobeSettings:output_type -> cv.runtime.proto.GetRecommendedStrobeSettingsResponse
	105, // 134: cv.runtime.proto.CVRuntimeService.P2PCapture:output_type -> cv.runtime.proto.P2PCaptureResponse
	27,  // 135: cv.runtime.proto.CVRuntimeService.SetAutoWhitebalance:output_type -> cv.runtime.proto.SetAutoWhitebalanceResponse
	96,  // 136: cv.runtime.proto.CVRuntimeService.GetNextDeepweedOutput:output_type -> cv.runtime.proto.DeepweedOutput
	35,  // 137: cv.runtime.proto.CVRuntimeService.GetNextP2POutput:output_type -> cv.runtime.proto.P2POutputProto
	111, // 138: cv.runtime.proto.CVRuntimeService.SetTargetingState:output_type -> cv.runtime.proto.SetTargetingStateResponse
	108, // 139: cv.runtime.proto.CVRuntimeService.P2PBufferringBurstCapture:output_type -> cv.runtime.proto.P2PBufferringBurstCaptureResponse
	113, // 140: cv.runtime.proto.CVRuntimeService.GetNextFocusMetric:output_type -> cv.runtime.proto.GetNextFocusMetricResponse
	115, // 141: cv.runtime.proto.CVRuntimeService.RemoveDataDir:output_type -> cv.runtime.proto.RemoveDataDirResponse
	78,  // 142: cv.runtime.proto.CVRuntimeService.GetLastNImages:output_type -> cv.runtime.proto.ImageAndMetadataResponse
	117, // 143: cv.runtime.proto.CVRuntimeService.GetComputeCapabilities:output_type -> cv.runtime.proto.ComputeCapabilitiesResponse
	118, // 144: cv.runtime.proto.CVRuntimeService.GetSupportedTensorRTVersions:output_type -> cv.runtime.proto.SupportedTensorRTVersionsResponse
	119, // 145: cv.runtime.proto.CVRuntimeService.ReloadCategoryCollection:output_type -> cv.runtime.proto.Empty
	122, // 146: cv.runtime.proto.CVRuntimeService.GetCategoryCollection:output_type -> cv.runtime.proto.GetCategoryCollectionResponse
	94,  // 147: cv.runtime.proto.CVRuntimeService.GetErrorState:output_type -> cv.runtime.proto.GetErrorStateResponse
	125, // 148: cv.runtime.proto.CVRuntimeService.SnapshotPredictImages:output_type -> cv.runtime.proto.SnapshotPredictImagesResponse
	127, // 149: cv.runtime.proto.CVRuntimeService.GetChipForPredictImage:output_type -> cv.runtime.proto.GetChipForPredictImageResponse
	96,  // [96:150] is the sub-list for method output_type
	42,  // [42:96] is the sub-list for method input_type
	42,  // [42:42] is the sub-list for extension type_name
	42,  // [42:42] is the sub-list for extension extendee
	0,   // [0:42] is the sub-list for field type_name
}

func init() { file_cv_runtime_proto_cv_runtime_proto_init() }
func file_cv_runtime_proto_cv_runtime_proto_init() {
	if File_cv_runtime_proto_cv_runtime_proto != nil {
		return
	}
	file_lib_common_camera_proto_camera_proto_init()
	file_proto_cv_cv_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetSafetyZone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetP2PContextRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetP2PContextResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraDimensionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraDimensionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartP2PDataCaptureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PointDetectionCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SegmentationDetectionCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedDetectionCriteriaSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDeepweedDetectionCriteriaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDeepweedDetectionCriteriaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedDetectionCriteriaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedDetectionCriteriaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedSupportedCategoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedSupportedCategoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedIndexToCategoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedIndexToCategoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPredictCamMatrixRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPredictCamDistortionCoefficientsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCameraSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCameraSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAutoWhitebalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAutoWhitebalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartBurstRecordFramesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartBurstRecordFramesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopBurstRecordFramesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopBurstRecordFramesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2POutputProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextP2POutputRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetConnectorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetConnectorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeTiming); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTimingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTimingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadAndQueueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadAndQueueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetImageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnsetImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnsetImageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelPathsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelPathsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraTemperaturesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraTemperature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraTemperaturesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeoLLA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeoECEF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetGPSLocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetGPSLocationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetImplementStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetImplementStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetImageScoreRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetImageScoreResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScoreQueueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScoreObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScoreQueueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMaxImageScoreRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMaxImageScoreResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMaxScoredImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestP2PImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetImageNearTimestampRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChipImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlushQueuesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlushQueuesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageAndMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryPrediction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChipPrediction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChipImageAndMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChipQueueInformationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChipQueueInformationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PImageAndMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLightweightBurstRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLightweightBurstRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBootedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBootedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReadyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReadyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetErrorStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedDetection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedOutputByTimestampRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommendedStrobeSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommendedStrobeSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartP2PBufferringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartP2PBufferringResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopP2PBufferringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopP2PBufferringResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PCaptureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PCaptureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PBufferingBurstPredictMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PBufferringBurstCaptureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PBufferringBurstCaptureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextDeepweedOutputRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTargetingStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[107].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTargetingStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[108].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextFocusMetricRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[109].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextFocusMetricResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[110].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveDataDirRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[111].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveDataDirResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[112].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LastNImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[113].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComputeCapabilitiesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[114].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SupportedTensorRTVersionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[115].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[116].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScoreQueueAndCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[117].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListScoreQueuesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[118].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryCollectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[119].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotPredictImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[120].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PcamSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[121].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotPredictImagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[122].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChipForPredictImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cv_runtime_proto_cv_runtime_proto_msgTypes[123].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChipForPredictImageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[25].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[36].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[50].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[54].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[55].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[72].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[74].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[82].OneofWrappers = []interface{}{}
	file_cv_runtime_proto_cv_runtime_proto_msgTypes[103].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cv_runtime_proto_cv_runtime_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   130,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cv_runtime_proto_cv_runtime_proto_goTypes,
		DependencyIndexes: file_cv_runtime_proto_cv_runtime_proto_depIdxs,
		EnumInfos:         file_cv_runtime_proto_cv_runtime_proto_enumTypes,
		MessageInfos:      file_cv_runtime_proto_cv_runtime_proto_msgTypes,
	}.Build()
	File_cv_runtime_proto_cv_runtime_proto = out.File
	file_cv_runtime_proto_cv_runtime_proto_rawDesc = nil
	file_cv_runtime_proto_cv_runtime_proto_goTypes = nil
	file_cv_runtime_proto_cv_runtime_proto_depIdxs = nil
}
