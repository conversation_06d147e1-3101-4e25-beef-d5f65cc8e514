// @ts-nocheck
// @generated by protobuf-ts 2.9.1
// @generated from protobuf file "sim_UI.proto" (package "carbon.simulator_UI", syntax proto3)
// tslint:disable
import { ServiceType } from "@protobuf-ts/runtime-rpc";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { Unknown<PERSON>ield<PERSON>andler } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MESSAGE_TYPE } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
/**
 * @generated from protobuf message carbon.simulator_UI.GetVelocityRequest
 */
export interface GetVelocityRequest {
}
/**
 * @generated from protobuf message carbon.simulator_UI.GetVelocityResponse
 */
export interface GetVelocityResponse {
    /**
     * @generated from protobuf field: float velocity = 1;
     */
    velocity: number;
}
/**
 * @generated from protobuf message carbon.simulator_UI.SetVelocityRequest
 */
export interface SetVelocityRequest {
    /**
     * @generated from protobuf field: float velocity = 1;
     */
    velocity: number;
}
/**
 * @generated from protobuf message carbon.simulator_UI.SetVelocityResponse
 */
export interface SetVelocityResponse {
}
/**
 * @generated from protobuf message carbon.simulator_UI.GetPresetsRequest
 */
export interface GetPresetsRequest {
}
/**
 * [parents..., leaf], value
 *
 * @generated from protobuf message carbon.simulator_UI.PresetSetting
 */
export interface PresetSetting {
    /**
     * @generated from protobuf field: repeated string path = 1;
     */
    path: string[];
    /**
     * @generated from protobuf field: string value = 2;
     */
    value: string;
}
/**
 * [Setting, Setting, Setting...]
 *
 * @generated from protobuf message carbon.simulator_UI.GetPresetsResponse
 */
export interface GetPresetsResponse {
    /**
     * @generated from protobuf field: repeated carbon.simulator_UI.PresetSetting settings = 1;
     */
    settings: PresetSetting[];
}
/**
 * @generated from protobuf message carbon.simulator_UI.SetPresetRequest
 */
export interface SetPresetRequest {
    /**
     * @generated from protobuf field: carbon.simulator_UI.PresetSetting setting = 1;
     */
    setting?: PresetSetting;
}
/**
 * @generated from protobuf message carbon.simulator_UI.SetPresetResponse
 */
export interface SetPresetResponse {
}
/**
 * @generated from protobuf message carbon.simulator_UI.DiagnosticSettings
 */
export interface DiagnosticSettings {
    /**
     * @generated from protobuf field: string current_diagnostic = 1;
     */
    currentDiagnostic: string;
    /**
     * @generated from protobuf field: repeated string diagnostics = 2;
     */
    diagnostics: string[];
    /**
     * @generated from protobuf field: int64 row = 3;
     */
    row: bigint;
    /**
     * @generated from protobuf field: int64 ticket = 5;
     */
    ticket: bigint;
}
/**
 * @generated from protobuf message carbon.simulator_UI.GetDiagnosticSettingsRequest
 */
export interface GetDiagnosticSettingsRequest {
    /**
     * @generated from protobuf field: int64 ticket = 1;
     */
    ticket: bigint;
}
/**
 * @generated from protobuf message carbon.simulator_UI.GetDiagnosticSettingsResponse
 */
export interface GetDiagnosticSettingsResponse {
    /**
     * @generated from protobuf field: carbon.simulator_UI.DiagnosticSettings settings = 1;
     */
    settings?: DiagnosticSettings;
}
/**
 * @generated from protobuf message carbon.simulator_UI.SetDiagnosticSettingsRequest
 */
export interface SetDiagnosticSettingsRequest {
    /**
     * @generated from protobuf field: string current_diagnostic = 1;
     */
    currentDiagnostic: string;
    /**
     * @generated from protobuf field: int64 row = 2;
     */
    row: bigint;
}
/**
 * @generated from protobuf message carbon.simulator_UI.SetDiagnosticSettingsResponse
 */
export interface SetDiagnosticSettingsResponse {
}
// @generated message type with reflection information, may provide speed optimized methods
class GetVelocityRequest$Type extends MessageType<GetVelocityRequest> {
    constructor() {
        super("carbon.simulator_UI.GetVelocityRequest", []);
    }
    create(value?: PartialMessage<GetVelocityRequest>): GetVelocityRequest {
        const message = {};
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<GetVelocityRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetVelocityRequest): GetVelocityRequest {
        return target ?? this.create();
    }
    internalBinaryWrite(message: GetVelocityRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.GetVelocityRequest
 */
export const GetVelocityRequest = new GetVelocityRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetVelocityResponse$Type extends MessageType<GetVelocityResponse> {
    constructor() {
        super("carbon.simulator_UI.GetVelocityResponse", [
            { no: 1, name: "velocity", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ }
        ]);
    }
    create(value?: PartialMessage<GetVelocityResponse>): GetVelocityResponse {
        const message = { velocity: 0 };
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<GetVelocityResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetVelocityResponse): GetVelocityResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* float velocity */ 1:
                    message.velocity = reader.float();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetVelocityResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* float velocity = 1; */
        if (message.velocity !== 0)
            writer.tag(1, WireType.Bit32).float(message.velocity);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.GetVelocityResponse
 */
export const GetVelocityResponse = new GetVelocityResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SetVelocityRequest$Type extends MessageType<SetVelocityRequest> {
    constructor() {
        super("carbon.simulator_UI.SetVelocityRequest", [
            { no: 1, name: "velocity", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ }
        ]);
    }
    create(value?: PartialMessage<SetVelocityRequest>): SetVelocityRequest {
        const message = { velocity: 0 };
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<SetVelocityRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SetVelocityRequest): SetVelocityRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* float velocity */ 1:
                    message.velocity = reader.float();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SetVelocityRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* float velocity = 1; */
        if (message.velocity !== 0)
            writer.tag(1, WireType.Bit32).float(message.velocity);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.SetVelocityRequest
 */
export const SetVelocityRequest = new SetVelocityRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SetVelocityResponse$Type extends MessageType<SetVelocityResponse> {
    constructor() {
        super("carbon.simulator_UI.SetVelocityResponse", []);
    }
    create(value?: PartialMessage<SetVelocityResponse>): SetVelocityResponse {
        const message = {};
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<SetVelocityResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SetVelocityResponse): SetVelocityResponse {
        return target ?? this.create();
    }
    internalBinaryWrite(message: SetVelocityResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.SetVelocityResponse
 */
export const SetVelocityResponse = new SetVelocityResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetPresetsRequest$Type extends MessageType<GetPresetsRequest> {
    constructor() {
        super("carbon.simulator_UI.GetPresetsRequest", []);
    }
    create(value?: PartialMessage<GetPresetsRequest>): GetPresetsRequest {
        const message = {};
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<GetPresetsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetPresetsRequest): GetPresetsRequest {
        return target ?? this.create();
    }
    internalBinaryWrite(message: GetPresetsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.GetPresetsRequest
 */
export const GetPresetsRequest = new GetPresetsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class PresetSetting$Type extends MessageType<PresetSetting> {
    constructor() {
        super("carbon.simulator_UI.PresetSetting", [
            { no: 1, name: "path", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "value", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<PresetSetting>): PresetSetting {
        const message = { path: [], value: "" };
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<PresetSetting>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: PresetSetting): PresetSetting {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string path */ 1:
                    message.path.push(reader.string());
                    break;
                case /* string value */ 2:
                    message.value = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: PresetSetting, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string path = 1; */
        for (let i = 0; i < message.path.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.path[i]);
        /* string value = 2; */
        if (message.value !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.value);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.PresetSetting
 */
export const PresetSetting = new PresetSetting$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetPresetsResponse$Type extends MessageType<GetPresetsResponse> {
    constructor() {
        super("carbon.simulator_UI.GetPresetsResponse", [
            { no: 1, name: "settings", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => PresetSetting }
        ]);
    }
    create(value?: PartialMessage<GetPresetsResponse>): GetPresetsResponse {
        const message = { settings: [] };
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<GetPresetsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetPresetsResponse): GetPresetsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated carbon.simulator_UI.PresetSetting settings */ 1:
                    message.settings.push(PresetSetting.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetPresetsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated carbon.simulator_UI.PresetSetting settings = 1; */
        for (let i = 0; i < message.settings.length; i++)
            PresetSetting.internalBinaryWrite(message.settings[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.GetPresetsResponse
 */
export const GetPresetsResponse = new GetPresetsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SetPresetRequest$Type extends MessageType<SetPresetRequest> {
    constructor() {
        super("carbon.simulator_UI.SetPresetRequest", [
            { no: 1, name: "setting", kind: "message", T: () => PresetSetting }
        ]);
    }
    create(value?: PartialMessage<SetPresetRequest>): SetPresetRequest {
        const message = {};
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<SetPresetRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SetPresetRequest): SetPresetRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* carbon.simulator_UI.PresetSetting setting */ 1:
                    message.setting = PresetSetting.internalBinaryRead(reader, reader.uint32(), options, message.setting);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SetPresetRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* carbon.simulator_UI.PresetSetting setting = 1; */
        if (message.setting)
            PresetSetting.internalBinaryWrite(message.setting, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.SetPresetRequest
 */
export const SetPresetRequest = new SetPresetRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SetPresetResponse$Type extends MessageType<SetPresetResponse> {
    constructor() {
        super("carbon.simulator_UI.SetPresetResponse", []);
    }
    create(value?: PartialMessage<SetPresetResponse>): SetPresetResponse {
        const message = {};
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<SetPresetResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SetPresetResponse): SetPresetResponse {
        return target ?? this.create();
    }
    internalBinaryWrite(message: SetPresetResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.SetPresetResponse
 */
export const SetPresetResponse = new SetPresetResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class DiagnosticSettings$Type extends MessageType<DiagnosticSettings> {
    constructor() {
        super("carbon.simulator_UI.DiagnosticSettings", [
            { no: 1, name: "current_diagnostic", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "diagnostics", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "row", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "ticket", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<DiagnosticSettings>): DiagnosticSettings {
        const message = { currentDiagnostic: "", diagnostics: [], row: 0n, ticket: 0n };
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<DiagnosticSettings>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: DiagnosticSettings): DiagnosticSettings {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string current_diagnostic */ 1:
                    message.currentDiagnostic = reader.string();
                    break;
                case /* repeated string diagnostics */ 2:
                    message.diagnostics.push(reader.string());
                    break;
                case /* int64 row */ 3:
                    message.row = reader.int64().toBigInt();
                    break;
                case /* int64 ticket */ 5:
                    message.ticket = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: DiagnosticSettings, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string current_diagnostic = 1; */
        if (message.currentDiagnostic !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.currentDiagnostic);
        /* repeated string diagnostics = 2; */
        for (let i = 0; i < message.diagnostics.length; i++)
            writer.tag(2, WireType.LengthDelimited).string(message.diagnostics[i]);
        /* int64 row = 3; */
        if (message.row !== 0n)
            writer.tag(3, WireType.Varint).int64(message.row);
        /* int64 ticket = 5; */
        if (message.ticket !== 0n)
            writer.tag(5, WireType.Varint).int64(message.ticket);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.DiagnosticSettings
 */
export const DiagnosticSettings = new DiagnosticSettings$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetDiagnosticSettingsRequest$Type extends MessageType<GetDiagnosticSettingsRequest> {
    constructor() {
        super("carbon.simulator_UI.GetDiagnosticSettingsRequest", [
            { no: 1, name: "ticket", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<GetDiagnosticSettingsRequest>): GetDiagnosticSettingsRequest {
        const message = { ticket: 0n };
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<GetDiagnosticSettingsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetDiagnosticSettingsRequest): GetDiagnosticSettingsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int64 ticket */ 1:
                    message.ticket = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetDiagnosticSettingsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int64 ticket = 1; */
        if (message.ticket !== 0n)
            writer.tag(1, WireType.Varint).int64(message.ticket);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.GetDiagnosticSettingsRequest
 */
export const GetDiagnosticSettingsRequest = new GetDiagnosticSettingsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetDiagnosticSettingsResponse$Type extends MessageType<GetDiagnosticSettingsResponse> {
    constructor() {
        super("carbon.simulator_UI.GetDiagnosticSettingsResponse", [
            { no: 1, name: "settings", kind: "message", T: () => DiagnosticSettings }
        ]);
    }
    create(value?: PartialMessage<GetDiagnosticSettingsResponse>): GetDiagnosticSettingsResponse {
        const message = {};
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<GetDiagnosticSettingsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetDiagnosticSettingsResponse): GetDiagnosticSettingsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* carbon.simulator_UI.DiagnosticSettings settings */ 1:
                    message.settings = DiagnosticSettings.internalBinaryRead(reader, reader.uint32(), options, message.settings);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetDiagnosticSettingsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* carbon.simulator_UI.DiagnosticSettings settings = 1; */
        if (message.settings)
            DiagnosticSettings.internalBinaryWrite(message.settings, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.GetDiagnosticSettingsResponse
 */
export const GetDiagnosticSettingsResponse = new GetDiagnosticSettingsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SetDiagnosticSettingsRequest$Type extends MessageType<SetDiagnosticSettingsRequest> {
    constructor() {
        super("carbon.simulator_UI.SetDiagnosticSettingsRequest", [
            { no: 1, name: "current_diagnostic", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "row", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<SetDiagnosticSettingsRequest>): SetDiagnosticSettingsRequest {
        const message = { currentDiagnostic: "", row: 0n };
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<SetDiagnosticSettingsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SetDiagnosticSettingsRequest): SetDiagnosticSettingsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string current_diagnostic */ 1:
                    message.currentDiagnostic = reader.string();
                    break;
                case /* int64 row */ 2:
                    message.row = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SetDiagnosticSettingsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string current_diagnostic = 1; */
        if (message.currentDiagnostic !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.currentDiagnostic);
        /* int64 row = 2; */
        if (message.row !== 0n)
            writer.tag(2, WireType.Varint).int64(message.row);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.SetDiagnosticSettingsRequest
 */
export const SetDiagnosticSettingsRequest = new SetDiagnosticSettingsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SetDiagnosticSettingsResponse$Type extends MessageType<SetDiagnosticSettingsResponse> {
    constructor() {
        super("carbon.simulator_UI.SetDiagnosticSettingsResponse", []);
    }
    create(value?: PartialMessage<SetDiagnosticSettingsResponse>): SetDiagnosticSettingsResponse {
        const message = {};
        globalThis.Object.defineProperty(message, MESSAGE_TYPE, { enumerable: false, value: this });
        if (value !== undefined)
            reflectionMergePartial<SetDiagnosticSettingsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SetDiagnosticSettingsResponse): SetDiagnosticSettingsResponse {
        return target ?? this.create();
    }
    internalBinaryWrite(message: SetDiagnosticSettingsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message carbon.simulator_UI.SetDiagnosticSettingsResponse
 */
export const SetDiagnosticSettingsResponse = new SetDiagnosticSettingsResponse$Type();
/**
 * @generated ServiceType for protobuf service carbon.simulator_UI.SimulatorUIService
 */
export const SimulatorUIService = new ServiceType("carbon.simulator_UI.SimulatorUIService", [
    { name: "GetVelocityStream", serverStreaming: true, options: {}, I: GetVelocityRequest, O: GetVelocityResponse },
    { name: "SetVelocity", options: {}, I: SetVelocityRequest, O: SetVelocityResponse },
    { name: "GetPresetStream", serverStreaming: true, options: {}, I: GetPresetsRequest, O: GetPresetsResponse },
    { name: "SetPreset", options: {}, I: SetPresetRequest, O: SetPresetResponse },
    { name: "GetDiagnosticSettings", options: {}, I: GetDiagnosticSettingsRequest, O: GetDiagnosticSettingsResponse },
    { name: "SetDiagnosticSettings", options: {}, I: SetDiagnosticSettingsRequest, O: SetDiagnosticSettingsResponse }
]);
