#include "feature_extractor.h"

#include <c10/cuda/CUDAGuard.h>
#include <fmt/format.h>
#include <google/protobuf/util/json_util.h>
#include <spdlog/spdlog.h>

#include "deeplearning/model_io/cpp/model_utils.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/resize_util.h"

namespace F = torch::nn::functional;

namespace cv::encoder {

FeatureExtractor::FeatureExtractor(model::AtomicModel model) : model_(model), gpu_id_(model_.get_gpu_id()) {
  if (!model_) {
    throw maka_error("FeatureExtractor cannot be constructed with an empty model.");
  }

  if (!model_.get_internal_metadata().means().empty()) {
    std::vector<float> means(model_.get_internal_metadata().means().begin(),
                             model_.get_internal_metadata().means().end());
    means_ = torch::tensor(means).to({torch::kCUDA, model_.get_gpu_id()});
  }

  if (!model_.get_internal_metadata().stds().empty()) {
    std::vector<float> stds(model_.get_internal_metadata().stds().begin(), model_.get_internal_metadata().stds().end());
    stds_ = torch::tensor(stds).to({torch::kCUDA, model_.get_gpu_id()});
  }

  batch_size_ = model_.get_internal_metadata().max_batch_size();
}

const ModelMetadataProto &FeatureExtractor::get_internal_metadata() { return model_.get_internal_metadata(); }

std::tuple<int, int> FeatureExtractor::get_input_size() {
  return std::make_tuple(model_.get_internal_metadata().input_size().width(),
                         model_.get_internal_metadata().input_size().height());
}

torch::Tensor FeatureExtractor::normalize(torch::Tensor image) {
  torch::Tensor stds;
  torch::Tensor means;
  image = image.permute({0, 2, 3, 1});
  stds = stds_.slice(0, 0, 3);
  means = means_.slice(0, 0, 3);
  image /= 255.0f;
  if (!model_.get_internal_metadata().means().empty()) {
    image -= means;
  }
  if (!model_.get_internal_metadata().stds().empty()) {
    image /= stds;
  }
  image = image.permute({0, 3, 1, 2});
  return image;
}

torch::Tensor FeatureExtractor::infer(torch::Tensor image) {
  auto im_tensor = image.to({torch::kCUDA, gpu_id_});
  int64_t in_batch_size = im_tensor.size(0);

  im_tensor = normalize(im_tensor);
  std::vector<torch::Tensor> tensors;
  std::vector<torch::Tensor> outputs;
  if (im_tensor.size(0) < batch_size_) {
    int64_t additional_batch_items = batch_size_ - im_tensor.size(0);
    im_tensor =
        torch::cat({im_tensor, im_tensor.slice(0, 0, 1).expand({additional_batch_items, -1, -1, -1})}, 0).contiguous();
    tensors.push_back(im_tensor);

    auto output = model_(tensors);
    return output[0].slice(0, 0, in_batch_size, 1);

  } else if (im_tensor.size(0) > batch_size_) {
    int64_t num_batches = (in_batch_size + batch_size_ - 1) / batch_size_;
    int64_t additional_batch_items = num_batches * batch_size_ - in_batch_size;

    im_tensor =
        torch::cat({im_tensor, im_tensor.slice(0, 0, 1).expand({additional_batch_items, -1, -1, -1})}, 0).contiguous();

    for (int i = 0; i < num_batches; i++) {
      tensors.clear();
      tensors.push_back(im_tensor.narrow(0, i * batch_size_, batch_size_));

      auto output = model_(tensors);
      outputs.push_back(output[0]);
    }

    auto output = torch::cat(outputs, 0);
    output = output.slice(0, 0, in_batch_size, 1);

    return output;
  } else {
    tensors.push_back(im_tensor);

    auto output = model_(tensors);
    return output[0];
  }
}

} // namespace cv::encoder