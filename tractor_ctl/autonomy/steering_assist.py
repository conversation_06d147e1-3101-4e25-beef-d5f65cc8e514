import asyncio
from typing import Optional

from tenacity import Re<PERSON><PERSON>allState, Retrying, stop_after_attempt

from lib.common.asyncio.task_master import TASK_TYPE
from lib.common.config.accessor import TypedConfigAccessor
from lib.common.logging import get_logger
from lib.common.units.speed import Speed
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.rtc.farm_zone_data import FarmAreas, TractorBoundaryState
from tractor_ctl.autonomy.autonomy import SteeringAssistAlgo
from tractor_ctl.autonomy.steering_estimator import PathCompleteException, SteeringEstimator
from tractor_ctl.event_bus import TRACTOR_EVENT_BUS
from tractor_ctl.state.global_states import ACTIVE_FARM, TRACTOR_BOUNDARY_STATE
from tractor_ctl.tractor_if import TractorIF

LOG = get_logger(__name__)


STEERING_ASSIST_STOPPED = "STEERING_ASSIST_STOPPED"


def log_steering_failure(_: RetryCallState) -> None:
    LOG.warning("Call to set steering angle failed")


class SteeringEstimatorAlgo:
    def __init__(
        self,
        tractor: TractorIF,
        algo_id: SteeringAssistAlgo,
        estimator: SteeringEstimator,
        min_speed: TypedConfigAccessor[float, Speed],
    ):
        self._tractor = tractor
        self._id = algo_id
        self._enabled = asyncio.Event()
        self._estimator = estimator
        self._min_speed = min_speed
        self._task: Optional[TASK_TYPE] = None
        TRACTOR_BOUNDARY_STATE.watch(self._on_boundary_update)

    async def enable(self) -> None:
        if self._task is not None:
            await self._task
        await self._estimator.reset()
        self._enabled.set()
        self._task = asyncio.get_event_loop().create_task(self._loop())

    async def disable(self) -> None:
        self._enabled.clear()

    async def _loop(self) -> None:
        if TRACTOR_BOUNDARY_STATE.current.valid:
            await self._do_boundary_check(TRACTOR_BOUNDARY_STATE.current, ACTIVE_FARM.current)
        while self._enabled.is_set():
            try:
                if self._min_speed.value.mph >= 0.0 and (self._tractor.get_speed() < self._min_speed.value):
                    await asyncio.sleep(0.25)
                    continue
                await self.__control()
            except BaseException as ex:
                LOG.error(f"Unknown error {ex}")

    async def __disable(self, msg: str) -> None:
        await self.disable()
        TRACTOR_EVENT_BUS.emit(STEERING_ASSIST_STOPPED, msg)

    async def __control(self) -> None:
        try:
            angle = await self._estimator.get_target_angle()
            if angle is not None:
                for attempt in Retrying(reraise=True, stop=stop_after_attempt(2), after=log_steering_failure):
                    with attempt:
                        await self._tractor.set_wheel_angle(angle)
        except PathCompleteException:
            await self.__disable("Requested steering path complete")
            LOG.info(f"{self._id.name} autonomy completed successfully")
        except RetryableMakaDeviceException:
            await self.__disable("Failed to communicate with hardware")
            LOG.exception("Failed to send message to tractor board.")
        except Exception:
            await self.__disable("Failed to complete requested steering path")
            LOG.exception(f"{self._id.name} autonomy failed.")

    @property
    def algo_id(self) -> SteeringAssistAlgo:
        return self._id

    async def _on_boundary_update(self, state: TractorBoundaryState) -> None:
        if not self._enabled.is_set():
            return
        await self._do_boundary_check(state, ACTIVE_FARM.current)

    async def _do_boundary_check(self, state: TractorBoundaryState, farm: FarmAreas) -> None:
        # TODO handle more cases later
        if state.touches(farm.private_road):
            await self.__disable("Requested steering path stopped as we have entered a private road")
        if self._id == SteeringAssistAlgo.CV_FURROW_FOLLOW:
            if state.inside.isdisjoint(farm.field_area):
                await self.__disable("Furrow following has stopped as we are no longer fully within a field")
        if self._id == SteeringAssistAlgo.GPS_HEADING:
            if state.inside.isdisjoint(farm.field_area):
                await self.__disable("GPS heading following has stopped as we are no longer fully within a field")
