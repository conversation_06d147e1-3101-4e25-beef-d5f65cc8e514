// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: recorder/proto/recorder.proto

package recorder

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeepweedDetection_HitClass int32

const (
	DeepweedDetection_WEED DeepweedDetection_HitClass = 0
	DeepweedDetection_CROP DeepweedDetection_HitClass = 1
)

// Enum value maps for DeepweedDetection_HitClass.
var (
	DeepweedDetection_HitClass_name = map[int32]string{
		0: "WEED",
		1: "CROP",
	}
	DeepweedDetection_HitClass_value = map[string]int32{
		"WEED": 0,
		"CROP": 1,
	}
)

func (x DeepweedDetection_HitClass) Enum() *DeepweedDetection_HitClass {
	p := new(DeepweedDetection_HitClass)
	*p = x
	return p
}

func (x DeepweedDetection_HitClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeepweedDetection_HitClass) Descriptor() protoreflect.EnumDescriptor {
	return file_recorder_proto_recorder_proto_enumTypes[0].Descriptor()
}

func (DeepweedDetection_HitClass) Type() protoreflect.EnumType {
	return &file_recorder_proto_recorder_proto_enumTypes[0]
}

func (x DeepweedDetection_HitClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeepweedDetection_HitClass.Descriptor instead.
func (DeepweedDetection_HitClass) EnumDescriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{1, 0}
}

type DetectionClass struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Class string  `protobuf:"bytes,1,opt,name=class,proto3" json:"class,omitempty"`
	Score float32 `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *DetectionClass) Reset() {
	*x = DetectionClass{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectionClass) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionClass) ProtoMessage() {}

func (x *DetectionClass) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionClass.ProtoReflect.Descriptor instead.
func (*DetectionClass) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{0}
}

func (x *DetectionClass) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

func (x *DetectionClass) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type DeepweedDetection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X                 float32                    `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y                 float32                    `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	Size              float32                    `protobuf:"fixed32,3,opt,name=size,proto3" json:"size,omitempty"`
	Score             float32                    `protobuf:"fixed32,4,opt,name=score,proto3" json:"score,omitempty"`
	HitClass          DeepweedDetection_HitClass `protobuf:"varint,5,opt,name=hit_class,json=hitClass,proto3,enum=recorder.DeepweedDetection_HitClass" json:"hit_class,omitempty"`
	DetectionClasses  []*DetectionClass          `protobuf:"bytes,6,rep,name=detection_classes,json=detectionClasses,proto3" json:"detection_classes,omitempty"`
	MaskIntersections [][]byte                   `protobuf:"bytes,7,rep,name=mask_intersections,json=maskIntersections,proto3" json:"mask_intersections,omitempty"`
	TrajectoryId      uint32                     `protobuf:"varint,8,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
	WeedScore         float32                    `protobuf:"fixed32,9,opt,name=weed_score,json=weedScore,proto3" json:"weed_score,omitempty"`
	CropScore         float32                    `protobuf:"fixed32,10,opt,name=crop_score,json=cropScore,proto3" json:"crop_score,omitempty"` // TODO add plant score
}

func (x *DeepweedDetection) Reset() {
	*x = DeepweedDetection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedDetection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedDetection) ProtoMessage() {}

func (x *DeepweedDetection) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedDetection.ProtoReflect.Descriptor instead.
func (*DeepweedDetection) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{1}
}

func (x *DeepweedDetection) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *DeepweedDetection) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *DeepweedDetection) GetSize() float32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DeepweedDetection) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DeepweedDetection) GetHitClass() DeepweedDetection_HitClass {
	if x != nil {
		return x.HitClass
	}
	return DeepweedDetection_WEED
}

func (x *DeepweedDetection) GetDetectionClasses() []*DetectionClass {
	if x != nil {
		return x.DetectionClasses
	}
	return nil
}

func (x *DeepweedDetection) GetMaskIntersections() [][]byte {
	if x != nil {
		return x.MaskIntersections
	}
	return nil
}

func (x *DeepweedDetection) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

func (x *DeepweedDetection) GetWeedScore() float32 {
	if x != nil {
		return x.WeedScore
	}
	return 0
}

func (x *DeepweedDetection) GetCropScore() float32 {
	if x != nil {
		return x.CropScore
	}
	return 0
}

type DeepweedPredictionFrame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs int64                `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	Detections  []*DeepweedDetection `protobuf:"bytes,2,rep,name=detections,proto3" json:"detections,omitempty"`
}

func (x *DeepweedPredictionFrame) Reset() {
	*x = DeepweedPredictionFrame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedPredictionFrame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedPredictionFrame) ProtoMessage() {}

func (x *DeepweedPredictionFrame) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedPredictionFrame.ProtoReflect.Descriptor instead.
func (*DeepweedPredictionFrame) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{2}
}

func (x *DeepweedPredictionFrame) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *DeepweedPredictionFrame) GetDetections() []*DeepweedDetection {
	if x != nil {
		return x.Detections
	}
	return nil
}

type DeepweedPredictionRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordTimestampMs uint64                   `protobuf:"varint,1,opt,name=record_timestamp_ms,json=recordTimestampMs,proto3" json:"record_timestamp_ms,omitempty"`
	Frame             *DeepweedPredictionFrame `protobuf:"bytes,2,opt,name=frame,proto3" json:"frame,omitempty"`
}

func (x *DeepweedPredictionRecord) Reset() {
	*x = DeepweedPredictionRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedPredictionRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedPredictionRecord) ProtoMessage() {}

func (x *DeepweedPredictionRecord) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedPredictionRecord.ProtoReflect.Descriptor instead.
func (*DeepweedPredictionRecord) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{3}
}

func (x *DeepweedPredictionRecord) GetRecordTimestampMs() uint64 {
	if x != nil {
		return x.RecordTimestampMs
	}
	return 0
}

func (x *DeepweedPredictionRecord) GetFrame() *DeepweedPredictionFrame {
	if x != nil {
		return x.Frame
	}
	return nil
}

type LaneHeightSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedHeight []float64 `protobuf:"fixed64,1,rep,packed,name=weed_height,json=weedHeight,proto3" json:"weed_height,omitempty"`
	CropHeight []float64 `protobuf:"fixed64,2,rep,packed,name=crop_height,json=cropHeight,proto3" json:"crop_height,omitempty"`
}

func (x *LaneHeightSnapshot) Reset() {
	*x = LaneHeightSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaneHeightSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaneHeightSnapshot) ProtoMessage() {}

func (x *LaneHeightSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaneHeightSnapshot.ProtoReflect.Descriptor instead.
func (*LaneHeightSnapshot) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{4}
}

func (x *LaneHeightSnapshot) GetWeedHeight() []float64 {
	if x != nil {
		return x.WeedHeight
	}
	return nil
}

func (x *LaneHeightSnapshot) GetCropHeight() []float64 {
	if x != nil {
		return x.CropHeight
	}
	return nil
}

type LaneHeightRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordTimestampMs uint64              `protobuf:"varint,1,opt,name=record_timestamp_ms,json=recordTimestampMs,proto3" json:"record_timestamp_ms,omitempty"`
	Snapshot          *LaneHeightSnapshot `protobuf:"bytes,2,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
}

func (x *LaneHeightRecord) Reset() {
	*x = LaneHeightRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaneHeightRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaneHeightRecord) ProtoMessage() {}

func (x *LaneHeightRecord) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaneHeightRecord.ProtoReflect.Descriptor instead.
func (*LaneHeightRecord) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{5}
}

func (x *LaneHeightRecord) GetRecordTimestampMs() uint64 {
	if x != nil {
		return x.RecordTimestampMs
	}
	return 0
}

func (x *LaneHeightRecord) GetSnapshot() *LaneHeightSnapshot {
	if x != nil {
		return x.Snapshot
	}
	return nil
}

type RotaryTicksSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampUs uint64 `protobuf:"varint,1,opt,name=timestamp_us,json=timestampUs,proto3" json:"timestamp_us,omitempty"`
	Fl          int32  `protobuf:"varint,2,opt,name=fl,proto3" json:"fl,omitempty"`
	Fr          int32  `protobuf:"varint,3,opt,name=fr,proto3" json:"fr,omitempty"`
	Bl          int32  `protobuf:"varint,4,opt,name=bl,proto3" json:"bl,omitempty"`
	Br          int32  `protobuf:"varint,5,opt,name=br,proto3" json:"br,omitempty"`
	FlEnabled   bool   `protobuf:"varint,6,opt,name=fl_enabled,json=flEnabled,proto3" json:"fl_enabled,omitempty"`
	FrEnabled   bool   `protobuf:"varint,7,opt,name=fr_enabled,json=frEnabled,proto3" json:"fr_enabled,omitempty"`
	BlEnabled   bool   `protobuf:"varint,8,opt,name=bl_enabled,json=blEnabled,proto3" json:"bl_enabled,omitempty"`
	BrEnabled   bool   `protobuf:"varint,9,opt,name=br_enabled,json=brEnabled,proto3" json:"br_enabled,omitempty"`
}

func (x *RotaryTicksSnapshot) Reset() {
	*x = RotaryTicksSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryTicksSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryTicksSnapshot) ProtoMessage() {}

func (x *RotaryTicksSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryTicksSnapshot.ProtoReflect.Descriptor instead.
func (*RotaryTicksSnapshot) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{6}
}

func (x *RotaryTicksSnapshot) GetTimestampUs() uint64 {
	if x != nil {
		return x.TimestampUs
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetFl() int32 {
	if x != nil {
		return x.Fl
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetFr() int32 {
	if x != nil {
		return x.Fr
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetBl() int32 {
	if x != nil {
		return x.Bl
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetBr() int32 {
	if x != nil {
		return x.Br
	}
	return 0
}

func (x *RotaryTicksSnapshot) GetFlEnabled() bool {
	if x != nil {
		return x.FlEnabled
	}
	return false
}

func (x *RotaryTicksSnapshot) GetFrEnabled() bool {
	if x != nil {
		return x.FrEnabled
	}
	return false
}

func (x *RotaryTicksSnapshot) GetBlEnabled() bool {
	if x != nil {
		return x.BlEnabled
	}
	return false
}

func (x *RotaryTicksSnapshot) GetBrEnabled() bool {
	if x != nil {
		return x.BrEnabled
	}
	return false
}

type RotaryTicksRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordTimestampMs uint64               `protobuf:"varint,1,opt,name=record_timestamp_ms,json=recordTimestampMs,proto3" json:"record_timestamp_ms,omitempty"`
	Snapshot          *RotaryTicksSnapshot `protobuf:"bytes,2,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
}

func (x *RotaryTicksRecord) Reset() {
	*x = RotaryTicksRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recorder_proto_recorder_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryTicksRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryTicksRecord) ProtoMessage() {}

func (x *RotaryTicksRecord) ProtoReflect() protoreflect.Message {
	mi := &file_recorder_proto_recorder_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryTicksRecord.ProtoReflect.Descriptor instead.
func (*RotaryTicksRecord) Descriptor() ([]byte, []int) {
	return file_recorder_proto_recorder_proto_rawDescGZIP(), []int{7}
}

func (x *RotaryTicksRecord) GetRecordTimestampMs() uint64 {
	if x != nil {
		return x.RecordTimestampMs
	}
	return 0
}

func (x *RotaryTicksRecord) GetSnapshot() *RotaryTicksSnapshot {
	if x != nil {
		return x.Snapshot
	}
	return nil
}

var File_recorder_proto_recorder_proto protoreflect.FileDescriptor

var file_recorder_proto_recorder_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x3c, 0x0a, 0x0e, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x95, 0x03, 0x0a, 0x11, 0x44, 0x65, 0x65, 0x70,
	0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a,
	0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x48, 0x69, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x08, 0x68, 0x69,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x45, 0x0a, 0x11, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x10, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x2d, 0x0a,
	0x12, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x11, 0x6d, 0x61, 0x73, 0x6b, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x77, 0x65, 0x65, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x63, 0x72, 0x6f, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22,
	0x1e, 0x0a, 0x08, 0x48, 0x69, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x08, 0x0a, 0x04, 0x57,
	0x45, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x52, 0x4f, 0x50, 0x10, 0x01, 0x22,
	0x79, 0x0a, 0x17, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x3b, 0x0a,
	0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a,
	0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x18, 0x44,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x22, 0x56, 0x0a, 0x12, 0x4c, 0x61, 0x6e, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x53, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0a, 0x77, 0x65, 0x65,
	0x64, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x6f, 0x70, 0x5f,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0a, 0x63, 0x72,
	0x6f, 0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x7c, 0x0a, 0x10, 0x4c, 0x61, 0x6e, 0x65,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2e, 0x0a, 0x13,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x38, 0x0a, 0x08,
	0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x6e, 0x65, 0x48, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x08, 0x73, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x22, 0xf4, 0x01, 0x0a, 0x13, 0x52, 0x6f, 0x74, 0x61, 0x72,
	0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x55,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x66,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x66,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x62,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x62,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x62, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x62, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x62, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x7e, 0x0a,
	0x11, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x4d, 0x73, 0x12, 0x39, 0x0a, 0x08, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x52, 0x08, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x42, 0x10, 0x5a,
	0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_recorder_proto_recorder_proto_rawDescOnce sync.Once
	file_recorder_proto_recorder_proto_rawDescData = file_recorder_proto_recorder_proto_rawDesc
)

func file_recorder_proto_recorder_proto_rawDescGZIP() []byte {
	file_recorder_proto_recorder_proto_rawDescOnce.Do(func() {
		file_recorder_proto_recorder_proto_rawDescData = protoimpl.X.CompressGZIP(file_recorder_proto_recorder_proto_rawDescData)
	})
	return file_recorder_proto_recorder_proto_rawDescData
}

var file_recorder_proto_recorder_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_recorder_proto_recorder_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_recorder_proto_recorder_proto_goTypes = []interface{}{
	(DeepweedDetection_HitClass)(0),  // 0: recorder.DeepweedDetection.HitClass
	(*DetectionClass)(nil),           // 1: recorder.DetectionClass
	(*DeepweedDetection)(nil),        // 2: recorder.DeepweedDetection
	(*DeepweedPredictionFrame)(nil),  // 3: recorder.DeepweedPredictionFrame
	(*DeepweedPredictionRecord)(nil), // 4: recorder.DeepweedPredictionRecord
	(*LaneHeightSnapshot)(nil),       // 5: recorder.LaneHeightSnapshot
	(*LaneHeightRecord)(nil),         // 6: recorder.LaneHeightRecord
	(*RotaryTicksSnapshot)(nil),      // 7: recorder.RotaryTicksSnapshot
	(*RotaryTicksRecord)(nil),        // 8: recorder.RotaryTicksRecord
}
var file_recorder_proto_recorder_proto_depIdxs = []int32{
	0, // 0: recorder.DeepweedDetection.hit_class:type_name -> recorder.DeepweedDetection.HitClass
	1, // 1: recorder.DeepweedDetection.detection_classes:type_name -> recorder.DetectionClass
	2, // 2: recorder.DeepweedPredictionFrame.detections:type_name -> recorder.DeepweedDetection
	3, // 3: recorder.DeepweedPredictionRecord.frame:type_name -> recorder.DeepweedPredictionFrame
	5, // 4: recorder.LaneHeightRecord.snapshot:type_name -> recorder.LaneHeightSnapshot
	7, // 5: recorder.RotaryTicksRecord.snapshot:type_name -> recorder.RotaryTicksSnapshot
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_recorder_proto_recorder_proto_init() }
func file_recorder_proto_recorder_proto_init() {
	if File_recorder_proto_recorder_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_recorder_proto_recorder_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectionClass); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedDetection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedPredictionFrame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedPredictionRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaneHeightSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaneHeightRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryTicksSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recorder_proto_recorder_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryTicksRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_recorder_proto_recorder_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_recorder_proto_recorder_proto_goTypes,
		DependencyIndexes: file_recorder_proto_recorder_proto_depIdxs,
		EnumInfos:         file_recorder_proto_recorder_proto_enumTypes,
		MessageInfos:      file_recorder_proto_recorder_proto_msgTypes,
	}.Build()
	File_recorder_proto_recorder_proto = out.File
	file_recorder_proto_recorder_proto_rawDesc = nil
	file_recorder_proto_recorder_proto_goTypes = nil
	file_recorder_proto_recorder_proto_depIdxs = nil
}
