import logging
import unittest
from threading import Lock

import numpy as np

from veselka.cv.deepweed.server import DeepweedPredictionHandler
from veselka.cv.deepweed.types import (
    ComparisonEmbeddingRequest,
    ComparisonEmbeddingResponse,
    DeepweedEmbeddingRequest,
    DeepweedEmbeddingResponse,
    DeepweedFocusScoreRequest,
    DeepweedFocusScoreResponse,
    DeepweedPredictionRequest,
    DeepweedPredictionResponse,
    DeepweedRequestType,
    FeatureExtractorEmbeddingRequest,
    FeatureExtractorEmbeddingResponse,
)

LOG = logging.getLogger(__name__)

IMAGE_S3_PATH = "s3://maka-pono/media/slayer97/2025-03-06/0c065c8e-2996-4bb1-8500-87bb5850c91e/predict_slayer97_row2_predict2_2025-03-06T20-00-44-606000Z.png"
IMAGE_ID = "1200218c-d9ab-4e48-ab46-921c3d949f79"
CROP_ID = "0c065c8e-2996-4bb1-8500-87bb5850c91e"

MODEL_S3_PATH = "s3://maka-pono/models/fut-20250228-m8rpaojjoa/trt_int8_8016_86.trt"
MODEL_ID = "fut-20250228-m8rpaojjoa"

CROP_EMBEDDINGS_MODEL_S3_PATH = "s3://maka-pono/models/4c24b4f9910757b002cac466d3d221ca/trt_int8_8016_86.trt"
CROP_EMBEDDINGS_MODEL_ID = "4c24b4f9910757b002cac466d3d221ca"
FEWSHOT_MODEL_ID = "fst-20250505-ts41qvc7la"
FEWSHOT_MODEL_S3_PATH = "s3://maka-pono/models/fst-20250505-ts41qvc7la/trt_int8_8016_86.trt"

COMPARISON_MODEL_ID = "cmp-20250221-n6u5miogir"
COMPARISON_MODEL_S3_PATH = "s3://maka-pono/models/cmp-20250221-n6u5miogir/trt_fp32.trt"

SELFSUP_MODEL_ID = "9fa74afe296f4b9f0a1677bd5df5572c"
SELFSUP_MODEL_S3_PATH = "s3://maka-pono/models/9fa74afe296f4b9f0a1677bd5df5572c/trt_int8_8016_86.trt"

SAMPLE_FREQUENCY = 0.4
FOCUS_SCORE = 0.03275036573270462

EMB_MODEL_S3_PATH = "s3://maka-pono/models/fut-20250504-64284uqrh6/trt_int8_8016_86.trt"
EMB_MODEL_ID = "fut-20250504-64284uqrh6"

LETTUCE_COORDS_1 = (519, 963)
LETTUCE_COORDS_2 = (577, 2278)
WEED_COORDS_1 = (522, 2584)


class TestDeepweedPredictionHandlerV1(unittest.TestCase):
    def test_happy_path(self) -> None:
        lock = Lock()

        prediction_handler = DeepweedPredictionHandler(lock)

        request = {
            "image": {"s3_path": IMAGE_S3_PATH, "ppi": 200},
            "model_id": MODEL_ID,
            "model_s3_path": MODEL_S3_PATH,
            "crop_threshold": 0.5,
            "weed_threshold": 0.5,
            "segmentation_threshold": 0.5,
        }

        response = prediction_handler.dl_prediction(request)
        assert len(response["points"]) == 46
        assert len(response["masks"]) == 1
        weed_count = 0
        crop_count = 0
        for point in response["points"]:
            if point["cls"] == "CROP":
                crop_count += 1
            else:
                weed_count += 1
        assert crop_count == 1
        assert weed_count == 45


class TestDeepweedPredictionHandlerV2(unittest.TestCase):
    def test_happy_path(self) -> None:

        lock = Lock()
        prediction_handler = DeepweedPredictionHandler(lock)

        request = DeepweedPredictionRequest(
            threshold=0.05,
            model_id=MODEL_ID,
            image_id=IMAGE_ID,
            model_s3_path=MODEL_S3_PATH,
            image_s3_path=IMAGE_S3_PATH,
            predict_border=False,
            ppi=200,
            message_version="v2",
        )

        response_dict = prediction_handler.dl_prediction(request.model_dump())

        response = DeepweedPredictionResponse(**response_dict)

        assert len(response.points) == 29
        assert len(response.masks) == 1

    def test_crop_embeddings_model(self) -> None:

        lock = Lock()
        prediction_handler = DeepweedPredictionHandler(lock)

        request = DeepweedPredictionRequest(
            threshold=0.05,
            model_id=CROP_EMBEDDINGS_MODEL_ID,
            image_id=IMAGE_ID,
            model_s3_path=CROP_EMBEDDINGS_MODEL_S3_PATH,
            image_s3_path=IMAGE_S3_PATH,
            predict_border=False,
            ppi=200,
            crop_id=CROP_ID,
            message_version="v2",
        )

        response_dict = prediction_handler.dl_prediction(request.model_dump())

        response = DeepweedPredictionResponse(**response_dict)

        assert len(response.points) == 33


class TestDeepweedEmbeddingHandlerV1(unittest.TestCase):
    lock = Lock()
    prediction_handler = DeepweedPredictionHandler(lock)

    l1_request = DeepweedEmbeddingRequest(
        model_id=CROP_EMBEDDINGS_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=CROP_EMBEDDINGS_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        crop_id=CROP_ID,
        x=LETTUCE_COORDS_1[0],
        y=LETTUCE_COORDS_1[1],
        ppi=200,
        message_version="v1",
    )

    l1_response_dict = prediction_handler.dl_prediction(l1_request.model_dump())
    lettuce1 = DeepweedEmbeddingResponse(**l1_response_dict)
    assert len(lettuce1.embedding) == 1024

    l2_request = DeepweedEmbeddingRequest(
        model_id=CROP_EMBEDDINGS_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=CROP_EMBEDDINGS_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        crop_id=CROP_ID,
        x=LETTUCE_COORDS_2[0],
        y=LETTUCE_COORDS_2[1],
        ppi=200,
        message_version="v1",
    )

    l2_response_dict = prediction_handler.dl_prediction(l2_request.model_dump())
    lettuce2 = DeepweedEmbeddingResponse(**l2_response_dict)
    assert len(lettuce2.embedding) == 1024

    w1_request = DeepweedEmbeddingRequest(
        model_id=CROP_EMBEDDINGS_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=CROP_EMBEDDINGS_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        crop_id=CROP_ID,
        x=WEED_COORDS_1[0],
        y=WEED_COORDS_1[1],
        ppi=200,
        message_version="v1",
    )

    w1_response_dict = prediction_handler.dl_prediction(w1_request.model_dump())
    weed1 = DeepweedEmbeddingResponse(**w1_response_dict)
    assert len(weed1.embedding) == 1024

    lettuce1_n = np.array(lettuce1.embedding)
    lettuce2_n = np.array(lettuce2.embedding)
    weed_n = np.array(weed1.embedding)

    L_L_dist = lettuce1_n @ lettuce2_n.T / (np.linalg.norm(lettuce1_n) * np.linalg.norm(lettuce2_n))
    L_W_dist = lettuce1_n @ weed_n.T / (np.linalg.norm(lettuce1_n) * np.linalg.norm(weed_n))

    LOG.info(f"Deepweed Lettuce-lettuce dist: {L_L_dist}")
    LOG.info(f"Deepweed Lettuce-weed dist: {L_W_dist}")

    assert L_L_dist > (L_W_dist + 0.5)


class TestFocusScoreV1(unittest.TestCase):
    lock = Lock()
    prediction_handler = DeepweedPredictionHandler(lock)

    request = DeepweedFocusScoreRequest(
        image_id=IMAGE_ID, image_s3_path=IMAGE_S3_PATH, sample_frequency=SAMPLE_FREQUENCY,
    )

    response_dict = prediction_handler.dl_prediction(request.model_dump())

    response = DeepweedFocusScoreResponse(**response_dict)

    assert response.score is not None
    assert abs(response.score - FOCUS_SCORE) < 1e-4


class TestFewshotEmbeddingV1(unittest.TestCase):
    lock = Lock()
    prediction_handler = DeepweedPredictionHandler(lock)

    l1_request = FeatureExtractorEmbeddingRequest(
        model_id=FEWSHOT_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=FEWSHOT_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        x=LETTUCE_COORDS_1[0],
        y=LETTUCE_COORDS_1[1],
        message_version="v1",
    )

    l1_response_dict = prediction_handler.dl_prediction(l1_request.model_dump())
    lettuce1 = FeatureExtractorEmbeddingResponse(**l1_response_dict)
    assert len(lettuce1.embedding) == 2048

    l2_request = FeatureExtractorEmbeddingRequest(
        model_id=FEWSHOT_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=FEWSHOT_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        x=LETTUCE_COORDS_2[0],
        y=LETTUCE_COORDS_2[1],
        message_version="v1",
    )

    l2_request_dict = prediction_handler.dl_prediction(l2_request.model_dump())
    lettuce2 = FeatureExtractorEmbeddingResponse(**l2_request_dict)
    assert len(lettuce2.embedding) == 2048

    w1_request = FeatureExtractorEmbeddingRequest(
        model_id=FEWSHOT_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=FEWSHOT_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        x=WEED_COORDS_1[0],
        y=WEED_COORDS_1[1],
        message_version="v1",
    )

    w1_request_dict = prediction_handler.dl_prediction(w1_request.model_dump())
    weed1 = FeatureExtractorEmbeddingResponse(**w1_request_dict)
    assert len(weed1.embedding) == 2048

    lettuce1_n = np.array(lettuce1.embedding)
    lettuce2_n = np.array(lettuce2.embedding)
    weed_n = np.array(weed1.embedding)

    L_L_dist = lettuce1_n @ lettuce2_n.T / (np.linalg.norm(lettuce1_n) * np.linalg.norm(lettuce2_n))
    L_W_dist = lettuce1_n @ weed_n.T / (np.linalg.norm(lettuce1_n) * np.linalg.norm(weed_n))

    LOG.info(f"Fewshot Lettuce-lettuce dist: {L_L_dist}")
    LOG.info(f"Fewshot Lettuce-weed dist: {L_W_dist}")

    assert L_L_dist > L_W_dist


class TestComparisonEmbeddingV1(unittest.TestCase):
    lock = Lock()
    prediction_handler = DeepweedPredictionHandler(lock)

    l1_request = ComparisonEmbeddingRequest(
        model_id=COMPARISON_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=COMPARISON_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        x=LETTUCE_COORDS_1[0],
        y=LETTUCE_COORDS_1[1],
        message_version="v1",
    )

    l1_response_dict = prediction_handler.dl_prediction(l1_request.model_dump())
    lettuce1 = ComparisonEmbeddingResponse(**l1_response_dict)
    assert len(lettuce1.embedding) == 4096

    l2_request = ComparisonEmbeddingRequest(
        model_id=COMPARISON_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=COMPARISON_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        x=LETTUCE_COORDS_2[0],
        y=LETTUCE_COORDS_2[1],
        message_version="v1",
    )

    l2_request_dict = prediction_handler.dl_prediction(l2_request.model_dump())
    lettuce2 = ComparisonEmbeddingResponse(**l2_request_dict)
    assert len(lettuce2.embedding) == 4096

    w1_request = ComparisonEmbeddingRequest(
        model_id=COMPARISON_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=COMPARISON_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        x=WEED_COORDS_1[0],
        y=WEED_COORDS_1[1],
        message_version="v1",
    )

    w1_request_dict = prediction_handler.dl_prediction(w1_request.model_dump())
    weed1 = ComparisonEmbeddingResponse(**w1_request_dict)
    assert len(weed1.embedding) == 4096

    lettuce1_n = np.array(lettuce1.embedding)
    lettuce2_n = np.array(lettuce2.embedding)
    weed_n = np.array(weed1.embedding)

    L_L_dist = lettuce1_n @ lettuce2_n.T / (np.linalg.norm(lettuce1_n) * np.linalg.norm(lettuce2_n))
    L_W_dist = lettuce1_n @ weed_n.T / (np.linalg.norm(lettuce1_n) * np.linalg.norm(weed_n))

    LOG.info(f"Comparison Lettuce-lettuce dist: {L_L_dist}")
    LOG.info(f"Comparison Lettuce-weed dist: {L_W_dist}")

    assert L_L_dist > (L_W_dist + 0.5)


class TestSelfSupEmbeddingV1(unittest.TestCase):
    lock = Lock()
    prediction_handler = DeepweedPredictionHandler(lock)

    l1_request = FeatureExtractorEmbeddingRequest(
        model_id=SELFSUP_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=SELFSUP_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        message_type=DeepweedRequestType.SELFSUP_EMBEDDING,
        x=LETTUCE_COORDS_1[0],
        y=LETTUCE_COORDS_1[1],
        message_version="v1",
    )

    l1_response_dict = prediction_handler.dl_prediction(l1_request.model_dump())
    lettuce1 = FeatureExtractorEmbeddingResponse(**l1_response_dict)
    assert len(lettuce1.embedding) == 2048

    l2_request = FeatureExtractorEmbeddingRequest(
        model_id=SELFSUP_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=SELFSUP_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        message_type=DeepweedRequestType.SELFSUP_EMBEDDING,
        x=LETTUCE_COORDS_2[0],
        y=LETTUCE_COORDS_2[1],
        message_version="v1",
    )

    l2_request_dict = prediction_handler.dl_prediction(l2_request.model_dump())
    lettuce2 = FeatureExtractorEmbeddingResponse(**l2_request_dict)
    assert len(lettuce2.embedding) == 2048

    w1_request = FeatureExtractorEmbeddingRequest(
        model_id=SELFSUP_MODEL_ID,
        image_id=IMAGE_ID,
        model_s3_path=SELFSUP_MODEL_S3_PATH,
        image_s3_path=IMAGE_S3_PATH,
        message_type=DeepweedRequestType.SELFSUP_EMBEDDING,
        x=WEED_COORDS_1[0],
        y=WEED_COORDS_1[1],
        message_version="v1",
    )

    w1_request_dict = prediction_handler.dl_prediction(w1_request.model_dump())
    weed1 = FeatureExtractorEmbeddingResponse(**w1_request_dict)
    assert len(weed1.embedding) == 2048

    lettuce1_n = np.array(lettuce1.embedding)
    lettuce2_n = np.array(lettuce2.embedding)
    weed_n = np.array(weed1.embedding)

    L_L_dist = lettuce1_n @ lettuce2_n.T / (np.linalg.norm(lettuce1_n) * np.linalg.norm(lettuce2_n))
    L_W_dist = lettuce1_n @ weed_n.T / (np.linalg.norm(lettuce1_n) * np.linalg.norm(weed_n))

    LOG.info(f"SelfSupLettuce-lettuce dist: {L_L_dist}")
    LOG.info(f"Selfsup Lettuce-weed dist: {L_W_dist}")

    assert L_L_dist > L_W_dist
