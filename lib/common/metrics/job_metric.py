import asyncio
from abc import abstractmethod
from typing import Dict, Optional

from lib.common.logging import get_logger

LOG = get_logger(__name__)


class JobMetric:
    def __init__(self, name: str):
        self._name = name
        self._task: Optional[asyncio.Task[None]] = None
        self._lock = asyncio.Lock()
        self._job_id: Optional[str] = None

    async def add_metrics(self, key_map: Dict[str, str]) -> None:
        async with self._lock:
            await self._do_add_metrics(key_map)

    @abstractmethod
    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        pass

    async def reload(self, job_id: Optional[str], metrics: Dict[str, str]) -> None:
        async with self._lock:
            self._job_id = job_id
            await self._do_reload(metrics)

    async def paused(self) -> bool:
        async with self._lock:
            return self._paused

    @property
    def _paused(self) -> bool:
        return self._job_id is None

    async def job_id(self) -> Optional[str]:
        async with self._lock:
            return self._job_id

    @abstractmethod
    async def _do_reload(self, metrics: Dict[str, str]) -> None:
        pass

    @abstractmethod
    async def _loop(self) -> None:
        pass

    async def _loop_catch(self) -> None:
        try:
            await self._loop()
        except Exception as e:
            LOG.warn(f"{self._name}: Failed with unhandled exception {e}")

    async def start(self) -> None:
        if self._task is None:
            self._task = asyncio.get_event_loop().create_task(self._loop_catch())

    async def stop(self) -> None:
        if self._task is not None:
            self._task.cancel()
            await self._task
            self._task = None

    def __repr__(self) -> str:
        return self.__str__()

    def __str__(self) -> str:
        return f"JobMetrics for {self._name}"
