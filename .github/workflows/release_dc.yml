name: release_dc

on:
  release:
    types: [published]

env:
  REGISTRY: ghcr.io
  GITHUB_TOKEN: ${{ secrets.CR_PAT }}

jobs:
  release_dc:
    runs-on: [self-hosted, linux, release_dc_only]
    timeout-minutes: 200
    permissions:
      actions: write
      contents: write
      packages: write
    env:
      GIT_BRANCH: ${{github.head_ref}}
      MAKA_ROBOT_DIR:  ${{github.workspace}}
      MAKA_LOG_DIR: ${{github.workspace}}/tmp
      MAKA_ROLE: all
      CARBON_HOST_BASE_DIR: ${{github.workspace}}/..
      AUTH0_TOKEN_CACHE_FILE: /data/release_auth_token.json
      AUTH0_DOMAIN: ${{ secrets.AUTH0_DOMAIN }}
      AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
      AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
      TARGET_LATEST_TAG: ""
      RELEASE_TAG_NAME: ${{ github.event.release.tag_name }}
      S3_CACHE_PROXY_SERVICE_HOST: frankenmini01.dc.carbonrobotics.com
      NUM_GPUS: 1
    steps:

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Load .env file
        uses: xom9ikk/dotenv@v2
        with:
          path: ${{github.workspace}}/../../..
      - name: Set env
        run: |
          echo "MAKA_DATA_DIR=${{env.DATA_DIR}}" >> $GITHUB_ENV
          echo "CUDA_VISIBLE_DEVICES=${{ env.CUDA_VISIBLE_DEVICES }}" >> $GITHUB_ENV
          echo "DEVICES_STR=${{ env.DEVICES_STR }}" >> $GITHUB_ENV
      - name: Set workspace permissions
        run: |
          chown -R $USER:$USER ${{ github.workspace }}

      - name: Checkout branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
          submodules: recursive
          token: ${{ secrets.CR_PAT }}

      - name: Setup environment
        run: |
          if [ -n "${{ github.head_ref }}" ]; then # a PR
            BRANCH_TAG=`echo -n ${GIT_BRANCH} | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            echo "MAKA_DOCKER_TAG=$BRANCH_TAG-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
          else
            # If release tag name is c1.3.4, BRANCH_TAG will be shasum of c1.3
            BRANCH_TAG=`echo -n $RELEASE_TAG_NAME | sed 's/\.[0-9][0-9]*$//' | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            if [[ $RELEASE_TAG_NAME = "v*" ]]; then
              RELEASE_TAG_WITHOUT_PREFIX=${RELEASE_TAG_NAME#b}
              echo "TARGET_LATEST_TAG=${RELEASE_TAG_WITHOUT_PREFIX%.*}" >> $GITHUB_ENV  # vX.Y.Z -> X.Y
            fi
          fi
          docker images --no-trunc | grep -E "(REPOSITORY|${DOCKER_TAG})"

      - name: Echo Release
        run: |
          echo "Releasing version: $RELEASE_TAG_NAME"

      - name: Echo System Version
        run: |
          python -m tools.ci.read_system_version
          export CARBON_SYSTEM_VERSION=$(python -m tools.ci.read_system_version)
          echo "Releasing for System Version: ${CARBON_SYSTEM_VERSION}"

      - name: Setup Bot Context
        run: |
          export CI_ROBOT_USERNAME=${{ secrets.CI_ROBOT_USERNAME }}
          export CI_ROBOT_PASSWORD=${{ secrets.CI_ROBOT_PASSWORD }}
          python3 ${{github.workspace}}/bot/initialize.py ci --repo ${{github.workspace}}

      - name: Build Gobot Container
        run: |
          docker buildx build -t ghcr.io/carbonrobotics/robot/gobot:latest --cache-from ghcr.io/carbonrobotics/robot/gobot:latest-amd64-cache --cache-from ghcr.io/carbonrobotics/robot/gobot:latest-arm64-cache -f services/containers/gobot/Dockerfile .

      - name: Build and push containers
        run: |
          if [[ -n "$TARGET_LATEST_TAG" ]]; then 
            ${{github.workspace}}/bot/bot release --release $RELEASE_TAG_NAME --tag $BRANCH_TAG --tag $TARGET_LATEST_TAG --id ${{ github.sha }} --version $RELEASE_TAG_NAME -ba GITHUB_TOKEN=${GITHUB_TOKEN} --container-build-retries 3 --skip-semver-check
          else
            ${{github.workspace}}/bot/bot release --release $RELEASE_TAG_NAME --tag $BRANCH_TAG --id ${{ github.sha }} --version $RELEASE_TAG_NAME -ba GITHUB_TOKEN=${GITHUB_TOKEN} --container-build-retries 3
          fi
