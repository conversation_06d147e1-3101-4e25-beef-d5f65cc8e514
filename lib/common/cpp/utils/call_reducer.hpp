#include <chrono>
#include <functional>

namespace carbon::common {
template <typename T, typename... Args>
class CallReducer {
public:
  using ActualFunc = std::function<void(Args...)>;
  CallReducer(T delta, ActualFunc func) : next_call_(std::chrono::steady_clock::now()), delta_(delta), func_(func) {}
  void operator()(Args &&... args) {
    // Note: This is not thread safe. if we want a thread safe version we can wrap this in a lock, but currently not
    // needed
    auto now = std::chrono::steady_clock::now();
    if (next_call_ <= now) {
      next_call_ = now + delta_;
      func_(std::forward<Args>(args)...);
    }
  }

private:
  std::chrono::steady_clock::time_point next_call_;
  T delta_;
  ActualFunc func_;
};
} // namespace carbon::common