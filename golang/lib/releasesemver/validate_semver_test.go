package releasesemver

import (
	"testing"
)

func TestIsValidSemver(t *testing.T) {
	tests := []struct {
		name     string
		version  string
		expected bool
	}{
		{"valid version", "1.0.0", true},
		{"valid version with v prefix", "v1.0.0", true},
		{"valid version with c prefix", "c1.0.0", true},
		{"valid version with vcv prefix", "vcv1.0.0", true},
		{"invalid version", "1.0", false},
		{"invalid version with v prefix", "v1.0", false},
		{"invalid version with c prefix", "c1.0", false},
		{"valid version with beta tag", "1.0.0-beta.1", true},
		{"valid version with daily tag", "1.0.0-daily.1", true},
		{"valid version with build metadata", "1.0.0+build", true},
		{"valid version with beta tag and build metadata", "1.0.0-beta.1+build", true},
		{"random string", "random", false},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := IsValidSemver(test.version)
			if test.expected && err != nil {
				t.Errorf("IsValidSemver(%v), Expected valid semver, got error: %v", test.version, err)
			} else if !test.expected && err == nil {
				t.Errorf("IsValidSemver(%v), Expected invalid semver, got no error", test.version)
			}
		})
	}
}
