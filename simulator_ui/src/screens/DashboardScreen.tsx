import Presets from '../components/Presets';
import Velocity from '../components/Velocity';
import ReplayDiagnostic from '../components/ReplayDiagnostic';
import { useState } from 'react';
import { Tooltip } from '@mui/material';

const DashboardScreen = (): JSX.Element => {
    const [simulationType, setSimulationType] = useState('Random Field');

    const renderRandomField = () => {
        return (
            <div className="flex items-center mt-10 text-center justify-center flex-row">
                <Velocity />
                <Presets />
            </div>
        );
    };

    const renderDiagnostic = () => {
        return (
            <div className="flex items-center mt-10 text-center justify-center flex-row">
                <ReplayDiagnostic />
            </div>
        );
    };

    return (
        <div className="flex items-center mt-10 text-center justify-center flex-col">
            <Tooltip title="Select the simulation type" placement="right">
                <select
                    value={simulationType}
                    onChange={(e) => setSimulationType(e.target.value)}
                    className="pr-2 text-right bg-transparent text-blue"
                >
                    <option value="Random Field">Random Field</option>
                    <option value="Diagnostic">Diagnostic</option>
                </select>
            </Tooltip>
            {simulationType === 'Random Field' ? renderRandomField() : renderDiagnostic()}
        </div>
    );
};

export default DashboardScreen;
