#include "weed_tracking/cpp/deduplication/deduplication.h"

#include <algorithm>
#include <forward_list>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>
#include <mutex>
#include <sstream>

namespace carbon::weed_tracking {

void calculate_specific_average_error(CandidateMatch *option, size_t max_matches, double default_score) {
  if (max_matches == 0) {
    option->error = 0.0;
    return;
  }
  double sum = 0.0;
  size_t count = 0;
  for (auto &match : option->matches) {
    if (match.valid()) {
      ++count;
      sum += static_cast<double>(match.get().second);
    }
  }
  if (count < max_matches) {
    sum += default_score * (static_cast<double>(max_matches - count));
  }
  option->error = sum / (double)max_matches;
}

void undistort_model_centroids(
    std::vector<trajectory::TrackedItemCentroid> &model_centroids, torch::Tensor d2u_mapx, torch::Tensor d2u_mapy,
    std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &plant_centroids_with_keys) {
  for (size_t j = 0; j < model_centroids.size(); j++) {
    model_centroids[j].convert_x_y_from_distortion_undistortion_maps(d2u_mapx, d2u_mapy);
    plant_centroids_with_keys.push_back(std::pair<size_t, trajectory::TrackedItemCentroid>(model_centroids[j].index(), model_centroids[j]));
  }
}

void undistort_tracked_item_centroids_at_timestamp(
    const std::vector<uint32_t> &tracked_object_ids, Tracker *tracker,
    std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam, int64_t timestamp_ms,
    std::vector<std::optional<std::tuple<float, float>>> &tracked_objects_xy,
    std::map<size_t, std::optional<std::pair<bool, ::recorder::TrackedItemCoordinates>>> &tracked_items_coordinates
    ) { // TODO Should I store this information in a better way?
      // TODO I should have a separate way to store stuff so that I can also include locations for things that are deemed outside of range
  for (auto id : tracked_object_ids) {
    auto trajectory = tracker->get_trajectory(id);
    std::optional<trajectory::TrackedItemCentroid> last_location = trajectory->get_coords_at_timestamp(timestamp_ms);
    if (!last_location.has_value()) {
      tracked_objects_xy.push_back(std::nullopt);
      tracked_items_coordinates.emplace(id, std::nullopt);
    } else {
      auto pos = std::make_tuple((double)last_location.value().get_x_mm(), (double)last_location.value().get_y_mm(),
                                 (double)last_location.value().get_z_mm());
      auto position = geo_cam->get_undistorted_px_from_abs_position(pos);
      bool in_camera = geo_cam->is_point_in_camera(pos);
      if (in_camera) {
        tracked_objects_xy.emplace_back(position);
        // tracked_objects_xyz_mm.emplace_back(std::make_tuple(last_location.value().get_x_mm(),
        //                                                                     last_location.value().get_y_mm(),
        //                                                                     last_location.value().get_z_mm()));
      } else {
        tracked_objects_xy.emplace_back(std::nullopt);
        // tracked_objects_xyz_mm.emplace_back(std::nullopt);
      }
      ::recorder::TrackedItemCoordinates coordinates;
      coordinates.set_x_mm((float)last_location.value().get_x_mm());
      coordinates.set_y_mm((float)last_location.value().get_y_mm());
      coordinates.set_z_mm((float)last_location.value().get_z_mm());
      coordinates.set_x_px((float)std::get<0>(position));
      coordinates.set_y_px((float)std::get<1>(position));
      tracked_items_coordinates[id] = std::make_pair(in_camera, coordinates);
    }
  }
}

void calc_final_matches(CandidateMatch *candidate, size_t max_matches, double dedup_radius) {
  std::unordered_map<size_t, size_t> model_to_track;
  for (size_t i = 0; i < candidate->matches.size(); ++i) {
    calc_final_matches_recurse(candidate, model_to_track, i);
  }
  calculate_specific_average_error(candidate, max_matches, 2 * dedup_radius);
}

void calc_final_matches_recurse(CandidateMatch *candidate, std::unordered_map<size_t, size_t> &model_to_track,
                                size_t i) {
  if (!candidate->matches[i].valid()) {
    return;
  }
  const auto &[candidate_match, inserted] = model_to_track.try_emplace(candidate->matches[i].get().first, i);
  if (!inserted) {
    if (candidate->matches[candidate_match->second].get().second < candidate->matches[i].get().second) {
      ++candidate->matches[i];
      calc_final_matches_recurse(candidate, model_to_track, i);
    } else {
      size_t j = candidate_match->second;
      candidate_match->second = i;
      ++candidate->matches[j];
      calc_final_matches_recurse(candidate, model_to_track, j);
    }
  }
}

template <typename IndexTypeT, typename ItemTypeT>
void shift_all_tracked_objects_by_vector_and_get_distances(
    CandidateMatch *candidate, const std::vector<std::optional<std::tuple<float, float>>> &tracked_items_xy,
    const carbon::kd_tree::KDTreeAdaptorBase<IndexTypeT, ItemTypeT> &kd_tree_centroids, size_t max_matches,
    double dedup_radius) {
  auto &model_matches = candidate->matches;
  auto dedup_radius_sqrd = dedup_radius * dedup_radius;
  for (size_t k = 0; k < tracked_items_xy.size(); k++) {
    auto &neighbor_data = model_matches[k];
    if (tracked_items_xy[k].has_value()) {
      std::vector<size_t> indices(max_neighbor_testing, 0);
      std::vector<float> distances(max_neighbor_testing, 0.0f);
      auto [tracked_x, tracked_y] = tracked_items_xy[k].value();

      auto count = kd_tree_centroids.query_nn(tracked_x + candidate->shift_x, tracked_y + candidate->shift_y,
                                              max_neighbor_testing, indices, distances);
      for (size_t i = 0; i < count; ++i) {
        if (distances[i] < dedup_radius_sqrd) {
          neighbor_data.neighbors.emplace_back(indices[i], std::sqrt(distances[i]));
        }
      }
      neighbor_data.sort();
    }
  }
  calc_final_matches(candidate, max_matches, dedup_radius);
}

OptimalMatcher::OptimalMatcher(const std::vector<std::optional<std::tuple<float, float>>> &tracked_items_xy,
                 const std::vector<trajectory::Trajectory::ID> &tracked_ids, Tracker *tracker,
                 const KDTreeAdaptor<2, nanoflann::metric_L2> &kd_tree_centroids,
                 const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &centroids_with_keys,
                 float dedup_radius)
      : tracked_items_xy_(tracked_items_xy), tracked_ids_(tracked_ids), tracker_(tracker),
        kd_tree_centroids_(kd_tree_centroids), centroids_with_keys_(centroids_with_keys), dedup_radius_(dedup_radius),
        dedup_radius_sqrd_(dedup_radius * dedup_radius), valid_(false) {
    max_matches_ = std::min(centroids_with_keys_.size(), tracked_items_xy_.size()); // Maximum possible matches
  }

void OptimalMatcher::find_optimal_matches() {
  static constexpr double min_error = 10000.0;
  std::vector<common::ThreadPool::JobID> jobs;

  for (size_t ind = 0; ind < tracked_items_xy_.size(); ++ind) {
    if (!tracked_items_xy_[ind]) {
      continue;
    }
    auto tracked_object_xy = tracked_items_xy_[ind].value();
    auto id = tracked_ids_[ind];

    auto trajectory = tracker_->get_nullable_trajectory(id);
    if (!trajectory) {
      continue;
    }
    float search_radius = trajectory->get_search_radius();

    auto [last_px_x, last_px_y] = tracked_object_xy;
    // Get all model_centroids within search radius of last location
    std::vector<std::pair<size_t, float>> points_to_check;

    size_t num_matches =
        kd_tree_centroids_.query_radius(last_px_x, last_px_y, search_radius * search_radius, points_to_check);

    for (size_t j = 0; j < num_matches; j++) {
      auto candidate_point_index = points_to_check[j].first;

      auto candidate_point = centroids_with_keys_[candidate_point_index].second;
      float projected_to_centroid_x = candidate_point.get_x() - last_px_x;
      float projected_to_centroid_y = candidate_point.get_y() - last_px_y;
      CandidateMatch *candidate =
          &options_.emplace_front(tracked_ids_.size(), projected_to_centroid_x, projected_to_centroid_y, candidate_point.index(), id);

      auto job = tracker_->add_job([this, candidate]() {
        shift_all_tracked_objects_by_vector_and_get_distances<size_t, carbon::trajectory::TrackedItemCentroid>(
            candidate, tracked_items_xy_, kd_tree_centroids_, max_matches_, dedup_radius_);
      });
      jobs.push_back(job);
    }
  }
  for (auto &job : jobs) {
    tracker_->await_job(job);
  }
  if (options_.empty()) {
    return;
  }
  best_ = options_.begin();
  for (auto it = options_.begin(); it != options_.end(); ++it) {
    if (it->error < best_->error) {
      best_ = it;
    }
  }
  if (best_->error < min_error) {
    valid_ = true;
  }
}

bool OptimalMatcher::valid() const { return valid_; }
const CandidateMatch &OptimalMatcher::best() const { return *best_; }

void find_deduped_and_undeduped_tracked_objects(
    const std::vector<uint32_t> &tracked_object_ids,
    const std::vector<std::optional<std::tuple<float, float>>> tracked_object_xys,
    const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &model_centroids_with_keys,
    const OptimalMatcher &matcher, Tracker *tracker, int64_t timestamp_ms,
    std::set<size_t> &matched_model_prediction_keys,
    std::map<uint32_t, std::pair<size_t, trajectory::TrackedItemCentroid>> &deduplicated_tracked_items,
    std::map<uint32_t, trajectory::TrackedItemCentroid> &undeduplicated_tracked_items) {

  size_t unmatched_inbounds_count = 0;
  for (size_t j = 0; j < tracked_object_ids.size(); ++j) {
    auto trajectory = tracker->get_trajectory(tracked_object_ids[j]);
    std::optional<trajectory::TrackedItemCentroid> last_location = trajectory->get_coords_at_timestamp(timestamp_ms);
    if (!last_location.has_value()) {
      continue;
    }
    if (matcher.valid() && matcher.best().matches[j].valid()) {
      auto &model_centroid_with_key = model_centroids_with_keys[matcher.best().matches[j].get().first];
      deduplicated_tracked_items.emplace(tracked_object_ids[j], model_centroid_with_key);
      matched_model_prediction_keys.insert(model_centroid_with_key.first);
    } else {
      if (tracked_object_xys[j].has_value()) {
        // count the tracked objects that we previously determined to be inbounds
        ++unmatched_inbounds_count;
      }
      undeduplicated_tracked_items.emplace(tracked_object_ids[j], last_location.value());
    }
  }
}

std::tuple<std::map<uint32_t, std::pair<size_t, trajectory::TrackedItemCentroid>>,
           std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>>,
           std::map<uint32_t, trajectory::TrackedItemCentroid>, bool>
deduplicate_ransac(Tracker *tracker, std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam,
                   int64_t timestamp_ms, float plant_dedup_radius,
                   const KDTreeAdaptor<2, nanoflann::metric_L2> &kd_tree_plant_centroids,
                   const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &plant_centroids_with_keys,
                   std::vector<trajectory::Trajectory::ID> tracked_plant_ids,
                   std::vector<std::optional<std::tuple<float, float>>> &tracked_plant_xy,
                   ::recorder::CandidateShift &best_candidate_match,
                   std::vector<::recorder::CandidateShift> &all_candidate_matches,
                   std::map<size_t, std::optional<std::pair<bool, ::recorder::TrackedItemCoordinates>>> &tracked_items_coordinates
                  ) {
  std::set<size_t> matched_plant_prediction_keys;

  std::map<uint32_t, std::pair<size_t, trajectory::TrackedItemCentroid>> deduplicated_tracked_items;
  std::map<uint32_t, trajectory::TrackedItemCentroid> undeduplicated_tracked_items;

  std::set<uint32_t> claimed_centroid_keys;

  undistort_tracked_item_centroids_at_timestamp(tracked_plant_ids, tracker, geo_cam, timestamp_ms, tracked_plant_xy, tracked_items_coordinates);

  OptimalMatcher plant_matcher(tracked_plant_xy, tracked_plant_ids, tracker, kd_tree_plant_centroids,
                               plant_centroids_with_keys, plant_dedup_radius);

  plant_matcher.find_optimal_matches();

  auto options = plant_matcher.get_options();

  if (plant_matcher.valid()) {
    auto best = plant_matcher.best();

    best_candidate_match.set_x_shift(best.shift_x);
    best_candidate_match.set_y_shift(best.shift_y);
    best_candidate_match.set_centroid_id((uint32_t)best.centroid_id);
    best_candidate_match.set_trajectory_id(best.trajectory_id);

    for (auto &option : options) {
      ::recorder::CandidateShift candidate;
      candidate.set_x_shift(option.shift_x);
      candidate.set_y_shift(option.shift_y);
      candidate.set_centroid_id((uint32_t)option.centroid_id);
      candidate.set_trajectory_id(option.trajectory_id);
      all_candidate_matches.push_back(candidate);
    }
  }

  find_deduped_and_undeduped_tracked_objects(tracked_plant_ids, tracked_plant_xy, plant_centroids_with_keys,
                                             plant_matcher, tracker, timestamp_ms, matched_plant_prediction_keys,
                                             deduplicated_tracked_items, undeduplicated_tracked_items);

  std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> unmatched_model_predictions;

  for (auto model_centroid_with_key : plant_centroids_with_keys) {
    if (matched_plant_prediction_keys.count(model_centroid_with_key.first) == 0) {
      unmatched_model_predictions.push_back(model_centroid_with_key);
    }
  }

  return std::tuple<std::map<uint32_t, std::pair<size_t, trajectory::TrackedItemCentroid>>,
                    std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>>,
                    std::map<uint32_t, trajectory::TrackedItemCentroid>, bool>(
      deduplicated_tracked_items, unmatched_model_predictions, undeduplicated_tracked_items, plant_matcher.valid());
}

template void shift_all_tracked_objects_by_vector_and_get_distances<size_t, trajectory::TrackedItemCentroid>(
    CandidateMatch *candidate, const std::vector<std::optional<std::tuple<float, float>>> &tracked_items_xy,
    const carbon::kd_tree::KDTreeAdaptorBase<size_t, trajectory::TrackedItemCentroid> &kd_tree_centroids,
    size_t max_matches, double dedup_radius); // For functions

template void
shift_all_tracked_objects_by_vector_and_get_distances<trajectory::Trajectory::ID, trajectory::TrackedItemPosition>(
    CandidateMatch *candidate, const std::vector<std::optional<std::tuple<float, float>>> &tracked_items_xy,
    const carbon::kd_tree::KDTreeAdaptorBase<trajectory::Trajectory::ID, trajectory::TrackedItemPosition>
        &kd_tree_centroids,
    size_t max_matches, double dedup_radius);
} // namespace carbon::weed_tracking