import { GrpcWebFetchTransport } from '@protobuf-ts/grpcweb-transport';

import { SimulatorUIServiceClient } from './generated/sim_UI.client';
import { PresetSetting, DiagnosticSettings, SetDiagnosticSettingsRequest } from './generated/sim_UI';
import env from '../../env';

const transport = new GrpcWebFetchTransport({ baseUrl: env.apiURL });
const client = new SimulatorUIServiceClient(transport);

const pingInterval = 2000; // in ms

export const setVelocityAPI = async (newVelocity: number): Promise<void> => {
    await client.setVelocity({ velocity: newVelocity }).response.catch((reason) => {
        alert(
            `Error: Could not set velocity. Try refreshing or try again later. Details: ${JSON.stringify(
                reason.message,
            )}`,
        );
    });
};

export const onGetVelocityAPI = (onMessage: (newVelocity: number) => void, setLoading: (loaded: boolean) => void) => {
    const stream = client.getVelocityStream({});
    stream.responses.onError(async (reason) => {
        console.error(`Stream error getting velocity. Details: ${JSON.stringify(reason.message)}`);
        setLoading(true);
        await new Promise((_) => setTimeout(_, pingInterval));
        onGetVelocityAPI(onMessage, setLoading);
    });
    stream.responses.onMessage((message) => {
        setLoading(false);
        onMessage(message.velocity);
    });
};

export const setPresetAPI = async (newPreset: PresetSetting): Promise<void> => {
    await client.setPreset({ setting: newPreset }).response.catch((reason) => {
        alert(
            `Error: Could not set preset. Try refreshing or try again later. Details: ${JSON.stringify(
                reason.message,
            )}`,
        );
    });
};

export const onGetPresetsAPI = (onMessage: (paths: PresetSetting[]) => void, setLoading: (loaded: boolean) => void) => {
    const stream = client.getPresetStream({});
    stream.responses.onError(async (reason) => {
        console.error(`Stream error getting presets. Details: ${JSON.stringify(reason.message)}`);
        setLoading(true);
        await new Promise((_) => setTimeout(_, pingInterval));
        onGetPresetsAPI(onMessage, setLoading);
    });
    stream.responses.onMessage((message) => {
        setLoading(false);
        onMessage(message.settings);
    });
};

export const GetDiagnosticSettingsAPI = async (ticket: bigint): Promise<DiagnosticSettings> => {
    const response = await client.getDiagnosticSettings({ ticket }, { timeout: 5 * 1000 }).response;
    if (response.settings) {
        return response.settings;
    } else {
        throw new Error('Server returned empty diagnostic settings');
    }
};

export const SetDiagnosticSettingsAPI = async (newDiagnosticSettings: SetDiagnosticSettingsRequest) => {
    await client.setDiagnosticSettings(newDiagnosticSettings).response.catch((reason) => {
        alert(
            `Error: Could not set diagnostic settings. Try refreshing or try again later. Details: ${JSON.stringify(
                reason.message,
            )}`,
        );
    });
};
