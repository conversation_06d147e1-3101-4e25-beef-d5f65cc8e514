package services

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/png"
	"io"
	"io/ioutil"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/config_service"
	"github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/recorder"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/auth"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/cv_utils"
	"github.com/carbonrobotics/robot/golang/lib/data_upload_manager"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/jlaffaye/ftp"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

const ftpRoot = "/diagnostics"
const dataDir = "/ftp/recorder/diagnostics"

type WeedingDiagnosticsService struct {
	frontend.UnimplementedWeedingDiagnosticsServiceServer
	wdState           *state.WeedingDiagnosticsState
	rows              map[int]*rows.RowClients
	redis             *redis.Client
	scanners          *state.OverallScannerState
	configClient      *config.ConfigClient
	hwClient          *hardware_manager.HardwareManagerClient
	bandingService    *BandingService
	env               *environment.Robot
	dumClient         *data_upload_manager.EmergencyClient
	uploadStates      *state.WeedingDiagnosticsUploadState
	mutex             sync.Mutex
	thinningConfState *state.ThinningConfState
}

func NewWeedingDiagnosticsService(
	grpcServer *grpc.Server,
	serveMux *http.ServeMux,
	state *state.WeedingDiagnosticsState,
	rowClients map[int]*rows.RowClients,
	redis *redis.Client,
	scanners *state.OverallScannerState,
	configClient *config.ConfigClient,
	hwClient *hardware_manager.HardwareManagerClient,
	bandingService *BandingService,
	env *environment.Robot,
	dumClient *data_upload_manager.EmergencyClient,
	uploadStates *state.WeedingDiagnosticsUploadState,
	thinningConfState *state.ThinningConfState) *WeedingDiagnosticsService {

	rowsToUse := rowClients
	if env.IsSimulator() {
		// in simulator, use only row 1
		rowsToUse = make(map[int]*rows.RowClients)
		rowsToUse[1] = rowClients[1]
	}

	s := &WeedingDiagnosticsService{
		wdState:           state,
		rows:              rowsToUse,
		redis:             redis,
		scanners:          scanners,
		configClient:      configClient,
		hwClient:          hwClient,
		bandingService:    bandingService,
		env:               env,
		dumClient:         dumClient,
		uploadStates:      uploadStates,
		thinningConfState: thinningConfState,
	}

	serveMux.Handle("/diagnostics/predict_image", http.HandlerFunc(s.HandleGetPredictImage))
	serveMux.Handle("/diagnostics/trajectory_predict_image", http.HandlerFunc(s.HandleGetTrajectoryPredictImage))
	serveMux.Handle("/diagnostics/trajectory_target_image", http.HandlerFunc(s.HandleGetTrajectoryTargetImage))
	serveMux.Handle("/diagnostics/p2p_predict_image", http.HandlerFunc(s.HandleGetP2PPredictImage))

	frontend.RegisterWeedingDiagnosticsServiceServer(grpcServer, s)
	return s
}

func isAlphaNumeric(s string) bool {
	for _, r := range s {
		if !unicode.IsLetter(r) && !unicode.IsDigit(r) && r != '-' && r != '_' && r != '.' {
			return false
		}
	}
	return true
}

func (d *WeedingDiagnosticsService) RecordWeedingDiagnostics(ctx context.Context, req *frontend.RecordWeedingDiagnosticsRequest) (*frontend.Empty, error) {
	if !isAlphaNumeric(req.Name) {
		return nil, errors.New("recording name must be alphanumeric")
	}

	os.Mkdir("/data/diagnostics", os.ModePerm)
	folder := "/data/diagnostics/" + req.Name
	if _, err := os.Stat(folder); !os.IsNotExist(err) {
		return nil, errors.New("recording " + req.Name + " already exists")
	}

	// Clean up in case of panic
	defer func() {
		if r := recover(); r != nil {
			logrus.Errorf("WeedingDiagnostics: Panic during recording start: %v", r)
			d.cleanupOnError(folder)
			panic(r)
		}
	}()

	var alreadyRecording bool
	d.wdState.WriteOnCurrent(func() {
		if d.wdState.Status == weed_tracking.RecordingStatus_RECORDING_STARTED {
			alreadyRecording = true
		} else {
			d.wdState.Status = weed_tracking.RecordingStatus_RECORDING_STARTED
			d.wdState.RecordingName = req.Name
			d.wdState.EndRecordingTsSec = uint64(time.Now().Unix()) + uint64(req.TtlSec)
		}
	})
	if alreadyRecording {
		return nil, errors.New("Recording already in progress")
	}

	for id, r := range d.rows {
		err := r.WeedTrackingClient.StartRecordingDiagnostics(req.TtlSec, req.CropImagesPerSec, req.WeedImagesPerSec)
		if err != nil {
			logrus.Errorf("WeedingDiagnostics: Error from aimbot at row %v, error: %v", id, err)
			d.cleanupOnError(folder)
			return nil, errors.New(err.Error())
		}
	}

	err := d.saveStaticRecordingData(folder)
	if err != nil {
		d.cleanupOnError(folder)
		return nil, err
	}

	go d.queryForRecordingData(folder)

	return &frontend.Empty{}, nil
}

func findNode(root *config_service.ConfigNode, name string) *config_service.ConfigNode {
	for _, node := range root.Children {
		if node.Name == name {
			return node
		}
	}
	return nil
}
func findNodePattern(root *config_service.ConfigNode, namePattern string, maxDepth int) []*config_service.ConfigNode {
	var ret []*config_service.ConfigNode
	r, _ := regexp.Compile(namePattern)
	var inner func(*config_service.ConfigNode, int)
	inner = func(curRoot *config_service.ConfigNode, curDepth int) {
		if curDepth >= maxDepth {
			return
		}
		for _, node := range curRoot.Children {
			inner(node, curDepth+1)
			if r.MatchString(node.Name) {
				ret = append(ret, node)
			}
		}
	}
	inner(root, 0)
	return ret
}

func (d *WeedingDiagnosticsService) getCropSafetyRadius(root *config_service.ConfigNode) float32 {
	aimbot := findNode(root, "aimbot")
	weed_tracking := findNode(aimbot, "weed_tracking")
	radius := findNode(weed_tracking, "crop_safety_radius_mm")
	return float32(radius.Value.GetFloatVal())
}

func (d *WeedingDiagnosticsService) getDeepweedConfig(root *config_service.ConfigNode, configName string) float32 {
	common := findNode(root, "common")
	deepweed := findNode(common, "deepweed")
	return float32(findNode(deepweed, configName).Value.GetFloatVal())
}

func (d *WeedingDiagnosticsService) getWheelDiameterConfig(root *config_service.ConfigNode, configName string) float32 {
	common := findNode(root, "common")
	encoders := findNode(common, "wheel_encoders")
	wheel := findNode(encoders, configName)
	return float32(findNode(wheel, "diameter_in").Value.GetFloatVal())
}

func (d *WeedingDiagnosticsService) saveStaticRecordingData(folder string) error {
	srd := &frontend.StaticRecordingData{
		LasersEnabled: map[string]bool{},
		RootConfig: &frontend.ConfigNodeSnapshot{
			Values:     make(map[string]string),
			ChildNodes: make(map[string]*frontend.ConfigNodeSnapshot),
		},
		RowsRecorded:             make([]int32, 0),
		RowDimensions:            make(map[uint32]*aimbot.GetDimensionsResponse),
		CropSafetyRadiusMmPerRow: map[uint32]float32{},
		RecordingTimestamp:       time.Now().UnixMilli(),
		RowCameras:               make(map[uint32]*frontend.RowCameras),
	}

	for row := range d.rows {
		srd.RowsRecorded = append(srd.RowsRecorded, int32(row))
	}

	for idx, row := range d.rows {
		srd.RowCameras[uint32(idx)] = &frontend.RowCameras{
			Cams: make(map[string]*frontend.CameraDimensions),
		}
		for _, cvClient := range row.CVRuntimeClients {
			cams, err := cvClient.GetCameraInfo()
			if err != nil {
				return err
			}
			for _, cam := range cams.CameraInfo {
				if strings.HasPrefix(cam.CamId, "predict") {
					srd.RowCameras[uint32(idx)].Cams[cam.CamId] = &frontend.CameraDimensions{
						Width:  cam.Width,
						Height: cam.Height,
					}
				}
			}
		}
	}

	d.scanners.ReadOnCurrent(func() {
		for c, scanner := range d.scanners.CamMapping {
			srd.LasersEnabled[c] = scanner.Laser.Enabled
		}
	})

	resp, err := d.configClient.GetTree()
	if err != nil {
		return err
	}
	for _, node := range resp.Node.Children {
		addConfigNode(srd.RootConfig, node)
	}

	lastRow := uint32(1)
	for row := range d.rows {
		lastRow = uint32(row)
		rowDim, err := d.bandingService.GetRowDimensions(row)
		if err != nil {
			return err
		}
		srd.RowDimensions[uint32(row)] = rowDim
	}
	gen := environment.CarbonGen(d.env.MakaGen)
	pattern := "bud"
	depth := 1
	if gen == environment.CarbonGenSlayer {
		pattern = "row([0-9]+)"
	} else if gen == environment.CarbonGenReaper {
		pattern = "row([0-9]+)"
		depth = 2
	}
	topNodes := findNodePattern(resp.Node, pattern, depth)
	if gen == environment.CarbonGenBud {
		if len(topNodes) != 1 {
			return fmt.Errorf("Failed to find top level node found %v nodes", len(topNodes))
		}
		srd.CropSafetyRadiusMmPerRow[lastRow] = d.getCropSafetyRadius(topNodes[0])
	} else {
		r, _ := regexp.Compile(pattern)
		for _, node := range topNodes {
			matches := r.FindStringSubmatch(node.Name)
			row, err := strconv.ParseUint(matches[1], 10, 32)
			if err != nil {
				return err
			}
			srd.CropSafetyRadiusMmPerRow[uint32(row)] = d.getCropSafetyRadius(node)
		}
	}

	srd.WeedPointThreshold = d.getDeepweedConfig(resp.Node, "weed_point_threshold")
	srd.CropPointThreshold = d.getDeepweedConfig(resp.Node, "crop_point_threshold")
	srd.WheelDiameterBackLeftIn = d.getWheelDiameterConfig(resp.Node, "back_left")
	srd.WheelDiameterBackRightIn = d.getWheelDiameterConfig(resp.Node, "back_right")
	srd.WheelDiameterFrontLeftIn = d.getWheelDiameterConfig(resp.Node, "front_left")
	srd.WheelDiameterFrontRightIn = d.getWheelDiameterConfig(resp.Node, "front_right")
	d.thinningConfState.ReadOnCurrent(func() {
		val, ok := d.thinningConfState.Configs[d.thinningConfState.ActiveConfig]
		if ok {
			srd.ThinningConfig = val
		}
	})

	bytes, err := proto.Marshal(srd)
	if err != nil {
		return err
	}

	err = os.Mkdir(folder, os.ModePerm)
	if err != nil {
		return errors.New("could not create directory for recording")
	}

	err = os.WriteFile(folder+"/static_data.carbon", bytes, 0644)
	if err != nil {
		return err
	}

	return nil
}

func addConfigNode(snapshot *frontend.ConfigNodeSnapshot, node *config_service.ConfigNode) {
	if node.Children == nil || len(node.Children) == 0 {
		switch node.Value.Value.(type) {
		case *config_service.ConfigValue_Int64Val:
			snapshot.Values[node.Name] = strconv.FormatInt(node.Value.GetInt64Val(), 10)
		case *config_service.ConfigValue_Uint64Val:
			snapshot.Values[node.Name] = strconv.FormatInt(int64(node.Value.GetUint64Val()), 10)
		case *config_service.ConfigValue_BoolVal:
			snapshot.Values[node.Name] = strconv.FormatBool(node.Value.GetBoolVal())
		case *config_service.ConfigValue_FloatVal:
			snapshot.Values[node.Name] = strconv.FormatFloat(node.Value.GetFloatVal(), 'f', 2, 64)
		case *config_service.ConfigValue_StringVal:
			snapshot.Values[node.Name] = node.Value.GetStringVal()
		}
	} else {
		newSnapshot := &frontend.ConfigNodeSnapshot{Values: make(map[string]string), ChildNodes: make(map[string]*frontend.ConfigNodeSnapshot)}
		snapshot.ChildNodes[node.Name] = newSnapshot
		for _, child := range node.Children {
			addConfigNode(newSnapshot, child)
		}
	}
}

func recursiveCopy(conn *ftp.ServerConn, rootLocal string) error {
	w := conn.Walk(ftpRoot)
	err := os.MkdirAll(rootLocal, os.ModePerm)
	if err != nil {
		return err
	}
	for ok := w.Next(); ok; ok = w.Next() {
		if w.Path() == ftpRoot {
			continue
		}
		localPath := filepath.Join(rootLocal, w.Path()[len(ftpRoot):])
		if w.Stat().Type == ftp.EntryTypeFolder {
			err := os.MkdirAll(localPath, os.ModePerm)
			if err != nil {
				return err
			}
		} else {
			err := func() error {
				r, err := conn.Retr(w.Path())
				if err != nil {
					return err
				}
				defer r.Close()
				outFile, err := os.Create(localPath)
				if err != nil {
					return err
				}
				defer outFile.Close()
				bufferedWriter := bufio.NewWriter(outFile)
				buf := make([]byte, 1024)
				for {
					n, err := r.Read(buf)
					_, err1 := bufferedWriter.Write(buf[:n])
					if err == io.EOF {
						break
					}
					if err != nil {
						return err
					}
					if err1 != nil {
						return err1
					}
				}
				bufferedWriter.Flush()
				return nil
			}()
			if err != nil {
				return err
			}
		}
	}
	return nil
}
func (d *WeedingDiagnosticsService) copyFromRow(folder string, rowId int, pcId int, streamHost string) error {
	conn, err := ftp.Dial(streamHost+":21", ftp.DialWithTimeout(10*time.Second))
	if err != nil {
		logrus.Errorf("WeedingDiagnostics: could not ftp-dial to row %v:%v, err=%v", rowId, pcId, err)
		return err
	}
	err = conn.Login("recorder", auth.FTPPassword)
	if err != nil {
		logrus.Errorf("WeedingDiagnostics: could not ftp-login to row %v:%v, err=%v", rowId, pcId, err)
		return err
	}
	rowDirectory := fmt.Sprintf("%v/row%v", folder, rowId)
	err = recursiveCopy(conn, rowDirectory)
	if err != nil {
		logrus.Errorf("WeedingDiagnostics: could not transfer data on row %v:%v, err=%v", rowId, pcId, err)
		return err
	}
	logrus.Infof("WeedingDiagnostics: Gathering files on row %v:%v finished", rowId, pcId)
	return nil
}

func copyFile(src, dst string) error {
	inputFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer inputFile.Close()

	outputFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer outputFile.Close()

	if _, err := io.Copy(outputFile, inputFile); err != nil {
		return err
	}
	return nil
}

func (d *WeedingDiagnosticsService) queryForRecordingData(folder string) {
	ready := make(map[int]bool)
	for i := range d.rows {
		ready[i] = false
	}
	for {
		allReady := true
		for i := range d.rows {
			allReady = allReady && ready[i]
		}

		if allReady {
			break
		}

		time.Sleep(1 * time.Second)

		for rowIdx, row := range d.rows {
			if ready[rowIdx] {
				continue
			}

			res, err := row.WeedTrackingClient.GetDiagnosticsRecordingStatus()
			if err != nil || res.Status == weed_tracking.RecordingStatus_RECORDING_FAILED || res.Status == weed_tracking.RecordingStatus_NOT_RECORDING {
				logrus.Errorf("WeedingDiagnostics: Recording failed during getRecordingStatus on row %v %v", rowIdx, res)
				d.cleanupOnError(folder)
				return
			}
			if res.Status == weed_tracking.RecordingStatus_RECORDING_FINISHED {
				logrus.Infof("WeedingDiagnostics: Recording on row %v finished, starting gathering files", rowIdx)
				for id := range row.CVRuntimeClients {
					logrus.Infof("WeedingDiagnostics: collecting images from row: %v, pc: %v", rowIdx, id)
					streamHost := row.StreamHosts[id]

					err := d.copyFromRow(folder, rowIdx, int(id), streamHost)
					if err != nil {
						d.cleanupOnError(folder)
						return
					}
				}
				ready[rowIdx] = true
			}
		}
	}
	d.wdState.WriteOnCurrent(func() {
		d.wdState.Status = weed_tracking.RecordingStatus_RECORDING_FINISHED
		d.wdState.RecordingName = ""
	})
}

func (d *WeedingDiagnosticsService) cleanupOnError(folder string) {
	for _, r := range d.rows {
		for _, cvClient := range r.CVRuntimeClients {
			cvClient.RemoveDataDir(dataDir)
		}
	}
	os.RemoveAll(folder)
	d.wdState.WriteOnCurrent(func() {
		d.wdState.Status = weed_tracking.RecordingStatus_RECORDING_FAILED
		d.wdState.RecordingName = ""
		d.wdState.EndRecordingTsSec = 0
	})
}

func (d *WeedingDiagnosticsService) GetCurrentTrajectories(ctx context.Context, req *frontend.GetCurrentTrajectoriesRequest) (*weed_tracking.DiagnosticsSnapshot, error) {
	r, ok := d.rows[int(req.RowId)]
	if !ok {
		return nil, errors.New("row doesn't exist")
	}
	return r.WeedTrackingClient.GetCurrentTrajectories()
}

func (d *WeedingDiagnosticsService) GetRecordingsList(ctx context.Context, req *frontend.GetRecordingsListRequest) (*frontend.GetRecordingsListResponse, error) {
	dir, err := ioutil.ReadDir("/data/diagnostics")
	if err != nil {
		return nil, err
	}
	currentRecording := ""
	d.wdState.ReadOnCurrent(func() {
		if d.wdState.Status == weed_tracking.RecordingStatus_RECORDING_STARTED {
			currentRecording = d.wdState.RecordingName
		}
	})

	recordings := make([]string, 0)
	for _, f := range dir {
		if f.IsDir() && currentRecording != f.Name() {
			recordings = append(recordings, f.Name())
		}
	}
	return &frontend.GetRecordingsListResponse{Name: recordings}, nil
}

func (d *WeedingDiagnosticsService) OpenRecording(ctx context.Context, req *frontend.OpenRecordingRequest) (*frontend.OpenRecordingResponse, error) {
	_, err := os.Stat("/data/diagnostics/" + req.RecordingName)
	if err != nil {
		return nil, fmt.Errorf("Recording %v doesnt exist", req.RecordingName)
	}

	r, err := d.doOpenRecording(req.RecordingName)
	if err != nil {
		return nil, err
	}

	numSnapshotsPerRow := make(map[uint32]uint32)
	for i, f := range r.RowSnapshots {
		numSnapshotsPerRow[uint32(i)] = uint32(f.NumRecords())
	}

	twiList := make(map[uint32]*frontend.TrajectoriesWithImages)
	for row, m := range r.RowTrajectoryImages {
		twi := &frontend.TrajectoriesWithImages{Ids: make([]uint32, 0)}
		twiList[row] = twi

		for id, data := range m {
			if len(data.TargetImages) > 0 {
				twi.Ids = append(twi.Ids, id)
			}
		}
		for id, data := range m {
			if len(data.TargetImages) == 0 {
				twi.Ids = append(twi.Ids, id)
			}
		}
	}

	return &frontend.OpenRecordingResponse{RecordingData: r.StaticData, NumSnapshotsPerRow: numSnapshotsPerRow, TrajectoryImagesPerRow: twiList, PredictImagesPerRow: r.RowPredictImages}, nil
}

func (d *WeedingDiagnosticsService) doOpenRecording(recordingName string) (*state.Recording, error) {
	r, err := state.OpenRecording(recordingName, d.rows)
	if err != nil {
		return nil, err
	}
	d.wdState.WriteOnCurrent(func() {
		d.wdState.OpenRecordings[recordingName] = r
	})
	return r, nil
}

func (d *WeedingDiagnosticsService) GetSnapshot(ctx context.Context, req *frontend.GetSnapshotRequest) (*frontend.GetSnapshotResponse, error) {
	var r *state.Recording
	d.wdState.ReadOnCurrent(func() {
		if rec, ok := d.wdState.OpenRecordings[req.RecordingName]; ok {
			r = rec
		}
	})
	if r == nil {
		var err error
		r, err = d.doOpenRecording(req.RecordingName)
		if err != nil {
			return nil, err
		}
	}

	if pfr, ok := r.RowSnapshots[int(req.RowId)]; ok {
		var data []byte
		var err error
		d.wdState.ReadOnCurrent(func() {
			data, err = pfr.ReadRecord(req.SnapshotNumber)
		})

		if err != nil {
			return nil, err
		}

		snapshot := weed_tracking.DiagnosticsSnapshot{}
		proto.Unmarshal(data, &snapshot)

		return &frontend.GetSnapshotResponse{Snapshot: &snapshot}, nil
	} else {
		return nil, fmt.Errorf("No data for row %v", req.RowId)
	}
}

func (d *WeedingDiagnosticsService) DeleteRecording(ctx context.Context, req *frontend.DeleteRecordingRequest) (*frontend.Empty, error) {
	currentRecording := false
	d.wdState.ReadOnCurrent(func() {
		currentRecording = d.wdState.Status == weed_tracking.RecordingStatus_RECORDING_STARTED && d.wdState.RecordingName == req.RecordingName
	})
	if currentRecording {
		return nil, errors.New("Can not delete recording that is in progress")
	}
	folder := "/data/diagnostics/" + req.RecordingName
	os.RemoveAll(folder)
	return &frontend.Empty{}, nil
}

func (d *WeedingDiagnosticsService) GetTrajectoryData(ctx context.Context, req *frontend.GetTrajectoryDataRequest) (*frontend.TrajectoryData, error) {
	var r *state.Recording
	d.wdState.ReadOnCurrent(func() {
		if rec, ok := d.wdState.OpenRecordings[req.RecordingName]; ok {
			r = rec
		}
	})
	if r == nil {
		var err error
		r, err = d.doOpenRecording(req.RecordingName)
		if err != nil {
			return nil, err
		}
	}

	if m, ok := r.RowTrajectoryImages[uint32(req.RowId)]; ok {
		if ti, ok := m[uint32(req.TrajectoryId)]; ok {
			return ti, nil
		}
	}
	return nil, fmt.Errorf("No image data found")
}

func (d *WeedingDiagnosticsService) GetTrajectoryTargetImage(req *frontend.GetTrajectoryTargetImageRequest, stream frontend.WeedingDiagnosticsService_GetTrajectoryTargetImageServer) error {
	filename := fmt.Sprintf("/data/diagnostics/%v/row%v/burst_records/%v_burst_record/%v", req.RecordingName, req.RowId, req.TrajectoryId, req.ImageName)
	return d.streamImage(filename, func(c *frontend.ImageChunk) {
		stream.Send(c)
	})
}

func (d *WeedingDiagnosticsService) streamImage(filename string, streamfunc func(*frontend.ImageChunk)) error {
	f, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("Couldn't open image file %v, error=%v", filename, err)
	}

	buf := make([]byte, 2*1024*1024)
	for {
		bytesRead, err := f.Read(buf)
		if err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("Couldn't read image file %v, error=%v", filename, err)
		}
		chunk := frontend.ImageChunk{ImageChunk: buf[:bytesRead]}
		streamfunc(&chunk)
	}

	return nil
}

func (d *WeedingDiagnosticsService) GetPredictImageMetadata(ctx context.Context, req *frontend.GetPredictImageMetadataRequest) (*frontend.GetPredictImageMetadataResponse, error) {
	var r *state.Recording
	d.wdState.ReadOnCurrent(func() {
		if rec, ok := d.wdState.OpenRecordings[req.RecordingName]; ok {
			r = rec
		}
	})
	if r == nil {
		var err error
		r, err = d.doOpenRecording(req.RecordingName)
		if err != nil {
			return nil, err
		}
	}

	filename := fmt.Sprintf("/data/diagnostics/%v/row%v/predict_images/%v.meta.json", req.RecordingName, req.RowId, req.ImageName[:len(req.ImageName)-4])
	json, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	annotations := &frontend.Annotations{}
	protojson.Unmarshal(json, annotations)

	idx1 := strings.LastIndex(filename, "_") + 1
	idx2 := len(filename) - 10 // .meta.json
	ts, _ := strconv.Atoi(filename[idx1:idx2])

	filename = fmt.Sprintf("/data/diagnostics/%v/row%v/predict_images/%v.deepweed.json", req.RecordingName, req.RowId, req.ImageName[:len(req.ImageName)-4])
	json, err = ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	deepweedOutput := &cv.DeepweedOutput{}
	protojson.Unmarshal(json, deepweedOutput)

	outputAboveThreshold := &cv.DeepweedOutput{
		Detections:         make([]*cv.DeepweedDetection, 0),
		MaskWidth:          deepweedOutput.MaskWidth,
		MaskHeight:         deepweedOutput.MaskHeight,
		MaskChannels:       deepweedOutput.MaskChannels,
		Mask:               deepweedOutput.Mask,
		MaskChannelClasses: deepweedOutput.MaskChannelClasses,
	}

	outputBelowThreshold := &cv.DeepweedOutput{
		Detections:         make([]*cv.DeepweedDetection, 0),
		MaskWidth:          deepweedOutput.MaskWidth,
		MaskHeight:         deepweedOutput.MaskHeight,
		MaskChannels:       deepweedOutput.MaskChannels,
		Mask:               deepweedOutput.Mask,
		MaskChannelClasses: deepweedOutput.MaskChannelClasses,
	}

	for _, d := range deepweedOutput.Detections {
		if d.HitClass == cv.HitClass_CROP {
			if d.Score > r.StaticData.CropPointThreshold {
				outputAboveThreshold.Detections = append(outputAboveThreshold.Detections, d)
			} else {
				outputBelowThreshold.Detections = append(outputBelowThreshold.Detections, d)
			}
		} else {
			if d.Score > r.StaticData.WeedPointThreshold {
				outputAboveThreshold.Detections = append(outputAboveThreshold.Detections, d)
			} else {
				outputBelowThreshold.Detections = append(outputBelowThreshold.Detections, d)
			}
		}
	}

	return &frontend.GetPredictImageMetadataResponse{
		Annotations:                  annotations,
		DeepweedOutput:               outputAboveThreshold,
		DeepweedOutputBelowThreshold: outputBelowThreshold,
		Ts:                           &frontend.Timestamp{TimestampMs: int64(ts)},
	}, nil
}

func (d *WeedingDiagnosticsService) GetPredictImage(req *frontend.GetPredictImageRequest, stream frontend.WeedingDiagnosticsService_GetPredictImageServer) error {
	filename := fmt.Sprintf("/data/diagnostics/%v/row%v/predict_images/%v", req.RecordingName, req.RowId, req.ImageName)
	return d.streamImage(filename, func(c *frontend.ImageChunk) {
		stream.Send(c)
	})
}

func (d *WeedingDiagnosticsService) GetTrajectoryPredictImage(req *frontend.GetTrajectoryPredictImageRequest, stream frontend.WeedingDiagnosticsService_GetTrajectoryPredictImageServer) error {
	filename := fmt.Sprintf("/data/diagnostics/%v/row%v/trajectory_predict_images/%v.png", req.RecordingName, req.RowId, req.TrajectoryId)
	return d.streamImage(filename, func(c *frontend.ImageChunk) {
		stream.Send(c)
	})
}

type PredictImageRequest struct {
	RecordingName string
	RowId         int
	ImageName     string
}

type TrajectoryPredictImageRequest struct {
	RecordingName string
	RowId         int
	TrajectoryId  uint
}

type TrajectoryTargetImageRequest struct {
	RecordingName string
	RowId         int
	TrajectoryId  uint
	ImageName     string
}

type P2PPredictImageRequest struct {
	RecordingName string
	RowId         int
	TrajectoryId  uint
	ImageName     string
}

func handleImageRequest(w http.ResponseWriter, filepath string) {
	buf, err := ioutil.ReadFile(filepath)

	if err != nil {
		logrus.WithError(err).Error("Failed to read image file")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "image/png")
	w.Write(buf)
}

func (d *WeedingDiagnosticsService) HandleGetPredictImage(w http.ResponseWriter, r *http.Request) {
	if r == nil {
		logrus.Error("Request for HandleGetPredictImage is nil")
		http.Error(w, "Request is nil", http.StatusBadRequest)
		return
	}

	var imgReq PredictImageRequest

	err := json.NewDecoder(r.Body).Decode(&imgReq)
	if err != nil {
		logrus.WithError(err).Error("Error Decoding PredictImageRequest Request")
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	filepath := fmt.Sprintf("/data/diagnostics/%v/row%v/predict_images/%v", imgReq.RecordingName, imgReq.RowId, imgReq.ImageName)

	handleImageRequest(w, filepath)
}

func (d *WeedingDiagnosticsService) HandleGetTrajectoryPredictImage(w http.ResponseWriter, r *http.Request) {
	if r == nil {
		logrus.Error("Request for HandleGetTrajectoryPredictImage is nil")
		http.Error(w, "Request is nil", http.StatusBadRequest)
		return
	}

	var imgReq TrajectoryPredictImageRequest

	err := json.NewDecoder(r.Body).Decode(&imgReq)
	if err != nil {
		logrus.WithError(err).Error("Error Decoding HandleGetTrajectoryPredictImage Request")
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	filepath := fmt.Sprintf("/data/diagnostics/%v/row%v/trajectory_predict_images/%v.png", imgReq.RecordingName, imgReq.RowId, imgReq.TrajectoryId)

	handleImageRequest(w, filepath)
}

func (d *WeedingDiagnosticsService) HandleGetTrajectoryTargetImage(w http.ResponseWriter, r *http.Request) {
	if r == nil {
		logrus.Error("Request for HandleGetTrajectoryTargetImage is nil")
		http.Error(w, "Request is nil", http.StatusBadRequest)
		return
	}

	var imgReq TrajectoryTargetImageRequest

	err := json.NewDecoder(r.Body).Decode(&imgReq)
	if err != nil {
		logrus.WithError(err).Error("Error Decoding HandleGetTrajectoryTargetImage Request")
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	filepath := fmt.Sprintf("/data/diagnostics/%v/row%v/burst_records/%v_burst_record/%v", imgReq.RecordingName, imgReq.RowId, imgReq.TrajectoryId, imgReq.ImageName)

	handleImageRequest(w, filepath)
}

func (d *WeedingDiagnosticsService) HandleGetP2PPredictImage(w http.ResponseWriter, r *http.Request) {
	if r == nil {
		logrus.Error("Request for HandleGetP2PPredictImage is nil")
		http.Error(w, "Request is nil", http.StatusBadRequest)
		return
	}

	var imgReq P2PPredictImageRequest

	err := json.NewDecoder(r.Body).Decode(&imgReq)
	if err != nil {
		logrus.WithError(err).Error("Error Decoding HandleGetP2PPredictImage Request")
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	filepath := fmt.Sprintf("/data/diagnostics/%v/row%v/p2p_predicts/%v/%v", imgReq.RecordingName, imgReq.RowId, imgReq.TrajectoryId, imgReq.ImageName)

	logrus.Infof("diag: fetching image %v", filepath)

	handleImageRequest(w, filepath)
}

func (d *WeedingDiagnosticsService) StartUpload(ctx context.Context, req *frontend.StartUploadRequest) (*frontend.Empty, error) {
	logrus.Infof("WeedingDiagnostics: received request to start upload %v", req.RecordingName)

	d.mutex.Lock()
	defer d.mutex.Unlock()

	st := frontend.UploadState_NONE
	d.uploadStates.ReadOnCurrent(func() {
		if s, ok := d.uploadStates.UploadStates[req.RecordingName]; ok {
			st = s.UploadState
		}
	})

	if st == frontend.UploadState_IN_PROGRESS {
		return nil, fmt.Errorf("WeedingDiagnostics: upload of %v is already in progress", req.RecordingName)
	}
	if st == frontend.UploadState_DONE {
		d.redis.HDel("weeding_diagnostics/uploads", req.RecordingName)
	}

	d.dumClient.StartWeedingDiagnosticsUpload(req.RecordingName)
	d.uploadStates.WriteOnCurrent(func() {
		d.uploadStates.UploadStates[req.RecordingName] = &state.WeedingDiagnosticsUploadProgress{UploadState: frontend.UploadState_IN_PROGRESS}
	})
	return &frontend.Empty{}, nil
}

func (d *WeedingDiagnosticsService) GetNextUploadState(ctx context.Context, req *frontend.GetNextUploadStateRequest) (*frontend.GetNextUploadStateResponse, error) {
	var m map[string]*state.WeedingDiagnosticsUploadProgress
	var ts int64

	if req == nil || req.Ts == nil {
		return nil, fmt.Errorf("WeedingDiagnostics: Malformed Request")
	}

	d.uploadStates.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		m = d.uploadStates.UploadStates
		ts = d.uploadStates.GetTimestampMs()
	})

	us := frontend.UploadState_NONE
	percent := 0
	if state, ok := m[req.RecordingName]; ok {
		us = state.UploadState
		percent = int(state.Percent)
	}
	return &frontend.GetNextUploadStateResponse{Ts: &frontend.Timestamp{TimestampMs: ts}, UploadState: us, PercentUploaded: uint32(percent)}, nil
}

func (d *WeedingDiagnosticsService) FindTrajectory(ctx context.Context, req *frontend.FindTrajectoryRequest) (*frontend.FindTrajectoryResponse, error) {
	var r *state.Recording
	d.wdState.ReadOnCurrent(func() {
		if rec, ok := d.wdState.OpenRecordings[req.RecordingName]; ok {
			r = rec
		}
	})
	if r == nil {
		var err error
		r, err = d.doOpenRecording(req.RecordingName)
		if err != nil {
			return nil, err
		}
	}
	if pfr, ok := r.RowSnapshots[int(req.RowId)]; ok {
		left := 0
		right := pfr.NumRecords()
		for left != right {
			sn := (left + right) / 2
			var data []byte
			var err error
			d.wdState.ReadOnCurrent(func() {
				data, err = pfr.ReadRecord(uint32(sn))
			})

			if err != nil {
				return nil, err
			}
			snapshot := weed_tracking.DiagnosticsSnapshot{}
			proto.Unmarshal(data, &snapshot)

			minId := math.MaxInt
			maxId := 0
			for _, t := range snapshot.Trajectories {
				id := int(t.Id)
				if id < minId {
					minId = id
				}
				if id > maxId {
					maxId = id
				}
				if t.Id == req.TrajectoryId {
					return &frontend.FindTrajectoryResponse{SnapshotId: uint32(sn), Trajectory: t}, nil
				}
			}
			if int(req.TrajectoryId) < minId {
				right = sn
				continue
			}
			if int(req.TrajectoryId) > maxId {
				left = sn + 1
				continue
			}
			return nil, fmt.Errorf("No trajectory found for id=%v row=%v", req.TrajectoryId, req.RowId)
		}
		return nil, fmt.Errorf("No trajectory found for id=%v row=%v", req.TrajectoryId, req.RowId)
	} else {
		return nil, fmt.Errorf("No data for row %v", req.RowId)
	}
}

func (d *WeedingDiagnosticsService) GetDeepweedPredictionsCount(ctx context.Context, req *frontend.GetDeepweedPredictionsCountRequest) (*frontend.GetDeepweedPredictionsCountResponse, error) {
	var r *state.Recording
	d.wdState.ReadOnCurrent(func() {
		if rec, ok := d.wdState.OpenRecordings[req.RecordingName]; ok {
			r = rec
		}
	})
	if r == nil {
		var err error
		r, err = d.doOpenRecording(req.RecordingName)
		if err != nil {
			return nil, err
		}
	}

	if rowPredictions, ok := r.RowDeepweedPredictions[int32(req.Row)]; ok {
		if camPredictions, ok := rowPredictions[int32(req.CamId)]; ok {
			return &frontend.GetDeepweedPredictionsCountResponse{Count: uint32(camPredictions.NumRecords())}, nil
		} else {
			return nil, fmt.Errorf("Cam %v not found", req.CamId)
		}
	} else {
		return nil, fmt.Errorf("Row %v not found", req.Row)
	}
}

func (d *WeedingDiagnosticsService) GetDeepweedPredictions(ctx context.Context, req *frontend.GetDeepweedPredictionsRequest) (*frontend.GetDeepweedPredictionsResponse, error) {
	var r *state.Recording
	d.wdState.ReadOnCurrent(func() {
		if rec, ok := d.wdState.OpenRecordings[req.RecordingName]; ok {
			r = rec
		}
	})
	if r == nil {
		var err error
		r, err = d.doOpenRecording(req.RecordingName)
		if err != nil {
			return nil, err
		}
	}
	if rowPredictions, ok := r.RowDeepweedPredictions[int32(req.Row)]; ok {
		if camPredictions, ok := rowPredictions[int32(req.CamId)]; ok {
			recordBytes, err := camPredictions.ReadRecord(req.Idx)
			if err != nil {
				return nil, err
			}
			record := recorder.DeepweedPredictionRecord{}
			proto.Unmarshal(recordBytes, &record)

			return &frontend.GetDeepweedPredictionsResponse{Predictions: &record}, nil
		} else {
			return nil, fmt.Errorf("Cam %v not found", req.CamId)
		}
	} else {
		return nil, fmt.Errorf("Row %v not found", req.Row)
	}
}

func (d *WeedingDiagnosticsService) GetRotaryTicks(ctx context.Context, req *frontend.GetRotaryTicksRequest) (*frontend.GetRotaryTicksResponse, error) {
	var r *state.Recording
	d.wdState.ReadOnCurrent(func() {
		if rec, ok := d.wdState.OpenRecordings[req.RecordingName]; ok {
			r = rec
		}
	})
	if r == nil {
		var err error
		r, err = d.doOpenRecording(req.RecordingName)
		if err != nil {
			return nil, err
		}
	}
	if rowTicks, ok := r.RowRotaryTicks[int32(req.RowId)]; ok {
		resp := &frontend.GetRotaryTicksResponse{Records: make([]*recorder.RotaryTicksRecord, 0)}
		for i := 0; i < rowTicks.NumRecords(); i++ {
			recordBytes, err := rowTicks.ReadRecord(uint32(i))
			if err != nil {
				return nil, err
			}
			record := recorder.RotaryTicksRecord{}
			proto.Unmarshal(recordBytes, &record)
			resp.Records = append(resp.Records, &record)
		}
		resp.WheelEncoderResolution, _ = d.hwClient.GetWheelEncoderResolution()
		return resp, nil
	} else {
		return nil, fmt.Errorf("Row %v not found", req.RowId)
	}
}

func (d *WeedingDiagnosticsService) SnapshotPredictImages(ctx context.Context, req *frontend.SnapshotPredictImagesRequest) (*frontend.SnapshotPredictImagesResponse, error) {
	row, ok := d.rows[int(req.RowId)]
	if !ok {
		return nil, fmt.Errorf("row %v doesn't exist", req.RowId)
	}

	var allSnapshots []*frontend.PcamSnapshot
	for _, cvClient := range row.CVRuntimeClients {
		resp, err := cvClient.SnapshotPredictImages()
		if err != nil {
			logrus.Errorf("WeedingDiagnostics: Error calling SnapshotPredictImages on row %v: %v", req.RowId, err)
			continue
		}
		for _, snapshot := range resp.Snapshots {
			allSnapshots = append(allSnapshots, &frontend.PcamSnapshot{
				PcamId:      snapshot.PcamId,
				TimestampMs: snapshot.TimestampMs,
			})
		}
	}

	return &frontend.SnapshotPredictImagesResponse{
		Snapshots: allSnapshots,
	}, nil
}

func (d *WeedingDiagnosticsService) GetChipForPredictImage(ctx context.Context, req *frontend.GetChipForPredictImageRequest) (*frontend.GetChipForPredictImageResponse, error) {
	rowClient, ok := d.rows[int(req.RowId)]
	if !ok {
		return nil, fmt.Errorf("row %v doesn't exist", req.RowId)
	}

	cvClient, err := cv_utils.GetCVClientForCamId(rowClient, req.PcamId)
	if err != nil {
		return nil, err
	}

	resp, err := cvClient.GetChipForPredictImage(req.PcamId, req.TimestampMs, req.CenterXPx, req.CenterYPx)
	if err != nil {
		return nil, fmt.Errorf("WeedingDiagnostics: Error calling GetChipForPredictImage for pcam_id %s on row %v: %v", req.PcamId, req.RowId, err)
	}

	// Convert raw RGBA bytes to PNG format
	img := &image.RGBA{
		Pix:    resp.ImageAndMetadata.Bytes,
		Stride: int(resp.ImageAndMetadata.Width) * 4, // 4 bytes per pixel (RGBA)
		Rect:   image.Rect(0, 0, int(resp.ImageAndMetadata.Width), int(resp.ImageAndMetadata.Height)),
	}
	var pngBuffer bytes.Buffer
	err = png.Encode(&pngBuffer, img)
	if err != nil {
		return nil, fmt.Errorf("WeedingDiagnostics: Error encoding PNG: %v", err)
	}

	return &frontend.GetChipForPredictImageResponse{
		ChipImage: pngBuffer.Bytes(),
	}, nil
}
