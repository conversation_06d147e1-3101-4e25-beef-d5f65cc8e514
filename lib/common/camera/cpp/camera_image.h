#pragma once

#include <optional>

#include <fmt/format.h>
#include <glm/glm.hpp>
#include <torch/torch.h>

#include "camera_settings.h"
#include "generated/golang/simulator/hardware/proto/sim/sim.pb.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/geo_data.h"
#include "lib/common/cpp/resize_util.h"

namespace F = torch::nn::functional;

namespace lib {
namespace common {
namespace camera {

struct SimulatorData {
  std::vector<carbon::simulator::Prediction> predictions;
};

struct CameraImage {
  std::string camera_id;
  int64_t timestamp_ms;
  PixelFormat pixel_format;
  std::optional<float> ppi;
  std::optional<float> focus_metric;
  GeoLLAData geo_lla_data = GeoLLAData(0.0, 0.0, 0.0, 0);
  GeoECEFData geo_ecef_data = GeoECEFData(0.0, 0.0, 0.0, 0);
  std::optional<std::map<std::string, std::vector<double>>> bedtop_profile;
  double bbh_offset_mm;
  double exposure_us;
  double gain_db;
  bool available_for_snapshot = false;
  // HxW for Bayer8 and Mono8, CxHxW image for RGB8
  torch::Tensor image;
  torch::Tensor depth;

  std::optional<SimulatorData> simulator_data;

  int get_height() const { return (int)image.size(-2); }
  int get_width() const { return (int)image.size(-1); }
  std::optional<int> get_depth_height() const {
    return depth.defined() ? std::make_optional<int>((int)depth.size(-2)) : std::nullopt;
  }
  std::optional<int> get_depth_width() const {
    return depth.defined() ? std::make_optional<int>((int)depth.size(-1)) : std::nullopt;
  }
  glm::ivec2 get_dimensions() const { return {get_width(), get_height()}; }
  std::optional<glm::ivec2> get_depth_dimensions() const {
    return depth.defined() ? std::make_optional<glm::ivec2>(glm::ivec2(depth.size(-1), depth.size(-2))) : std::nullopt;
  }
  torch::Device get_device() const { return image.device(); }

  CameraImage cuda(int device) {
    CameraImage out = *this;
    out.image = image.to({torch::kCUDA, device});
    if (depth.defined()) {
      out.depth = depth.to({torch::kCUDA, device});
    }
    return out;
  }

  CameraImage cpu() {
    CameraImage out = *this;
    out.image = image.cpu();
    if (depth.defined()) {
      out.depth = depth.cpu();
    }
    return out;
  }

  CameraImage transpose() {
    CameraImage out = *this;
    out.image = image.transpose(-1, -2);
    if (depth.defined()) {
      out.depth = depth.transpose(-1, -2);
    }
    switch (pixel_format) {
    case PixelFormat::kBayerGR8:
      out.pixel_format = PixelFormat::kBayerGB8;
      break;
    case PixelFormat::kBayerGB8:
      out.pixel_format = PixelFormat::kBayerGR8;
      break;
    default:
      break;
    }
    return out;
  }

  CameraImage pad_and_crop(int center_x, int center_y, int width, int height) {
    CameraImage out = *this;
    std::vector<int64_t> pad_vec{width / 2, width / 2, height / 2, height / 2};
    out.image = torch::nn::functional::pad(
                    image, torch::nn::functional::PadFuncOptions(pad_vec).mode(torch::kConstant).value(0))
                    .slice(-2, center_y, center_y + height)
                    .slice(-1, center_x, center_x + width);
    if (depth.defined()) {
      out.depth = torch::nn::functional::pad(
                      depth, torch::nn::functional::PadFuncOptions(pad_vec).mode(torch::kConstant).value(0))
                      .slice(-2, center_y, center_y + height)
                      .slice(-1, center_x, center_x + width);
    }
    return out;
  }

  CameraImage resize(float target_ppi) {
    if (!ppi.has_value()) {
      throw maka_error(fmt::format("Camera image for camera {} does not have ppi.", camera_id));
    }

    const float scale_factor = target_ppi / *ppi;
    CameraImage out = *this;

    out.image = interpolate(out.image, glm::round(glm::vec2(get_dimensions()) * scale_factor));
    if (depth.defined()) {
      out.depth = interpolate(out.depth, glm::round(glm::vec2(*get_depth_dimensions()) * scale_factor));
    }

    out.ppi = target_ppi;
    return out;
  }

  CameraImage rescale(float scale_factor) {
    CameraImage out = *this;

    bool unfloat = false;

    if (out.image.scalar_type() == torch::kUInt8) {
      unfloat = true;
      out.image = out.image.toType(torch::kFloat);
      out.image /= 255.0f;
    }

    out.image = interpolate(out.image.unsqueeze(0), glm::round(glm::vec2(get_dimensions()) * scale_factor)).squeeze(0);
    if (depth.defined()) {
      out.depth =
          interpolate(out.depth.unsqueeze(0), glm::round(glm::vec2(*get_depth_dimensions()) * scale_factor)).squeeze(0);
    }

    if (ppi.has_value()) {
      out.ppi = *ppi * scale_factor;
    }

    if (unfloat) {
      out.image *= 255;
      out.image = out.image.toType(torch::kUInt8);
    }
    return out;
  }

  CameraImage contiguous() {
    CameraImage out = *this;
    out.image = image.contiguous();
    if (depth.defined()) {
      out.depth = depth.contiguous();
    }
    return out;
  }
};

} // namespace camera
} // namespace common
} // namespace lib
