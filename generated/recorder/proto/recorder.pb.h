// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: recorder/proto/recorder.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_recorder_2fproto_2frecorder_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_recorder_2fproto_2frecorder_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_recorder_2fproto_2frecorder_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_recorder_2fproto_2frecorder_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[12]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_recorder_2fproto_2frecorder_2eproto;
namespace recorder {
class CandidateShift;
struct CandidateShiftDefaultTypeInternal;
extern CandidateShiftDefaultTypeInternal _CandidateShift_default_instance_;
class DeepweedDetection;
struct DeepweedDetectionDefaultTypeInternal;
extern DeepweedDetectionDefaultTypeInternal _DeepweedDetection_default_instance_;
class DeepweedPredictionFrame;
struct DeepweedPredictionFrameDefaultTypeInternal;
extern DeepweedPredictionFrameDefaultTypeInternal _DeepweedPredictionFrame_default_instance_;
class DeepweedPredictionRecord;
struct DeepweedPredictionRecordDefaultTypeInternal;
extern DeepweedPredictionRecordDefaultTypeInternal _DeepweedPredictionRecord_default_instance_;
class DetectionClass;
struct DetectionClassDefaultTypeInternal;
extern DetectionClassDefaultTypeInternal _DetectionClass_default_instance_;
class EmbeddingCategory;
struct EmbeddingCategoryDefaultTypeInternal;
extern EmbeddingCategoryDefaultTypeInternal _EmbeddingCategory_default_instance_;
class LaneHeightRecord;
struct LaneHeightRecordDefaultTypeInternal;
extern LaneHeightRecordDefaultTypeInternal _LaneHeightRecord_default_instance_;
class LaneHeightSnapshot;
struct LaneHeightSnapshotDefaultTypeInternal;
extern LaneHeightSnapshotDefaultTypeInternal _LaneHeightSnapshot_default_instance_;
class RotaryTicksRecord;
struct RotaryTicksRecordDefaultTypeInternal;
extern RotaryTicksRecordDefaultTypeInternal _RotaryTicksRecord_default_instance_;
class RotaryTicksSnapshot;
struct RotaryTicksSnapshotDefaultTypeInternal;
extern RotaryTicksSnapshotDefaultTypeInternal _RotaryTicksSnapshot_default_instance_;
class TrackedItem;
struct TrackedItemDefaultTypeInternal;
extern TrackedItemDefaultTypeInternal _TrackedItem_default_instance_;
class TrackedItemCoordinates;
struct TrackedItemCoordinatesDefaultTypeInternal;
extern TrackedItemCoordinatesDefaultTypeInternal _TrackedItemCoordinates_default_instance_;
}  // namespace recorder
PROTOBUF_NAMESPACE_OPEN
template<> ::recorder::CandidateShift* Arena::CreateMaybeMessage<::recorder::CandidateShift>(Arena*);
template<> ::recorder::DeepweedDetection* Arena::CreateMaybeMessage<::recorder::DeepweedDetection>(Arena*);
template<> ::recorder::DeepweedPredictionFrame* Arena::CreateMaybeMessage<::recorder::DeepweedPredictionFrame>(Arena*);
template<> ::recorder::DeepweedPredictionRecord* Arena::CreateMaybeMessage<::recorder::DeepweedPredictionRecord>(Arena*);
template<> ::recorder::DetectionClass* Arena::CreateMaybeMessage<::recorder::DetectionClass>(Arena*);
template<> ::recorder::EmbeddingCategory* Arena::CreateMaybeMessage<::recorder::EmbeddingCategory>(Arena*);
template<> ::recorder::LaneHeightRecord* Arena::CreateMaybeMessage<::recorder::LaneHeightRecord>(Arena*);
template<> ::recorder::LaneHeightSnapshot* Arena::CreateMaybeMessage<::recorder::LaneHeightSnapshot>(Arena*);
template<> ::recorder::RotaryTicksRecord* Arena::CreateMaybeMessage<::recorder::RotaryTicksRecord>(Arena*);
template<> ::recorder::RotaryTicksSnapshot* Arena::CreateMaybeMessage<::recorder::RotaryTicksSnapshot>(Arena*);
template<> ::recorder::TrackedItem* Arena::CreateMaybeMessage<::recorder::TrackedItem>(Arena*);
template<> ::recorder::TrackedItemCoordinates* Arena::CreateMaybeMessage<::recorder::TrackedItemCoordinates>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace recorder {

enum DeepweedDetection_HitClass : int {
  DeepweedDetection_HitClass_WEED = 0,
  DeepweedDetection_HitClass_CROP = 1,
  DeepweedDetection_HitClass_DeepweedDetection_HitClass_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DeepweedDetection_HitClass_DeepweedDetection_HitClass_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DeepweedDetection_HitClass_IsValid(int value);
constexpr DeepweedDetection_HitClass DeepweedDetection_HitClass_HitClass_MIN = DeepweedDetection_HitClass_WEED;
constexpr DeepweedDetection_HitClass DeepweedDetection_HitClass_HitClass_MAX = DeepweedDetection_HitClass_CROP;
constexpr int DeepweedDetection_HitClass_HitClass_ARRAYSIZE = DeepweedDetection_HitClass_HitClass_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DeepweedDetection_HitClass_descriptor();
template<typename T>
inline const std::string& DeepweedDetection_HitClass_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DeepweedDetection_HitClass>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DeepweedDetection_HitClass_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DeepweedDetection_HitClass_descriptor(), enum_t_value);
}
inline bool DeepweedDetection_HitClass_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DeepweedDetection_HitClass* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DeepweedDetection_HitClass>(
    DeepweedDetection_HitClass_descriptor(), name, value);
}
// ===================================================================

class DetectionClass final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.DetectionClass) */ {
 public:
  inline DetectionClass() : DetectionClass(nullptr) {}
  ~DetectionClass() override;
  explicit constexpr DetectionClass(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DetectionClass(const DetectionClass& from);
  DetectionClass(DetectionClass&& from) noexcept
    : DetectionClass() {
    *this = ::std::move(from);
  }

  inline DetectionClass& operator=(const DetectionClass& from) {
    CopyFrom(from);
    return *this;
  }
  inline DetectionClass& operator=(DetectionClass&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DetectionClass& default_instance() {
    return *internal_default_instance();
  }
  static inline const DetectionClass* internal_default_instance() {
    return reinterpret_cast<const DetectionClass*>(
               &_DetectionClass_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DetectionClass& a, DetectionClass& b) {
    a.Swap(&b);
  }
  inline void Swap(DetectionClass* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DetectionClass* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DetectionClass* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DetectionClass>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DetectionClass& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DetectionClass& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DetectionClass* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.DetectionClass";
  }
  protected:
  explicit DetectionClass(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClassFieldNumber = 1,
    kScoreFieldNumber = 2,
  };
  // string class = 1;
  void clear_class_();
  const std::string& class_() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_class_(ArgT0&& arg0, ArgT... args);
  std::string* mutable_class_();
  PROTOBUF_NODISCARD std::string* release_class_();
  void set_allocated_class_(std::string* class_);
  private:
  const std::string& _internal_class_() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_class_(const std::string& value);
  std::string* _internal_mutable_class_();
  public:

  // float score = 2;
  void clear_score();
  float score() const;
  void set_score(float value);
  private:
  float _internal_score() const;
  void _internal_set_score(float value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.DetectionClass)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr class__;
  float score_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class DeepweedDetection final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.DeepweedDetection) */ {
 public:
  inline DeepweedDetection() : DeepweedDetection(nullptr) {}
  ~DeepweedDetection() override;
  explicit constexpr DeepweedDetection(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeepweedDetection(const DeepweedDetection& from);
  DeepweedDetection(DeepweedDetection&& from) noexcept
    : DeepweedDetection() {
    *this = ::std::move(from);
  }

  inline DeepweedDetection& operator=(const DeepweedDetection& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeepweedDetection& operator=(DeepweedDetection&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeepweedDetection& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeepweedDetection* internal_default_instance() {
    return reinterpret_cast<const DeepweedDetection*>(
               &_DeepweedDetection_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DeepweedDetection& a, DeepweedDetection& b) {
    a.Swap(&b);
  }
  inline void Swap(DeepweedDetection* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeepweedDetection* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeepweedDetection* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeepweedDetection>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeepweedDetection& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeepweedDetection& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeepweedDetection* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.DeepweedDetection";
  }
  protected:
  explicit DeepweedDetection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef DeepweedDetection_HitClass HitClass;
  static constexpr HitClass WEED =
    DeepweedDetection_HitClass_WEED;
  static constexpr HitClass CROP =
    DeepweedDetection_HitClass_CROP;
  static inline bool HitClass_IsValid(int value) {
    return DeepweedDetection_HitClass_IsValid(value);
  }
  static constexpr HitClass HitClass_MIN =
    DeepweedDetection_HitClass_HitClass_MIN;
  static constexpr HitClass HitClass_MAX =
    DeepweedDetection_HitClass_HitClass_MAX;
  static constexpr int HitClass_ARRAYSIZE =
    DeepweedDetection_HitClass_HitClass_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  HitClass_descriptor() {
    return DeepweedDetection_HitClass_descriptor();
  }
  template<typename T>
  static inline const std::string& HitClass_Name(T enum_t_value) {
    static_assert(::std::is_same<T, HitClass>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function HitClass_Name.");
    return DeepweedDetection_HitClass_Name(enum_t_value);
  }
  static inline bool HitClass_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      HitClass* value) {
    return DeepweedDetection_HitClass_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDetectionClassesFieldNumber = 6,
    kMaskIntersectionsFieldNumber = 7,
    kEmbeddingCategoryDistancesFieldNumber = 12,
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kSizeFieldNumber = 3,
    kScoreFieldNumber = 4,
    kHitClassFieldNumber = 5,
    kTrajectoryIdFieldNumber = 8,
    kWeedScoreFieldNumber = 9,
    kCropScoreFieldNumber = 10,
    kPlantScoreFieldNumber = 11,
    kXUndistortedFieldNumber = 13,
    kYUndistortedFieldNumber = 14,
    kUsedInDedupFieldNumber = 15,
  };
  // repeated .recorder.DetectionClass detection_classes = 6;
  int detection_classes_size() const;
  private:
  int _internal_detection_classes_size() const;
  public:
  void clear_detection_classes();
  ::recorder::DetectionClass* mutable_detection_classes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DetectionClass >*
      mutable_detection_classes();
  private:
  const ::recorder::DetectionClass& _internal_detection_classes(int index) const;
  ::recorder::DetectionClass* _internal_add_detection_classes();
  public:
  const ::recorder::DetectionClass& detection_classes(int index) const;
  ::recorder::DetectionClass* add_detection_classes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DetectionClass >&
      detection_classes() const;

  // repeated bytes mask_intersections = 7;
  int mask_intersections_size() const;
  private:
  int _internal_mask_intersections_size() const;
  public:
  void clear_mask_intersections();
  const std::string& mask_intersections(int index) const;
  std::string* mutable_mask_intersections(int index);
  void set_mask_intersections(int index, const std::string& value);
  void set_mask_intersections(int index, std::string&& value);
  void set_mask_intersections(int index, const char* value);
  void set_mask_intersections(int index, const void* value, size_t size);
  std::string* add_mask_intersections();
  void add_mask_intersections(const std::string& value);
  void add_mask_intersections(std::string&& value);
  void add_mask_intersections(const char* value);
  void add_mask_intersections(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& mask_intersections() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_mask_intersections();
  private:
  const std::string& _internal_mask_intersections(int index) const;
  std::string* _internal_add_mask_intersections();
  public:

  // repeated float embedding_category_distances = 12;
  int embedding_category_distances_size() const;
  private:
  int _internal_embedding_category_distances_size() const;
  public:
  void clear_embedding_category_distances();
  private:
  float _internal_embedding_category_distances(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_embedding_category_distances() const;
  void _internal_add_embedding_category_distances(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_embedding_category_distances();
  public:
  float embedding_category_distances(int index) const;
  void set_embedding_category_distances(int index, float value);
  void add_embedding_category_distances(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      embedding_category_distances() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_embedding_category_distances();

  // float x = 1;
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // float y = 2;
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // float size = 3;
  void clear_size();
  float size() const;
  void set_size(float value);
  private:
  float _internal_size() const;
  void _internal_set_size(float value);
  public:

  // float score = 4;
  void clear_score();
  float score() const;
  void set_score(float value);
  private:
  float _internal_score() const;
  void _internal_set_score(float value);
  public:

  // .recorder.DeepweedDetection.HitClass hit_class = 5;
  void clear_hit_class();
  ::recorder::DeepweedDetection_HitClass hit_class() const;
  void set_hit_class(::recorder::DeepweedDetection_HitClass value);
  private:
  ::recorder::DeepweedDetection_HitClass _internal_hit_class() const;
  void _internal_set_hit_class(::recorder::DeepweedDetection_HitClass value);
  public:

  // uint32 trajectory_id = 8;
  void clear_trajectory_id();
  uint32_t trajectory_id() const;
  void set_trajectory_id(uint32_t value);
  private:
  uint32_t _internal_trajectory_id() const;
  void _internal_set_trajectory_id(uint32_t value);
  public:

  // float weed_score = 9;
  void clear_weed_score();
  float weed_score() const;
  void set_weed_score(float value);
  private:
  float _internal_weed_score() const;
  void _internal_set_weed_score(float value);
  public:

  // float crop_score = 10;
  void clear_crop_score();
  float crop_score() const;
  void set_crop_score(float value);
  private:
  float _internal_crop_score() const;
  void _internal_set_crop_score(float value);
  public:

  // float plant_score = 11;
  void clear_plant_score();
  float plant_score() const;
  void set_plant_score(float value);
  private:
  float _internal_plant_score() const;
  void _internal_set_plant_score(float value);
  public:

  // float x_undistorted = 13;
  void clear_x_undistorted();
  float x_undistorted() const;
  void set_x_undistorted(float value);
  private:
  float _internal_x_undistorted() const;
  void _internal_set_x_undistorted(float value);
  public:

  // float y_undistorted = 14;
  void clear_y_undistorted();
  float y_undistorted() const;
  void set_y_undistorted(float value);
  private:
  float _internal_y_undistorted() const;
  void _internal_set_y_undistorted(float value);
  public:

  // bool used_in_dedup = 15;
  void clear_used_in_dedup();
  bool used_in_dedup() const;
  void set_used_in_dedup(bool value);
  private:
  bool _internal_used_in_dedup() const;
  void _internal_set_used_in_dedup(bool value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.DeepweedDetection)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DetectionClass > detection_classes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> mask_intersections_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > embedding_category_distances_;
  float x_;
  float y_;
  float size_;
  float score_;
  int hit_class_;
  uint32_t trajectory_id_;
  float weed_score_;
  float crop_score_;
  float plant_score_;
  float x_undistorted_;
  float y_undistorted_;
  bool used_in_dedup_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class TrackedItemCoordinates final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.TrackedItemCoordinates) */ {
 public:
  inline TrackedItemCoordinates() : TrackedItemCoordinates(nullptr) {}
  ~TrackedItemCoordinates() override;
  explicit constexpr TrackedItemCoordinates(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TrackedItemCoordinates(const TrackedItemCoordinates& from);
  TrackedItemCoordinates(TrackedItemCoordinates&& from) noexcept
    : TrackedItemCoordinates() {
    *this = ::std::move(from);
  }

  inline TrackedItemCoordinates& operator=(const TrackedItemCoordinates& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackedItemCoordinates& operator=(TrackedItemCoordinates&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TrackedItemCoordinates& default_instance() {
    return *internal_default_instance();
  }
  static inline const TrackedItemCoordinates* internal_default_instance() {
    return reinterpret_cast<const TrackedItemCoordinates*>(
               &_TrackedItemCoordinates_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TrackedItemCoordinates& a, TrackedItemCoordinates& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackedItemCoordinates* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackedItemCoordinates* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TrackedItemCoordinates* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TrackedItemCoordinates>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TrackedItemCoordinates& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TrackedItemCoordinates& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackedItemCoordinates* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.TrackedItemCoordinates";
  }
  protected:
  explicit TrackedItemCoordinates(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXMmFieldNumber = 1,
    kYMmFieldNumber = 2,
    kZMmFieldNumber = 3,
    kXPxFieldNumber = 4,
    kYPxFieldNumber = 5,
  };
  // float x_mm = 1;
  void clear_x_mm();
  float x_mm() const;
  void set_x_mm(float value);
  private:
  float _internal_x_mm() const;
  void _internal_set_x_mm(float value);
  public:

  // float y_mm = 2;
  void clear_y_mm();
  float y_mm() const;
  void set_y_mm(float value);
  private:
  float _internal_y_mm() const;
  void _internal_set_y_mm(float value);
  public:

  // float z_mm = 3;
  void clear_z_mm();
  float z_mm() const;
  void set_z_mm(float value);
  private:
  float _internal_z_mm() const;
  void _internal_set_z_mm(float value);
  public:

  // float x_px = 4;
  void clear_x_px();
  float x_px() const;
  void set_x_px(float value);
  private:
  float _internal_x_px() const;
  void _internal_set_x_px(float value);
  public:

  // float y_px = 5;
  void clear_y_px();
  float y_px() const;
  void set_y_px(float value);
  private:
  float _internal_y_px() const;
  void _internal_set_y_px(float value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.TrackedItemCoordinates)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float x_mm_;
  float y_mm_;
  float z_mm_;
  float x_px_;
  float y_px_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class EmbeddingCategory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.EmbeddingCategory) */ {
 public:
  inline EmbeddingCategory() : EmbeddingCategory(nullptr) {}
  ~EmbeddingCategory() override;
  explicit constexpr EmbeddingCategory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EmbeddingCategory(const EmbeddingCategory& from);
  EmbeddingCategory(EmbeddingCategory&& from) noexcept
    : EmbeddingCategory() {
    *this = ::std::move(from);
  }

  inline EmbeddingCategory& operator=(const EmbeddingCategory& from) {
    CopyFrom(from);
    return *this;
  }
  inline EmbeddingCategory& operator=(EmbeddingCategory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EmbeddingCategory& default_instance() {
    return *internal_default_instance();
  }
  static inline const EmbeddingCategory* internal_default_instance() {
    return reinterpret_cast<const EmbeddingCategory*>(
               &_EmbeddingCategory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(EmbeddingCategory& a, EmbeddingCategory& b) {
    a.Swap(&b);
  }
  inline void Swap(EmbeddingCategory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EmbeddingCategory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EmbeddingCategory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EmbeddingCategory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EmbeddingCategory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EmbeddingCategory& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EmbeddingCategory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.EmbeddingCategory";
  }
  protected:
  explicit EmbeddingCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCategoryFieldNumber = 1,
    kDistanceFieldNumber = 2,
  };
  // string category = 1;
  void clear_category();
  const std::string& category() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_category(ArgT0&& arg0, ArgT... args);
  std::string* mutable_category();
  PROTOBUF_NODISCARD std::string* release_category();
  void set_allocated_category(std::string* category);
  private:
  const std::string& _internal_category() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_category(const std::string& value);
  std::string* _internal_mutable_category();
  public:

  // float distance = 2;
  void clear_distance();
  float distance() const;
  void set_distance(float value);
  private:
  float _internal_distance() const;
  void _internal_set_distance(float value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.EmbeddingCategory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_;
  float distance_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class TrackedItem final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.TrackedItem) */ {
 public:
  inline TrackedItem() : TrackedItem(nullptr) {}
  ~TrackedItem() override;
  explicit constexpr TrackedItem(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TrackedItem(const TrackedItem& from);
  TrackedItem(TrackedItem&& from) noexcept
    : TrackedItem() {
    *this = ::std::move(from);
  }

  inline TrackedItem& operator=(const TrackedItem& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackedItem& operator=(TrackedItem&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TrackedItem& default_instance() {
    return *internal_default_instance();
  }
  static inline const TrackedItem* internal_default_instance() {
    return reinterpret_cast<const TrackedItem*>(
               &_TrackedItem_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TrackedItem& a, TrackedItem& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackedItem* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackedItem* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TrackedItem* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TrackedItem>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TrackedItem& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TrackedItem& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackedItem* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.TrackedItem";
  }
  protected:
  explicit TrackedItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCoordinatesBeforeFieldNumber = 3,
    kCoordinatesAfterFieldNumber = 4,
    kTrajectoryIdFieldNumber = 1,
    kDeduplicatedFieldNumber = 2,
    kInCameraFieldNumber = 5,
  };
  // .recorder.TrackedItemCoordinates coordinates_before = 3;
  bool has_coordinates_before() const;
  private:
  bool _internal_has_coordinates_before() const;
  public:
  void clear_coordinates_before();
  const ::recorder::TrackedItemCoordinates& coordinates_before() const;
  PROTOBUF_NODISCARD ::recorder::TrackedItemCoordinates* release_coordinates_before();
  ::recorder::TrackedItemCoordinates* mutable_coordinates_before();
  void set_allocated_coordinates_before(::recorder::TrackedItemCoordinates* coordinates_before);
  private:
  const ::recorder::TrackedItemCoordinates& _internal_coordinates_before() const;
  ::recorder::TrackedItemCoordinates* _internal_mutable_coordinates_before();
  public:
  void unsafe_arena_set_allocated_coordinates_before(
      ::recorder::TrackedItemCoordinates* coordinates_before);
  ::recorder::TrackedItemCoordinates* unsafe_arena_release_coordinates_before();

  // .recorder.TrackedItemCoordinates coordinates_after = 4;
  bool has_coordinates_after() const;
  private:
  bool _internal_has_coordinates_after() const;
  public:
  void clear_coordinates_after();
  const ::recorder::TrackedItemCoordinates& coordinates_after() const;
  PROTOBUF_NODISCARD ::recorder::TrackedItemCoordinates* release_coordinates_after();
  ::recorder::TrackedItemCoordinates* mutable_coordinates_after();
  void set_allocated_coordinates_after(::recorder::TrackedItemCoordinates* coordinates_after);
  private:
  const ::recorder::TrackedItemCoordinates& _internal_coordinates_after() const;
  ::recorder::TrackedItemCoordinates* _internal_mutable_coordinates_after();
  public:
  void unsafe_arena_set_allocated_coordinates_after(
      ::recorder::TrackedItemCoordinates* coordinates_after);
  ::recorder::TrackedItemCoordinates* unsafe_arena_release_coordinates_after();

  // uint32 trajectory_id = 1;
  void clear_trajectory_id();
  uint32_t trajectory_id() const;
  void set_trajectory_id(uint32_t value);
  private:
  uint32_t _internal_trajectory_id() const;
  void _internal_set_trajectory_id(uint32_t value);
  public:

  // bool deduplicated = 2;
  void clear_deduplicated();
  bool deduplicated() const;
  void set_deduplicated(bool value);
  private:
  bool _internal_deduplicated() const;
  void _internal_set_deduplicated(bool value);
  public:

  // bool in_camera = 5;
  void clear_in_camera();
  bool in_camera() const;
  void set_in_camera(bool value);
  private:
  bool _internal_in_camera() const;
  void _internal_set_in_camera(bool value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.TrackedItem)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::recorder::TrackedItemCoordinates* coordinates_before_;
  ::recorder::TrackedItemCoordinates* coordinates_after_;
  uint32_t trajectory_id_;
  bool deduplicated_;
  bool in_camera_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class CandidateShift final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.CandidateShift) */ {
 public:
  inline CandidateShift() : CandidateShift(nullptr) {}
  ~CandidateShift() override;
  explicit constexpr CandidateShift(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CandidateShift(const CandidateShift& from);
  CandidateShift(CandidateShift&& from) noexcept
    : CandidateShift() {
    *this = ::std::move(from);
  }

  inline CandidateShift& operator=(const CandidateShift& from) {
    CopyFrom(from);
    return *this;
  }
  inline CandidateShift& operator=(CandidateShift&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CandidateShift& default_instance() {
    return *internal_default_instance();
  }
  static inline const CandidateShift* internal_default_instance() {
    return reinterpret_cast<const CandidateShift*>(
               &_CandidateShift_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(CandidateShift& a, CandidateShift& b) {
    a.Swap(&b);
  }
  inline void Swap(CandidateShift* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CandidateShift* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CandidateShift* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CandidateShift>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CandidateShift& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CandidateShift& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CandidateShift* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.CandidateShift";
  }
  protected:
  explicit CandidateShift(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXShiftFieldNumber = 1,
    kYShiftFieldNumber = 2,
    kCentroidIdFieldNumber = 3,
    kTrajectoryIdFieldNumber = 4,
  };
  // float x_shift = 1;
  void clear_x_shift();
  float x_shift() const;
  void set_x_shift(float value);
  private:
  float _internal_x_shift() const;
  void _internal_set_x_shift(float value);
  public:

  // float y_shift = 2;
  void clear_y_shift();
  float y_shift() const;
  void set_y_shift(float value);
  private:
  float _internal_y_shift() const;
  void _internal_set_y_shift(float value);
  public:

  // uint32 centroid_id = 3;
  void clear_centroid_id();
  uint32_t centroid_id() const;
  void set_centroid_id(uint32_t value);
  private:
  uint32_t _internal_centroid_id() const;
  void _internal_set_centroid_id(uint32_t value);
  public:

  // uint32 trajectory_id = 4;
  void clear_trajectory_id();
  uint32_t trajectory_id() const;
  void set_trajectory_id(uint32_t value);
  private:
  uint32_t _internal_trajectory_id() const;
  void _internal_set_trajectory_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.CandidateShift)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float x_shift_;
  float y_shift_;
  uint32_t centroid_id_;
  uint32_t trajectory_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class DeepweedPredictionFrame final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.DeepweedPredictionFrame) */ {
 public:
  inline DeepweedPredictionFrame() : DeepweedPredictionFrame(nullptr) {}
  ~DeepweedPredictionFrame() override;
  explicit constexpr DeepweedPredictionFrame(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeepweedPredictionFrame(const DeepweedPredictionFrame& from);
  DeepweedPredictionFrame(DeepweedPredictionFrame&& from) noexcept
    : DeepweedPredictionFrame() {
    *this = ::std::move(from);
  }

  inline DeepweedPredictionFrame& operator=(const DeepweedPredictionFrame& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeepweedPredictionFrame& operator=(DeepweedPredictionFrame&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeepweedPredictionFrame& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeepweedPredictionFrame* internal_default_instance() {
    return reinterpret_cast<const DeepweedPredictionFrame*>(
               &_DeepweedPredictionFrame_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DeepweedPredictionFrame& a, DeepweedPredictionFrame& b) {
    a.Swap(&b);
  }
  inline void Swap(DeepweedPredictionFrame* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeepweedPredictionFrame* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeepweedPredictionFrame* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeepweedPredictionFrame>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeepweedPredictionFrame& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeepweedPredictionFrame& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeepweedPredictionFrame* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.DeepweedPredictionFrame";
  }
  protected:
  explicit DeepweedPredictionFrame(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDetectionsFieldNumber = 2,
    kEmbeddingCategoriesFieldNumber = 3,
    kTrackedItemsFieldNumber = 4,
    kCandidateShiftsFieldNumber = 6,
    kBestShiftFieldNumber = 5,
    kTimestampMsFieldNumber = 1,
    kPlantMatcherValidFieldNumber = 7,
  };
  // repeated .recorder.DeepweedDetection detections = 2;
  int detections_size() const;
  private:
  int _internal_detections_size() const;
  public:
  void clear_detections();
  ::recorder::DeepweedDetection* mutable_detections(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DeepweedDetection >*
      mutable_detections();
  private:
  const ::recorder::DeepweedDetection& _internal_detections(int index) const;
  ::recorder::DeepweedDetection* _internal_add_detections();
  public:
  const ::recorder::DeepweedDetection& detections(int index) const;
  ::recorder::DeepweedDetection* add_detections();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DeepweedDetection >&
      detections() const;

  // repeated string embedding_categories = 3;
  int embedding_categories_size() const;
  private:
  int _internal_embedding_categories_size() const;
  public:
  void clear_embedding_categories();
  const std::string& embedding_categories(int index) const;
  std::string* mutable_embedding_categories(int index);
  void set_embedding_categories(int index, const std::string& value);
  void set_embedding_categories(int index, std::string&& value);
  void set_embedding_categories(int index, const char* value);
  void set_embedding_categories(int index, const char* value, size_t size);
  std::string* add_embedding_categories();
  void add_embedding_categories(const std::string& value);
  void add_embedding_categories(std::string&& value);
  void add_embedding_categories(const char* value);
  void add_embedding_categories(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& embedding_categories() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_embedding_categories();
  private:
  const std::string& _internal_embedding_categories(int index) const;
  std::string* _internal_add_embedding_categories();
  public:

  // repeated .recorder.TrackedItem tracked_items = 4;
  int tracked_items_size() const;
  private:
  int _internal_tracked_items_size() const;
  public:
  void clear_tracked_items();
  ::recorder::TrackedItem* mutable_tracked_items(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::TrackedItem >*
      mutable_tracked_items();
  private:
  const ::recorder::TrackedItem& _internal_tracked_items(int index) const;
  ::recorder::TrackedItem* _internal_add_tracked_items();
  public:
  const ::recorder::TrackedItem& tracked_items(int index) const;
  ::recorder::TrackedItem* add_tracked_items();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::TrackedItem >&
      tracked_items() const;

  // repeated .recorder.CandidateShift candidate_shifts = 6;
  int candidate_shifts_size() const;
  private:
  int _internal_candidate_shifts_size() const;
  public:
  void clear_candidate_shifts();
  ::recorder::CandidateShift* mutable_candidate_shifts(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::CandidateShift >*
      mutable_candidate_shifts();
  private:
  const ::recorder::CandidateShift& _internal_candidate_shifts(int index) const;
  ::recorder::CandidateShift* _internal_add_candidate_shifts();
  public:
  const ::recorder::CandidateShift& candidate_shifts(int index) const;
  ::recorder::CandidateShift* add_candidate_shifts();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::CandidateShift >&
      candidate_shifts() const;

  // .recorder.CandidateShift best_shift = 5;
  bool has_best_shift() const;
  private:
  bool _internal_has_best_shift() const;
  public:
  void clear_best_shift();
  const ::recorder::CandidateShift& best_shift() const;
  PROTOBUF_NODISCARD ::recorder::CandidateShift* release_best_shift();
  ::recorder::CandidateShift* mutable_best_shift();
  void set_allocated_best_shift(::recorder::CandidateShift* best_shift);
  private:
  const ::recorder::CandidateShift& _internal_best_shift() const;
  ::recorder::CandidateShift* _internal_mutable_best_shift();
  public:
  void unsafe_arena_set_allocated_best_shift(
      ::recorder::CandidateShift* best_shift);
  ::recorder::CandidateShift* unsafe_arena_release_best_shift();

  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // bool plant_matcher_valid = 7;
  void clear_plant_matcher_valid();
  bool plant_matcher_valid() const;
  void set_plant_matcher_valid(bool value);
  private:
  bool _internal_plant_matcher_valid() const;
  void _internal_set_plant_matcher_valid(bool value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.DeepweedPredictionFrame)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DeepweedDetection > detections_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> embedding_categories_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::TrackedItem > tracked_items_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::CandidateShift > candidate_shifts_;
  ::recorder::CandidateShift* best_shift_;
  int64_t timestamp_ms_;
  bool plant_matcher_valid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class DeepweedPredictionRecord final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.DeepweedPredictionRecord) */ {
 public:
  inline DeepweedPredictionRecord() : DeepweedPredictionRecord(nullptr) {}
  ~DeepweedPredictionRecord() override;
  explicit constexpr DeepweedPredictionRecord(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeepweedPredictionRecord(const DeepweedPredictionRecord& from);
  DeepweedPredictionRecord(DeepweedPredictionRecord&& from) noexcept
    : DeepweedPredictionRecord() {
    *this = ::std::move(from);
  }

  inline DeepweedPredictionRecord& operator=(const DeepweedPredictionRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeepweedPredictionRecord& operator=(DeepweedPredictionRecord&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeepweedPredictionRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeepweedPredictionRecord* internal_default_instance() {
    return reinterpret_cast<const DeepweedPredictionRecord*>(
               &_DeepweedPredictionRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(DeepweedPredictionRecord& a, DeepweedPredictionRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(DeepweedPredictionRecord* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeepweedPredictionRecord* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeepweedPredictionRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeepweedPredictionRecord>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeepweedPredictionRecord& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeepweedPredictionRecord& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeepweedPredictionRecord* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.DeepweedPredictionRecord";
  }
  protected:
  explicit DeepweedPredictionRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFrameFieldNumber = 2,
    kRecordTimestampMsFieldNumber = 1,
  };
  // .recorder.DeepweedPredictionFrame frame = 2;
  bool has_frame() const;
  private:
  bool _internal_has_frame() const;
  public:
  void clear_frame();
  const ::recorder::DeepweedPredictionFrame& frame() const;
  PROTOBUF_NODISCARD ::recorder::DeepweedPredictionFrame* release_frame();
  ::recorder::DeepweedPredictionFrame* mutable_frame();
  void set_allocated_frame(::recorder::DeepweedPredictionFrame* frame);
  private:
  const ::recorder::DeepweedPredictionFrame& _internal_frame() const;
  ::recorder::DeepweedPredictionFrame* _internal_mutable_frame();
  public:
  void unsafe_arena_set_allocated_frame(
      ::recorder::DeepweedPredictionFrame* frame);
  ::recorder::DeepweedPredictionFrame* unsafe_arena_release_frame();

  // uint64 record_timestamp_ms = 1;
  void clear_record_timestamp_ms();
  uint64_t record_timestamp_ms() const;
  void set_record_timestamp_ms(uint64_t value);
  private:
  uint64_t _internal_record_timestamp_ms() const;
  void _internal_set_record_timestamp_ms(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.DeepweedPredictionRecord)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::recorder::DeepweedPredictionFrame* frame_;
  uint64_t record_timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class LaneHeightSnapshot final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.LaneHeightSnapshot) */ {
 public:
  inline LaneHeightSnapshot() : LaneHeightSnapshot(nullptr) {}
  ~LaneHeightSnapshot() override;
  explicit constexpr LaneHeightSnapshot(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaneHeightSnapshot(const LaneHeightSnapshot& from);
  LaneHeightSnapshot(LaneHeightSnapshot&& from) noexcept
    : LaneHeightSnapshot() {
    *this = ::std::move(from);
  }

  inline LaneHeightSnapshot& operator=(const LaneHeightSnapshot& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneHeightSnapshot& operator=(LaneHeightSnapshot&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaneHeightSnapshot& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaneHeightSnapshot* internal_default_instance() {
    return reinterpret_cast<const LaneHeightSnapshot*>(
               &_LaneHeightSnapshot_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(LaneHeightSnapshot& a, LaneHeightSnapshot& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneHeightSnapshot* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneHeightSnapshot* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaneHeightSnapshot* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaneHeightSnapshot>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaneHeightSnapshot& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaneHeightSnapshot& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneHeightSnapshot* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.LaneHeightSnapshot";
  }
  protected:
  explicit LaneHeightSnapshot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWeedHeightFieldNumber = 1,
    kCropHeightFieldNumber = 2,
  };
  // repeated double weed_height = 1;
  int weed_height_size() const;
  private:
  int _internal_weed_height_size() const;
  public:
  void clear_weed_height();
  private:
  double _internal_weed_height(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_weed_height() const;
  void _internal_add_weed_height(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_weed_height();
  public:
  double weed_height(int index) const;
  void set_weed_height(int index, double value);
  void add_weed_height(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      weed_height() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_weed_height();

  // repeated double crop_height = 2;
  int crop_height_size() const;
  private:
  int _internal_crop_height_size() const;
  public:
  void clear_crop_height();
  private:
  double _internal_crop_height(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_crop_height() const;
  void _internal_add_crop_height(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_crop_height();
  public:
  double crop_height(int index) const;
  void set_crop_height(int index, double value);
  void add_crop_height(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      crop_height() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_crop_height();

  // @@protoc_insertion_point(class_scope:recorder.LaneHeightSnapshot)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > weed_height_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > crop_height_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class LaneHeightRecord final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.LaneHeightRecord) */ {
 public:
  inline LaneHeightRecord() : LaneHeightRecord(nullptr) {}
  ~LaneHeightRecord() override;
  explicit constexpr LaneHeightRecord(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaneHeightRecord(const LaneHeightRecord& from);
  LaneHeightRecord(LaneHeightRecord&& from) noexcept
    : LaneHeightRecord() {
    *this = ::std::move(from);
  }

  inline LaneHeightRecord& operator=(const LaneHeightRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneHeightRecord& operator=(LaneHeightRecord&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaneHeightRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaneHeightRecord* internal_default_instance() {
    return reinterpret_cast<const LaneHeightRecord*>(
               &_LaneHeightRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(LaneHeightRecord& a, LaneHeightRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneHeightRecord* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneHeightRecord* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaneHeightRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaneHeightRecord>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaneHeightRecord& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaneHeightRecord& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneHeightRecord* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.LaneHeightRecord";
  }
  protected:
  explicit LaneHeightRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSnapshotFieldNumber = 2,
    kRecordTimestampMsFieldNumber = 1,
  };
  // .recorder.LaneHeightSnapshot snapshot = 2;
  bool has_snapshot() const;
  private:
  bool _internal_has_snapshot() const;
  public:
  void clear_snapshot();
  const ::recorder::LaneHeightSnapshot& snapshot() const;
  PROTOBUF_NODISCARD ::recorder::LaneHeightSnapshot* release_snapshot();
  ::recorder::LaneHeightSnapshot* mutable_snapshot();
  void set_allocated_snapshot(::recorder::LaneHeightSnapshot* snapshot);
  private:
  const ::recorder::LaneHeightSnapshot& _internal_snapshot() const;
  ::recorder::LaneHeightSnapshot* _internal_mutable_snapshot();
  public:
  void unsafe_arena_set_allocated_snapshot(
      ::recorder::LaneHeightSnapshot* snapshot);
  ::recorder::LaneHeightSnapshot* unsafe_arena_release_snapshot();

  // uint64 record_timestamp_ms = 1;
  void clear_record_timestamp_ms();
  uint64_t record_timestamp_ms() const;
  void set_record_timestamp_ms(uint64_t value);
  private:
  uint64_t _internal_record_timestamp_ms() const;
  void _internal_set_record_timestamp_ms(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.LaneHeightRecord)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::recorder::LaneHeightSnapshot* snapshot_;
  uint64_t record_timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class RotaryTicksSnapshot final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.RotaryTicksSnapshot) */ {
 public:
  inline RotaryTicksSnapshot() : RotaryTicksSnapshot(nullptr) {}
  ~RotaryTicksSnapshot() override;
  explicit constexpr RotaryTicksSnapshot(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RotaryTicksSnapshot(const RotaryTicksSnapshot& from);
  RotaryTicksSnapshot(RotaryTicksSnapshot&& from) noexcept
    : RotaryTicksSnapshot() {
    *this = ::std::move(from);
  }

  inline RotaryTicksSnapshot& operator=(const RotaryTicksSnapshot& from) {
    CopyFrom(from);
    return *this;
  }
  inline RotaryTicksSnapshot& operator=(RotaryTicksSnapshot&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RotaryTicksSnapshot& default_instance() {
    return *internal_default_instance();
  }
  static inline const RotaryTicksSnapshot* internal_default_instance() {
    return reinterpret_cast<const RotaryTicksSnapshot*>(
               &_RotaryTicksSnapshot_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(RotaryTicksSnapshot& a, RotaryTicksSnapshot& b) {
    a.Swap(&b);
  }
  inline void Swap(RotaryTicksSnapshot* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RotaryTicksSnapshot* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RotaryTicksSnapshot* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RotaryTicksSnapshot>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RotaryTicksSnapshot& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RotaryTicksSnapshot& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RotaryTicksSnapshot* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.RotaryTicksSnapshot";
  }
  protected:
  explicit RotaryTicksSnapshot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimestampUsFieldNumber = 1,
    kFlFieldNumber = 2,
    kFrFieldNumber = 3,
    kBlFieldNumber = 4,
    kBrFieldNumber = 5,
    kFlEnabledFieldNumber = 6,
    kFrEnabledFieldNumber = 7,
    kBlEnabledFieldNumber = 8,
    kBrEnabledFieldNumber = 9,
  };
  // uint64 timestamp_us = 1;
  void clear_timestamp_us();
  uint64_t timestamp_us() const;
  void set_timestamp_us(uint64_t value);
  private:
  uint64_t _internal_timestamp_us() const;
  void _internal_set_timestamp_us(uint64_t value);
  public:

  // int32 fl = 2;
  void clear_fl();
  int32_t fl() const;
  void set_fl(int32_t value);
  private:
  int32_t _internal_fl() const;
  void _internal_set_fl(int32_t value);
  public:

  // int32 fr = 3;
  void clear_fr();
  int32_t fr() const;
  void set_fr(int32_t value);
  private:
  int32_t _internal_fr() const;
  void _internal_set_fr(int32_t value);
  public:

  // int32 bl = 4;
  void clear_bl();
  int32_t bl() const;
  void set_bl(int32_t value);
  private:
  int32_t _internal_bl() const;
  void _internal_set_bl(int32_t value);
  public:

  // int32 br = 5;
  void clear_br();
  int32_t br() const;
  void set_br(int32_t value);
  private:
  int32_t _internal_br() const;
  void _internal_set_br(int32_t value);
  public:

  // bool fl_enabled = 6;
  void clear_fl_enabled();
  bool fl_enabled() const;
  void set_fl_enabled(bool value);
  private:
  bool _internal_fl_enabled() const;
  void _internal_set_fl_enabled(bool value);
  public:

  // bool fr_enabled = 7;
  void clear_fr_enabled();
  bool fr_enabled() const;
  void set_fr_enabled(bool value);
  private:
  bool _internal_fr_enabled() const;
  void _internal_set_fr_enabled(bool value);
  public:

  // bool bl_enabled = 8;
  void clear_bl_enabled();
  bool bl_enabled() const;
  void set_bl_enabled(bool value);
  private:
  bool _internal_bl_enabled() const;
  void _internal_set_bl_enabled(bool value);
  public:

  // bool br_enabled = 9;
  void clear_br_enabled();
  bool br_enabled() const;
  void set_br_enabled(bool value);
  private:
  bool _internal_br_enabled() const;
  void _internal_set_br_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.RotaryTicksSnapshot)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t timestamp_us_;
  int32_t fl_;
  int32_t fr_;
  int32_t bl_;
  int32_t br_;
  bool fl_enabled_;
  bool fr_enabled_;
  bool bl_enabled_;
  bool br_enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// -------------------------------------------------------------------

class RotaryTicksRecord final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:recorder.RotaryTicksRecord) */ {
 public:
  inline RotaryTicksRecord() : RotaryTicksRecord(nullptr) {}
  ~RotaryTicksRecord() override;
  explicit constexpr RotaryTicksRecord(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RotaryTicksRecord(const RotaryTicksRecord& from);
  RotaryTicksRecord(RotaryTicksRecord&& from) noexcept
    : RotaryTicksRecord() {
    *this = ::std::move(from);
  }

  inline RotaryTicksRecord& operator=(const RotaryTicksRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline RotaryTicksRecord& operator=(RotaryTicksRecord&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RotaryTicksRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const RotaryTicksRecord* internal_default_instance() {
    return reinterpret_cast<const RotaryTicksRecord*>(
               &_RotaryTicksRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(RotaryTicksRecord& a, RotaryTicksRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(RotaryTicksRecord* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RotaryTicksRecord* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RotaryTicksRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RotaryTicksRecord>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RotaryTicksRecord& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RotaryTicksRecord& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RotaryTicksRecord* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "recorder.RotaryTicksRecord";
  }
  protected:
  explicit RotaryTicksRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSnapshotFieldNumber = 2,
    kRecordTimestampMsFieldNumber = 1,
  };
  // .recorder.RotaryTicksSnapshot snapshot = 2;
  bool has_snapshot() const;
  private:
  bool _internal_has_snapshot() const;
  public:
  void clear_snapshot();
  const ::recorder::RotaryTicksSnapshot& snapshot() const;
  PROTOBUF_NODISCARD ::recorder::RotaryTicksSnapshot* release_snapshot();
  ::recorder::RotaryTicksSnapshot* mutable_snapshot();
  void set_allocated_snapshot(::recorder::RotaryTicksSnapshot* snapshot);
  private:
  const ::recorder::RotaryTicksSnapshot& _internal_snapshot() const;
  ::recorder::RotaryTicksSnapshot* _internal_mutable_snapshot();
  public:
  void unsafe_arena_set_allocated_snapshot(
      ::recorder::RotaryTicksSnapshot* snapshot);
  ::recorder::RotaryTicksSnapshot* unsafe_arena_release_snapshot();

  // uint64 record_timestamp_ms = 1;
  void clear_record_timestamp_ms();
  uint64_t record_timestamp_ms() const;
  void set_record_timestamp_ms(uint64_t value);
  private:
  uint64_t _internal_record_timestamp_ms() const;
  void _internal_set_record_timestamp_ms(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:recorder.RotaryTicksRecord)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::recorder::RotaryTicksSnapshot* snapshot_;
  uint64_t record_timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_recorder_2fproto_2frecorder_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DetectionClass

// string class = 1;
inline void DetectionClass::clear_class_() {
  class__.ClearToEmpty();
}
inline const std::string& DetectionClass::class_() const {
  // @@protoc_insertion_point(field_get:recorder.DetectionClass.class)
  return _internal_class_();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectionClass::set_class_(ArgT0&& arg0, ArgT... args) {
 
 class__.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:recorder.DetectionClass.class)
}
inline std::string* DetectionClass::mutable_class_() {
  std::string* _s = _internal_mutable_class_();
  // @@protoc_insertion_point(field_mutable:recorder.DetectionClass.class)
  return _s;
}
inline const std::string& DetectionClass::_internal_class_() const {
  return class__.Get();
}
inline void DetectionClass::_internal_set_class_(const std::string& value) {
  
  class__.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DetectionClass::_internal_mutable_class_() {
  
  return class__.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DetectionClass::release_class_() {
  // @@protoc_insertion_point(field_release:recorder.DetectionClass.class)
  return class__.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DetectionClass::set_allocated_class_(std::string* class_) {
  if (class_ != nullptr) {
    
  } else {
    
  }
  class__.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), class_,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (class__.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    class__.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:recorder.DetectionClass.class)
}

// float score = 2;
inline void DetectionClass::clear_score() {
  score_ = 0;
}
inline float DetectionClass::_internal_score() const {
  return score_;
}
inline float DetectionClass::score() const {
  // @@protoc_insertion_point(field_get:recorder.DetectionClass.score)
  return _internal_score();
}
inline void DetectionClass::_internal_set_score(float value) {
  
  score_ = value;
}
inline void DetectionClass::set_score(float value) {
  _internal_set_score(value);
  // @@protoc_insertion_point(field_set:recorder.DetectionClass.score)
}

// -------------------------------------------------------------------

// DeepweedDetection

// float x = 1;
inline void DeepweedDetection::clear_x() {
  x_ = 0;
}
inline float DeepweedDetection::_internal_x() const {
  return x_;
}
inline float DeepweedDetection::x() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.x)
  return _internal_x();
}
inline void DeepweedDetection::_internal_set_x(float value) {
  
  x_ = value;
}
inline void DeepweedDetection::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.x)
}

// float y = 2;
inline void DeepweedDetection::clear_y() {
  y_ = 0;
}
inline float DeepweedDetection::_internal_y() const {
  return y_;
}
inline float DeepweedDetection::y() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.y)
  return _internal_y();
}
inline void DeepweedDetection::_internal_set_y(float value) {
  
  y_ = value;
}
inline void DeepweedDetection::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.y)
}

// float size = 3;
inline void DeepweedDetection::clear_size() {
  size_ = 0;
}
inline float DeepweedDetection::_internal_size() const {
  return size_;
}
inline float DeepweedDetection::size() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.size)
  return _internal_size();
}
inline void DeepweedDetection::_internal_set_size(float value) {
  
  size_ = value;
}
inline void DeepweedDetection::set_size(float value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.size)
}

// float score = 4;
inline void DeepweedDetection::clear_score() {
  score_ = 0;
}
inline float DeepweedDetection::_internal_score() const {
  return score_;
}
inline float DeepweedDetection::score() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.score)
  return _internal_score();
}
inline void DeepweedDetection::_internal_set_score(float value) {
  
  score_ = value;
}
inline void DeepweedDetection::set_score(float value) {
  _internal_set_score(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.score)
}

// .recorder.DeepweedDetection.HitClass hit_class = 5;
inline void DeepweedDetection::clear_hit_class() {
  hit_class_ = 0;
}
inline ::recorder::DeepweedDetection_HitClass DeepweedDetection::_internal_hit_class() const {
  return static_cast< ::recorder::DeepweedDetection_HitClass >(hit_class_);
}
inline ::recorder::DeepweedDetection_HitClass DeepweedDetection::hit_class() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.hit_class)
  return _internal_hit_class();
}
inline void DeepweedDetection::_internal_set_hit_class(::recorder::DeepweedDetection_HitClass value) {
  
  hit_class_ = value;
}
inline void DeepweedDetection::set_hit_class(::recorder::DeepweedDetection_HitClass value) {
  _internal_set_hit_class(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.hit_class)
}

// repeated .recorder.DetectionClass detection_classes = 6;
inline int DeepweedDetection::_internal_detection_classes_size() const {
  return detection_classes_.size();
}
inline int DeepweedDetection::detection_classes_size() const {
  return _internal_detection_classes_size();
}
inline void DeepweedDetection::clear_detection_classes() {
  detection_classes_.Clear();
}
inline ::recorder::DetectionClass* DeepweedDetection::mutable_detection_classes(int index) {
  // @@protoc_insertion_point(field_mutable:recorder.DeepweedDetection.detection_classes)
  return detection_classes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DetectionClass >*
DeepweedDetection::mutable_detection_classes() {
  // @@protoc_insertion_point(field_mutable_list:recorder.DeepweedDetection.detection_classes)
  return &detection_classes_;
}
inline const ::recorder::DetectionClass& DeepweedDetection::_internal_detection_classes(int index) const {
  return detection_classes_.Get(index);
}
inline const ::recorder::DetectionClass& DeepweedDetection::detection_classes(int index) const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.detection_classes)
  return _internal_detection_classes(index);
}
inline ::recorder::DetectionClass* DeepweedDetection::_internal_add_detection_classes() {
  return detection_classes_.Add();
}
inline ::recorder::DetectionClass* DeepweedDetection::add_detection_classes() {
  ::recorder::DetectionClass* _add = _internal_add_detection_classes();
  // @@protoc_insertion_point(field_add:recorder.DeepweedDetection.detection_classes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DetectionClass >&
DeepweedDetection::detection_classes() const {
  // @@protoc_insertion_point(field_list:recorder.DeepweedDetection.detection_classes)
  return detection_classes_;
}

// repeated bytes mask_intersections = 7;
inline int DeepweedDetection::_internal_mask_intersections_size() const {
  return mask_intersections_.size();
}
inline int DeepweedDetection::mask_intersections_size() const {
  return _internal_mask_intersections_size();
}
inline void DeepweedDetection::clear_mask_intersections() {
  mask_intersections_.Clear();
}
inline std::string* DeepweedDetection::add_mask_intersections() {
  std::string* _s = _internal_add_mask_intersections();
  // @@protoc_insertion_point(field_add_mutable:recorder.DeepweedDetection.mask_intersections)
  return _s;
}
inline const std::string& DeepweedDetection::_internal_mask_intersections(int index) const {
  return mask_intersections_.Get(index);
}
inline const std::string& DeepweedDetection::mask_intersections(int index) const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.mask_intersections)
  return _internal_mask_intersections(index);
}
inline std::string* DeepweedDetection::mutable_mask_intersections(int index) {
  // @@protoc_insertion_point(field_mutable:recorder.DeepweedDetection.mask_intersections)
  return mask_intersections_.Mutable(index);
}
inline void DeepweedDetection::set_mask_intersections(int index, const std::string& value) {
  mask_intersections_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.mask_intersections)
}
inline void DeepweedDetection::set_mask_intersections(int index, std::string&& value) {
  mask_intersections_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.mask_intersections)
}
inline void DeepweedDetection::set_mask_intersections(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  mask_intersections_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:recorder.DeepweedDetection.mask_intersections)
}
inline void DeepweedDetection::set_mask_intersections(int index, const void* value, size_t size) {
  mask_intersections_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:recorder.DeepweedDetection.mask_intersections)
}
inline std::string* DeepweedDetection::_internal_add_mask_intersections() {
  return mask_intersections_.Add();
}
inline void DeepweedDetection::add_mask_intersections(const std::string& value) {
  mask_intersections_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:recorder.DeepweedDetection.mask_intersections)
}
inline void DeepweedDetection::add_mask_intersections(std::string&& value) {
  mask_intersections_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:recorder.DeepweedDetection.mask_intersections)
}
inline void DeepweedDetection::add_mask_intersections(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  mask_intersections_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:recorder.DeepweedDetection.mask_intersections)
}
inline void DeepweedDetection::add_mask_intersections(const void* value, size_t size) {
  mask_intersections_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:recorder.DeepweedDetection.mask_intersections)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DeepweedDetection::mask_intersections() const {
  // @@protoc_insertion_point(field_list:recorder.DeepweedDetection.mask_intersections)
  return mask_intersections_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DeepweedDetection::mutable_mask_intersections() {
  // @@protoc_insertion_point(field_mutable_list:recorder.DeepweedDetection.mask_intersections)
  return &mask_intersections_;
}

// uint32 trajectory_id = 8;
inline void DeepweedDetection::clear_trajectory_id() {
  trajectory_id_ = 0u;
}
inline uint32_t DeepweedDetection::_internal_trajectory_id() const {
  return trajectory_id_;
}
inline uint32_t DeepweedDetection::trajectory_id() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.trajectory_id)
  return _internal_trajectory_id();
}
inline void DeepweedDetection::_internal_set_trajectory_id(uint32_t value) {
  
  trajectory_id_ = value;
}
inline void DeepweedDetection::set_trajectory_id(uint32_t value) {
  _internal_set_trajectory_id(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.trajectory_id)
}

// float weed_score = 9;
inline void DeepweedDetection::clear_weed_score() {
  weed_score_ = 0;
}
inline float DeepweedDetection::_internal_weed_score() const {
  return weed_score_;
}
inline float DeepweedDetection::weed_score() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.weed_score)
  return _internal_weed_score();
}
inline void DeepweedDetection::_internal_set_weed_score(float value) {
  
  weed_score_ = value;
}
inline void DeepweedDetection::set_weed_score(float value) {
  _internal_set_weed_score(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.weed_score)
}

// float crop_score = 10;
inline void DeepweedDetection::clear_crop_score() {
  crop_score_ = 0;
}
inline float DeepweedDetection::_internal_crop_score() const {
  return crop_score_;
}
inline float DeepweedDetection::crop_score() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.crop_score)
  return _internal_crop_score();
}
inline void DeepweedDetection::_internal_set_crop_score(float value) {
  
  crop_score_ = value;
}
inline void DeepweedDetection::set_crop_score(float value) {
  _internal_set_crop_score(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.crop_score)
}

// float plant_score = 11;
inline void DeepweedDetection::clear_plant_score() {
  plant_score_ = 0;
}
inline float DeepweedDetection::_internal_plant_score() const {
  return plant_score_;
}
inline float DeepweedDetection::plant_score() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.plant_score)
  return _internal_plant_score();
}
inline void DeepweedDetection::_internal_set_plant_score(float value) {
  
  plant_score_ = value;
}
inline void DeepweedDetection::set_plant_score(float value) {
  _internal_set_plant_score(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.plant_score)
}

// repeated float embedding_category_distances = 12;
inline int DeepweedDetection::_internal_embedding_category_distances_size() const {
  return embedding_category_distances_.size();
}
inline int DeepweedDetection::embedding_category_distances_size() const {
  return _internal_embedding_category_distances_size();
}
inline void DeepweedDetection::clear_embedding_category_distances() {
  embedding_category_distances_.Clear();
}
inline float DeepweedDetection::_internal_embedding_category_distances(int index) const {
  return embedding_category_distances_.Get(index);
}
inline float DeepweedDetection::embedding_category_distances(int index) const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.embedding_category_distances)
  return _internal_embedding_category_distances(index);
}
inline void DeepweedDetection::set_embedding_category_distances(int index, float value) {
  embedding_category_distances_.Set(index, value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.embedding_category_distances)
}
inline void DeepweedDetection::_internal_add_embedding_category_distances(float value) {
  embedding_category_distances_.Add(value);
}
inline void DeepweedDetection::add_embedding_category_distances(float value) {
  _internal_add_embedding_category_distances(value);
  // @@protoc_insertion_point(field_add:recorder.DeepweedDetection.embedding_category_distances)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
DeepweedDetection::_internal_embedding_category_distances() const {
  return embedding_category_distances_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
DeepweedDetection::embedding_category_distances() const {
  // @@protoc_insertion_point(field_list:recorder.DeepweedDetection.embedding_category_distances)
  return _internal_embedding_category_distances();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
DeepweedDetection::_internal_mutable_embedding_category_distances() {
  return &embedding_category_distances_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
DeepweedDetection::mutable_embedding_category_distances() {
  // @@protoc_insertion_point(field_mutable_list:recorder.DeepweedDetection.embedding_category_distances)
  return _internal_mutable_embedding_category_distances();
}

// float x_undistorted = 13;
inline void DeepweedDetection::clear_x_undistorted() {
  x_undistorted_ = 0;
}
inline float DeepweedDetection::_internal_x_undistorted() const {
  return x_undistorted_;
}
inline float DeepweedDetection::x_undistorted() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.x_undistorted)
  return _internal_x_undistorted();
}
inline void DeepweedDetection::_internal_set_x_undistorted(float value) {
  
  x_undistorted_ = value;
}
inline void DeepweedDetection::set_x_undistorted(float value) {
  _internal_set_x_undistorted(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.x_undistorted)
}

// float y_undistorted = 14;
inline void DeepweedDetection::clear_y_undistorted() {
  y_undistorted_ = 0;
}
inline float DeepweedDetection::_internal_y_undistorted() const {
  return y_undistorted_;
}
inline float DeepweedDetection::y_undistorted() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.y_undistorted)
  return _internal_y_undistorted();
}
inline void DeepweedDetection::_internal_set_y_undistorted(float value) {
  
  y_undistorted_ = value;
}
inline void DeepweedDetection::set_y_undistorted(float value) {
  _internal_set_y_undistorted(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.y_undistorted)
}

// bool used_in_dedup = 15;
inline void DeepweedDetection::clear_used_in_dedup() {
  used_in_dedup_ = false;
}
inline bool DeepweedDetection::_internal_used_in_dedup() const {
  return used_in_dedup_;
}
inline bool DeepweedDetection::used_in_dedup() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedDetection.used_in_dedup)
  return _internal_used_in_dedup();
}
inline void DeepweedDetection::_internal_set_used_in_dedup(bool value) {
  
  used_in_dedup_ = value;
}
inline void DeepweedDetection::set_used_in_dedup(bool value) {
  _internal_set_used_in_dedup(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedDetection.used_in_dedup)
}

// -------------------------------------------------------------------

// TrackedItemCoordinates

// float x_mm = 1;
inline void TrackedItemCoordinates::clear_x_mm() {
  x_mm_ = 0;
}
inline float TrackedItemCoordinates::_internal_x_mm() const {
  return x_mm_;
}
inline float TrackedItemCoordinates::x_mm() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItemCoordinates.x_mm)
  return _internal_x_mm();
}
inline void TrackedItemCoordinates::_internal_set_x_mm(float value) {
  
  x_mm_ = value;
}
inline void TrackedItemCoordinates::set_x_mm(float value) {
  _internal_set_x_mm(value);
  // @@protoc_insertion_point(field_set:recorder.TrackedItemCoordinates.x_mm)
}

// float y_mm = 2;
inline void TrackedItemCoordinates::clear_y_mm() {
  y_mm_ = 0;
}
inline float TrackedItemCoordinates::_internal_y_mm() const {
  return y_mm_;
}
inline float TrackedItemCoordinates::y_mm() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItemCoordinates.y_mm)
  return _internal_y_mm();
}
inline void TrackedItemCoordinates::_internal_set_y_mm(float value) {
  
  y_mm_ = value;
}
inline void TrackedItemCoordinates::set_y_mm(float value) {
  _internal_set_y_mm(value);
  // @@protoc_insertion_point(field_set:recorder.TrackedItemCoordinates.y_mm)
}

// float z_mm = 3;
inline void TrackedItemCoordinates::clear_z_mm() {
  z_mm_ = 0;
}
inline float TrackedItemCoordinates::_internal_z_mm() const {
  return z_mm_;
}
inline float TrackedItemCoordinates::z_mm() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItemCoordinates.z_mm)
  return _internal_z_mm();
}
inline void TrackedItemCoordinates::_internal_set_z_mm(float value) {
  
  z_mm_ = value;
}
inline void TrackedItemCoordinates::set_z_mm(float value) {
  _internal_set_z_mm(value);
  // @@protoc_insertion_point(field_set:recorder.TrackedItemCoordinates.z_mm)
}

// float x_px = 4;
inline void TrackedItemCoordinates::clear_x_px() {
  x_px_ = 0;
}
inline float TrackedItemCoordinates::_internal_x_px() const {
  return x_px_;
}
inline float TrackedItemCoordinates::x_px() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItemCoordinates.x_px)
  return _internal_x_px();
}
inline void TrackedItemCoordinates::_internal_set_x_px(float value) {
  
  x_px_ = value;
}
inline void TrackedItemCoordinates::set_x_px(float value) {
  _internal_set_x_px(value);
  // @@protoc_insertion_point(field_set:recorder.TrackedItemCoordinates.x_px)
}

// float y_px = 5;
inline void TrackedItemCoordinates::clear_y_px() {
  y_px_ = 0;
}
inline float TrackedItemCoordinates::_internal_y_px() const {
  return y_px_;
}
inline float TrackedItemCoordinates::y_px() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItemCoordinates.y_px)
  return _internal_y_px();
}
inline void TrackedItemCoordinates::_internal_set_y_px(float value) {
  
  y_px_ = value;
}
inline void TrackedItemCoordinates::set_y_px(float value) {
  _internal_set_y_px(value);
  // @@protoc_insertion_point(field_set:recorder.TrackedItemCoordinates.y_px)
}

// -------------------------------------------------------------------

// EmbeddingCategory

// string category = 1;
inline void EmbeddingCategory::clear_category() {
  category_.ClearToEmpty();
}
inline const std::string& EmbeddingCategory::category() const {
  // @@protoc_insertion_point(field_get:recorder.EmbeddingCategory.category)
  return _internal_category();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EmbeddingCategory::set_category(ArgT0&& arg0, ArgT... args) {
 
 category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:recorder.EmbeddingCategory.category)
}
inline std::string* EmbeddingCategory::mutable_category() {
  std::string* _s = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:recorder.EmbeddingCategory.category)
  return _s;
}
inline const std::string& EmbeddingCategory::_internal_category() const {
  return category_.Get();
}
inline void EmbeddingCategory::_internal_set_category(const std::string& value) {
  
  category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EmbeddingCategory::_internal_mutable_category() {
  
  return category_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EmbeddingCategory::release_category() {
  // @@protoc_insertion_point(field_release:recorder.EmbeddingCategory.category)
  return category_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EmbeddingCategory::set_allocated_category(std::string* category) {
  if (category != nullptr) {
    
  } else {
    
  }
  category_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), category,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (category_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:recorder.EmbeddingCategory.category)
}

// float distance = 2;
inline void EmbeddingCategory::clear_distance() {
  distance_ = 0;
}
inline float EmbeddingCategory::_internal_distance() const {
  return distance_;
}
inline float EmbeddingCategory::distance() const {
  // @@protoc_insertion_point(field_get:recorder.EmbeddingCategory.distance)
  return _internal_distance();
}
inline void EmbeddingCategory::_internal_set_distance(float value) {
  
  distance_ = value;
}
inline void EmbeddingCategory::set_distance(float value) {
  _internal_set_distance(value);
  // @@protoc_insertion_point(field_set:recorder.EmbeddingCategory.distance)
}

// -------------------------------------------------------------------

// TrackedItem

// uint32 trajectory_id = 1;
inline void TrackedItem::clear_trajectory_id() {
  trajectory_id_ = 0u;
}
inline uint32_t TrackedItem::_internal_trajectory_id() const {
  return trajectory_id_;
}
inline uint32_t TrackedItem::trajectory_id() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItem.trajectory_id)
  return _internal_trajectory_id();
}
inline void TrackedItem::_internal_set_trajectory_id(uint32_t value) {
  
  trajectory_id_ = value;
}
inline void TrackedItem::set_trajectory_id(uint32_t value) {
  _internal_set_trajectory_id(value);
  // @@protoc_insertion_point(field_set:recorder.TrackedItem.trajectory_id)
}

// bool deduplicated = 2;
inline void TrackedItem::clear_deduplicated() {
  deduplicated_ = false;
}
inline bool TrackedItem::_internal_deduplicated() const {
  return deduplicated_;
}
inline bool TrackedItem::deduplicated() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItem.deduplicated)
  return _internal_deduplicated();
}
inline void TrackedItem::_internal_set_deduplicated(bool value) {
  
  deduplicated_ = value;
}
inline void TrackedItem::set_deduplicated(bool value) {
  _internal_set_deduplicated(value);
  // @@protoc_insertion_point(field_set:recorder.TrackedItem.deduplicated)
}

// .recorder.TrackedItemCoordinates coordinates_before = 3;
inline bool TrackedItem::_internal_has_coordinates_before() const {
  return this != internal_default_instance() && coordinates_before_ != nullptr;
}
inline bool TrackedItem::has_coordinates_before() const {
  return _internal_has_coordinates_before();
}
inline void TrackedItem::clear_coordinates_before() {
  if (GetArenaForAllocation() == nullptr && coordinates_before_ != nullptr) {
    delete coordinates_before_;
  }
  coordinates_before_ = nullptr;
}
inline const ::recorder::TrackedItemCoordinates& TrackedItem::_internal_coordinates_before() const {
  const ::recorder::TrackedItemCoordinates* p = coordinates_before_;
  return p != nullptr ? *p : reinterpret_cast<const ::recorder::TrackedItemCoordinates&>(
      ::recorder::_TrackedItemCoordinates_default_instance_);
}
inline const ::recorder::TrackedItemCoordinates& TrackedItem::coordinates_before() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItem.coordinates_before)
  return _internal_coordinates_before();
}
inline void TrackedItem::unsafe_arena_set_allocated_coordinates_before(
    ::recorder::TrackedItemCoordinates* coordinates_before) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(coordinates_before_);
  }
  coordinates_before_ = coordinates_before;
  if (coordinates_before) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:recorder.TrackedItem.coordinates_before)
}
inline ::recorder::TrackedItemCoordinates* TrackedItem::release_coordinates_before() {
  
  ::recorder::TrackedItemCoordinates* temp = coordinates_before_;
  coordinates_before_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::recorder::TrackedItemCoordinates* TrackedItem::unsafe_arena_release_coordinates_before() {
  // @@protoc_insertion_point(field_release:recorder.TrackedItem.coordinates_before)
  
  ::recorder::TrackedItemCoordinates* temp = coordinates_before_;
  coordinates_before_ = nullptr;
  return temp;
}
inline ::recorder::TrackedItemCoordinates* TrackedItem::_internal_mutable_coordinates_before() {
  
  if (coordinates_before_ == nullptr) {
    auto* p = CreateMaybeMessage<::recorder::TrackedItemCoordinates>(GetArenaForAllocation());
    coordinates_before_ = p;
  }
  return coordinates_before_;
}
inline ::recorder::TrackedItemCoordinates* TrackedItem::mutable_coordinates_before() {
  ::recorder::TrackedItemCoordinates* _msg = _internal_mutable_coordinates_before();
  // @@protoc_insertion_point(field_mutable:recorder.TrackedItem.coordinates_before)
  return _msg;
}
inline void TrackedItem::set_allocated_coordinates_before(::recorder::TrackedItemCoordinates* coordinates_before) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete coordinates_before_;
  }
  if (coordinates_before) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::recorder::TrackedItemCoordinates>::GetOwningArena(coordinates_before);
    if (message_arena != submessage_arena) {
      coordinates_before = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, coordinates_before, submessage_arena);
    }
    
  } else {
    
  }
  coordinates_before_ = coordinates_before;
  // @@protoc_insertion_point(field_set_allocated:recorder.TrackedItem.coordinates_before)
}

// .recorder.TrackedItemCoordinates coordinates_after = 4;
inline bool TrackedItem::_internal_has_coordinates_after() const {
  return this != internal_default_instance() && coordinates_after_ != nullptr;
}
inline bool TrackedItem::has_coordinates_after() const {
  return _internal_has_coordinates_after();
}
inline void TrackedItem::clear_coordinates_after() {
  if (GetArenaForAllocation() == nullptr && coordinates_after_ != nullptr) {
    delete coordinates_after_;
  }
  coordinates_after_ = nullptr;
}
inline const ::recorder::TrackedItemCoordinates& TrackedItem::_internal_coordinates_after() const {
  const ::recorder::TrackedItemCoordinates* p = coordinates_after_;
  return p != nullptr ? *p : reinterpret_cast<const ::recorder::TrackedItemCoordinates&>(
      ::recorder::_TrackedItemCoordinates_default_instance_);
}
inline const ::recorder::TrackedItemCoordinates& TrackedItem::coordinates_after() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItem.coordinates_after)
  return _internal_coordinates_after();
}
inline void TrackedItem::unsafe_arena_set_allocated_coordinates_after(
    ::recorder::TrackedItemCoordinates* coordinates_after) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(coordinates_after_);
  }
  coordinates_after_ = coordinates_after;
  if (coordinates_after) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:recorder.TrackedItem.coordinates_after)
}
inline ::recorder::TrackedItemCoordinates* TrackedItem::release_coordinates_after() {
  
  ::recorder::TrackedItemCoordinates* temp = coordinates_after_;
  coordinates_after_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::recorder::TrackedItemCoordinates* TrackedItem::unsafe_arena_release_coordinates_after() {
  // @@protoc_insertion_point(field_release:recorder.TrackedItem.coordinates_after)
  
  ::recorder::TrackedItemCoordinates* temp = coordinates_after_;
  coordinates_after_ = nullptr;
  return temp;
}
inline ::recorder::TrackedItemCoordinates* TrackedItem::_internal_mutable_coordinates_after() {
  
  if (coordinates_after_ == nullptr) {
    auto* p = CreateMaybeMessage<::recorder::TrackedItemCoordinates>(GetArenaForAllocation());
    coordinates_after_ = p;
  }
  return coordinates_after_;
}
inline ::recorder::TrackedItemCoordinates* TrackedItem::mutable_coordinates_after() {
  ::recorder::TrackedItemCoordinates* _msg = _internal_mutable_coordinates_after();
  // @@protoc_insertion_point(field_mutable:recorder.TrackedItem.coordinates_after)
  return _msg;
}
inline void TrackedItem::set_allocated_coordinates_after(::recorder::TrackedItemCoordinates* coordinates_after) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete coordinates_after_;
  }
  if (coordinates_after) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::recorder::TrackedItemCoordinates>::GetOwningArena(coordinates_after);
    if (message_arena != submessage_arena) {
      coordinates_after = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, coordinates_after, submessage_arena);
    }
    
  } else {
    
  }
  coordinates_after_ = coordinates_after;
  // @@protoc_insertion_point(field_set_allocated:recorder.TrackedItem.coordinates_after)
}

// bool in_camera = 5;
inline void TrackedItem::clear_in_camera() {
  in_camera_ = false;
}
inline bool TrackedItem::_internal_in_camera() const {
  return in_camera_;
}
inline bool TrackedItem::in_camera() const {
  // @@protoc_insertion_point(field_get:recorder.TrackedItem.in_camera)
  return _internal_in_camera();
}
inline void TrackedItem::_internal_set_in_camera(bool value) {
  
  in_camera_ = value;
}
inline void TrackedItem::set_in_camera(bool value) {
  _internal_set_in_camera(value);
  // @@protoc_insertion_point(field_set:recorder.TrackedItem.in_camera)
}

// -------------------------------------------------------------------

// CandidateShift

// float x_shift = 1;
inline void CandidateShift::clear_x_shift() {
  x_shift_ = 0;
}
inline float CandidateShift::_internal_x_shift() const {
  return x_shift_;
}
inline float CandidateShift::x_shift() const {
  // @@protoc_insertion_point(field_get:recorder.CandidateShift.x_shift)
  return _internal_x_shift();
}
inline void CandidateShift::_internal_set_x_shift(float value) {
  
  x_shift_ = value;
}
inline void CandidateShift::set_x_shift(float value) {
  _internal_set_x_shift(value);
  // @@protoc_insertion_point(field_set:recorder.CandidateShift.x_shift)
}

// float y_shift = 2;
inline void CandidateShift::clear_y_shift() {
  y_shift_ = 0;
}
inline float CandidateShift::_internal_y_shift() const {
  return y_shift_;
}
inline float CandidateShift::y_shift() const {
  // @@protoc_insertion_point(field_get:recorder.CandidateShift.y_shift)
  return _internal_y_shift();
}
inline void CandidateShift::_internal_set_y_shift(float value) {
  
  y_shift_ = value;
}
inline void CandidateShift::set_y_shift(float value) {
  _internal_set_y_shift(value);
  // @@protoc_insertion_point(field_set:recorder.CandidateShift.y_shift)
}

// uint32 centroid_id = 3;
inline void CandidateShift::clear_centroid_id() {
  centroid_id_ = 0u;
}
inline uint32_t CandidateShift::_internal_centroid_id() const {
  return centroid_id_;
}
inline uint32_t CandidateShift::centroid_id() const {
  // @@protoc_insertion_point(field_get:recorder.CandidateShift.centroid_id)
  return _internal_centroid_id();
}
inline void CandidateShift::_internal_set_centroid_id(uint32_t value) {
  
  centroid_id_ = value;
}
inline void CandidateShift::set_centroid_id(uint32_t value) {
  _internal_set_centroid_id(value);
  // @@protoc_insertion_point(field_set:recorder.CandidateShift.centroid_id)
}

// uint32 trajectory_id = 4;
inline void CandidateShift::clear_trajectory_id() {
  trajectory_id_ = 0u;
}
inline uint32_t CandidateShift::_internal_trajectory_id() const {
  return trajectory_id_;
}
inline uint32_t CandidateShift::trajectory_id() const {
  // @@protoc_insertion_point(field_get:recorder.CandidateShift.trajectory_id)
  return _internal_trajectory_id();
}
inline void CandidateShift::_internal_set_trajectory_id(uint32_t value) {
  
  trajectory_id_ = value;
}
inline void CandidateShift::set_trajectory_id(uint32_t value) {
  _internal_set_trajectory_id(value);
  // @@protoc_insertion_point(field_set:recorder.CandidateShift.trajectory_id)
}

// -------------------------------------------------------------------

// DeepweedPredictionFrame

// int64 timestamp_ms = 1;
inline void DeepweedPredictionFrame::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t DeepweedPredictionFrame::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t DeepweedPredictionFrame::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionFrame.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void DeepweedPredictionFrame::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void DeepweedPredictionFrame::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedPredictionFrame.timestamp_ms)
}

// repeated .recorder.DeepweedDetection detections = 2;
inline int DeepweedPredictionFrame::_internal_detections_size() const {
  return detections_.size();
}
inline int DeepweedPredictionFrame::detections_size() const {
  return _internal_detections_size();
}
inline void DeepweedPredictionFrame::clear_detections() {
  detections_.Clear();
}
inline ::recorder::DeepweedDetection* DeepweedPredictionFrame::mutable_detections(int index) {
  // @@protoc_insertion_point(field_mutable:recorder.DeepweedPredictionFrame.detections)
  return detections_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DeepweedDetection >*
DeepweedPredictionFrame::mutable_detections() {
  // @@protoc_insertion_point(field_mutable_list:recorder.DeepweedPredictionFrame.detections)
  return &detections_;
}
inline const ::recorder::DeepweedDetection& DeepweedPredictionFrame::_internal_detections(int index) const {
  return detections_.Get(index);
}
inline const ::recorder::DeepweedDetection& DeepweedPredictionFrame::detections(int index) const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionFrame.detections)
  return _internal_detections(index);
}
inline ::recorder::DeepweedDetection* DeepweedPredictionFrame::_internal_add_detections() {
  return detections_.Add();
}
inline ::recorder::DeepweedDetection* DeepweedPredictionFrame::add_detections() {
  ::recorder::DeepweedDetection* _add = _internal_add_detections();
  // @@protoc_insertion_point(field_add:recorder.DeepweedPredictionFrame.detections)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::DeepweedDetection >&
DeepweedPredictionFrame::detections() const {
  // @@protoc_insertion_point(field_list:recorder.DeepweedPredictionFrame.detections)
  return detections_;
}

// repeated string embedding_categories = 3;
inline int DeepweedPredictionFrame::_internal_embedding_categories_size() const {
  return embedding_categories_.size();
}
inline int DeepweedPredictionFrame::embedding_categories_size() const {
  return _internal_embedding_categories_size();
}
inline void DeepweedPredictionFrame::clear_embedding_categories() {
  embedding_categories_.Clear();
}
inline std::string* DeepweedPredictionFrame::add_embedding_categories() {
  std::string* _s = _internal_add_embedding_categories();
  // @@protoc_insertion_point(field_add_mutable:recorder.DeepweedPredictionFrame.embedding_categories)
  return _s;
}
inline const std::string& DeepweedPredictionFrame::_internal_embedding_categories(int index) const {
  return embedding_categories_.Get(index);
}
inline const std::string& DeepweedPredictionFrame::embedding_categories(int index) const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionFrame.embedding_categories)
  return _internal_embedding_categories(index);
}
inline std::string* DeepweedPredictionFrame::mutable_embedding_categories(int index) {
  // @@protoc_insertion_point(field_mutable:recorder.DeepweedPredictionFrame.embedding_categories)
  return embedding_categories_.Mutable(index);
}
inline void DeepweedPredictionFrame::set_embedding_categories(int index, const std::string& value) {
  embedding_categories_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedPredictionFrame.embedding_categories)
}
inline void DeepweedPredictionFrame::set_embedding_categories(int index, std::string&& value) {
  embedding_categories_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:recorder.DeepweedPredictionFrame.embedding_categories)
}
inline void DeepweedPredictionFrame::set_embedding_categories(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  embedding_categories_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:recorder.DeepweedPredictionFrame.embedding_categories)
}
inline void DeepweedPredictionFrame::set_embedding_categories(int index, const char* value, size_t size) {
  embedding_categories_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:recorder.DeepweedPredictionFrame.embedding_categories)
}
inline std::string* DeepweedPredictionFrame::_internal_add_embedding_categories() {
  return embedding_categories_.Add();
}
inline void DeepweedPredictionFrame::add_embedding_categories(const std::string& value) {
  embedding_categories_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:recorder.DeepweedPredictionFrame.embedding_categories)
}
inline void DeepweedPredictionFrame::add_embedding_categories(std::string&& value) {
  embedding_categories_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:recorder.DeepweedPredictionFrame.embedding_categories)
}
inline void DeepweedPredictionFrame::add_embedding_categories(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  embedding_categories_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:recorder.DeepweedPredictionFrame.embedding_categories)
}
inline void DeepweedPredictionFrame::add_embedding_categories(const char* value, size_t size) {
  embedding_categories_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:recorder.DeepweedPredictionFrame.embedding_categories)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DeepweedPredictionFrame::embedding_categories() const {
  // @@protoc_insertion_point(field_list:recorder.DeepweedPredictionFrame.embedding_categories)
  return embedding_categories_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DeepweedPredictionFrame::mutable_embedding_categories() {
  // @@protoc_insertion_point(field_mutable_list:recorder.DeepweedPredictionFrame.embedding_categories)
  return &embedding_categories_;
}

// repeated .recorder.TrackedItem tracked_items = 4;
inline int DeepweedPredictionFrame::_internal_tracked_items_size() const {
  return tracked_items_.size();
}
inline int DeepweedPredictionFrame::tracked_items_size() const {
  return _internal_tracked_items_size();
}
inline void DeepweedPredictionFrame::clear_tracked_items() {
  tracked_items_.Clear();
}
inline ::recorder::TrackedItem* DeepweedPredictionFrame::mutable_tracked_items(int index) {
  // @@protoc_insertion_point(field_mutable:recorder.DeepweedPredictionFrame.tracked_items)
  return tracked_items_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::TrackedItem >*
DeepweedPredictionFrame::mutable_tracked_items() {
  // @@protoc_insertion_point(field_mutable_list:recorder.DeepweedPredictionFrame.tracked_items)
  return &tracked_items_;
}
inline const ::recorder::TrackedItem& DeepweedPredictionFrame::_internal_tracked_items(int index) const {
  return tracked_items_.Get(index);
}
inline const ::recorder::TrackedItem& DeepweedPredictionFrame::tracked_items(int index) const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionFrame.tracked_items)
  return _internal_tracked_items(index);
}
inline ::recorder::TrackedItem* DeepweedPredictionFrame::_internal_add_tracked_items() {
  return tracked_items_.Add();
}
inline ::recorder::TrackedItem* DeepweedPredictionFrame::add_tracked_items() {
  ::recorder::TrackedItem* _add = _internal_add_tracked_items();
  // @@protoc_insertion_point(field_add:recorder.DeepweedPredictionFrame.tracked_items)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::TrackedItem >&
DeepweedPredictionFrame::tracked_items() const {
  // @@protoc_insertion_point(field_list:recorder.DeepweedPredictionFrame.tracked_items)
  return tracked_items_;
}

// .recorder.CandidateShift best_shift = 5;
inline bool DeepweedPredictionFrame::_internal_has_best_shift() const {
  return this != internal_default_instance() && best_shift_ != nullptr;
}
inline bool DeepweedPredictionFrame::has_best_shift() const {
  return _internal_has_best_shift();
}
inline void DeepweedPredictionFrame::clear_best_shift() {
  if (GetArenaForAllocation() == nullptr && best_shift_ != nullptr) {
    delete best_shift_;
  }
  best_shift_ = nullptr;
}
inline const ::recorder::CandidateShift& DeepweedPredictionFrame::_internal_best_shift() const {
  const ::recorder::CandidateShift* p = best_shift_;
  return p != nullptr ? *p : reinterpret_cast<const ::recorder::CandidateShift&>(
      ::recorder::_CandidateShift_default_instance_);
}
inline const ::recorder::CandidateShift& DeepweedPredictionFrame::best_shift() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionFrame.best_shift)
  return _internal_best_shift();
}
inline void DeepweedPredictionFrame::unsafe_arena_set_allocated_best_shift(
    ::recorder::CandidateShift* best_shift) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(best_shift_);
  }
  best_shift_ = best_shift;
  if (best_shift) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:recorder.DeepweedPredictionFrame.best_shift)
}
inline ::recorder::CandidateShift* DeepweedPredictionFrame::release_best_shift() {
  
  ::recorder::CandidateShift* temp = best_shift_;
  best_shift_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::recorder::CandidateShift* DeepweedPredictionFrame::unsafe_arena_release_best_shift() {
  // @@protoc_insertion_point(field_release:recorder.DeepweedPredictionFrame.best_shift)
  
  ::recorder::CandidateShift* temp = best_shift_;
  best_shift_ = nullptr;
  return temp;
}
inline ::recorder::CandidateShift* DeepweedPredictionFrame::_internal_mutable_best_shift() {
  
  if (best_shift_ == nullptr) {
    auto* p = CreateMaybeMessage<::recorder::CandidateShift>(GetArenaForAllocation());
    best_shift_ = p;
  }
  return best_shift_;
}
inline ::recorder::CandidateShift* DeepweedPredictionFrame::mutable_best_shift() {
  ::recorder::CandidateShift* _msg = _internal_mutable_best_shift();
  // @@protoc_insertion_point(field_mutable:recorder.DeepweedPredictionFrame.best_shift)
  return _msg;
}
inline void DeepweedPredictionFrame::set_allocated_best_shift(::recorder::CandidateShift* best_shift) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete best_shift_;
  }
  if (best_shift) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::recorder::CandidateShift>::GetOwningArena(best_shift);
    if (message_arena != submessage_arena) {
      best_shift = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, best_shift, submessage_arena);
    }
    
  } else {
    
  }
  best_shift_ = best_shift;
  // @@protoc_insertion_point(field_set_allocated:recorder.DeepweedPredictionFrame.best_shift)
}

// repeated .recorder.CandidateShift candidate_shifts = 6;
inline int DeepweedPredictionFrame::_internal_candidate_shifts_size() const {
  return candidate_shifts_.size();
}
inline int DeepweedPredictionFrame::candidate_shifts_size() const {
  return _internal_candidate_shifts_size();
}
inline void DeepweedPredictionFrame::clear_candidate_shifts() {
  candidate_shifts_.Clear();
}
inline ::recorder::CandidateShift* DeepweedPredictionFrame::mutable_candidate_shifts(int index) {
  // @@protoc_insertion_point(field_mutable:recorder.DeepweedPredictionFrame.candidate_shifts)
  return candidate_shifts_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::CandidateShift >*
DeepweedPredictionFrame::mutable_candidate_shifts() {
  // @@protoc_insertion_point(field_mutable_list:recorder.DeepweedPredictionFrame.candidate_shifts)
  return &candidate_shifts_;
}
inline const ::recorder::CandidateShift& DeepweedPredictionFrame::_internal_candidate_shifts(int index) const {
  return candidate_shifts_.Get(index);
}
inline const ::recorder::CandidateShift& DeepweedPredictionFrame::candidate_shifts(int index) const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionFrame.candidate_shifts)
  return _internal_candidate_shifts(index);
}
inline ::recorder::CandidateShift* DeepweedPredictionFrame::_internal_add_candidate_shifts() {
  return candidate_shifts_.Add();
}
inline ::recorder::CandidateShift* DeepweedPredictionFrame::add_candidate_shifts() {
  ::recorder::CandidateShift* _add = _internal_add_candidate_shifts();
  // @@protoc_insertion_point(field_add:recorder.DeepweedPredictionFrame.candidate_shifts)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::recorder::CandidateShift >&
DeepweedPredictionFrame::candidate_shifts() const {
  // @@protoc_insertion_point(field_list:recorder.DeepweedPredictionFrame.candidate_shifts)
  return candidate_shifts_;
}

// bool plant_matcher_valid = 7;
inline void DeepweedPredictionFrame::clear_plant_matcher_valid() {
  plant_matcher_valid_ = false;
}
inline bool DeepweedPredictionFrame::_internal_plant_matcher_valid() const {
  return plant_matcher_valid_;
}
inline bool DeepweedPredictionFrame::plant_matcher_valid() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionFrame.plant_matcher_valid)
  return _internal_plant_matcher_valid();
}
inline void DeepweedPredictionFrame::_internal_set_plant_matcher_valid(bool value) {
  
  plant_matcher_valid_ = value;
}
inline void DeepweedPredictionFrame::set_plant_matcher_valid(bool value) {
  _internal_set_plant_matcher_valid(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedPredictionFrame.plant_matcher_valid)
}

// -------------------------------------------------------------------

// DeepweedPredictionRecord

// uint64 record_timestamp_ms = 1;
inline void DeepweedPredictionRecord::clear_record_timestamp_ms() {
  record_timestamp_ms_ = uint64_t{0u};
}
inline uint64_t DeepweedPredictionRecord::_internal_record_timestamp_ms() const {
  return record_timestamp_ms_;
}
inline uint64_t DeepweedPredictionRecord::record_timestamp_ms() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionRecord.record_timestamp_ms)
  return _internal_record_timestamp_ms();
}
inline void DeepweedPredictionRecord::_internal_set_record_timestamp_ms(uint64_t value) {
  
  record_timestamp_ms_ = value;
}
inline void DeepweedPredictionRecord::set_record_timestamp_ms(uint64_t value) {
  _internal_set_record_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:recorder.DeepweedPredictionRecord.record_timestamp_ms)
}

// .recorder.DeepweedPredictionFrame frame = 2;
inline bool DeepweedPredictionRecord::_internal_has_frame() const {
  return this != internal_default_instance() && frame_ != nullptr;
}
inline bool DeepweedPredictionRecord::has_frame() const {
  return _internal_has_frame();
}
inline void DeepweedPredictionRecord::clear_frame() {
  if (GetArenaForAllocation() == nullptr && frame_ != nullptr) {
    delete frame_;
  }
  frame_ = nullptr;
}
inline const ::recorder::DeepweedPredictionFrame& DeepweedPredictionRecord::_internal_frame() const {
  const ::recorder::DeepweedPredictionFrame* p = frame_;
  return p != nullptr ? *p : reinterpret_cast<const ::recorder::DeepweedPredictionFrame&>(
      ::recorder::_DeepweedPredictionFrame_default_instance_);
}
inline const ::recorder::DeepweedPredictionFrame& DeepweedPredictionRecord::frame() const {
  // @@protoc_insertion_point(field_get:recorder.DeepweedPredictionRecord.frame)
  return _internal_frame();
}
inline void DeepweedPredictionRecord::unsafe_arena_set_allocated_frame(
    ::recorder::DeepweedPredictionFrame* frame) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(frame_);
  }
  frame_ = frame;
  if (frame) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:recorder.DeepweedPredictionRecord.frame)
}
inline ::recorder::DeepweedPredictionFrame* DeepweedPredictionRecord::release_frame() {
  
  ::recorder::DeepweedPredictionFrame* temp = frame_;
  frame_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::recorder::DeepweedPredictionFrame* DeepweedPredictionRecord::unsafe_arena_release_frame() {
  // @@protoc_insertion_point(field_release:recorder.DeepweedPredictionRecord.frame)
  
  ::recorder::DeepweedPredictionFrame* temp = frame_;
  frame_ = nullptr;
  return temp;
}
inline ::recorder::DeepweedPredictionFrame* DeepweedPredictionRecord::_internal_mutable_frame() {
  
  if (frame_ == nullptr) {
    auto* p = CreateMaybeMessage<::recorder::DeepweedPredictionFrame>(GetArenaForAllocation());
    frame_ = p;
  }
  return frame_;
}
inline ::recorder::DeepweedPredictionFrame* DeepweedPredictionRecord::mutable_frame() {
  ::recorder::DeepweedPredictionFrame* _msg = _internal_mutable_frame();
  // @@protoc_insertion_point(field_mutable:recorder.DeepweedPredictionRecord.frame)
  return _msg;
}
inline void DeepweedPredictionRecord::set_allocated_frame(::recorder::DeepweedPredictionFrame* frame) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete frame_;
  }
  if (frame) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::recorder::DeepweedPredictionFrame>::GetOwningArena(frame);
    if (message_arena != submessage_arena) {
      frame = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, frame, submessage_arena);
    }
    
  } else {
    
  }
  frame_ = frame;
  // @@protoc_insertion_point(field_set_allocated:recorder.DeepweedPredictionRecord.frame)
}

// -------------------------------------------------------------------

// LaneHeightSnapshot

// repeated double weed_height = 1;
inline int LaneHeightSnapshot::_internal_weed_height_size() const {
  return weed_height_.size();
}
inline int LaneHeightSnapshot::weed_height_size() const {
  return _internal_weed_height_size();
}
inline void LaneHeightSnapshot::clear_weed_height() {
  weed_height_.Clear();
}
inline double LaneHeightSnapshot::_internal_weed_height(int index) const {
  return weed_height_.Get(index);
}
inline double LaneHeightSnapshot::weed_height(int index) const {
  // @@protoc_insertion_point(field_get:recorder.LaneHeightSnapshot.weed_height)
  return _internal_weed_height(index);
}
inline void LaneHeightSnapshot::set_weed_height(int index, double value) {
  weed_height_.Set(index, value);
  // @@protoc_insertion_point(field_set:recorder.LaneHeightSnapshot.weed_height)
}
inline void LaneHeightSnapshot::_internal_add_weed_height(double value) {
  weed_height_.Add(value);
}
inline void LaneHeightSnapshot::add_weed_height(double value) {
  _internal_add_weed_height(value);
  // @@protoc_insertion_point(field_add:recorder.LaneHeightSnapshot.weed_height)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
LaneHeightSnapshot::_internal_weed_height() const {
  return weed_height_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
LaneHeightSnapshot::weed_height() const {
  // @@protoc_insertion_point(field_list:recorder.LaneHeightSnapshot.weed_height)
  return _internal_weed_height();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
LaneHeightSnapshot::_internal_mutable_weed_height() {
  return &weed_height_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
LaneHeightSnapshot::mutable_weed_height() {
  // @@protoc_insertion_point(field_mutable_list:recorder.LaneHeightSnapshot.weed_height)
  return _internal_mutable_weed_height();
}

// repeated double crop_height = 2;
inline int LaneHeightSnapshot::_internal_crop_height_size() const {
  return crop_height_.size();
}
inline int LaneHeightSnapshot::crop_height_size() const {
  return _internal_crop_height_size();
}
inline void LaneHeightSnapshot::clear_crop_height() {
  crop_height_.Clear();
}
inline double LaneHeightSnapshot::_internal_crop_height(int index) const {
  return crop_height_.Get(index);
}
inline double LaneHeightSnapshot::crop_height(int index) const {
  // @@protoc_insertion_point(field_get:recorder.LaneHeightSnapshot.crop_height)
  return _internal_crop_height(index);
}
inline void LaneHeightSnapshot::set_crop_height(int index, double value) {
  crop_height_.Set(index, value);
  // @@protoc_insertion_point(field_set:recorder.LaneHeightSnapshot.crop_height)
}
inline void LaneHeightSnapshot::_internal_add_crop_height(double value) {
  crop_height_.Add(value);
}
inline void LaneHeightSnapshot::add_crop_height(double value) {
  _internal_add_crop_height(value);
  // @@protoc_insertion_point(field_add:recorder.LaneHeightSnapshot.crop_height)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
LaneHeightSnapshot::_internal_crop_height() const {
  return crop_height_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
LaneHeightSnapshot::crop_height() const {
  // @@protoc_insertion_point(field_list:recorder.LaneHeightSnapshot.crop_height)
  return _internal_crop_height();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
LaneHeightSnapshot::_internal_mutable_crop_height() {
  return &crop_height_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
LaneHeightSnapshot::mutable_crop_height() {
  // @@protoc_insertion_point(field_mutable_list:recorder.LaneHeightSnapshot.crop_height)
  return _internal_mutable_crop_height();
}

// -------------------------------------------------------------------

// LaneHeightRecord

// uint64 record_timestamp_ms = 1;
inline void LaneHeightRecord::clear_record_timestamp_ms() {
  record_timestamp_ms_ = uint64_t{0u};
}
inline uint64_t LaneHeightRecord::_internal_record_timestamp_ms() const {
  return record_timestamp_ms_;
}
inline uint64_t LaneHeightRecord::record_timestamp_ms() const {
  // @@protoc_insertion_point(field_get:recorder.LaneHeightRecord.record_timestamp_ms)
  return _internal_record_timestamp_ms();
}
inline void LaneHeightRecord::_internal_set_record_timestamp_ms(uint64_t value) {
  
  record_timestamp_ms_ = value;
}
inline void LaneHeightRecord::set_record_timestamp_ms(uint64_t value) {
  _internal_set_record_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:recorder.LaneHeightRecord.record_timestamp_ms)
}

// .recorder.LaneHeightSnapshot snapshot = 2;
inline bool LaneHeightRecord::_internal_has_snapshot() const {
  return this != internal_default_instance() && snapshot_ != nullptr;
}
inline bool LaneHeightRecord::has_snapshot() const {
  return _internal_has_snapshot();
}
inline void LaneHeightRecord::clear_snapshot() {
  if (GetArenaForAllocation() == nullptr && snapshot_ != nullptr) {
    delete snapshot_;
  }
  snapshot_ = nullptr;
}
inline const ::recorder::LaneHeightSnapshot& LaneHeightRecord::_internal_snapshot() const {
  const ::recorder::LaneHeightSnapshot* p = snapshot_;
  return p != nullptr ? *p : reinterpret_cast<const ::recorder::LaneHeightSnapshot&>(
      ::recorder::_LaneHeightSnapshot_default_instance_);
}
inline const ::recorder::LaneHeightSnapshot& LaneHeightRecord::snapshot() const {
  // @@protoc_insertion_point(field_get:recorder.LaneHeightRecord.snapshot)
  return _internal_snapshot();
}
inline void LaneHeightRecord::unsafe_arena_set_allocated_snapshot(
    ::recorder::LaneHeightSnapshot* snapshot) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(snapshot_);
  }
  snapshot_ = snapshot;
  if (snapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:recorder.LaneHeightRecord.snapshot)
}
inline ::recorder::LaneHeightSnapshot* LaneHeightRecord::release_snapshot() {
  
  ::recorder::LaneHeightSnapshot* temp = snapshot_;
  snapshot_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::recorder::LaneHeightSnapshot* LaneHeightRecord::unsafe_arena_release_snapshot() {
  // @@protoc_insertion_point(field_release:recorder.LaneHeightRecord.snapshot)
  
  ::recorder::LaneHeightSnapshot* temp = snapshot_;
  snapshot_ = nullptr;
  return temp;
}
inline ::recorder::LaneHeightSnapshot* LaneHeightRecord::_internal_mutable_snapshot() {
  
  if (snapshot_ == nullptr) {
    auto* p = CreateMaybeMessage<::recorder::LaneHeightSnapshot>(GetArenaForAllocation());
    snapshot_ = p;
  }
  return snapshot_;
}
inline ::recorder::LaneHeightSnapshot* LaneHeightRecord::mutable_snapshot() {
  ::recorder::LaneHeightSnapshot* _msg = _internal_mutable_snapshot();
  // @@protoc_insertion_point(field_mutable:recorder.LaneHeightRecord.snapshot)
  return _msg;
}
inline void LaneHeightRecord::set_allocated_snapshot(::recorder::LaneHeightSnapshot* snapshot) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete snapshot_;
  }
  if (snapshot) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::recorder::LaneHeightSnapshot>::GetOwningArena(snapshot);
    if (message_arena != submessage_arena) {
      snapshot = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, snapshot, submessage_arena);
    }
    
  } else {
    
  }
  snapshot_ = snapshot;
  // @@protoc_insertion_point(field_set_allocated:recorder.LaneHeightRecord.snapshot)
}

// -------------------------------------------------------------------

// RotaryTicksSnapshot

// uint64 timestamp_us = 1;
inline void RotaryTicksSnapshot::clear_timestamp_us() {
  timestamp_us_ = uint64_t{0u};
}
inline uint64_t RotaryTicksSnapshot::_internal_timestamp_us() const {
  return timestamp_us_;
}
inline uint64_t RotaryTicksSnapshot::timestamp_us() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.timestamp_us)
  return _internal_timestamp_us();
}
inline void RotaryTicksSnapshot::_internal_set_timestamp_us(uint64_t value) {
  
  timestamp_us_ = value;
}
inline void RotaryTicksSnapshot::set_timestamp_us(uint64_t value) {
  _internal_set_timestamp_us(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.timestamp_us)
}

// int32 fl = 2;
inline void RotaryTicksSnapshot::clear_fl() {
  fl_ = 0;
}
inline int32_t RotaryTicksSnapshot::_internal_fl() const {
  return fl_;
}
inline int32_t RotaryTicksSnapshot::fl() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.fl)
  return _internal_fl();
}
inline void RotaryTicksSnapshot::_internal_set_fl(int32_t value) {
  
  fl_ = value;
}
inline void RotaryTicksSnapshot::set_fl(int32_t value) {
  _internal_set_fl(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.fl)
}

// int32 fr = 3;
inline void RotaryTicksSnapshot::clear_fr() {
  fr_ = 0;
}
inline int32_t RotaryTicksSnapshot::_internal_fr() const {
  return fr_;
}
inline int32_t RotaryTicksSnapshot::fr() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.fr)
  return _internal_fr();
}
inline void RotaryTicksSnapshot::_internal_set_fr(int32_t value) {
  
  fr_ = value;
}
inline void RotaryTicksSnapshot::set_fr(int32_t value) {
  _internal_set_fr(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.fr)
}

// int32 bl = 4;
inline void RotaryTicksSnapshot::clear_bl() {
  bl_ = 0;
}
inline int32_t RotaryTicksSnapshot::_internal_bl() const {
  return bl_;
}
inline int32_t RotaryTicksSnapshot::bl() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.bl)
  return _internal_bl();
}
inline void RotaryTicksSnapshot::_internal_set_bl(int32_t value) {
  
  bl_ = value;
}
inline void RotaryTicksSnapshot::set_bl(int32_t value) {
  _internal_set_bl(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.bl)
}

// int32 br = 5;
inline void RotaryTicksSnapshot::clear_br() {
  br_ = 0;
}
inline int32_t RotaryTicksSnapshot::_internal_br() const {
  return br_;
}
inline int32_t RotaryTicksSnapshot::br() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.br)
  return _internal_br();
}
inline void RotaryTicksSnapshot::_internal_set_br(int32_t value) {
  
  br_ = value;
}
inline void RotaryTicksSnapshot::set_br(int32_t value) {
  _internal_set_br(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.br)
}

// bool fl_enabled = 6;
inline void RotaryTicksSnapshot::clear_fl_enabled() {
  fl_enabled_ = false;
}
inline bool RotaryTicksSnapshot::_internal_fl_enabled() const {
  return fl_enabled_;
}
inline bool RotaryTicksSnapshot::fl_enabled() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.fl_enabled)
  return _internal_fl_enabled();
}
inline void RotaryTicksSnapshot::_internal_set_fl_enabled(bool value) {
  
  fl_enabled_ = value;
}
inline void RotaryTicksSnapshot::set_fl_enabled(bool value) {
  _internal_set_fl_enabled(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.fl_enabled)
}

// bool fr_enabled = 7;
inline void RotaryTicksSnapshot::clear_fr_enabled() {
  fr_enabled_ = false;
}
inline bool RotaryTicksSnapshot::_internal_fr_enabled() const {
  return fr_enabled_;
}
inline bool RotaryTicksSnapshot::fr_enabled() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.fr_enabled)
  return _internal_fr_enabled();
}
inline void RotaryTicksSnapshot::_internal_set_fr_enabled(bool value) {
  
  fr_enabled_ = value;
}
inline void RotaryTicksSnapshot::set_fr_enabled(bool value) {
  _internal_set_fr_enabled(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.fr_enabled)
}

// bool bl_enabled = 8;
inline void RotaryTicksSnapshot::clear_bl_enabled() {
  bl_enabled_ = false;
}
inline bool RotaryTicksSnapshot::_internal_bl_enabled() const {
  return bl_enabled_;
}
inline bool RotaryTicksSnapshot::bl_enabled() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.bl_enabled)
  return _internal_bl_enabled();
}
inline void RotaryTicksSnapshot::_internal_set_bl_enabled(bool value) {
  
  bl_enabled_ = value;
}
inline void RotaryTicksSnapshot::set_bl_enabled(bool value) {
  _internal_set_bl_enabled(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.bl_enabled)
}

// bool br_enabled = 9;
inline void RotaryTicksSnapshot::clear_br_enabled() {
  br_enabled_ = false;
}
inline bool RotaryTicksSnapshot::_internal_br_enabled() const {
  return br_enabled_;
}
inline bool RotaryTicksSnapshot::br_enabled() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksSnapshot.br_enabled)
  return _internal_br_enabled();
}
inline void RotaryTicksSnapshot::_internal_set_br_enabled(bool value) {
  
  br_enabled_ = value;
}
inline void RotaryTicksSnapshot::set_br_enabled(bool value) {
  _internal_set_br_enabled(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksSnapshot.br_enabled)
}

// -------------------------------------------------------------------

// RotaryTicksRecord

// uint64 record_timestamp_ms = 1;
inline void RotaryTicksRecord::clear_record_timestamp_ms() {
  record_timestamp_ms_ = uint64_t{0u};
}
inline uint64_t RotaryTicksRecord::_internal_record_timestamp_ms() const {
  return record_timestamp_ms_;
}
inline uint64_t RotaryTicksRecord::record_timestamp_ms() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksRecord.record_timestamp_ms)
  return _internal_record_timestamp_ms();
}
inline void RotaryTicksRecord::_internal_set_record_timestamp_ms(uint64_t value) {
  
  record_timestamp_ms_ = value;
}
inline void RotaryTicksRecord::set_record_timestamp_ms(uint64_t value) {
  _internal_set_record_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:recorder.RotaryTicksRecord.record_timestamp_ms)
}

// .recorder.RotaryTicksSnapshot snapshot = 2;
inline bool RotaryTicksRecord::_internal_has_snapshot() const {
  return this != internal_default_instance() && snapshot_ != nullptr;
}
inline bool RotaryTicksRecord::has_snapshot() const {
  return _internal_has_snapshot();
}
inline void RotaryTicksRecord::clear_snapshot() {
  if (GetArenaForAllocation() == nullptr && snapshot_ != nullptr) {
    delete snapshot_;
  }
  snapshot_ = nullptr;
}
inline const ::recorder::RotaryTicksSnapshot& RotaryTicksRecord::_internal_snapshot() const {
  const ::recorder::RotaryTicksSnapshot* p = snapshot_;
  return p != nullptr ? *p : reinterpret_cast<const ::recorder::RotaryTicksSnapshot&>(
      ::recorder::_RotaryTicksSnapshot_default_instance_);
}
inline const ::recorder::RotaryTicksSnapshot& RotaryTicksRecord::snapshot() const {
  // @@protoc_insertion_point(field_get:recorder.RotaryTicksRecord.snapshot)
  return _internal_snapshot();
}
inline void RotaryTicksRecord::unsafe_arena_set_allocated_snapshot(
    ::recorder::RotaryTicksSnapshot* snapshot) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(snapshot_);
  }
  snapshot_ = snapshot;
  if (snapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:recorder.RotaryTicksRecord.snapshot)
}
inline ::recorder::RotaryTicksSnapshot* RotaryTicksRecord::release_snapshot() {
  
  ::recorder::RotaryTicksSnapshot* temp = snapshot_;
  snapshot_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::recorder::RotaryTicksSnapshot* RotaryTicksRecord::unsafe_arena_release_snapshot() {
  // @@protoc_insertion_point(field_release:recorder.RotaryTicksRecord.snapshot)
  
  ::recorder::RotaryTicksSnapshot* temp = snapshot_;
  snapshot_ = nullptr;
  return temp;
}
inline ::recorder::RotaryTicksSnapshot* RotaryTicksRecord::_internal_mutable_snapshot() {
  
  if (snapshot_ == nullptr) {
    auto* p = CreateMaybeMessage<::recorder::RotaryTicksSnapshot>(GetArenaForAllocation());
    snapshot_ = p;
  }
  return snapshot_;
}
inline ::recorder::RotaryTicksSnapshot* RotaryTicksRecord::mutable_snapshot() {
  ::recorder::RotaryTicksSnapshot* _msg = _internal_mutable_snapshot();
  // @@protoc_insertion_point(field_mutable:recorder.RotaryTicksRecord.snapshot)
  return _msg;
}
inline void RotaryTicksRecord::set_allocated_snapshot(::recorder::RotaryTicksSnapshot* snapshot) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete snapshot_;
  }
  if (snapshot) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::recorder::RotaryTicksSnapshot>::GetOwningArena(snapshot);
    if (message_arena != submessage_arena) {
      snapshot = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, snapshot, submessage_arena);
    }
    
  } else {
    
  }
  snapshot_ = snapshot;
  // @@protoc_insertion_point(field_set_allocated:recorder.RotaryTicksRecord.snapshot)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace recorder

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::recorder::DeepweedDetection_HitClass> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::recorder::DeepweedDetection_HitClass>() {
  return ::recorder::DeepweedDetection_HitClass_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_recorder_2fproto_2frecorder_2eproto
