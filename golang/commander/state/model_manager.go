package state

/*
See Model Managers documentation in Confluence
https://carbonrobotics.atlassian.net/wiki/spaces/Engineering/pages/552370183/Model+Manager
*/

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	hw_proto "github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	"github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/carbonrobotics/robot/golang/lib/model_manager"
	crredis "github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/go-redis/redis/v8"
	"github.com/mmcloughlin/geohash"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

const (
	allCropsCacheFilename              = "all_crops_cache.json"
	requestTimeout                     = 5 * time.Minute
	redisTimeout                       = 10 * time.Second
	defaultP2PModelRefreshInterval     = 8 * time.Hour // how often we should check for new default p2p
	modelSyncInterval                  = 30 * time.Second
	defaultRecommendationInterval      = 6 * time.Hour
	minRecommendationInterval          = 30 * time.Minute
	downloadTriggerInterval            = 30 * time.Minute
	modelCleanUpInterval               = 30 * time.Minute
	geohashPrecision                   = 12
	modelBatchSize                     = 10
	pointCategoriesRetryInitialBackoff = 30 * time.Second
	pointCategoriesRetryMaxBackoff     = 5 * time.Minute
	pointCategoriesSyncInterval        = 6 * time.Hour

	DefaultWeedPointThreshold = 0.5
	DefaultCropPointThreshold = 0.5

	commonNodeName                 = "common"
	commanderNodeName              = "commander"
	cropNameOverrideNodeName       = "crop_name_override"
	enabledNodeName                = "enabled"
	pinnedModelNodeName            = "pinned_model"
	recommendedModelNodeName       = "recommended_model"
	lastActiveModelNodeName        = "last_active_model"
	maintainedModelsNodeName       = "maintained_models"
	currentCropIDNodeName          = "current_crop_id"
	cropIDsNodeName                = "crop_ids"
	pinnedNonCropModelsNodeName    = "pinned_noncrop_models"
	p2pNodeName                    = "p2p"
	deepweedNodeName               = "deepweed"
	modelIDNodeName                = "model_id"
	cropPointThresholdNodeName     = "crop_point_threshold"
	weedPointThresholdNodeName     = "weed_point_threshold"
	modelExpirationDaysNodeName    = "model_expiration_days"
	recommendationIntervalNodeName = "recommendation_interval"
	logLevelNodeName               = "log_level"
	pointCategoriesNodeName        = "almanac/point_categories"

	modelParametersHashKey = "commander:model:parameters"
	modelNicknameKeyPrefix = "commander:model:nickname:"
)

var (
	ModelManagerFs = afero.NewOsFs()
	appModelLogger = logrus.New()

	// test point
	configGetComputerConfigPrefix = config.GetComputerConfigPrefix

	ErrNotFound = errors.New("not found")

	// Model Manager alarm definitions
	alrmNoDeepweedModel = modelManagerAlarm{
		identifier:  "no_detection_model_available_for_crop",
		code:        1,
		description: "No detection model available for crop {crop_id}",
	}
	alrmDownloadingDeepweedModel = modelManagerAlarm{
		identifier:  "downloading_detection_model",
		code:        2,
		description: "Downloading detection model_id: {model_id}",
	}
	alrmCropMissingDisabled = modelManagerAlarm{
		identifier:  "crop_missing_or_disabled",
		code:        3,
		description: "No crop selected, or selected crop is disabled",
	}
)

type DownloadingModelArtifactState struct {
	Model           *model_manager.Model
	Artifact        *veselka.ModelArtifact
	Progress        float32
	RemainingTimeMs uint64
}

type EnabledCrop struct {
	veselka.Crop
	PinnedModel      string
	RecommendedModel string
	LastActiveModel  string
	MaintainedModels []string

	customNameOverride bool
}

type LocalizeCrop struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

func (ec *EnabledCrop) Localize(lang string) LocalizeCrop {
	lc := LocalizeCrop{}
	if lang != "" {
		for _, localized := range ec.Crop.Translations {
			if localized.Language == lang {
				if !ec.customNameOverride {
					lc.Name = localized.Name
				}
				lc.Description = localized.Description
				break
			}
		}
	}
	if lc.Name == "" {
		lc.Name = ec.CommonName
	}
	if lc.Description == "" {
		lc.Description = ec.Description
	}
	return lc
}

type EnabledCropList []EnabledCrop

type ModelManagerState struct {
	ManagedStateImpl
	LocalModels               map[string]*model_manager.Model
	SyncedModels              map[string]*model_manager.Model
	DownloadingModelArtifacts map[string]DownloadingModelArtifactState
	ModelReceiverConnected    []bool
	ModelReceiversConnected   int
	Synchronized              bool                       // Does current model match selected crop/ pinned model
	NeedsUpdate               map[veselka.ModelType]bool // Do we have a newer model to switch to
	CropEnvironments          []veselka.CropEnvironment
	EnabledCropsList          EnabledCropList
	CurrentPointCategories    []veselka.PointCategory
	CurrentDeepWeedModelId    string
	CurrentP2PModelID         string
	PreviousPinnedP2PModelID  string
	ActiveAlarms              sync.Map

	defaultP2PModelID             string
	lastDefaultP2PModelUpdate     time.Time
	lastCropIDSync                time.Time
	lastModelSync                 time.Time
	lastModelDownload             time.Time
	lastModelCleanup              time.Time
	lastUpdateSynced              time.Time
	lastPointCatsSync             time.Time
	currentCropID                 string
	currentCrop                   string
	modelParameterChangeCallbacks map[string]func(cfg *almanac.ModelinatorConfig)
	tensorRTVersionCache          []string
	computeCapabilityCache        []string
}

func NewModelManagerState() *ModelManagerState {
	state := &ModelManagerState{
		LocalModels:                   make(map[string]*model_manager.Model),
		SyncedModels:                  make(map[string]*model_manager.Model),
		DownloadingModelArtifacts:     make(map[string]DownloadingModelArtifactState),
		NeedsUpdate:                   make(map[veselka.ModelType]bool),
		CropEnvironments:              make([]veselka.CropEnvironment, 0),
		EnabledCropsList:              make(EnabledCropList, 0),
		CurrentPointCategories:        make([]veselka.PointCategory, 0),
		modelParameterChangeCallbacks: make(map[string]func(cfg *almanac.ModelinatorConfig)),
	}
	state.initialize()
	return state
}

type modelManagerAlarm struct {
	identifier  string
	code        uint32
	description string
	params      map[string]any
}

func (m modelManagerAlarm) CloneWithParams(params map[string]any) modelManagerAlarm {
	// Need to clone to keep alarm definitions as vars
	return modelManagerAlarm{
		identifier:  m.identifier,
		code:        m.code,
		description: m.description,
		params:      params,
	}
}

func (mms *ModelManagerState) activateAlarm(alarm modelManagerAlarm, params map[string]any) {
	// *NOTE* when params are wrapped in {} then interpolated down stream (alarm.go) these should line up with '{param_key}' in alarm descriptions.
	mms.ActiveAlarms.Store(alarm.identifier, alarm.CloneWithParams(params))
}

func (mms *ModelManagerState) deactivateAlarm(ident string) {
	mms.ActiveAlarms.Delete(ident)
}

func (mms *ModelManagerState) GetActiveAlarms() []modelManagerAlarm {
	activeAlarms := make([]modelManagerAlarm, 0)
	mms.ActiveAlarms.Range(func(key, value any) bool {
		if alarm, ok := value.(modelManagerAlarm); ok {
			activeAlarms = append(activeAlarms, alarm)
		}
		return true
	})
	return activeAlarms
}

func (mms *ModelManagerState) RegisterModelParameterCallback(cb func(cfg *almanac.ModelinatorConfig)) string {
	var callbackID = ""
	mms.WriteOnCurrent(func() {
		callbackID = fmt.Sprint(time.Now().UnixNano())
		mms.modelParameterChangeCallbacks[callbackID] = cb
	})
	return callbackID
}

func (mms *ModelManagerState) UnRegisterModelParameterCallback(callbackID string) {
	mms.WriteOnCurrent(func() {
		delete(mms.modelParameterChangeCallbacks, callbackID)
	})
}

func (mms *ModelManagerState) triggerModelParameterCallbacks(cfg *almanac.ModelinatorConfig) {
	mms.ReadOnCurrent(func() {
		wg := sync.WaitGroup{}
		for _, cb := range mms.modelParameterChangeCallbacks {
			cbFn := cb
			wg.Add(1)
			go func() {
				defer wg.Done()
				cbFn(cfg)
			}()
		}
		wg.Wait()
	})
}

func (mms *ModelManagerState) updateModelCreationGauges() {
	mms.ReadOnCurrent(func() {
		if model, ok := mms.LocalModels[mms.CurrentDeepWeedModelId]; ok {
			deepweedModelCreateTSGauge.Set(float64(model.Created))
		}
		if model, ok := mms.LocalModels[mms.CurrentP2PModelID]; ok {
			p2pModelCreateTSGauge.Set(float64(model.Created))
		}
	})
}

var modelManagerWatcherEnabledModelTypes = []veselka.ModelType{veselka.ModelTypeP2P, veselka.ModelTypeDeepweed}

type VeselkaClient interface {
	SetLogLevel(level logrus.Level)
	GetModelInfo(ctx context.Context, id string) (*veselka.Model, error)
	GetModelArtifact(ctx context.Context, computeCapability string, modelID string, tensorRTVersion string, startingSize int64) (*http.Response, error)
	GetModelInfos(ctx context.Context, ids []string) (map[string]*veselka.Model, error)
	GetDefaultP2PModel(ctx context.Context, currentModelVersion int) (*veselka.Model, error)
	GetModelForCropID(ctx context.Context, params veselka.GetRecommendedModelParams) (*veselka.Model, error)
	GetAllCrops(ctx context.Context) ([]*veselka.Crop, error)
	GetModelParameters(ctx context.Context, parametersRequest []veselka.ModelParameterRequest) ([]*almanac.ModelinatorConfig, error)
	GetAllPointCategories(ctx context.Context) ([]veselka.PointCategory, error)
}

type HWMgrClient interface {
	GetGPSData(bool) (*hw_proto.GetGPSDataResponse, error)
}

type ConfigClient interface {
	SetDoubleValue(key string, val float64) error
	SetStringValue(key string, val string) error
	SetBoolValue(key string, val bool) error
	AddToList(key string, name string) error
	RemoveFromList(key string, name string) error
}

type ModelinatorConfigClient interface {
	CopyFrom(cropID, oldModelID, newModelID string) error
}

type ModelManagerWatcher struct {
	EventTrigger
	commanderNode           config.Tree
	logLevelNode            config.Tree
	commonNode              config.Tree
	cropIDsNode             config.Tree
	recommendationInterval  config.Tree
	currentCropIDNode       config.Tree
	pinnedNonCropModelsNode config.Tree
	deepweedNode            config.Tree
	pointCategoriesNode     config.Tree
	modelState              *ModelManagerState
	operationsState         *OperationsState
	implementState          *ImplementState
	rows                    RowClientList
	modelArtifactCacheDir   string
	modelMetadataCacheDir   string
	usbDir                  string
	env                     environment.Robot
	configClient            ConfigClient
	httpClient              *http.Client
	currentModelVersion     map[veselka.ModelType]int
	downloadChannel         chan bool
	syncChannel             chan bool
	syncCropIDsCh           chan bool
	updateSyncedCh          chan bool
	syncPointCatsCh         chan bool
	modelCacheWriteMutex    sync.Mutex
	logger                  *logrus.Entry
	veselkaClient           VeselkaClient
	hardwareManagerClient   HWMgrClient
	modelHistoryTracker     *ModelHistoryTracker
	redis                   crredis.RedisClient
	modelinatorConfigClient ModelinatorConfigClient
}

type ModelManagerWatcherParams struct {
	CommanderNode           *config.ConfigTree
	CommonNode              *config.ConfigTree
	Env                     environment.Robot
	ConfigClient            *config.ConfigClient
	Rows                    map[int]*rows.RowClients
	HttpClient              *http.Client
	ModelState              *ModelManagerState
	OperationsState         *OperationsState
	ImplementState          *ImplementState
	StopCtx                 context.Context
	VeselkaClient           *veselka.Client
	HardwareManagerClient   *hardware_manager.HardwareManagerClient
	Redis                   crredis.RedisClient
	ModelinatorConfigClient ModelinatorConfigClient
}

func NewModelManagerWatcher(params ModelManagerWatcherParams) *ModelManagerWatcher {
	dataDir := params.Env.MakaDataDir
	if len(dataDir) == 0 {
		dataDir = "/data"
	}
	mediaDir := params.Env.MakaMediaDir
	if len(mediaDir) == 0 {
		mediaDir = "/media"
	}

	rowClients := make([]*RowClient, 0)
	for _, row := range params.Rows {
		for id, cvClient := range row.CVRuntimeClients {
			modelReceiverClient, mrOk := row.ModelReceiverClients[id]
			streamHost, shOk := row.StreamHosts[id]
			if !mrOk || !shOk {
				continue
			}
			if modelReceiverClient != nil && cvClient != nil {
				rowClients = append(rowClients, &RowClient{
					addr:            streamHost,
					client:          modelReceiverClient,
					cvRuntimeClient: cvClient,
					syncedModels:    make([]string, 0),
				})
			}
		}
	}

	commanderNode := config.WrapConfigTree(params.CommanderNode)
	commonNode := config.WrapConfigTree(params.CommonNode)
	stopCtx := params.StopCtx
	action := &ModelManagerWatcher{
		EventTrigger: EventTrigger{
			triggerChannel: make(chan bool),
		},
		commanderNode:           commanderNode,
		commonNode:              commonNode,
		modelState:              params.ModelState,
		operationsState:         params.OperationsState,
		implementState:          params.ImplementState,
		currentCropIDNode:       commanderNode.GetNode(currentCropIDNodeName),
		logLevelNode:            commanderNode.GetNode(logLevelNodeName),
		cropIDsNode:             commanderNode.GetNode(cropIDsNodeName),
		recommendationInterval:  commanderNode.GetNode(recommendationIntervalNodeName),
		pinnedNonCropModelsNode: commanderNode.GetNode(pinnedNonCropModelsNodeName),
		deepweedNode:            commonNode.GetNode(deepweedNodeName),
		pointCategoriesNode:     commonNode.GetNode(pointCategoriesNodeName),
		env:                     params.Env,
		rows:                    rowClients,
		configClient:            params.ConfigClient,
		httpClient:              params.HttpClient,
		downloadChannel:         make(chan bool),
		syncChannel:             make(chan bool),
		syncCropIDsCh:           make(chan bool),
		updateSyncedCh:          make(chan bool),
		syncPointCatsCh:         make(chan bool),
		veselkaClient:           params.VeselkaClient,
		hardwareManagerClient:   params.HardwareManagerClient,
		currentModelVersion: map[veselka.ModelType]int{
			veselka.ModelTypeDeepweed: readModelVersion("/robot/deeplearning/deepweed/version.txt"),
			veselka.ModelTypeP2P:      readModelVersion("/robot/deeplearning/p2p/version.txt"),
		},
		modelArtifactCacheDir:   filepath.Join(dataDir, "model_manager", "model_artifact_cache"),
		modelMetadataCacheDir:   filepath.Join(dataDir, "model_manager", "model_cache"),
		usbDir:                  filepath.Join(mediaDir, "usb-device"),
		logger:                  appModelLogger.WithFields(logrus.Fields{"module": "ModelManagerWatcher"}),
		modelHistoryTracker:     NewModelHistoryTracker(stopCtx, params.Redis),
		redis:                   params.Redis,
		modelinatorConfigClient: params.ModelinatorConfigClient,
	}

	level := logging.StringToLogLevel(action.logLevelNode.GetStringValue())
	appModelLogger.SetLevel(level)
	appModelLogger.SetFormatter(logging.DefaultLogFormat)
	action.veselkaClient.SetLogLevel(level)
	action.modelHistoryTracker.SetLogLevel(level)

	action.modelState.WriteOnCurrent(func() {
		action.modelState.LocalModels = action.GetLocalModels()
		action.modelState.currentCropID = action.currentCropIDNode.GetStringValue()
		action.modelState.CurrentDeepWeedModelId = action.getConfigModelID(veselka.ModelTypeDeepweed)
		action.modelState.PreviousPinnedP2PModelID = action.getPinnedP2P()
		action.modelState.CurrentP2PModelID = action.getConfigModelID(veselka.ModelTypeP2P)
		if err := action.modelHistoryTracker.SubmitEvent(ModelEvent{
			Type:      RobotStart,
			ModelID:   action.modelState.CurrentDeepWeedModelId,
			ModelType: string(veselka.ModelTypeDeepweed),
			CropID:    action.modelState.currentCropID,
		}); err != nil {
			action.logger.WithError(err).Error("tracking model history startup event failed")
		}
		if err := action.modelHistoryTracker.SubmitEvent(ModelEvent{
			Type:      RobotStart,
			ModelID:   action.modelState.CurrentP2PModelID,
			ModelType: string(veselka.ModelTypeP2P),
		}); err != nil {
			action.logger.WithError(err).Error("tracking model history startup event failed")
		}
	})

	action.logLevelNode.RegisterCallback(func() {
		level := logging.StringToLogLevel(action.logLevelNode.GetStringValue())
		appModelLogger.SetLevel(level)
		action.veselkaClient.SetLogLevel(level)
		action.modelHistoryTracker.SetLogLevel(level)
	})
	action.currentCropIDNode.RegisterCallback(func() {
		action.TriggerDownload()
		action.ForceModelUpdate()
		action.Trigger()
		action.modelState.WriteOnCurrent(func() {})
	})
	action.cropIDsNode.RegisterCallback(func() {
		action.TriggerCropIDSync(false)
	})
	action.pinnedNonCropModelsNode.RegisterCallback(func() {
		p2pModelID := action.getPinnedP2P()
		event := Pinned
		if p2pModelID == "" {
			event = UnPinned
			action.modelState.ReadOnCurrent(func() { p2pModelID = action.modelState.PreviousPinnedP2PModelID })
		}
		if err := action.recordHistoryEvent(event, veselka.ModelTypeP2P, p2pModelID, ""); err != nil {
			action.logger.WithError(err).Errorf("failed to record p2p pinning history")
		}
		action.TriggerDownload()
		action.modelState.WriteOnCurrent(func() { action.modelState.PreviousPinnedP2PModelID = p2pModelID })
	})
	action.deepweedNode.RegisterCallback(func() {
		action.triggerSync()
	})
	action.pointCategoriesNode.RegisterCallback(func() {
		action.TriggerPointCatsSync(false)
	})

	if err := action.copyModelsToArtifactsOnUpgrade(stopCtx); err != nil {
		action.logger.WithError(err).Error("failed to copy models to artifacts on upgrade")
	}

	go action.syncCropIDs(stopCtx)
	go action.downloadModelsLoop(stopCtx)
	go action.syncModels(stopCtx)
	go action.updateSyncedModels(stopCtx)
	go action.cleanupModels(stopCtx, modelCleanUpInterval)
	go action.syncPointCategories(stopCtx)
	return action
}

func readModelVersion(versionFileName string) int {
	deepweedModelVersion, err := afero.ReadFile(ModelManagerFs, versionFileName)
	if err != nil {
		logrus.Warnf("failed to read model version from %s - %v", versionFileName, err)
		return 1
	}
	deepweedModelVersionInt, err := strconv.Atoi(string(deepweedModelVersion))
	if err != nil {
		logrus.Warnf("failed to read model version from %s - %v", versionFileName, err)
		return 1
	}
	return deepweedModelVersionInt
}

func (m *ModelManagerWatcher) TriggerCropIDSync(forceCacheRefresh bool) {
	select {
	case m.syncCropIDsCh <- forceCacheRefresh:
	default: // prevent blocking
	}
}

func (m *ModelManagerWatcher) recordHistoryEvent(event ModelEventType, modelType veselka.ModelType, modelID, cropID string) error {
	err := m.modelHistoryTracker.SubmitEvent(ModelEvent{
		Type:      event,
		ModelID:   modelID,
		ModelType: string(modelType),
		CropID:    cropID,
	})
	return err
}

func (m *ModelManagerWatcher) syncCropIDs(ctx context.Context) {
	cachedCropState := make(map[string]EnabledCrop)
	lastEnabledCropCount := 0
	lastCacheUpdate := time.Time{}
	for {
		currentActiveCropID := m.getConfigCurrentCropID()
		recommendationInterval := parseConfigDuration(m.recommendationInterval, defaultRecommendationInterval)
		updateCache := time.Since(lastCacheUpdate) >= min(recommendationInterval, minRecommendationInterval)
		allCrops, err := m.GetCacheAllCrops(updateCache)
		if err != nil {
			m.logger.Error(err)
		} else {

			for _, crop := range allCrops {
				cropConfig := CropConfig{ID: crop.ID}
				if err := AddMissingCropConfig(m.configClient, m.cropIDsNode, cropConfig); err != nil {
					m.logger.WithError(err).Errorf("failed to add crop")
					continue
				}
				// update config.carbon_name = crop.common_name (should only happen once or when crop name changes)
			}

			m.UpdateEnabledCropList(allCrops)
			enabledCropList := m.EnabledCropList(false)
			for _, currentCrop := range enabledCropList {
				if currentActiveCropID == currentCrop.ID {
					// force update when current crop has no recommendation
					updateCache = updateCache || (currentCrop.RecommendedModel == "")
				}
				if prevCrop, ok := cachedCropState[currentCrop.ID]; ok {
					if prevCrop.PinnedModel != currentCrop.PinnedModel {
						if currentCrop.PinnedModel != "" {
							if config.FeatureFlagEnabledIfExists(m.commonNode, config.FeatureFlagCopyModelinator) {
								if err := m.modelinatorConfigClient.CopyFrom(currentCrop.ID, prevCrop.LastActiveModel, currentCrop.PinnedModel); err != nil {
									m.logger.WithError(err).Warnf("failed to copy modelinator config state cropID: %s, oldModelID: %s, newModelID: %s", currentCrop.ID, currentCrop.LastActiveModel, currentCrop.PinnedModel)
								}
							}
							if err := m.recordHistoryEvent(Pinned, veselka.ModelTypeDeepweed, currentCrop.PinnedModel, currentCrop.ID); err != nil {
								m.logger.WithError(err)
							}
							m.TriggerDownload()
						} else {
							if err := m.recordHistoryEvent(UnPinned, veselka.ModelTypeDeepweed, prevCrop.PinnedModel, currentCrop.ID); err != nil {
								m.logger.WithError(err)
							}
						}
					}
				}
				cachedCropState[currentCrop.ID] = currentCrop
			}

			enabledCropCount := len(enabledCropList)
			updateCache = updateCache || enabledCropCount > lastEnabledCropCount
			if updateCache {
				m.logger.Info("requesting updated model recommendations")
				for _, enabledCrop := range enabledCropList {
					hash, err := m.getCurrentGeoHash()
					if err != nil {
						m.logger.WithError(err).Warnf("failed to retrieve geohash")
					}

					ctx, cancel := context.WithTimeout(context.Background(), requestTimeout)
					featureFlags := map[string]bool{
						config.FeatureFlagEmbeddingBasedClassification: config.FeatureFlagEnabledIfExists(m.commonNode, config.FeatureFlagEmbeddingBasedClassification)}
					if vm, err := m.veselkaClient.GetModelForCropID(ctx, veselka.GetRecommendedModelParams{
						CropID:              enabledCrop.ID,
						RobotName:           m.env.MakaRobotName,
						Geohash:             hash,
						CurrentModelVersion: m.currentModelVersion[veselka.ModelTypeDeepweed],
						FeatureFlags:        featureFlags,
					}); err != nil { // no subType so veselka side can control
						m.logger.WithError(err).Warnf("failed to retrieve recommended model for cropID: %s", enabledCrop.ID)
					} else {
						if config.FeatureFlagEnabledIfExists(m.commonNode, config.FeatureFlagModelPinningDefault) {
							if enabledCrop.PinnedModel == "" {
								if err := UpdateCropConfigPinnedModel(m.configClient, enabledCrop.ID, vm.ID); err != nil {
									m.logger.WithError(err).Warn("failed to update")
								}
							}
						}
						if config.FeatureFlagEnabledIfExists(m.commonNode, config.FeatureFlagCopyModelinator) {
							if len(enabledCrop.LastActiveModel) > 0 && enabledCrop.LastActiveModel != vm.ID {
								if err := m.modelinatorConfigClient.CopyFrom(enabledCrop.ID, enabledCrop.LastActiveModel, vm.ID); err != nil {
									m.logger.WithError(err).Warnf("failed to copy modelinator config state cropID: %s, oldModelID: %s, newModelID: %s", enabledCrop.ID, enabledCrop.LastActiveModel, vm.ID)
								}
							}
						}
						if err := m.SaveDefaultModelParameters(vm.ID, enabledCrop.ID, vm.ModelParameters); err != nil {
							m.logger.WithError(err).Warnf("failed to update model parameters for modelID: %s cropID: %s", vm.ID, enabledCrop.ID)
						}
						if enabledCrop.RecommendedModel != vm.ID {
							if err := UpdateCropConfigRecommendedModel(m.configClient, enabledCrop.ID, vm.ID); err != nil {
								m.logger.WithError(err).Warn("failed to update")
							}
							if err := m.modelHistoryTracker.SubmitEvent(ModelEvent{
								Type:      Recommended,
								ModelID:   vm.ID,
								ModelType: string(veselka.ModelTypeDeepweed),
								CropID:    enabledCrop.ID,
							}); err != nil {
								m.logger.WithError(err)
							}
							m.TriggerDownload()
						}
					}
					cancel()
				}

				lastEnabledCropCount = enabledCropCount
				lastCacheUpdate = time.Now()
			}
			for _, enabledCrop := range m.EnabledCropList(false) {
				if enabledCrop.ID == currentActiveCropID {
					if enabledCrop.RecommendedModel == "" && enabledCrop.PinnedModel == "" {
						m.modelState.activateAlarm(alrmNoDeepweedModel, map[string]any{"crop_id": currentActiveCropID})
					} else {
						m.modelState.deactivateAlarm(alrmNoDeepweedModel.identifier)
					}
				}
			}

			m.modelState.WriteOnCurrent(func() {
				now := time.Now()
				m.logger.Debugln("last crop id sync:", now, "last cache update:", lastCacheUpdate)
				m.modelState.lastCropIDSync = now
				lastCropIDSyncGauge.SetToCurrentTime()
			})
		}
		select {
		case <-ctx.Done():
			return
		case forceCacheRefresh := <-m.syncCropIDsCh:
			if forceCacheRefresh {
				lastCacheUpdate = time.UnixMilli(0)
			}
		}
	}
}

func (m *ModelManagerWatcher) GetSelectedCropID() string {
	return m.getConfigCurrentCropID()
}

// SelectCropByID update configuration to apply the provided id as the currently selected crop id.
func (m *ModelManagerWatcher) SelectCropByID(id string) error {
	for _, enabledCrop := range m.EnabledCropList(false) {
		if enabledCrop.ID == id {
			return m.setConfigCurrentCropID(id)
		}
	}

	return fmt.Errorf("id: %s is not a valid cropID", id)
}

// EnabledCropList retrieves the enabled crop list from the model state.
func (m *ModelManagerWatcher) EnabledCropList(includeCaptureOnly bool) EnabledCropList {
	var enabledCropList EnabledCropList
	m.modelState.ReadOnCurrent(func() {
		enabledCropList = m.enabledCropListFromState(m.modelState.EnabledCropsList, includeCaptureOnly)
	})
	return enabledCropList
}

func (mms *ModelManagerState) GetNextEnabledCrops(ctx context.Context, timestampMs int64, watcher *ModelManagerWatcher) (EnabledCropList, bool, int64) {
	var enabledCropList EnabledCropList
	var ts int64
	result := mms.ReadOnNext(ctx, timestampMs, func() {
		ts = mms.GetTimestampMs()
		enabledCropList = watcher.enabledCropListFromState(mms.EnabledCropsList, false)
	})
	return enabledCropList, result, ts
}

func (mms *ModelManagerState) GetNextCaptureCrops(ctx context.Context, timestampMs int64, watcher *ModelManagerWatcher) (EnabledCropList, bool, int64) {
	var enabledCropList EnabledCropList
	var ts int64
	result := mms.ReadOnNext(ctx, timestampMs, func() {
		ts = mms.GetTimestampMs()
		enabledCropList = watcher.enabledCropListFromState(mms.EnabledCropsList, true)
	})
	return enabledCropList, result, ts
}

// enabledCropListFromState filters crops without acquiring locks (assumes caller holds lock)
func (m *ModelManagerWatcher) enabledCropListFromState(crops EnabledCropList, includeCaptureOnly bool) EnabledCropList {
	enabledCropList := make(EnabledCropList, 0)
	for _, crop := range crops {
		if includeCaptureOnly || !crop.CaptureOnly {
			enabledCropList = append(enabledCropList, crop)
		}
	}
	return enabledCropList
}

func (m *ModelManagerWatcher) syncPointCategories(ctx context.Context) {
	// 1. get the point categories from veselka
	// 2. set them in redis
	// 3. set any missing ones in config
	// 4. update the current point categories in the model state

	// hack: the base 4 weeds are enabled by default
	originalWeedIDs := map[string]bool{
		"9fd4e9d0-2d01-4ae1-89c5-f4b4f6de9a3c": true, // broadleaf
		"f032b206-92cf-400a-b79e-e22bdb7930e3": true, // grass
		"2e678c61-f476-4485-aa9c-6a0e4bc7c626": true, // offshoot
		"1659226a-fe1c-450c-828f-88dc8819c25d": true, // purslane
	}

	syncFromVeselka := true

	// use exponential backoff for retries
	retryTimeS := pointCategoriesRetryInitialBackoff

	for {

		select {
		case <-ctx.Done():
			return
		case syncFromVeselka = <-m.syncPointCatsCh:
		}

		m.logger.Info("Syncing point categories")

		var pointCategories []veselka.PointCategory
		var err error

		successfulVeselkaSync := true

		if syncFromVeselka {
			m.logger.Info("Syncing point categories from Veselka")
			pointCategories, err = m.getAllPointCategories()
			if err != nil {
				m.logger.WithError(err).Error("failed to get point categories")
				successfulVeselkaSync = false
			} else {
				if err = m.UpdatePointCategoriesRedis(pointCategories); err != nil {
					m.logger.WithError(err).Error("failed to update point categories")
					successfulVeselkaSync = false
				} else {
					retryTimeS = pointCategoriesRetryInitialBackoff
				}
			}
		}

		if len(pointCategories) == 0 { // nil or empty
			// the sync from veselka failed or wasn't required, try to grab what is currently in redis
			pointCategories, err = m.GetPointCategoryDefsRedis()
			if err != nil {
				// failed to grab from redis, either because redis is down or
				// doesn't have the defs. we need to set successfulVeselkaSync to false to trigger a retry
				m.logger.WithError(err).Error("failed to get point categories from redis")
				successfulVeselkaSync = false
			}
		}

		for _, category := range pointCategories {
			_, ok := originalWeedIDs[category.ID]
			if err := AddMissingPointCategoryConfig(m.configClient, m.pointCategoriesNode, category, ok); err != nil {
				m.logger.WithError(err).Error("failed to add point category")
			}
		}

		m.UpdateCurrentPointCategories(pointCategories)

		if !successfulVeselkaSync {
			m.logger.Warnf("Veselka point categories sync failed, retrying in %v", retryTimeS)
			time.AfterFunc(retryTimeS, func() {
				m.TriggerPointCatsSync(true)
			})
			retryTimeS = min(retryTimeS*2, pointCategoriesRetryMaxBackoff)
		}

		// if you don't do this here, action will continue to call syncPointCategories
		// at an interval of 500 ms. Instead, of action being responsible for triggering
		// the sync again after a failed call to veselka, we let the exponential backoff logic in
		// this function handle it.
		m.modelState.WriteOnCurrent(func() {
			now := time.Now()
			m.modelState.lastPointCatsSync = now
		})
	}

}

func (m *ModelManagerWatcher) GetLocalModels() map[string]*model_manager.Model {
	models := make(map[string]*model_manager.Model, 0)
	localModelArtifactsCount := 0
	localModelCount := 0
	files, err := afero.ReadDir(ModelManagerFs, m.modelMetadataCacheDir)
	if err != nil {
		return models
	}

	totalModelBytes := int64(0)
	malformedDownload := false
	for _, file := range files {
		if !strings.HasSuffix(file.Name(), model_manager.MetaExt) {
			continue
		}
		metaFilename := filepath.Join(m.modelMetadataCacheDir, strings.TrimSuffix(file.Name(), model_manager.ModelExt))

		model, err := model_manager.GetVerifyMetadata(metaFilename)
		if err != nil {
			m.logger.Warnf("invalid metadata file: %s - %v", metaFilename, err)
			if err := ModelManagerFs.Remove(metaFilename); err != nil {
				m.logger.Warnf("failed to remove %s - %v", metaFilename, err)
			}
			malformedDownload = true
			continue
		}
		localModelCount += 1

		for _, artifact := range model.ModelArtifacts {
			modelArtifactFilePath := filepath.Join(m.modelArtifactCacheDir, artifact.Filename())
			if validErr := model_manager.VerifyModelArtifactDownload(artifact, modelArtifactFilePath); err != nil {
				if _, err := ModelManagerFs.Stat(modelArtifactFilePath); err == nil {
					// if invalid and the file exists, remove the file to force redownload. If does not exist, will
					// be redownloaded on next sync and can be ignored here
					m.logger.Infof("invalid model artifact file, removing: %s - %v", modelArtifactFilePath, validErr)
					if err := ModelManagerFs.Remove(modelArtifactFilePath); err != nil {
						m.logger.Warnf("failed to remove %s - %v", modelArtifactFilePath, err)
					}
				}
				malformedDownload = true
				continue
			}
			totalModelBytes += file.Size()
			localModelArtifactsCount += 1
		}
		models[model.ID] = model
	}

	if malformedDownload {
		m.TriggerDownload()
	}

	modelDiskBytesGauge.Set(float64(totalModelBytes))
	localModelCountGauge.Set(float64(localModelCount))
	localModelArtifactCountGauge.Set(float64(localModelArtifactsCount))
	return models
}

func (m *ModelManagerWatcher) Action() {
	var needsModelUpdate map[veselka.ModelType]bool
	var synchronized bool
	m.modelState.ReadOnCurrent(func() {
		if !m.modelState.lastCropIDSync.IsZero() {
			m.triggerUpdateSynced()
		}
		if time.Since(m.modelState.lastModelSync) > modelSyncInterval {
			m.triggerSync()
		}
		recommendationInterval := parseConfigDuration(m.recommendationInterval, defaultRecommendationInterval)
		if time.Since(m.modelState.lastCropIDSync) >= min(recommendationInterval, minRecommendationInterval) {
			m.TriggerCropIDSync(false)
		}
		if time.Since(m.modelState.lastModelDownload) > downloadTriggerInterval {
			m.TriggerDownload()
		}
		if time.Since(m.modelState.lastPointCatsSync) > pointCategoriesSyncInterval {
			m.TriggerPointCatsSync(true)
		}
		synchronized = m.modelState.Synchronized
		needsModelUpdate = CopyMap(m.modelState.NeedsUpdate)
	})
	anyRowEnabled := false
	m.operationsState.ReadOnCurrent(func() {
		for _, state := range m.operationsState.RowWeeding {
			anyRowEnabled = anyRowEnabled || state.ActualTargeting.Enabled()
		}
	})
	lifted := false
	m.implementState.ReadOnCurrent(func() {
		lifted = m.implementState.SafetyState.Lifted
	})
	ready := true
	m.operationsState.ReadOnCurrent(func() {
		ready = m.operationsState.Ready
	})
	if !synchronized || (needsModelUpdate[veselka.ModelTypeDeepweed] && (!anyRowEnabled || lifted || !ready)) {
		err := m.ReloadModel(veselka.ModelTypeDeepweed)
		if err != nil {
			m.logger.WithError(err).Warnf("Failed to reload model type:%s", veselka.ModelTypeDeepweed)
		} else {
			m.modelState.WriteOnCurrent(func() { m.modelState.Synchronized = true })
		}
	}
	if needsModelUpdate[veselka.ModelTypeP2P] && (!anyRowEnabled || lifted || !ready) {
		if err := m.ReloadModel(veselka.ModelTypeP2P); err != nil {
			m.logger.WithError(err).Warnf("Failed to reload model type:%s", veselka.ModelTypeP2P)
		}
	}
}

func getYoungestModel(models map[string]*model_manager.Model) *model_manager.Model {
	youngest := time.Time{}
	result := &model_manager.Model{}
	for _, m := range models {
		if m.Downloaded.After(youngest) {
			result = m
		}
	}
	return result
}

func (m *ModelManagerWatcher) updateSyncedModels(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-m.updateSyncedCh:
		}

		// reaper might not have rows connected, assume synced if no rows connected
		if environment.IsReaper() && len(m.rows) == 0 {
			m.logger.Warn("No rows connected - aborting sync")
			m.modelState.ConditionalWriteOnCurrent(func() bool {
				if !m.modelState.Synchronized {
					m.modelState.Synchronized = true
					return true
				}
				return false
			})
		}

		localModels := m.GetLocalModels()
		youngest := getYoungestModel(localModels)
		modelMinDownloadAge.Set(float64(youngest.Downloaded.UnixMilli()))

		m.rows.Sync()
		syncedModels := make(map[string]*model_manager.Model)
		for _, modelID := range m.rows.GetSyncedModels() {
			if local, ok := localModels[modelID]; ok {
				syncedModels[modelID] = local
			}
		}

		needsModelUpdate := make(map[veselka.ModelType]bool)
		rowsConnected, modelReceiverConnected := m.rows.ConnectionState()
		knownRows := len(m.rows)
		if (knownRows == 0 && environment.IsSlayer()) || rowsConnected < knownRows {
			m.logger.Warnf("Row Connection Error %d/%d (connected/known) - aborting sync", rowsConnected, knownRows)
			syncedModels = localModels
			m.modelState.WriteOnCurrent(func() { m.modelState.Synchronized = false })
		} else {
			for _, modelType := range modelManagerWatcherEnabledModelTypes {
				currentModel := m.getConfigModelID(modelType)
				bestModel, err := m.getBestModel(modelType)
				if err != nil {
					m.logger.WithError(err).Warn("Failed to get best model")
					needsModelUpdate[modelType] = true
					continue
				}
				needsModelUpdate[modelType] = bestModel.ID != currentModel
			}
		}

		m.logger.Debug(strings.Join(m.rows.ReportStatus(), "\n"))

		changed := false
		m.modelState.ReadOnCurrent(func() {
			if len(m.modelState.SyncedModels) != len(syncedModels) || len(m.modelState.LocalModels) != len(localModels) {
				changed = true
				return
			}
			for key := range m.modelState.SyncedModels {
				if _, ok := syncedModels[key]; !ok {
					changed = true
					return
				}
			}
			for i := range m.modelState.LocalModels {
				if m.modelState.LocalModels[i] != localModels[i] {
					changed = true
					return
				}
			}
			if len(m.modelState.NeedsUpdate) != len(needsModelUpdate) {
				changed = true
				return
			}
			for i := range m.modelState.NeedsUpdate {
				if m.modelState.NeedsUpdate[i] != needsModelUpdate[i] {
					changed = true
					return
				}
			}

			changed = m.modelState.ModelReceiversConnected != rowsConnected || !equalSlices(m.modelState.ModelReceiverConnected, modelReceiverConnected)
		})

		if changed {
			m.modelState.WriteOnCurrent(func() {
				m.modelState.SyncedModels = syncedModels
				m.modelState.LocalModels = localModels
				m.modelState.NeedsUpdate = needsModelUpdate
				m.modelState.ModelReceiverConnected = modelReceiverConnected
			})
		}

		m.modelState.WriteOnCurrent(func() {
			now := time.Now()
			m.logger.Debugln("last update synced models:", now)
			m.modelState.lastUpdateSynced = now
			lastModelUpdateSyncGauge.SetToCurrentTime()
		})
	}
}

func equalSlices(s1 []bool, s2 []bool) bool {
	if len(s1) != len(s2) {
		return false
	}
	for idx := range s1 {
		if s1[idx] != s2[idx] {
			return false
		}
	}
	return true
}

func (m *ModelManagerWatcher) createWatchedDownloadProgressCh(ctx context.Context, artifact veselka.ModelArtifact) chan DownloadingProgressState {
	progressCh := make(chan DownloadingProgressState, 100)
	go func() {
		m.modelState.activateAlarm(alrmDownloadingDeepweedModel, map[string]any{"model_id": artifact.ModelID})
		defer m.modelState.deactivateAlarm(alrmDownloadingDeepweedModel.identifier)
		lastProgress := time.Time{}
		for {
			select {
			case <-ctx.Done():
				return
			case progress := <-progressCh:
				if time.Since(lastProgress) > 5*time.Second {
					m.logger.Infof("Downloading artifact: %s, progress: %f, remaining_time: %d", artifact.ModelManagerArtifactID(), progress.Progress, progress.RemainingTimeMs)
					lastProgress = time.Now()
				}
				m.modelState.WriteOnCurrent(func() {
					if downloadingModel, ok := m.modelState.DownloadingModelArtifacts[artifact.ModelManagerArtifactID()]; ok {
						downloadingModel.Progress = progress.Progress
						downloadingModel.RemainingTimeMs = progress.RemainingTimeMs
						m.modelState.DownloadingModelArtifacts[artifact.ModelManagerArtifactID()] = downloadingModel
					}
				})
			}
		}
	}()
	return progressCh
}

func (m *ModelManagerWatcher) DownloadModelArtifact(ctx context.Context, artifact veselka.ModelArtifact) error {
	downloadCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	model, err := model_manager.GetVerifyMetadata(filepath.Join(m.modelMetadataCacheDir, artifact.ModelID+model_manager.MetaExt))
	if err != nil {
		return fmt.Errorf("invalid metadata file: %s - %s", artifact.ModelID+model_manager.MetaExt, err)
	}

	progressCh := m.createWatchedDownloadProgressCh(downloadCtx, artifact)

	// return early if file was already successfully downloaded or is in the process of being downloaded
	cachedModelArtifactPath := filepath.Join(m.modelArtifactCacheDir, artifact.Filename())
	err = model_manager.VerifyModelArtifactDownload(artifact, cachedModelArtifactPath)
	if err == nil {
		progressCh <- DownloadingProgressState{Progress: 1, RemainingTimeMs: 0}
		return nil
	}

	// remove old trt file associated with specified artifact if it exists
	if err := ModelManagerFs.Remove(cachedModelArtifactPath); err != nil && !errors.Is(err, os.ErrNotExist) {
		m.logger.Warnf("failed to remove %s - %v", cachedModelArtifactPath, err)
	}

	m.logger.Infof("starting download of artifact: %s", artifact.Filename())
	startingSize := int64(0)
	cacheModelTempPath := cachedModelArtifactPath + ".tmp"
	if fi, err := ModelManagerFs.Stat(cacheModelTempPath); err == nil {
		startingSize = fi.Size()
	}

	response, err := m.veselkaClient.GetModelArtifact(ctx, artifact.ComputeCapability, artifact.ModelID, artifact.TensorRTVersion, startingSize)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	artifact.ContentLength = response.ContentLength
	now := time.Now()
	model.Downloaded = now
	model.DownloadedTimestamp = uint64(now.UnixMilli())
	if model.RecommendedCropPointThreshold == 0.0 {
		model.RecommendedCropPointThreshold = DefaultCropPointThreshold
	}
	if model.RecommendedWeedPointThreshold == 0.0 {
		model.RecommendedWeedPointThreshold = DefaultWeedPointThreshold
	}
	model.LastUsedCropPointThreshold = model.RecommendedCropPointThreshold
	model.LastUsedWeedPointThreshold = model.RecommendedWeedPointThreshold

	for i, modelArtitact := range model.ModelArtifacts {
		if modelArtitact.ModelManagerArtifactID() == artifact.ModelManagerArtifactID() {
			// update artifact in model metadata
			model.ModelArtifacts[i].ContentLength = artifact.ContentLength
			break
		}
	}

	// write metadata first so we know how big the file should be
	if err := m.writeModelMetadata(model); err != nil {
		return err
	}

	trtFile, err := ModelManagerFs.OpenFile(cacheModelTempPath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, os.ModePerm)
	if err != nil {
		return err
	}

	defer trtFile.Close()
	startingTime := time.Now()
	var chunkSize int64 = 5000
	for begin := startingSize; begin < artifact.ContentLength; begin += chunkSize {
		bytesToRead := chunkSize
		remainingBytes := artifact.ContentLength - begin
		if remainingBytes < bytesToRead {
			bytesToRead = remainingBytes
		}
		if _, err = io.CopyN(trtFile, response.Body, bytesToRead); err != nil && err != io.EOF {
			return err
		}
		diff := time.Since(startingTime)
		perByteMs := float32(diff.Milliseconds()) / float32((begin+bytesToRead)-startingSize)

		progressCh <- DownloadingProgressState{
			Progress:        float32(begin+bytesToRead) / float32(artifact.ContentLength),
			RemainingTimeMs: uint64(perByteMs * float32(artifact.ContentLength-begin+bytesToRead)),
		}
		select {
		case <-ctx.Done():
			return errors.New("download aborted")
		default:
		}
	}
	totalDownloadTime := time.Since(startingTime)

	if err := model_manager.VerifyModelArtifactDownload(artifact, cacheModelTempPath); err != nil {
		if err := ModelManagerFs.Remove(cacheModelTempPath); err != nil {
			m.logger.Warnf("failed to remove: %s - %v", cacheModelTempPath, err)
		}
		perByteMs := float32(totalDownloadTime.Milliseconds()) / float32(artifact.ContentLength-startingSize)
		progressCh <- DownloadingProgressState{Progress: 0.0, RemainingTimeMs: uint64(perByteMs * float32(artifact.ContentLength))}
		return fmt.Errorf("download malformed: %w", err)
	}

	m.modelCacheWriteMutex.Lock()
	defer m.modelCacheWriteMutex.Unlock()
	return ModelManagerFs.Rename(cacheModelTempPath, cachedModelArtifactPath)
}

func (m *ModelManagerWatcher) getBestP2PModel() (*model_manager.Model, error) {
	pinnedP2PModelID := m.getPinnedP2P()

	var syncedModels map[string]*model_manager.Model
	var defaultP2PModelID string
	m.modelState.ReadOnCurrent(func() {
		defaultP2PModelID = m.modelState.defaultP2PModelID
		syncedModels = CopyMap(m.modelState.SyncedModels)
	})

	var pinnedModel *model_manager.Model
	var defaultP2pModel *model_manager.Model
	bestModel := &model_manager.Model{Model: veselka.Model{}}
	for id, model := range syncedModels {
		if pinnedP2PModelID == id {
			pinnedModel = model
		} else if defaultP2PModelID == id {
			defaultP2pModel = model
		} else if model.Type == veselka.ModelTypeP2P && model.Created > bestModel.Created {
			bestModel = model
		}
	}

	if pinnedModel != nil {
		m.logger.Debugf("best p2p model %s [pinned]", pinnedModel.ID)
		return pinnedModel, nil
	}
	if defaultP2pModel != nil {
		m.logger.Debugf("best p2p model %s [default]", defaultP2pModel.ID)
		return defaultP2pModel, nil
	}
	if bestModel.ID != "" {
		m.logger.Debugf("best p2p model %s [fallback]", bestModel.ID)
		return bestModel, nil
	}
	m.TriggerDownload()
	return nil, errors.New(fmt.Sprintf("Pinned %v model with id %v is not downloaded and synced", p2pNodeName, pinnedP2PModelID))
}

func (m *ModelManagerWatcher) getBestModelForCropID(cropID string) (*model_manager.Model, error) {
	pinnedModelGauge.Set(0)
	recommendedModelGauge.Set(0)
	lastActiveModelGauge.Set(0)
	viableModelGauge.Set(0)

	var targetCrop EnabledCrop
	for _, crop := range m.EnabledCropList(false) {
		if crop.ID == cropID {
			targetCrop = crop
			break
		}
	}
	if targetCrop.ID == "" {
		m.modelState.activateAlarm(alrmCropMissingDisabled, map[string]any{"crop_id": cropID})
		return nil, fmt.Errorf("crop id: %s is not enabled or does not exist", cropID)
	}
	m.modelState.deactivateAlarm(alrmCropMissingDisabled.identifier)

	var syncedModels map[string]*model_manager.Model
	m.modelState.ReadOnCurrent(func() { syncedModels = CopyMap(m.modelState.SyncedModels) })

	if targetCrop.PinnedModel != "" {
		if pinned, ok := syncedModels[targetCrop.PinnedModel]; ok {
			m.logger.Debugf("best model for cropID: %s - %s [pinned]", cropID, pinned.ID)
			pinnedModelGauge.Set(1)
			return pinned, nil
		} else {
			m.logger.Warnf("pinned model: %s requested for crop id: %s but model not found", targetCrop.PinnedModel, targetCrop.ID)
			m.TriggerDownload()
		}
	}

	var recommendedModel *model_manager.Model
	var lastActiveModel *model_manager.Model
	bestModel := &model_manager.Model{Model: veselka.Model{}}
	for id, model := range syncedModels {
		if targetCrop.RecommendedModel == id {
			recommendedModel = model
		} else if targetCrop.LastActiveModel == id {
			lastActiveModel = model
		} else {
			if model.ViableForCropID(targetCrop.ID) && model.Created > bestModel.Created {
				bestModel = model
			}
		}
	}

	if recommendedModel != nil {
		recommendedModelGauge.Set(1)
		m.logger.Debugf("best model for cropID: %s - %s [recommended]", cropID, recommendedModel.ID)
		return recommendedModel, nil
	}
	if lastActiveModel != nil {
		lastActiveModelGauge.Set(1)
		m.logger.Debugf("best model for cropID: %s - %s [last_active]", cropID, lastActiveModel.ID)
		return lastActiveModel, nil
	}
	if bestModel.ID != "" {
		viableModelGauge.Set(1)
		m.logger.Debugf("best model for cropID: %s - %s [viable]", cropID, bestModel.ID)
		return bestModel, nil
	}
	m.TriggerCropIDSync(false)
	m.TriggerDownload()
	return nil, fmt.Errorf("could not find best model for crop id: %s", cropID)
}

func (m *ModelManagerWatcher) getBestModel(modelType veselka.ModelType) (*model_manager.Model, error) {
	var bestModel *model_manager.Model
	var err error
	if modelType == veselka.ModelTypeDeepweed {
		cropID := m.getConfigCurrentCropID()
		bestModel, err = m.getBestModelForCropID(cropID)
	}
	if modelType == veselka.ModelTypeP2P {
		bestModel, err = m.getBestP2PModel()
	}

	if err != nil {
		return nil, err
	}
	return bestModel, nil
}

func (m *ModelManagerWatcher) ReloadModel(modelType veselka.ModelType) error {
	m.logger.Infof("reloading %s model", modelType)
	bestModel, err := m.getBestModel(modelType)
	if err != nil {
		return err
	}

	currentModelId := m.getConfigModelID(modelType)
	if currentModelId != bestModel.ID {
		if modelType == veselka.ModelTypeDeepweed {
			if err := UpdateCropConfigLastActiveModel(m.configClient, m.getConfigCurrentCropID(), bestModel.ID); err != nil {
				m.logger.WithError(err).Warnf("failed to update last active model: %s - %v", bestModel.ID, err)
			}
		}
		m.logger.Infoln("current", modelType, "model:", bestModel.ID)
		modelIDKey := path.Join(commonNodeName, string(modelType), modelIDNodeName)
		if err := m.configClient.SetStringValue(modelIDKey, bestModel.ID); err != nil {
			m.logger.WithError(err).Warnf("failed to set config key: %s", modelIDKey)
		}
		cropID := ""
		switch modelType {
		case veselka.ModelTypeDeepweed:
			cropID = m.getConfigCurrentCropID()
			gh, err := m.getCurrentGeoHash()
			if err != nil {
				m.logger.Warn(err)
			}
			if err := m.UpdateModelParametersFromRemote([]veselka.ModelParameterRequest{{CropID: cropID, ModelID: bestModel.ID, RobotID: m.env.MakaRobotName, Geohash: gh}}); err != nil {
				m.logger.WithError(err).Warnf("failed to update model parameters cropID: %s modelID: %s, robotID: %s, Geohash: %s", cropID, bestModel.ID, m.env.MakaRobotName, gh)
			}
			if config.FeatureFlagEnabledIfExists(m.commonNode, config.FeatureFlagModelPinningDefault) {
				if err := UpdateCropConfigPinnedModel(m.configClient, cropID, bestModel.ID); err != nil {
					m.logger.WithError(err).Warn("failed to update")
				}
			}
			if config.FeatureFlagEnabledIfExists(m.commonNode, config.FeatureFlagCopyModelinator) {
				if err := m.modelinatorConfigClient.CopyFrom(cropID, currentModelId, bestModel.ID); err != nil {
					m.logger.WithError(err).Warnf("failed to copy modelinator config state cropID: %s, oldModelID: %s, newModelID: %s", cropID, currentModelId, bestModel.ID)
				}
			}
			m.modelState.WriteOnCurrent(func() {
				m.modelState.CurrentDeepWeedModelId = bestModel.ID
			})

		case veselka.ModelTypeP2P:
			m.modelState.WriteOnCurrent(func() {
				m.modelState.CurrentP2PModelID = bestModel.ID
			})
		}
		if err := m.modelHistoryTracker.SubmitEvent(ModelEvent{
			Type:      Activated,
			ModelID:   bestModel.ID,
			ModelType: string(modelType),
			CropID:    cropID,
		}); err != nil {
			m.logger.WithError(err)
		}
	}
	m.modelState.updateModelCreationGauges()
	return nil
}

func SetConfigDouble(client ConfigClient, key string, value, defaultValue float64) error {
	if value == 0.0 {
		value = defaultValue
	}
	if err := client.SetDoubleValue(key, value); err != nil {
		return err
	}
	return nil
}

type DownloadingProgressState struct {
	Progress        float32
	RemainingTimeMs uint64
}

func compareSemanticVersions(a, b string) (int, error) {
	// can't use semver.Compare because it doesn't handle versions with different number of parts
	aParts := strings.Split(a, ".")
	bParts := strings.Split(b, ".")

	// compare each part
	for i := 0; i < len(aParts) && i < len(bParts); i++ {
		aPart, err := strconv.Atoi(aParts[i])
		if err != nil {
			return 0, err
		}
		bPart, err := strconv.Atoi(bParts[i])
		if err != nil {
			return 0, err
		}

		if aPart < bPart {
			return -1, nil
		} else if aPart > bPart {
			return 1, nil
		}
	}

	return 0, nil
}

func (m *ModelManagerWatcher) fetchTensorRTandComputeCapabilities() ([]string, []string, error) {
	supportedTensorRTVersions, err := m.rows.GetCVSupportedTensorRTVersions()
	if err != nil {
		return nil, nil, fmt.Errorf("CV failed to respond with supported TensorRT versions %w", err)
	}
	cvComputeCapabilities, err := m.rows.GetCVComputeCapabilities()
	if err != nil {
		return nil, nil, fmt.Errorf("CV failed to respond with supported compute capabilities %w", err)
	}
	return supportedTensorRTVersions, cvComputeCapabilities, nil
}

func (m *ModelManagerWatcher) filterModelArtifacts(veselkaModel *veselka.Model) []veselka.ModelArtifact {
	modelArtifactsMap := make(map[string][]veselka.ModelArtifact, 0)
	// Compose map of all model artifacts for all supported tensorRT versions, grouped by
	// compute capability.
	computeCapabilities := make([]string, 0)
	tensorRTVersions := make([]string, 0)
	m.modelState.ReadOnCurrent(func() {
		computeCapabilities = append(computeCapabilities, m.modelState.computeCapabilityCache...)
		tensorRTVersions = append(tensorRTVersions, m.modelState.tensorRTVersionCache...)
	})

	if len(computeCapabilities) == 0 || len(tensorRTVersions) == 0 {
		m.logger.Warn("no compute capabilities or tensorRT versions found in cache")
	}

	m.logger.Infof("compute capabilities: %v, tensorRT versions: %v", computeCapabilities, tensorRTVersions)
	m.logger.Infof("model artifacts: %v", veselkaModel.ModelArtifacts)

	for _, computeCapability := range computeCapabilities {
		for _, tensorRTVersion := range tensorRTVersions {
			// find associated artifacts in veselka model
			for _, artifact := range veselkaModel.ModelArtifacts {
				if artifact.ComputeCapability == computeCapability && artifact.TensorRTVersion == tensorRTVersion {
					if _, ok := modelArtifactsMap[computeCapability]; !ok {
						modelArtifactsMap[computeCapability] = make([]veselka.ModelArtifact, 0)
					}

					modelArtifactsMap[computeCapability] = append(modelArtifactsMap[computeCapability], artifact)
				}
			}
		}
	}

	m.logger.Infof("model artifacts map: %v", modelArtifactsMap)

	// For DeepWeed and P2P models, we should only download artifacts associated with the highest
	// supported tensorRT version. Filter out the lower versions.
	filteredModelArtifacts := make([]veselka.ModelArtifact, 0)
	for _, artifacts := range modelArtifactsMap {
		artifactWithHighestTensorRTVersion := artifacts[0]
		highestTensorRTVersion := "0.0.0"

		for _, artifact := range artifacts {
			compareVersions, err := compareSemanticVersions(artifact.TensorRTVersion, highestTensorRTVersion)
			if err != nil {
				m.logger.WithError(err).Warnf("invalid TensorRT version: %s", artifact.TensorRTVersion)
				continue
			}
			if compareVersions > 0 {
				highestTensorRTVersion = artifact.TensorRTVersion
				artifactWithHighestTensorRTVersion = artifact
			}
		}

		filteredModelArtifacts = append(filteredModelArtifacts, artifactWithHighestTensorRTVersion)
	}

	m.logger.Infof("filtered model artifacts: %v", filteredModelArtifacts)

	return filteredModelArtifacts
}

func (m *ModelManagerWatcher) UpdateModelMetadata(modelID string, forceDownload bool) ([]veselka.ModelArtifact, error) {
	modelArtifacts := make([]veselka.ModelArtifact, 0)
	if forceDownload {
		// download latest model metadata from veselka
		ctx, cancel := context.WithTimeout(context.Background(), requestTimeout)
		veselkaModel, err := m.veselkaClient.GetModelInfo(ctx, modelID)
		cancel()
		if err != nil {
			return nil, err
		}

		if veselkaModel.Type == veselka.ModelTypeP2P || veselkaModel.Type == veselka.ModelTypeDeepweed {
			modelArtifacts = m.filterModelArtifacts(veselkaModel)
			veselkaModel.ModelArtifacts = modelArtifacts
		} else {
			return nil, fmt.Errorf("unsupported model type: %s", veselkaModel.Type)
		}

		modelFileModTimes := make(map[string]time.Time)
		files, err := afero.ReadDir(ModelManagerFs, m.modelMetadataCacheDir)
		if err != nil {
			return nil, err
		}

		for _, metadataFile := range files {
			// skip all non metadata files
			if !strings.HasSuffix(metadataFile.Name(), model_manager.MetaExt) {
				continue
			}
			fileModelID := strings.TrimSuffix(metadataFile.Name(), model_manager.MetaExt)

			// if this is most recent use of that model, update our record
			if modTime, ok := modelFileModTimes[fileModelID]; ok {
				if metadataFile.ModTime().Compare(modTime) > 0 {
					modelFileModTimes[fileModelID] = metadataFile.ModTime()
				}
			} else {
				modelFileModTimes[fileModelID] = metadataFile.ModTime()
			}
		}

		var writeErr error
		var model *model_manager.Model
		m.modelState.WriteOnCurrent(func() {
			model = &model_manager.Model{
				Model: *veselkaModel,
			}
			if fileModel, ok := m.modelState.LocalModels[model.ID]; ok {
				model.Merge(fileModel)
			}
			if modelModTime, ok := modelFileModTimes[model.ID]; ok && model.Downloaded.IsZero() {
				model.Downloaded = modelModTime
			}

			if err := m.writeModelMetadata(model); err != nil {
				writeErr = err
				return
			}
			m.modelState.LocalModels[model.ID] = model
		})
		if writeErr != nil {
			return nil, err
		}
	} else {
		// attempt to read model metadata from local metadata directory
		metaFilePath := filepath.Join(m.modelMetadataCacheDir, modelID+model_manager.MetaExt)
		localModel, err := model_manager.GetVerifyMetadata(metaFilePath)
		if err != nil {
			m.logger.Warnf("invalid metadata file: %s - %v, re-downloading from veselka", metaFilePath, err)
			return m.UpdateModelMetadata(modelID, true)
		}
		modelArtifacts = localModel.ModelArtifacts
	}

	return modelArtifacts, nil
}

func (m *ModelManagerWatcher) getCurrentGeoHash() (string, error) {
	gpsData, err := m.hardwareManagerClient.GetGPSData(false)
	if err != nil {
		return "", err
	}
	if lla := gpsData.GetLla(); lla != nil {
		return geohash.EncodeWithPrecision(lla.GetLat(), lla.GetLng(), geohashPrecision), nil
	}
	return "", fmt.Errorf("failed to encode gps data to geohash")
}

func (m *ModelManagerWatcher) getEnabledCropModels() ([]string, error) {
	models := make([]string, 0)
	currentCropID := m.getConfigCurrentCropID()
	for _, enabledCrop := range m.EnabledCropList(false) {
		cropIDModels := make([]string, 0)
		if enabledCrop.PinnedModel != "" {
			cropIDModels = append(cropIDModels, enabledCrop.PinnedModel)
		}
		if enabledCrop.RecommendedModel != "" {
			cropIDModels = append(cropIDModels, enabledCrop.RecommendedModel)
		}
		if len(enabledCrop.MaintainedModels) > 0 {
			cropIDModels = append(cropIDModels, enabledCrop.MaintainedModels...)
		}

		modelsToAdd := make([]string, 0)
		modelsToAdd = append(modelsToAdd, cropIDModels...)

		// put the current models to the front of the list.
		if currentCropID == enabledCrop.ID {
			models = append(modelsToAdd, models...)
		} else {
			models = append(models, modelsToAdd...)
		}
	}
	return models, nil
}

func (m *ModelManagerWatcher) getP2PModel() (string, error) {
	// get pinned
	pinnedP2PModelID := m.getPinnedP2P()
	if len(pinnedP2PModelID) > 0 {
		return pinnedP2PModelID, nil
	}
	// if no pinned, retrieve default

	var defaultP2PModelID string
	var lastDefaultP2PModelUpdate time.Time
	m.modelState.ReadOnCurrent(func() {
		defaultP2PModelID = m.modelState.defaultP2PModelID
		lastDefaultP2PModelUpdate = m.modelState.lastDefaultP2PModelUpdate
	})
	if defaultP2PModelID == "" || time.Since(lastDefaultP2PModelUpdate) > defaultP2PModelRefreshInterval {
		ctx, cancel := context.WithTimeout(context.Background(), requestTimeout)
		defer cancel()
		vm, err := m.veselkaClient.GetDefaultP2PModel(ctx, m.currentModelVersion[veselka.ModelTypeP2P])
		if err != nil {
			m.logger.WithError(err).Warnf("failed to retrieve default p2p model")
		} else {
			defaultP2PModelID = vm.ID
			m.modelState.WriteOnCurrent(func() {
				m.modelState.defaultP2PModelID = defaultP2PModelID
				m.modelState.lastDefaultP2PModelUpdate = lastDefaultP2PModelUpdate
			})
		}
	}
	if defaultP2PModelID != "" {
		return defaultP2PModelID, nil
	}
	return "", fmt.Errorf("failed to determine p2p for download")
}

func (m *ModelManagerWatcher) getModelsToDownload() ([]string, error) {
	models := make([]string, 0)

	if p2pModelID, err := m.getP2PModel(); err != nil {
		m.logger.Error(err)
	} else {
		models = append(models, p2pModelID)
	}

	enabledCropModelIDs, err := m.getEnabledCropModels()
	if err != nil {
		return nil, err
	}
	models = append(models, enabledCropModelIDs...)

	// filter for models that are unverified, not in the local modelState cache, or
	// contain artifacts that are not downloaded
	modelsToDownload := make([]string, 0)
	m.modelState.WriteOnCurrent(func() {
		for _, modelID := range models {
			metaFilePath := filepath.Join(m.modelMetadataCacheDir, modelID+model_manager.MetaExt)
			model, err := model_manager.GetVerifyMetadata(metaFilePath)
			if err != nil {
				modelsToDownload = append(modelsToDownload, modelID)
				continue
			}

			// make sure we have model artifacts for the latest supported tensorRT version and compute capability
			for _, computeCapability := range m.modelState.computeCapabilityCache {
				for _, tensorRTVersion := range m.modelState.tensorRTVersionCache {
					found := false
					for _, artifact := range model.ModelArtifacts {
						if artifact.ComputeCapability == computeCapability && artifact.TensorRTVersion == tensorRTVersion {
							found = true
							break
						}
					}
					if !found {
						modelsToDownload = append(modelsToDownload, modelID)
						break
					}
				}
			}

			if _, ok := m.modelState.LocalModels[modelID]; !ok {
				modelsToDownload = append(modelsToDownload, modelID)
				continue
			}

			for _, artifact := range model.ModelArtifacts {
				modelArtifactFilePath := filepath.Join(m.modelArtifactCacheDir, artifact.Filename())
				if err := model_manager.VerifyModelArtifactDownload(artifact, modelArtifactFilePath); err != nil {
					modelsToDownload = append(modelsToDownload, modelID)
					break
				}
			}
		}
	})
	return modelsToDownload, nil
}

func copyFile(dest, source string) (err error) {
	f, err := ModelManagerFs.Open(source)
	if err != nil {
		return err
	}
	defer f.Close()

	temp := dest + ".tmp"
	destTmp, err := ModelManagerFs.Create(temp)
	if err != nil {
		return err
	}

	defer destTmp.Close()
	copyBytes, err := io.Copy(destTmp, f)
	if err != nil {
		ModelManagerFs.Remove(temp)
		return err
	}
	logrus.Infof("copied %d bytes from %s to %s", copyBytes, source, dest)

	if err = ModelManagerFs.Rename(temp, dest); err != nil {
		ModelManagerFs.Remove(temp)
		return err
	}
	return nil
}

func (m *ModelManagerWatcher) DownloadModel(ctx context.Context, modelID string) error {
	// update model metadata with mod time and artifacts list
	modelArtifacts, err := m.UpdateModelMetadata(modelID, true)
	if err != nil {
		return fmt.Errorf("failed to update model metadata: %v", err)
	}

	if len(modelArtifacts) == 0 {
		return fmt.Errorf("veselka returned no model artifacts for model %s", modelID)
	}

	for _, artifact := range modelArtifacts {
		if err := m.DownloadModelArtifact(ctx, artifact); err != nil {
			return fmt.Errorf("failed to download model artifact %s: %v", artifact.ModelManagerArtifactID(), err)
		}
		m.modelState.WriteOnCurrent(func() { delete(m.modelState.DownloadingModelArtifacts, artifact.ModelManagerArtifactID()) })
		m.logger.Infof("confirmed download of model artifact %v", artifact.ModelManagerArtifactID())
	}

	m.logger.Infof("downloads of all artifacts for model %v completed", modelID)
	m.triggerSync()
	return nil
}

// Updates the model metadata with information about each of the model artifacts.
// Downloads each of the model artifacts and writes them to disk.
func (m *ModelManagerWatcher) downloadModels(ctx context.Context) error {
	if err := ModelManagerFs.MkdirAll(m.modelMetadataCacheDir, os.ModePerm); err != nil {
		m.logger.Warningf("Couldn't create model cache folder %v", err)
		return err
	}
	if err := ModelManagerFs.MkdirAll(m.modelArtifactCacheDir, os.ModePerm); err != nil {
		m.logger.Warningf("Couldn't create model artifact cache folder %v", err)
		return err
	}

	// fetch TensorRT and compute capability versions from CV, cache them for use in model downloads
	supportedTensorRTVersions, cvComputeCapabilities, err := m.fetchTensorRTandComputeCapabilities()
	if err != nil {
		return err
	}
	m.modelState.WriteOnCurrent(func() {
		m.modelState.tensorRTVersionCache = supportedTensorRTVersions
		m.modelState.computeCapabilityCache = cvComputeCapabilities
	})

	if len(supportedTensorRTVersions) == 0 || len(cvComputeCapabilities) == 0 {
		m.logger.Warnf("no supported TensorRT versions or compute capabilities found, skipping model download")
		return nil
	}

	// update the metadata of our existing local models, ensuring we have the most
	// up to date artifacts from veselka corresponding with what our cv computers need
	for modelID, _ := range m.GetLocalModels() {
		if _, err := m.UpdateModelMetadata(modelID, false); err != nil {
			m.logger.Warnf("failed to update model %s metadata: %v", modelID, err)
		}
	}

	modelsToDownload, err := m.getModelsToDownload()
	if err != nil {
		return err
	}

	m.logger.Infoln(len(modelsToDownload), "models awaiting download:", modelsToDownload)

	for _, modelID := range modelsToDownload {
		if err = m.DownloadModel(ctx, modelID); err != nil {
			return err
		}
	}
	return nil
}

func (m *ModelManagerWatcher) downloadModelsLoop(stopCtx context.Context) {
	maxBackoff := downloadTriggerInterval
	backoffDur := time.Minute
	for {
		select {
		case <-stopCtx.Done():
			return
		case <-m.downloadChannel:
		}
		if err := m.downloadModels(stopCtx); err != nil {
			if backoffDur > maxBackoff {
				backoffDur = maxBackoff
			}
			m.logger.Infoln("download error encountered:", err, "backing off:", backoffDur)
			time.Sleep(backoffDur)
			backoffDur = backoffDur * 2
		} else {
			backoffDur = time.Minute
		}

		m.modelState.WriteOnCurrent(func() {
			now := time.Now()
			m.logger.Debugln("last model download:", now)
			m.modelState.lastModelDownload = now
			lastModelDownloadGauge.SetToCurrentTime()
		})
	}
}

func (m *ModelManagerWatcher) cleanupModels(stopCtx context.Context, interval time.Duration) {
	for {
		select {
		case <-stopCtx.Done():
			return
		case <-time.After(interval):
		}
		cropIDModelsToKeep := make(map[string]bool)

		for _, enabledCrop := range m.EnabledCropList(false) {
			cropIDModelsToKeep[enabledCrop.RecommendedModel] = true
			cropIDModelsToKeep[enabledCrop.PinnedModel] = true
			cropIDModelsToKeep[enabledCrop.LastActiveModel] = true
			for _, modelID := range enabledCrop.MaintainedModels {
				cropIDModelsToKeep[modelID] = true
			}
		}

		m.modelCacheWriteMutex.Lock()
		m.modelState.ReadOnCurrent(func() {
			defer m.modelCacheWriteMutex.Unlock()

			mostRecentModelsByType := make(map[veselka.ModelType]*model_manager.Model)
			for _, model := range m.modelState.LocalModels {
				if mostRecentModel, ok := mostRecentModelsByType[model.Type]; ok {
					if mostRecentModel.Created < model.Created {
						mostRecentModelsByType[model.Type] = model
					}
				} else {
					mostRecentModelsByType[model.Type] = model
				}
			}

			modelsToRemove := make([]string, 0)
			for _, model := range m.modelState.LocalModels {
				if mostRecentModelsByType[model.Type] == model {
					continue
				}
				if _, ok := cropIDModelsToKeep[model.ID]; ok {
					continue
				}
				days := m.commanderNode.GetNode(modelExpirationDaysNodeName).GetUIntValue()
				daysDuration := time.Duration(days) * 24 * time.Hour
				if model.IsExpired(daysDuration) {
					m.logger.Infoln("removing expired model:", model.ID, "-", model.String())
					model_manager.DeleteModel(model, m.modelMetadataCacheDir, m.modelArtifactCacheDir)
					modelsToRemove = append(modelsToRemove, model.ID)
				}
			}
			for _, modelID := range m.rows.GetAllModels() {
				if _, ok := m.modelState.LocalModels[modelID]; !ok {
					modelsToRemove = append(modelsToRemove, modelID)
				}
			}
			if len(modelsToRemove) > 0 {
				for _, row := range m.rows {
					go func(row *RowClient, models []string) {
						if _, err := row.client.CleanupModels(modelsToRemove); err != nil {
							m.logger.WithError(err).Warnf("row %s failed to clean up models", row.addr)
						}
					}(row, modelsToRemove)
				}
			}

			// Support for downgrading away from model artifacts
			//
			// If any models files in old location exist without metdata files, remove them.
			// This avoids downgrade issues where models are not removed from the old location
			// but are no longer used or expired. Upon downgrade, existence of these .trt would
			// cause model_manager to continuously retry and fail to remove the metadata file
			// that does not exist.
			files, err := afero.ReadDir(ModelManagerFs, m.modelMetadataCacheDir)
			if err != nil {
				m.logger.Warnf("failed to read model metadata cache folder: %v", err)
			} else {
				for _, oldModelFile := range files {
					if !strings.HasSuffix(oldModelFile.Name(), model_manager.ModelExt) {
						continue
					}
					modelID := strings.TrimSuffix(oldModelFile.Name(), model_manager.ModelExt)
					metaFilename := filepath.Join(m.modelMetadataCacheDir, modelID+model_manager.MetaExt)

					if _, err := ModelManagerFs.Stat(metaFilename); err != nil {
						m.logger.Infof("removing old model file from model cache dir %s", oldModelFile.Name())
						modelFilePath := filepath.Join(m.modelMetadataCacheDir, oldModelFile.Name())
						if err := ModelManagerFs.Remove(modelFilePath); err != nil {
							logrus.Warnf("failed to remove %s - %v", modelFilePath, err)
						}

					}
				}
			}
		})

		m.modelState.WriteOnCurrent(func() {
			now := time.Now()
			m.logger.Debugln("last model cleanup:", now)
			m.modelState.lastModelCleanup = now
			lastModelCleanupGauge.SetToCurrentTime()
		})
	}
}

func (m *ModelManagerWatcher) syncModels(stopCtx context.Context) {
	for {
		select {
		case <-stopCtx.Done():
			return
		case <-m.syncChannel:
		}

		pushModelToRowFn := func(row *RowClient, model *model_manager.Model, rowSyncedModels, rowSyncedArtifacts []string) error {
			if StringInSlice(rowSyncedModels, model.ID) {
				// confirm rows have all artifacts associated with the model
				foundUnsyncedArtifact := false
				for _, artifact := range model.ModelArtifacts {
					if !StringInSlice(rowSyncedArtifacts, artifact.ModelManagerArtifactID()) {
						m.logger.Infof("row %s missing artifact: %s, syncing", row.addr, artifact.ModelManagerArtifactID())
						foundUnsyncedArtifact = true
					}
				}

				if !foundUnsyncedArtifact {
					return nil
				}
			}
			metaFile, err := afero.ReadFile(ModelManagerFs, filepath.Join(m.modelMetadataCacheDir, model.MetaFilename()))
			if err != nil {
				return fmt.Errorf("failed to read metadata: %v", err)
			}

			modelMetadata := model_manager.Model{}
			if err := json.Unmarshal(metaFile, &modelMetadata); err != nil {
				return fmt.Errorf("failed to unmarshal model metadata from file:%s - %w", model.MetaFilename(), err)
			}

			modelArtifactFileNameMapping := make(map[string]string, 0)
			for _, artifact := range modelMetadata.ModelArtifacts {
				// We rename artifact files before writing them to the row computer.
				// Artifact files are written to a folder named after the associated
				// model ID and named sm{compute_capability}_trt{tensorrt_version}.trt
				modelArtifactFileName := model_manager.ComposeModelArtifactFilename(artifact.ComputeCapability, artifact.TensorRTVersion)
				modelArtifactFileNameMapping[modelArtifactFileName] = filepath.Join(m.modelArtifactCacheDir, artifact.Filename())
			}

			if len(modelArtifactFileNameMapping) == 0 {
				m.logger.Warnf("no model artifacts associated with model: %s, skipping sync", model.ID)
			} else {
				if _, err := row.client.DownloadModelMetadata(&model_receiver.DownloadModelMetadataRequest{ModelId: model.ID, MetadataContents: metaFile}); err != nil {
					return fmt.Errorf("failed to push model %s metadata: %v", model.ID, err)
				}

				for artifactFileDestName, localArtifactName := range modelArtifactFileNameMapping {
					modelArtifactContents, err := afero.ReadFile(ModelManagerFs, localArtifactName)
					if err != nil {
						return fmt.Errorf("failed to read model: %v", err)
					}

					if _, err := row.client.DownloadModelArtifact(&model_receiver.DownloadModelArtifactRequest{ModelId: model.ID, ArtifactName: artifactFileDestName, ArtifactContents: modelArtifactContents}); err != nil {
						return fmt.Errorf("failed to push model %s artifact: %v", model.ID, err)
					}
				}
			}

			return nil
		}

		priorityModels := make([]string, 0)
		for _, modelType := range modelManagerWatcherEnabledModelTypes {
			currentModel := m.getConfigModelID(modelType)
			m.touchLastUsed(currentModel)
			priorityModels = append(priorityModels, currentModel)
		}

		localModels := m.GetLocalModels()
		for _, row := range m.rows {
			// need copy as we delete from the map as we go to avoid double syncing
			localModelsCopy := CopyMap(localModels)
			if !row.connected.Load() {
				continue
			}

			rowSyncedModels := row.GetSyncedModels()
			rowSyncedArtifacts := row.GetSyncedArtifacts()
			for _, priorityModel := range priorityModels {
				if localCurrent, ok := localModelsCopy[priorityModel]; ok {
					if err := pushModelToRowFn(row, localCurrent, rowSyncedModels, rowSyncedArtifacts); err != nil {
						m.logger.WithError(err).Warnf("failed to push priority model: %s to %s", localCurrent.ID, row.addr)
					}
					delete(localModelsCopy, priorityModel)
				}
			}

			for _, model := range localModelsCopy {
				if err := pushModelToRowFn(row, model, rowSyncedModels, rowSyncedArtifacts); err != nil {
					m.logger.WithError(err).Warnf("failed to push model: %s to %s", model.ID, row.addr)
				}
			}
		}

		m.modelState.WriteOnCurrent(func() {
			now := time.Now()
			m.logger.Debugln("last model sync:", now)
			m.modelState.lastModelSync = now
			lastModelSyncGauge.SetToCurrentTime()
		})
	}
}

func StringInSlice(slice []string, s string) bool {
	for _, str := range slice {
		if str == s {
			return true
		}
	}
	return false
}

func (m *ModelManagerWatcher) triggerSync() {
	select {
	case m.syncChannel <- true:
	default: // no block
	}
}

func (m *ModelManagerWatcher) TriggerDownload() {
	select {
	case m.downloadChannel <- true:
	default: // no block
	}
}

func (m *ModelManagerWatcher) triggerUpdateSynced() {
	select {
	case m.updateSyncedCh <- true:
	default: // no block
	}
}

func (m *ModelManagerWatcher) TriggerPointCatsSync(syncFromVeselka bool) {
	select {
	case m.syncPointCatsCh <- syncFromVeselka:
	default: // no block
	}
}

func (m *ModelManagerWatcher) ForceModelUpdate() {
	m.logger.Debug("force model update")
	var currentCropID string
	m.modelState.ReadOnCurrent(func() {
		currentCropID = m.modelState.currentCropID
	})
	if currentCropID != m.currentCropIDNode.GetStringValue() {
		m.modelState.WriteOnCurrent(func() {
			m.modelState.Synchronized = false
			m.modelState.currentCropID = m.currentCropIDNode.GetStringValue()
		})
	}
}

// GetAllKnownModels returns a map of modelID:model for all local and downloading models.
func (m *ModelManagerWatcher) GetAllKnownModels() map[string]*model_manager.Model {
	allModels := make(map[string]*model_manager.Model)
	m.modelState.ReadOnCurrent(func() {
		for _, model := range m.modelState.LocalModels {
			if _, ok := allModels[model.ID]; !ok {
				allModels[model.ID] = model
			}
		}
		for _, downloadingModel := range m.modelState.DownloadingModelArtifacts {
			model := downloadingModel.Model
			if _, ok := allModels[model.ID]; !ok {
				allModels[model.ID] = model
			}
		}
	})
	return allModels
}

func (m *ModelManagerWatcher) touchLastUsed(modelID string) {
	var model *model_manager.Model
	var modelPtr *model_manager.Model
	var ok bool

	m.modelState.WriteOnCurrent(func() {
		modelPtr, ok = m.modelState.SyncedModels[modelID]
		if !ok {
			return
		}
		now := time.Now()
		modelPtr.LastUsed = now
		modelPtr.LastUsedTimestamp = uint64(now.UnixMilli())
	})

	m.modelState.ReadOnCurrent(func() {
		modelPtr, ok = m.modelState.SyncedModels[modelID]
		if !ok {
			return
		}
		model = modelPtr
	})
	if !ok {
		return
	}

	model.LastUsedCropPointThreshold = m.commonNode.GetNode(path.Join(deepweedNodeName, cropPointThresholdNodeName)).GetFloatValue()
	model.LastUsedWeedPointThreshold = m.commonNode.GetNode(path.Join(deepweedNodeName, weedPointThresholdNodeName)).GetFloatValue()

	err := m.writeModelMetadata(model)
	if err != nil {
		m.logger.Error(err)
	}
}

func (m *ModelManagerWatcher) writeModelMetadata(model *model_manager.Model) error {
	m.modelCacheWriteMutex.Lock()
	defer m.modelCacheWriteMutex.Unlock()

	model.ModelParameters = nil // don't ever write this to disk
	jsonMetadata, err := json.Marshal(model)
	if err != nil {
		return err
	}
	modelMetaFileName := filepath.Join(m.modelMetadataCacheDir, model.MetaFilename())
	if err := afero.WriteFile(ModelManagerFs, modelMetaFileName+".tmp", jsonMetadata, 0666); err != nil {
		return err
	}
	return ModelManagerFs.Rename(modelMetaFileName+".tmp", modelMetaFileName)
}

func (m *ModelManagerWatcher) PinModel(modelID, cropID string) error {
	m.logger.Debugf("pin model: %s, cropID: %s", modelID, cropID)
	if err := UpdateCropConfigPinnedModel(m.configClient, cropID, modelID); err != nil {
		return err
	}
	m.modelState.WriteOnCurrent(func() { m.modelState.Synchronized = false })
	return nil
}

func (m *ModelManagerWatcher) UnpinModel(cropID string) error {
	m.logger.Debugf("unpin cropID: %s", cropID)
	if err := UpdateCropConfigPinnedModel(m.configClient, cropID, ""); err != nil {
		return err
	}
	m.modelState.WriteOnCurrent(func() { m.modelState.Synchronized = false })
	return nil
}

func (m *ModelManagerWatcher) PinP2PModel(modelID string) error {
	m.logger.Debugf("pin p2p model: %s", modelID)
	if err := UpdateP2PPinnedModel(m.configClient, modelID); err != nil {
		return err
	}
	m.modelState.WriteOnCurrent(func() { m.modelState.Synchronized = false })
	return nil
}

func (m *ModelManagerWatcher) UnpinP2PModel() error {
	m.logger.Debugln("unpinning p2p")
	if err := UpdateP2PPinnedModel(m.configClient, ""); err != nil {
		return err
	}
	m.modelState.WriteOnCurrent(func() { m.modelState.Synchronized = false })
	return nil
}

func (m *ModelManagerWatcher) GetCacheAllCrops(updateCache bool) (retrievedCrops []*veselka.Crop, err error) {
	cacheFile := filepath.Join(m.env.MakaDataDir, allCropsCacheFilename)
	ctx, cancel := context.WithTimeout(context.Background(), requestTimeout)
	defer cancel()
	if updateCache {
		retrievedCrops, err = m.veselkaClient.GetAllCrops(ctx) // SOURCE of TRUTH
		if err != nil {
			m.logger.WithError(err).Warn("failed to retrieve crops from veselka")
		}
	}
	if err != nil || retrievedCrops == nil {
		b, err := afero.ReadFile(ModelManagerFs, cacheFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load cached crops: %w", err)
		}
		if err := json.Unmarshal(b, &retrievedCrops); err != nil {
			return nil, fmt.Errorf("failed to unmarshal crops from file:%s - %w", cacheFile, err)
		}
	}

	cacheData, err := json.Marshal(retrievedCrops)
	if err != nil {
		m.logger.WithError(err).Warnf("failed to marshal crops to file: %s - %v", cacheFile, err)
	} else {
		if err := afero.WriteFile(ModelManagerFs, cacheFile, cacheData, os.ModePerm); err != nil {
			m.logger.WithError(err).Warnf("failed to write cache file:%s - %v", cacheFile, err)
		}
	}

	return retrievedCrops, nil
}

func (m *ModelManagerWatcher) UpdateEnabledCropList(crops []*veselka.Crop) {
	enabledCrops := make([]EnabledCrop, 0)
	for _, crop := range crops {
		if len(crop.ID) < 1 {
			continue
		}
		cropConfig := GetCropConfigByID(m.cropIDsNode, crop.ID)
		if cropConfig.Enabled {
			enabledCrop := EnabledCrop{Crop: *crop}
			if cropConfig.CropNameOverride != "" {
				enabledCrop.CommonName = cropConfig.CropNameOverride
				enabledCrop.customNameOverride = true
			}
			enabledCrop.PinnedModel = cropConfig.PinnedModel
			enabledCrop.RecommendedModel = cropConfig.RecommendedModel
			enabledCrop.LastActiveModel = cropConfig.LastActiveModel
			enabledCrop.MaintainedModels = cropConfig.MaintainedModels
			enabledCrops = append(enabledCrops, enabledCrop)
		}
	}

	m.modelState.WriteOnCurrent(func() { m.modelState.EnabledCropsList = enabledCrops })
}

func (m *ModelManagerWatcher) getAllPointCategories() ([]veselka.PointCategory, error) {
	ctx, cancel := context.WithTimeout(context.Background(), requestTimeout)
	defer cancel()
	return m.veselkaClient.GetAllPointCategories(ctx)
}

func (m *ModelManagerWatcher) UpdatePointCategoriesRedis(pointCategories []veselka.PointCategory) error {
	idKeyVals := make([]string, 0)
	defKeyVals := make([]string, 0)
	for _, pc := range pointCategories {
		if pc.Archived {
			continue
		}
		var pcJSON []byte
		pcJSON, err := json.Marshal(pc)
		if err != nil {
			return fmt.Errorf("failed to marshal point category: %v", err)
		}
		idKeyVals = append(idKeyVals, pc.ID, pc.Name)
		defKeyVals = append(defKeyVals, pc.ID, string(pcJSON))
	}

	if len(idKeyVals) < 1 {
		return fmt.Errorf("no point categories retrieved")
	}

	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	err := m.redis.HSetWithContext(ctx, crredis.PointCategoryIds, idKeyVals)
	if err != nil {
		return fmt.Errorf("failed to set point category ids: %v", err)
	}
	return m.redis.HSetWithContext(ctx, crredis.PointCategoryDefs, defKeyVals)
}

func (m *ModelManagerWatcher) GetPointCategoryDefsRedis() ([]veselka.PointCategory, error) {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	pointCategoryDefs, err := m.redis.HGetAllWithContext(ctx, crredis.PointCategoryDefs)
	if err != nil {
		return nil, err
	}

	pointCategories := make([]veselka.PointCategory, 0)
	for _, def := range pointCategoryDefs {
		var pc veselka.PointCategory
		if err := json.Unmarshal([]byte(def), &pc); err != nil {
			return nil, fmt.Errorf("failed to unmarshal point category: %v", err)
		}
		pointCategories = append(pointCategories, pc)
	}
	return pointCategories, nil
}

func (m *ModelManagerWatcher) UpdateCurrentPointCategories(pointCategories []veselka.PointCategory) {
	if len(pointCategories) < 1 {
		return
	}

	currentPointCategories := make([]veselka.PointCategory, 0)

	for _, pc := range pointCategories {
		if m.pointCategoriesNode.HasNode(pc.Name) {
			node := m.pointCategoriesNode.GetNode(pc.Name)
			if node == nil {
				continue // point category not in config
			}
			enabledNode := node.GetNode(enabledNodeName)
			if enabledNode == nil {
				continue
			}
			if enabledNode.GetBoolValue() {
				currentPointCategories = append(currentPointCategories, pc)
			}
		}
	}

	m.modelState.WriteOnCurrent(func() { m.modelState.CurrentPointCategories = currentPointCategories })
}

type ModelInfo struct {
	*model_manager.Model
	Active                 bool
	Maintained             bool
	Synced                 bool
	RowsSynced             []bool
	Pinned                 bool
	Recommended            bool
	Downloading            bool
	AllArtifactsDownloaded bool
	DownloadingProgress    float32
	RemainingTimeMs        uint64
	Nickname               string
}

func (m *ModelManagerWatcher) ListModels(cropID string) []ModelInfo {
	m.logger.Debugf("listing models for cropID: %s", cropID)

	activeModels := make(map[string]bool)
	enabledCropList := m.EnabledCropList(false)

	pinnedModelIDs, recommendedModelIDs, maintainedModelIDs := m.getCropIDModels(cropID, enabledCropList)

	for _, pinnedModelNode := range m.pinnedNonCropModelsNode.GetChildrenNodes() {
		pinnedModelIDs[pinnedModelNode.GetStringValue()] = true
	}

	for _, modelType := range modelManagerWatcherEnabledModelTypes {
		modelID := m.getConfigModelID(modelType)
		activeModels[modelID] = true
	}

	results := make([]ModelInfo, 0)
	m.modelState.ReadOnCurrent(func() {
		for _, model := range m.modelState.LocalModels {
			if cropID != "" && model.Type != veselka.ModelTypeDeepweed {
				// skip models that are not relevant to the crop id (eg: p2p)
				continue
			}
			mi := ModelInfo{Model: model}
			mi.Synced, mi.RowsSynced = m.rows.IsSynced(model.ID)
			mi.Active = activeModels[model.ID]
			mi.Recommended = recommendedModelIDs[model.ID]
			mi.Pinned = pinnedModelIDs[model.ID]
			mi.Maintained = maintainedModelIDs[model.ID]
			if nickname, err := m.GetModelNickname(model.ID); err != nil && !errors.Is(err, ErrNotFound) {
				m.logger.Debugln(err)
			} else {
				mi.Nickname = nickname
			}

			allArtifactsDownloaded := true
			for _, artifact := range model.ModelArtifacts {
				// if any of a models artifacts are downloading, set its state to downloading = true
				if downloading, ok := m.modelState.DownloadingModelArtifacts[artifact.ModelManagerArtifactID()]; ok {
					mi.Downloading = true
					mi.DownloadingProgress = downloading.Progress
					mi.RemainingTimeMs = downloading.RemainingTimeMs
					allArtifactsDownloaded = false
				}
			}
			mi.AllArtifactsDownloaded = allArtifactsDownloaded

			mustShow := mi.Active || mi.Recommended || mi.Pinned || mi.Maintained
			if cropID == "" || mustShow || model.ViableForCropID(cropID) {
				results = append(results, mi)
			}
		}
	})

	return results
}

func (m *ModelManagerWatcher) GetModelHistory(query ModelHistoryQuery) ([]ModelEvent, error) {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	return m.modelHistoryTracker.GetModelHistory(ctx, query)
}

func (m *ModelManagerWatcher) getCropIDModels(cropId string, list EnabledCropList) (pinned map[string]bool, recommended map[string]bool, maintained map[string]bool) {
	pinned = make(map[string]bool)
	recommended = make(map[string]bool)
	maintained = make(map[string]bool)

	for _, enabledCrop := range list {
		if len(cropId) > 0 && cropId != enabledCrop.ID {
			continue
		}
		if enabledCrop.PinnedModel != "" {
			pinned[enabledCrop.PinnedModel] = true
		}
		if enabledCrop.RecommendedModel != "" {
			recommended[enabledCrop.RecommendedModel] = true
		}
		for _, maintainedModel := range enabledCrop.MaintainedModels {
			maintained[maintainedModel] = true
		}
	}
	return
}

func (m *ModelManagerWatcher) getConfigModelID(modelType veselka.ModelType) string {
	modelIDKey := path.Join(string(modelType), modelIDNodeName)
	return m.commonNode.GetNode(modelIDKey).GetStringValue()
}

func (m *ModelManagerWatcher) getPinnedP2P() string {
	return m.pinnedNonCropModelsNode.GetNode(p2pNodeName).GetStringValue()
}

func (m *ModelManagerWatcher) getConfigCurrentCropID() string {
	return m.currentCropIDNode.GetStringValue()
}

func (m *ModelManagerWatcher) setConfigCurrentCropID(cropID string) error {
	scopedCommanderNodeName := m.commanderNode.GetName()
	scopedCurrentCropIDNodeName := m.currentCropIDNode.GetName()
	return m.configClient.SetStringValue(path.Join(configGetComputerConfigPrefix(), scopedCommanderNodeName, scopedCurrentCropIDNodeName), cropID)
}

func (m *ModelManagerWatcher) UpdateModelParametersFromRemote(paramReqs []veselka.ModelParameterRequest) error {
	m.logger.Debugf("update parameters from remove called with params: %v", paramReqs)
	ctx, cancel := context.WithTimeout(context.Background(), requestTimeout)
	defer cancel()
	cropModelParams, err := m.veselkaClient.GetModelParameters(ctx, paramReqs)
	if err != nil {
		return err
	}
	for _, cm := range cropModelParams {
		if err := m.SaveDefaultModelParameters(cm.ModelId, cm.CropId, cm); err != nil {
			m.logger.WithError(err).Warnf("failed to save default model params modelID: %s cropID: %s", cm.ModelId, cm.CropId)
		}
	}
	return nil
}

func (m *ModelManagerWatcher) SaveDefaultModelParameters(modelID, cropID string, cfg *almanac.ModelinatorConfig) error {
	b, err := json.Marshal(cfg)
	if err != nil {
		return err
	}
	cfgStr := string(b)
	key := fmt.Sprintf("%s-%s", modelID, cropID)
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	existing, err := m.redis.HGetWithContext(ctx, modelParametersHashKey, key)
	if err != nil && err != redis.Nil {
		return err
	}
	if existing == cfgStr {
		return nil
	}
	if err := m.redis.HSetWithContext(ctx, modelParametersHashKey, key, cfgStr); err != nil {
		return err
	}
	m.modelState.triggerModelParameterCallbacks(cfg)
	if err := m.modelHistoryTracker.SubmitEvent(ModelEvent{
		Type:            DefaultParameterChange,
		ModelID:         modelID,
		CropID:          cropID,
		ModelType:       string(veselka.ModelTypeDeepweed),
		ModelParameters: cfgStr,
	}); err != nil {
		m.logger.WithError(err)
	}
	return nil
}

func (m *ModelManagerWatcher) TrackModelinatorChange(modelID, cropID string, cfg *almanac.ModelinatorConfig) error {
	b, err := json.Marshal(cfg)
	if err != nil {
		return err
	}
	cfgStr := string(b)
	if err := m.modelHistoryTracker.SubmitEvent(ModelEvent{
		Type:            ParameterChange,
		ModelID:         modelID,
		CropID:          cropID,
		ModelType:       string(veselka.ModelTypeDeepweed),
		ModelParameters: cfgStr,
	}); err != nil {
		m.logger.WithError(err)
	}
	return nil
}

func (m *ModelManagerWatcher) GetDefaultModelParameters(modelID, cropID string) (*almanac.ModelinatorConfig, error) {
	key := fmt.Sprintf("%s-%s", modelID, cropID)
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	val, err := m.redis.HGetWithContext(ctx, modelParametersHashKey, key)
	if err != nil {
		return nil, err
	}
	cfg := new(almanac.ModelinatorConfig)
	if err := json.Unmarshal([]byte(val), &cfg); err != nil {
		return nil, err
	}
	return cfg, nil
}

func (m *ModelManagerWatcher) SaveModelNickname(modelID, nickname string, tm time.Time) error {
	if modelID == "" || nickname == "" || tm.IsZero() {
		return fmt.Errorf("invalid parameters. valid modelID, nickname, and timestamp are required")
	}
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	key := fmt.Sprint(modelNicknameKeyPrefix, modelID)
	if _, err := m.redis.ZAdd(ctx, key, &redis.Z{
		Score:  float64(tm.UnixMilli()),
		Member: nickname,
	}); err != nil {
		return err
	}
	if err := m.modelHistoryTracker.SubmitEvent(ModelEvent{
		Type:          NicknameChange,
		ModelID:       modelID,
		ModelNickname: nickname,
		ModelType:     string(veselka.ModelTypeDeepweed),
	}); err != nil {
		m.logger.WithError(err)
	}
	return nil
}

func (m *ModelManagerWatcher) GetModelNickname(modelID string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	key := fmt.Sprint(modelNicknameKeyPrefix, modelID)
	results, err := m.redis.ZRevRangeByScoreWithScores(ctx, key, &redis.ZRangeBy{
		Min:   crredis.NegInfinity,
		Max:   crredis.Infinity,
		Count: 1,
	})
	if err != nil {
		return "", err
	}
	if len(results) < 1 {
		return "", ErrNotFound
	}
	if nickname, ok := results[0].Member.(string); ok {
		return nickname, nil
	}
	return "", fmt.Errorf("failed to get nickname")
}

func (m *ModelManagerWatcher) DeleteModelNickname(modelID string) error {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	key := fmt.Sprint(modelNicknameKeyPrefix, modelID)
	if _, err := m.redis.ZRemRangeByScore(ctx, key, crredis.NegInfinity, crredis.Infinity); err != nil {
		return err
	}
	if err := m.modelHistoryTracker.SubmitEvent(ModelEvent{
		Type:      NicknameDelete,
		ModelID:   modelID,
		ModelType: string(veselka.ModelTypeDeepweed),
	}); err != nil {
		m.logger.WithError(err)
	}
	return nil
}

// CropConfig models a node in the config service crop_ids list
type CropConfig struct {
	ID               string
	CropNameOverride string
	Enabled          bool
	PinnedModel      string
	RecommendedModel string
	LastActiveModel  string
	MaintainedModels []string
}

func GetCropConfigByID(cropIDsNode config.Tree, id string) CropConfig {
	if cropIDsNode == nil {
		return CropConfig{}
	}

	cropTree := cropIDsNode.GetNode(id)
	if cropTree == nil {
		return CropConfig{}
	}
	cropNameOverrideNode := cropTree.GetNode(cropNameOverrideNodeName)
	enabledNode := cropTree.GetNode(enabledNodeName)
	pinnedModelNode := cropTree.GetNode(pinnedModelNodeName)
	recommendedModelNode := cropTree.GetNode(recommendedModelNodeName)
	lastActiveModelNode := cropTree.GetNode(lastActiveModelNodeName)
	maintainedModelsNode := cropTree.GetNode(maintainedModelsNodeName)
	if anyNil(cropNameOverrideNode, enabledNode, pinnedModelNode, recommendedModelNode, lastActiveModelNode, maintainedModelsNode) {
		return CropConfig{}
	}

	defer func() {
		config.PreDeleteConfigTree(cropTree)
		config.PreDeleteConfigTree(cropNameOverrideNode)
		config.PreDeleteConfigTree(enabledNode)
		config.PreDeleteConfigTree(pinnedModelNode)
		config.PreDeleteConfigTree(recommendedModelNode)
		config.PreDeleteConfigTree(lastActiveModelNode)
		config.PreDeleteConfigTree(maintainedModelsNode)
	}()

	maintainedModels := make([]string, 0)
	if maintainedModelsNode != nil {
		for _, maintained := range maintainedModelsNode.GetChildrenNodes() {
			maintainedModels = append(maintainedModels, maintained.GetName())
		}
	}
	return CropConfig{
		ID:               id,
		CropNameOverride: cropNameOverrideNode.GetStringValue(),
		Enabled:          enabledNode.GetBoolValue(),
		PinnedModel:      pinnedModelNode.GetStringValue(),
		RecommendedModel: recommendedModelNode.GetStringValue(),
		LastActiveModel:  lastActiveModelNode.GetStringValue(),
		MaintainedModels: maintainedModels,
	}
}

func UpdateCropConfigLastActiveModel(cfgClient ConfigClient, cropID, modelID string) error {
	prefix := configGetComputerConfigPrefix()
	recModelKey := path.Join(prefix, commanderNodeName, cropIDsNodeName, cropID, lastActiveModelNodeName)
	if err := cfgClient.SetStringValue(recModelKey, modelID); err != nil {
		return fmt.Errorf("failed to set config key: %s value: %s - %w", recModelKey, modelID, err)
	}
	return nil
}

func UpdateCropConfigRecommendedModel(cfgClient ConfigClient, cropID, modelID string) error {
	prefix := configGetComputerConfigPrefix()
	recModelKey := path.Join(prefix, commanderNodeName, cropIDsNodeName, cropID, recommendedModelNodeName)
	if err := cfgClient.SetStringValue(recModelKey, modelID); err != nil {
		return fmt.Errorf("failed to set config key: %s value: %s - %w", recModelKey, modelID, err)
	}
	return nil
}

func UpdateP2PPinnedModel(cfgClient ConfigClient, modelID string) error {
	prefix := configGetComputerConfigPrefix()
	currentModelKey := path.Join(prefix, commanderNodeName, pinnedNonCropModelsNodeName, p2pNodeName)
	if err := cfgClient.SetStringValue(currentModelKey, modelID); err != nil {
		return fmt.Errorf("failed to set config key: %s value: %s - %w", currentModelKey, modelID, err)
	}
	return nil
}

func UpdateCropConfigPinnedModel(cfgClient ConfigClient, cropID, modelID string) error {
	prefix := configGetComputerConfigPrefix()
	currentModelKey := path.Join(prefix, commanderNodeName, cropIDsNodeName, cropID, pinnedModelNodeName)
	if err := cfgClient.SetStringValue(currentModelKey, modelID); err != nil {
		return fmt.Errorf("failed to set config key: %s value: %s - %w", currentModelKey, modelID, err)
	}
	return nil
}

// AddMissingCropConfig adds new crop configurations to the appropriate node in the config svc.
func AddMissingCropConfig(cfgClient ConfigClient, cropsNode config.Tree, crop CropConfig) error {
	if len(crop.ID) < 1 {
		return fmt.Errorf("invalid crop id: %s", crop.ID)
	}
	prefix := configGetComputerConfigPrefix()
	listKey := path.Join(prefix, commanderNodeName, cropIDsNodeName)
	enabledKey := path.Join(prefix, commanderNodeName, cropIDsNodeName, crop.ID, enabledNodeName)
	pinnedModelKey := path.Join(prefix, commanderNodeName, cropIDsNodeName, crop.ID, pinnedModelNodeName)
	recModelKey := path.Join(prefix, commanderNodeName, cropIDsNodeName, crop.ID, recommendedModelNodeName)

	if !cropsNode.HasNode(crop.ID) {
		if err := cfgClient.AddToList(listKey, crop.ID); err != nil {
			return fmt.Errorf("failed to add cropID: %s to config list: %s - %w", crop.ID, listKey, err)
		}
		if err := cfgClient.SetBoolValue(enabledKey, false); err != nil {
			return fmt.Errorf("failed to set config key: %s value: %v - %w", enabledKey, false, err)
		}
		if err := cfgClient.SetStringValue(pinnedModelKey, ""); err != nil {
			return fmt.Errorf("failed to set config key: %s value: %v - %w", pinnedModelKey, "", err)
		}
		if err := cfgClient.SetStringValue(recModelKey, ""); err != nil {
			return fmt.Errorf("failed to set config key: %s value: %v - %w", recModelKey, "", err)
		}
	}
	return nil
}

func AddMissingPointCategoryConfig(cfgClient ConfigClient, pointCategoriesNode config.Tree, pc veselka.PointCategory, enabled bool) error {
	if len(pc.ID) < 1 {
		return fmt.Errorf("invalid point category id: %s", pc.ID)
	}

	listKey := path.Join(commonNodeName, pointCategoriesNodeName)
	enabledKey := path.Join(commonNodeName, pointCategoriesNodeName, pc.Name, enabledNodeName)

	if !pointCategoriesNode.HasNode(pc.Name) {
		if err := cfgClient.AddToList(listKey, pc.Name); err != nil {
			return fmt.Errorf("failed to add point category: %s to config list: %s - %w", pc.Name, listKey, err)
		}
		if err := cfgClient.SetBoolValue(enabledKey, enabled); err != nil {
			return fmt.Errorf("failed to set config key: %s value: %v - %w", enabledKey, enabled, err)
		}
	}

	return nil
}

func CopyMap[K, V comparable](src map[K]V) map[K]V {
	dest := make(map[K]V)
	for k, v := range src {
		dest[k] = v
	}
	return dest
}

func anyNil(in ...any) bool {
	for _, value := range in {
		if value == nil {
			return true
		}
	}
	return false
}

func parseConfigDuration(tree config.Tree, def time.Duration) time.Duration {
	s := tree.GetStringValue()
	dur, err := time.ParseDuration(s)
	if err != nil {
		return def
	}
	return dur
}
