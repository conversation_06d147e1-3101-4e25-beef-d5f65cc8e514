import argparse
import json
import logging
import os
import subprocess
import warnings

import pytest

from lib.common.veselka.client import VeselkaClient

warnings.filterwarnings("ignore", category=DeprecationWarning)  # isort:skip

NUM_GPUS = os.getenv("NUM_GPUS", "1")

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


def call(command: str) -> None:
    LOG.info(f"\nRunning: {command}\n\n")
    subprocess.check_call(
        command, shell=True,
    )


def get_prefix(disable_cache_proxy: bool) -> str:
    prefix = "WANDB_MODE=offline"
    if disable_cache_proxy:
        prefix = prefix + " DISABLE_S3_CACHE_PROXY=true"
    return prefix


pipeline_id = "0339ae02-4423-4aaa-9578-d53342b943c7"  # Garlic

geo_fine_tune_geohashes = ["9t", "9q"]
fine_tune_dl_config = {
    "goal_percentage_new": 0.5,
    "use_recency": False,
    "use_geohash": True,
    "emphasized_geohashes": geo_fine_tune_geohashes,
    "new_data_weight": 0.5,
    "dataset_balance_geohash": 4,
    "batch_size": 2,
}


@pytest.mark.dl_release_full
def test_standard_jobs_full(disable_cache_proxy: bool = False) -> None:
    from deeplearning.scripts.utils.utils import generate_model_id

    pretrain_model_id = generate_model_id()
    no_crop_model_id = generate_model_id()
    driptape_model_id = generate_model_id()
    full_train_model_id = generate_model_id()
    furrows_model_id = generate_model_id()

    prefix = get_prefix(disable_cache_proxy)

    # Deepweed
    dl_config = {"batch_size": 2}
    call(
        f"{prefix} torchrun --nproc-per-node {NUM_GPUS} -m deeplearning.scripts.deepweed.driptape --job-id {driptape_model_id} --dl-config '{json.dumps(dl_config)}' --fast-run"
    )

    call(
        f"{prefix} python -m deeplearning.scripts.deepweed.pretrain_spawn --nproc-per-node {NUM_GPUS} --job-id {pretrain_model_id} --dl-config '{json.dumps(dl_config)}' --fast-run "
    )

    call(
        f"{prefix} python -m deeplearning.scripts.deepweed.train_spawn --nproc-per-node {NUM_GPUS} --fast-run --pretrained-model {pretrain_model_id} --no-autodetect-driptape-model --job-id {full_train_model_id} --pipeline-id {pipeline_id} --dl-config '{json.dumps(dl_config)}'"
    )

    call(
        f"{prefix} python -m deeplearning.scripts.deepweed.train_spawn --nproc-per-node {NUM_GPUS} --disable-crop --fast-run --pretrained-model {pretrain_model_id} --no-autodetect-driptape-model --job-id {no_crop_model_id} --pipeline-id {pipeline_id} --dl-config '{json.dumps(dl_config)}'"
    )

    call(
        f"{prefix} torchrun --nproc-per-node 1 -m deeplearning.scripts.deepweed.evaluate --model-id {full_train_model_id} --fast-run --dl-config '{json.dumps(dl_config)}'"
    )

    # Geo FiT
    call(
        f"{prefix} python -m deeplearning.scripts.deepweed.fine_tune_spawn --nproc-per-node {NUM_GPUS} --pretrained-model {full_train_model_id} --no-autodetect-driptape-model --pipeline-id {pipeline_id} --fast-run --dl-config '{json.dumps(fine_tune_dl_config)}'"
    )

    # Traditional FiT
    call(
        f"{prefix} python -m deeplearning.scripts.deepweed.fine_tune_spawn --nproc-per-node {NUM_GPUS} --pretrained-model {full_train_model_id} --no-autodetect-driptape-model --pipeline-id {pipeline_id} --fast-run --dl-config '{json.dumps(dl_config)}' --learning-rate 0.000250 --lr-gamma 0.500000 --lr-milestones 10,15,20 --num-balanced-examples 2000 --goal-percentage-new 0.300000"
    )

    # P2P
    call(
        f"{prefix} torchrun --nproc-per-node 1 -m deeplearning.scripts.p2p.train --pretrained-deepweed-model c5cca0506888939a6fd0f44f920f1c90 --fast-run --dl-config '{json.dumps(dl_config)}'"
    )

    # Furrows
    call(
        f"{prefix} torchrun --nproc-per-node {NUM_GPUS} -m deeplearning.scripts.furrows.train --fast-run --job-id {furrows_model_id} --dl-config '{json.dumps(dl_config)}'"
    )
    call(
        f"{prefix} python -m deeplearning.scripts.furrows.trt_convert --model-id {furrows_model_id} --tensorrt-version 10.0.1"
    )

    # Comparison
    call(
        f"{prefix} torchrun --nproc-per-node {NUM_GPUS} -m deeplearning.scripts.comparison.train --fast-run --train-batch-size 2 --val-batch-size 2 --data-pipeline-processes 2"
    )

    call(
        f"{prefix} torchrun --nproc-per-node {NUM_GPUS} -m deeplearning.scripts.comparison.evaluate --fast-run --pipeline-id {pipeline_id}"
    )

    # Self Supervised
    call(f"{prefix} python -m deeplearning.scripts.self_supervised.train --fast-run")


individual_dl_config = {"make_trt_model": False, "ci_run": True, "batch_size": 2}
individual_fine_tune_dl_config = {
    **fine_tune_dl_config,
    **individual_dl_config,
}


@pytest.mark.dl_release_fast
def test_standard_jobs_driptape(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)

    client = VeselkaClient()
    _, model_id = client.get_driptape_version(
        container_version="none", fast_run=True, environment="development", deploy=False
    )
    model_info = client.get_model_info(model_id)
    dataset_id = model_info["dataset_id"]

    call(
        f"{prefix} torchrun --nproc-per-node {NUM_GPUS} -m deeplearning.scripts.deepweed.driptape --fast-run --dataset-id {dataset_id} --dl-config '{json.dumps(individual_dl_config)}'"
    )


@pytest.mark.dl_release_fast
def test_standard_jobs_p2p(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)

    client = VeselkaClient()
    _, pretrain_model_id = client.get_pretrain_version()

    call(
        f"{prefix} torchrun --nproc-per-node 1 -m deeplearning.scripts.p2p.train --pretrained-deepweed-model {pretrain_model_id} --fast-run  --dl-config '{json.dumps(individual_dl_config)}'"
    )


@pytest.mark.dl_release_fast
def test_standard_jobs_furrows(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)

    call(
        f"{prefix} torchrun --nproc-per-node {NUM_GPUS} -m deeplearning.scripts.furrows.train --fast-run  --dl-config '{json.dumps(individual_dl_config)}'"
    )


@pytest.mark.dl_release_fast
def test_standard_jobs_comparison(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)

    call(
        f"{prefix} torchrun --nproc-per-node {NUM_GPUS} -m deeplearning.scripts.comparison.train --fast-run --val-batch-size 2 --data-pipeline-processes 2 --dl-config '{json.dumps(individual_dl_config)}'"
    )


@pytest.mark.dl_release_fast
def test_standard_jobs_comparison_evaluate(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)

    client = VeselkaClient()

    _, model_id = client.get_full_train_version(
        pipeline_id=pipeline_id, container_version="none", deploy=False, fast_run=True, environment="development"
    )
    model_info = client.get_model_info(model_id)
    dataset_id = model_info["dataset_id"]

    call(
        f"{prefix} torchrun --nproc-per-node {NUM_GPUS} -m deeplearning.scripts.comparison.evaluate --pipeline-id {pipeline_id} --dataset-id {dataset_id} --fast-run --dl-config '{json.dumps(individual_dl_config)}'"
    )


@pytest.mark.dl_release_fast
def test_standard_jobs_train_spawn(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)

    client = VeselkaClient()
    _, pretrain_model_id = client.get_pretrain_version()

    _, model_id = client.get_full_train_version(
        pipeline_id=pipeline_id, container_version="none", deploy=False, fast_run=True, environment="development"
    )
    model_info = client.get_model_info(model_id)
    dataset_id = model_info["dataset_id"]

    call(
        f"{prefix} python -m deeplearning.scripts.deepweed.train_spawn --nproc-per-node {NUM_GPUS} --fast-run --pretrained-model {pretrain_model_id} --no-autodetect-driptape-model --pipeline-id {pipeline_id} --dataset-id {dataset_id} --dl-config '{json.dumps(individual_dl_config)}'"
    )


@pytest.mark.dl_release_fast
def test_standard_jobs_geo_fine_tune_spawn(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)

    client = VeselkaClient()
    _, full_train_model_id = client.get_full_train_version(pipeline_id=pipeline_id)

    _, model_id = client.get_geo_fine_tune_version(
        pipeline_id=pipeline_id,
        container_version="none",
        fast_run=True,
        deploy=False,
        geohash=geo_fine_tune_geohashes[0],
        environment="development",
    )
    model_info = client.get_model_info(model_id)
    dataset_id = model_info["dataset_id"]

    call(
        f"{prefix} python -m deeplearning.scripts.deepweed.fine_tune_spawn --nproc-per-node {NUM_GPUS} --pretrained-model {full_train_model_id} --no-autodetect-driptape-model --pipeline-id {pipeline_id} --dataset-id {dataset_id} --fast-run --dl-config '{json.dumps(individual_fine_tune_dl_config)}'"
    )


@pytest.mark.dl_release_fast
def test_standard_jobs_evaluate(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)

    client = VeselkaClient()
    _, full_train_model_id = client.get_full_train_version(
        pipeline_id=pipeline_id, container_version="none", fast_run=True, environment="development"
    )
    model_info = client.get_model_info(full_train_model_id)
    dataset_id = model_info["dataset_id"]

    call(
        f"{prefix} torchrun --nproc-per-node 1 -m deeplearning.scripts.deepweed.evaluate --model-id {full_train_model_id} --dataset-id {dataset_id} --fast-run"
    )


@pytest.mark.dl_release_fast
def test_standard_jobs_selfsup() -> None:
    from deeplearning.self_supervised.test.end_to_end import TestEndToEnd

    TestEndToEnd().test_end_to_end(num_gpus=1)


if __name__ == "__main__":
    p = argparse.ArgumentParser()
    p.add_argument("--disable-cache-proxy", action="store_true")
    args = p.parse_args()
    test_standard_jobs_full(args.disable_cache_proxy)
