#include <pybind11/functional.h>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include <vector>

#include "lib/common/cpp/geo_data.h"
#include <torch/torch.h>
#include <weed_tracking/cpp/aimbot/aimbot_tracker.hpp>
#include <weed_tracking/cpp/exceptions.hpp>
#include <weed_tracking/cpp/ingest_clients/crop_line_detector.hpp>
#include <weed_tracking/cpp/ingest_clients/diagnostics.hpp>
#include <weed_tracking/cpp/tracking/tracker.h>
#include <weed_tracking/cpp/tracking/weed_tracker.h>
#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>
#include <weed_tracking_libs/arbiter/cpp/arbiter_trigger.hpp>

#include <torch/extension.h>
namespace py = pybind11;

namespace carbon {
namespace weed_tracking {

PYBIND11_MODULE(weed_tracking_python, m) {
  py::module::import("trajectory.pybind.trajectory_python");
  py::class_<WeedTrackerOutput, std::shared_ptr<WeedTrackerOutput>>(m, "WeedTrackerOutput")
      .def("get_timestamp_ms", &WeedTrackerOutput::get_timestamp_ms, py::call_guard<py::gil_scoped_release>())
      .def("get_trajectories", &WeedTrackerOutput::get_trajectories, py::call_guard<py::gil_scoped_release>())
      .def("get_newly_tracked_ids", &WeedTrackerOutput::get_newly_tracked_ids, py::call_guard<py::gil_scoped_release>())
      .def("get_deepweed_flow_offsets_xy", &WeedTrackerOutput::get_deepweed_flow_offsets_xy,
           py::call_guard<py::gil_scoped_release>())
      .def("get_number_missed_first_instances", &WeedTrackerOutput::get_number_missed_first_instances,
           py::call_guard<py::gil_scoped_release>())
      .def("get_vertical_offset_first_instances", &WeedTrackerOutput::get_vertical_offset_first_instances,
           py::call_guard<py::gil_scoped_release>());

  py::class_<WeedTracker, std::shared_ptr<WeedTracker>>(m, "WeedTracker")
      .def(py::init<std::string, uint32_t, std::shared_ptr<lib::common::geometric::GeometricCam>, int64_t, uint32_t,
                    uint32_t, float, uint32_t>(),
           py::arg("pcam_id"), py::arg("numeric_id"), py::arg("geo_cam"), py::arg("prune_grace_ms"), py::arg("width"),
           py::arg("height"), py::arg("max_pos_mm_y"), py::arg("max_number_of_weeds"),
           py::call_guard<py::gil_scoped_release>())
      .def("add_rotary_ticks", &WeedTracker::add_rotary_ticks, py::arg("timestamp_ms"), py::arg("mm_travelled"),
           py::call_guard<py::gil_scoped_release>())
      .def("add_deepweed", &WeedTracker::add_deepweed, py::arg("deepweed_output"), py::arg("recorded_tracked_items"), py::arg("model_undistorted_coordinates"), py::arg("best_candidate_match"), py::arg("all_candidate_matches"), py::arg("centroids_used_in_dedup"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_latest_weeds", &WeedTracker::get_latest_weeds, py::call_guard<py::gil_scoped_release>());

  py::module_::import("ingest.pybind.ingest_python"); // Need the IngestClient and IngestProducer objects
  py::module_::import("weed_tracking_libs.arbiter.pybind.arbiter_python");

  py::class_<AimbotWeedTracker, std::shared_ptr<AimbotWeedTracker>, ingest::IngestProducer, arbiter::ArbiterTrigger>(
      m, "AimbotWeedTracker")
      .def(py::init<std::string, uint32_t, uint32_t, uint32_t, bool,
                    std::shared_ptr<lib::common::geometric::GeometricCam>, float, bool, uint32_t, std::string>(),
           py::arg("pcam_id"), py::arg("numeric_id"), py::arg("width"), py::arg("height"), py::arg("log_ticks"),
           py::arg("geo_cam"), py::arg("max_pos_mm_y"), py::arg("score_images"), py::arg("max_number_of_weeds"),
           py::arg("cv_address"), py::call_guard<py::gil_scoped_release>())
      .def("get_pcam_id", &AimbotWeedTracker::get_pcam_id, py::call_guard<py::gil_scoped_release>())
      .def("get_latest_weeds", &AimbotWeedTracker::get_latest_weeds, py::call_guard<py::gil_scoped_release>())
      .def("get_weed_tracker", &AimbotWeedTracker::get_weed_tracker, py::call_guard<py::gil_scoped_release>())
      .def("get_last_centroid_with_perspective_before_timestamp_from_trajectory",
           &AimbotWeedTracker::get_last_centroid_with_perspective_before_timestamp_from_trajectory,
           py::arg("trajectory"), py::arg("timestamp_ms"), py::call_guard<py::gil_scoped_release>())
      .def("get_estimated_position_at_timestamp_from_trajectory",
           &AimbotWeedTracker::get_estimated_position_at_timestamp_from_trajectory, py::arg("trajectory"),
           py::arg("timestamp_ms"), py::call_guard<py::gil_scoped_release>())
      .def("get_last_item_from_trajectory", &AimbotWeedTracker::get_last_item_from_trajectory, py::arg("trajectory"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_geo_cam", &AimbotWeedTracker::get_geo_cam, py::call_guard<py::gil_scoped_release>())
      .def("get_pix_per_ticks", &AimbotWeedTracker::get_pix_per_ticks, py::call_guard<py::gil_scoped_release>())
      .def("get_averages_for_all_columns", &AimbotWeedTracker::get_averages_for_all_columns,
           py::arg("size_mm") = py::none(), py::call_guard<py::gil_scoped_release>())
      .def("aimbot_weed_tracker_state", &AimbotWeedTracker::aimbot_weed_tracker_state,
           py::call_guard<py::gil_scoped_release>());

  py::class_<AimbotTrackerOwner, std::unique_ptr<AimbotTrackerOwner, py::nodelete>>(m, "AimbotTrackerOwner")
      .def(py::init([]() { return std::unique_ptr<AimbotTrackerOwner, py::nodelete>(&AimbotTrackerOwner::get()); }))
      .def("set", &AimbotTrackerOwner::set, py::arg("trackers"), py::call_guard<py::gil_scoped_release>())
      .def("get_tracker", &AimbotTrackerOwner::get_tracker, py::arg("id"), py::call_guard<py::gil_scoped_release>());

  auto py_weed_tracking_exception = py::register_exception<weed_tracking_error>(m, "WeedTrackingException");
  py::register_exception<weed_tracking_timeout_error>(m, "WeedTrackingTimeoutException",
                                                      py_weed_tracking_exception.ptr());

  py::class_<CropLineDetector, std::shared_ptr<CropLineDetector>, arbiter::ArbiterClient<>>(m, "CropLineDetector")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def("start", &CropLineDetector::start, py::call_guard<py::gil_scoped_release>());

  py::class_<WeedingDiagnostics, std::shared_ptr<WeedingDiagnostics>, ingest::IngestClient<>>(m, "WeedingDiagnostics")
      .def_static("get", &WeedingDiagnostics::get, py::call_guard<py::gil_scoped_release>())
      .def("is_marked_for_images", &WeedingDiagnostics::is_marked_for_images, py::arg("tid"),
           py::call_guard<py::gil_scoped_release>())
      .def("attempting_to_save_predict", &WeedingDiagnostics::attempting_to_save_predict, py::arg("tid"),
           py::call_guard<py::gil_scoped_release>())
      .def("finished_attempting_to_save_predict", &WeedingDiagnostics::finished_attempting_to_save_predict,
           py::arg("tid"), py::call_guard<py::gil_scoped_release>());
}

} // namespace weed_tracking
} // namespace carbon