import asyncio
import sys
from typing import List

from config.client.cpp.config_client_python import get_computer_config_prefix, get_global_config_subscriber
from hardware_manager.python.client import HardwareManagerClient
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.device_configuration import get_devices_for_service, get_skip_list
from lib.common.devices.registry import DeviceRegistry
from lib.common.generation import is_reaper
from lib.common.logging import get_logger, init_log
from lib.common.metrics.acres_daily_metric import AcresDailyMetric
from lib.common.metrics.acres_job_metric import AcresJobMetric
from lib.common.metrics.banding_daily_metric import BandingDailyMetric
from lib.common.metrics.banding_job_metric import BandingJobMetric
from lib.common.metrics.banding_percentage_daily_metric import BandingPercentageDailyMetric
from lib.common.metrics.banding_percentage_job_metric import BandingPercentageJobMetric
from lib.common.metrics.conclusion_daily_metric import ConclusionDailyMetric
from lib.common.metrics.conclusion_job_metric import ConclusionJobMetric
from lib.common.metrics.daily_metric_aggregator import DailyMetricAggregator
from lib.common.metrics.distance_daily_metric import DistanceDailyMetric
from lib.common.metrics.distance_job_metric import DistanceJobMetric
from lib.common.metrics.job_metric_aggregator import JobMetricAggregator
from lib.common.metrics.laser_time_metric import LaserTimeMetric
from lib.common.metrics.power_checker import PowerChecker
from lib.common.metrics.uptime_daily_metric import UptimeDailyMetric
from lib.common.metrics.uptime_job_metric import UptimeJobMetric
from lib.common.redis_client import RedisClient
from lib.common.robot_definition.pybind.robot_definition_python import RobotDefinition
from lib.common.role import is_simulator
from lib.common.tasks.manager import get_event_loop_by_name
from metrics.pybind.metrics_python import SpatialBlockOwner
from metrics.python.grpc import GrpcServer
from metrics.python.spatial.spatial_banding_metric import SpatialBandingMetric
from metrics.python.spatial.spatial_block_generator import BuildSpatialBlockGenerator
from metrics.python.spatial.spatial_hw_metric import SpatialHWMetric
from metrics.python.spatial.spatial_job_metric import SpatialJobMetric
from metrics.python.spatial.spatial_metric import SpatialMetic
from metrics.python.spatial.spatial_velocity_metric import SpatialVelocityMetric
from metrics.python.spatial.spatial_weed_counter_writter import SpatialWeedCounterWritter
from metrics.python.spatial.spatial_width_metric import SpatialWidthMetric
from metrics.python.spatial.we_spatial_metric import WESpatialMetric

LOG = get_logger(__name__)
SERVICE_NAME = "metrics_aggregator"


async def main() -> None:
    if is_simulator():
        # let nofx board within simulator start up
        await asyncio.sleep(5)

    config_subscriber = get_global_config_subscriber()

    config_subscriber.add_config_tree("common", "common", "services/common.yaml")
    config_subscriber.add_config_tree(
        SERVICE_NAME, f"{get_computer_config_prefix()}/{SERVICE_NAME}", f"services/{SERVICE_NAME}.yaml"
    )
    config_subscriber.start()
    await asyncio.get_event_loop().run_in_executor(None, lambda: config_subscriber.wait_until_ready())
    LOG.info("Done starting the config subscriber")
    device_conf = config_subscriber.get_config_node(SERVICE_NAME, "device_overrides")
    common_conf = config_subscriber.get_config_node("common", "")

    registry = DeviceRegistry()
    await registry.boot_devices_from_iterable(
        get_devices_for_service(SERVICE_NAME, device_conf, common_conf), skip_list=get_skip_list(device_conf)
    )

    hmc = HardwareManagerClient()
    power_checker = PowerChecker()
    redis = await RedisClient.build()

    if is_reaper():
        # robot definition pulls from redis until it gets a valid definition, better to clearly block here
        # rather than the first time we try to use the robot definition
        LOG.info("Reaper, initializing robot definition")
        RobotDefinition.get()

    power_checker = PowerChecker()
    redis = await RedisClient.build()

    daily_aggregator = DailyMetricAggregator(redis=redis)
    daily_aggregator.add_metric(AcresDailyMetric(redis=redis, dtz=daily_aggregator.dtz))
    daily_aggregator.add_metric(BandingDailyMetric(redis=redis, dtz=daily_aggregator.dtz))
    daily_aggregator.add_metric(BandingPercentageDailyMetric(common_conf))
    daily_aggregator.add_metric(ConclusionDailyMetric(dtz=daily_aggregator.dtz))
    daily_aggregator.add_metric(UptimeDailyMetric(redis=redis, dtz=daily_aggregator.dtz, checker=power_checker))
    daily_aggregator.add_metric(DistanceDailyMetric(redis=redis, dtz=daily_aggregator.dtz))
    ltm = LaserTimeMetric(redis=redis, checker=power_checker)

    job_aggregator = JobMetricAggregator(redis)
    job_aggregator.add_metric(AcresJobMetric(redis))
    job_aggregator.add_metric(BandingJobMetric(redis))
    job_aggregator.add_metric(ConclusionJobMetric())
    job_aggregator.add_metric(UptimeJobMetric(redis, power_checker))
    job_aggregator.add_metric(DistanceJobMetric(redis))
    job_aggregator.add_metric(BandingPercentageJobMetric(common_conf))

    owner = SpatialBlockOwner()
    binary_redis = await RedisClient.build(False)
    width_metric = SpatialWidthMetric(owner)
    spatial_metrics: List[SpatialMetic] = [
        SpatialWeedCounterWritter(binary_redis),
        WESpatialMetric(owner),
        SpatialBandingMetric(owner),
        width_metric,
        SpatialVelocityMetric(owner, binary_redis),
        SpatialJobMetric(binary_redis),
        SpatialHWMetric(owner),
    ]
    spatial_generator = await BuildSpatialBlockGenerator(
        hmc, binary_redis, owner, spatial_metrics, width_metric.get_width
    )

    server = GrpcServer(
        61010,
        daily_metrics=daily_aggregator,
        job_metrics=job_aggregator,
        laser_time_metric=ltm,
        spatial_block_generator=spatial_generator,
        tasks=[power_checker],
    )
    LOG.info("server created")
    bot_stop_handler.add_callback(server.shutdown)
    await server.serve()


if __name__ == "__main__":
    init_log(level="INFO")
    asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name())
    bot_stop_handler.ready_for_termination_event.wait()
    sys.exit(bot_stop_handler.error_code)
