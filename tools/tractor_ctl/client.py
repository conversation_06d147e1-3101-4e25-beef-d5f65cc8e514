import asyncio
import json
from argparse import Namespace
from typing import List, Optional, cast

import lib.common.geo.geojson as geojson
from lib.common.logging import get_logger
from lib.common.messaging.message import ErrorMsgException
from lib.rtc.messaging.message import STATUS_MSG, Message
from lib.rtc.webrtc.controllable_client import Channel, ControllableClient
from tractor_ctl import messages as msg
from tractor_ctl.autonomy.autonomy import AutonomyMode, SteeringAssistAlgo

LOG = get_logger(__name__)


class TractorCtlClient(ControllableClient):
    def __init__(self) -> None:
        super().__init__(Channel.TRACTOR_CTL)

        self._state_lock = asyncio.Lock()
        self._total_state = msg.ControlState()

    @property
    def control_state(self) -> msg.ControlState:
        return self._total_state

    async def _server_initiated_msg(self, m: Message) -> None:
        if m.msg_type == msg.MessageType.CONTROL_STATE and m.content is not None:
            state = msg.ControlState.from_dict(m.content)
            async with self._state_lock:
                self._total_state.update(state)
        elif m.msg_type == STATUS_MSG:
            pass
        else:
            LOG.warning(f"Unknown msg {m}")

    async def sr_msg(self, in_msg: Message, timeout: float = 1.0) -> Message:
        m = await super().sr_msg(in_msg, timeout)
        resp_type = msg.REQ_RESP_MAP[in_msg.msg_type]
        assert m.msg_type == resp_type
        return m

    async def sr(self, in_msg: Message, timeout: float = 1.0) -> msg.JSONWizard:
        m = await self.sr_msg(in_msg, timeout)
        if m.error is not None:
            raise ErrorMsgException(m.error)
        assert m.content is not None
        return msg.RESP_MAP[m.msg_type].from_dict(m.content)

    async def beat(self, request_state: bool = False) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.HB_REQ, content=msg.HBMsg(request_state=request_state)),)

    async def set_sw_steering_ctl(self, enabled: bool = True) -> None:
        await self.sr(
            Message.build(msg_type=msg.MessageType.SW_CONTROLLED_STEERING_REQ, content=msg.ToggleMsg(enabled))
        )

    async def set_current_pos_as_center(self) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SET_STEERING_ZERO_REQ, content={}))

    async def set_steering(self, pos: float) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SET_STEERING_REQ, content=msg.SetFloatMsg(pos)))

    async def set_speed(self, speed: float) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SET_SPEED_REQ, content=msg.SetSpeedMsg(speed)))

    async def set_gear(self, gear: int) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SET_GEAR_REQ, content=msg.SetIntMsg(gear)))

    async def get_fault_details(self) -> msg.FaultDetails:
        return cast(
            msg.FaultDetails, await self.sr(Message.build(msg_type=msg.MessageType.STEERING_FAULT_REQ, content={}))
        )

    async def set_steering_fault(self) -> None:
        await self.sr(
            Message.build(msg_type=msg.MessageType.SET_STEERING_FAULT_REQ, content=msg.FaultDetails(overcurrent=True))
        )

    async def clear_fault(self) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.CLEAR_STEERING_FAULT_REQ, content={}))

    async def get_current_path(self) -> msg.CurrPathResp:
        return cast(
            msg.CurrPathResp, await self.sr(Message.build(msg_type=msg.MessageType.GET_CURRENT_PATH_REQ, content={}))
        )

    async def get_current_path_input(self) -> msg.CurrPathPlanInputResp:
        return cast(
            msg.CurrPathPlanInputResp,
            await self.sr(Message.build(msg_type=msg.MessageType.GET_PATH_PLANNING_INPUT_REQ, content={})),
        )

    async def set_current_path(self, path_input: str, path: str) -> None:
        await self.sr(
            Message.build(
                msg_type=msg.MessageType.SET_CURRENT_PATH_REQ,
                content=msg.SetPathReq(path_feature=path, path_planning_input=path_input),
            )
        )

    async def get_current_heading(self) -> msg.CurrHeadingResp:
        return cast(
            msg.CurrHeadingResp,
            await self.sr(Message.build(msg_type=msg.MessageType.GET_CURRENT_HEADING_REQ, content={})),
        )

    async def set_current_heading(self, heading_deg: Optional[float] = None) -> None:
        await self.sr(
            Message.build(
                msg_type=msg.MessageType.SET_CURRENT_HEADING_REQ, content=msg.SetHeadingReq(heading_deg=heading_deg),
            )
        )

    async def set_path_from_gps_log(self, gps_log: str) -> None:
        line_str = geojson.LineString([])
        with open(gps_log, "r") as fin:
            for line in fin:
                data = json.loads(line)
                line_str.coordinates.append([data["lng"], data["lat"], data["alt"]])
        feat = geojson.Feature(geometry=line_str)
        return await self.set_current_path("", feat.to_json())

    async def set_autonomy(self, mode: AutonomyMode, steering_algo: SteeringAssistAlgo) -> None:
        await self.sr(
            Message.build(
                msg_type=msg.MessageType.SET_AUTONOMY_REQ,
                content=msg.AutonomyMsg(mode=mode, steering_assist_algo=steering_algo),
            )
        )

    async def set_auto_steer_range(self, min_deg: float, max_deg: float) -> None:
        await self.sr(
            Message.build(
                msg_type=msg.MessageType.SET_AUTO_STEER_RANGE_REQ, content=msg.AutoSteerRange(min=min_deg, max=max_deg)
            )
        )

    async def send_remote_assist(self, details: str) -> None:
        await self.sr(
            Message.build(
                msg_type=msg.MessageType.SEND_REMOTE_ASSIST_REQUIRED_REQ, content=msg.RemoteAssistanceReq(msg=details)
            )
        )

    async def set_stop_state(self, stop_state: int) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SET_STOP_REQ, content=msg.SetIntMsg(value=stop_state)))

    async def set_hh_state(self, hh_state: msg.HHState) -> None:
        await self.sr(
            Message.build(msg_type=msg.MessageType.SET_HH_STATE_REQ, content=msg.SetIntMsg(value=hh_state.value))
        )

    async def set_control_board_enable(self, enabled: bool, make_safe: bool = False) -> None:
        await self.sr(
            Message.build(
                msg_type=msg.MessageType.SET_CONTROL_BOARD_ENABLE_REQ,
                content=msg.ControlBoardEnableMsg(value=enabled, make_safe=make_safe),
            )
        )

    async def set_brakes(self, force_left: int, force_right: int) -> None:
        await self.sr(
            Message.build(
                msg_type=msg.MessageType.SET_BRAKE_REQ,
                content=msg.SetBrakeReq(force_left=force_left, force_right=force_right),
            )
        )

    async def set_safety_bypass(self, bypass: bool) -> None:
        await self.sr(
            Message.build(msg_type=msg.MessageType.SET_SAFETY_BYPASS_REQ, content=msg.SetBoolMsg(value=bypass))
        )

    async def set_hitch(self, lift: bool, force: float) -> None:
        await self.sr(
            Message.build(msg_type=msg.MessageType.SET_HITCH_REQ, content=msg.SetHitchMsg(lift=lift, force=force))
        )

    async def set_lights(self, lights: int) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SET_LIGHTS_REQ, content=msg.SetIntMsg(value=lights)))

    async def get_safety_state(self) -> msg.SafetySensorStateResp:
        return cast(
            msg.SafetySensorStateResp,
            await self.sr(Message.build(msg_type=msg.MessageType.GET_SAFETY_SENSOR_STATE_REQ, content={})),
        )

    async def get_remote_lockout_state(self) -> bool:
        return cast(
            msg.SetBoolMsg, await self.sr(Message.build(msg_type=msg.MessageType.GET_RMT_LOCKOUT_STATE_REQ, content={}))
        ).value

    async def reload_wheel_pos(self) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.RELOAD_WHEEL_POS_REQ, content={}))

    async def save_sequence(self, seq: msg.SequenceDef) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SAVE_SEQUENCE_REQ, content=seq))

    async def run_sequence(self, seq: str) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.RUN_SEQUENCE_REQ, content=msg.SequenceId(seq=seq)))

    async def list_sequences(self) -> List[str]:
        return cast(
            msg.ListSequencesMsg, await self.sr(Message.build(msg_type=msg.MessageType.LIST_SEQUENCES_REQ, content={}))
        ).sequences

    async def get_sequence(self, seq: str) -> msg.SequenceDef:
        return cast(
            msg.SequenceDef,
            await self.sr(Message.build(msg_type=msg.MessageType.GET_SEQUENCE_REQ, content=msg.SequenceId(seq=seq))),
        )

    async def set_engine_rpm(self, rpms: int) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SET_ENGINE_RPM_REQ, content=msg.SetIntMsg(value=rpms)))

    async def enable_atim(self, enabled: bool = True) -> None:
        await self.sr(Message.build(msg_type=msg.MessageType.SET_ATIM_REQ, content=msg.ToggleMsg(enabled=enabled)))

    async def shutdown_tractor(self) -> None:
        await self.send(Message.build(msg_type=msg.MessageType.TRACTOR_SHUTDOWN_REQ, content={}))

    async def set_boundary_bypass(self, enabled: bool = True) -> None:
        await self.sr(
            Message.build(msg_type=msg.MessageType.SET_BOUNDARY_BYPASS_REQ, content=msg.ToggleMsg(enabled=enabled))
        )


imports = Namespace(
    **{"msg": msg, "geojson": geojson, "AutonomyMode": AutonomyMode, "SteeringAssistAlgo": SteeringAssistAlgo}
)
