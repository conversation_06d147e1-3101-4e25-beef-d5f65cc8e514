package pulczar

import (
	"fmt"
	"log"
	"net"

	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/arc_detector"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/dawg"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/epos"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/gimbal"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/laser"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/lens"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/pulczar"
	nanopb "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/pulczar_board"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/scanner_config"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/servo"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type PulczarBoardSim struct {
	scannerId       int
	laserOn         bool
	laserType       laser.LaserType
	override        uint32
	dawgTimeoutMs   uint32
	dtTargetConfig  *scanner_config.Delta_Target_Config
	colorConfig     *scanner_config.Color_Config
	scannerBcConfig *scanner_config.Scanner_Barcode_Str_Config
	hwRevision      uint32
	panConfig       *servo.Config
	tiltConfig      *servo.Config
	panEposPid      *epos.EPOS_PID
	tiltEposPid     *epos.EPOS_PID
	panHomeParams   *epos.Home_Params
	tiltHomeParams  *epos.Home_Params
	lensValue       uint32
	armed           bool
	arcConfig       *arc_detector.Config
}

func NewPulczarBoardSim(scannerId int) *PulczarBoardSim {
	return &PulczarBoardSim{scannerId: scannerId}
}

func (g *PulczarBoardSim) Start() {
	fmt.Printf("Pulczar board simulation starting...\n")

	port := 64548 + g.scannerId
	pc, err := net.ListenPacket("udp", fmt.Sprintf(":%v", port))
	if err != nil {
		log.Fatal(err)
	}
	defer pc.Close()

	for {
		buf := make([]byte, 1024)
		n, addr, err := pc.ReadFrom(buf)
		if err != nil {
			continue
		}
		go g.serve(pc, addr, buf[:n])
	}
}

func (s *PulczarBoardSim) handleDawg(req *nanopb.Request, reply *nanopb.Reply) {
	switch req.GetPulczar().GetDawg().GetRequest().(type) {
	case *dawg.Request_Arm:
		s.armed = req.GetPulczar().GetDawg().GetArm().GetArmed()
		reply.Reply = dawgAck()
	case *dawg.Request_Config:
		s.dawgTimeoutMs = req.GetPulczar().GetDawg().GetConfig().TimeoutMs
		reply.Reply = dawgAck()
	case *dawg.Request_Pet:
		reply.Reply = dawgAck()
	default:
		logrus.Debugf("Pulczar(%v) request not handled: %v", s.scannerId, req)
	}
}

func (s *PulczarBoardSim) handleLaser(req *nanopb.Request, reply *nanopb.Reply) {
	switch req.GetPulczar().GetLaser().GetRequest().(type) {
	case *laser.Request_Laser:
		s.laserOn = req.GetPulczar().GetLaser().GetLaser().On
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Laser{
					Laser: &laser.Reply{
						Reply: &laser.Reply_LaserReply{
							LaserReply: &laser.Laser_Reply{
								RawTherm1ReadingMv: 2500,
								RawTherm2ReadingMv: 2500,
								On:                 s.laserOn,
								LpsuState:          true,
								Fireable:           true,
							},
						},
					},
				},
			},
		}
	case *laser.Request_Intensity:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Laser{
					Laser: &laser.Reply{
						Reply: &laser.Reply_Ack{
							Ack: &ack.Ack{},
						},
					},
				},
			},
		}
	case *laser.Request_SetType:
		s.laserType = req.GetPulczar().GetLaser().GetSetType().Type
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Laser{
					Laser: &laser.Reply{
						Reply: &laser.Reply_Ack{
							Ack: &ack.Ack{},
						},
					},
				},
			},
		}
	default:
		logrus.Debugf("Pulczar(%v) request not handled: %v", s.scannerId, req)
	}
}

func (s *PulczarBoardSim) handleClear(req *nanopb.Request, reply *nanopb.Reply) {
	reply.Reply = pulczarAck()
}

func (s *PulczarBoardSim) handleOverride(req *nanopb.Request, reply *nanopb.Reply) {
	s.override = req.GetPulczar().GetOverride().Override
	reply.Reply = pulczarAck()
}

func (s *PulczarBoardSim) handleConf(req *nanopb.Request, reply *nanopb.Reply) {
	confSetAckReply := &nanopb.Reply_Pulczar{
		Pulczar: &pulczar.Reply{
			Reply: &pulczar.Reply_Conf{
				Conf: &scanner_config.Reply{
					Reply: &scanner_config.Reply_Ack{
						Ack: &ack.Ack{},
					},
				},
			},
		},
	}

	switch req.GetPulczar().GetConf().GetRequest().(type) {
	case *scanner_config.Request_GetSn:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Conf{
					Conf: &scanner_config.Reply{
						Reply: &scanner_config.Reply_Sn{
							Sn: &scanner_config.Get_Camera_Serial_Number_Config_Reply{
								Config: &scanner_config.Camera_Serial_Number_Config{
									SerialNumber: []byte(fmt.Sprintf("%v", s.scannerId)),
								},
							},
						},
					},
				},
			},
		}
	case *scanner_config.Request_SetDt:
		s.dtTargetConfig = req.GetPulczar().GetConf().GetSetDt().Config
		reply.Reply = confSetAckReply
	case *scanner_config.Request_GetDt:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Conf{
					Conf: &scanner_config.Reply{
						Reply: &scanner_config.Reply_Dt{
							Dt: &scanner_config.Get_Delta_Target_Config_Reply{
								Config: s.dtTargetConfig,
							},
						},
					},
				},
			},
		}
	case *scanner_config.Request_SetColor:
		s.colorConfig = req.GetPulczar().GetConf().GetSetColor().Config
		reply.Reply = confSetAckReply
	case *scanner_config.Request_GetColor:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Conf{
					Conf: &scanner_config.Reply{
						Reply: &scanner_config.Reply_Color{
							Color: &scanner_config.Get_Color_Config_Reply{
								Config: s.colorConfig,
							},
						},
					},
				},
			},
		}
	case *scanner_config.Request_SetHw:
		s.hwRevision = req.GetPulczar().GetConf().GetSetHw().Revision
		reply.Reply = confSetAckReply
	case *scanner_config.Request_GetHw:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Conf{
					Conf: &scanner_config.Reply{
						Reply: &scanner_config.Reply_Hw{
							Hw: &scanner_config.Get_HW_Revision_Reply{
								Revision: s.hwRevision,
							},
						},
					},
				},
			},
		}
	case *scanner_config.Request_SetBc:
		s.scannerBcConfig = req.GetPulczar().GetConf().GetSetBc().Config
		reply.Reply = confSetAckReply
	case *scanner_config.Request_GetBc:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Conf{
					Conf: &scanner_config.Reply{
						Reply: &scanner_config.Reply_Bc{
							Bc: &scanner_config.Get_Scanner_Barcode_Str_Reply{
								Config: s.scannerBcConfig,
							},
						},
					},
				},
			},
		}
	default:
		logrus.Debugf("Pulczar(%v) request not handled: %v", s.scannerId, req)
	}
}

func (s *PulczarBoardSim) handleGimbalEposRequestPan(req *nanopb.Request, reply *nanopb.Reply) {
	switch req.GetPulczar().GetGimbal().GetServos().Pan.GetEpos().GetRequest().(type) {
	case *epos.Request_PosVel:
		reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Pan = &servo.Reply{Reply: &servo.Reply_Epos{Epos: &epos.Reply{Reply: &epos.Reply_PosVel{PosVel: &epos.Pos_Vel_Reply{Position: 0, Velocity: 0}}}}}
	default:
		s.panEposPid = req.GetPulczar().GetGimbal().GetServos().Pan.GetEpos().GetPidV2().GetEpos()
		reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Pan = &servo.Reply{Reply: &servo.Reply_Epos{Epos: &epos.Reply{Reply: &epos.Reply_Ack{}}}}
	}
}

func (s *PulczarBoardSim) handleGimbalEposRequestTilt(req *nanopb.Request, reply *nanopb.Reply) {
	switch req.GetPulczar().GetGimbal().GetServos().Tilt.GetEpos().GetRequest().(type) {
	case *epos.Request_PosVel:
		reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Tilt = &servo.Reply{Reply: &servo.Reply_Epos{Epos: &epos.Reply{Reply: &epos.Reply_PosVel{PosVel: &epos.Pos_Vel_Reply{Position: 0, Velocity: 0}}}}}
	default:
		s.panEposPid = req.GetPulczar().GetGimbal().GetServos().Tilt.GetEpos().GetPidV2().GetEpos()
		reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Tilt = &servo.Reply{Reply: &servo.Reply_Epos{Epos: &epos.Reply{Reply: &epos.Reply_Ack{}}}}
	}
}

func (s *PulczarBoardSim) handleGimbal(req *nanopb.Request, reply *nanopb.Reply) {
	if req.GetPulczar().GetGimbal() != nil {
		if req.GetPulczar().GetGimbal().GetServos() != nil {
			reply.Reply = &nanopb.Reply_Pulczar{
				Pulczar: &pulczar.Reply{
					Reply: &pulczar.Reply_Gimbal{
						Gimbal: &gimbal.Reply{
							Reply: &gimbal.Reply_Servos{
								Servos: &gimbal.Servos_Reply{},
							},
						},
					},
				},
			}

			switch req.GetPulczar().GetGimbal().GetServos().Pan.GetRequest().(type) {
			case *servo.Request_Config:
				s.panConfig = req.GetPulczar().GetGimbal().GetServos().Pan.GetConfig().Config
				reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Pan = &servo.Reply{Reply: &servo.Reply_Ack{}}
			case *servo.Request_Limit:
				max := int32(13000)
				if environment.IsReaper() {
					max = int32(10000)
				}
				reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Pan = &servo.Reply{Reply: &servo.Reply_Limit{Limit: &servo.Limits_Reply{Min: 0, Max: max}}}
			case *servo.Request_GoToTimestamp:
				gt := req.GetPulczar().GetGimbal().GetServos().Pan.GetGoToTimestamp()
				reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Pan = &servo.Reply{Reply: &servo.Reply_GoToTimestamp{GoToTimestamp: &servo.Go_To_Timestamp_Reply{PrePosition: gt.Position - 1, PreTimestamp: gt.Timestamp, PostPosition: gt.Position, PostTimestamp: gt.Timestamp}}}
			case *servo.Request_Follow:
				reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Pan = &servo.Reply{Reply: &servo.Reply_Pos{Pos: &servo.Position_Reply{Position: 10}}}
			case *servo.Request_Epos:
				s.handleGimbalEposRequestPan(req, reply)
			default:
				logrus.Errorf("Pulczar(%v) request not handled: %v", s.scannerId, req)
			}

			switch req.GetPulczar().GetGimbal().GetServos().Tilt.GetRequest().(type) {
			case *servo.Request_Config:
				s.tiltConfig = req.GetPulczar().GetGimbal().GetServos().Tilt.GetConfig().Config
				reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Tilt = &servo.Reply{Reply: &servo.Reply_Ack{}}
			case *servo.Request_Limit:
				max := int32(12000)
				if environment.IsReaper() {
					max = int32(10000)
				}
				reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Tilt = &servo.Reply{Reply: &servo.Reply_Limit{Limit: &servo.Limits_Reply{Min: 0, Max: max}}}
			case *servo.Request_GoToTimestamp:
				gt := req.GetPulczar().GetGimbal().GetServos().Tilt.GetGoToTimestamp()
				reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Tilt = &servo.Reply{Reply: &servo.Reply_GoToTimestamp{GoToTimestamp: &servo.Go_To_Timestamp_Reply{PrePosition: gt.Position - 1, PreTimestamp: gt.Timestamp, PostPosition: gt.Position, PostTimestamp: gt.Timestamp}}}
			case *servo.Request_Follow:
				reply.Reply.(*nanopb.Reply_Pulczar).Pulczar.Reply.(*pulczar.Reply_Gimbal).Gimbal.Reply.(*gimbal.Reply_Servos).Servos.Tilt = &servo.Reply{Reply: &servo.Reply_Pos{Pos: &servo.Position_Reply{Position: 10}}}
			case *servo.Request_Epos:
				s.handleGimbalEposRequestTilt(req, reply)
			}
		}
		if req.GetPulczar().GetGimbal().GetBoot() != nil {
			s.panHomeParams = req.GetPulczar().GetGimbal().GetBoot().PanParams
			s.tiltHomeParams = req.GetPulczar().GetGimbal().GetBoot().TiltParams
			reply.Reply = &nanopb.Reply_Pulczar{
				Pulczar: &pulczar.Reply{
					Reply: &pulczar.Reply_Gimbal{
						Gimbal: &gimbal.Reply{
							Reply: &gimbal.Reply_Ack{},
						},
					},
				},
			}
		}
	}
	if req.GetPulczar().GetGimbal().GetBoot() != nil {
		s.panHomeParams = req.GetPulczar().GetGimbal().GetBoot().PanParams
		s.tiltHomeParams = req.GetPulczar().GetGimbal().GetBoot().TiltParams
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Gimbal{
					Gimbal: &gimbal.Reply{
						Reply: &gimbal.Reply_Ack{},
					},
				},
			},
		}
	} else {
		logrus.Debugf("Pulczar(%v) request not handled: %v", s.scannerId, req)
	}
}

func (s *PulczarBoardSim) handleLens(req *nanopb.Request, reply *nanopb.Reply) {
	switch req.GetPulczar().GetLens().GetRequest().(type) {
	case *lens.Request_Set:
		s.lensValue = req.GetPulczar().GetLens().GetSet().Value
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Lens{
					Lens: &lens.Reply{
						Reply: &lens.Reply_Ack{},
					},
				},
			},
		}
	case *lens.Request_Get:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Lens{
					Lens: &lens.Reply{
						Reply: &lens.Reply_Get{
							Get: &lens.Get_Reply{
								Value: s.lensValue,
							},
						},
					},
				},
			},
		}
	default:
		logrus.Debugf("Pulczar(%v) request not handled: %v", s.scannerId, req)
	}
}

func (s *PulczarBoardSim) handleStatus(req *nanopb.Request, reply *nanopb.Reply) {
	status := 0
	if s.armed {
		status = 1
	}
	reply.Reply = &nanopb.Reply_Pulczar{
		Pulczar: &pulczar.Reply{
			Reply: &pulczar.Reply_Status{
				Status: &pulczar.Status_Reply{
					Status: uint32(status),
					LaserStatus: &laser.Laser_Status_Reply{
						LpsuState:   true,
						LpsuCurrent: 1,
						Power:       2600.5,
					},
				},
			},
		},
	}
}

func (s *PulczarBoardSim) handleArc(req *nanopb.Request, reply *nanopb.Reply) {
	switch req.GetPulczar().GetArc().GetRequest().(type) {
	case *arc_detector.Request_SetConf:
		s.arcConfig = req.GetPulczar().GetArc().GetSetConf().NewConfig
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Arc{
					Arc: &arc_detector.Reply{
						Reply: &arc_detector.Reply_Conf{
							Conf: &arc_detector.Config_Reply{
								Conf: s.arcConfig,
							},
						},
					},
				},
			},
		}
	case *arc_detector.Request_GetConf:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Arc{
					Arc: &arc_detector.Reply{
						Reply: &arc_detector.Reply_Conf{
							Conf: &arc_detector.Config_Reply{
								Conf: s.arcConfig,
							},
						},
					},
				},
			},
		}
	case *arc_detector.Request_Status:
		reply.Reply = &nanopb.Reply_Pulczar{
			Pulczar: &pulczar.Reply{
				Reply: &pulczar.Reply_Arc{
					Arc: &arc_detector.Reply{
						Reply: &arc_detector.Reply_Status{
							Status: &arc_detector.Status_Reply{
								Enabled:    s.arcConfig.GetEnabled(),
								Alarm:      false,
								MinCurrent: s.arcConfig.GetLowerLimit() + 1,
								MaxCurrent: s.arcConfig.GetUpperLimit() - 1,
							},
						},
					},
				},
			},
		}
	case *arc_detector.Request_Reset_:
		logrus.Warn("Arc reset not implemented")
	default:
		logrus.Debugf("Pulczar(%v) request not handled: %v", s.scannerId, req)
	}
}

func (s *PulczarBoardSim) serve(pc net.PacketConn, addr net.Addr, buf []byte) {
	req := &nanopb.Request{}
	proto.Unmarshal(buf, req)

	reqs, _ := protojson.Marshal(req)
	logrus.Debugf("PulczarRequest(%v)=%v", s.scannerId, string(reqs))

	reply := &nanopb.Reply{
		Header: &request.RequestHeader{
			RequestId: req.GetHeader().RequestId,
		},
	}

	if req.GetPing() != nil {
		reply.Reply = &nanopb.Reply_Pong{
			Pong: &diagnostic.Pong{
				X: req.GetPing().X,
			},
		}
	} else {
		logrus.Debugf("Pulczar(%v) request not handled: %v", s.scannerId, reqs)
	}

	switch req.GetPulczar().GetRequest().(type) {
	case *pulczar.Request_Dawg:
		s.handleDawg(req, reply)
	case *pulczar.Request_Laser:
		s.handleLaser(req, reply)
	case *pulczar.Request_Clear:
		s.handleClear(req, reply)
	case *pulczar.Request_Override:
		s.handleOverride(req, reply)
	case *pulczar.Request_Conf:
		s.handleConf(req, reply)
	case *pulczar.Request_Gimbal:
		s.handleGimbal(req, reply)
	case *pulczar.Request_Lens:
		s.handleLens(req, reply)
	case *pulczar.Request_Status:
		s.handleStatus(req, reply)
	case *pulczar.Request_Arc:
		s.handleArc(req, reply)
	default:
		logrus.Debugf("Pulczar(%v) request not handled: %v", s.scannerId, req)
	}

	resp, _ := protojson.Marshal(reply)
	logrus.Debugf("PulczarReply(%v)=%v", s.scannerId, string(resp))
	s.write(reply, pc, addr)
}

func (s *PulczarBoardSim) write(reply proto.Message, pc net.PacketConn, addr net.Addr) {
	msg, err := proto.Marshal(reply)
	if err != nil {
		logrus.Error(err)
	} else {
		pc.WriteTo(msg, addr)
	}
}

func dawgAck() *nanopb.Reply_Pulczar {
	return &nanopb.Reply_Pulczar{
		Pulczar: &pulczar.Reply{
			Reply: &pulczar.Reply_Dawg{
				Dawg: &dawg.Reply{
					Reply: &dawg.Reply_Ack{
						Ack: &ack.Ack{},
					},
				},
			},
		},
	}
}

func pulczarAck() *nanopb.Reply_Pulczar {
	return &nanopb.Reply_Pulczar{
		Pulczar: &pulczar.Reply{
			Reply: &pulczar.Reply_Ack{
				Ack: &ack.Ack{},
			},
		},
	}
}
