import argparse
import json
import logging
import os
from collections import defaultdict
from pathlib import Path
from typing import Any, Dict, List, Tuple

import boto3
from scipy.spatial import cKDTree
from tqdm import tqdm

from deeplearning.comparison.constants import SIZE_BUCKETS
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.comparison.constants import COMPARISON_PREFIX, S3_CLOUD_BUCKET
from deeplearning.scripts.utils.utils import (
    add_job_creator_arguments,
    get_dataset,
    get_dataset_v2,
    update_dataset_version,
)
from lib.common.veselka.client import VeselkaClient

logging.basicConfig(format="%(asctime)s %(levelname)s:%(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S")
LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


def point_query_f(p: Dict[str, Any]) -> <PERSON><PERSON>[int, int]:
    return (p["x"], p["y"])


def in_bounds(x: int, y: int, height: int, width: int, border_to_extract_px: int) -> bool:
    return (
        x > border_to_extract_px
        and x < (width - border_to_extract_px)
        and y > border_to_extract_px
        and y < (height - border_to_extract_px)
    )


def preprocess_dataset(
    file_suffix: str, dataset_json_dir: str
) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
    images_to_annotations = defaultdict(list)
    images_dict = {}
    category_dict = {}

    for file in ["train", "validation", "test"]:
        dataset_json_path = os.path.join(dataset_json_dir, f"{file}{file_suffix}")
        if not os.path.exists(dataset_json_path):
            LOG.warning(f"File {dataset_json_path} does not exist")
            continue
        if file_suffix == ".json":  # dataset v1
            with open(dataset_json_path, "r") as f:
                data = json.load(f)

            for annotation in data["annotations"]:
                if annotation["annotation_type"] == "point":
                    images_to_annotations[annotation["image_id"]].append(annotation)

            images_dict.update({image["id"]: image for image in data["images"]})
            category_dict.update({cat["id"]: cat["name"] for cat in data["categories"]})

        elif file_suffix == ".jsonl":  # dataset v2
            with open(dataset_json_path, "r") as f:
                for line in f:
                    data = json.loads(line)
                    images_to_annotations[data["image_id"]] = data["points"]
                    images_dict[data["image_id"]] = {k: v for k, v in data.items() if k != "points"}

            get_point_categories_list = VeselkaClient().get_point_categories()
            for point_category in get_point_categories_list:
                category_dict[point_category["id"]] = point_category[
                    "name"
                ].lower()  # Convert it to lowercase to ensure consistency with dataset v1.

    return images_to_annotations, images_dict, category_dict


def load_annotations_images_categories(
    dataset_json_dir: str, buckets: List[int], neighbors_to_remove_px: float = 100, file_suffix: str = ".jsonl"
) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
    size_category_options: Dict[str, Any] = {str(size): defaultdict(list) for size in buckets[:-1]}
    category_size_options: Dict[str, Any] = defaultdict(dict)

    images_to_annotations, images_dict, category_dict = preprocess_dataset(
        file_suffix=file_suffix, dataset_json_dir=dataset_json_dir
    )
    annotations_to_consider = []

    for image_id, anns in tqdm(
        images_to_annotations.items(), desc="Perform annotations_to_consider"
    ):  # images_to_annotations with key: image_id, values: points
        point_queries = [point_query_f(a) for a in anns]
        if len(point_queries) == 0:
            continue

        tree = cKDTree(point_queries)
        for point in anns:
            if point.get("confidence", 0) != 2:  # We can skip if the confidence of the point != 2.
                continue

            near_points = tree.query_ball_point(point_query_f(point), neighbors_to_remove_px, return_length=True)
            if (
                near_points == 1
            ):  # One item will be the item in question, so we want to know if that's the only close point
                if (
                    file_suffix == ".jsonl"
                ):  # Add some feature for dataset v2. We will use this information when we sample the labels.
                    point["image_id"] = image_id
                    point["label_id"] = images_dict[image_id]["label_id"]
                    point["label"] = category_dict[point["point_category_id"]]
                annotations_to_consider.append(point)

    for annotation in tqdm(annotations_to_consider, desc="Create category_size_options"):
        for i in range(len(buckets) - 1):
            min_size = (200 / 25.4) * buckets[i]
            max_size = (200 / 25.4) * buckets[i + 1]

            if (
                annotation["radius"] >= min_size
                and annotation["radius"] < max_size
                and in_bounds(
                    x=annotation["x"],
                    y=annotation["y"],
                    height=images_dict[annotation["image_id"]]["height"],
                    width=images_dict[annotation["image_id"]]["width"],
                    border_to_extract_px=annotation["radius"],
                )
            ):
                if file_suffix == ".json":
                    category = category_dict[annotation["category_id"]]
                else:
                    category = category_dict[annotation["point_category_id"]]

                if int(annotation["radius"]) not in category_size_options[category]:
                    category_size_options[category][int(annotation["radius"])] = []
                category_size_options[category][int(annotation["radius"])].append(annotation)

    return size_category_options, images_dict, category_size_options


def extract_size_category_options(
    dataset_json_dir: str, size_category_filepath: str, buckets: List[int], env: str, file_suffix: str
) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
    size_category_options, images_dict, category_size_options = load_annotations_images_categories(
        dataset_json_dir=dataset_json_dir, buckets=buckets, file_suffix=file_suffix
    )
    combo = {
        "size_category_options": size_category_options,
        "images_dict": images_dict,
        "category_size_options": category_size_options,
    }

    dir_path = os.path.dirname(size_category_filepath)
    os.makedirs(dir_path, exist_ok=True)
    with open(size_category_filepath, "w") as f:
        json.dump(combo, f)

    dataset_id_filename = Path(size_category_filepath).name

    prefix = f"{COMPARISON_PREFIX}/{env}"
    with open(size_category_filepath, "rb") as f:
        s3_path = os.path.join(prefix, f"size_category_options/{dataset_id_filename}")
        LOG.info(f"Uploading {size_category_filepath} to s3://{S3_CLOUD_BUCKET}/{s3_path}")
        boto3.client("s3").upload_fileobj(f, S3_CLOUD_BUCKET, s3_path)

    return size_category_options, images_dict, category_size_options


def get_dataset_info(args_dataset_id: str) -> Tuple[str, str, str]:
    dataset_version = update_dataset_version(dataset_id=args_dataset_id, parent_dataset_id=None, dataset_version=2)
    if dataset_version == 1:
        dataset_id, _ = get_dataset(dataset_id=args_dataset_id)
        dataset_json_dir = os.path.join(f"{CARBON_DATA_DIR}/deeplearning/datasets", dataset_id)
        file_suffix = ".json"
    elif dataset_version == 2:
        dataset_id, _ = get_dataset_v2(dataset_id=args_dataset_id)
        dataset_json_dir = os.path.join(f"{CARBON_DATA_DIR}/deeplearning/datasets", dataset_id)
        file_suffix = ".jsonl"

    return dataset_id, dataset_json_dir, file_suffix


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset-id", type=str, required=False)
    parser.add_argument("--update-comparison-source", action="store_true", default=False)
    parser.add_argument("--get-latest-pretrain-dataset", action="store_true")

    add_job_creator_arguments(parser)

    args = parser.parse_args()

    assert (
        args.dataset_id is not None or args.pipeline_id is not None
    ) or args.get_latest_pretrain_dataset, "One of dataset id or pipeline id must be set"
    veselka_client = VeselkaClient()

    file_suffix = ".jsonl"

    LOG.info("Getting dataset")
    if args.get_latest_pretrain_dataset:
        _, pretrain_model_id = veselka_client.get_pretrain_version(environment="production")
        model_info = veselka_client.get_model_info(pretrain_model_id)
        dataset_id = model_info["dataset_id"]
        LOG.info(f"Using dataset for model {pretrain_model_id}")
        dataset_id, dataset_json_dir, file_suffix = get_dataset_info(dataset_id)
    elif args.pipeline_id is not None:
        dataset_id, _ = get_dataset_v2(pipeline_id=args.pipeline_id,)
        dataset_json_dir = os.path.join(f"{CARBON_DATA_DIR}/deeplearning/datasets", dataset_id)
        file_suffix = ".jsonl"
    else:
        dataset_id, dataset_json_dir, file_suffix = get_dataset_info(args.dataset_id)

    LOG.info(f"Using dataset_id: {dataset_id}")

    size_category_filepath = os.path.join(f"{CARBON_DATA_DIR}/deeplearning", f"size_category_options/{dataset_id}.json")

    _, _, _ = extract_size_category_options(
        dataset_json_dir=dataset_json_dir,
        size_category_filepath=size_category_filepath,
        buckets=SIZE_BUCKETS,
        env="production" if veselka_client.using_production else "staging",
        file_suffix=file_suffix,
    )

    if args.update_comparison_source:
        LOG.info(f"Setting {dataset_id} as the comparison source dataset")
        comparison_labeling_dataset_json = {
            "dataset_id": dataset_id,
            "valid": True,
        }
        veselka_client.post_comparison_labeling_dataset(payload=comparison_labeling_dataset_json)
        latest_valid_dataset_json = veselka_client.get_comparison_labeling_dataset()
        LOG.info(f"Retrieved the latest valid json: {latest_valid_dataset_json}")
        assert dataset_id == latest_valid_dataset_json["dataset_id"], "Dataset not match."


if __name__ == "__main__":
    main()
