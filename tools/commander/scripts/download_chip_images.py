#!/usr/bin/env python3
"""
Script to download chip images for coordinates on snapshotted predict images.

This script:
1. Gets current trajectories
2. Gets snapshotted predict images
3. Correlates trajectories with snapshots
4. Downloads chip images for matching coordinates
5. Compiles statistics per predict camera
"""

import asyncio
import os
from datetime import datetime
from typing import Any, Dict, List, Set, Tuple

from lib.common.commander.client import CommanderClient
from lib.common.tasks.manager import get_event_loop_by_name


def compile_camera_stats(
    trajectories_with_metadata: List[Any], chips_to_download: List[Any], available_snapshots: Set[Tuple[str, int]]
) -> Dict[str, Any]:
    """
    Compile statistics per predict camera.

    Args:
        trajectories_with_metadata: List of trajectories with snapshot metadata
        chips_to_download: List of trajectories that have corresponding snapshots
        available_snapshots: Set of available snapshots (pcam_id, timestamp_ms)

    Returns:
        Dictionary with statistics per camera
    """
    camera_stats: Dict[str, Any] = {}

    # Count trajectories with metadata per camera
    for traj in trajectories_with_metadata:
        metadata = getattr(traj, "snapshot_metadata")
        pcam_id = metadata.pcam_id

        if pcam_id not in camera_stats:
            camera_stats[pcam_id] = {
                "trajectories_with_metadata": 0,
                "available_snapshots": 0,
                "chips_to_download": 0,
                "unique_timestamps": set(),
                "coordinate_ranges": {"x": [], "y": []},
            }

        camera_stats[pcam_id]["trajectories_with_metadata"] += 1
        camera_stats[pcam_id]["unique_timestamps"].add(metadata.timestamp_ms)
        camera_stats[pcam_id]["coordinate_ranges"]["x"].append(metadata.center_x_px)
        camera_stats[pcam_id]["coordinate_ranges"]["y"].append(metadata.center_y_px)

    # Count available snapshots per camera
    for snapshot_key in available_snapshots:
        pcam_id, timestamp_ms = snapshot_key
        if pcam_id not in camera_stats:
            camera_stats[pcam_id] = {
                "trajectories_with_metadata": 0,
                "available_snapshots": 0,
                "chips_to_download": 0,
                "unique_timestamps": set(),
                "coordinate_ranges": {"x": [], "y": []},
            }
        camera_stats[pcam_id]["available_snapshots"] += 1

    # Count chips to download per camera
    for traj in chips_to_download:
        metadata = getattr(traj, "snapshot_metadata")
        pcam_id = metadata.pcam_id
        if pcam_id not in camera_stats:
            camera_stats[pcam_id] = {
                "trajectories_with_metadata": 0,
                "available_snapshots": 0,
                "chips_to_download": 0,
                "unique_timestamps": set(),
                "coordinate_ranges": {"x": [], "y": []},
            }
        camera_stats[pcam_id]["chips_to_download"] += 1

    # Calculate missing timestamps per camera
    for pcam_id in camera_stats:
        stats = camera_stats[pcam_id]

        # Get all available timestamps for this camera from snapshots
        available_timestamps_for_camera = set()
        for snapshot_key in available_snapshots:
            snapshot_pcam_id, timestamp_ms = snapshot_key
            if snapshot_pcam_id == pcam_id:
                available_timestamps_for_camera.add(timestamp_ms)

        # Find timestamps in trajectories that are not in available snapshots
        missing_timestamps = stats["unique_timestamps"] - available_timestamps_for_camera
        used_timestamps = stats["unique_timestamps"] & available_timestamps_for_camera

        stats["missing_timestamps_count"] = len(missing_timestamps)
        stats["used_timestamps_count"] = len(used_timestamps)
        stats["missing_timestamps"] = missing_timestamps
        stats["used_timestamps"] = used_timestamps

    # Convert sets to counts and add summary stats
    for pcam_id in camera_stats:
        stats = camera_stats[pcam_id]
        stats["unique_timestamps_count"] = len(stats["unique_timestamps"])
        if stats["coordinate_ranges"]["x"]:
            stats["x_range"] = (min(stats["coordinate_ranges"]["x"]), max(stats["coordinate_ranges"]["x"]))
            stats["y_range"] = (min(stats["coordinate_ranges"]["y"]), max(stats["coordinate_ranges"]["y"]))
        else:
            stats["x_range"] = (0, 0)
            stats["y_range"] = (0, 0)

        # Clean up the data structure
        del stats["unique_timestamps"]
        del stats["coordinate_ranges"]
        del stats["missing_timestamps"]  # Remove the set, keep only the count
        del stats["used_timestamps"]  # Remove the set, keep only the count

    return camera_stats


def print_camera_statistics(
    camera_stats: Dict[str, Any],
    total_trajectories_with_metadata: int,
    total_available_snapshots: int,
    total_chips_to_download: int,
) -> None:
    """Print detailed statistics per camera."""
    print("\n" + "=" * 60)
    print("📊 STATISTICS PER PREDICT CAMERA")
    print("=" * 60)

    # Sort cameras by ID for consistent output
    sorted_cameras = sorted(camera_stats.keys())

    for pcam_id in sorted_cameras:
        stats = camera_stats[pcam_id]
        print(f"\n📷 Camera {pcam_id}:")
        print(f"   • Trajectories with snapshot metadata: {stats['trajectories_with_metadata']}")
        print(f"   • Available snapshots: {stats['available_snapshots']}")
        print(f"   • Chips to download: {stats['chips_to_download']}")
        print(
            f"   • Unique timestamps in trajectories: {stats['unique_timestamps_count']} ({stats['used_timestamps_count']} used, {stats['missing_timestamps_count']} missing)"
        )
        if stats["x_range"][0] != stats["x_range"][1]:
            print(f"   • X coordinate range: {stats['x_range'][0]} - {stats['x_range'][1]}")
            print(f"   • Y coordinate range: {stats['y_range'][0]} - {stats['y_range'][1]}")

        # Calculate success rate
        if stats["trajectories_with_metadata"] > 0:
            success_rate = (stats["chips_to_download"] / stats["trajectories_with_metadata"]) * 100
            print(f"   • Success rate: {success_rate:.1f}%")

    print("\n📈 SUMMARY:")
    print(f"   • Total trajectories with snapshot metadata: {total_trajectories_with_metadata}")
    print(f"   • Total available snapshots: {total_available_snapshots}")
    print(f"   • Total chips to download: {total_chips_to_download}")
    if total_trajectories_with_metadata > 0:
        overall_success_rate = (total_chips_to_download / total_trajectories_with_metadata) * 100
        print(f"   • Overall success rate: {overall_success_rate:.1f}%")
    print("=" * 60)


async def download_chip_images(row_id: int = 1) -> None:
    """
    Main function to download chip images for trajectories with snapshot metadata.

    Args:
        row_id: The row ID to get trajectories and snapshots from
    """
    client = CommanderClient()

    print("🔍 Getting current trajectories...")
    trajectories_resp = await client.GetCurrentTrajectories(row_id=row_id)

    # Extract trajectories from the response
    trajectories = trajectories_resp.trajectories
    print(f"Found {len(trajectories)} trajectories")

    # Find trajectories with snapshot metadata (using 'snapshot_metadata')
    trajectories_with_metadata = []
    for traj in trajectories:
        if hasattr(traj, "snapshot_metadata"):
            metadata = getattr(traj, "snapshot_metadata")
            # Check if metadata is not empty (protobuf: all fields default to empty/zero)
            if metadata and metadata.ByteSize() > 0:
                # Check for required fields
                if (
                    hasattr(metadata, "pcam_id")
                    and metadata.pcam_id
                    and hasattr(metadata, "timestamp_ms")
                    and metadata.timestamp_ms
                    and hasattr(metadata, "center_x_px")
                    and metadata.center_x_px
                    and hasattr(metadata, "center_y_px")
                    and metadata.center_y_px
                ):
                    trajectories_with_metadata.append(traj)

    print(f"Found {len(trajectories_with_metadata)} trajectories with snapshot metadata")
    if len(trajectories_with_metadata) == 0:
        print(
            "⚠️  No trajectories with non-empty snapshot_metadata. If you expect some, check your data source or try a different row/time."
        )

    print("\n📸 Getting snapshotted predict images...")
    snapshots_resp = await client.SnapshotPredictImages(row_id=row_id)

    # Create a set of available snapshots for quick lookup
    available_snapshots = set()
    for snapshot in snapshots_resp.snapshots:
        available_snapshots.add((snapshot.pcam_id, snapshot.timestamp_ms))

    print(f"Found {len(available_snapshots)} unique snapshots")

    # Find trajectories that have metadata and a corresponding snapshot
    chips_to_download = []
    for traj in trajectories_with_metadata:
        metadata = getattr(traj, "snapshot_metadata")
        snapshot_key = (metadata.pcam_id, metadata.timestamp_ms)
        if snapshot_key in available_snapshots:
            chips_to_download.append(traj)

    # Compile statistics per camera
    camera_stats = compile_camera_stats(trajectories_with_metadata, chips_to_download, available_snapshots)

    # Print detailed statistics per camera
    total_trajectories_with_metadata = len(trajectories_with_metadata)
    total_available_snapshots = len(available_snapshots)
    total_chips_to_download = len(chips_to_download)

    print_camera_statistics(
        camera_stats, total_trajectories_with_metadata, total_available_snapshots, total_chips_to_download
    )

    if not chips_to_download:
        print("\nNo chip images to download.")
        return

    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"chip_downloads_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    print(f"\nCreated directory for downloads: {output_dir}\n")

    # Download chips
    for i, traj in enumerate(chips_to_download):
        metadata = getattr(traj, "snapshot_metadata")
        output_file = os.path.join(output_dir, f"chip_{traj.id}.png")

        print(f"Downloading chip {i+1}/{len(chips_to_download)} (ID: {traj.id})...")
        print(f"  Camera: {metadata.pcam_id}")
        print(f"  Timestamp: {metadata.timestamp_ms}")
        print(f"  Coordinates: ({metadata.center_x_px}, {metadata.center_y_px})")

        try:
            chip_resp = await client.GetChipForPredictImage(
                row_id=row_id,
                pcam_id=metadata.pcam_id,
                timestamp_ms=metadata.timestamp_ms,
                center_x_px=int(metadata.center_x_px),
                center_y_px=int(metadata.center_y_px),
            )

            # Write the chip image to file
            with open(output_file, "wb") as f:
                f.write(chip_resp.chip_image)

            print(f"  ✅ Saved to: {output_file}")

        except Exception as e:
            print(f"  ❌ Error downloading chip: {e}")

    print(f"\n✅ Success! Downloaded {len(chips_to_download)} chip images to '{output_dir}'.")


async def main() -> int:
    """Main entry point."""
    try:
        await download_chip_images()
    except Exception as e:
        print(f"Error: {e}")
        return 1
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name()).result()
    exit(exit_code)
