import asyncio
import json
import time
from collections import defaultdict
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, Union, cast

import grpc
import numpy as np
import numpy.typing as npt

from core.controls.exterminator.controllers.aimbot.error import P2PContextFailure, P2PTimeoutError
from cv.deepweed.deepweed_python import DeepweedDetection, DeepweedOutput
from cv.p2p.p2p_python import P2POutput
from cv.runtime.client.base_client import DEFAULT_CV_HOST, DEFAULT_CV_PORT
from cv.runtime.client.utils import CVCameraMap, get_secondary_hostname, get_secondary_port
from generated.cv.runtime.proto.cv_runtime_pb2 import DeepweedOutput as DeepweedOutputProto
from generated.cv.runtime.proto.cv_runtime_pb2 import (
    Empty,
    GetBootedRequest,
    GetCameraDimensionsRequest,
    GetCameraInfoRequest,
    GetCameraInfoResponse,
    GetCameraSettingsRequest,
    GetCameraSettingsResponse,
    GetCameraTemperaturesRequest,
    GetChipForPredictImageRequest,
    GetConnectorsRequest,
    GetConnectorsResponse,
    GetDeepweedDetectionCriteriaRequest,
    GetDeepweedDetectionCriteriaResponse,
    GetDeepweedSupportedCategoriesRequest,
    GetDeepweedSupportedCategoriesResponse,
    GetLightweightBurstRecordRequest,
    GetModelPathsRequest,
    GetNextDeepweedOutputRequest,
    GetNextFocusMetricRequest,
    GetNextFocusMetricResponse,
    GetNextP2POutputRequest,
    GetReadyRequest,
    GetReadyResponse,
    GetScoreQueueRequest,
    GetScoreQueueResponse,
    GetTimingRequest,
    GetTimingResponse,
    HitClass,
    LastNImageRequest,
    ListScoreQueuesResponse,
    LoadAndQueueRequest,
    P2PBufferingBurstPredictMetadata,
    P2PBufferringBurstCaptureRequest,
    P2PCaptureRequest,
    P2PContext,
    PointDetectionCategory,
    PredictRequest,
    SegmentationDetectionCategory,
    SetCameraSettingsRequest,
    SetConnectorsRequest,
    SetDeepweedDetectionCriteriaRequest,
    SetImageRequest,
    SetP2PContextRequest,
    SetTargetingStateRequest,
    SnapshotPredictImagesRequest,
    StartBurstRecordFramesRequest,
    StopBurstRecordFramesRequest,
    TargetSafetyZone,
    UnsetImageRequest,
)
from generated.cv.runtime.proto.cv_runtime_pb2_grpc import CVRuntimeServiceStub
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.time.time import maka_control_timestamp_ms
from tools.cvrt.utils import STR_TO_LIGHT_SOURCE_PRESET

if TYPE_CHECKING:
    from generated.proto.cv.cv_pb2 import P2PCaptureReasonValue

LOG = get_logger(__name__)


class CVRuntimeClient:
    def __init__(
        self, hostname: str = DEFAULT_CV_HOST, port: int = DEFAULT_CV_PORT, hostname2: str = "", port2: int = 0,
    ) -> None:
        self._addr = f"{hostname}:{port}"
        if hostname2 == "":
            hostname2 = get_secondary_hostname(hostname)
        if port2 == 0:
            port2 = get_secondary_port()
        self._addr2 = f"{hostname2}:{port2}"
        self._channel = None
        self._stub: Optional[CVRuntimeServiceStub] = None
        self._async_channel = None
        self._async_stub: Optional[CVRuntimeServiceStub] = None
        self._channel2 = None
        self._stub2: Optional[CVRuntimeServiceStub] = None
        self._async_channel2 = None
        self._async_stub2: Optional[CVRuntimeServiceStub] = None
        self._camera_dimensions_cache: Dict[str, Tuple[int, int]] = {}
        self._camera_matrix_cache: Dict[str, npt.NDArray[Any]] = {}
        self._distortion_coefficient_cache: Dict[str, npt.NDArray[Any]] = {}
        self._cam_map: CVCameraMap = CVCameraMap()

    def has_secondary(self) -> bool:
        return self._cam_map.has_secondary()

    def _get_primary_grpc_stub(self) -> CVRuntimeServiceStub:
        if self._stub is None:
            if self._channel is None:
                self._channel = grpc.insecure_channel(
                    self._addr, options=[("grpc.max_receive_message_length", 50 * 1024 * 1024)]
                )
            self._stub = CVRuntimeServiceStub(self._channel)
        return self._stub

    def _get_secondary_grpc_stub(self) -> CVRuntimeServiceStub:
        if self._stub2 is None:
            if self._channel2 is None:
                self._channel2 = grpc.insecure_channel(
                    self._addr2, options=[("grpc.max_receive_message_length", 50 * 1024 * 1024)]
                )
            self._stub2 = CVRuntimeServiceStub(self._channel2)
        return self._stub2

    def _get_grpc_stub(self, cam_id: Optional[str] = None) -> CVRuntimeServiceStub:
        if cam_id is None:
            return self._get_primary_grpc_stub()

        return self._get_primary_grpc_stub() if self._cam_map.is_on_primary(cam_id) else self._get_secondary_grpc_stub()

    def _get_primary_async_grpc_stub(self) -> CVRuntimeServiceStub:
        if self._async_stub is None:
            if self._async_channel is None:
                self._async_channel = grpc.aio.insecure_channel(
                    self._addr, options=[("grpc.max_receive_message_length", 50 * 1024 * 1024)]
                )
            self._async_stub = CVRuntimeServiceStub(self._async_channel)
        return self._async_stub

    def _get_secondary_async_grpc_stub(self) -> CVRuntimeServiceStub:
        if self._async_stub2 is None:
            if self._async_channel2 is None:
                self._async_channel2 = grpc.aio.insecure_channel(
                    self._addr2, options=[("grpc.max_receive_message_length", 50 * 1024 * 1024)]
                )
            self._async_stub2 = CVRuntimeServiceStub(self._async_channel2)
        return self._async_stub2

    def _get_async_grpc_stub(self, cam_id: str) -> CVRuntimeServiceStub:
        return (
            self._get_primary_async_grpc_stub()
            if self._cam_map.is_on_primary(cam_id)
            else self._get_secondary_async_grpc_stub()
        )

    def reset(self) -> None:
        self._stub = None
        self._channel = None
        self._async_stub = None
        self._async_channel = None
        if self.has_secondary():
            self._stub2 = None
            self._channel2 = None
            self._async_stub2 = None
            self._async_channel2 = None

    def start_burst_record(self, camera_id: str, duration_ms: int, path: str, downsample_factor: int = 1) -> None:
        req = StartBurstRecordFramesRequest(
            cam_id=camera_id, duration_ms=duration_ms, path=path, downsample_factor=downsample_factor
        )
        self._get_grpc_stub(camera_id).StartBurstRecordFrames(req)

    def start_burst_record_async(
        self, camera_id: str, duration_ms: int, path: str, dont_capture_predict_image: bool = False,
    ) -> "asyncio.Future[bool]":
        req = StartBurstRecordFramesRequest(
            cam_id=camera_id, duration_ms=duration_ms, path=path, dont_capture_predict_image=dont_capture_predict_image,
        )
        loop = asyncio.get_event_loop()
        fgrpc = self._get_grpc_stub(camera_id).StartBurstRecordFrames.future(req)
        done_future: "asyncio.Future[bool]" = loop.create_future()

        def done_xlate_futures(grpc_future: "asyncio.Future[bool]") -> None:
            fail = grpc_future.exception()
            if fail is not None:
                loop.call_soon_threadsafe(done_future.set_exception, fail)
            else:
                loop.call_soon_threadsafe(done_future.set_result, True)

        fgrpc.add_done_callback(done_xlate_futures)
        return done_future

    def stop_burst_record_async(
        self, camera_id: str, last_frame_timestamp_ms: Optional[int] = None
    ) -> "asyncio.Future[bool]":
        req = StopBurstRecordFramesRequest(cam_id=camera_id, last_frame_timestamp_ms=last_frame_timestamp_ms)
        loop = asyncio.get_event_loop()
        fgrpc = self._get_grpc_stub(camera_id).StopBurstRecordFrames.future(req)
        done_future: "asyncio.Future[bool]" = loop.create_future()

        def done_xlate_futures(grpc_future: "asyncio.Future[bool]") -> None:
            fail = grpc_future.exception()
            if fail is not None:
                loop.call_soon_threadsafe(done_future.set_exception, fail)
            else:
                loop.call_soon_threadsafe(done_future.set_result, True)

        fgrpc.add_done_callback(done_xlate_futures)
        return done_future

    def stop_burst_record(self, camera_id: str, last_frame_timestamp_ms: Optional[int] = None) -> None:
        req = StopBurstRecordFramesRequest(cam_id=camera_id, last_frame_timestamp_ms=last_frame_timestamp_ms)
        self._get_grpc_stub(camera_id).StopBurstRecordFrames(req)

    def set_p2p_context(
        self,
        target_camera_id: str,
        predict_camera_id: str,
        predict_timestamp_ms: int,
        perspective_coord: Tuple[float, float],
        safe_zone_up: float,
        safe_zone_down: float,
        safe_zone_left: float,
        safe_zone_right: float,
        secondary_predict_camera_id: Optional[str] = None,
        secondary_predict_timestamp_ms: Optional[int] = None,
        secondary_perspective_coord: Optional[Tuple[float, float]] = None,
    ) -> None:
        secondary_context = None
        if (
            secondary_predict_camera_id is not None
            and secondary_predict_timestamp_ms is not None
            and secondary_perspective_coord is not None
        ):
            secondary_context = P2PContext(
                predict_cam_id=secondary_predict_camera_id,
                predict_timestamp_ms=secondary_predict_timestamp_ms,
                predict_coord_x=secondary_perspective_coord[0],
                predict_coord_y=secondary_perspective_coord[1],
            )
        req = SetP2PContextRequest(
            target_cam_id=target_camera_id,
            primary_context=P2PContext(
                predict_cam_id=predict_camera_id,
                predict_timestamp_ms=predict_timestamp_ms,
                predict_coord_x=perspective_coord[0],
                predict_coord_y=perspective_coord[1],
            ),
            secondary_context=secondary_context,
            safety_zone=TargetSafetyZone(
                up=safe_zone_up, down=safe_zone_down, left=safe_zone_left, right=safe_zone_right,
            ),
        )
        self._get_grpc_stub(target_camera_id).SetP2PContext(req)

    async def set_p2p_context_async(
        self,
        target_camera_id: str,
        predict_camera_id: str,
        predict_timestamp_ms: int,
        perspective_coord: Tuple[float, float],
        safe_zone_up: float,
        safe_zone_down: float,
        safe_zone_left: float,
        safe_zone_right: float,
        secondary_predict_camera_id: Optional[str] = None,
        secondary_predict_timestamp_ms: Optional[int] = None,
        secondary_perspective_coord: Optional[Tuple[float, float]] = None,
        identifier: int = 0,
    ) -> None:
        secondary_context = None
        if (
            secondary_predict_camera_id is not None
            and secondary_predict_timestamp_ms is not None
            and secondary_perspective_coord is not None
        ):
            secondary_context = P2PContext(
                predict_cam_id=secondary_predict_camera_id,
                predict_timestamp_ms=secondary_predict_timestamp_ms,
                predict_coord_x=secondary_perspective_coord[0],
                predict_coord_y=secondary_perspective_coord[1],
            )
        req = SetP2PContextRequest(
            target_cam_id=target_camera_id,
            primary_context=P2PContext(
                predict_cam_id=predict_camera_id,
                predict_timestamp_ms=predict_timestamp_ms,
                predict_coord_x=perspective_coord[0],
                predict_coord_y=perspective_coord[1],
            ),
            secondary_context=secondary_context,
            safety_zone=TargetSafetyZone(
                up=safe_zone_up, down=safe_zone_down, left=safe_zone_left, right=safe_zone_right,
            ),
            target_id=identifier,
        )
        try:
            await self._get_async_grpc_stub(target_camera_id).SetP2PContext(req)
        except Exception as ex:
            raise P2PContextFailure(
                f"Failed to set p2p context for target {target_camera_id}, predict {predict_camera_id} ts = {predict_timestamp_ms}"
            ) from ex

    def get_latest_p2p_prediction(self, target_cam_id: str) -> P2POutput:
        return self.get_next_p2p_prediction(target_cam_id, 0, 0)

    def get_next_p2p_prediction(self, target_cam_id: str, timeout_ms: int, last_timestamp_ms: int) -> P2POutput:
        req = GetNextP2POutputRequest(cam_id=target_cam_id, timeout_ms=timeout_ms, timestamp_ms=last_timestamp_ms)
        try:
            output_proto = self._get_grpc_stub(target_cam_id).GetNextP2POutput(req)
            output = P2POutput()
            output.matched = output_proto.matched
            output.target_coord_x = output_proto.target_coord_x
            output.target_coord_y = output_proto.target_coord_y
            output.target_timestamp_ms = output_proto.target_timestamp_ms
            output.predict_timestamp_ms = output_proto.predict_timestamp_ms
            output.safe = output_proto.safe
            output.predict_coord_x = output_proto.predict_coord_x
            output.predict_coord_y = output_proto.predict_coord_y
            output.predict_cam = output_proto.predict_cam
            return output
        except grpc.RpcError as err:
            if err.code() == grpc.StatusCode.DEADLINE_EXCEEDED:
                raise P2PTimeoutError(
                    f"Failed to find next p2p for {target_cam_id}, after {last_timestamp_ms} in {timeout_ms}ms"
                ) from None
            raise

    def convert_deepweed_prediction(self, output_proto: DeepweedOutputProto, buffer: DeepweedOutput) -> None:
        for pDet in output_proto.detections:
            detection = DeepweedDetection()
            detection.x = pDet.x
            detection.y = pDet.y
            detection.size = pDet.size
            detection.score = pDet.score
            detection.set_hit_class(1 if pDet.hit_class == HitClass.CROP else 0)

            weedDetectionClassScores: List[float] = []
            for score in pDet.weed_detection_class_scores:
                weedDetectionClassScores.append(score)
            detection.set_weed_detection_class_scores(weedDetectionClassScores)

            detection.set_mask_intersections(list(pDet.mask_intersections))

            buffer.detections.append(detection)
        buffer.timestamp_ms = output_proto.timestamp_ms
        buffer.mask_channel_classes = list(output_proto.mask_channel_classes)
        buffer.weed_detection_classes = list(output_proto.weed_detection_classes)

    def get_next_deepweed_prediction(
        self, predict_cam_id: str, timeout_ms: int, last_timestamp_ms: int, buffer: Optional[DeepweedOutput] = None
    ) -> DeepweedOutput:
        if buffer is None:
            buffer = DeepweedOutput()
        req = GetNextDeepweedOutputRequest(cam_id=predict_cam_id, timeout_ms=timeout_ms, timestamp_ms=last_timestamp_ms)
        output_proto = self._get_grpc_stub(predict_cam_id).GetNextDeepweedOutput(req)
        self.convert_deepweed_prediction(output_proto, buffer)
        return buffer

    def get_latest_deepweed_prediction(
        self, predict_cam_id: str, buffer: Optional[DeepweedOutput] = None
    ) -> DeepweedOutput:
        return self.get_next_deepweed_prediction(predict_cam_id, 0, 0, buffer)

    def get_camera_dimensions(self, cam_id: str) -> Tuple[int, int]:
        if cam_id not in self._camera_dimensions_cache:
            req = GetCameraDimensionsRequest(cam_id=cam_id)
            response = self._get_grpc_stub(cam_id).GetCameraDimensions(req)
            height = response.height
            width = response.width
            self._camera_dimensions_cache[cam_id] = (height, width)

        return self._camera_dimensions_cache[cam_id]

    def set_camera_settings(
        self,
        *,
        cam_ids: List[str],
        exposure_us: Optional[float] = None,
        gamma: Optional[float] = None,
        gain_db: Optional[float] = None,
        wb_ratio_red: Optional[float] = None,
        wb_ratio_green: Optional[float] = None,
        wb_ratio_blue: Optional[float] = None,
        auto_whitebalance: Optional[bool] = None,
        light_source_preset: Optional[str] = None,
        roi_offset_x: Optional[int] = None,
        roi_offset_y: Optional[int] = None,
        mirror: Optional[bool] = None,
        flip: Optional[bool] = None,
        strobing: Optional[bool] = None,
        ptp: Optional[bool] = None,
        print_request: bool = True,
    ) -> None:
        _light_source_preset = None

        if light_source_preset is not None:
            _light_source_preset = STR_TO_LIGHT_SOURCE_PRESET[light_source_preset]

        req = SetCameraSettingsRequest(
            cam_ids=cam_ids,
            exposure_us=exposure_us,
            gamma=gamma,
            gain_db=gain_db,
            wb_ratio_red=wb_ratio_red,
            wb_ratio_green=wb_ratio_green,
            wb_ratio_blue=wb_ratio_blue,
            auto_whitebalance=auto_whitebalance,
            light_source_preset=_light_source_preset,
            roi_offset_x=roi_offset_x,
            roi_offset_y=roi_offset_y,
            mirror=mirror,
            flip=flip,
            strobing=strobing,
            ptp=ptp,
        )

        if print_request:
            print(f"request: {req}")

        self._get_grpc_stub().SetCameraSettings(req, timeout=10)

    def get_camera_settings(self, *, cam_ids: List[str],) -> Dict[str, Dict[str, Any]]:
        req = GetCameraSettingsRequest(cam_ids=cam_ids,)

        response: GetCameraSettingsResponse = self._get_grpc_stub().GetCameraSettings(req, timeout=5)

        return self._format_settings(response)

    def get_camera_info(self) -> GetCameraInfoResponse:
        req = GetCameraInfoRequest()

        response: GetCameraInfoResponse = self._get_grpc_stub().GetCameraInfo(req, timeout=5)

        return response

    def _format_settings(self, response: GetCameraSettingsResponse) -> Dict[str, Dict[str, Any]]:
        ret = {}

        for camera_settings_response in response.camera_settings_response:
            settings = {
                "exposure_us": camera_settings_response.exposure_us
                if camera_settings_response.HasField("exposure_us")
                else None,
                "gamma": camera_settings_response.gamma if camera_settings_response.HasField("gamma") else None,
                "gain_db": camera_settings_response.gain_db if camera_settings_response.HasField("gain_db") else None,
                "light_source_preset": camera_settings_response.light_source_preset
                if camera_settings_response.HasField("light_source_preset")
                else None,
                "wb_ratio_red": camera_settings_response.wb_ratio_red
                if camera_settings_response.HasField("wb_ratio_red")
                else None,
                "wb_ratio_green": camera_settings_response.wb_ratio_green
                if camera_settings_response.HasField("wb_ratio_green")
                else None,
                "wb_ratio_blue": camera_settings_response.wb_ratio_blue
                if camera_settings_response.HasField("wb_ratio_blue")
                else None,
                "auto_whitebalance": camera_settings_response.auto_whitebalance
                if camera_settings_response.HasField("auto_whitebalance")
                else None,
                "roi_width": camera_settings_response.roi_width
                if camera_settings_response.HasField("roi_width")
                else None,
                "roi_height": camera_settings_response.roi_height
                if camera_settings_response.HasField("roi_height")
                else None,
                "roi_offset_x": camera_settings_response.roi_offset_x
                if camera_settings_response.HasField("roi_offset_x")
                else None,
                "roi_offset_y": camera_settings_response.roi_offset_y
                if camera_settings_response.HasField("roi_offset_y")
                else None,
                "gpu_id": camera_settings_response.gpu_id if camera_settings_response.HasField("gpu_id") else None,
                "mirror": camera_settings_response.mirror,
                "flip": camera_settings_response.flip,
                "strobing": camera_settings_response.strobing,
                "ptp": camera_settings_response.ptp,
            }

            ret[camera_settings_response.cam_id] = settings

        return ret

    def get_connectors(self, *, cam_ids: List[str], connector_ids: List[str],) -> Dict[str, List[Dict[str, Any]]]:
        req = GetConnectorsRequest(cam_ids=cam_ids, connector_ids=connector_ids,)

        response: GetConnectorsResponse = self._get_grpc_stub().GetConnectors(req, timeout=5)

        connectors: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        for connector in response.connector_response:
            connectors[connector.cam_id].append(
                {
                    "connector_id": connector.connector_id,
                    "is_enabled": connector.is_enabled,
                    "reduction_ratio": connector.reduction_ratio,
                }
            )

        return connectors

    def set_connectors(
        self,
        *,
        cam_ids: List[str],
        connector_ids: List[str],
        is_enabled: Optional[bool],
        reduction_ratio: Optional[int],
    ) -> None:
        req = SetConnectorsRequest(
            cam_ids=cam_ids, connector_ids=connector_ids, is_enabled=is_enabled, reduction_ratio=reduction_ratio
        )

        print(f"request: {req}")
        self._get_grpc_stub().SetConnectors(req, timeout=5)

    def get_timing(self) -> Dict[str, Dict[str, Any]]:
        req = GetTimingRequest()

        response: GetTimingResponse = self._get_grpc_stub().GetTiming(req, timeout=5)

        nodes: Dict[str, Dict[str, Any]] = {}
        for node in response.node_timing:
            nodes[node.name] = {
                "name": node.name,
                "fps_mean": node.fps_mean,
                "fps_99pct": node.fps_99pct,
                "latency_ms_mean": node.latency_ms_mean,
                "latency_ms_99pct": node.latency_ms_99pct,
                "state": node.state,
            }
            for state in node.state_timings:
                nodes[node.name][f"state_{state}_ms"] = node.state_timings[state]

        return nodes

    def get_score_queue(self, score_type: str, sort: bool = False) -> List[Tuple[str, int, float]]:
        req = GetScoreQueueRequest(score_type=score_type)
        response: GetScoreQueueResponse = self._get_grpc_stub().GetScoreQueue(req, timeout=5)

        response_items: List[Tuple[str, int, float]] = [
            (r.cam_id, r.timestamp_ms, r.score) for r in response.score_object
        ]

        if sort:
            response_items.sort(key=lambda r: r[2], reverse=True)
        return response_items

    def list_score_queues(self) -> List[Dict[str, Any]]:
        req = Empty()
        response: ListScoreQueuesResponse = self._get_grpc_stub().ListScoreQueues(req, timeout=5)
        response_items: List[Dict[str, Any]] = [
            {"queue_type": sq.score_queue, "number_items": sq.num_items} for sq in response.score_queue
        ]

        return response_items

    def send_and_load_images(self, *, file_paths: List[str], timestamps_ms: List[int], cam_id: str) -> None:
        req = LoadAndQueueRequest(cam_id=cam_id, file_paths=file_paths, timestamps_ms=timestamps_ms)
        self._get_grpc_stub(cam_id).LoadAndQueue(req, timeout=80)

    def predict(  # noqa: C901
        self,
        *,
        file_paths: List[str],
        timestamps_ms: List[int],
        use_case: str,
        cam_id: str,
        perspective_file_paths: Optional[List[str]] = None,
        perspective_cam_id: Optional[str] = None,
        perspective_coords: Optional[Tuple[float, float]] = None,
    ) -> Union[List[Optional[DeepweedOutput]], List[Optional[P2POutput]]]:
        req = PredictRequest(cam_id=cam_id, file_paths=file_paths, timestamps_ms=timestamps_ms)
        self._get_grpc_stub(cam_id).Predict(req, timeout=15)

        results: List[Optional[Any]] = []

        if use_case == "deepweed":
            results = cast(List[Optional[DeepweedOutput]], results)
            for timestamp in timestamps_ms:
                try:
                    results.append(self.get_next_deepweed_prediction(cam_id, 5000, timestamp - 1))
                except Exception:
                    print("Timeout")
                    results.append(None)

        elif use_case == "p2p":
            assert perspective_coords is not None

            req = PredictRequest(
                cam_id=perspective_cam_id, file_paths=perspective_file_paths, timestamps_ms=[timestamps_ms[0] - 2000],
            )
            self._get_grpc_stub(perspective_cam_id).Predict(req, timeout=15)
            time.sleep(0.5)  # Allow time for predict to set image before setting context
            self.set_p2p_context("target1", "predict1", timestamps_ms[0] - 2000, perspective_coords, 0.0, 0.0, 0.0, 0.0)

            for timestamp in timestamps_ms:
                try:
                    return_val = self.get_next_p2p_prediction(cam_id, 2000, timestamp - 1)
                    assert return_val.predict_timestamp_ms == timestamps_ms[0] - 2000
                    assert return_val.target_timestamp_ms == timestamp
                    results.append(return_val)
                except Exception:
                    import traceback

                    print(traceback.format_exc())
                    print("Timeout")
                    results.append(None)

        return results

    def set_image(self, *, cam_id: str, image_source: str) -> None:
        req = SetImageRequest(cam_id=cam_id, file_path=image_source)
        self._get_grpc_stub(cam_id).SetImage(req, timeout=5)

    def unset_image(self, *, cam_id: str) -> None:
        req = UnsetImageRequest(cam_id=cam_id)
        self._get_grpc_stub(cam_id).UnsetImage(req, timeout=5)

    def get_model_paths(self, print_paths: bool = True) -> None:
        req = GetModelPathsRequest()
        resp = self._get_grpc_stub().GetModelPaths(req)

        if print_paths:
            print(f"deepweed: {resp.deepweed}")
            print(f"furrows: {resp.furrows}")
            print(f"p2p: {resp.p2p}")

    def get_deepweed_supported_categories(self, cam_id: Optional[str] = None) -> GetDeepweedSupportedCategoriesResponse:
        req = GetDeepweedSupportedCategoriesRequest(cam_id=cam_id)
        response = self._get_grpc_stub(cam_id).GetDeepweedSupportedCategories(req)
        return cast(GetDeepweedSupportedCategoriesResponse, response)

    def get_deepweed_detection_criteria(self, cam_id: Optional[str] = None) -> GetDeepweedDetectionCriteriaResponse:
        req = GetDeepweedDetectionCriteriaRequest()
        response = self._get_grpc_stub(cam_id).GetDeepweedDetectionCriteria(req)
        return cast(GetDeepweedDetectionCriteriaResponse, response)

    def set_deepweed_detection_criteria(
        self,
        point_categories: List[Tuple[str, float]],
        segmentation_categories: List[Tuple[str, float, float]],
        weed_point_threshold: float,
        crop_point_threshold: float,
    ) -> None:
        req = SetDeepweedDetectionCriteriaRequest(
            weed_point_threshold=weed_point_threshold,
            crop_point_threshold=crop_point_threshold,
            point_categories=[PointDetectionCategory(category=x[0], threshold=float(x[1])) for x in point_categories],
            segmentation_categories=[
                SegmentationDetectionCategory(category=x[0], threshold=float(x[1]), safety_radius_in=float(x[2]))
                for x in segmentation_categories
            ],
        )
        self._get_grpc_stub().SetDeepweedDetectionCriteria(req)

    def get_temperatures(self) -> None:
        req = GetCameraTemperaturesRequest()
        resp = self._get_grpc_stub().GetCameraTemperatures(req)

        blob = {}

        blob["timestamp_ms"] = maka_control_timestamp_ms()
        for camera_temp in resp.temperature:
            blob[camera_temp.cam_id] = camera_temp.temperature

        print(json.dumps(blob))

    def wait_for_cv(self) -> bool:
        error_count = 0
        while not bot_stop_handler.stopped:
            loading = False
            try:
                if not self.get_booted():
                    loading = True
                    time.sleep(1)
                    continue
                return True
            except grpc.RpcError:
                self.reset()
                if error_count % 60 == 0:
                    if loading:
                        LOG.warning("Connected to CV Runtime. Waiting for CV Runtime to boot...")
                    else:
                        LOG.warning(
                            "Failed To Connect to CV Runtime. Waiting available connection to CV Runtime Process..."
                        )
                error_count += 1
            time.sleep(1)
        return False

    def get_lightweight_burst_record(self, show_response: bool = False) -> None:
        request = GetLightweightBurstRecordRequest()
        response = self._get_grpc_stub().GetLightweightBurstRecord(request)

        if show_response:
            print(response)

    def get_booted(self) -> bool:
        if self.has_secondary():
            response1 = self._get_primary_grpc_stub().GetBooted(GetBootedRequest())
            response2 = self._get_secondary_grpc_stub().GetBooted(GetBootedRequest())
            return cast(bool, response1.booted) and cast(bool, response2.booted)

        response = self._get_grpc_stub().GetBooted(GetBootedRequest())
        return cast(bool, response.booted)

    def get_ready(self, check_secondary: bool = False) -> GetReadyResponse:
        if check_secondary and self.has_secondary():
            response = self._get_secondary_grpc_stub().GetReady(GetReadyRequest())
        else:
            response = self._get_primary_grpc_stub().GetReady(GetReadyRequest())
        return cast(GetReadyResponse, response)

    async def save_buffered_burst(
        self,
        camera_id: str,
        path: str = "",
        start_timestamp_ms: int = 0,
        end_timestamp_ms: int = 0,
        dont_capture_predict: bool = False,
        predict_path: Optional[str] = None,
        save_predict_metadata: bool = False,
        plant_size_px: float = 0,
    ) -> None:
        req = P2PBufferringBurstCaptureRequest(
            cam_id=camera_id,
            path=path,
            start_timestamp_ms=start_timestamp_ms,
            end_timestamp_ms=end_timestamp_ms,
            dont_capture_predict_image=dont_capture_predict,
            predict_path=predict_path,
            predict_path_exists=predict_path is not None,
            save_predict_metadata=save_predict_metadata,
            predict_metadata=P2PBufferingBurstPredictMetadata(plant_size_px=plant_size_px),
        )
        try:
            await self._get_async_grpc_stub(camera_id).P2PBufferringBurstCapture(req)
        except grpc.aio.AioRpcError as e:
            LOG.info(f"Failed to stop buffering Error: {e}")

    async def p2p_capture(
        self, camera_id: str, name: str, timestamp_ms: int, write_to_disk: bool, reason: "P2PCaptureReasonValue"
    ) -> None:
        req = P2PCaptureRequest(
            cam_id=camera_id, name=name, timestamp_ms=timestamp_ms, write_to_disk=write_to_disk, reason=reason
        )
        try:
            await self._get_async_grpc_stub(camera_id).P2PCapture(req)
        except grpc.aio.AioRpcError as e:
            LOG.info(f"Failed to capture p2p Error: {e}")

    async def set_targeting_state(self, weeding: bool, thinning: bool) -> None:
        req = SetTargetingStateRequest(weeding_enabled=weeding, thinning_enabled=thinning)
        try:
            await self._get_primary_async_grpc_stub().SetTargetingState(req)
            if self.has_secondary():
                await self._get_secondary_async_grpc_stub().SetTargetingState(req)
        except grpc.aio.AioRpcError as e:
            LOG.info(f"Failed to set targeting state. Error: {e}")

    async def get_next_focus_metric(self, camera_id: str, timestamp_ms: int = 0) -> Optional[Tuple[float, int]]:
        req = GetNextFocusMetricRequest(cam_id=camera_id, timestamp_ms=timestamp_ms)
        try:
            resp: GetNextFocusMetricResponse = await self._get_async_grpc_stub(camera_id).GetNextFocusMetric(req)
            return (resp.focus_metric, resp.timestamp_ms)
        except grpc.aio.AioRpcError as e:
            LOG.info(f"Failed to get next focus metric for {camera_id} Error: {e}")
            return None

    def get_last_n_images_meta(self, cam_id: str, n: int) -> List[Dict[str, Any]]:
        req = LastNImageRequest(cam_id=cam_id, num_images=n)

        image_metadata: List[Dict[str, Any]] = []

        for image_meta in self._get_grpc_stub(cam_id).GetLastNImages(req):
            image_metadata.append(
                {
                    "timestamp_ms": image_meta.timestamp_ms,
                    "image": np.fromstring(image_meta.bytes, dtype=np.uint8).reshape(  # type: ignore
                        (image_meta.height, image_meta.width, 4)
                    ),
                }
            )

        return image_metadata

    async def snapshot_predict_images(self) -> List[Tuple[str, int]]:
        """Trigger snapshot collection of predict images.

        Returns:
            List of (pcam_id, timestamp_ms) pairs for snapshotted images.
        """
        req = SnapshotPredictImagesRequest()
        try:
            resp = await self._get_primary_async_grpc_stub().SnapshotPredictImages(req)
            return [(snapshot.pcam_id, snapshot.timestamp_ms) for snapshot in resp.snapshots]
        except grpc.aio.AioRpcError as e:
            LOG.error(f"Failed to snapshot predict images. Error: {e}")
            raise

    async def get_chip_for_predict_image(
        self, pcam_id: str, timestamp_ms: int, center_x_px: int, center_y_px: int
    ) -> bytes:
        """Get a cropped chip image around specified coordinates."""
        req = GetChipForPredictImageRequest(
            pcam_id=pcam_id, timestamp_ms=timestamp_ms, center_x_px=center_x_px, center_y_px=center_y_px,
        )
        try:
            resp = await self._get_async_grpc_stub(pcam_id).GetChipForPredictImage(req)
            return bytes(resp.image_and_metadata.bytes)
        except grpc.aio.AioRpcError as e:
            LOG.error(f"Failed to get chip for predict image {pcam_id}@{timestamp_ms}. Error: {e}")
            raise
