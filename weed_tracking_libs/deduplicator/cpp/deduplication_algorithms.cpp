#include <cmath>
#include <nanoflann.hpp>
#include <set>
#include <spdlog/spdlog.h>
#include <trajectory/cpp/trajectory.hpp>
#include <weed_tracking/cpp/deduplication/deduplication.h>
#include <weed_tracking_libs/decision_line/cpp/decision_line.hpp>
#include <weed_tracking_libs/deduplicator/cpp/deduplication_algorithms.hpp>

namespace carbon {
namespace deduplicator {

void populate_id_positions(
    const std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
        &trajectories,
    std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> &items,
    std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> &excluded,
    filterOpts &opts) {
  for (auto &pair : trajectories) {
    if ((pair.second->get_num_detections() >= opts.min_num_detections) &&
        (!opts.only_use_in_band || pair.second->banding_state() != carbon::weed_tracking::OutOfBand) &&
        (!opts.only_use_predict_space || pair.second->is_in_predict_space())) {
      items.emplace_back(pair.first, pair.second->get_estimated_position_mm_from_now(0));
    } else {
      excluded.emplace_back(pair.first, pair.second->get_estimated_position_mm_from_now(0));
    }
  }
}

bool allow_dedup(std::shared_ptr<carbon::trajectory::Trajectory> trajectory_a,
                 std::shared_ptr<carbon::trajectory::Trajectory> trajectory_b) {
  auto a_dup_status = trajectory_a->duplicate_status();
  auto b_dup_status = trajectory_b->duplicate_status();
  bool allow_a =
      a_dup_status == trajectory::DuplicateStatus::kUnset || a_dup_status == trajectory::DuplicateStatus::kUnique;
  bool allow_b =
      b_dup_status == trajectory::DuplicateStatus::kUnset || b_dup_status == trajectory::DuplicateStatus::kUnique;
  return allow_a && allow_b;
}

void DeduplicationAlgorithms::deduplicate_classic(
    std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
        &trajectories,
    DeduplicationParameters deduplication_parameters, std::vector<DuplicateTraj> &dup_ids) {
  // Build a kdtree with trajectories
  std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> items;
  std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> excluded;
  auto opt = filterOpts{deduplication_parameters.min_num_detections, deduplication_parameters.only_use_in_band, false};
  populate_id_positions(trajectories, items, excluded, opt);
  KDTreeAdaptor tree(items);

  std::unordered_map<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition> pos_map(items.begin(),
                                                                                                          items.end());

  std::unordered_set<carbon::trajectory::Trajectory::ID> used;
  std::vector<DuplicateTrajPtr> dups;
  std::unordered_set<carbon::trajectory::Trajectory::ID> uniques;
  add_passed_decision_line(trajectories, uniques, excluded);
  for (size_t i = 0; i < tree.kdtree_get_point_count(); ++i) {
    carbon::trajectory::Trajectory::ID id = tree.get_id_for_index(i);
    if (used.find(id) != used.end()) {
      continue;
    }
    auto pit = trajectories.find(id);
    if (pit == trajectories.end()) {
      continue;
    }
    std::vector<std::pair<carbon::trajectory::Trajectory::ID, float>> results;
    // Get all points within radius of current point
    tree.query_radius(i, deduplication_parameters.radius_sqrd, results);
    if (results.empty()) {
      continue;
    }
    size_t min_index = 0;
    bool valid = false;
    std::shared_ptr<carbon::trajectory::Trajectory> duplicate = nullptr;
    for (size_t j = 0; j < results.size(); ++j) {
      if (id == results[j].first) {
        continue;
      }

      if ((!valid || results[j].second < results[min_index].second) && used.find(results[j].first) == used.end()) {
        auto dit = trajectories.find(results[j].first);
        if (dit == trajectories.end()) {
          continue;
        }
        auto tid = pit->second->tracker_id();
        auto dtid = dit->second->tracker_id();
        if (tid == (dtid + 1) || tid == (dtid - 1)) {
          // Only allow duplicate from adjacent trackers
          duplicate = dit->second;
          valid = true;
          min_index = j;
        }
      }
    }
    if (!valid) {
      if (decision_line::DecisionLine::get()->trajectory_passed_line(pos_map[id])) {
        uniques.emplace(id);
      }
      continue;
    }

    auto other_id = results[min_index].first;
    bool either_passed = decision_line::DecisionLine::get()->trajectory_passed_line(pos_map[id]) ||
                         decision_line::DecisionLine::get()->trajectory_passed_line(pos_map[other_id]);
    if (either_passed && allow_dedup(trajectories[id], trajectories[other_id])) {
      used.insert(id);
      used.insert(other_id);
      uniques.erase(id);
      uniques.erase(other_id);

      add_dup(pit->second, duplicate, dups);
    }
  }
  // assign and remove from the trajectories store
  if (!dups.empty()) {
    assign_duplicates(trajectories, dups, dup_ids, true);
  }
  if (!uniques.empty()) {
    assign_uniques(trajectories, uniques);
  }
}

void add_dup(std::shared_ptr<carbon::trajectory::Trajectory> trajectory_a,
             std::shared_ptr<carbon::trajectory::Trajectory> trajectory_b, std::vector<DuplicateTrajPtr> &dups) {
  auto a_dup_status = trajectory_a->duplicate_status();
  auto b_dup_status = trajectory_b->duplicate_status();
  // in the case of one being a unique and the other being unset, we want to enforce the unique as primary
  if (a_dup_status == trajectory::DuplicateStatus::kUnique && b_dup_status == trajectory::DuplicateStatus::kUnset) {
    dups.emplace_back(trajectory_a, trajectory_b);
  } else if (a_dup_status == trajectory::DuplicateStatus::kUnset &&
             b_dup_status == trajectory::DuplicateStatus::kUnique) {
    dups.emplace_back(trajectory_b, trajectory_a);
  } else if (trajectory_a->is_important_to_algos() && !trajectory_b->is_important_to_algos()) {
    dups.emplace_back(trajectory_a, trajectory_b);
  } else if (!trajectory_a->is_important_to_algos() && trajectory_b->is_important_to_algos()) {
    dups.emplace_back(trajectory_b, trajectory_a);
  } else {
    if (trajectory_a->is_important_to_algos() && trajectory_b->is_important_to_algos()) {
      spdlog::warn("Both deduplicated trajectories {} and {} are important.", trajectory_a->id(), trajectory_b->id());
    }
    dups.emplace_back(trajectory_a, trajectory_b);
  }
}

void DeduplicationAlgorithms::deduplicate_ransac(
    std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
        &trajectories,
    DeduplicationParameters deduplication_parameters, std::vector<DuplicateTraj> &dup_ids) {

  std::unordered_map<std::pair<uint32_t, uint32_t>, std::unordered_set<carbon::trajectory::Trajectory::ID>,
                     key_hash_function>
      per_overlap_trajectories;

  std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> items;
  std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> excluded;
  auto opt = filterOpts{deduplication_parameters.min_num_detections, deduplication_parameters.only_use_in_band, true};
  populate_id_positions(trajectories, items, excluded, opt);
  KDTreeAdaptor tree(items);

  std::unordered_map<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition> pos_map(items.begin(),
                                                                                                          items.end());
  std::unordered_set<carbon::trajectory::Trajectory::ID> uniques;
  add_passed_decision_line(trajectories, uniques, pos_map);
  add_passed_decision_line(trajectories, uniques, excluded);
  for (size_t i = 0; i < tree.kdtree_get_point_count(); i++) {
    carbon::trajectory::Trajectory::ID id = tree.get_id_for_index(i);
    auto pit = trajectories.find(id);
    if (pit == trajectories.end()) {
      continue;
    }
    std::vector<std::pair<carbon::trajectory::Trajectory::ID, float>> results;

    tree.query_radius(i, deduplication_parameters.radius_sqrd, results);
    if (results.empty()) {
      continue;
    }

    for (auto result : results) {
      // Checks if the trackers are neighboring trackers
      if ((pit->second->tracker_id() == trajectories[result.first]->tracker_id() + 1) ||
          (trajectories[result.first]->tracker_id() == pit->second->tracker_id() + 1)) {
        auto key = std::pair<uint32_t, uint32_t>(pit->second->tracker_id(), trajectories[result.first]->tracker_id());
        per_overlap_trajectories[key].emplace(id);
      }
    }
  }

  std::set<std::pair<uint32_t, uint32_t>> keys_used;
  std::vector<DuplicateTrajPtr> dups;
  for (auto pot_it = per_overlap_trajectories.begin(); pot_it != per_overlap_trajectories.end(); pot_it++) {
    auto key = pot_it->first;
    if (keys_used.find(key) != keys_used.end()) {
      continue;
    }
    auto tracker_1 = key.first;
    auto tracker_2 = key.second;

    auto inverse_key = std::pair(tracker_2, tracker_1);

    keys_used.emplace(key);
    keys_used.emplace(inverse_key);

    if (per_overlap_trajectories.find(inverse_key) == per_overlap_trajectories.end()) {
      continue;
    }

    auto &overlaping_left = pot_it->second;
    std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> items_left;
    std::transform(overlaping_left.begin(), overlaping_left.end(), std::back_inserter(items_left),
                   [&pos_map](auto id) { return std::make_pair(id, pos_map[id]); });
    KDTreeAdaptor tree_left(items_left);

    auto &overlaping_right = per_overlap_trajectories.find(inverse_key)->second;
    std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> items_right;
    std::transform(overlaping_right.begin(), overlaping_right.end(), std::back_inserter(items_right),
                   [&pos_map](auto id) { return std::make_pair(id, pos_map[id]); });
    KDTreeAdaptor tree_right(items_right);

    ransac(tree_left, tree_right, deduplication_parameters, trajectories, pos_map, dups, uniques);
  }
  // assign and but don't remove from the trajecories store
  assign_duplicates(trajectories, dups, dup_ids);
  assign_uniques(trajectories, uniques);
}

void DeduplicationAlgorithms::ransac(
    KDTreeAdaptor &tree_left, KDTreeAdaptor &tree_right, DeduplicationParameters dedup_params,
    std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
        &trajectories,
    std::unordered_map<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition> &pos_map,
    std::vector<DuplicateTrajPtr> &dups, std::unordered_set<carbon::trajectory::Trajectory::ID> &uniques) {
  auto radius_sqrd = dedup_params.radius_sqrd;
  auto match_radius_sqrd = dedup_params.match_radius_sqrd;
  std::forward_list<carbon::weed_tracking::CandidateMatch> options;

  std::vector<std::optional<std::tuple<float, float>>> tree_left_xys;
  std::vector<common::ThreadPool::JobID> jobs;

  for (size_t i = 0; i < tree_left.kdtree_get_point_count(); i++) {
    auto pos = tree_left.get_position_for_index(i);
    tree_left_xys.emplace_back(std::make_tuple(pos.get_x(), pos.get_y()));
  }

  std::vector<carbon::trajectory::Trajectory::ID> item_ids;
  for (size_t i = 0; i < tree_left.kdtree_get_point_count(); i++) {
    auto position = tree_left.get_position_for_index(i);
    auto id = tree_left.get_id_for_index(i);
    item_ids.push_back(id);

    std::vector<std::pair<carbon::trajectory::Trajectory::ID, float>> results;
    tree_right.query_radius(position.get_x(), position.get_y(), radius_sqrd, results);

    if (results.empty()) {
      continue;
    }

    auto max_matches = std::min<size_t>(tree_left.kdtree_get_point_count(), tree_right.kdtree_get_point_count());
    for (size_t j = 0; j < results.size(); j++) {
      auto trajectory_position = pos_map[results[j].first];
      auto shift_x = trajectory_position.get_x() - position.get_x();
      auto shift_y = trajectory_position.get_y() - position.get_y();

      carbon::weed_tracking::CandidateMatch *candidate =
          &options.emplace_front(tree_left.kdtree_get_point_count(), shift_x, shift_y);
      auto job = this->add_job([this, candidate, &tree_left_xys, &tree_right, max_matches, match_radius_sqrd]() {
        carbon::weed_tracking::shift_all_tracked_objects_by_vector_and_get_distances<
            carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>(
            candidate, tree_left_xys, tree_right, max_matches, std::sqrt(match_radius_sqrd));
      });
      jobs.push_back(job);
    }
  }

  for (auto &job : jobs) {
    this->await_job(job);
  }

  if (options.empty()) {
    return;
  }
  auto best = options.begin();
  for (auto it = options.begin(); it != options.end(); ++it) {
    if (it->error < best->error) {
      best = it;
    }
  }

  for (size_t j = 0; j < item_ids.size(); j++) {
    if (!best->matches[j].valid()) {
      continue;
    }
    auto this_id = item_ids[j];
    auto other_id = tree_right.get_id_for_index(best->matches[j].get().first);
    auto either_passed = decision_line::DecisionLine::get()->trajectory_passed_line(pos_map[this_id]) ||
                         decision_line::DecisionLine::get()->trajectory_passed_line(pos_map[other_id]);
    if (either_passed && allow_dedup(trajectories[this_id], trajectories[other_id])) {
      auto item_left = trajectories[this_id];
      auto item_right = trajectories[other_id];
      uniques.erase(item_right->id());
      uniques.erase(item_left->id());
      add_dup(item_left, item_right, dups);
    }
  }
}

float DeduplicationAlgorithms::get_error(KDTreeAdaptor &tree_left, KDTreeAdaptor &tree_right, float shift_x,
                                         float shift_y) {
  float err = 0.0f;
  for (size_t i = 0; i < tree_left.kdtree_get_point_count(); i++) {
    auto position = tree_left.get_position_for_index(i);
    auto new_x = position.get_x() + shift_x;
    auto new_y = position.get_y() + shift_y;

    std::vector<std::pair<carbon::trajectory::Trajectory::ID, float>> results;
    tree_right.query_nn(new_x, new_y, 1, results);

    if (results.empty()) {
      continue;
    }

    err += results[0].second;
  }

  return err;
}

// In the classic algorithm, we remove the duplicates from the trajectories store since we don't need them anymore
void assign_duplicates(std::unordered_map<carbon::trajectory::Trajectory::ID,
                                          std::shared_ptr<carbon::trajectory::Trajectory>> &trajectories,
                       std::vector<DuplicateTrajPtr> &dups, std::vector<DuplicateTraj> &dup_ids,

                       bool remove) {
  for (const auto &dup : dups) {
    dup.primary->set_duplicate_trajectory(dup.duplicate);
    dup.duplicate->set_primary_trajectory(dup.primary);
    dup_ids.emplace_back(dup.primary->id(), dup.duplicate->id());
    if (remove) {
      trajectories.erase(dup.primary->id());
      trajectories.erase(dup.duplicate->id());
    }
  }
}

// Never erase uniques from the trajectories store incase a duplicate appears later
void assign_uniques(std::unordered_map<carbon::trajectory::Trajectory::ID,
                                       std::shared_ptr<carbon::trajectory::Trajectory>> &trajectories,
                    std::unordered_set<carbon::trajectory::Trajectory::ID> &uniques) {
  for (auto &id : uniques) {
    trajectories[id]->duplicate_status(trajectory::DuplicateStatus::kUnique);
  }
}

template <typename T>
void add_passed_decision_line(const std::unordered_map<carbon::trajectory::Trajectory::ID,
                                                       std::shared_ptr<carbon::trajectory::Trajectory>> &trajectories,
                              std::unordered_set<carbon::trajectory::Trajectory::ID> &uniques, T &iterable_id_pos) {
  for (const auto &[id, pos] : iterable_id_pos) {
    if (decision_line::DecisionLine::get()->trajectory_passed_line(pos) &&
        trajectories.at(id)->duplicate_status() == trajectory::DuplicateStatus::kUnset) {
      uniques.emplace(id);
    }
  }
}

} // namespace deduplicator
} // namespace carbon