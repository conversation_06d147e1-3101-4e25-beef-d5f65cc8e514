import time
from typing import Callable, List, <PERSON>ple

import numpy as np
import torch
import torchvision.transforms as TF

from deeplearning.self_supervised.encoders import ResNet
from deeplearning.self_supervised.models import SimpleContrastiveModel
from deeplearning.self_supervised.samplers import BaseSampler
from deeplearning.self_supervised.train import train


class TestDataset(torch.utils.data.Dataset[torch.Tensor]):
    def __init__(self) -> None:
        self._data = torch.randn(20, 3, 224, 224)

    def __len__(self) -> int:
        return self._data.shape[0]

    def __getitem__(self, index: int) -> torch.Tensor:
        return self._data[index, :, :, :]


class TestDoubleDataset(torch.utils.data.Dataset[Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]]):
    def __init__(self, transforms: List[Callable[[torch.Tensor], torch.Tensor]]) -> None:
        self._data = torch.randn(20, 3, 224, 224)
        self._transforms = transforms

    def __len__(self) -> int:
        return self._data.shape[0]

    def __getitem__(self, index: int) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        rng = np.random.RandomState(index)
        i1 = self._data[rng.randint(0, self.__len__()), :, :, :]
        i2 = self._data[rng.randint(0, self.__len__()), :, :, :]

        i1_t = self._transforms[rng.randint(0, len(self._transforms))](i1)
        i2_t = self._transforms[rng.randint(0, len(self._transforms))](i2)

        return i1, i1_t, i2_t, i2


# Reserved for manual testing for now, since the github runner doesn't have wandb module
class TestEndToEnd:
    def test_end_to_end(self, num_gpus: int = 8) -> None:
        encoder = ResNet(pretrained=False, architecture="resnet50")

        transforms = [
            TF.RandomCrop(224),
            TF.GaussianBlur(kernel_size=3, sigma=(0.1, 2.0)),
            TF.RandomHorizontalFlip(),
            TF.RandomVerticalFlip(),
            TF.RandomRotation(10),
        ]
        model = SimpleContrastiveModel(encoder, loss_fn="quadruplet")

        model_id = "selfsup_test"
        config_dict = {
            "model_id": model_id,
            "num_epochs": 1,
            "num_samples": 20,
            "batch_size": 2,
            "learning_rate": 0.0001,
            "data_dir": f"/data/deeplearning/models/{model_id}",
            "wandb_project": "selfsup-fast-run",
            "description": f"End-to-end test {time.strftime('%Y%m%d%H%M%S')}",
            "num_gpus": num_gpus,
        }

        training_dataset = TestDoubleDataset(transforms=transforms)
        validation_dataset = TestDoubleDataset(transforms=transforms)

        train(model, training_dataset, validation_dataset, sampler=BaseSampler, **config_dict)
