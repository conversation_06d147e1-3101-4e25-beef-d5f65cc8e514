#include "redis_client.hpp"
#include <config/client/cpp/config_subscriber.hpp>
#include <spdlog/spdlog.h>
#include <string>
#include <string_view>

namespace lib {
namespace common {

RedisClient::RedisClient() {
  client_ = std::make_unique<sw::redis::Redis>("tcp://T2Yw28ctIZ3gwI1t@" + carbon::config::make_robot_local_addr(6379) +
                                               "?keep_alive=true");
}

bool RedisClient::set(const sw::redis::StringView &key, const sw::redis::StringView &val,
                      const std::chrono::milliseconds &ttl) {
  return client_->set(key, val, ttl);
}

bool RedisClient::set_long(const sw::redis::StringView &key, int64_t val, const std::chrono::milliseconds &ttl) {
  return set(key, std::string_view{std::to_string(val)}, ttl);
}

bool RedisClient::set_long_safe(const sw::redis::StringView &key, int64_t val, const std::chrono::milliseconds &ttl) {
  try {
    return set_long(key, val, ttl);
  } catch (const std::exception &ex) {
    spdlog::warn("Error while writing {} to redis: {}", key, ex.what());
    return false;
  }
}

bool RedisClient::set_double(const sw::redis::StringView &key, double val, const std::chrono::milliseconds &ttl) {
  return set(key, std::string_view{std::to_string(val)}, ttl);
}

std::optional<std::string> RedisClient::get(const sw::redis::StringView &key) {
  try {
    return client_->get(key);
  } catch (const std::exception &ex) {
    spdlog::warn("Error while getting {} from redis: {}", key, ex.what());
    return {};
  }
}

std::optional<int64_t> RedisClient::get_long(const sw::redis::StringView &key) {
  auto str = get(key);
  if (str) {
    return strtoll((*str).c_str(), NULL, 10);
  } else {
    return {};
  }
}

int64_t RedisClient::get_long_def(const sw::redis::StringView &key, int64_t def) {
  auto val = get_long(key);
  if (val) {
    return *val;
  }
  return def;
}

std::optional<double> RedisClient::get_double(const sw::redis::StringView &key) {
  auto str = get(key);
  if (str) {
    return stod(*str);
  } else {
    return {};
  }
}

sw::redis::OptionalString RedisClient::hget(const sw::redis::StringView &key, const sw::redis::StringView &field) {
  try {
    return client_->hget(key, field);
  } catch (const std::exception &ex) {
    spdlog::warn("Error while getting {} -> {} from redis: {}", key, field, ex.what());
    return {};
  }
}
sw::redis::OptionalLongLong RedisClient::hget_long(const sw::redis::StringView &key,
                                                   const sw::redis::StringView &field) {
  auto str = hget(key, field);
  if (str) {
    return strtoll((*str).c_str(), NULL, 10);
  } else {
    return {};
  }
}
int64_t RedisClient::hget_long_def(const sw::redis::StringView &key, const sw::redis::StringView &field, int64_t def) {
  auto val = hget_long(key, field);
  if (val) {
    return *val;
  }
  return def;
}
sw::redis::OptionalDouble RedisClient::hget_double(const sw::redis::StringView &key,
                                                   const sw::redis::StringView &field) {
  auto str = hget(key, field);
  if (str) {
    return strtod((*str).c_str(), NULL);
  } else {
    return {};
  }
}
double RedisClient::hget_double_def(const sw::redis::StringView &key, const sw::redis::StringView &field, double def) {
  auto val = hget_double(key, field);
  if (val) {
    return *val;
  }
  return def;
}

bool RedisClient::hset(const sw::redis::StringView &key, const sw::redis::StringView &field,
                       const sw::redis::StringView &val) {
  return client_->hset(key, field, val);
}
bool RedisClient::hset_safe(const sw::redis::StringView &key, const sw::redis::StringView &field,
                            const sw::redis::StringView &val) {
  try {
    return client_->hset(key, field, val);
  } catch (const std::exception &ex) {
    spdlog::warn("Error while writing {} -> {} to redis: {}", key, field, ex.what());
    return false;
  }
}
bool RedisClient::expire(const sw::redis::StringView &key, const std::chrono::seconds &timeout) {
  try {
    return client_->expire(key, timeout);
  } catch (const std::exception &ex) {
    spdlog::warn("Error while setting expiration for {}: {}", key, ex.what());
    return false;
  }
}
bool RedisClient::exists(const sw::redis::StringView &key) {
  try {
    return client_->exists(key);
  } catch (const std::exception &ex) {
    spdlog::warn("Error while checking existence of {}: {}", key, ex.what());
    return false;
  }
}
bool RedisClient::hexists(const sw::redis::StringView &key, const sw::redis::StringView &field) {
  try {
    return client_->hexists(key, field);
  } catch (const std::exception &ex) {
    spdlog::warn("Error while checking existence of {} -> {}: {}", key, field, ex.what());
    return false;
  }
}
bool RedisClient::hincrby_safe(const sw::redis::StringView &key, const sw::redis::StringView &field, int64_t inc) {
  try {
    client_->hincrby(key, field, inc);
    return true;
  } catch (const std::exception &ex) {
    spdlog::warn("Error while incrementing {}:{} by {}. EX={}", key, field, inc, ex.what());
  }
  return false;
}
bool RedisClient::hincrbyfloat_safe(const sw::redis::StringView &key, const sw::redis::StringView &field, double inc) {
  try {
    client_->hincrbyfloat(key, field, inc);
    return true;
  } catch (const std::exception &ex) {
    spdlog::warn("Error while incrementing {}:{} by {}. EX={}", key, field, inc, ex.what());
  }
  return false;
}

void RedisClient::ping() { client_->ping(); }

void RedisClient::wait_until_ready() {
  auto time_to_sleep = 100;
  while (true) {
    try {
      ping();
      break;
    } catch (const std::exception &ex) {
      spdlog::warn("Redis connection is down, will retry");
      std::this_thread::sleep_for(std::chrono::milliseconds(time_to_sleep));
      time_to_sleep = std::min(time_to_sleep * 2, 10000);
    }
  }
}
} // namespace common
} // namespace lib
