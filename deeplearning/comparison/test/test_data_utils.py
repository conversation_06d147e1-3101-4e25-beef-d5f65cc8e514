import random

import torch

from deeplearning.comparison.data_utils import cosine_similarity_normed_inputs, generate_samples_outside_of_size_limit
from deeplearning.comparison.tables import ComparisonLabels

EPS = 1e-5


def test_metrics_precision() -> None:
    query = 2 * torch.rand((1, 128))
    db = 3 * torch.rand((20, 128))

    torch_cosine_similarity = torch.nn.functional.cosine_similarity(query, db)
    our_cosine_similarity = cosine_similarity_normed_inputs(
        torch.nn.functional.normalize(query, dim=1), torch.nn.functional.normalize(db, dim=1)
    )

    assert (torch.abs(torch.sub(torch_cosine_similarity, our_cosine_similarity)) < EPS).all()


def test_generate_samples_outside_of_size_limit() -> None:
    comparison_labels = [
        ComparisonLabels(
            id="1",
            created=1,
            match=False,
            valid=True,
            image_one="image_one_a",
            label_one="label_one_a",
            label_one_category="label_one_category_a",
            label_one_x=1.0,
            label_one_y=1.1,
            label_one_radius=1.2,
            image_two="image_two_a",
            label_two="label_two_a",
            label_two_category="label_two_category_a",
            label_two_x=1.4,
            label_two_y=1.5,
            label_two_radius=1.6,
            source="legit",
            user="l1",
        ),
        ComparisonLabels(
            id="2",
            created=2,
            match=False,
            valid=True,
            image_one="image_one_b",
            label_one="label_one_b",
            label_one_category="label_one_category_b",
            label_one_x=2.0,
            label_one_y=2.1,
            label_one_radius=2.2,
            image_two="image_two_b",
            label_two="label_two_b",
            label_two_category="label_two_category_b",
            label_two_x=2.4,
            label_two_y=2.5,
            label_two_radius=2.6,
            source="legit",
            user="l2",
        ),
        ComparisonLabels(
            id="3",
            created=3,
            match=False,
            valid=True,
            image_one="image_one_c",
            label_one="label_one_c",
            label_one_category="label_one_category_c",
            label_one_x=3.0,
            label_one_y=3.1,
            label_one_radius=3.2,
            image_two="image_two_c",
            label_two="label_two_c",
            label_two_category="label_two_category_c",
            label_two_x=3.4,
            label_two_y=3.5,
            label_two_radius=3.6,
            source="legit",
            user="l3",
        ),
    ]
    random.seed(2)
    new_comparison_labels = generate_samples_outside_of_size_limit(
        n=2, size_multiple=2.0, comparison_labels=comparison_labels
    )

    assert len(new_comparison_labels) == 2

    assert new_comparison_labels[0].image_one == "image_one_a"
    assert new_comparison_labels[0].label_one == "label_one_a"
    assert new_comparison_labels[0].label_one_category == "label_one_category_a"
    assert new_comparison_labels[0].label_one_x == 1.0
    assert new_comparison_labels[0].label_one_y == 1.1
    assert new_comparison_labels[0].label_one_radius == 1.2

    assert new_comparison_labels[0].image_two == "image_one_c"
    assert new_comparison_labels[0].label_two == "label_one_c"
    assert new_comparison_labels[0].label_two_category == "label_one_category_c"
    assert new_comparison_labels[0].label_two_x == 3.0
    assert new_comparison_labels[0].label_two_y == 3.1
    assert new_comparison_labels[0].label_two_radius == 3.2

    assert new_comparison_labels[0].user == "<EMAIL>"

    assert new_comparison_labels[1].image_one == "image_one_c"
    assert new_comparison_labels[1].label_one == "label_one_c"
    assert new_comparison_labels[1].label_one_category == "label_one_category_c"
    assert new_comparison_labels[1].label_one_x == 3.0
    assert new_comparison_labels[1].label_one_y == 3.1
    assert new_comparison_labels[1].label_one_radius == 3.2

    assert new_comparison_labels[1].image_two == "image_one_a"
    assert new_comparison_labels[1].label_two == "label_one_a"
    assert new_comparison_labels[1].label_two_category == "label_one_category_a"
    assert new_comparison_labels[1].label_two_x == 1.0
    assert new_comparison_labels[1].label_two_y == 1.1
    assert new_comparison_labels[1].label_two_radius == 1.2

    assert new_comparison_labels[1].user == "<EMAIL>"
