#include "metrics/cpp/plant_conclusion_counter.hpp"

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/utils/environment.hpp>
#include <metrics/cpp/daily_timezone.hpp>

#include <chrono>
#include <cstdlib>
#include <iterator>
#include <string>
#include <string_view>

#include <spdlog/spdlog.h>

auto constexpr key_fmt_str = "/{}/{}/weed_tracking/combined/counts";
auto constexpr weed_size_key = "size/weeds";
auto constexpr crop_size_key = "size/crops";
auto constexpr targetable_time_key = "laser_time/targetable";
auto constexpr untargetable_time_key = "laser_time/untargetable";
auto constexpr targetable_count_key = "laser_time_count/targetable";
auto constexpr untargetable_count_key = "laser_time_count/untargetable";
auto constexpr valid_crop_count_key = "laser_time_count/valid_crops";
auto constexpr type_fmt_str = "type/weeds_type_count_{}";
constexpr uint32_t wait_time_ms = 10000;
namespace carbon::metrics {
std::string index_to_hask_key(size_t index) {
  switch (index) {
  case 0:
    return "disarmed/weed/{}";
  case 1:
    return "armed/weed/{}";
  case 2:
    return "disarmed/crop/{}";
  case 3:
    return "armed/crop/{}";
  default:
    return "";
  }
}
size_t bools_to_index(bool armed, bool is_weed) { return (size_t)armed + ((size_t)!is_weed << 1); }

PlantConclusionCounter::PlantData::PlantData()
    : counts(4), total_weeds_size_mm(0.0), total_crops_size_mm(0.0), total_targetable_req_laser_time_ms(0),
      total_untargetable_req_laser_time_ms(0), total_targetable_count(0), total_untargetable_count(0),
      total_valid_crop_count(0), count_by_type([](const std::string &key) {
        (void)key;
        return 0;
      }) {
  for (auto &cnt : counts) {
    cnt.resize(static_cast<size_t>(ConclusionType::kConclusionTypeNumber), 0);
  }
}
void PlantConclusionCounter::PlantData::write(const std::string &key, lib::common::RedisClient &redis) {
  for (size_t i = 0; i < counts.size(); ++i) {
    for (size_t j = 0; j < counts[i].size(); ++j) {
      redis.hincrby_safe(key, fmt::format(index_to_hask_key(i), j), counts[i][j]);
    }
  }
  for (auto &it : count_by_type) {
    redis.hincrby_safe(key, it.first, it.second);
  }
  redis.hincrbyfloat_safe(key, weed_size_key, total_weeds_size_mm);
  redis.hincrbyfloat_safe(key, crop_size_key, total_crops_size_mm);
  redis.hincrby_safe(key, targetable_time_key, total_targetable_req_laser_time_ms);
  redis.hincrby_safe(key, untargetable_time_key, total_untargetable_req_laser_time_ms);
  redis.hincrby_safe(key, targetable_count_key, total_targetable_count);
  redis.hincrby_safe(key, untargetable_count_key, total_untargetable_count);
  redis.hincrby_safe(key, valid_crop_count_key, total_valid_crop_count);
}

PlantConclusionCounter::PlantDataWrapper::PlantDataWrapper() : data(std::make_unique<PlantData>()) {}

void PlantConclusionCounter::PlantDataWrapper::increment(ConclusionType type, bool armed, bool is_weed, float size_mm,
                                                         const std::string &type_name, uint32_t required_laser_time,
                                                         bool valid, bool is_marked_for_thinning) {
  std::unique_lock lk(mut);
  ++(data->counts[bools_to_index(armed, is_weed)][static_cast<size_t>(type)]);
  if (type == ConclusionType::kFlicker) {
    // don't count flicker in any other metric
    return;
  }
  if (is_weed) {
    data->total_weeds_size_mm += size_mm;
    auto key = fmt::format(type_fmt_str, type_name);
    data->count_by_type[key] += 1u;
  } else {
    data->total_crops_size_mm += size_mm;
    if (valid) {
      ++(data->total_valid_crop_count);
    }
  }
  if (conclusion_is_targetable(type)) {
    // only count crops that are marked for thinning as targetable
    // count all weeds as targetable
    if (is_weed || is_marked_for_thinning) {
      data->total_targetable_req_laser_time_ms += required_laser_time;
      ++(data->total_targetable_count);
    }
  } else {
    data->total_untargetable_req_laser_time_ms += required_laser_time;
    ++(data->total_untargetable_count);
  }
}
PlantConclusionCounter::PlantConclusionCounter(std::shared_ptr<DailyTimezone> dtz)
    : dtz_(dtz ? dtz : std::make_shared<DailyTimezone>()) {
  thread_ = std::thread(std::bind(&PlantConclusionCounter::loop, this));
}

PlantConclusionCounter::~PlantConclusionCounter() { thread_.join(); }

void PlantConclusionCounter::increment(ConclusionType type, bool armed, bool is_weed, float size_mm,
                                       const std::string &type_name, uint32_t required_laser_time, bool valid,
                                       bool is_marked_for_thinning) {
  if (is_weed && !valid) {
    // Do not count invalid weeds at all
    return;
  }
  daily_data_.increment(type, armed, is_weed, size_mm, type_name, required_laser_time, valid, is_marked_for_thinning);
  job_data_.increment(type, armed, is_weed, size_mm, type_name, required_laser_time, valid, is_marked_for_thinning);
}
void PlantConclusionCounter::loop() {
  char *host_ptr = std::getenv("HOSTNAME");
  std::string host;
  if (host_ptr != NULL) {
    host += host_ptr;
  }
  if (host != "aimbot") {
    spdlog::info("Weed Counter running within {} which is not aimbot", host);
    return;
  }
  while (!lib::common::bot::BotStopHandler::get().is_stopped()) {
    write();
    lib::common::bot::BotStopHandler::get().sleep_safe(wait_time_ms);
  }
  write();
}
void PlantConclusionCounter::write() {
  auto daily_data = std::make_unique<PlantData>();
  auto job_data = std::make_unique<PlantData>();
  {
    std::unique_lock lk(daily_data_.mut);
    daily_data_.data.swap(daily_data);
  }
  {
    std::unique_lock lk(job_data_.mut);
    job_data_.data.swap(job_data);
  }
  auto daily_key = fmt::format(key_fmt_str, "daily", dtz_->get_day());
  daily_data->write(daily_key, dtz_->redis());
  dtz_->redis().expire(daily_key, std::chrono::seconds(864000));
  // TODO get jobs key
  auto active_job = dtz_->redis().get("jobs/active_job");
  if (active_job) {
    auto job_key = fmt::format(key_fmt_str, "jobs", *active_job);
    job_data->write(job_key, dtz_->redis());
  }
}
uint32_t PlantConclusionCounter::get_weeds_shot_today() {
  auto daily_key = fmt::format(key_fmt_str, "daily", dtz_->get_day());
  auto field_fmt_str = index_to_hask_key(bools_to_index(true, true));
  try {
    auto count = dtz_->redis().hget_long_def(
        daily_key, fmt::format(field_fmt_str, static_cast<uint32_t>(ConclusionType::kShot)), 0);
    return static_cast<uint32_t>(count);
  } catch (const std::exception &ex) {
    spdlog::warn("Failed to fetch shot count error={}", ex.what());
    return 0;
  }
}
} // namespace carbon::metrics