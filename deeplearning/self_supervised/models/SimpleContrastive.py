import torch

from ..losses import quadruplet_loss
from ..models.base import BaseEncoder
from .base import BaseModel

IMAGENET_MEAN = [0.485, 0.456, 0.406]
IMAGENET_STD = [0.229, 0.224, 0.225]

COMPATIBLE_LOSSES = ["quadruplet", "triplet"]


class SimpleContrastiveModel(BaseModel):
    def __init__(self, encoder: BaseEncoder, loss_fn: str = "quadruplet", distance: str = "euclidean"):
        super().__init__()

        self._encoder = encoder
        self._loss_fn_name = loss_fn
        self._distance = distance

        if distance not in ["euclidean", "cosine"]:
            raise ValueError(f"Distance function must be either 'euclidean' or 'cosine'. Got: {distance}")

        if loss_fn not in COMPATIBLE_LOSSES:
            raise ValueError(f"Unknown loss function: {loss_fn}")

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def forward(self, *args: torch.Tensor) -> torch.Tensor:
        assert len(args) == 4, f"Expected 4 inputs, got {len(args)}"
        x1, x2, x1_t, x2_t = args

        rank = torch.distributed.get_rank()
        x1 = x1.to(f"cuda:{rank}")
        x2 = x2.to(f"cuda:{rank}")
        x1_t = x1_t.to(f"cuda:{rank}")
        x2_t = x2_t.to(f"cuda:{rank}")

        emb_1 = self._encoder(x1)
        emb_2 = self._encoder(x2)

        emb_1_t = self._encoder(x1_t)
        emb_2_t = self._encoder(x2_t)

        if self._loss_fn_name == "quadruplet":
            loss = quadruplet_loss(emb_1, emb_2, emb_1_t, emb_2_t, margin=0.1, distance=self._distance)
        elif self._loss_fn_name == "triplet":
            distance_function = (
                lambda x, y: (1.0 - torch.nn.functional.cosine_similarity(x, y, dim=1))
                if self._distance == "cosine"
                else torch.nn.PairwiseDistance()(x, y)
            )
            loss = torch.nn.functional.triplet_margin_with_distance_loss(
                emb_1, emb_2, emb_2_t, margin=0.1, distance_function=distance_function
            )

        return loss
