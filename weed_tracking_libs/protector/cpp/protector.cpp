#include "weed_tracking_libs/protector/cpp/protector.hpp"

#include <config/client/cpp/config_subscriber.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <metrics/cpp/metrics_aggregator.hpp>
#include <targeting/cpp/targeting_mode_watcher.hpp>
#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>
#include <wheel_encoder/cpp/wheel_encoder.hpp>

#include <algorithm>
#include <chrono>
#include <functional>
#include <mutex>
#include <unordered_set>

#include <nanoflann.hpp>
#include <prometheus/gauge.h>
#include <prometheus/registry.h>
#include <spdlog/spdlog.h>

namespace carbon::protector {
constexpr auto call_delta = std::chrono::milliseconds(50);
static constexpr size_t num_pool_threads = 10;
using PosVec = std::vector<std::pair<trajectory::Trajectory::ID, std::vector<float>>>;

#define DIM 2
class PKDTreeAdaptor {
public:
  typedef typename nanoflann::metric_L2::template traits<float, PKDTreeAdaptor>::distance_t Metric_t;
  typedef nanoflann::KDTreeSingleIndexAdaptor<Metric_t, PKDTreeAdaptor, DIM> Index_t;

  PKDTreeAdaptor(const PosVec &positions, const size_t leaf_max_size = 10)
      : positions_(positions), index_(new Index_t(2, *this, nanoflann::KDTreeSingleIndexAdaptorParams(leaf_max_size))) {
    index_->buildIndex();
  }

  inline size_t kdtree_get_point_count() const { return positions_.size(); }

  inline float kdtree_get_pt(const size_t idx, const size_t dim) const {
    if (dim < positions_[idx].second.size()) {
      return positions_[idx].second[dim];
    } else {
      spdlog::warn("Accessing invalid dimension {}", dim);
      return 0.0f;
    }
  }
  inline const std::vector<float> &get_pos(const size_t idx) const { return positions_[idx].second; }

  template <class BBOX>
  bool kdtree_get_bbox(BBOX &) const {
    return false;
  }

  inline size_t query_radius(const std::vector<float> &query_point, float radius_sqrd,
                             std::vector<std::pair<trajectory::Trajectory::ID, float>> &out_points) const {
    std::vector<std::pair<size_t, float>> tmp_out;
    size_t count = index_->radiusSearch(query_point.data(), radius_sqrd, tmp_out, nanoflann::SearchParams());
    for (const auto &p : tmp_out) {
      out_points.emplace_back(positions_[p.first].first, p.second); // convert to trajectory id and distance sqrd;
    }
    return count;
  }
  inline trajectory::Trajectory::ID get_id_for_index(size_t index) { return positions_[index].first; }

private:
  const PosVec &positions_;
  std::unique_ptr<Index_t> index_;
};

std::shared_ptr<Protector> Protector::get() {
  static std::shared_ptr<Protector> protector{new Protector()};
  return protector;
}
void Protector::trajectory_request(const arbiter::Request<> &req) { new_requests_.add(req); }
Protector::Protector()
    : scoped_tree_(carbon::config::get_global_config_subscriber()->get_config_node("common", "protector"), false),
      pool_(num_pool_threads), thread_(&Protector::work_loop, this) {}

Protector::~Protector() { thread_.join(); }

void Protector::reload() {
  if (!scoped_tree_.reload_required()) {
    return;
  }
  cfg_.crop_protect_radius_mm = scoped_tree_->get_node("crop_protect_radius")->get_value<float>();
  cfg_.reverse_crop_protect_radius_mm = scoped_tree_->get_node("reverse_crop_protect_radius")->get_value<float>();
  cfg_.crop_protect_radius_multiplier = scoped_tree_->get_node("crop_protect_radius_multiplier")->get_value<float>();
  cfg_.speculative_crop_protect_radius_mm =
      scoped_tree_->get_node("speculative_crop_protect_radius")->get_value<float>();
  cfg_.allow_unprotect = scoped_tree_->get_node("allow_unprotect")->get_value<bool>();
  cfg_.speculative_crop_protect_min_radius_mm =
      scoped_tree_->get_node("speculative_crop_protect_min_radius")->get_value<float>();
  cfg_.speculative_crop_protect_radius_multiplier =
      scoped_tree_->get_node("speculative_crop_protect_radius_multiplier")->get_value<float>();
}

void Protector::work_loop() {
  std::mutex crop_mut, spec_mut, weed_mut;
  auto &frame_time = prometheus::BuildGauge()
                         .Name("protector_frame_time")
                         .Help("Avg time to process a frame in ms for the protector ingest client")
                         .Register(*(metrics::MetricsAggregator::get()->registry()))
                         .Add({});
  while (!lib::common::bot::BotStopHandler::get().is_stopped()) {
    auto start = std::chrono::system_clock::now();
    for (const auto &req : new_requests_.pop_all()) {
      if (req.type == arbiter::ARBITER_CLIENT_ADD) {
        try {
          bool is_weed = req.trajectory->get_decision(trajectory::DecisionFlag::kWeedingWeed) ||
                         req.trajectory->get_decision(trajectory::DecisionFlag::kThinningWeed);
          bool is_crop = req.trajectory->get_decision(trajectory::DecisionFlag::kWeedingCrop) ||
                         req.trajectory->get_decision(trajectory::DecisionFlag::kThinningCrop);
          if (is_weed) {
            weeds_.emplace(req.trajectory->id(), req.trajectory);
          }
          if (is_crop) {
            crops_.emplace(req.trajectory->id(), req.trajectory);
          }
        } catch (const std::exception &e) {
          spdlog::error("Protector::work_loop: {}", e.what());
        }
      } else {
        weeds_.erase(req.trajectory->id());
        crops_.erase(req.trajectory->id());
      }
    }
    if (!weeds_.empty() || !crops_.empty()) {
      reload();
      int64_t now =
          std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
              .count();
      std::optional<double> vel_mm = wheel_encoder::WheelEncoder::get()->get_cur_avg_vel().vel_mm;

      std::unordered_set<trajectory::Trajectory::ID> crop_protect_ids, spec_protect_ids, rev_crop_protect_ids,
          prev_protected_ids, prev_rev_protected_ids;
      PosVec crop_vec, weed_vec;
      crop_vec.reserve(crops_.size());
      weed_vec.reserve(weeds_.size());
      for (auto it = weeds_.begin(); it != weeds_.end();) {
        if (it->second->duplicate_status() == trajectory::DuplicateStatus::kDuplicate || it->second->past_kill_box() ||
            it->second->permanantly_oob()) {
          it = weeds_.erase(it);
          continue;
        }
        if (cfg_.allow_unprotect) {
          if (it->second->nonshootable_type() == trajectory::NonshootableType::kCrop &&
              it->second->is_in_predict_space()) {
            prev_protected_ids.insert(it->first);
          }
        }
        auto pos = it->second->get_estimated_position_mm_at_timestamp(now, vel_mm);
        std::vector<float> vec_pos = {pos.get_x(), pos.get_y()};
        weed_vec.emplace_back(it->first, vec_pos);
        ++it;
      }
      for (auto it = crops_.begin(); it != crops_.end();) {
        if (it->second->duplicate_status() == trajectory::DuplicateStatus::kDuplicate || it->second->past_kill_box() ||
            it->second->permanantly_oob()) {
          it = crops_.erase(it);
          continue;
        }
        if (cfg_.allow_unprotect) {
          if (it->second->crop_intersects_with_weed() && it->second->is_in_predict_space()) {
            prev_rev_protected_ids.insert(it->first);
          }
        }
        auto pos = it->second->get_estimated_position_mm_at_timestamp(now, vel_mm);
        std::vector<float> vec_pos = {pos.get_x(), pos.get_y()};
        crop_vec.emplace_back(it->first, vec_pos);
        ++it;
      }
      std::vector<common::ThreadPool::JobID> jobs;
      PKDTreeAdaptor crops(crop_vec);
      PKDTreeAdaptor weeds(weed_vec);
      if (crops.kdtree_get_point_count() > 0 && weeds.kdtree_get_point_count() > 0) {

        if (targeting::TargetingModeWatcher::get().weeding_enabled()) {
          bool use_crop_radius_speculative = cfg_.speculative_crop_protect_radius_mm < 0.0f;
          float spec_rad_sqrd = cfg_.speculative_crop_protect_radius_mm * cfg_.speculative_crop_protect_radius_mm;

          bool use_crop_radius_cpr = cfg_.crop_protect_radius_mm < 0.0f;
          float cpr_sqrd = cfg_.crop_protect_radius_mm * cfg_.crop_protect_radius_mm;
          for (size_t i = 0; i < crops.kdtree_get_point_count(); ++i) {
            auto traj = crops_[crops.get_id_for_index(i)].get();
            try {
              if (!traj->get_decision(trajectory::DecisionFlag::kWeedingCrop)) {
                continue;
              }
            } catch (const std::exception &e) {
              spdlog::error("Protector::work_loop: {}", e.what());
              continue;
            }
            float crop_rad = traj->size_mm();
            jobs.push_back(pool_.add_job([&, i, crop_rad]() {
              float spec_rad_sqrd_item = spec_rad_sqrd;
              if (use_crop_radius_speculative) {
                spec_rad_sqrd_item = cfg_.speculative_crop_protect_radius_multiplier * crop_rad;
                spec_rad_sqrd_item *= spec_rad_sqrd_item;
              }
              float cpr_sqrd_item = cpr_sqrd;
              if (use_crop_radius_cpr) {
                cpr_sqrd_item = cfg_.crop_protect_radius_multiplier * crop_rad;
                cpr_sqrd_item *= cpr_sqrd_item;
              }
              float rad_sqrd = std::max(spec_rad_sqrd_item, cpr_sqrd_item);
              std::vector<std::pair<trajectory::Trajectory::ID, float>> out_points;
              weeds.query_radius(crops.get_pos(i), rad_sqrd, out_points);
              for (const auto &pair : out_points) {
                if (pair.second <= cpr_sqrd_item) {
                  const std::unique_lock lk(weed_mut);
                  crop_protect_ids.insert(pair.first);
                }
                if (crop_rad >= cfg_.speculative_crop_protect_min_radius_mm) {
                  const std::unique_lock lk(spec_mut);
                  spec_protect_ids.insert(pair.first);
                }
              }
            }));
          }
        }
        if (cfg_.reverse_crop_protect_radius_mm > 0.0f && targeting::TargetingModeWatcher::get().thinning_enabled()) {
          float rcpr_sqrd = cfg_.reverse_crop_protect_radius_mm * cfg_.reverse_crop_protect_radius_mm;
          for (size_t i = 0; i < weeds.kdtree_get_point_count(); ++i) {
            auto traj = weeds_[weeds.get_id_for_index(i)].get();
            try {
              if (!traj->get_decision(trajectory::DecisionFlag::kThinningWeed)) {
                continue;
              }
            } catch (const std::exception &e) {
              spdlog::error("Protector::work_loop: {}", e.what());
              continue;
            }
            jobs.push_back(pool_.add_job([&, i]() {
              std::vector<std::pair<trajectory::Trajectory::ID, float>> out_points;
              crops.query_radius(weeds.get_pos(i), rcpr_sqrd, out_points);
              for (const auto &pair : out_points) {
                const std::unique_lock lk(crop_mut);
                rev_crop_protect_ids.insert(pair.first);
              }
            }));
          }
        }
        for (auto &job : jobs) {
          pool_.await_job(job);
        }
        if (!prev_protected_ids.empty()) {
          for (const auto &id : crop_protect_ids) {
            prev_protected_ids.erase(id);
          }
        }
        if (!prev_rev_protected_ids.empty()) {
          for (const auto &id : rev_crop_protect_ids) {
            prev_rev_protected_ids.erase(id);
          }
        }
        // prev_protected_ids now contain only ids that were protected, but should no longer be.
        for (const auto &id : crop_protect_ids) {
          weeds_[id]->set_nonshootable_type(trajectory::NonshootableType::kCrop);
        }
        for (const auto &id : spec_protect_ids) {
          weeds_[id]->disable_speculative();
        }
        for (const auto &id : rev_crop_protect_ids) {
          crops_[id]->set_crop_intersects_with_weed(true);
        }
        for (const auto &id : prev_protected_ids) {
          weeds_[id]->set_nonshootable_type(trajectory::NonshootableType::kNone);
        }
        for (const auto &id : prev_rev_protected_ids) {
          crops_[id]->set_crop_intersects_with_weed(false);
        }
      }
    }
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - start);
    frame_time.Set(static_cast<double>(elapsed.count()));
    if (elapsed < call_delta) {
      auto remaining = call_delta - elapsed;
      std::this_thread::sleep_for(remaining);
    } else {
      spdlog::warn("Crop/Weed protect running too slow. required < {}ms, actual = {}ms", call_delta.count(),
                   elapsed.count());
    }
  }
}

} // namespace carbon::protector