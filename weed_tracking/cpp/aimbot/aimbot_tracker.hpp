#pragma once

#ifdef <PERSON>
#include <pybind11/functional.h>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
namespace py = pybind11;
#endif

#include "cv/deepweed/output.h"
#include <atomic>
#include <condition_variable>
#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <cv/runtime/cpp/client/cv_runtime_client.hpp>
#include <ingest/cpp/ingest_producer.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/time.h>
#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>
#include <lib/common/geometric/cpp/geometric_scanner.hpp>
#include <memory>
#include <mutex>
#include <recorder/lib/proto_file_stream.hpp>
#include <stdint.h>
#include <string>
#include <thread>
#include <torch/torch.h>
#include <tuple>
#include <weed_tracking/cpp/tracking/weed_tracker.h>
#include <weed_tracking_libs/arbiter/cpp/arbiter_trigger.hpp>
#include <wheel_encoder/cpp/wheel_encoder.hpp>
#include "generated/recorder/proto/recorder.pb.h"

#define NUMBER_OF_DW_BUFFERS 2

namespace weed_tracking {
class Bands;
}
namespace carbon {
namespace weed_tracking {
struct PostDedupFrame {

  // TODO should I store this in a better way to not require copying?
  int64_t timestamp_ms;
  int64_t receive_ts_ms;
  cv::deepweed::DeepweedOutput dw_output;
  std::map<size_t, trajectory::Trajectory::ID> dw_to_track;
  std::vector<::recorder::TrackedItem> tracked_items;
  std::map<size_t, std::pair<float, float>> model_undistorted_coordinates;
  ::recorder::CandidateShift best_candidate_match;
  std::vector<::recorder::CandidateShift> all_candidate_matches;
  bool plant_matcher_valid;
  std::unordered_set<size_t> centroids_used_in_dedup;
};

class AimbotWeedTracker : public ingest::IngestProducer, public arbiter::ArbiterTrigger {
protected:
  std::string pcam_id_;
  uint32_t numeric_id_;
  uint32_t dedup_radius_px_;

  int64_t latest_dw_timestamp_ms_;
  int64_t latest_rotary_timestamp_ms_;

  std::unique_ptr<cv::runtime::client::CVRuntimeClient> cv_runtime_client_;

  cv::deepweed::DeepweedOutput dw_buffers_[NUMBER_OF_DW_BUFFERS];
  PostDedupFrame post_dedup_buffers_[NUMBER_OF_DW_BUFFERS];

  int latest_buffer_index_;
  int latest_post_dedup_buffer_index_;

  std::mutex mutex_;
  std::condition_variable start_cv_;
  bool start_ready_;

  int64_t previous_re_timestamp_ms_;
  int64_t current_re_timestamp_ms_;
  bool log_ticks_;
  std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam_;
  bool score_images_;
  std::atomic<bool> rotary_timeout_;
  std::atomic<bool> deepweed_error_;
  std::condition_variable we_cv_;
  std::mutex we_mut_;
  uint64_t last_we_timestamp_ms_;
  std::mutex buffer_mutex_;
  std::condition_variable buffer_cv_;
  std::shared_ptr<WeedTracker> weed_tracker_;
  std::atomic<bool> stopped_;
  int64_t receive_ts_;
  std::map<size_t, trajectory::Trajectory::ID> dw_to_track_;
  std::atomic<uint32_t> deepweed_output_processed_count_;
  std::atomic<uint32_t> deepweed_output_timeout_count_;
  std::atomic<uint32_t> deepweed_output_error_count_;

  // Keep the threads last
  std::thread dw_task_;
  std::thread we_task_;

  void dw_poll_loop();
  void we_poll_loop();
  void wait_for_ready();
  void join();
  void add_rotary_ticks(int64_t timestamp_ms, float mm_travelled);
  bool await_we(uint32_t timeout_ms, uint64_t compare_time_ms);

  std::shared_ptr<config::ConfigTree> record_predictions_;

public:
  AimbotWeedTracker(std::string pcam_id, uint32_t numeric_id, uint32_t width, uint32_t height, bool log_ticks,
                    std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam, float max_pos_mm_y,
                    bool score_images, uint32_t max_number_of_weeds, std::string cv_address);
  ~AimbotWeedTracker();
  std::string get_pcam_id();
  WeedTrackerOutput get_latest_weeds();
  std::shared_ptr<WeedTracker> get_weed_tracker();
  trajectory::TrackedItemPosition
  get_estimated_position_at_timestamp_from_trajectory(std::shared_ptr<trajectory::Trajectory> trajectory,
                                                      int64_t timestamp_ms);
  trajectory::TrackedItemCentroid get_last_centroid_with_perspective_before_timestamp_from_trajectory(
      std::shared_ptr<trajectory::Trajectory> trajectory, int64_t timestamp_ms);
  std::pair<float, float> get_flow(int64_t from_timestamp_ms, int64_t to_timestamp_ms);
  trajectory::TrackedItemCentroid get_last_item_from_trajectory(std::shared_ptr<trajectory::Trajectory> trajectory);
  float get_pix_per_ticks();
  std::shared_ptr<lib::common::geometric::GeometricCam> get_geo_cam();
  std::vector<double> get_averages_for_all_columns(std::optional<float> size_mm = std::nullopt);
  std::shared_ptr<trajectory::Trajectory> get_trajectory_for_id(uint32_t id);
  inline uint32_t numeric_id() { return numeric_id_; }
  void get_bands(int max_x, ::weed_tracking::Bands *result);
  bool get_next_retrieved_deepweed(uint32_t timeout_ms, int64_t previous_timestamp_ms, PostDedupFrame *output);
  void write_dw_predictions_at_time(int64_t timestamp_ms,
                                    std::shared_ptr<carbon::recorder::ProtobufStreamFileWriter> writer);

  std::tuple<uint32_t, size_t, size_t, bool, bool> aimbot_weed_tracker_state();

  inline float latency() const { return weed_tracker_->latency(); }
  inline float processing_time() const { return weed_tracker_->processing_time(); }
  virtual void set_add_trajectory_callback(ingest::AddDelCallback cb) override {
    weed_tracker_->get_tracker()->set_new_id_callback(cb);
  }
  virtual void set_del_trajectory_callback(ingest::AddDelCallback cb) override {
    weed_tracker_->get_tracker()->set_delete_id_callback(cb);
  }
  virtual void set_position_update_cb(arbiter::PositionUpdateCallback cb) override {
    weed_tracker_->get_tracker()->set_position_update_callback(cb);
  }
  inline uint32_t get_and_reset_deepweed_output_processed_count() {
    return deepweed_output_processed_count_.exchange(0);
  }
  inline uint32_t get_and_reset_deepweed_output_timeout_count() { return deepweed_output_timeout_count_.exchange(0); }
  inline uint32_t get_and_reset_deepweed_output_error_count() { return deepweed_output_error_count_.exchange(0); }
};

class AimbotTrackerOwner {
public:
  AimbotTrackerOwner(const AimbotTrackerOwner &ato) = delete;
  AimbotTrackerOwner &operator=(const AimbotTrackerOwner &) = delete;
  static AimbotTrackerOwner &get();
  void set(const std::vector<std::shared_ptr<AimbotWeedTracker>> &trackers);
  const std::unordered_map<uint32_t, std::shared_ptr<AimbotWeedTracker>> &get_trackers() const;
  const std::shared_ptr<AimbotWeedTracker> get_tracker(uint32_t id) const;

private:
  mutable std::mutex mut_;
  bool set_;
  std::unordered_map<uint32_t, std::shared_ptr<AimbotWeedTracker>> trackers_;
  AimbotTrackerOwner();
  bool check_exists() const;
};

} // namespace weed_tracking
} // namespace carbon