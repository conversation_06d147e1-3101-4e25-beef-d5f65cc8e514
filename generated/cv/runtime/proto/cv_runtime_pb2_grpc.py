# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.cv.runtime.proto import cv_runtime_pb2 as cv_dot_runtime_dot_proto_dot_cv__runtime__pb2


class CVRuntimeServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetP2PContext = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetP2PContext',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetP2PContextRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetP2PContextResponse.FromString,
                )
        self.GetCameraDimensions = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCameraDimensions',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraDimensionsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraDimensionsResponse.FromString,
                )
        self.GetCameraInfo = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCameraInfo',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraInfoRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraInfoResponse.FromString,
                )
        self.GetDeepweedIndexToCategory = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetDeepweedIndexToCategory',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryResponse.FromString,
                )
        self.SetDeepweedDetectionCriteria = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetDeepweedDetectionCriteria',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaResponse.FromString,
                )
        self.GetDeepweedDetectionCriteria = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetDeepweedDetectionCriteria',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaResponse.FromString,
                )
        self.GetDeepweedSupportedCategories = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetDeepweedSupportedCategories',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesResponse.FromString,
                )
        self.GetCameraTemperatures = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCameraTemperatures',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraTemperaturesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraTemperaturesResponse.FromString,
                )
        self.SetCameraSettings = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetCameraSettings',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetCameraSettingsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetCameraSettingsResponse.FromString,
                )
        self.GetCameraSettings = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCameraSettings',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraSettingsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraSettingsResponse.FromString,
                )
        self.StartBurstRecordFrames = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/StartBurstRecordFrames',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StartBurstRecordFramesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StartBurstRecordFramesResponse.FromString,
                )
        self.StopBurstRecordFrames = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/StopBurstRecordFrames',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StopBurstRecordFramesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StopBurstRecordFramesResponse.FromString,
                )
        self.GetConnectors = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetConnectors',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetConnectorsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetConnectorsResponse.FromString,
                )
        self.SetConnectors = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetConnectors',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetConnectorsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetConnectorsResponse.FromString,
                )
        self.GetTiming = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetTiming',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetTimingRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetTimingResponse.FromString,
                )
        self.Predict = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/Predict',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.PredictRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.PredictResponse.FromString,
                )
        self.LoadAndQueue = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/LoadAndQueue',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LoadAndQueueRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LoadAndQueueResponse.FromString,
                )
        self.SetImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetImage',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageResponse.FromString,
                )
        self.UnsetImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/UnsetImage',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.UnsetImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.UnsetImageResponse.FromString,
                )
        self.GetModelPaths = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetModelPaths',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetModelPathsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetModelPathsResponse.FromString,
                )
        self.SetGPSLocation = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetGPSLocation',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetGPSLocationRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetGPSLocationResponse.FromString,
                )
        self.SetImplementStatus = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetImplementStatus',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImplementStatusRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImplementStatusResponse.FromString,
                )
        self.SetImageScore = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetImageScore',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageScoreRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageScoreResponse.FromString,
                )
        self.GetScoreQueue = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetScoreQueue',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetScoreQueueRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetScoreQueueResponse.FromString,
                )
        self.ListScoreQueues = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/ListScoreQueues',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ListScoreQueuesResponse.FromString,
                )
        self.GetMaxImageScore = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetMaxImageScore',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxImageScoreRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxImageScoreResponse.FromString,
                )
        self.GetMaxScoredImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetMaxScoredImage',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxScoredImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
                )
        self.GetLatestP2PImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetLatestP2PImage',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLatestP2PImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PImageAndMetadataResponse.FromString,
                )
        self.GetChipImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetChipImage',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipImageAndMetadataResponse.FromString,
                )
        self.GetChipQueueInformation = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetChipQueueInformation',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipQueueInformationRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipQueueInformationResponse.FromString,
                )
        self.FlushQueues = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/FlushQueues',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.FlushQueuesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.FlushQueuesResponse.FromString,
                )
        self.GetLatestImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetLatestImage',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLatestImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
                )
        self.GetImageNearTimestamp = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetImageNearTimestamp',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetImageNearTimestampRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
                )
        self.GetLightweightBurstRecord = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetLightweightBurstRecord',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLightweightBurstRecordRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLightweightBurstRecordResponse.FromString,
                )
        self.GetBooted = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetBooted',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetBootedRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetBootedResponse.FromString,
                )
        self.GetReady = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetReady',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetReadyRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetReadyResponse.FromString,
                )
        self.GetDeepweedOutputByTimestamp = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetDeepweedOutputByTimestamp',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedOutputByTimestampRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.DeepweedOutput.FromString,
                )
        self.GetRecommendedStrobeSettings = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetRecommendedStrobeSettings',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsResponse.FromString,
                )
        self.P2PCapture = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/P2PCapture',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PCaptureRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PCaptureResponse.FromString,
                )
        self.SetAutoWhitebalance = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetAutoWhitebalance',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetAutoWhitebalanceRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetAutoWhitebalanceResponse.FromString,
                )
        self.GetNextDeepweedOutput = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetNextDeepweedOutput',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextDeepweedOutputRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.DeepweedOutput.FromString,
                )
        self.GetNextP2POutput = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetNextP2POutput',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextP2POutputRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2POutputProto.FromString,
                )
        self.SetTargetingState = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetTargetingState',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetTargetingStateRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetTargetingStateResponse.FromString,
                )
        self.P2PBufferringBurstCapture = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/P2PBufferringBurstCapture',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PBufferringBurstCaptureRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PBufferringBurstCaptureResponse.FromString,
                )
        self.GetNextFocusMetric = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetNextFocusMetric',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextFocusMetricRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextFocusMetricResponse.FromString,
                )
        self.RemoveDataDir = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/RemoveDataDir',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.RemoveDataDirRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.RemoveDataDirResponse.FromString,
                )
        self.GetLastNImages = channel.unary_stream(
                '/cv.runtime.proto.CVRuntimeService/GetLastNImages',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LastNImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
                )
        self.GetComputeCapabilities = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetComputeCapabilities',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ComputeCapabilitiesResponse.FromString,
                )
        self.GetSupportedTensorRTVersions = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetSupportedTensorRTVersions',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SupportedTensorRTVersionsResponse.FromString,
                )
        self.ReloadCategoryCollection = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/ReloadCategoryCollection',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.FromString,
                )
        self.GetCategoryCollection = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCategoryCollection',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCategoryCollectionResponse.FromString,
                )
        self.GetErrorState = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetErrorState',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetErrorStateResponse.FromString,
                )
        self.SnapshotPredictImages = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SnapshotPredictImages',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SnapshotPredictImagesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SnapshotPredictImagesResponse.FromString,
                )
        self.GetChipForPredictImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetChipForPredictImage',
                request_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipForPredictImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipForPredictImageResponse.FromString,
                )


class CVRuntimeServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SetP2PContext(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCameraDimensions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCameraInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedIndexToCategory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetDeepweedDetectionCriteria(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedDetectionCriteria(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedSupportedCategories(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCameraTemperatures(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCameraSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCameraSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartBurstRecordFrames(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopBurstRecordFrames(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetConnectors(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConnectors(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTiming(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Predict(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadAndQueue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UnsetImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModelPaths(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetGPSLocation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetImplementStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetImageScore(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetScoreQueue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListScoreQueues(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMaxImageScore(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMaxScoredImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLatestP2PImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetChipImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetChipQueueInformation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FlushQueues(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLatestImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetImageNearTimestamp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLightweightBurstRecord(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBooted(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetReady(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedOutputByTimestamp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRecommendedStrobeSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def P2PCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetAutoWhitebalance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextDeepweedOutput(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextP2POutput(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetTargetingState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def P2PBufferringBurstCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextFocusMetric(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RemoveDataDir(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLastNImages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetComputeCapabilities(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSupportedTensorRTVersions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReloadCategoryCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCategoryCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetErrorState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SnapshotPredictImages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetChipForPredictImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CVRuntimeServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetP2PContext': grpc.unary_unary_rpc_method_handler(
                    servicer.SetP2PContext,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetP2PContextRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetP2PContextResponse.SerializeToString,
            ),
            'GetCameraDimensions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraDimensions,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraDimensionsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraDimensionsResponse.SerializeToString,
            ),
            'GetCameraInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraInfo,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraInfoRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraInfoResponse.SerializeToString,
            ),
            'GetDeepweedIndexToCategory': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedIndexToCategory,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryResponse.SerializeToString,
            ),
            'SetDeepweedDetectionCriteria': grpc.unary_unary_rpc_method_handler(
                    servicer.SetDeepweedDetectionCriteria,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaResponse.SerializeToString,
            ),
            'GetDeepweedDetectionCriteria': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedDetectionCriteria,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaResponse.SerializeToString,
            ),
            'GetDeepweedSupportedCategories': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedSupportedCategories,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesResponse.SerializeToString,
            ),
            'GetCameraTemperatures': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraTemperatures,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraTemperaturesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraTemperaturesResponse.SerializeToString,
            ),
            'SetCameraSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCameraSettings,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetCameraSettingsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetCameraSettingsResponse.SerializeToString,
            ),
            'GetCameraSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraSettings,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraSettingsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraSettingsResponse.SerializeToString,
            ),
            'StartBurstRecordFrames': grpc.unary_unary_rpc_method_handler(
                    servicer.StartBurstRecordFrames,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StartBurstRecordFramesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StartBurstRecordFramesResponse.SerializeToString,
            ),
            'StopBurstRecordFrames': grpc.unary_unary_rpc_method_handler(
                    servicer.StopBurstRecordFrames,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StopBurstRecordFramesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StopBurstRecordFramesResponse.SerializeToString,
            ),
            'GetConnectors': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConnectors,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetConnectorsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetConnectorsResponse.SerializeToString,
            ),
            'SetConnectors': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConnectors,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetConnectorsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetConnectorsResponse.SerializeToString,
            ),
            'GetTiming': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTiming,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetTimingRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetTimingResponse.SerializeToString,
            ),
            'Predict': grpc.unary_unary_rpc_method_handler(
                    servicer.Predict,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.PredictRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.PredictResponse.SerializeToString,
            ),
            'LoadAndQueue': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadAndQueue,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LoadAndQueueRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LoadAndQueueResponse.SerializeToString,
            ),
            'SetImage': grpc.unary_unary_rpc_method_handler(
                    servicer.SetImage,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageResponse.SerializeToString,
            ),
            'UnsetImage': grpc.unary_unary_rpc_method_handler(
                    servicer.UnsetImage,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.UnsetImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.UnsetImageResponse.SerializeToString,
            ),
            'GetModelPaths': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModelPaths,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetModelPathsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetModelPathsResponse.SerializeToString,
            ),
            'SetGPSLocation': grpc.unary_unary_rpc_method_handler(
                    servicer.SetGPSLocation,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetGPSLocationRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetGPSLocationResponse.SerializeToString,
            ),
            'SetImplementStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.SetImplementStatus,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImplementStatusRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImplementStatusResponse.SerializeToString,
            ),
            'SetImageScore': grpc.unary_unary_rpc_method_handler(
                    servicer.SetImageScore,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageScoreRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageScoreResponse.SerializeToString,
            ),
            'GetScoreQueue': grpc.unary_unary_rpc_method_handler(
                    servicer.GetScoreQueue,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetScoreQueueRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetScoreQueueResponse.SerializeToString,
            ),
            'ListScoreQueues': grpc.unary_unary_rpc_method_handler(
                    servicer.ListScoreQueues,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ListScoreQueuesResponse.SerializeToString,
            ),
            'GetMaxImageScore': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMaxImageScore,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxImageScoreRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxImageScoreResponse.SerializeToString,
            ),
            'GetMaxScoredImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMaxScoredImage,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxScoredImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.SerializeToString,
            ),
            'GetLatestP2PImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLatestP2PImage,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLatestP2PImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PImageAndMetadataResponse.SerializeToString,
            ),
            'GetChipImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetChipImage,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipImageAndMetadataResponse.SerializeToString,
            ),
            'GetChipQueueInformation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetChipQueueInformation,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipQueueInformationRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipQueueInformationResponse.SerializeToString,
            ),
            'FlushQueues': grpc.unary_unary_rpc_method_handler(
                    servicer.FlushQueues,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.FlushQueuesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.FlushQueuesResponse.SerializeToString,
            ),
            'GetLatestImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLatestImage,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLatestImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.SerializeToString,
            ),
            'GetImageNearTimestamp': grpc.unary_unary_rpc_method_handler(
                    servicer.GetImageNearTimestamp,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetImageNearTimestampRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.SerializeToString,
            ),
            'GetLightweightBurstRecord': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLightweightBurstRecord,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLightweightBurstRecordRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLightweightBurstRecordResponse.SerializeToString,
            ),
            'GetBooted': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBooted,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetBootedRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetBootedResponse.SerializeToString,
            ),
            'GetReady': grpc.unary_unary_rpc_method_handler(
                    servicer.GetReady,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetReadyRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetReadyResponse.SerializeToString,
            ),
            'GetDeepweedOutputByTimestamp': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedOutputByTimestamp,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedOutputByTimestampRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.DeepweedOutput.SerializeToString,
            ),
            'GetRecommendedStrobeSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRecommendedStrobeSettings,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsResponse.SerializeToString,
            ),
            'P2PCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.P2PCapture,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PCaptureRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PCaptureResponse.SerializeToString,
            ),
            'SetAutoWhitebalance': grpc.unary_unary_rpc_method_handler(
                    servicer.SetAutoWhitebalance,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetAutoWhitebalanceRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetAutoWhitebalanceResponse.SerializeToString,
            ),
            'GetNextDeepweedOutput': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextDeepweedOutput,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextDeepweedOutputRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.DeepweedOutput.SerializeToString,
            ),
            'GetNextP2POutput': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextP2POutput,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextP2POutputRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2POutputProto.SerializeToString,
            ),
            'SetTargetingState': grpc.unary_unary_rpc_method_handler(
                    servicer.SetTargetingState,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetTargetingStateRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetTargetingStateResponse.SerializeToString,
            ),
            'P2PBufferringBurstCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.P2PBufferringBurstCapture,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PBufferringBurstCaptureRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PBufferringBurstCaptureResponse.SerializeToString,
            ),
            'GetNextFocusMetric': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextFocusMetric,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextFocusMetricRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextFocusMetricResponse.SerializeToString,
            ),
            'RemoveDataDir': grpc.unary_unary_rpc_method_handler(
                    servicer.RemoveDataDir,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.RemoveDataDirRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.RemoveDataDirResponse.SerializeToString,
            ),
            'GetLastNImages': grpc.unary_stream_rpc_method_handler(
                    servicer.GetLastNImages,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LastNImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.SerializeToString,
            ),
            'GetComputeCapabilities': grpc.unary_unary_rpc_method_handler(
                    servicer.GetComputeCapabilities,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ComputeCapabilitiesResponse.SerializeToString,
            ),
            'GetSupportedTensorRTVersions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSupportedTensorRTVersions,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SupportedTensorRTVersionsResponse.SerializeToString,
            ),
            'ReloadCategoryCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.ReloadCategoryCollection,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
            ),
            'GetCategoryCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCategoryCollection,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCategoryCollectionResponse.SerializeToString,
            ),
            'GetErrorState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetErrorState,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetErrorStateResponse.SerializeToString,
            ),
            'SnapshotPredictImages': grpc.unary_unary_rpc_method_handler(
                    servicer.SnapshotPredictImages,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SnapshotPredictImagesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SnapshotPredictImagesResponse.SerializeToString,
            ),
            'GetChipForPredictImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetChipForPredictImage,
                    request_deserializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipForPredictImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipForPredictImageResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'cv.runtime.proto.CVRuntimeService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CVRuntimeService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SetP2PContext(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetP2PContext',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetP2PContextRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetP2PContextResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCameraDimensions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetCameraDimensions',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraDimensionsRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraDimensionsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCameraInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetCameraInfo',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraInfoRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraInfoResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDeepweedIndexToCategory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetDeepweedIndexToCategory',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetDeepweedDetectionCriteria(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetDeepweedDetectionCriteria',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDeepweedDetectionCriteria(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetDeepweedDetectionCriteria',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDeepweedSupportedCategories(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetDeepweedSupportedCategories',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCameraTemperatures(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetCameraTemperatures',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraTemperaturesRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraTemperaturesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetCameraSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetCameraSettings',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetCameraSettingsRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetCameraSettingsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCameraSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetCameraSettings',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraSettingsRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCameraSettingsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartBurstRecordFrames(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/StartBurstRecordFrames',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StartBurstRecordFramesRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StartBurstRecordFramesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StopBurstRecordFrames(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/StopBurstRecordFrames',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StopBurstRecordFramesRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.StopBurstRecordFramesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetConnectors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetConnectors',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetConnectorsRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetConnectorsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetConnectors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetConnectors',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetConnectorsRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetConnectorsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTiming(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetTiming',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetTimingRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetTimingResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Predict(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/Predict',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.PredictRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.PredictResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LoadAndQueue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/LoadAndQueue',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LoadAndQueueRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LoadAndQueueResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetImage',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UnsetImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/UnsetImage',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.UnsetImageRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.UnsetImageResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetModelPaths(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetModelPaths',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetModelPathsRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetModelPathsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetGPSLocation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetGPSLocation',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetGPSLocationRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetGPSLocationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetImplementStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetImplementStatus',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImplementStatusRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImplementStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetImageScore(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetImageScore',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageScoreRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetImageScoreResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetScoreQueue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetScoreQueue',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetScoreQueueRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetScoreQueueResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListScoreQueues(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/ListScoreQueues',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ListScoreQueuesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetMaxImageScore(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetMaxImageScore',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxImageScoreRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxImageScoreResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetMaxScoredImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetMaxScoredImage',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetMaxScoredImageRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetLatestP2PImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetLatestP2PImage',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLatestP2PImageRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PImageAndMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetChipImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetChipImage',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipImageRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipImageAndMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetChipQueueInformation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetChipQueueInformation',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipQueueInformationRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ChipQueueInformationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FlushQueues(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/FlushQueues',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.FlushQueuesRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.FlushQueuesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetLatestImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetLatestImage',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLatestImageRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetImageNearTimestamp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetImageNearTimestamp',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetImageNearTimestampRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetLightweightBurstRecord(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetLightweightBurstRecord',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLightweightBurstRecordRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetLightweightBurstRecordResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetBooted(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetBooted',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetBootedRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetBootedResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetReady(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetReady',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetReadyRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetReadyResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDeepweedOutputByTimestamp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetDeepweedOutputByTimestamp',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetDeepweedOutputByTimestampRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.DeepweedOutput.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRecommendedStrobeSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetRecommendedStrobeSettings',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def P2PCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/P2PCapture',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PCaptureRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PCaptureResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetAutoWhitebalance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetAutoWhitebalance',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetAutoWhitebalanceRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetAutoWhitebalanceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextDeepweedOutput(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetNextDeepweedOutput',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextDeepweedOutputRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.DeepweedOutput.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextP2POutput(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetNextP2POutput',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextP2POutputRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2POutputProto.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetTargetingState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SetTargetingState',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetTargetingStateRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SetTargetingStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def P2PBufferringBurstCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/P2PBufferringBurstCapture',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PBufferringBurstCaptureRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.P2PBufferringBurstCaptureResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextFocusMetric(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetNextFocusMetric',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextFocusMetricRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetNextFocusMetricResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RemoveDataDir(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/RemoveDataDir',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.RemoveDataDirRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.RemoveDataDirResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetLastNImages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/cv.runtime.proto.CVRuntimeService/GetLastNImages',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.LastNImageRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetComputeCapabilities(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetComputeCapabilities',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.ComputeCapabilitiesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSupportedTensorRTVersions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetSupportedTensorRTVersions',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SupportedTensorRTVersionsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ReloadCategoryCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/ReloadCategoryCollection',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCategoryCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetCategoryCollection',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetCategoryCollectionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetErrorState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetErrorState',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.Empty.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetErrorStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SnapshotPredictImages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/SnapshotPredictImages',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SnapshotPredictImagesRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.SnapshotPredictImagesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetChipForPredictImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.runtime.proto.CVRuntimeService/GetChipForPredictImage',
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipForPredictImageRequest.SerializeToString,
            cv_dot_runtime_dot_proto_dot_cv__runtime__pb2.GetChipForPredictImageResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
