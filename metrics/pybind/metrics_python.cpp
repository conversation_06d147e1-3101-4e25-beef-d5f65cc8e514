#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "metrics/cpp/conclusion_counter.hpp"
#include "metrics/cpp/conclusion_types.hpp"
#include "metrics/cpp/daily_timezone.hpp"
#include "metrics/cpp/metrics_aggregator.hpp"
#include "metrics/cpp/p2p_accuracy.hpp"
#include "metrics/cpp/plant_conclusion_counter.hpp"
#include "metrics/cpp/spatial/spatial_block_owner.hpp"

namespace py = pybind11;

namespace carbon::metrics {

PYBIND11_MODULE(metrics_python, m) {
  py::class_<DailyTimezone, std::shared_ptr<DailyTimezone>>(m, "DailyTimezone")
      .def(py::init<bool>(), py::arg("owner") = false, py::call_guard<py::gil_scoped_release>())
      .def("get_day", &DailyTimezone::get_day, py::call_guard<py::gil_scoped_release>());

  py::enum_<ConclusionType>(m, "ConclusionType")
      .value("kNotWeeding", ConclusionType::kNotWeeding)
      .value("kOutOfBand", ConclusionType::kOutOfBand)
      .value("kIntersectsWithNonShootable", ConclusionType::kIntersectsWithNonShootable)
      .value("kOutOfRange", ConclusionType::kOutOfRange)
      .value("kUnimportant", ConclusionType::kUnimportant)
      .value("kNotShot", ConclusionType::kNotShot)
      .value("kPartiallyShot", ConclusionType::kPartiallyShot)
      .value("kShot", ConclusionType::kShot)
      .value("kP2PNotFound", ConclusionType::kP2PNotFound)
      .value("kError", ConclusionType::kError)
      .value("kFlicker", ConclusionType::kFlicker)
      .value("kMarkedForThinning", ConclusionType::kMarkedForThinning)
      .value("kNotTargeted", ConclusionType::kNotTargeted)
      .value("kP2PMissingContext", ConclusionType::kP2PMissingContext)
      .value("kConclusionTypeNumber", ConclusionType::kConclusionTypeNumber)
      .export_values();

  py::class_<ConclusionCounterFetcher, std::shared_ptr<ConclusionCounterFetcher>>(m, "ConclusionCounterFetcher");

  py::module_::import("ingest.pybind.ingest_python"); // Need the IngestClient object
  py::class_<MetricsAggregator, std::shared_ptr<MetricsAggregator>, ingest::IngestClient<>, ConclusionCounterFetcher>(
      m, "MetricsAggregator")
      .def_static("get", &MetricsAggregator::get, py::call_guard<py::gil_scoped_release>())
      .def("start", &MetricsAggregator::start, py::call_guard<py::gil_scoped_release>());

  py::class_<P2PAccuracy, std::unique_ptr<P2PAccuracy, py::nodelete>>(m, "P2PAccuracy")
      .def(py::init([]() { return std::unique_ptr<P2PAccuracy, py::nodelete>(&P2PAccuracy::get()); }))
      .def("add", &P2PAccuracy::add, py::arg("scanner_id"), py::arg("pan_offset"), py::arg("tilt_offset"),
           py::arg("num_p2p_matches"), py::call_guard<py::gil_scoped_release>());

  py::class_<SpatialBlockOwner, std::shared_ptr<SpatialBlockOwner>>(m, "SpatialBlockOwner")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def("register_client", &SpatialBlockOwner::register_client, py::call_guard<py::gil_scoped_release>())
      .def("beat", &SpatialBlockOwner::beat, py::arg("id"), py::call_guard<py::gil_scoped_release>())
      .def("ack", &SpatialBlockOwner::ack, py::arg("client_id"), py::arg("block_id"),
           py::call_guard<py::gil_scoped_release>())
      .def("can_close", &SpatialBlockOwner::can_close, py::arg("block_id"), py::call_guard<py::gil_scoped_release>());
}

} // namespace carbon::metrics