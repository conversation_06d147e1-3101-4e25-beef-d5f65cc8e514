import datetime
import enum
import json
import logging
import os
import time
from dataclasses import dataclass
from http import HTTPStatus
from typing import Any, Dict, List, Optional, Tuple, cast

import boto3
import pydantic
import requests
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential

from deeplearning.constants import DeepweedTrainingSubtype, Environment
from lib.common.s3_cache_proxy.client import S3CacheProxyClient
from lib.common.time import maka_control_timestamp_ms
from lib.common.veselka.ml_authenticator import AUTH0_TOKEN_CACHE_FILE, MlAuthenticator

logging.getLogger("botocore").setLevel(logging.INFO)

CLASS_RENAMES = {
    "baby-weed": "baby_weed",
    "leafy-weed": "leafy_weed",
    "london-rocket": "london_rocket",
}


class ExportType(enum.Enum):
    LABELING = 0
    REVIEW = 1
    ADMIN = 2


LOG = logging.getLogger(__name__)


def get_datapoint_json(datapoint: Dict[str, Any], formatted_points: List[Dict[str, Any]]) -> Dict[str, Any]:
    datapoint_json = {
        "version": 1,
        "certified": True,
        "username": os.getenv("USER"),
        "source": "veselka",
        "timestamp": datetime.datetime.utcnow().isoformat(timespec="seconds"),
        "md5sum": None,
        "points": formatted_points,
        "city": datapoint["city"],
        "image_id": datapoint["imageId"],
        "last_updated": datapoint.get("updated", None),
        "points_certified_categories": list(datapoint["categories"]["crownLabels"].keys()),
    }

    return datapoint_json


@dataclass(eq=True)
class Pipeline:
    id: str
    name: str
    data_source_crop_ids: List[str]
    deployment_crop_ids: List[str]
    custom_arguments: Dict[str, Any]


class DatasetV2(pydantic.BaseModel):
    id: str
    name: Optional[str]
    train: Optional[str]
    validation: Optional[str]
    test: Optional[str]
    s3_prefix: Optional[str] = None


PROD_AUTH0_DOMAIN = "carbonrobotics.us.auth0.com"


class VeselkaClient:
    def __init__(
        self, data_dir: Optional[str] = None, use_test: bool = True, num_workers: int = 4, use_carbon_cache: bool = True
    ) -> None:
        self._data_dir = data_dir
        self.use_test = use_test
        self.num_workers = num_workers
        self.auth0_domain = os.getenv("AUTH0_DOMAIN", "")
        self.ml_authenticator = MlAuthenticator(
            self.auth0_domain,
            os.getenv("AUTH0_CLIENT_ID", ""),
            os.getenv("AUTH0_CLIENT_SECRET", ""),
            token_cache_file=os.getenv("AUTH0_TOKEN_CACHE_FILE", AUTH0_TOKEN_CACHE_FILE),
        )

        if self.auth0_domain and self.auth0_domain != PROD_AUTH0_DOMAIN:
            if self.use_test:
                self.base_url = "https://veselka-test.cloud.carbonrobotics.com"
            else:
                self.base_url = "https://veselka-stg.cloud.carbonrobotics.com"
        else:
            self.base_url = "https://veselka.cloud.carbonrobotics.com"

        self.carbon_cache_host: Optional[str] = None
        if use_carbon_cache and not os.getenv("DISABLE_S3_CACHE_PROXY"):
            carbon_cache_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")
            if carbon_cache_host is None:
                self.carbon_cache_host = "storage1.dc.carbonrobotics.com"
            else:
                self.carbon_cache_host = carbon_cache_host

        self._s3_cache_proxy_client = S3CacheProxyClient(self.carbon_cache_host)

    @property
    def using_production(self) -> bool:
        return self.auth0_domain == PROD_AUTH0_DOMAIN

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def create_deepweed_dataset_v2(
        self,
        pipeline_id: str,
        start_timestamp: Optional[int] = None,
        stop_timestamp: Optional[int] = None,
        robot_ids: Optional[List[str]] = None,
        crop_ids: Optional[List[str]] = None,
        geohashes: Optional[List[str]] = None,
        excluded_robot_ids: Optional[List[str]] = None,
        excluded_crop_ids: Optional[List[str]] = None,
        limit: Optional[int] = None,
        train_geohashes: Optional[List[str]] = None,
        val_geohashes: Optional[List[str]] = None,
        test_geohashes: Optional[List[str]] = None,
        train_limit: Optional[int] = None,
        val_limit: Optional[int] = None,
        test_limit: Optional[int] = None,
        datapoints_limit: Optional[int] = None,
        splits: bool = True,
        parent_dataset_id: Optional[str] = None,
        only_new_data: bool = False,
        train_driptape_percentage: Optional[float] = None,
        val_driptape_percentage: Optional[float] = None,
        test_driptape_percentage: Optional[float] = None,
        balancing_keys: Optional[List[str]] = None,
        unlabeled_data: bool = False,
        name: Optional[str] = None,
        evaluation: bool = False,
        page_size: Optional[int] = None,
    ) -> str:
        url = f"{self.base_url}/ml/dataset_v2"

        data = {
            "pipeline_id": pipeline_id,
            "start_timestamp": start_timestamp,
            "stop_timestamp": stop_timestamp,
            "robot_ids": robot_ids,
            "crop_ids": crop_ids,
            "geohashes": geohashes,
            "excluded_robot_ids": excluded_robot_ids,
            "excluded_crop_ids": excluded_crop_ids,
            "limit": limit,
            "train_geohashes": train_geohashes,
            "val_geohashes": val_geohashes,
            "test_geohashes": test_geohashes,
            "train_limit": train_limit,
            "val_limit": val_limit,
            "test_limit": test_limit,
            "datapoints_limit": datapoints_limit,
            "splits": splits,
            "parent_dataset_id": parent_dataset_id,
            "only_new_data": only_new_data,
            "train_driptape_percentage": train_driptape_percentage,
            "val_driptape_percentage": val_driptape_percentage,
            "test_driptape_percentage": test_driptape_percentage,
            "balancing_keys": balancing_keys,
            "unlabeled_data": unlabeled_data,
            "name": name,
            "evaluation": evaluation,
        }

        if page_size is not None:
            data["page_size"] = page_size

        response = requests.post(url, json=data, auth=self.ml_authenticator)

        assert response.ok, response.text

        dataset_id: Optional[str] = response.json().get("id")

        assert dataset_id is not None, "Failed to get dataset ID"

        return dataset_id

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_dataset_v2(self, dataset_id: str, timeout: int = 240) -> Optional[DatasetV2]:
        url = f"{self.base_url}/ml/dataset_v2/{dataset_id}"

        start = time.time()
        under_time_limit = True
        first_request = True

        while under_time_limit:

            if not first_request:
                LOG.debug("Polling for dataset...")

            first_request = False

            response = requests.get(url, auth=self.ml_authenticator)

            assert response.ok, response.text

            if response.status_code == HTTPStatus.ACCEPTED and response.text == "Dataset creation pending":
                time.sleep(5)

            if response.status_code == HTTPStatus.OK:
                break

            if time.time() - start > timeout * 60:
                under_time_limit = False

        response_json = response.json()
        LOG.info(response_json)
        if "s3_prefix" not in response_json:
            response_json["s3_prefix"] = "s3://carbon-ml/datasets/" + dataset_id

        dataset: DatasetV2 = DatasetV2.model_validate(response_json)

        return dataset

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def download_dataset_v2(self, dataset: DatasetV2) -> None:
        assert self._data_dir is not None, "Data directory not set"

        dataset_splits = [
            dataset.train,
            dataset.validation,
            dataset.test,
        ]

        if dataset.test is not None and dataset.test.endswith(".jsonl"):
            dataset_splits.append("/".join(dataset.test.split("/")[:-1] + ["metadata.json"]))

        if dataset.s3_prefix is not None and not dataset.s3_prefix.startswith("s3://"):
            dataset.s3_prefix = f"s3://carbon-ml/{dataset.s3_prefix}"

        if not any(dataset_splits):
            dataset_splits = [
                f"{dataset.s3_prefix}/datapoints.jsonl",
                f"{dataset.s3_prefix}/metadata.json",
            ]

        for dataset_split in dataset_splits:
            if dataset_split is not None:
                filename = dataset_split.split("/")[-1]
                filepath = f"{self._data_dir}/datasets/{dataset.id}/{filename}"
                bucket, key = self._s3_cache_proxy_client.split_uri(dataset_split)
                LOG.info(f"Downloading {bucket}/{key} to {filepath}")
                self._s3_cache_proxy_client.download(bucket, key, filepath, exist_ok=True)

                try:
                    with open(filepath, "r") as f:
                        if filepath.endswith("jsonl"):
                            for line in f:
                                json.loads(line)
                        elif filepath.endswith("json"):
                            json.load(f)
                except Exception as e:
                    LOG.info(f"Failed to validate {dataset_split}- retrying download")
                    os.remove(filepath)
                    raise Exception(f"Failed to parse {filepath} as JSON: {e}")

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def create_deepweed_dataset(  # noqa: C901
        self,
        start_timestamp: Optional[int] = None,
        stop_timestamp: Optional[int] = None,
        crops: Optional[List[str]] = None,
        crop_ids: Optional[List[str]] = None,
        robots: Optional[List[str]] = None,
        parent_id: Optional[str] = None,
        fast_run: Optional[bool] = None,
        evaluation: Optional[bool] = None,
        balance_positive_classes: Optional[bool] = None,
        balance_robot_ids: Optional[bool] = None,
        balance_geohash: Optional[int] = None,
        exclude_robots: Optional[List[str]] = None,
        exclude_crop_ids: Optional[List[str]] = None,
        only_new_data: Optional[bool] = None,
        geohash: Optional[List[str]] = None,
        val_test_crop_ids: Optional[List[str]] = None,
        core_data_params: Optional[Dict[str, Any]] = None,
        test_set_geohashes: Optional[List[str]] = None,  # Limits the geohashes for just the test set
        train_set_size_limit: Optional[int] = None,
        validation_set_size_limit: Optional[int] = None,
        test_set_size_limit: Optional[int] = None,
        val_test_geohashes: Optional[List[str]] = None,  # Limits the geohashes for both the val and test sets
        driptape: Optional[bool] = None,
        train_no_driptape_keep_multiplier: Optional[int] = None,
        val_no_driptape_keep_multiplier: Optional[int] = None,
        test_no_driptape_keep_multiplier: Optional[int] = None,
    ) -> Any:

        data: Dict[str, Any] = {"create_async": True}

        if start_timestamp is not None:
            data["start_timestamp"] = start_timestamp

        if stop_timestamp is not None:
            data["stop_timestamp"] = stop_timestamp

        if crops is not None:
            if len(crops) > 0:
                data["crops"] = crops

        if crop_ids is not None:
            if len(crop_ids) > 0:
                data["crop_ids"] = crop_ids

        if robots is not None:
            if len(robots) > 0:
                data["robots"] = robots

        if parent_id is not None:
            data["parent_id"] = parent_id

        if fast_run is not None:
            data["fast_run"] = fast_run

        if evaluation is not None:
            data["evaluation"] = evaluation

        if balance_positive_classes is not None:
            data["balance_positive_classes"] = balance_positive_classes

        if balance_robot_ids is not None:
            data["balance_robot_ids"] = balance_robot_ids

        if balance_geohash is not None:
            data["balance_geohash"] = balance_geohash

        if exclude_robots is not None:
            data["exclude_robots"] = exclude_robots

        if exclude_crop_ids is not None:
            data["exclude_crop_ids"] = exclude_crop_ids

        if only_new_data is not None:
            data["only_new_data"] = only_new_data

        if geohash is not None:
            data["geohash"] = geohash

        if val_test_crop_ids is not None:
            data["val_test_crop_ids"] = val_test_crop_ids

        if core_data_params is not None:
            data["core_data_params"] = core_data_params

        if test_set_geohashes is not None:
            data["test_set_geohashes"] = test_set_geohashes

        if train_set_size_limit is not None:
            data["train_set_size_limit"] = train_set_size_limit

        if validation_set_size_limit is not None:
            data["validation_set_size_limit"] = validation_set_size_limit

        if test_set_size_limit is not None:
            data["test_set_size_limit"] = test_set_size_limit

        if val_test_geohashes is not None:
            data["val_test_geohashes"] = val_test_geohashes

        if driptape is not None:
            data["driptape"] = driptape

        if train_no_driptape_keep_multiplier is not None:
            data["train_no_driptape_keep_multiplier"] = train_no_driptape_keep_multiplier

        if val_no_driptape_keep_multiplier is not None:
            data["val_no_driptape_keep_multiplier"] = val_no_driptape_keep_multiplier

        if test_no_driptape_keep_multiplier is not None:
            data["test_no_driptape_keep_multiplier"] = test_no_driptape_keep_multiplier

        LOG.info(f"Creating dataset with params: {json.dumps(data, indent=4)}")
        response = requests.post(url=f"{self.base_url}/ml/dataset", json=data, auth=self.ml_authenticator)

        assert response.ok, response.text

        dataset_info = response.json()
        dataset_id = dataset_info["id"]

        wait_for_export = True
        max_backoff_seconds = 600
        backoff_seconds = 10
        while wait_for_export:
            response = requests.get(f"{self.base_url}/ml/dataset?id={dataset_id}", auth=self.ml_authenticator)
            assert response.ok, f"{response.status_code}: {response.text}"
            if response.status_code == 200:
                wait_for_export = False
                dataset_info = response.json()
            elif response.status_code == 202:
                time.sleep(backoff_seconds)
                backoff_seconds = backoff_seconds * 2
                if backoff_seconds > max_backoff_seconds:
                    backoff_seconds = max_backoff_seconds
            else:
                LOG.info(f"Failed to get dataset with id {dataset_id}: {response.text}")
                assert False

        return dataset_info

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_dataset(self, dataset_id: str) -> Any:
        response = requests.get(f"{self.base_url}/ml/dataset?id={dataset_id}", auth=self.ml_authenticator)
        assert response.ok, f"response.status_code={response.status_code}, response.text={response.text}"
        dataset_info = response.json()
        return dataset_info

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def create_furrows_dataset(self) -> str:
        data = {"name": f"dataset - {maka_control_timestamp_ms()}"}
        response = requests.post(url=f"{self.base_url}/ml/furrow/dataset/job", json=data, auth=self.ml_authenticator)
        assert response.ok, response.text

        job_id = response.json()["job_id"]

        max_backoff_seconds = 600
        backoff_seconds = 10
        while True:
            response = requests.get(f"{self.base_url}/ml/furrow/dataset/job/{job_id}", auth=self.ml_authenticator)
            assert response.ok, f"{response.status_code}: {response.text}"
            response_json = response.json()
            status = response_json.get("status", "failed")
            if status == "success":
                return str(response_json["dataset_id"])
            elif status == "pending":
                time.sleep(backoff_seconds)
                backoff_seconds = backoff_seconds * 2
                if backoff_seconds > max_backoff_seconds:
                    backoff_seconds = max_backoff_seconds
            else:
                failure_message = response_json.get("message")
                LOG.info(f"Failed to get dataset with job id {job_id}: {failure_message}")
                break

        assert False

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_pipeline(self, pipeline_id: str) -> Any:
        response = requests.get(f"{self.base_url}/ml/pipeline?id={pipeline_id}", auth=self.ml_authenticator)
        assert response.ok, f"response.status_code={response.status_code}, response.text={response.text}"
        response_json = response.json()
        pipeline = Pipeline(
            id=response_json.get("id"),
            name=response_json.get("name"),
            data_source_crop_ids=response_json.get("data_sources_crop_ids"),
            deployment_crop_ids=response_json.get("deployments_crop_ids"),
            custom_arguments=response_json.get("custom_arguments", {}),
        )
        return pipeline

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_model_info(self, model_id: str) -> Any:
        response = requests.get(f"{self.base_url}/ml/model/{model_id}", auth=self.ml_authenticator)
        assert response.ok, f"response.status_code={response.status_code}, response.text={response.text}"
        model_info = response.json()
        return model_info

    def get_pretrain_version(
        self,
        container_version: Optional[str] = None,
        environment: Optional[str] = None,
        fast_run: Optional[bool] = None,
    ) -> Tuple[str, str]:
        v, m = self.get_parent_version(
            "pretrain", container_version=container_version, environment=environment, fast_run=fast_run
        )
        assert v is not None and m is not None, "No parent version found"
        return v, m

    def get_full_train_version(
        self,
        pipeline_id: str,
        container_version: Optional[str] = None,
        environment: Optional[str] = None,
        deploy: Optional[bool] = None,
        fast_run: Optional[bool] = None,
    ) -> Tuple[str, str]:
        v, m = self.get_parent_version(
            "full_train",
            pipeline_id=pipeline_id,
            container_version=container_version,
            environment=environment,
            deploy=deploy,
            fast_run=fast_run,
        )
        assert v is not None and m is not None, "No parent version found"
        return v, m

    def get_fine_tune_version(
        self,
        pipeline_id: str,
        container_version: Optional[str] = None,
        environment: Optional[str] = None,
        deploy: Optional[bool] = None,
        fast_run: Optional[bool] = None,
    ) -> Tuple[str, str]:
        v, m = self.get_parent_version(
            "fine_tune",
            pipeline_id=pipeline_id,
            container_version=container_version,
            environment=environment,
            deploy=deploy,
            fast_run=fast_run,
        )
        assert v is not None and m is not None, "No parent version found"
        return v, m

    def get_geo_fine_tune_version(
        self,
        pipeline_id: str,
        geohash: str,
        container_version: Optional[str] = None,
        environment: Optional[str] = None,
        deploy: Optional[bool] = None,
        fast_run: Optional[bool] = None,
    ) -> Tuple[str, str]:
        v, m = self.get_parent_version(
            "geo_fine_tune",
            pipeline_id=pipeline_id,
            container_version=container_version,
            environment=environment,
            deploy=deploy,
            geohash=geohash,
            fast_run=fast_run,
        )
        assert v is not None and m is not None, "No parent version found"
        return v, m

    def get_driptape_version(
        self,
        container_version: Optional[str] = None,
        min_test_oec: Optional[float] = None,
        environment: Optional[str] = None,
        deploy: bool = True,
        fast_run: Optional[bool] = None,
    ) -> Tuple[str, str]:
        v, m = self.get_parent_version(
            "driptape",
            container_version=container_version,
            min_test_oec=min_test_oec,
            environment=environment,
            deploy=deploy,
            fast_run=fast_run,
        )
        assert v is not None and m is not None, "No parent version found"
        return v, m

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_parent_version(
        self,
        sub_type: str,
        pipeline_id: Optional[str] = None,
        container_version: Optional[str] = None,
        min_test_oec: Optional[float] = None,
        environment: Optional[str] = None,
        deploy: Optional[bool] = None,
        geohash: Optional[str] = None,
        fast_run: Optional[bool] = None,
    ) -> Tuple[Optional[str], Optional[str]]:
        params = {
            "target": container_version,
            "min_test_oec": min_test_oec,
            "environment": environment,
            "fast_run": fast_run,
            "deploy": deploy,
            "geohash": geohash,
            "pipeline": pipeline_id,
            "sub_type": sub_type,
        }
        response = requests.get(
            f"{self.base_url}/ml/modelsvc/parent_versions", params=params, auth=self.ml_authenticator,
        )
        if response.status_code == requests.codes.not_found:
            return (None, None)

        assert response.ok, response.text

        for v, m in response.json().items():
            return (v, m)
        assert False, response.text

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_embedding_balancing_model(
        self,
        container_version: str,
        pipeline_id: str,
        sub_type: DeepweedTrainingSubtype,
        environment: Environment,
        embedding_version: int,
        deploy: bool = True,
    ) -> Optional[Dict[str, Any]]:
        response = requests.get(
            f"{self.base_url}/ml/embedding_model?container_version={container_version}&pipeline_id={pipeline_id}&environment={environment.name.lower()}&deploy={deploy}&sub_type={sub_type.name.lower()}&embedding_version={embedding_version}",
            auth=self.ml_authenticator,
        )
        if response.status_code == 404:
            return None

        assert response.ok, response.text
        return cast(Dict[str, Any], response.json())

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def download_dataset(self, dataset_info: Dict[str, Any]) -> None:
        dataset_id = dataset_info["id"]

        data_dir = f"/data/deeplearning/datasets/{dataset_id}"
        os.makedirs(data_dir, exist_ok=True)

        for split in ["train", "validation", "test"]:
            s3_path = dataset_info[split]
            split_s3_path = s3_path.split("/", maxsplit=3)
            if len(split_s3_path) == 4:  # Ex: ['s3:', '', 'maka-pono', 'media/test.png']
                bucket = split_s3_path[2]
                s3_filepath = split_s3_path[3]
            else:
                bucket = "carbon-ml"
                s3_filepath = s3_path
            local_filepath = os.path.join(data_dir, os.path.basename(s3_filepath))

            if os.path.exists(local_filepath):
                continue

            if self.carbon_cache_host is not None:
                LOG.info(f"Downloading file from Carbon Cache: {local_filepath}")
                try:
                    response = requests.get(f"http://{self.carbon_cache_host}/{bucket}/{s3_filepath}", timeout=30)
                    assert response.ok, f"{response.status_code}: {response.text}"
                except Exception as e:
                    LOG.info(f"Request to carbon cache failed: {e}. Retrying.")
                    response = requests.get(f"http://{self.carbon_cache_host}/{bucket}/{s3_filepath}", timeout=30)

                assert response.ok, f"{response.status_code}: {response.text}"

                with open(local_filepath, "wb") as f:
                    f.write(response.content)
            else:
                LOG.info(f"Downloading file from S3: {local_filepath}")
                boto3.resource("s3").Bucket(bucket).download_file(s3_filepath, local_filepath)

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def trigger_metrics_generation(self, model_id: str) -> None:

        data = {"model_id": model_id}
        response = requests.get(f"{self.base_url}/ml/modelsvc/run_metrics", json=data, auth=self.ml_authenticator)

        if response.status_code != 200:
            raise RuntimeError(f"Bad response from server: {response.status_code}, {response.text}")

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_evaluations_by_model_id(self, model_id: str) -> List[Dict[str, Any]]:
        response = requests.get(f"{self.base_url}/ml/evaluation?model_id={model_id}", auth=self.ml_authenticator)
        assert response.ok, response.text
        evaluations = response.json()
        return cast(List[Dict[str, Any]], evaluations)

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def create_evaluation(self, payload: Dict[str, Any]) -> None:
        response = requests.post(f"{self.base_url}/ml/evaluation", auth=self.ml_authenticator, json=payload)
        assert response.ok, response.text

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def post_model(self, payload: Dict[str, Any]) -> None:
        response = requests.post(f"{self.base_url}/ml/model", auth=self.ml_authenticator, json=payload)
        assert response.ok, response.text

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def post_model_artifact(
        self,
        model_id: str,
        tensorrt_version: str,
        compute_capability: str,
        url: Optional[str] = None,
        checksum: Optional[str] = None,
        test_results: Optional[Dict[str, Any]] = None,
    ) -> None:
        payload = {
            "model_id": model_id,
            "tensorrt_version": tensorrt_version,
            "compute_capability": compute_capability,
            "test_results_json": json.dumps(test_results),
        }
        if url is not None:
            payload["url"] = url
        if checksum is not None:
            payload["checksum"] = checksum
        response = requests.post(f"{self.base_url}/ml/model_artifact", auth=self.ml_authenticator, json=payload)
        assert response.ok, response.text

    def get_image_ids_to_meta(
        self, image_ids: List[str]
    ) -> Tuple[Dict[str, str], Dict[str, Optional[str]], Dict[str, Tuple[int, int]]]:
        data: Dict[str, Any] = {"image_ids": image_ids}
        response = requests.post(url=f"{self.base_url}/ml/images/ids_to_meta", json=data, auth=self.ml_authenticator)

        assert response.ok, response.text

        response_json = response.json()
        image_ids_to_urls: Dict[str, str] = {key: value["url"] for key, value in response_json["images"].items()}
        image_ids_to_geo: Dict[str, Optional[str]] = {
            key: value["geohash"] for key, value in response_json["images"].items()
        }

        image_ids_to_height_width: Dict[str, Tuple[int, int]] = {
            key: (value["height"], value["width"]) for key, value in response_json["images"].items()
        }

        return image_ids_to_urls, image_ids_to_geo, image_ids_to_height_width

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_production_comparison_model(self) -> Dict[str, str]:
        endpoint = f"{self.base_url}/ml/production_comparison"
        production_comparison_model_response = requests.get(endpoint, auth=self.ml_authenticator,)
        assert production_comparison_model_response.ok, production_comparison_model_response.status_code
        production_comparison_json = cast(Dict[str, str], production_comparison_model_response.json())

        return production_comparison_json

    def get_all_crop_metadata(self) -> Any:
        response = requests.get(f"{self.base_url}/ml/crops", auth=self.ml_authenticator)
        assert response.ok, response.text
        return response.json()

    def get_point_categories(self) -> Any:
        response = requests.get(f"{self.base_url}/ml/point_categories", auth=self.ml_authenticator)
        assert response.ok, response.text
        return response.json()

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def post_comparison_labeling_dataset(self, payload: Dict[str, Any]) -> None:
        response = requests.post(
            f"{self.base_url}/ml/comparison_labeling_dataset", auth=self.ml_authenticator, json=payload
        )
        assert response.ok, response.text

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_comparison_labeling_dataset(self,) -> Dict[str, Any]:
        response = requests.get(
            f"{self.base_url}/ml/comparison_labeling_dataset?valid=true", auth=self.ml_authenticator,
        )
        assert response.ok, response.status_code
        comparison_labeling_dataset_json = cast(Dict[str, Any], response.json())

        return comparison_labeling_dataset_json
