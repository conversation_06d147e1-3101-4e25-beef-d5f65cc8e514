package simulation

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/config_service"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/recorder"
	"github.com/carbonrobotics/robot/golang/generated/proto/sim_UI"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/types"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

const (
	DIAGNOSTIC_BASE_PATH = "/data/diagnostics/%v/row%v/%v"
)

type DeepweedFrames struct {
	Frames []*recorder.DeepweedPredictionRecord
}

func (f *DeepweedFrames) GetInitialTimestamp() int64 {
	if len(f.Frames) > 0 {
		return f.Frames[0].Frame.TimestampMs
	}
	return 0
}

func LoadDeepweedFrames(path string) (*DeepweedFrames, error) {
	frames := &DeepweedFrames{}
	fr, err := state.NewProtobufFileReader(path, "DeepweedPredictionRecord")
	if err != nil {
		return nil, fmt.Errorf("Error opening input file %v: %v", path, err)
	}

	var prevTs int64 = 0

	for i := 0; i < fr.NumRecords(); i++ {
		recordBytes, err := fr.ReadRecord(uint32(i))
		if err != nil {
			return nil, fmt.Errorf("Corrupt input file %v on record %v: %v", path, i, err)
		}
		record := recorder.DeepweedPredictionRecord{}
		proto.Unmarshal(recordBytes, &record)

		delta := record.Frame.TimestampMs - prevTs
		if delta > 500 {
			logrus.Warnf("Loading %v Delta at %v of %v", path, i, delta)
		}
		prevTs = record.Frame.TimestampMs

		frames.Frames = append(frames.Frames, &record)
	}

	return frames, nil
}

type RotaryEncoderFrames struct {
	Frames []*recorder.RotaryTicksRecord
}

func (f *RotaryEncoderFrames) GetInitialTimestamp() int64 {
	if len(f.Frames) > 0 {
		return int64(f.Frames[0].Snapshot.TimestampUs / 1000)
	}
	return 0
}

func LoadRotaryEncoderFrames(path string) (*RotaryEncoderFrames, error) {
	frames := &RotaryEncoderFrames{}
	fr, err := state.NewProtobufFileReader(path, "RotaryTicksRecord")
	if err != nil {
		return nil, fmt.Errorf("Error opening input file %v: %v", path, err)
	}

	for i := 0; i < fr.NumRecords(); i++ {
		recordBytes, err := fr.ReadRecord(uint32(i))
		if err != nil {
			return nil, fmt.Errorf("Corrupt input file %v on record %v: %v", path, i, err)
		}
		record := recorder.RotaryTicksRecord{}
		proto.Unmarshal(recordBytes, &record)

		frames.Frames = append(frames.Frames, &record)
	}

	return frames, nil
}

type CarbonRecordReplayer struct {
	sync.Mutex
	cond                      *sync.Cond
	ctx                       context.Context
	cancelFunc                context.CancelFunc
	terminated                bool
	timeDelta                 int64
	timeDeltaChannel          chan int64
	latestTimestampMs         int64
	setActiveFrameFunc        func(timestampMs int64, index int)
	getTimestampMsByIndexFunc func(index int) int64
	numFrames                 int
	readyForLoopChannel       chan bool
}

func NewCarbonRecordReplayer(timeDelta int64, setActiveFrameFunc func(timestampMs int64, index int), getTimestampMsByIndexFunc func(index int) int64, numFrames int) *CarbonRecordReplayer {
	ctx, cancelFunc := context.WithCancel(context.Background())
	replayer := &CarbonRecordReplayer{
		ctx:                       ctx,
		timeDelta:                 timeDelta,
		timeDeltaChannel:          make(chan int64),
		cancelFunc:                cancelFunc,
		setActiveFrameFunc:        setActiveFrameFunc,
		getTimestampMsByIndexFunc: getTimestampMsByIndexFunc,
		numFrames:                 numFrames,
		readyForLoopChannel:       make(chan bool),
		terminated:                false,
	}
	replayer.cond = sync.NewCond(replayer)
	return replayer
}

func (r *CarbonRecordReplayer) ReadOnNext(timestampMs int64, readOnNext func()) error {
	r.Lock()
	defer r.Unlock()

	for !r.terminated && r.latestTimestampMs <= timestampMs {
		r.cond.Wait()
	}

	if r.terminated {
		return fmt.Errorf("Replayer Terminated")
	}

	readOnNext()

	return nil
}

func (r *CarbonRecordReplayer) Notify() {
	r.cond.Broadcast()
}

type CarbonRecordReplayerInterface interface {
	Stop()
	SetNewTimeDelta(timeDelta int64)
	Play()
	ReadyForLoopChannel() chan bool
}

type CarbonRecordReplayerWrapper struct {
	carbonReplayer *CarbonRecordReplayer
}

func (r *CarbonRecordReplayerWrapper) Stop() {
	r.carbonReplayer.cancelFunc()
}

func (r *CarbonRecordReplayerWrapper) SetNewTimeDelta(timeDelta int64) {
	r.carbonReplayer.timeDeltaChannel <- timeDelta
}

func (r *CarbonRecordReplayerWrapper) isTerminated() bool {
	r.carbonReplayer.Lock()
	defer r.carbonReplayer.Unlock()
	return r.carbonReplayer.terminated
}

func (r *CarbonRecordReplayerWrapper) setTerminated() {
	r.carbonReplayer.Lock()
	defer r.carbonReplayer.Unlock()
	r.carbonReplayer.terminated = true
}

func (r *CarbonRecordReplayerWrapper) setNewTimeDelta(timeDelta int64) {
	r.carbonReplayer.Lock()
	defer r.carbonReplayer.Unlock()
	r.carbonReplayer.timeDelta = timeDelta
}

func (r *CarbonRecordReplayerWrapper) getTimeDelta() int64 {
	r.carbonReplayer.Lock()
	defer r.carbonReplayer.Unlock()
	return r.carbonReplayer.timeDelta
}

func (r *CarbonRecordReplayerWrapper) selectSleep(timestampMs int64) bool {
	select {
	case newTimeDelta := <-r.carbonReplayer.timeDeltaChannel:
		r.setNewTimeDelta(newTimeDelta)
		return false
	case <-r.carbonReplayer.ctx.Done():
		r.setTerminated()
		return false
	case <-time.After(time.Until(time.UnixMilli(timestampMs))):
		return true
	}
}

func (r *CarbonRecordReplayerWrapper) setActiveFrame(timestampMs int64, index int) {
	r.carbonReplayer.Lock()
	defer r.carbonReplayer.Unlock()
	r.carbonReplayer.latestTimestampMs = timestampMs
	r.carbonReplayer.setActiveFrameFunc(timestampMs, index)
	r.carbonReplayer.Notify()
}

func (r *CarbonRecordReplayerWrapper) ReadyForLoopChannel() chan bool {
	return r.carbonReplayer.readyForLoopChannel
}

func (r *CarbonRecordReplayerWrapper) Play() {
	for {

		reloop := false
		timeDelta := r.getTimeDelta()

		for timeDelta == 0 {
			if !r.selectSleep(time.Now().UnixMilli() + 1000) {
				if r.isTerminated() {
					return
				} else {
					reloop = true
					break
				}
			}
		}

		if reloop {
			continue
		}

		// Here we just got a new time delta to start the recording with
		var averageFrameDeltaMs int64 = 180 // TODO actually compute this value
		for i := 0; i < r.carbonReplayer.numFrames; i++ {
			frameTime := r.carbonReplayer.getTimestampMsByIndexFunc(i) + timeDelta
			currentTime := time.Now().UnixMilli()
			if frameTime > currentTime {
				if !r.selectSleep(frameTime) { // TODO should we pump blanks if timeDelta has large delay?
					if r.isTerminated() {
						return
					} else {
						reloop = true
						break
					}
				}
			}

			r.setActiveFrame(frameTime, i)
		}

		if reloop {
			continue
		}

		r.carbonReplayer.readyForLoopChannel <- true

		// Here we produce blank frames in between
		for {
			frameTime := time.Now().UnixMilli() + averageFrameDeltaMs
			if !r.selectSleep(frameTime) {
				if r.isTerminated() {
					return
				} else {
					break
				}
			}
			r.setActiveFrame(frameTime, -1)
		}
	}

}

type DeepweedReplayer struct {
	CarbonRecordReplayerWrapper
	frames                *DeepweedFrames
	currentFrame          *recorder.DeepweedPredictionFrame
	lastGetTimestampMs    int64
	lastReturnTimestampMs int64
}

func NewDeepweedReplayer(frames *DeepweedFrames, timeDelta int64) *DeepweedReplayer {
	replayer := &DeepweedReplayer{
		frames: frames,
		currentFrame: &recorder.DeepweedPredictionFrame{
			TimestampMs: 0,
			Detections:  []*recorder.DeepweedDetection{},
		},
		lastGetTimestampMs:    0,
		lastReturnTimestampMs: 0,
	}
	replayer.carbonReplayer = NewCarbonRecordReplayer(timeDelta, replayer.setActiveFrame, replayer.getTimestampMsByIndex, len(frames.Frames))
	return replayer
}

func (r *DeepweedReplayer) GetNext(timestampMs int64) (*recorder.DeepweedPredictionFrame, error) {
	var frame *recorder.DeepweedPredictionFrame = nil

	curTimestampMs := time.Now().UnixMilli()
	delta := curTimestampMs - r.lastGetTimestampMs
	if delta > 500 {
		logrus.Debugf("Deepweed Get Delta Above 500: %v", delta)
	}
	r.lastGetTimestampMs = curTimestampMs
	err := r.carbonReplayer.ReadOnNext(timestampMs, func() {
		frame = r.currentFrame
	})

	if err != nil {
		return nil, err
	}

	curTimestampMs = time.Now().UnixMilli()
	delta = curTimestampMs - r.lastReturnTimestampMs
	if delta > 500 {
		logrus.Debugf("Deepweed Return Delta Above 500: %v", delta)
	}
	r.lastReturnTimestampMs = curTimestampMs
	r.lastGetTimestampMs = curTimestampMs

	return frame, nil
}

func (r *DeepweedReplayer) setActiveFrame(timestampMs int64, index int) {
	if index < 0 || index >= len(r.frames.Frames) {
		r.currentFrame = &recorder.DeepweedPredictionFrame{
			TimestampMs: timestampMs,
			Detections:  []*recorder.DeepweedDetection{},
		}
	} else {
		r.currentFrame = &recorder.DeepweedPredictionFrame{
			TimestampMs: timestampMs,
			Detections:  r.frames.Frames[index].Frame.Detections,
		}
	}
}

func (r *DeepweedReplayer) getTimestampMsByIndex(index int) int64 {
	if index < 0 || index >= len(r.frames.Frames) {
		return 0
	} else {
		return r.frames.Frames[index].Frame.TimestampMs
	}
}

type RotaryReplayer struct {
	CarbonRecordReplayerWrapper
	frames       *RotaryEncoderFrames
	currentFrame *recorder.RotaryTicksSnapshot
	offset       *recorder.RotaryTicksSnapshot
}

func NewRotaryReplayer(frames *RotaryEncoderFrames, timeDelta int64) *RotaryReplayer {
	replayer := &RotaryReplayer{
		frames:       frames,
		currentFrame: &recorder.RotaryTicksSnapshot{},
		offset:       &recorder.RotaryTicksSnapshot{},
	}
	replayer.carbonReplayer = NewCarbonRecordReplayer(timeDelta, replayer.setActiveFrame, replayer.getTimestampMsByIndex, len(frames.Frames))
	return replayer
}

func (r *RotaryReplayer) GetNext(timestampMs int64) (*recorder.RotaryTicksSnapshot, error) {
	var frame *recorder.RotaryTicksSnapshot = nil
	err := r.carbonReplayer.ReadOnNext(timestampMs, func() {
		frame = r.currentFrame
	})

	if err != nil {
		return nil, err
	}

	return frame, nil
}

func (r *RotaryReplayer) setActiveFrame(timestampMs int64, index int) {
	// TODO consider doing ticks offset?
	if index < 0 || index >= len(r.frames.Frames) {
		r.currentFrame = &recorder.RotaryTicksSnapshot{
			TimestampUs: uint64(timestampMs) * 1000,
			// TODO sim velocity here
		}
	} else {
		frame := r.frames.Frames[index].Snapshot
		r.currentFrame = &recorder.RotaryTicksSnapshot{
			TimestampUs: uint64(timestampMs) * 1000,
			Fr:          frame.Fr,
			Fl:          frame.Fl,
			Bl:          frame.Bl,
			Br:          frame.Br,
			FrEnabled:   frame.FrEnabled,
			FlEnabled:   frame.FlEnabled,
			BlEnabled:   frame.BlEnabled,
			BrEnabled:   frame.BrEnabled,
		}
	}
}

func (r *RotaryReplayer) getTimestampMsByIndex(index int) int64 {
	if index < 0 || index >= len(r.frames.Frames) {
		return 0
	} else {
		return int64(r.frames.Frames[index].Snapshot.TimestampUs / 1000)
	}
}

type DiagnosticState struct {
	lock              sync.RWMutex // lock for all members in the DiagnosticState
	row               int
	currentDiagnostic string
	ticket            int
	diagnosticCh      chan struct{}
}

func NewDiagnosticState(currentDiagnostic string, row int) *DiagnosticState {
	diagnosticState := &DiagnosticState{
		currentDiagnostic: currentDiagnostic,
		row:               row,
		ticket:            1,
		diagnosticCh:      make(chan struct{}),
	}
	return diagnosticState
}

func (d *DiagnosticState) GetDiagnosticState() (int, string, int) {
	d.lock.RLock()
	defer d.lock.RUnlock()
	return d.row, d.currentDiagnostic, d.ticket
}

type RecordingReplayer struct {
	diagnosticState    *DiagnosticState
	mode               *atomic.Value
	PredictFrames      []*DeepweedFrames
	RotaryFrames       *RotaryEncoderFrames
	InitialTimestampMs int64
	rotaryReplayer     *RotaryReplayer
	deepweedReplayers  []*DeepweedReplayer

	Lock sync.Mutex

	stopChannel chan bool
}

func NewRecordingReplayer(currentDiagnostic string, row int) (*RecordingReplayer, error) {
	diagnosticState := NewDiagnosticState(currentDiagnostic, row)
	if diagnosticState.currentDiagnostic == "" {
		return nil, fmt.Errorf("Diagnostic Name is empty")
	}
	replayer := &RecordingReplayer{
		diagnosticState: diagnosticState,
		stopChannel:     make(chan bool),
	}

	err := replayer.Reload()
	if err != nil {
		logrus.WithError(err).Error("Error Reloading Recording Replayer")
		return nil, err
	}

	return replayer, nil
}

func (r *RecordingReplayer) SetMode(mode *atomic.Value) {
	r.mode = mode
}

func (r *RecordingReplayer) GetNextRotary(timestampMs int64) (*recorder.RotaryTicksSnapshot, error) {
	return r.rotaryReplayer.GetNext(timestampMs)
}

func (r *RecordingReplayer) GetNextDeepweed(camId int, timestampMs int64) (*recorder.DeepweedPredictionFrame, error) {
	if camId < 1 || camId > len(r.deepweedReplayers) {
		return nil, fmt.Errorf("CamId: %v larger than number of deepweed replayers: %v", camId, len(r.deepweedReplayers))
	}

	return r.deepweedReplayers[camId-1].GetNext(timestampMs)
}

func (r *RecordingReplayer) GetReplayerDiagnosticState() *DiagnosticState {
	return r.diagnosticState
}

func (r *RecordingReplayer) GetNextDiagnosticState(ctx context.Context, req *sim_UI.GetDiagnosticSettingsRequest) (*sim_UI.DiagnosticSettings, error) {
	for {
		r.diagnosticState.lock.RLock()
		ch := r.diagnosticState.diagnosticCh
		if r.diagnosticState.ticket <= int(req.Ticket) {
			r.diagnosticState.lock.RUnlock()
		} else {
			break
		}
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ch:
			continue
		}
	}
	defer r.diagnosticState.lock.RUnlock()
	currentRow, currentDiagnostic, ticket := r.diagnosticState.GetDiagnosticState()

	diagnostics := []string{}
	diagnosticsPath := filepath.Join("/", "data", "diagnostics")
	if entries, err := os.ReadDir(diagnosticsPath); err == nil {
		for _, entry := range entries {
			diagnostics = append(diagnostics, entry.Name())
		}
	} else {
		logrus.Errorf("gRPC webserver: Diagnostics directory not found at %s", diagnosticsPath)
	}

	return &sim_UI.DiagnosticSettings{
		Row:               int64(currentRow),
		Diagnostics:       diagnostics,
		CurrentDiagnostic: currentDiagnostic,
		Ticket:            int64(ticket),
	}, nil
}

func (r *RecordingReplayer) UpdateDiagnosticState() {
	r.diagnosticState.lock.Lock()
	defer r.diagnosticState.lock.Unlock()
	close(r.diagnosticState.diagnosticCh)
	r.diagnosticState.diagnosticCh = make(chan struct{})
	r.diagnosticState.ticket = r.diagnosticState.ticket + 1
}

func (r *RecordingReplayer) Stop() {
	r.stopChannel <- true
}

func (r *RecordingReplayer) Reload() error {
	logrus.Info("Reloading Recording Replayer")

	row, currentDiagnostic, _ := r.diagnosticState.GetDiagnosticState()
	if currentDiagnostic == "" {
		return fmt.Errorf("DiagnosticName is empty")
	}
	initialTimestamps := []int64{}
	r.PredictFrames = []*DeepweedFrames{}
	for predictNum := 1; predictNum <= 4; predictNum++ {
		predictPath := fmt.Sprintf(DIAGNOSTIC_BASE_PATH, currentDiagnostic, row, fmt.Sprintf("predict%v_deepweed.carbon", predictNum))
		frames, err := LoadDeepweedFrames(predictPath)

		if err != nil {
			return err
		}

		initialTimestamps = append(initialTimestamps, frames.GetInitialTimestamp())
		r.PredictFrames = append(r.PredictFrames, frames)
	}

	rotaryPath := fmt.Sprintf(DIAGNOSTIC_BASE_PATH, currentDiagnostic, row, "wheel_encoder.carbon")
	frames, err := LoadRotaryEncoderFrames(rotaryPath)

	if err != nil {
		return err
	}

	r.RotaryFrames = frames

	initialTimestamps = append(initialTimestamps, frames.GetInitialTimestamp())
	sort.SliceStable(initialTimestamps, func(i, j int) bool { return initialTimestamps[i] < initialTimestamps[j] })

	if len(initialTimestamps) == 0 {
		return fmt.Errorf("For Some Reason initial timestamps has no entry")
	}
	r.InitialTimestampMs = initialTimestamps[0]

	r.rotaryReplayer = NewRotaryReplayer(r.RotaryFrames, 0)
	r.deepweedReplayers = []*DeepweedReplayer{}

	for _, frames := range r.PredictFrames {
		r.deepweedReplayers = append(r.deepweedReplayers, NewDeepweedReplayer(frames, 0))
	}
	return nil
}

func (r *RecordingReplayer) Play() error {
	logrus.Info("Starting Recording Replayer")
	replayers := []CarbonRecordReplayerInterface{}
	replayers = append(replayers, r.rotaryReplayer)
	for _, replayer := range r.deepweedReplayers {
		replayers = append(replayers, replayer)
	}

	timeDelta := (time.Now().UnixMilli() + 1000) - r.InitialTimestampMs

	wg := sync.WaitGroup{}
	wg.Add(len(replayers))

	wg.Add(1)

	for _, replayer := range replayers {
		go func(replayer2 CarbonRecordReplayerInterface) {
			defer wg.Done()
			replayer2.Play()
		}(replayer)
		replayer.SetNewTimeDelta(timeDelta)
	}

	wgChan := make(chan bool)

	go func() {
		wg.Wait()
		wgChan <- true
	}()

	cancelReloopChannel := make(chan bool)
	go func() {
		defer wg.Done()

		set := []reflect.SelectCase{}
		set = append(set, reflect.SelectCase{
			Dir:  reflect.SelectRecv,
			Chan: reflect.ValueOf(cancelReloopChannel),
		})
		for _, replayer := range replayers {
			set = append(set, reflect.SelectCase{
				Dir:  reflect.SelectRecv,
				Chan: reflect.ValueOf(replayer.ReadyForLoopChannel()),
			})
		}

		readyForLoopCount := 0
		for {
			index, _, _ := reflect.Select(set)
			if index == 0 {
				return
			}

			readyForLoopCount += 1

			if readyForLoopCount == len(replayers) {
				newTimeDelta := (time.Now().UnixMilli() + 50) - r.InitialTimestampMs
				for _, replayer := range replayers {
					replayer.SetNewTimeDelta(newTimeDelta)
				}

				readyForLoopCount = 0
			}
		}
	}()

	for {
		select {
		case <-wgChan:
			return nil
		case <-r.stopChannel:
			for _, replayer := range replayers {
				replayer.Stop()
				cancelReloopChannel <- true
			}
		}
	}
}

func (r *RecordingReplayer) SyncConfigs() error {
	if r.mode.Load() == types.Generate {
		return nil
	}

	_, currentDiagnostic, _ := r.diagnosticState.GetDiagnosticState()
	staticDataFile, err := os.ReadFile(fmt.Sprintf("/data/diagnostics/%v/static_data.carbon", currentDiagnostic))
	if err != nil {
		return err
	}

	staticData := &frontend.StaticRecordingData{}
	proto.Unmarshal(staticDataFile, staticData)

	blacklist_file, err := os.Open("/robot/golang/simulator/configs_blacklist")
	if err != nil {
		return err
	}
	defer blacklist_file.Close()

	var blacklist []string
	scanner := bufio.NewScanner(blacklist_file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if len(line) > 0 {
			blacklist = append(blacklist, line)
			logrus.Infof("Blacklist config: %v", line)
		}
	}

	configClient := config.NewConfigClient(config.MakeRobotLocalAddr(61001))

	r.setConfigValues(staticData.RootConfig, "", blacklist, configClient)

	return nil
}

func (r *RecordingReplayer) setConfigValues(node *frontend.ConfigNodeSnapshot, rootPath string, blacklist []string, configClient *config.ConfigClient) {
	resp, err := configClient.GetNodeSafe(rootPath)
	if err != nil {
		logrus.Errorf("ConfigSync couldn't find config node: %v", rootPath)
		return
	}

	if resp.GetNode().GetDef().GetType() == config_service.ConfigType_LIST {
		logrus.Infof("ConfigSync: found a list node parent %v, will remove old leaves and add new leaves", rootPath)
		for _, n := range resp.Node.GetChildren() {
			if isInBlacklist(blacklist, rootPath+"/"+n.GetName()) {
				continue
			}
			logrus.Infof("ConfigSync: removing old node %v", rootPath+"/"+n.GetName())
			configClient.RemoveFromList(rootPath, n.GetName())
		}
		for k := range node.Values {
			if isInBlacklist(blacklist, rootPath+"/"+k) {
				continue
			}
			logrus.Infof("ConfigSync: adding %v/%v", rootPath, k)
			configClient.AddToList(rootPath, k)
		}
		for k := range node.ChildNodes {
			if isInBlacklist(blacklist, rootPath+"/"+k) {
				continue
			}
			logrus.Infof("ConfigSync: adding %v/%v", rootPath, k)
			configClient.AddToList(rootPath, k)
		}
	}

	for k, v := range node.Values {
		path := rootPath + "/" + k
		if isInBlacklist(blacklist, path) {
			logrus.Infof("ConfigSync: %v blacklisted", path)
			continue
		}
		resp, err := configClient.GetNodeSafe(path)
		if err != nil {
			logrus.Errorf("ConfigSync couldn't find config node: %v", path)
			continue
		}
		if resp.GetNode().GetDef().GetType() == config_service.ConfigType_STRING {
			logrus.Infof("ConfigSync: setting %v=%v", path, v)
			configClient.SetStringValue(path, v)
		}
		if resp.GetNode().GetDef().GetType() == config_service.ConfigType_BOOL {
			logrus.Infof("ConfigSync: setting %v=%v", path, v)
			val := v == "true"
			configClient.SetBoolValue(path, val)
		}
		if resp.GetNode().GetDef().GetType() == config_service.ConfigType_FLOAT {
			logrus.Infof("ConfigSync: setting %v=%v", path, v)
			val, err := strconv.ParseFloat(v, 64)
			if err != nil {
				logrus.Errorf("ConfigSync: could not parse float %v: %v", v, err)
				continue
			}
			configClient.SetDoubleValue(path, val)
		}
		if resp.GetNode().GetDef().GetType() == config_service.ConfigType_INT {
			logrus.Infof("ConfigSync: setting %v=%v", path, v)
			val, err := strconv.Atoi(v)
			if err != nil {
				logrus.Errorf("ConfigSync: could not parse int %v: %v", v, err)
				continue
			}
			configClient.SetIntValue(path, int64(val))
		}
		if resp.GetNode().GetDef().GetType() == config_service.ConfigType_UINT {
			logrus.Infof("ConfigSync: setting %v=%v", path, v)
			val, err := strconv.Atoi(v)
			if err != nil {
				logrus.Errorf("ConfigSync: could not parse int %v: %v", v, err)
				continue
			}
			configClient.SetUIntValue(path, uint64(val))
		}
	}
	for k, v := range node.ChildNodes {
		path := rootPath + "/" + k
		if rootPath == "" {
			row, _, _ := r.diagnosticState.GetDiagnosticState()
			dataRow := fmt.Sprintf("row%v", row)
			if k == "row1" {
				logrus.Infof("ConfigSync: replacing row1 configs with %v", dataRow)
				path = dataRow
			} else if k == dataRow {
				logrus.Infof("ConfigSync: replacing %v configs with row1", dataRow)
				path = "row1"
			} else {
				path = k
			}
		}
		if isInBlacklist(blacklist, path) {
			logrus.Infof("ConfigSync: %v blacklisted", path)
			continue
		}
		r.setConfigValues(v, path, blacklist, configClient)
	}
}

func isInBlacklist(blacklist []string, path string) bool {
	for _, v := range blacklist {
		if strings.HasPrefix(path, v) {
			return true
		}
	}
	return false
}
