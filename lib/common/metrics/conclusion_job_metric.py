from typing import Dict

from lib.common.logging import get_logger
from lib.common.metrics.conclusion_checker import JobsConclusionChecker
from lib.common.metrics.job_metric import JobMetric

LOG = get_logger(__name__)


class ConclusionJobMetric(JobMetric):
    def __init__(self) -> None:
        super().__init__("ConclusionJobMetric")

    async def _do_reload(self, metrics: Dict[str, str]) -> None:
        LOG.info(f"JobMetrics: Conclusion reloading with job={self._job_id}, metrics={metrics}")
        job_id = "NONE"
        if self._job_id is not None:
            job_id = self._job_id
        await self._checker.job_change(job_id, metrics)

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        weed_info = await self._checker.check_weed_counters()
        crop_info = await self._checker.check_crop_counters()

        key_map["killed_weeds"] = str(weed_info.killed)
        key_map["missed_weeds"] = str(weed_info.missed)
        key_map["skipped_weeds"] = str(weed_info.skipped)
        key_map["not_weeding"] = str(weed_info.not_shooting)
        key_map["total_weeds"] = str(weed_info.count)
        key_map["avg_weed_size_mm"] = str(weed_info.avg_size())
        for weed_type, val in weed_info.type_counts.items():
            key_map[weed_type] = str(val)

        key_map["avg_targetable_req_laser_time"] = str(weed_info.avg_targetable_time())
        key_map["avg_untargetable_req_laser_time"] = str(weed_info.avg_untargetable_time())
        key_map["total_targetable_req_laser_time"] = str(weed_info.targetable_req_laser_time)
        key_map["targetable_count"] = str(weed_info.targetable_count)
        key_map["total_untargetable_req_laser_time"] = str(weed_info.untargetable_req_laser_time)
        key_map["untargetable_count"] = str(weed_info.untargetable_count)
        key_map["thinned_crops"] = str(crop_info.killed)
        key_map["missed_crops"] = str(crop_info.missed)
        key_map["skipped_crops"] = str(crop_info.skipped)
        key_map["not_thinning"] = str(crop_info.not_shooting)
        key_map["kept_crops"] = str(crop_info.kept)
        key_map["total_crops"] = str(crop_info.count)
        key_map["valid_crops"] = str(crop_info.valid_crops)
        key_map["avg_crop_size_mm"] = str(crop_info.avg_size())

    async def start(self) -> None:
        self._checker = await JobsConclusionChecker.build()

    async def stop(self) -> None:
        pass

    async def _loop(self) -> None:
        pass
