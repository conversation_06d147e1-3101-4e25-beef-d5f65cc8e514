#include "cv/runtime/cpp/grpc_services/impl/cv_runtime_service_impl.hpp"

#include <ATen/cuda/CUDAContext.h>
#include <glm/glm.hpp>
#include <random>
#include <spdlog/spdlog.h>
#include <vector>

#include "cv/runtime/cpp/image_service_consts.hpp"
#include "cv/utils/resize.h"

#include "cv/deepweed/deepweed_utils.h"
#include "cv/runtime/cpp/deepweed_output_protobuf.h"
#include "cv/runtime/cpp/nodes/burst_record.h"
#include "cv/runtime/cpp/nodes/deepweed_infer.h"
#include "cv/runtime/cpp/nodes/deepweed_output_buffer.h"
#include "cv/runtime/cpp/nodes/image_interest_scorer.h"
#include "cv/runtime/cpp/nodes/p2p/burst_record.h"
#include "cv/runtime/cpp/nodes/p2p/capture_buffer.h"
#include "cv/runtime/cpp/nodes/p2p/infer.h"
#include "cv/runtime/cpp/nodes/p2p/output_sink.h"
#include "cv/runtime/cpp/nodes/p2p/p2p_v4l2_stream.hpp"
#include "cv/runtime/cpp/nodes/target_img_buffer.h"
#include "cv/runtime/cpp/nodes/v4l2_sink.h"
#include "generated/cv/runtime/proto/cv_runtime.pb.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/time.h"
#include "lib/common/cpp/utils/environment.hpp"
#include "lib/common/cpp/utils/role.hpp"
#include "lib/common/cpp/utils/scope_guard.hpp"
#include "lib/common/image/cpp/focus_metric.h"
#include "lib/common/model/cpp/model_registry.h"
#include "lib/drivers/thinklucid/cpp/firmware_updater.h"
#include "targeting/cpp/targeting_mode_watcher.hpp"
#include "v4l2_utils/cpp/v4l2_utils.hpp"

namespace cv {
namespace runtime {

using namespace lib::common::camera;
using namespace cv::runtime::proto;
constexpr std::string_view data_dir("/data");

CVRuntimeServiceImplCommon::CVRuntimeServiceImplCommon(
    std::shared_ptr<NodeRegistry> node_registry,
    std::shared_ptr<std::map<std::string, std::map<proto::BufferUseCase, std::string>>> camera_to_buffer,
    std::shared_ptr<std::map<std::string, lib::common::camera::Camera>> camera_id_to_camera,
    std::shared_ptr<std::map<std::string, bool>> camera_id_to_transpose,
    std::shared_ptr<std::map<std::string, std::pair<std::string, lib::common::camera::Camera>>> sim_cameras,
    std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry,
    std::shared_ptr<lib::common::GeoData> geo_data, std::shared_ptr<lib::common::ImplementStatus> implement_status,
    std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues,
    std::shared_ptr<carbon::config::ConfigTree> deepweed_config, std::string kLightweightBurstDirectory,
    std::string kFullBurstDirectory,
    const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues,
    std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>>>>
        deepweed_buffers,
    std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>>
        p2p_buffers,
    std::string role,
    std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
        distance_camera_buffer,
    std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> chip_score_queues,
    std::function<void()> reload_category_collection, std::shared_ptr<cv::embeddings::Manager> embeddings_manager,
    std::shared_ptr<cv::runtime::ErrorState> error_state)
    : node_registry_(node_registry), camera_to_buffer_(camera_to_buffer), camera_id_to_camera_(camera_id_to_camera),
      camera_id_to_transpose_(camera_id_to_transpose), sim_cameras_(sim_cameras), model_registry_(model_registry),
      geo_data_(geo_data), implement_status_(implement_status), score_queues_(score_queues),
      deepweed_config_(deepweed_config),
      common_config_(carbon::config::get_global_config_subscriber()->get_config_node("common", "")),
      kLightweightBurstDirectory_(kLightweightBurstDirectory), kFullBurstDirectory_(kFullBurstDirectory),
      booted_(false), p2p_queues_(p2p_queues), deepweed_buffers_(deepweed_buffers), p2p_buffers_(p2p_buffers),
      role_(role), distance_camera_buffer_(distance_camera_buffer), chip_score_queues_(chip_score_queues),
      reload_category_collection_(reload_category_collection), embeddings_manager_(embeddings_manager),
      error_state_(error_state) {}

CVRuntimeServiceImplCommon::~CVRuntimeServiceImplCommon() {}

grpc::Status CVRuntimeServiceImplCommon::processGetBooted(const proto::GetBootedRequest *,
                                                          proto::GetBootedResponse *response) {
  response->set_booted(booted_);
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetReady(const proto::GetReadyRequest *,
                                                         proto::GetReadyResponse *response) {
  bool ready = true;
  if (!booted_) {
    ready = false;
    response->set_booted(false);
  } else {
    response->set_booted(true);
  }

  for (auto deepweed_node : node_registry_->get_nodes_by_type<nodes::DeepweedInfer>()) {
    if (!deepweed_node.second.as<nodes::DeepweedInfer>().ready()) {
      ready = false;
      (*response->mutable_deepweed_ready_state())[deepweed_node.first] = false;
    } else {
      (*response->mutable_deepweed_ready_state())[deepweed_node.first] = true;
    }
  }

  for (auto p2p_node : node_registry_->get_nodes_by_type<nodes::p2p::P2PInfer>()) {
    if (!p2p_node.second.as<nodes::p2p::P2PInfer>().ready()) {
      ready = false;
      (*response->mutable_p2p_ready_state())[p2p_node.first] = false;
    } else {
      (*response->mutable_p2p_ready_state())[p2p_node.first] = true;
    }
  }

  response->set_ready(ready);
  return grpc::Status::OK;
}

// NOLINTNEXTLINE
grpc::Status CVRuntimeServiceImplCommon::processSetP2PContext(const SetP2PContextRequest *request,
                                                              SetP2PContextResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  const std::string &target_cam_id = request->target_cam_id();
  const std::string &predict_cam_id = request->primary_context().predict_cam_id();
  const int64_t &predict_timestamp_ms = request->primary_context().predict_timestamp_ms();
  const glm::vec2 predict_coords(request->primary_context().predict_coord_x(),
                                 request->primary_context().predict_coord_y());

  // Convert from 0-100.0% to 0-1.0
  const float safety_zone_up = request->safety_zone().up() / 100.f;
  const float safety_zone_down = request->safety_zone().down() / 100.f;
  const float safety_zone_left = request->safety_zone().left() / 100.f;
  const float safety_zone_right = request->safety_zone().right() / 100.f;
  if (!node_registry_->has_typed_node<nodes::p2p::P2PInfer>(target_cam_id)) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND,
                        fmt::format("Requested target camera id {} does not have a p2p node", target_cam_id));
  }
  if (node_registry_->has_typed_node<nodes::V4L2ImageSink>(target_cam_id)) {
    auto &v4l2_node = node_registry_->get_typed_node<nodes::V4L2ImageSink>(target_cam_id);
    v4l2_node.set_identifier(static_cast<uint32_t>(request->target_id()));
  }
  if (node_registry_->has_typed_node<nodes::p2p::P2PV4l2Stream>(target_cam_id)) {
    auto &v4l2_node = node_registry_->get_typed_node<nodes::p2p::P2PV4l2Stream>(target_cam_id);
    v4l2_node.set_identifier(static_cast<uint32_t>(request->target_id()));
  }
  try {
    auto &p2p_node = node_registry_->get_typed_node<nodes::p2p::P2PInfer>(target_cam_id);
    if (!p2p_node.set_context(predict_coords, predict_timestamp_ms, predict_cam_id)) {
      if (request->has_secondary_context()) {
        const std::string &secondary_predict_cam_id = request->secondary_context().predict_cam_id();
        const int64_t &secondary_predict_timestamp_ms = request->secondary_context().predict_timestamp_ms();
        const glm::vec2 secondary_predict_coords(request->secondary_context().predict_coord_x(),
                                                 request->secondary_context().predict_coord_y());
        if (!p2p_node.set_context(secondary_predict_coords, secondary_predict_timestamp_ms, secondary_predict_cam_id)) {
          return grpc::Status(grpc::StatusCode::NOT_FOUND,
                              fmt::format("Failed to find predict image with id {} and timestamp {}",
                                          secondary_predict_cam_id, secondary_predict_timestamp_ms));
        }
      } else {
        return grpc::Status(grpc::StatusCode::NOT_FOUND,
                            fmt::format("Failed to find predict image with id {} and timestamp {}", predict_cam_id,
                                        predict_timestamp_ms));
      }
    }
    p2p_node.set_safety_zone(safety_zone_up, safety_zone_down, safety_zone_left, safety_zone_right);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}

// NOLINTNEXTLINE
grpc::Status CVRuntimeServiceImplCommon::processGetCameraDimensions(const GetCameraDimensionsRequest *request,
                                                                    GetCameraDimensionsResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  const std::string &cam_id = request->cam_id();
  if (camera_id_to_camera_->count(cam_id) == 0) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Requested camera id {} is unknown", cam_id));
  }

  try {
    auto impl = camera_id_to_camera_->at(cam_id).get_impl(kCameraTimeout);
    if (!impl) {
      return grpc::Status(grpc::StatusCode::UNKNOWN, "Failed to connect to camera within timeout");
    }
    response->set_width(impl->get_width());
    response->set_height(impl->get_height());
    response->set_transpose(camera_id_to_transpose_->at(cam_id));
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetCameraInfo(const GetCameraInfoRequest *,
                                                              GetCameraInfoResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    for (const auto &[name, camera] : *camera_id_to_camera_) {
      auto camera_info = response->add_camera_info();

      camera_info->set_cam_id(name);
      if (!camera.get_info().ip_address.empty()) {
        camera_info->set_ip_address(camera.get_info().ip_address);
      }
      if (!camera.get_info().serial_number.empty()) {
        camera_info->set_serial_number(camera.get_info().serial_number);
      }
      auto camera_model = camera.get_info().model;
      camera_info->set_model(camera_model);

      auto latest_firmware_version = cache_camera_model_firmware_version(camera_model);
      if (latest_firmware_version) {
        camera_info->set_latest_firmware_version(*latest_firmware_version);
      }

      auto impl = camera.get_impl(kCameraTimeout);
      if (impl) {
        int width = impl->get_width();
        int height = impl->get_height();
        if (camera_id_to_transpose_->at(name)) {
          std::swap(width, height);
        }
        camera_info->set_width(width);
        camera_info->set_height(height);

        camera_info->set_link_speed((uint64_t)impl->get_link_speed());
        camera_info->set_firmware_version(impl->get_info().firmware_version);
      }

      auto path = carbon::v4l2::find_v4l2_card(name);
      if (path) {
        camera_info->set_v4l2_device_id(path.value());
      }

      const int64_t one_minute_ms = 60 * 1000;
      auto &camera_grabber = node_registry_->get_typed_node<nodes::CameraGrabber>(name);
      bool image_in_last_minute =
          maka_control_timestamp_ms() - camera_grabber.get_last_grab_timestamp_ms() < one_minute_ms;
      bool received_image_after_disconnect = camera_grabber.get_received_image_after_disconnect();
      bool connected = received_image_after_disconnect && image_in_last_minute && impl;
      switch (camera_grabber.get_last_error_type()) {
      case ::cv::runtime::nodes::ERROR_TYPE::GRAB:
        camera_info->set_error_type(::cv::runtime::proto::ErrorType::GRAB);
        // Below handles cases where disconnection's first symptom is often a timeout as well
        connected = received_image_after_disconnect && image_in_last_minute;
        break;
      case ::cv::runtime::nodes::ERROR_TYPE::CONNECTION:
        camera_info->set_error_type(::cv::runtime::proto::ErrorType::CONNECTION);
        break;
      default:
        if (!image_in_last_minute) {
          camera_info->set_error_type(::cv::runtime::proto::ErrorType::NO_IMAGE_IN_LAST_MINUTE);
        } else if (!impl) {
          camera_info->set_error_type(::cv::runtime::proto::ErrorType::NO_IMPLEMENTATION);
        } else {
          camera_info->set_error_type(::cv::runtime::proto::ErrorType::NONE);
        }
        break;
      }
      camera_info->set_connected(connected);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processSetDeepweedDetectionCriteria(const SetDeepweedDetectionCriteriaRequest *request,
                                                                SetDeepweedDetectionCriteriaResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  const std::vector<PointDetectionCategory> point_categories(request->point_categories().begin(),
                                                             request->point_categories().end());
  const std::vector<SegmentationDetectionCategory> segmentation_categories(request->segmentation_categories().begin(),
                                                                           request->segmentation_categories().end());
  try {
    deepweed_config_->get_node("weed_point_threshold")
        ->set_value<double>(request->weed_point_threshold(), maka_control_timestamp_ms(), true);
    deepweed_config_->get_node("crop_point_threshold")
        ->set_value<double>(request->crop_point_threshold(), maka_control_timestamp_ms(), true);
    auto point_categories_legacy_node = deepweed_config_->get_node("pointCategoriesLegacy");
    for (const auto &point_category : point_categories) {
      if (!point_categories_legacy_node->get_node(point_category.category())) {
        auto config_def = point_categories_legacy_node->get_def()->get_child("item");
        point_categories_legacy_node->add_child(
            std::make_shared<carbon::config::ConfigTree>(point_category.category(), config_def));
      }
      point_categories_legacy_node->get_node(point_category.category() + "/threshold")
          ->set_value<double>(point_category.threshold(), maka_control_timestamp_ms(), true);
    }
    auto segmentation_categories_node = deepweed_config_->get_node("segmentationCategories");
    for (const auto &segmentation_category : segmentation_categories) {
      if (!segmentation_categories_node->get_node(segmentation_category.category())) {
        auto config_def = segmentation_categories_node->get_def()->get_child("item");
        segmentation_categories_node->add_child(
            std::make_shared<carbon::config::ConfigTree>(segmentation_category.category(), config_def));
      }
      segmentation_categories_node->get_node(segmentation_category.category() + "/threshold")
          ->set_value<double>(segmentation_category.threshold(), maka_control_timestamp_ms(), true);
      segmentation_categories_node->get_node(segmentation_category.category() + "/safety_radius_in")
          ->set_value<double>(segmentation_category.safety_radius_in(), maka_control_timestamp_ms(), true);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetDeepweedDetectionCriteria(const GetDeepweedDetectionCriteriaRequest *,
                                                                GetDeepweedDetectionCriteriaResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  std::string deepweed_cam_id;
  for (auto &[cam_id, camera] : *camera_id_to_camera_) {
    if (!node_registry_->has_model_node(cam_id, model::ModelUseCase::kPredict)) {
      continue;
    }
    deepweed_cam_id = cam_id;
  }
  if (!node_registry_->has_model_node(deepweed_cam_id, model::ModelUseCase::kPredict)) {
    return grpc::Status(
        grpc::StatusCode::NOT_FOUND,
        fmt::format("Requested camera id {} does not have a deepweed node registered for use case", deepweed_cam_id));
  }
  try {
    const auto &point_categories =
        node_registry_->get_model_node<nodes::DeepweedInfer>(deepweed_cam_id, model::ModelUseCase::kPredict)
            .get_supported_point_categories();
    const auto &segmentation_categories =
        node_registry_->get_model_node<nodes::DeepweedInfer>(deepweed_cam_id, model::ModelUseCase::kPredict)
            .get_supported_segmentation_categories();

    response->set_weed_point_threshold((float)deepweed_config_->get_node("weed_point_threshold")->get_value<double>());
    response->set_crop_point_threshold((float)deepweed_config_->get_node("crop_point_threshold")->get_value<double>());
    auto point_categories_legacy_node = deepweed_config_->get_node("pointCategoriesLegacy");
    for (const auto &point_category : point_categories) {
      auto point_category_criteria = response->add_point_categories();
      point_category_criteria->set_category(point_category);
      if (point_categories_legacy_node->get_node(point_category)) {
        point_category_criteria->set_threshold(
            (float)point_categories_legacy_node->get_node(point_category + "/threshold")->get_value<double>());
      } else {
        point_category_criteria->set_threshold(0.5f);
      }
    }
    auto segmentation_categories_node = deepweed_config_->get_node("segmentationCategories");
    for (const auto &segmentation_category : segmentation_categories) {
      auto segmentation_category_criteria = response->add_segmentation_categories();
      segmentation_category_criteria->set_category(segmentation_category);
      if (segmentation_categories_node->get_node(segmentation_category)) {
        segmentation_category_criteria->set_threshold(
            (float)segmentation_categories_node->get_node(segmentation_category + "/threshold")->get_value<double>());
        segmentation_category_criteria->set_safety_radius_in(
            (float)segmentation_categories_node->get_node(segmentation_category + "/safety_radius_in")
                ->get_value<double>());
      } else {
        segmentation_category_criteria->set_threshold(0.5f);
        segmentation_category_criteria->set_safety_radius_in(0.4f);
      }
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetDeepweedSupportedCategories(const GetDeepweedSupportedCategoriesRequest *request,
                                                                  GetDeepweedSupportedCategoriesResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  std::string deepweed_cam_id;
  if (request->has_cam_id()) {
    deepweed_cam_id = request->cam_id();
  } else {
    for (auto &[cam_id, camera] : *camera_id_to_camera_) {
      if (!node_registry_->has_model_node(cam_id, model::ModelUseCase::kPredict)) {
        continue;
      }
      deepweed_cam_id = cam_id;
    }
  }
  if (!node_registry_->has_model_node(deepweed_cam_id, model::ModelUseCase::kPredict)) {
    return grpc::Status(
        grpc::StatusCode::NOT_FOUND,
        fmt::format("Requested camera id {} does not have a deepweed node registered for use case", deepweed_cam_id));
  }
  try {
    const auto &point_categories =
        node_registry_->get_model_node<nodes::DeepweedInfer>(deepweed_cam_id, model::ModelUseCase::kPredict)
            .get_supported_point_categories();
    const auto &segmentation_categories =
        node_registry_->get_model_node<nodes::DeepweedInfer>(deepweed_cam_id, model::ModelUseCase::kPredict)
            .get_supported_segmentation_categories();
    *response->mutable_point_categories() = {point_categories.begin(), point_categories.end()};
    *response->mutable_segmentation_categories() = {segmentation_categories.begin(), segmentation_categories.end()};
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}

// NOLINTNEXTLINE
grpc::Status CVRuntimeServiceImplCommon::processSetCameraSettings(const SetCameraSettingsRequest *request,
                                                                  SetCameraSettingsResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  int cam_ids_size = request->cam_ids_size();

  for (int i = 0; i < cam_ids_size; i++) {
    const std::string &cam_id = request->cam_ids(i);
    if (camera_id_to_camera_->count(cam_id) == 0) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not have an associated camera. No changes are being "
                                      "applied to any cameras in this request",
                                      cam_id));
    }
  }

  try {
    lib::common::camera::CameraSettings camera_settings;

    if (request->has_exposure_us()) {
      camera_settings.exposure_us = request->exposure_us();
    }
    if (request->has_gamma()) {
      camera_settings.gamma = request->gamma();
    }
    if (request->has_gain_db()) {
      camera_settings.gain_db = request->gain_db();
    }

    if (request->has_wb_ratio_red()) {
      camera_settings.wb_ratio_red = request->wb_ratio_red();
    }
    if (request->has_wb_ratio_green()) {
      camera_settings.wb_ratio_green = request->wb_ratio_green();
    }
    if (request->has_wb_ratio_blue()) {
      camera_settings.wb_ratio_blue = request->wb_ratio_blue();
    }

    if (request->has_auto_whitebalance()) {
      camera_settings.auto_whitebalance = request->auto_whitebalance();
    }
    if (request->has_light_source_preset()) {
      camera_settings.light_source_preset = request->light_source_preset();
    }
    if (request->has_roi_offset_x()) {
      camera_settings.roi_offset_x = request->roi_offset_x();
    }
    if (request->has_roi_offset_y()) {
      camera_settings.roi_offset_y = request->roi_offset_y();
    }

    for (int i = 0; i < cam_ids_size; i++) {
      const std::string &cam_id = request->cam_ids(i);
      auto impl = camera_id_to_camera_->at(cam_id).get_impl(kCameraTimeout);
      if (!impl) {
        return grpc::Status(grpc::StatusCode::UNKNOWN, "Failed to connect to camera within timeout");
      }
      lib::common::camera::CameraSettings current_settings = impl->get_settings();
      camera_settings.mirror = request->has_mirror() ? request->mirror() : current_settings.mirror;
      camera_settings.flip = request->has_flip() ? request->flip() : current_settings.flip;
      camera_settings.strobing = request->has_strobing() ? request->strobing() : current_settings.strobing;
      camera_settings.ptp = request->has_ptp() ? request->ptp() : current_settings.ptp;
      impl->update_settings(camera_settings);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

// NOLINTNEXTLINE
grpc::Status CVRuntimeServiceImplCommon::processGetCameraSettings(const GetCameraSettingsRequest *request,
                                                                  GetCameraSettingsResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  int cam_ids_size = request->cam_ids_size();

  try {
    for (int i = 0; i < cam_ids_size; i++) {
      const std::string &cam_id = request->cam_ids(i);

      if (camera_id_to_camera_->count(cam_id) == 0) {
        spdlog::warn("Camera id {} is not recognized, there are no camera settings to return.", cam_id);
        continue;
      }

      auto impl = camera_id_to_camera_->at(cam_id).get_impl(kCameraTimeout);
      if (!impl) {
        return grpc::Status(grpc::StatusCode::UNKNOWN, "Failed to connect to camera within timeout");
      }
      impl->sync_settings();
      auto &camera_settings = impl->get_settings();

      CameraSettingsResponse *cam_settings_res = response->add_camera_settings_response();
      cam_settings_res->set_cam_id(cam_id);
      if (camera_settings.exposure_us.has_value()) {
        cam_settings_res->set_exposure_us(camera_settings.exposure_us.value());
      }
      if (camera_settings.gamma.has_value()) {
        cam_settings_res->set_gamma(camera_settings.gamma.value());
      }
      if (camera_settings.gain_db.has_value()) {
        cam_settings_res->set_gain_db(camera_settings.gain_db.value());
      }
      if (camera_settings.light_source_preset.has_value()) {
        cam_settings_res->set_light_source_preset(camera_settings.light_source_preset.value());
      }
      if (camera_settings.wb_ratio_red.has_value()) {
        cam_settings_res->set_wb_ratio_red(camera_settings.wb_ratio_red.value());
      }
      if (camera_settings.wb_ratio_blue.has_value()) {
        cam_settings_res->set_wb_ratio_blue(camera_settings.wb_ratio_blue.value());
      }
      if (camera_settings.wb_ratio_green.has_value()) {
        cam_settings_res->set_wb_ratio_green(camera_settings.wb_ratio_green.value());
      }
      if (camera_settings.auto_whitebalance.has_value()) {
        cam_settings_res->set_auto_whitebalance(camera_settings.auto_whitebalance.value());
      }

      if (camera_settings.roi_width.has_value()) {
        cam_settings_res->set_roi_width(camera_settings.roi_width.value());
      }
      if (camera_settings.roi_height.has_value()) {
        cam_settings_res->set_roi_height(camera_settings.roi_height.value());
      }
      if (camera_settings.roi_offset_x.has_value()) {
        cam_settings_res->set_roi_offset_x(camera_settings.roi_offset_x.value());
      }
      if (camera_settings.roi_offset_y.has_value()) {
        cam_settings_res->set_roi_offset_y(camera_settings.roi_offset_y.value());
      }
      if (camera_settings.gpu_id.has_value()) {
        cam_settings_res->set_gpu_id(camera_settings.gpu_id.value());
      }

      cam_settings_res->set_mirror(camera_settings.mirror);
      cam_settings_res->set_flip(camera_settings.flip);
      cam_settings_res->set_strobing(camera_settings.strobing);
      cam_settings_res->set_ptp(camera_settings.ptp);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

// NOLINTNEXTLINE
grpc::Status CVRuntimeServiceImplCommon::processStartBurstRecordFrames(const StartBurstRecordFramesRequest *request,
                                                                       StartBurstRecordFramesResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  const std::string &cam_id = request->cam_id();
  const int64_t duration_ms = request->duration_ms();
  const std::string &path = request->path();
  const int &downsample_factor = request->downsample_factor();
  const bool dont_capture_predict_image = request->dont_capture_predict_image();
  if (!node_registry_->has_typed_node<nodes::BurstRecord>(cam_id)) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Requested camera id {} is unknown", cam_id));
  }

  try {
    if (node_registry_->get_typed_node<nodes::BurstRecord>(cam_id).start_recording_images(
            std::chrono::milliseconds(duration_ms), path, downsample_factor)) {
      if (node_registry_->has_typed_node<nodes::p2p::P2PBurstRecord>(cam_id)) {
        node_registry_->get_typed_node<nodes::p2p::P2PBurstRecord>(cam_id).start_recording(
            std::chrono::milliseconds(duration_ms), path, dont_capture_predict_image);
      }
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processStopBurstRecordFrames(const StopBurstRecordFramesRequest *request,
                                                                      StopBurstRecordFramesResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  const std::string &cam_id = request->cam_id();
  if (!node_registry_->has_typed_node<nodes::BurstRecord>(cam_id)) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Requested camera id {} is unknown", cam_id));
  }

  try {
    std::optional<int64_t> last_frame_timestamp_ms = request->has_last_frame_timestamp_ms()
                                                         ? std::optional<int64_t>(request->last_frame_timestamp_ms())
                                                         : std::optional<int64_t>();
    node_registry_->get_typed_node<nodes::BurstRecord>(cam_id).stop_recording_images(last_frame_timestamp_ms);
    if (node_registry_->has_typed_node<nodes::p2p::P2PBurstRecord>(cam_id)) {
      node_registry_->get_typed_node<nodes::p2p::P2PBurstRecord>(cam_id).stop_recording(last_frame_timestamp_ms);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processSetConnectors(const SetConnectorsRequest *request,
                                                              SetConnectorsResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  std::vector<std::string> cam_ids(request->cam_ids().begin(), request->cam_ids().end());
  std::vector<std::string> connector_ids(request->connector_ids().begin(), request->connector_ids().end());

  for (auto &cam_id : cam_ids) {
    if (!node_registry_->has_camera(cam_id)) {
      for (const std::string &camera : node_registry_->get_known_cameras()) {
        std::cout << camera << std::endl;
      }
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not have an associated camera. No changes are being "
                                      "applied to any cameras in this request",
                                      cam_id));
    }
  }

  try {
    for (auto &cam_id : cam_ids) {
      for (auto &connector_id : connector_ids) {
        if (!node_registry_->has_connector(cam_id, connector_id)) {
          spdlog::warn("Connector id {} is not recognized for camera {}", connector_id, cam_id);
          continue;
        }

        std::shared_ptr<ConnectorControl> connector = node_registry_->get_connector(cam_id, connector_id);

        if (request->has_is_enabled()) {
          connector->set_enabled(request->is_enabled());
        }
        if (request->has_reduction_ratio()) {
          connector->set_reduction_ratio((int)request->reduction_ratio());
        }
      }
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetConnectors(const GetConnectorsRequest *request,
                                                              GetConnectorsResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    std::vector<std::string> cam_ids(request->cam_ids().begin(), request->cam_ids().end());
    std::vector<std::string> connector_ids(request->connector_ids().begin(), request->connector_ids().end());

    for (auto &cam_id : cam_ids) {
      if (connector_ids.size() == 0) {
        connector_ids = node_registry_->get_connector_ids(cam_id);
      }
      for (auto &connector_id : connector_ids) {
        if (!node_registry_->has_connector(cam_id, connector_id)) {
          continue;
        }
        auto connector = node_registry_->get_connector(cam_id, connector_id);
        ConnectorResponse *connector_response = response->add_connector_response();
        connector_response->set_cam_id(cam_id);
        connector_response->set_connector_id(connector_id);
        connector_response->set_is_enabled(connector->get_enabled());
        connector_response->set_reduction_ratio(connector->get_reduction_ratio());
      }
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetTiming(const GetTimingRequest *, GetTimingResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    for (auto node : node_registry_->get_nodes()) {
      if (node.get_name() == "noop") {
        continue;
      }
      NodeTiming *node_timing = response->add_node_timing();
      node_timing->set_name(node.get_name());
      node_timing->set_fps_mean(node.get_mean_fps());
      node_timing->set_fps_99pct(node.get_99pct_fps());
      node_timing->set_latency_ms_mean(node.get_mean_latency_ms());
      node_timing->set_latency_ms_99pct(node.get_99pct_latency_ms());
      node_timing->set_state(cv_node_state_to_string(node.get_state()));
      auto state_timings = node.get_state_timings();
      for (auto [state, time_ms] : state_timings) {
        (*node_timing->mutable_state_timings())[cv_node_state_to_string(state)] = (float)time_ms.count();
      }
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processPredict(const PredictRequest *request, PredictResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    std::vector<std::string> file_paths(request->file_paths().begin(), request->file_paths().end());
    std::vector<int64_t> timestamps(request->timestamps_ms().begin(), request->timestamps_ms().end());

    if (node_registry_->has_typed_node<nodes::CameraGrabber>(request->cam_id())) {
      node_registry_->get_typed_node<nodes::CameraGrabber>(request->cam_id()).queue_images(file_paths, timestamps);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processLoadAndQueue(const LoadAndQueueRequest *request,
                                                             LoadAndQueueResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    std::vector<std::string> file_paths(request->file_paths().begin(), request->file_paths().end());
    std::vector<int64_t> timestamps(request->timestamps_ms().begin(), request->timestamps_ms().end());

    if (node_registry_->has_typed_node<nodes::CameraGrabber>(request->cam_id())) {
      node_registry_->get_typed_node<nodes::CameraGrabber>(request->cam_id())
          .load_and_queue_images(file_paths, timestamps);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processSetImage(const SetImageRequest *request, SetImageResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    if (node_registry_->has_typed_node<nodes::CameraGrabber>(request->cam_id())) {
      node_registry_->get_typed_node<nodes::CameraGrabber>(request->cam_id()).set_image(request->file_path());
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processUnsetImage(const UnsetImageRequest *request, UnsetImageResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    if (node_registry_->has_typed_node<nodes::CameraGrabber>(request->cam_id())) {
      node_registry_->get_typed_node<nodes::CameraGrabber>(request->cam_id()).unset_image();
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetModelPaths(const GetModelPathsRequest *,
                                                              GetModelPathsResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    std::map<model::ModelUseCase, std::string> model_paths = model_registry_->get_model_paths();
    if (model_paths.count(model::ModelUseCase::kP2P) > 0) {
      response->set_p2p(model_paths.at(model::ModelUseCase::kP2P));
    }
    if (model_paths.count(model::ModelUseCase::kPredict) > 0) {
      response->set_deepweed(model_paths.at(model::ModelUseCase::kPredict));
    }
    if (model_paths.count(model::ModelUseCase::kDriving) > 0) {
      response->set_furrows(model_paths.at(model::ModelUseCase::kDriving));
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetCameraTemperatures(const GetCameraTemperaturesRequest *,
                                                                      GetCameraTemperaturesResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    for (auto &[cam_id, camera] : *camera_id_to_camera_) {
      if (camera.get_info().vendor == CameraVendor::kZed) {
        continue;
      }
      auto impl = camera.get_impl(kCameraTimeout);
      if (!impl) {
        return grpc::Status(grpc::StatusCode::UNKNOWN, "Failed to connect to camera within timeout");
      }
      CameraTemperature *temp = response->add_temperature();
      temp->set_cam_id(cam_id);
      temp->set_temperature(impl->get_temperature());
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processSetImageScore(const SetImageScoreRequest *request,
                                                              SetImageScoreResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    std::string cam_id = request->cam_id();
    double score = request->score();
    int64_t timestamp_ms = request->timestamp_ms();

    if (!node_registry_->has_typed_node<nodes::ImageInterestScorer>(cam_id)) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not have a image_interest_scorer node", cam_id));
    }

    auto &image_interest_scorer = node_registry_->get_typed_node<nodes::ImageInterestScorer>(cam_id);
    image_interest_scorer.push_or_update("weed_tracking", timestamp_ms, score, request->deepweed_output());
    image_interest_scorer.push_or_update("emergency_weed_tracking", timestamp_ms, score, request->deepweed_output());
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetScoreQueue(const GetScoreQueueRequest *request,
                                                              GetScoreQueueResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto score_type = request->score_type();
    bool in_score_queues = score_queues_.count(score_type) > 0;
    bool in_chip_score_queues = chip_score_queues_->count(score_type) > 0;
    if (!in_score_queues && !in_chip_score_queues) {
      return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, fmt::format("No score queue for type {}", score_type));
    }

    std::vector<proto::ScoreObject> score_objects;
    if (in_score_queues) {
      score_objects = score_queues_.at(score_type)->enumerate_heap();

    } else {
      score_objects = chip_score_queues_->at(score_type)->enumerate_heap();
    }
    for (auto score_object : score_objects) {
      auto sc = response->add_score_object();
      *sc = score_object;
    }

  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processListScoreQueues(const proto::Empty *,
                                                                proto::ListScoreQueuesResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    for (auto it = score_queues_.begin(); it != score_queues_.end(); it++) {
      auto sq = response->add_score_queue();
      sq->set_score_queue(it->first);
      sq->set_num_items(it->second->size());
    }

    for (auto it = chip_score_queues_->begin(); it != chip_score_queues_->end(); it++) {
      auto sq = response->add_score_queue();
      sq->set_score_queue(it->first);
      sq->set_num_items(it->second->size());
    }

  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetMaxImageScore(const GetMaxImageScoreRequest *request,
                                                                 GetMaxImageScoreResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto score_type = request->score_type();
    if (score_queues_.count(score_type) == 0) {
      return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, fmt::format("No score queue for type {}", score_type));
    }

    auto score_queue = score_queues_.at(score_type);

    if (score_queue->size() == 0) {
      return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, fmt::format("Score queue {} is empty", score_type));
    }
    auto max_score = (score_queue->peek_max()).score;

    response->set_score(max_score);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processFlushQueues(const proto::FlushQueuesRequest *request,
                                                            proto::FlushQueuesResponse *) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto score_types_size = request->score_type_size();
    for (int i = 0; i < score_types_size; i++) {
      auto score_type = request->score_type(i);

      if (request->score_queue_type() == proto::ScoreQueueType::CHIP) {
        if (chip_score_queues_->count(score_type) == 0) {
          return grpc::Status(grpc::StatusCode::OUT_OF_RANGE,
                              fmt::format("No chip score queue for type {}", score_type));
        }

        auto score_queue = chip_score_queues_->at(score_type);
        score_queue->flush();
      } else {
        if (score_queues_.count(score_type) == 0) {
          return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, fmt::format("No score queue for type {}", score_type));
        }

        auto score_queue = score_queues_.at(score_type);
        score_queue->flush();
      }
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

void CVRuntimeServiceImplCommon::setDetectionsMetadataResponse(const proto::DeepweedOutput &deepweed_output,
                                                               ImageAndMetadataResponse *response) {
  for (int i = 0; i < (int)deepweed_output.detections_size(); i++) {
    auto det = deepweed_output.detections(i);
    weed_tracking::Detection *detection = response->add_deepweed_detections();
    detection->set_x(det.x());
    detection->set_y(det.y());
    detection->set_size(det.size());
    detection->set_is_weed(det.hit_class() == proto::HitClass::WEED);
    std::vector<std::pair<std::string, float>> detection_classes;
    if (det.hit_class() == proto::HitClass::WEED) {
      for (int clz_i = 0; clz_i < det.weed_detection_class_scores_size(); clz_i++) {
        detection_classes.push_back(
            {deepweed_output.weed_detection_classes(clz_i), det.weed_detection_class_scores(clz_i)});
      }
    } else {
      detection_classes.push_back({proto::HitClass_Name(det.hit_class()), 1.0f});
    }
    std::string category = cv::deepweed::get_detection_clz(detection_classes);
    detection->set_clz(category);
  }
}

void CVRuntimeServiceImplCommon::setChipPredictionMetadata(ChipScoreMetadata metadata,
                                                           proto::ChipPrediction *prediction_metadata) {
  prediction_metadata->set_x(metadata.x);
  prediction_metadata->set_y(metadata.y);
  prediction_metadata->set_radius(metadata.radius);
  prediction_metadata->set_model_id(metadata.model_id);
  prediction_metadata->set_band_status(metadata.band_status);
  std::map<std::string, float>::iterator it;
  for (it = metadata.scores.begin(); it != metadata.scores.end(); it++) {
    auto category_score = prediction_metadata->add_score();
    category_score->set_category(it->first);
    category_score->set_score(it->second);
  }

  for (it = metadata.embedding_distances.begin(); it != metadata.embedding_distances.end(); it++) {
    auto category_score = prediction_metadata->add_embedding_distance();
    category_score->set_category(it->first);
    category_score->set_score(it->second);
  }
}

void CVRuntimeServiceImplCommon::setImageAndMetadataResponse(CameraImage cam_image,
                                                             ImageAndMetadataResponse *response) {
  auto image = cam_image.image.cpu().to(torch::kUInt8).contiguous();

  // Convert to RGBA
  image = image.permute({1, 2, 0});

  auto opts = torch::TensorOptions().dtype(torch::kUInt8);

  torch::Tensor alpha = 255 * torch::ones({image.size(0), image.size(1), 1}, opts);
  image = torch::cat({image, alpha}, -1);

  auto image_data = image.data_ptr<uint8_t>();
  std::string byte_string(reinterpret_cast<char *>(image_data), 4 * cam_image.get_width() * cam_image.get_height());

  response->set_width(cam_image.get_width());
  response->set_height(cam_image.get_height());
  response->set_bytes(byte_string);
  response->set_timestamp_ms(cam_image.timestamp_ms);

  response->set_cam_id(cam_image.camera_id);
  response->set_image_type("predict");
  response->set_model_url(
      model_registry_->get_internal_metadata_by_use_case(model::ModelUseCase::kPredict).experiment_url());
  response->set_crop_point_threshold(deepweed_config_->get_node("crop_point_threshold")->get_value<double>());
  response->set_weed_point_threshold(deepweed_config_->get_node("weed_point_threshold")->get_value<double>());

  // defaulting to 1 which is analogous to it not activated
  response->set_segmentation_threshold(1);
  for (const auto &node : deepweed_config_->get_node("segmentationCategories")->get_children_nodes()) {
    if (node->get_node("enabled")->get_value<bool>()) {
      auto segmentation_threshold = (float)node->get_node("threshold")->get_value<double>();
      // we set the segmentation threhhold to the first enabled value we find. Historically we have only supported one
      // category: driptape
      response->set_segmentation_threshold(segmentation_threshold);
      break;
    }
  }

  auto ppi = cam_image.ppi;
  if (ppi.has_value()) {
    response->set_ppi(ppi.value());
  } else {
    response->set_ppi(0.0);
  }

  response->set_iso_formatted_time(iso8601_timestamp(
      std::chrono::time_point<std::chrono::system_clock>(std::chrono::milliseconds(cam_image.timestamp_ms)), true,
      true));

  response->set_lla_lat(cam_image.geo_lla_data.get_lat());
  response->set_lla_lng(cam_image.geo_lla_data.get_lng());
  response->set_lla_alt(cam_image.geo_lla_data.get_alt());
  response->set_lla_timestamp_ms(cam_image.geo_lla_data.get_timestamp_ms());

  response->set_ecef_x(cam_image.geo_ecef_data.get_x());
  response->set_ecef_y(cam_image.geo_ecef_data.get_y());
  response->set_ecef_z(cam_image.geo_ecef_data.get_z());
  response->set_ecef_timestamp_ms(cam_image.geo_ecef_data.get_timestamp_ms());

  if (cam_image.bedtop_profile.has_value()) {
    for (auto &column_height : cam_image.bedtop_profile.value()["weed"]) {
      response->add_weed_height_columns(column_height);
    }
    for (auto &column_height : cam_image.bedtop_profile.value()["crop"]) {
      response->add_crop_height_columns(column_height);
    }
  }

  response->set_bbh_offset_mm(cam_image.bbh_offset_mm);

  if (!cam_image.focus_metric.has_value()) {
    lib::common::image::FocusMetric focus_metric;
    cam_image.focus_metric = focus_metric.compute(cam_image.image);
  }
  response->set_focus_metric(cam_image.focus_metric.value());
  response->set_exposure_us(cam_image.exposure_us);
  response->set_weeding_enabled(carbon::targeting::TargetingModeWatcher::get().weeding_enabled());
  response->set_thinning_enabled(carbon::targeting::TargetingModeWatcher::get().thinning_enabled());
  response->set_deepweed_id(
      model_registry_->get_external_metadata_by_use_case(model::ModelUseCase::kPredict)["id"].get<std::string>());
  response->set_p2p_id(
      model_registry_->get_external_metadata_by_use_case(model::ModelUseCase::kP2P)["id"].get<std::string>());
  response->set_simulator_generated(carbon::common::is_role_simulator());
}

void CVRuntimeServiceImplCommon::setP2PImageAndMetadataResponse(P2PScoreQueueObject p2p_score_object,
                                                                P2PImageAndMetadataResponse *response) {
  auto opts = torch::TensorOptions().dtype(torch::kUInt8);

  torch::Tensor target_alpha =
      255 * torch::ones({p2p_score_object.target.image.size(0), p2p_score_object.target.image.size(1), 1}, opts);
  auto target_image = torch::cat({p2p_score_object.target.image, target_alpha}, -1);
  auto target_image_data = target_image.data_ptr<uint8_t>();

  response->set_target_width((int)target_image.size(1));
  response->set_target_height((int)target_image.size(0));
  response->set_target_bytes(target_image_data, target_image.size(0) * target_image.size(1) * target_image.size(2));
  response->set_timestamp_ms(p2p_score_object.target.timestamp_ms);

  torch::Tensor perspective_alpha =
      255 *
      torch::ones({p2p_score_object.perspective.image.size(0), p2p_score_object.perspective.image.size(1), 1}, opts);
  auto perspective_image = torch::cat({p2p_score_object.perspective.image, perspective_alpha}, -1);
  auto perspective_image_data = perspective_image.data_ptr<uint8_t>();

  response->set_perspective_width((int)perspective_image.size(1));
  response->set_perspective_height((int)perspective_image.size(0));
  response->set_perspective_bytes(perspective_image_data,
                                  perspective_image.size(0) * perspective_image.size(1) * perspective_image.size(2));

  torch::Tensor annotated_target_alpha =
      255 *
      torch::ones({p2p_score_object.annotated_target.size(0), p2p_score_object.annotated_target.size(1), 1}, opts);
  auto annotated_target_image = torch::cat({p2p_score_object.annotated_target, annotated_target_alpha}, -1);
  auto annotated_target_image_data = annotated_target_image.data_ptr<uint8_t>();

  response->set_annotated_target_width((int)annotated_target_image.size(1));
  response->set_annotated_target_height((int)annotated_target_image.size(0));
  response->set_annotated_target_bytes(annotated_target_image_data, annotated_target_image.size(0) *
                                                                        annotated_target_image.size(1) *
                                                                        annotated_target_image.size(2));

  response->set_cam_id(p2p_score_object.target.camera_id);
  response->set_image_type("p2p");
  response->set_model_url(
      model_registry_->get_internal_metadata_by_use_case(model::ModelUseCase::kPredict).experiment_url());

  auto ppi = p2p_score_object.target.ppi;
  if (ppi.has_value()) {
    response->set_ppi(ppi.value());
  } else {
    response->set_ppi(0.0);
  }

  auto perspective_ppi = p2p_score_object.perspective.ppi;
  if (perspective_ppi.has_value()) {
    response->set_perspective_ppi(perspective_ppi.value());
  } else {
    response->set_perspective_ppi(0.0);
  }

  response->set_iso_formatted_time(
      iso8601_timestamp(std::chrono::time_point<std::chrono::system_clock>(
                            std::chrono::milliseconds(p2p_score_object.target.timestamp_ms)),
                        true, true));

  response->set_lla_lat(p2p_score_object.target.geo_lla_data.get_lat());
  response->set_lla_lng(p2p_score_object.target.geo_lla_data.get_lng());
  response->set_lla_alt(p2p_score_object.target.geo_lla_data.get_alt());
  response->set_lla_timestamp_ms(p2p_score_object.target.geo_lla_data.get_timestamp_ms());

  response->set_ecef_x(p2p_score_object.target.geo_ecef_data.get_x());
  response->set_ecef_y(p2p_score_object.target.geo_ecef_data.get_y());
  response->set_ecef_z(p2p_score_object.target.geo_ecef_data.get_z());
  response->set_ecef_timestamp_ms(p2p_score_object.target.geo_ecef_data.get_timestamp_ms());

  if (p2p_score_object.target.focus_metric.has_value()) {
    response->set_focus_metric(p2p_score_object.target.focus_metric.value());
  }
  response->set_exposure_us(p2p_score_object.target.exposure_us);
  response->set_weeding_enabled(carbon::targeting::TargetingModeWatcher::get().weeding_enabled());
  response->set_thinning_enabled(carbon::targeting::TargetingModeWatcher::get().thinning_enabled());
  response->set_deepweed_id(
      model_registry_->get_external_metadata_by_use_case(model::ModelUseCase::kPredict)["id"].get<std::string>());
  response->set_p2p_id(
      model_registry_->get_external_metadata_by_use_case(model::ModelUseCase::kP2P)["id"].get<std::string>());
}

grpc::Status CVRuntimeServiceImplCommon::processGetChipQueueInformation(const proto::ChipQueueInformationRequest *,
                                                                        proto::ChipQueueInformationResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    for (std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>::iterator it =
             chip_score_queues_->begin();
         it != chip_score_queues_->end(); it++) {
      auto key = it->first;
      if (it->second->size() == 0) {
        continue;
      }
      auto top_score = it->second->peek_max().score;

      auto queue_score = response->add_queue_score();

      queue_score->set_category(key);
      queue_score->set_score(top_score);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetChipImage(const proto::GetChipImageRequest *request,
                                                             proto::ChipImageAndMetadataResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto score_type = request->score_type();
    if (chip_score_queues_->count(score_type) == 0) {
      return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, fmt::format("No chip score queue for type {}", score_type));
    }

    auto score_queue = chip_score_queues_->at(score_type);

    if (score_queue->size() == 0) {
      return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, fmt::format("Score queue {} is empty", score_type));
    }

    auto max_item = score_queue->pop_max();
    proto::ImageAndMetadataResponse *image_and_meta = response->mutable_image_and_metadata();
    setImageAndMetadataResponse(max_item.cam_image, image_and_meta);
    proto::ChipPrediction *chip_prediction = response->mutable_prediction_metadata();
    setChipPredictionMetadata(max_item.metadata, chip_prediction);
    image_and_meta->set_score(max_item.score);
    image_and_meta->set_score_type(score_type);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetMaxScoredImage(const GetMaxScoredImageRequest *request,
                                                                  ImageAndMetadataResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto score_type = request->score_type();
    if (score_queues_.count(score_type) == 0) {
      return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, fmt::format("No score queue for type {}", score_type));
    }

    auto score_queue = score_queues_.at(score_type);

    if (score_queue->size() == 0) {
      return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, fmt::format("Score queue {} is empty", score_type));
    }

    auto max_item = score_queue->pop_max();
    setImageAndMetadataResponse(max_item.cam_image, response);

    if (max_item.deepweed_detections) {
      setDetectionsMetadataResponse(*max_item.deepweed_detections, response);
    }

    response->set_score(max_item.score);
    response->set_score_type(score_type);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetLatestP2PImage(const GetLatestP2PImageRequest *request,
                                                                  P2PImageAndMetadataResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto it = p2p_queues_.find(int(request->reason()));
    if (it == p2p_queues_.end()) {
      return grpc::Status(
          grpc::StatusCode::OUT_OF_RANGE,
          fmt::format("P2P reason {} does not exist", carbon::aimbot::cv::P2PCaptureReason_Name(request->reason())));
    }
    if (it->second->size() == 0) {
      return grpc::Status(
          grpc::StatusCode::OUT_OF_RANGE,
          fmt::format("P2P {} score queue is empty", carbon::aimbot::cv::P2PCaptureReason_Name(request->reason())));
    }

    auto max_item = it->second->pop_max();
    setP2PImageAndMetadataResponse(max_item, response);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetLatestImage(const GetLatestImageRequest *request,
                                                               ImageAndMetadataResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto cam_id = request->cam_id();
    if (!node_registry_->has_typed_node<nodes::LatestImage>(cam_id)) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not have a latest_image node", cam_id));
    }

    auto &latest_image_node = node_registry_->get_typed_node<nodes::LatestImage>(cam_id);
    auto cam_image = latest_image_node.get();
    if (!cam_image.has_value()) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("No camera image found for {}", cam_id));
    }

    // Try to get deepweed detections for the latest image
    const BufferUseCase use_case = BufferUseCase::Predict;
    auto image_timestamp = cam_image.value().timestamp_ms;
    if (camera_to_buffer_->count(cam_id) > 0 && camera_to_buffer_->at(cam_id).count(use_case) > 0) {
      // set deepweed detections on the image if they exist
      std::string buffer_name = camera_to_buffer_->at(cam_id).at(use_case);
      if (deepweed_buffers_->count(buffer_name) > 0) {
        auto buffer = deepweed_buffers_->at(buffer_name);
        auto deepweed_output = buffer->get_next(image_timestamp, 5000);
        if (!deepweed_output) {
          for (size_t i = 0; i < deepweed_output->detections.size(); i++) {
            deepweed::DeepweedDetection det = deepweed_output->detections[i];
            weed_tracking::Detection *deepweed_detection = response->add_deepweed_detections();
            deepweed_detection->set_x(det.x);
            deepweed_detection->set_y(det.y);
            deepweed_detection->set_size(det.size);
            auto detection_classes = det.get_detection_classes(deepweed_output->weed_detection_classes);
            std::string category = deepweed::get_detection_clz(detection_classes);
            deepweed_detection->set_clz(category);
          }
        }
      }
    }

    setImageAndMetadataResponse(cam_image.value(), response);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetImageNearTimestamp(const proto::GetImageNearTimestampRequest *request,
                                                         proto::ImageAndMetadataResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto cam_id = request->cam_id();
    auto requested_timestamp_ms = request->timestamp_ms();

    std::map<int64_t, lib::common::camera::CameraImage> image_map{};

    if (distance_camera_buffer_->at(cam_id)) {
      auto [nearest_timestamp, image] = distance_camera_buffer_->at(cam_id)->get_nearest(
          requested_timestamp_ms, [](int64_t a, int64_t b) { return std::abs(a - b); });
      image_map.emplace(nearest_timestamp, image);
    }

    if (image_map.empty()) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Could not find an image for camera {}", cam_id));
    }

    auto it = image_map.begin();
    auto nearest_timestamp = it->first;
    auto best_distance = std::abs(requested_timestamp_ms - nearest_timestamp);
    it++;

    while (it != image_map.end()) {
      auto dist = std::abs(requested_timestamp_ms - it->first) < best_distance;
      if (dist) {
        best_distance = dist;
        nearest_timestamp = it->first;
      }
      it++;
    }

    auto image = image_map[nearest_timestamp];

    const BufferUseCase use_case = BufferUseCase::Predict;
    auto image_timestamp = image.timestamp_ms;
    if (camera_to_buffer_->count(cam_id) > 0 && camera_to_buffer_->at(cam_id).count(use_case) > 0) {
      std::string buffer_name = camera_to_buffer_->at(cam_id).at(use_case);
      if (deepweed_buffers_->count(buffer_name) > 0) {
        auto buffer = deepweed_buffers_->at(buffer_name);
        auto deepweed_output = buffer->get_next(image_timestamp, 500);
        if (deepweed_output) {
          for (size_t i = 0; i < deepweed_output->detections.size(); i++) {
            deepweed::DeepweedDetection det = deepweed_output->detections[i];
            weed_tracking::Detection *deepweed_detection = response->add_deepweed_detections();
            deepweed_detection->set_x(det.x);
            deepweed_detection->set_y(det.y);
            deepweed_detection->set_size(det.size);
            auto detection_classes = det.get_detection_classes(deepweed_output->weed_detection_classes);
            std::string category = deepweed::get_detection_clz(detection_classes);
            deepweed_detection->set_clz(category);
          }
        }
      }
    }

    setImageAndMetadataResponse(image, response);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetLightweightBurstRecord(const GetLightweightBurstRecordRequest *,
                                                                          GetLightweightBurstRecordResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    // Get the list of available burst records from the directory names
    // NOTE: We only provide burst recordings from today
    time_t curr_time;
    tm *curr_tm;
    char date_string[100];
    time(&curr_time);
    curr_tm = localtime(&curr_time);
    strftime(date_string, 50, "%Y-%m-%d", curr_tm);

    if (!std::filesystem::exists(kLightweightBurstDirectory_)) {
      std::filesystem::create_directories(kLightweightBurstDirectory_);
    }

    // Get the ones we have already uploaded so we don't duplicate uploads
    std::vector<std::string> uploaded_burst_records;
    for (const auto &uploaded_burst_record : std::filesystem::directory_iterator(kLightweightBurstDirectory_)) {
      uploaded_burst_records.push_back(uploaded_burst_record.path());
    }

    std::vector<std::string> new_burst_records;
    for (const auto &burst_record_dir : std::filesystem::directory_iterator(kFullBurstDirectory_)) {
      new_burst_records.push_back(burst_record_dir.path());
    }

    bool match = false;
    std::vector<std::string> not_uploaded_dirs;
    for (auto &new_burst_record : new_burst_records) {
      match = false;
      std::string new_burst_record_basename = new_burst_record.substr(new_burst_record.find_last_of("/\\") + 1);
      for (auto &uploaded_burst_record : uploaded_burst_records) {
        std::string uploaded_burst_record_basename =
            uploaded_burst_record.substr(uploaded_burst_record.find_last_of("/\\") + 1);
        if (new_burst_record_basename == uploaded_burst_record_basename) {
          match = true;
          break;
        }
      }
      if (!match) {
        not_uploaded_dirs.push_back(new_burst_record);
      }
    }

    if (not_uploaded_dirs.size() == 0) {
      spdlog::info("CV runtime GRPC service: No new burst records to upload.");
      return grpc::Status(grpc::StatusCode::UNKNOWN, "CV runtime GRPC service: No new burst records to upload.");
    }

    // Randomly select one burst recording
    int random_index = std::rand() % int(not_uploaded_dirs.size());
    std::string read_dir = not_uploaded_dirs[random_index];

    // Run the compression script on the selected burst recording
    std::string command = fmt::format(
        "python -m tools.burst_record.make_lightweight_bursts --read-dir {} --write-dir {} {} {}", read_dir,
        kLightweightBurstDirectory_,
        carbon::targeting::TargetingModeWatcher::get().weeding_enabled() ? "--weeding-enabled" : "--weeding-disabled",
        carbon::targeting::TargetingModeWatcher::get().thinning_enabled() ? "--thinning-enabled"
                                                                          : "--thinning-disabled");
    int result = std::system(command.c_str());

    if (result == 0) {
      spdlog::info("Created lightweight burst recording.");
    } else {
      std::string message = fmt::format("Lightweight burst recording generation failed.");
      return grpc::Status(grpc::StatusCode::UNKNOWN, message);
    }

    std::string read_dir_base_name = read_dir.substr(read_dir.find_last_of("/\\") + 1);

    std::string zip_filepath = kLightweightBurstDirectory_ + read_dir_base_name + ".zip";
    std::string metadata_filepath = kLightweightBurstDirectory_ + read_dir_base_name + ".metadata.json";

    // Read the zip file in as a string of bytes
    auto zip_string_stream = std::ostringstream{};
    std::ifstream zip_input_file(zip_filepath);
    if (!zip_input_file.is_open()) {
      std::string message =
          fmt::format("Could not open zip file for transfer to data upload manager: {}", zip_filepath);
      return grpc::Status(grpc::StatusCode::UNKNOWN, message);
    }
    zip_string_stream << zip_input_file.rdbuf();
    std::string zip_file = zip_string_stream.str();

    // Read the metdata file into a string
    auto metadata_string_stream = std::ostringstream{};
    std::ifstream metadata_input_file(metadata_filepath);
    if (!metadata_input_file.is_open()) {
      std::string message =
          fmt::format("Could not open metadata file for transfer to data upload manager: {}", metadata_filepath);
      return grpc::Status(grpc::StatusCode::UNKNOWN, message);
    }
    metadata_string_stream << metadata_input_file.rdbuf();
    std::string metadata_file = metadata_string_stream.str();

    // Place data in response
    response->set_zip_file(zip_file);
    response->set_metadata_file(metadata_file);

  } catch (const std::exception &exception) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, exception.what());
  }
  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetDeepweedOutputByTimestamp(const GetDeepweedOutputByTimestampRequest *request,
                                                                DeepweedOutput *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto cam_id = request->cam_id();
    if (!node_registry_->has_typed_node<nodes::DeepweedOutputBuffer>(cam_id)) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not have a buffer node", cam_id));
    }

    auto &node = node_registry_->get_typed_node<nodes::DeepweedOutputBuffer>(cam_id);
    if (!node.get_output_by_ts(request->timestamp_ms(), response)) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not a deepweed output for timestamp {}", cam_id,
                                      request->timestamp_ms()));
    }
  } catch (const std::exception &exception) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, exception.what());
  }
  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetRecommendedStrobeSettings(const GetRecommendedStrobeSettingsRequest *,
                                                                GetRecommendedStrobeSettingsResponse *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  const int num_gpus = (int)torch::cuda::device_count();

  response->set_target_camera_fps(40.0f);
  response->set_targets_per_predict_ratio(6);

  if (role_ == "row-primary" || role_ == "row-secondary") {
    // If all gpus are ADA, we only need 4 gpus to run at full fps.
    bool all_ada = true;
    for (int i = 0; i < num_gpus; i++) {
      auto properties = at::cuda::getDeviceProperties((int8_t)i);
      if (!(properties->major > 8 || (properties->major == 8 && properties->minor >= 9))) {
        all_ada = false;
      }
    }
    if (!all_ada && num_gpus <= 4) {
      response->set_target_camera_fps(30.0f);
      response->set_targets_per_predict_ratio(4);
    } else {
      response->set_target_camera_fps(36.0f);
      response->set_targets_per_predict_ratio(4);
    }
  } else {
    if (num_gpus <= 4) {
      response->set_target_camera_fps(24.0f);
      response->set_targets_per_predict_ratio(4);
    } else if (num_gpus <= 6) {
      response->set_target_camera_fps(30.0f);
      response->set_targets_per_predict_ratio(5);
    }
  }
  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processP2PBufferringBurstCapture(const proto::P2PBufferringBurstCaptureRequest *request,
                                                             proto::P2PBufferringBurstCaptureResponse *response) {
  (void)response;
  const std::string &cam_id = request->cam_id();
  const std::string &path = request->path();
  const bool dont_capture_predict_image = request->dont_capture_predict_image();
  const int64_t &start_timestamp_ms = request->start_timestamp_ms();
  const int64_t &end_timestamp_ms = request->end_timestamp_ms();
  auto predict_path = request->predict_path();
  auto has_predict_path = request->predict_path_exists();
  auto save_predict_metadata = request->save_predict_metadata();
  auto addition_predict_metadata = request->predict_metadata();

  std::optional<std::string> pred_path = std::nullopt;
  if (has_predict_path) {
    pred_path = predict_path;
  }

  if (!node_registry_->has_typed_node<nodes::TargetImgBuffer>(cam_id)) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Requested target buf id {} is unknown", cam_id));
  }
  if (!node_registry_->has_typed_node<nodes::p2p::P2PCaptureBuffer>(cam_id)) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Requested p2p buf id {} is unknown", cam_id));
  }

  try {
    // Always re-enable buffering
    carbon::common::ScopeGuard restart(
        [&]() { node_registry_->get_typed_node<nodes::TargetImgBuffer>(cam_id).start_buffering(); });

    node_registry_->get_typed_node<nodes::TargetImgBuffer>(cam_id).stop_buffering();
    node_registry_->get_typed_node<nodes::p2p::P2PCaptureBuffer>(cam_id).burst_capture(
        path, start_timestamp_ms, end_timestamp_ms, !dont_capture_predict_image, pred_path, save_predict_metadata,
        addition_predict_metadata);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processP2PCapture(const proto::P2PCaptureRequest *request,
                                                           proto::P2PCaptureResponse *response) {
  (void)response;
  const std::string &cam_id = request->cam_id();
  const std::string &name = request->name();
  const int64_t &timestamp_ms = request->timestamp_ms();
  const bool write_to_disk = request->write_to_disk();
  const int reason = int(request->reason());
  if (!node_registry_->has_typed_node<nodes::p2p::P2PCaptureBuffer>(cam_id)) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Requested camera id {} is unknown", cam_id));
  }
  try {
    node_registry_->get_typed_node<nodes::p2p::P2PCaptureBuffer>(cam_id).capture_p2p(name, timestamp_ms, write_to_disk,
                                                                                     reason);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

// NOLINTNEXTLINE
grpc::Status CVRuntimeServiceImplCommon::processSetAutoWhitebalance(const SetAutoWhitebalanceRequest *request,
                                                                    SetAutoWhitebalanceResponse *) {

  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  int cam_ids_size = request->cam_ids_size();

  for (int i = 0; i < cam_ids_size; i++) {
    const std::string &cam_id = request->cam_ids(i);
    if (camera_id_to_camera_->count(cam_id) == 0) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not have an associated camera. No changes are being "
                                      "applied to any cameras in this request",
                                      cam_id));
    }
  }

  try {
    for (int i = 0; i < cam_ids_size; i++) {
      const std::string &cam_id = request->cam_ids(i);
      auto impl = camera_id_to_camera_->at(cam_id).get_impl(kCameraTimeout);
      if (!impl) {
        return grpc::Status(grpc::StatusCode::UNKNOWN, "Failed to connect to camera within timeout");
      }
      impl->set_auto_whitebalance(request->enable());
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetNextDeepweedOutput(const proto::GetNextDeepweedOutputRequest *request,
                                                         proto::DeepweedOutput *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  const std::string &cam_id = request->cam_id();
  if (camera_to_buffer_->count(cam_id) == 0) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Requested camera id {} is unknown", cam_id));
  }
  if (camera_to_buffer_->at(cam_id).count(BufferUseCase::Predict) == 0) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND,
                        fmt::format("Requested camera id {} does not have a buffer registered for deepweed", cam_id));
  }

  auto buffer = deepweed_buffers_->at(camera_to_buffer_->at(cam_id).at(BufferUseCase::Predict));
  auto data = buffer->get_next(request->timestamp_ms(), request->timeout_ms());
  if (!data) {
    // Using ABORTED since it is never set by grpc library code
    return grpc::Status(grpc::StatusCode::ABORTED,
                        fmt::format("Timed out waiting for next deepweed output in {}", cam_id));
  }

  DeepweedOutputToProtobuf::to_protobuf(&(*data), response);
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetNextP2POutput(const proto::GetNextP2POutputRequest *request,
                                                                 proto::P2POutputProto *response) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  const std::string &cam_id = request->cam_id();
  if (camera_to_buffer_->count(cam_id) == 0) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Requested camera id {} is unknown", cam_id));
  }
  if (camera_to_buffer_->at(cam_id).count(BufferUseCase::P2P) == 0) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND,
                        fmt::format("Requested camera id {} does not have a buffer registered for deepweed", cam_id));
  }

  auto buffer = p2p_buffers_->at(camera_to_buffer_->at(cam_id).at(BufferUseCase::P2P));
  auto data = buffer->get_next(request->timestamp_ms(), request->timeout_ms());
  if (!data) {
    return grpc::Status(grpc::StatusCode::DEADLINE_EXCEEDED,
                        fmt::format("Timed out waiting for next p2p output for {}", cam_id));
  }

  response->set_matched(data->matched);
  response->set_target_coord_x(data->target_coord_x);
  response->set_target_coord_y(data->target_coord_y);
  response->set_target_timestamp_ms(data->target_timestamp_ms);
  response->set_predict_timestamp_ms(data->predict_timestamp_ms);
  response->set_safe(data->safe);
  response->set_predict_coord_x(data->predict_coord_x);
  response->set_predict_coord_y(data->predict_coord_y);
  response->set_predict_cam(data->predict_cam);
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processSetTargetingState(const proto::SetTargetingStateRequest *request,
                                                                  proto::SetTargetingStateResponse *response) {
  (void)response;
  carbon::targeting::TargetingModeWatcher::get().set(request->weeding_enabled(), request->thinning_enabled());
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetNextFocusMetric(const proto::GetNextFocusMetricRequest *request,
                                                                   proto::GetNextFocusMetricResponse *response) {

  try {
    const auto &cam_id = request->cam_id();
    if (!node_registry_->has_typed_node<nodes::LatestImage>(cam_id)) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not have a latest_image node", cam_id));
    }

    auto &latest_image_node = node_registry_->get_typed_node<nodes::LatestImage>(cam_id);
    auto cam_image = latest_image_node.get_next(request->timestamp_ms(), 1000);
    if (!cam_image.has_value()) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("No camera image found for {}", cam_id));
    }
    if (!cam_image->focus_metric) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("No focus metric found for {}", cam_id));
    }
    response->set_focus_metric(*(cam_image->focus_metric));
    response->set_timestamp_ms(cam_image->timestamp_ms);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processRemoveDataDir(const proto::RemoveDataDirRequest *request,
                                                              proto::RemoveDataDirResponse *response) {

  (void)response;
  const auto &req_path(request->path());
  size_t i = 0;
  while (i < req_path.size() && req_path[i] == '/') {
    ++i;
  }
  if (i >= req_path.size()) {
    auto err_msg = fmt::format("Invalid path to remove '{}'", req_path);
    spdlog::warn(err_msg);
    return grpc::Status(grpc::StatusCode::NOT_FOUND, err_msg);
  }
  auto path = std::filesystem::path(data_dir) / req_path.substr(i);
  if (!std::filesystem::is_directory(path)) {
    auto err_msg = fmt::format("Invalid path to remove {}", path);
    spdlog::warn(err_msg);
    return grpc::Status(grpc::StatusCode::NOT_FOUND, err_msg);
  }
  std::filesystem::remove_all(path);
  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetLastNImages(const proto::LastNImageRequest *request,
                                                  grpc::ServerWriter<proto::ImageAndMetadataResponse> *writer) {
  if (!booted_)
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");

  try {
    auto cam_id = request->cam_id();
    uint64_t max_images = request->num_images();
    if (!distance_camera_buffer_->at(cam_id)) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND,
                          fmt::format("Requested camera id {} does not have a distance_camera_buffer node", cam_id));
    }

    auto distance_buffer = distance_camera_buffer_->at(cam_id);

    auto dist_buffer_timestamps = distance_buffer->keys();
    std::sort(dist_buffer_timestamps.begin(), dist_buffer_timestamps.end(), std::greater<>());

    std::vector<lib::common::camera::CameraImage> cam_images;
    for (size_t i = 0; i < max_images; i++) {
      if (i == dist_buffer_timestamps.size()) {
        break;
      }

      cam_images.push_back(distance_buffer->get(dist_buffer_timestamps[i]));
    }

    for (auto &cam_image : cam_images) {
      proto::ImageAndMetadataResponse image_and_meta = proto::ImageAndMetadataResponse();
      setImageAndMetadataResponse(cam_image, &image_and_meta);
      writer->Write(image_and_meta);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetComputeCapabilities(const proto::Empty *,
                                                                       proto::ComputeCapabilitiesResponse *response) {
  std::set<std::string> compute_capabilities;
  const int num_gpus = (int)torch::cuda::device_count();
  for (int i = 0; i < num_gpus; i++) {
    auto properties = at::cuda::getDeviceProperties((int8_t)i);
    compute_capabilities.insert(fmt::format("{}.{}", properties->major, properties->minor));
  }
  *response->mutable_capabilities() = {compute_capabilities.begin(), compute_capabilities.end()};
  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetSupportedTensorRTVersions(const proto::Empty *,
                                                                proto::SupportedTensorRTVersionsResponse *response) {
  *response->mutable_versions() = {deeplearning::server::trt_runtime::TRTRuntime::SUPPORTED_TRT_VERSIONS.begin(),
                                   deeplearning::server::trt_runtime::TRTRuntime::SUPPORTED_TRT_VERSIONS.end()};
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processReloadCategoryCollection(const proto::Empty *, proto::Empty *) {
  if (reload_category_collection_) {
    reload_category_collection_();
  }
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetCategoryCollection(const proto::Empty *,
                                                                      proto::GetCategoryCollectionResponse *response) {
  response->set_category_collection_id(embeddings_manager_->get_active_category_collection_id());
  response->set_last_updated_timestamp_ms(embeddings_manager_->get_last_updated_timestamp_ms());
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processGetErrorState(const proto::Empty *,
                                                              proto::GetErrorStateResponse *response) {
  response->set_model_unsupported_embeddings(error_state_->model_unsupported_embeddings);
  response->set_plant_profile_error(error_state_->plant_profile_error);
  return grpc::Status::OK;
}

grpc::Status CVRuntimeServiceImplCommon::processSnapshotPredictImages(const proto::SnapshotPredictImagesRequest *,
                                                                      proto::SnapshotPredictImagesResponse *response) {
  if (!booted_) {
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");
  }

  auto distance_buffer_nodes = node_registry_->get_nodes_by_type<nodes::DistanceBuffer>();
  for (const auto &[name, node_ref] : distance_buffer_nodes) {
    auto snapshots = node_ref.as<nodes::DistanceBuffer>().snapshot_and_clear();
    for (const auto &[pcam_id, timestamp_ms] : snapshots) {
      auto *snapshot = response->add_snapshots();
      snapshot->set_pcam_id(pcam_id);
      snapshot->set_timestamp_ms(timestamp_ms);
    }
  }

  return grpc::Status::OK;
}

grpc::Status
CVRuntimeServiceImplCommon::processGetChipForPredictImage(const proto::GetChipForPredictImageRequest *request,
                                                          proto::GetChipForPredictImageResponse *response) {
  if (!booted_) {
    return grpc::Status(grpc::StatusCode::UNAVAILABLE, "CV runtime not yet booted. Please wait until booted.");
  }

  // Find the DistanceBuffer node for this camera
  auto distance_buffer_nodes = node_registry_->get_nodes_by_type<nodes::DistanceBuffer>();
  auto it = std::find_if(distance_buffer_nodes.begin(), distance_buffer_nodes.end(), [&](const auto &node_pair) {
    return node_pair.first.find(request->pcam_id()) != std::string::npos;
  });

  if (it == distance_buffer_nodes.end()) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND,
                        fmt::format("Distance buffer node not found for pcam_id {}", request->pcam_id()));
  }

  auto &distance_buffer_node = it->second.as<nodes::DistanceBuffer>();
  auto snapshot_buffer = distance_buffer_node.get_snapshot_buffer();
  if (!snapshot_buffer) {
    return grpc::Status(grpc::StatusCode::INTERNAL,
                        fmt::format("Snapshot buffer is null for pcam_id {}", request->pcam_id()));
  }

  auto image_opt = snapshot_buffer->get_opt(request->timestamp_ms());
  if (!image_opt) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND,
                        fmt::format("Image with timestamp {} not found in distance buffer for pcam_id {}",
                                    request->timestamp_ms(), request->pcam_id()));
  }

  try {
    auto cam_image = *image_opt;
    glm::ivec2 center(request->center_x_px(), request->center_y_px());
    glm::ivec2 size(kCropRadius * 2, kCropRadius * 2);
    auto [cropped_tensor, new_coord] = cv::utils::crop_or_pad(cam_image.image, center, size);

    lib::common::camera::CameraImage chip_image;
    chip_image.image = cropped_tensor;
    chip_image.timestamp_ms = cam_image.timestamp_ms;
    chip_image.camera_id = cam_image.camera_id;
    proto::ImageAndMetadataResponse *image_and_meta = response->mutable_image_and_metadata();
    setImageAndMetadataResponse(chip_image, image_and_meta);

    return grpc::Status::OK;
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
}

std::optional<std::string> CVRuntimeServiceImplCommon::cache_camera_model_firmware_version(const std::string &model) {
  if (camera_model_to_firmware_version_.count(model) == 0) {
    auto firmware_version_opt = lib::drivers::thinklucid::get_latest_firmware_version(model);
    if (firmware_version_opt) {
      camera_model_to_firmware_version_[model] = firmware_version_opt->first.to_string();
    } else {
      camera_model_to_firmware_version_[model] = std::make_optional<std::string>();
    }
  }
  return camera_model_to_firmware_version_.at(model);
}

void CVRuntimeServiceImplCommon::set_booted(bool booted) { booted_ = booted; }

} // namespace runtime
} // namespace cv
