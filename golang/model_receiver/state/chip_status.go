package state

import (
	"context"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/spf13/afero"

	"github.com/carbonrobotics/robot/golang/lib/chip_manager"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/sirupsen/logrus"
)

type ChipStatus struct {
	env      environment.Robot
	mu       sync.RWMutex
	chips    map[string]*chip_manager.Chip
	cacheDir string
}

func NewChipStatus(env environment.Robot) *ChipStatus {
	return &ChipStatus{
		cacheDir: chip_manager.ChipCacheDir(env),
	}
}

func (ms *ChipStatus) ListChips() []*chip_manager.Chip {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	chips := make([]*chip_manager.Chip, 0)
	for _, chip := range ms.chips {
		chips = append(chips, chip)
	}

	return chips
}

func (ms *ChipStatus) WriteChip(chipId string, chipContents []byte) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	if err := appRecieverFs.MkdirAll(ms.cacheDir, 0777); err != nil {
		return err
	}

	chipFilePath := filepath.Join(ms.cacheDir, chipId+chip_manager.GraphicExt)
	if err := afero.WriteFile(appRecieverFs, chipFilePath, chipContents, 0666); err != nil {
		return err
	}

	logrus.Infof("Wrote chip to %s", chipFilePath)

	return nil
}

func (ms *ChipStatus) WriteChipMetadata(chipId string, MetadataContents []byte) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	if err := appRecieverFs.MkdirAll(ms.cacheDir, 0777); err != nil {
		return err
	}

	metadataFilePath := filepath.Join(ms.cacheDir, chipId+chip_manager.MetaExt)
	if err := afero.WriteFile(appRecieverFs, metadataFilePath, MetadataContents, 0666); err != nil {
		return err
	}

	logrus.Infof("Wrote chip metadata to %s", metadataFilePath)
	return nil
}

func (ms *ChipStatus) RemoveChips(chipIds []string) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	for _, chipId := range chipIds {
		chipFilePath := filepath.Join(ms.cacheDir, chipId)
		metadataFilePath := filepath.Join(ms.cacheDir, chipId+chip_manager.MetaExt)

		removeFiles(chipFilePath, metadataFilePath)
		delete(ms.chips, chipId)
	}
	return nil
}

func (ms *ChipStatus) UpdateChips() {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	metadataFiles, err := afero.ReadDir(appRecieverFs, ms.cacheDir)
	if err != nil {
		logrus.WithError(err).Error("Failed to read chip cache directory")
		return
	}

	chips := make(map[string]*chip_manager.Chip)
	for _, metadataFile := range metadataFiles {
		if metadataFile.IsDir() {
			continue
		}

		if !strings.HasSuffix(metadataFile.Name(), chip_manager.MetaExt) {
			continue
		}

		metaFilePath := filepath.Join(ms.cacheDir, metadataFile.Name())
		chip, err := chip_manager.GetVerifyMetadata(metaFilePath)
		if err != nil {
			logrus.Errorf("failed reading metadata: %s - %v", metaFilePath, err)
			removeFiles(metaFilePath)
			continue
		}

		if !chip.IsValid() {
			logrus.Errorf("Chip meta: %s is not valid", metaFilePath)
			removeFiles(metaFilePath)
			continue
		}

		chipFilePath := filepath.Join(ms.cacheDir, chip.Filename())
		if err := chip_manager.VerifyChipDownload(chip, chipFilePath); err != nil {
			logrus.Errorf("malformed chip download: %s - %v", chipFilePath, err)
			removeFiles(chipFilePath)
			continue
		}

		chips[chip.Id] = chip
	}
	logrus.Infof("UpdateChips: Found %d chips", len(chips))
	ms.chips = chips
}

func (ms *ChipStatus) RunChipWatcher(stopCtx context.Context, triggerChan <-chan bool) {
	for {
		ms.UpdateChips()
		select {
		case <-stopCtx.Done():
			return
		case <-time.After(30 * time.Second):
		case <-triggerChan:
		}
	}
}
