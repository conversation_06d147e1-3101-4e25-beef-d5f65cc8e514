#pragma once

#include <atomic>
#include <chrono>
#include <thread>

#include <c10/cuda/CUDAGuard.h>
#include <fmt/format.h>
#include <fmt/ostream.h>
#include <spdlog/spdlog.h>

#include "config/client/cpp/config_subscriber.hpp"
#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/utils/environment.hpp"
#include "lib/common/cpp/utils/moving_average.hpp"
#include "lib/common/redis/redis_client.hpp"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;

const int kSetSettingsRetryWaitMillis = 1000;

class BrightnessAdjuster : public CVNodeImpl {
public:
  BrightnessAdjuster(Camera cam, Input<CameraImage> input, std::shared_ptr<ImplementStatus> implement_status)
      : CVNodeImpl(fmt::format("/brightness_adj/{}", cam.get_info().camera_id)), cam_(cam), input_(input),
        config_subscriber_(carbon::config::get_global_config_subscriber()),
        last_sync_(std::chrono::system_clock::now()), implement_status_(implement_status), initialized_(false) {
    const auto &camera_id = cam.get_info().camera_id;
    const std::string target_string = "target";
    camera_index_ = 0;
    if (camera_id.find(target_string) != std::string::npos) {
      auto number_string = camera_id.substr(target_string.size());
      camera_index_ = std::stoi(number_string);
    }
  }

protected:
  int64_t tick() override {
    c10::cuda::OptionalCUDAStreamGuard stream_guard;
    if (cam_.get_settings().gpu_id) {
      if (!cuda_stream_) {
        cuda_stream_.reset(new c10::cuda::CUDAStream(
            c10::cuda::getStreamFromPool(true, c10::DeviceIndex(*cam_.get_settings().gpu_id))));
      }
      stream_guard.reset_stream(*cuda_stream_);
    }

    auto [lifted, estopped] = implement_status_->retrieve();

    if (lifted) {
      set_state(CVNodeState::kPulling);
      return input_.pop().timestamp_ms;
    }

    auto config_node = config_subscriber_->get_config_node("cv", "cameras")->get_node(cam_.get_info().camera_id);
    float min_exposure_us = config_node->get_node("auto_brightness/min_exposure_us")->get_value<float>();
    float max_exposure_us = config_node->get_node("auto_brightness/max_exposure_us")->get_value<float>();
    float exposure_delta_us = config_node->get_node("auto_brightness/exposure_delta_us")->get_value<float>();
    float min_gain_db = config_node->get_node("auto_brightness/min_gain_db")->get_value<float>();
    float max_gain_db = config_node->get_node("auto_brightness/max_gain_db")->get_value<float>();
    float gain_delta_db = config_node->get_node("auto_brightness/gain_delta_db")->get_value<float>();
    float max_brightness = config_node->get_node("auto_brightness/max_brightness")->get_value<float>();
    float min_brightness = config_node->get_node("auto_brightness/min_brightness")->get_value<float>();
    float quantile = config_node->get_node("auto_brightness/quantile")->get_value<float>();
    float exposure_offset_increment_us =
        config_node->get_node("auto_brightness/exposure_offset_increment_us")->get_value<float>();
    int brightness_moving_average_count =
        config_node->get_node("auto_brightness/moving_average_count")->get_value<int>();

    if (!config_node->get_node("auto_brightness_enabled")->get_value<bool>()) {
      auto &current_settings = cam_.get_settings();
      CameraSettings new_settings = current_settings;
      new_settings.exposure_us = config_node->get_node("exposure_us")->get_value<float>();
      new_settings.gain_db = config_node->get_node("gain_db")->get_value<float>();
      try {
        cam_.update_settings(new_settings);
      } catch (camera_error &ex) {
        spdlog::error("{}", ex.what());
      }

      set_state(CVNodeState::kPulling);
      return input_.pop().timestamp_ms;
    }

    if (!initialized_) {
      redis_client_.wait_until_ready();
      CameraSettings new_settings = cam_.get_settings();
      try {
        new_settings.gain_db = redis_client_.get_double(get_redis_prefix() + "/gain_db");
        new_settings.exposure_us = redis_client_.get_double(get_redis_prefix() + "/exposure_us");
      } catch (sw::redis::Error &ex) {
        spdlog::error("Failed to read brightness settings from redis: {}", ex.what());
        return input_.pop().timestamp_ms;
      }
      last_exposure_us_ = *new_settings.exposure_us;
      last_gain_db_ = *new_settings.gain_db;
      cam_.update_settings(new_settings);
      initialized_ = true;
    }

    if (brightness_moving_average_count_ != brightness_moving_average_count || brightness_moving_average_ == nullptr) {
      brightness_moving_average_count_ = brightness_moving_average_count;
      brightness_moving_average_.reset(new carbon::common::MovingAverage((size_t)brightness_moving_average_count_));
    }

    // Compute quantile based on per pixel max color values.
    set_state(CVNodeState::kPulling);
    CameraImage cam_image = input_.pop();
    set_state(CVNodeState::kProcessing);
    auto image_tensor_downsampled_100x =
        cam_image.image.index({"...", torch::indexing::Slice(0, torch::indexing::None, 10),
                               torch::indexing::Slice(0, torch::indexing::None, 10)});
    float current_brightness =
        std::get<0>(image_tensor_downsampled_100x.max(0)).to(torch::kFloat32).quantile(quantile).item<float>() / 255.0f;
    brightness_moving_average_->add(current_brightness);

    auto brightness = brightness_moving_average_->avg();
    if (brightness_moving_average_->full() && (brightness > max_brightness || brightness < min_brightness)) {
      brightness_moving_average_->clear();
      float exposure_offset = exposure_offset_increment_us * (float)camera_index_;

      auto current_exposure_us = last_exposure_us_ - exposure_offset;
      auto current_gain_db = last_gain_db_;

      const float pos_or_neg_exposure_delta_us = brightness > max_brightness ? -exposure_delta_us : exposure_delta_us;
      auto new_exposure_us = current_exposure_us + pos_or_neg_exposure_delta_us;
      auto new_gain_db = current_gain_db;

      const float exposure_midpoint = min_exposure_us + (max_exposure_us - min_exposure_us) / 2.0f;
      // If the exposure is maxed out or the exposure is less than half of the midpoint, then adjust gain.
      if ((current_exposure_us >= max_exposure_us || current_exposure_us <= exposure_midpoint) &&
          new_exposure_us == current_exposure_us) {
        const float pos_or_neg_gain_delta_db = brightness > max_brightness ? -gain_delta_db : gain_delta_db;
        new_gain_db = current_gain_db + pos_or_neg_gain_delta_db;
      }

      new_exposure_us = std::min(std::max(new_exposure_us, min_exposure_us), max_exposure_us);
      new_gain_db = std::min(std::max(new_gain_db, min_gain_db), max_gain_db);

      CameraSettings current_settings = cam_.get_settings();

      // Only apply if settings are different
      if (!current_settings.gain_db.has_value() || !current_settings.exposure_us.has_value() ||
          new_gain_db != *current_settings.gain_db || new_exposure_us != *current_settings.exposure_us) {
        try {
          CameraSettings new_settings = current_settings;
          new_settings.gain_db = new_gain_db;
          new_settings.exposure_us = new_exposure_us + exposure_offset;
          cam_.update_settings(new_settings);
        } catch (camera_error &ex) {
          spdlog::error("{}", ex.what());
          return cam_image.timestamp_ms;
        }
      }

      last_exposure_us_ = new_exposure_us + exposure_offset;
      last_gain_db_ = new_gain_db;
      try {
        redis_client_.set_double(get_redis_prefix() + "/exposure_us", last_exposure_us_);
        redis_client_.set_double(get_redis_prefix() + "/gain_db", last_gain_db_);
      } catch (sw::redis::Error &ex) {
        spdlog::error("Failed to write brightness adjuster settings to redis: {}", ex.what());
      }
    }

    return cam_image.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  std::string get_redis_prefix() {
    return fmt::format("brightness_adjuster/{}/{}", carbon::common::get_row(), cam_.get_info().camera_id);
  }

  Camera cam_;
  Input<CameraImage> input_;
  std::unique_ptr<c10::cuda::CUDAStream> cuda_stream_;
  std::shared_ptr<carbon::config::ConfigSubscriber> config_subscriber_;
  std::chrono::system_clock::time_point last_sync_;
  std::shared_ptr<ImplementStatus> implement_status_;
  std::unique_ptr<carbon::common::MovingAverage<float>> brightness_moving_average_;
  int brightness_moving_average_count_;
  int camera_index_;
  lib::common::RedisClient redis_client_;
  bool initialized_;
  float last_exposure_us_;
  float last_gain_db_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
