#!/usr/bin/env python3
"""
Test script to verify the enhanced tooltip functionality in trajectory viewer.
This script tests the modified plot_line_chart function with sample data.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Mock the required imports for testing
class MockStreamlit:
    def plotly_chart(self, fig):
        print("Mock: plotly_chart called with figure")
        print(f"Figure has {len(fig.data)} traces")
        for i, trace in enumerate(fig.data):
            print(f"Trace {i}: {trace.name}")
            if hasattr(trace, 'customdata') and trace.customdata:
                print(f"  Has custom hover data: {len(trace.customdata)} points")
                if trace.customdata:
                    print(f"  Sample hover text: {trace.customdata[0][:100]}...")

class MockPlotly:
    class graph_objects:
        class Figure:
            def __init__(self):
                self.data = []
                
            def add_trace(self, trace):
                self.data.append(trace)
                
            def update_layout(self, **kwargs):
                pass
                
        class Scatter:
            def __init__(self, x, y, mode, name, hovertemplate=None, customdata=None):
                self.x = x
                self.y = y
                self.mode = mode
                self.name = name
                self.hovertemplate = hovertemplate
                self.customdata = customdata

# Mock the imports
sys.modules['streamlit'] = MockStreamlit()
sys.modules['plotly'] = MockPlotly()
sys.modules['plotly.graph_objects'] = MockPlotly.graph_objects

# Import the function we want to test
from deeplearning.streamlit.trajectory_viewer import plot_line_chart

def test_enhanced_tooltips():
    """Test the enhanced tooltip functionality"""
    
    # Sample trajectory data with coordinates
    trajectories = {
        "row1_123": [(100.0, 50.0), (110.0, 52.0), (120.0, 48.0)],
        "row1_124": [(105.0, 45.0), (115.0, 47.0), (125.0, 44.0)]
    }
    
    # Sample full trajectory data with all information
    trajectory_by_id = {
        "row1_123": [
            {
                "y_mm": 100.0, "z_mm": 50.0, "timestamp_ms": 1000,
                "x_mm": 200.0, "id": 123, "kill_status": "ALIVE",
                "weed_score": 0.85, "crop_score": 0.15, "plant_score": 0.95,
                "num_detections_used_for_decision": 5, "classification": "WEED",
                "is_reachable": True, "intersected_with_nonshootable": False,
                "band_status": "IN_BAND", "crop_line_id": 1, "size_mm": 12.5
            },
            {
                "y_mm": 110.0, "z_mm": 52.0, "timestamp_ms": 1100,
                "x_mm": 210.0, "id": 123, "kill_status": "ALIVE",
                "weed_score": 0.90, "crop_score": 0.10, "plant_score": 0.98,
                "num_detections_used_for_decision": 6, "classification": "WEED",
                "is_reachable": True, "intersected_with_nonshootable": False,
                "band_status": "IN_BAND", "crop_line_id": 1, "size_mm": 13.2
            },
            {
                "y_mm": 120.0, "z_mm": 48.0, "timestamp_ms": 1200,
                "x_mm": 220.0, "id": 123, "kill_status": "KILLED",
                "weed_score": 0.88, "crop_score": 0.12, "plant_score": 0.96,
                "num_detections_used_for_decision": 7, "classification": "WEED",
                "is_reachable": True, "intersected_with_nonshootable": False,
                "band_status": "IN_BAND", "crop_line_id": 1, "size_mm": 11.8,
                "shoot_time_actual_ms": 1180
            }
        ],
        "row1_124": [
            {
                "y_mm": 105.0, "z_mm": 45.0, "timestamp_ms": 1050,
                "x_mm": 205.0, "id": 124, "kill_status": "ALIVE",
                "weed_score": 0.25, "crop_score": 0.75, "plant_score": 0.85,
                "num_detections_used_for_decision": 3, "classification": "CROP",
                "is_reachable": False, "intersected_with_nonshootable": True,
                "nonshootable_type_string": "CROP_PROTECT", "protected_by_traj": True,
                "band_status": "OUT_OF_BAND", "crop_line_id": 2, "size_mm": 8.5
            },
            {
                "y_mm": 115.0, "z_mm": 47.0, "timestamp_ms": 1150,
                "x_mm": 215.0, "id": 124, "kill_status": "ALIVE",
                "weed_score": 0.20, "crop_score": 0.80, "plant_score": 0.88,
                "num_detections_used_for_decision": 4, "classification": "CROP",
                "is_reachable": False, "intersected_with_nonshootable": True,
                "nonshootable_type_string": "CROP_PROTECT", "protected_by_traj": True,
                "band_status": "OUT_OF_BAND", "crop_line_id": 2, "size_mm": 9.2
            },
            {
                "y_mm": 125.0, "z_mm": 44.0, "timestamp_ms": 1250,
                "x_mm": 225.0, "id": 124, "kill_status": "ALIVE",
                "weed_score": 0.18, "crop_score": 0.82, "plant_score": 0.90,
                "num_detections_used_for_decision": 5, "classification": "CROP",
                "is_reachable": False, "intersected_with_nonshootable": True,
                "nonshootable_type_string": "CROP_PROTECT", "protected_by_traj": True,
                "band_status": "OUT_OF_BAND", "crop_line_id": 2, "size_mm": 8.8
            }
        ]
    }
    
    print("Testing enhanced tooltips...")
    print("=" * 50)
    
    # Test with enhanced trajectory data
    print("\n1. Testing with full trajectory data:")
    plot_line_chart(trajectories, "Test Chart", "y_mm", trajectory_by_id)
    
    print("\n2. Testing without trajectory data (fallback):")
    plot_line_chart(trajectories, "Test Chart", "y_mm", None)
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    test_enhanced_tooltips()
