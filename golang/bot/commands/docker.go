package commands

import (
	"context"
	"fmt"
	"net"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/carbonrobotics/robot/golang/bot/version_metadata"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/bot/docker"
	"github.com/carbonrobotics/robot/golang/bot/parameters"
	"github.com/carbonrobotics/robot/golang/bot/util"
	"github.com/carbonrobotics/robot/golang/lib/auth"
	crgit "github.com/carbonrobotics/robot/golang/lib/git"
	"github.com/carbonrobotics/robot/golang/lib/releasesemver"
	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/config"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/go-git/go-git/v5/plumbing/transport/http"
	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v2"
)

const arm64Suffix = "-arm64"
const arm64Latest = "latest-arm64"

func BotBuild(opts parameters.BotBuildOpts) {
	logrus.Info("Bot Building: ", opts.Container, "with options:", opts)
	if opts.CiTag && opts.OptionalTag == nil {
		tag, err := crgit.BranchSHA(bot_context.RobotRepoPath)
		util.LogFatalError(err)
		opts.OptionalTag = append(opts.OptionalTag, tag)
	}

	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	allContainers, err := docker.LoadContainerDefs()
	util.LogFatalError(err)

	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	filterContainers := bot_context.GetContainerNamesSetFromProject(project, false)
	if opts.Container != "" {
		filterContainers[opts.Container] = true
		for _, c := range role.Services {
			filterContainers[c] = true
		}
	}
	var roleContainers []docker.Container
	if opts.All {
		roleContainers = allContainers
	} else {
		roleContainers = docker.FilterContainers(allContainers, filterContainers)
	}
	toBuild, err := docker.GetOrderedContainers(roleContainers, opts.Container)
	util.LogFatalError(err)

	err = docker.BuildContainers(toBuild, cli, botDef, true, botDef.RobotDef.Computer.Role, opts)
	util.LogFatalError(err)

	// Pull to retag ...-<platform> containers to latest
	if opts.Push && opts.Release == "" && opts.Platform != "amd64" {
		BotPull(PullOpts{Platform: opts.Platform, Container: opts.Container, All: opts.All, Release: opts.Release})
	}
}

func BotTag(container string, source string, tag string, all bool, registrySrc string, registryDst string) {
	if tag == "" {
		logrus.Fatal("Must provided a target tag: --tag")
	}

	logrus.Info("Bot Tagging: ", container)
	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	allContainers, err := docker.LoadContainerDefs()
	util.LogFatalError(err)

	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	filterContainers := bot_context.GetContainerNamesSetFromProject(project, false)
	if container != "" {
		filterContainers[container] = true
		for _, c := range role.Services {
			filterContainers[c] = true
		}
	}
	var roleContainers []docker.Container
	if all {
		roleContainers = allContainers
	} else {
		roleContainers = docker.FilterContainers(allContainers, filterContainers)
	}

	toBuild, err := docker.GetOrderedContainers(roleContainers, container)
	util.LogFatalError(err)

	err = docker.TagContainers(docker.TagContainersOpts{
		Containers:  toBuild,
		Cli:         cli,
		BotDef:      botDef,
		Source:      source,
		Tag:         tag,
		RegistrySrc: registrySrc,
		RegistryDst: registryDst,
	})
	util.LogFatalError(err)
}

func BotContainers(all bool) {
	logrus.Info("Listing Bot Containers: ")
	allContainers, err := docker.LoadContainerDefs()
	util.LogFatalError(err)

	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	filterContainers := bot_context.GetContainerNamesSetFromProject(project, false)
	var roleContainers []docker.Container
	if all {
		roleContainers = allContainers
	} else {
		roleContainers = docker.FilterContainers(allContainers, filterContainers)
	}

	for _, container := range roleContainers {
		fmt.Println(container.Name)
	}
}

type PushOpts struct {
	Container string
	Release   string
	RemoteTag string
	CiTag     bool
	All       bool
	Platform  string
	Registry  string
}

func BotPush(opts PushOpts) {
	logrus.Info("Bot Pushing: ", opts.Container)

	if opts.Release == "" && opts.Platform == "arm64" {
		opts.Release = arm64Latest
		BotTag(opts.Container, "latest", opts.Release, opts.All, "", opts.Registry)
	} else if opts.Registry != "" {
		BotTag(opts.Container, opts.Release, opts.Release, opts.All, "", opts.Registry)
	}

	if opts.CiTag && opts.RemoteTag == "" {
		tag, err := crgit.BranchSHA("/robot")
		util.LogFatalError(err)
		opts.RemoteTag = tag
	}

	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	allContainers, err := docker.LoadContainerDefs()
	util.LogFatalError(err)

	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	filterContainers := bot_context.GetContainerNamesSetFromProject(project, false)
	var roleContainers []docker.Container
	if opts.All {
		roleContainers = allContainers
	} else {
		roleContainers = docker.FilterContainers(allContainers, filterContainers)
	}

	if opts.Container == "" {
		for _, c := range roleContainers {
			err = c.Push(cli, opts.Release, true, opts.RemoteTag, opts.Registry)
			util.LogFatalError(err)
		}
	} else {
		c, err := docker.GetContainer(allContainers, opts.Container)
		util.LogFatalError(err)

		err = c.Push(cli, opts.Release, true, opts.RemoteTag, opts.Registry)
		util.LogFatalError(err)
	}
}

type PullOpts struct {
	Container string
	Release   string
	All       bool
	Gobot     bool
	Platform  string
	Registry  string
}

func BotPull(opts PullOpts) {
	logrus.Info("Bot Pulling: ", opts.Container)

	if opts.Platform == "arm64" {
		if opts.Release == "" {
			opts.Release = arm64Latest
		} else if !strings.HasSuffix(opts.Release, arm64Suffix) {
			opts.Release += arm64Suffix
		}
	}

	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	allContainers, err := docker.LoadContainerDefs()
	util.LogFatalError(err)

	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	filterContainers := bot_context.GetContainerNamesSetFromProject(project, opts.Gobot)
	var roleContainers []docker.Container
	if opts.All {
		roleContainers = allContainers
	} else {
		roleContainers = docker.FilterContainers(allContainers, filterContainers)
	}

	if opts.Container == "" {
		errors := []error{}
		for _, c := range roleContainers {
			err = c.Pull(cli, opts.Release, true, opts.Registry)
			if err != nil {
				errors = append(errors, err)
				continue
			}

			if opts.Registry != "" {
				err = c.Tag(cli, opts.Release, opts.Release, opts.Registry, "")
				if err != nil {
					errors = append(errors, err)
				}
			}
		}
		if len(errors) > 0 {
			for _, err := range errors {
				logrus.WithError(err).Error("Error Pulling")
			}
			util.LogFatalError(fmt.Errorf("Error Pulling"))
		}
	} else {
		c, err := docker.GetContainer(allContainers, opts.Container)
		util.LogFatalError(err)

		err = c.Pull(cli, opts.Release, true, opts.Registry)
		util.LogFatalError(err)

		if opts.Registry != "" {
			err = c.Tag(cli, opts.Release, opts.Release, opts.Registry, "")
			util.LogFatalError(err)
		}
	}

	newTag, found := strings.CutSuffix(opts.Release, arm64Suffix)
	if found {
		BotTag(opts.Container, opts.Release, newTag, opts.All, "", "")
	}
}

func BotRun(container string, command []string, release string, gpus bool, privileged bool, user string, containerName string, runAsProd bool) {
	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	allContainers, err := docker.LoadContainerDefs()
	util.LogFatalError(err)

	botDef, err := bot_context.LoadBotDefForUser(user)
	util.LogFatalError(err)

	if runAsProd {
		botDef.CheckoutDef.Mode = "prod"
	}

	c, err := docker.GetContainer(allContainers, container)
	util.LogFatalError(err)

	releaseStr := botDef.VersionDef.Tag
	if release != "" {
		releaseStr = release
	}
	logrus.Info("Running command: ", strings.Join(command, " "), " in ", container, ":", releaseStr)

	err = docker.RunCommandInContainer(cli, &c, botDef, command, releaseStr, gpus, privileged, containerName)
	util.LogFatalError(err)
}

func BotDevRelease(release, id, version string, buildArgs map[string]string, multiarch bool, platform string) {
	logrus.Infof("Starting dev-release %v", release)
	BotBuild(parameters.BotBuildOpts{
		Container:         "",
		Release:           release,
		Fast:              false,
		All:               true,
		ID:                id,
		Version:           version,
		BuildArgs:         buildArgs,
		OptionalTag:       nil,
		OptionalCacheFrom: nil,
		Multiarch:         multiarch,
		Platform:          platform,
		Push:              true,
		CiTag:             false,
		NoBuildCmd:        false,
	})
	GitTagPush(release)
	BotPush(PushOpts{
		Release:   release,
		RemoteTag: release,
		All:       true,
		Platform:  platform,
	})
}

func BotDLBuildPush(version string, slow bool) {
	buildArgs := make(map[string]string)
	commitHash, err := crgit.CommitHash(bot_context.RobotRepoPath)
	util.LogFatalError(err)
	pushOpts := PushOpts{
		Container: "deeplearning",
		Release:   commitHash,
		RemoteTag: "",
		CiTag:     false,
		All:       true,
		Platform:  runtime.GOARCH,
	}
	if slow {
		BotPull(PullOpts{
			Container: "common",
			Release:   "latest",
			All:       true,
			Gobot:     false,
			Platform:  runtime.GOARCH,
		})
		BotBuild(parameters.BotBuildOpts{
			Container:         "deeplearning",
			Release:           commitHash,
			Fast:              false,
			All:               true,
			ID:                commitHash,
			Version:           version,
			BuildArgs:         buildArgs,
			OptionalTag:       nil,
			OptionalCacheFrom: nil,
			Multiarch:         false,
			Platform:          runtime.GOARCH,
			Push:              false,
			CiTag:             false,
			NoBuildCmd:        false,
		})
		BotPush(pushOpts)
	} else {
		BotPull(PullOpts{
			Container: "deeplearning",
			Release:   version,
			All:       true,
			Gobot:     false,
			Platform:  runtime.GOARCH,
		})
		BotPull(PullOpts{
			Container: "common",
			Release:   version,
			All:       true,
			Gobot:     false,
			Platform:  runtime.GOARCH,
		})
		BotTag("deeplearning", version, "latest", true, "", "")
		BotBuild(parameters.BotBuildOpts{
			Container:         "deeplearning",
			Release:           commitHash,
			Fast:              true,
			All:               true,
			ID:                commitHash,
			Version:           version,
			BuildArgs:         buildArgs,
			OptionalTag:       nil,
			OptionalCacheFrom: nil,
			Multiarch:         false,
			Platform:          runtime.GOARCH,
			Push:              false,
			CiTag:             false,
			NoBuildCmd:        false,
		})
		BotPush(pushOpts)
	}
	logrus.Infof("Built and pushed %s", commitHash)
}

type SystemVersion struct {
	Version string `yaml:"version"`
}

func getSystemVersion(rootDir string) (string, error) {
	content, err := os.ReadFile(rootDir + "/system_version.yaml")
	if err != nil {
		return "", err
	}

	systemVersion := SystemVersion{}

	err = yaml.Unmarshal(content, &systemVersion)
	if err != nil {
		return "", err
	}

	return systemVersion.Version, nil
}

func GitTagPush(release string) {
	logrus.Info("Starting git tag and push to remote")
	repo, err := git.PlainOpen(bot_context.RobotRepoPath)
	if err != nil {
		util.LogFatalError(err)
	}

	_, err = repo.Remote("origin")
	if err != nil {
		util.LogFatalError(err)
	}

	logrus.Info("Connected to git remote/origin")

	h, err := repo.Head()
	if err != nil {
		util.LogFatalError(err)
	}

	coDef, err := bot_context.LoadCheckoutDef()
	if err != nil {
		util.LogFatalError(err)
	}

	logrus.Info("Creating tag " + release + " by user " + coDef.User)
	_, err = repo.CreateTag(release, h.Hash(), &git.CreateTagOptions{
		Tagger: &object.Signature{
			Name:  coDef.User,
			Email: coDef.User + "@carbonrobotics.com",
			When:  time.Now(),
		},
		Message: "Automatic tag created by bot dev-release",
	})

	if err != nil {
		util.LogFatalError(err)
	}

	logrus.Info("Tag created: " + release)

	auth := &http.BasicAuth{
		Password: auth.GitHubAccessToken,
	}

	refspec := fmt.Sprintf("refs/tags/%v:refs/tags/%v", release, release)
	err = repo.Push(&git.PushOptions{
		RemoteName: "origin",
		Auth:       auth,
		RefSpecs:   []config.RefSpec{config.RefSpec(refspec)},
	})

	if err != nil && err != git.NoErrAlreadyUpToDate {
		util.LogFatalError(err)
	}
	logrus.Info("Tag pushed to remote")
}

func BotVerifyImages(container string, release string) {
	logrus.Infof("Verifying container(s) using checksums")

	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	allContainers, err := docker.LoadContainerDefs()
	util.LogFatalError(err)

	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)
	botDef.CheckoutDef.Mode = "prod" // always verify as prod to avoid robot mount

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	var containersToTest []docker.Container

	if container != "" {
		// Test a specific container
		c, err := docker.GetContainer(allContainers, container)
		util.LogFatalError(err)
		containersToTest = append(containersToTest, c)
	} else {
		// Test all containers
		filterContainers := bot_context.GetContainerNamesSetFromProject(project, false)
		containersToTest = docker.FilterContainers(allContainers, filterContainers)
		logrus.Infof("Testing all containers: %d found", len(containersToTest))
	}

	tag := "latest"
	if release != "" {
		tag = release
	}

	successCount := 0
	failed := make([]string, 0)

	for _, c := range containersToTest {
		fullName := c.TaggedImage(tag)
		logrus.Infof("Verifying image: %s", fullName)
		err = docker.VerifyImageUsingChecksums(context.Background(), cli, fullName)
		if err != nil {
			logrus.WithError(err).Errorf("Failed to verify image: %s", fullName)
			failed = append(failed, c.Name)
		} else {
			logrus.Infof("Successfully verified image: %s", fullName)
			successCount++
		}
	}

	logrus.Infof("Verification complete: %d succeeded, %d failed", successCount, len(failed))
	if len(failed) > 0 {
		logrus.Errorf("Failed to verify the following containers: %v", failed)
	}
}

func BotRelease(opts parameters.BotBuildOpts) {
	logrus.Infof("Starting release %v", opts.Release)

	if !opts.SkipSemverCheck {
		err := releasesemver.IsValidSemver(opts.Release)
		if err != nil {
			logrus.Fatalf("Invalid semantic version: %v", err)
		}
	}

	sysver, err := getSystemVersion("/robot")
	if err != nil {
		logrus.Fatalf("Could not read system version, err=%v", err)
	}

	opts.OptionalTag = append(opts.OptionalTag, sysver)

	BotBuild(opts)

	env, err := bot_context.LoadRobotDef()
	if err != nil {
		util.LogFatalError(err)
	}

	if opts.Platform == "amd64" {
		uploadVersionMetadata(opts.Release, sysver, env)
	}
}

func uploadVersionMetadata(version string, systemVersion string, env *bot_context.RobotDef) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if env.Generation == "" {
		env.Generation = "slayer" // needed for auth
	}
	// TODO:(smt) add version meta to bot context static urls, not even a url =/
	serviceURL := bot_context.VersionMetadataServiceProdHost
	if _, port, _ := net.SplitHostPort(serviceURL); port == "" {
		serviceURL = net.JoinHostPort(serviceURL, "443") // default to 443
	}
	os.Setenv("MAKA_ROBOT_DIR", "/robot")
	os.Setenv("MAKA_ROBOT_NAME", env.Name)
	os.Setenv("MAKA_GEN", env.Generation)
	os.Setenv("CARBON_ROBOT_USERNAME", env.CarbonRobotUsername)
	os.Setenv("CARBON_ROBOT_PASSWORD", env.CarbonRobotPassword)
	if err := version_metadata.UploadVersionMetadata(ctx, version, systemVersion, bot_context.IDMServiceProdHost, serviceURL); err != nil {
		util.LogFatalError(err)
	}
}
