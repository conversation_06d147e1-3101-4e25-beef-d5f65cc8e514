#pragma once

#include <c10/cuda/CUDAStream.h>
#include <glm/glm.hpp>
#include <torch/torch.h>

#include "lib/common/model/cpp/atomic_model.h"

namespace F = torch::nn::functional;

namespace cv::comparison {

using namespace lib::common;

struct ComparisonRawOutput {
public:
  ComparisonRawOutput(std::vector<torch::Tensor> output) {
    auto output_ptr = output.begin();
    int strides_len = 1;
    version = output[0].item().to<int>();
    output_ptr += strides_len;
    embedding = std::vector<torch::Tensor>(output_ptr, output_ptr + 1);
    output_ptr += strides_len;
  }

  c10::DeviceIndex device() { return embedding[0].device().index(); }

  int version;
  std::vector<torch::Tensor> embedding;
};

struct ComparisonOutput {
  std::vector<torch::Tensor> embedding;
};

class ComparisonModel {
public:
  ComparisonModel(model::AtomicModel model);

  const ModelMetadataProto &get_internal_metadata();
  const nlohmann::json &get_external_metadata();

  std::tuple<int, int> get_input_size();
  ComparisonOutput infer(torch::Tensor image, int x, int y);
  torch::Tensor infer_chip(torch::Tensor image);

private:
  torch::Tensor normalize(torch::Tensor image);
  static glm::vec2 adjust_size_ppi(glm::vec2 coord, float ppi, float new_ppi) { return coord / ppi * new_ppi; }
  torch::Tensor batch_infer(torch::Tensor image_tensor);

  model::AtomicModel model_;
  torch::Tensor means_;
  torch::Tensor stds_;
  glm::ivec2 input_size_;
  int batch_size_;
  glm::ivec2 tile_size_;

  int gpu_id_;
};

} // namespace cv::comparison
