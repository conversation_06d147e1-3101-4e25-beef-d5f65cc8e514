import asyncio
from typing import Awaitable, List, cast

from config.client.cpp.config_client_python import ConfigSubscriber
from hardware_manager.python.boards.board import HardwareManagedBoard
from hardware_manager.python.boards.cruise_if import CCStatus
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.boards.jimbox.jimbox_device import JimboxBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.logging import get_logger
from lib.common.time.sleep import async_sleep_ms
from lib.common.time.time import maka_control_timestamp_ms
from lib.common.units.speed import ZERO_SPEED, Speed
from lib.drivers.nanopb.jimbox_board.jimbox_board_connector import (
    JOHN_DEERE_TRANSMISSION_SPEED_MPH,
    JOHN_DEERE_TRANSMISSION_SPEED_MPH_KPH,
    TREADKILL_TRANSMISSION_SPEED_MPH,
)

LOG = get_logger(__name__)

UPDATE_INTERVAL_MS = 1000
BOARD_NAME = "jimbox"


class JimboxBoard(HardwareManagedBoard):
    def __init__(self, device_registry: DeviceRegistry, config_subscriber: ConfigSubscriber) -> None:
        super().__init__(device_registry, f"{BOARD_NAME}_1", JimboxBoardDevice, BOARD_NAME)
        self._config_subscriber = config_subscriber
        self._enabled = False
        self._homed = False
        self.transmission_speed_ratio = 1.0
        self.treadkill_mode_enable = False
        self.constant_speed_mode_enable = False
        self.constant_speed_mode_setpoint = 0.0
        self.homing_mode_enable = False
        self.homing_tick_count = -30
        self.average_transmission_speed_ratio = 0.0
        self.tractor_using_kph = False

    async def start(self) -> List[Awaitable[None]]:
        return [asyncio.create_task(self._run_forever())]

    async def _await_board(self) -> None:
        self._ok = False
        LOG.error("Jimbox board is unresponsive, reducing rate and attempting to re-establish communication.")
        while not bot_stop_handler.stopped:
            try:
                await cast(JimboxBoardDevice, self._board).ping()
                break
            except Exception:
                await asyncio.sleep(1)
        self._ok = True

    async def _check_jimbox_config(self) -> None:
        jimbox_config_tree = self._config_subscriber.get_config_node("hardware_manager", "jimbox")
        transmission_speed_ratio = jimbox_config_tree.get_node("transmission_speed_ratio").get_float_value()
        treadkill_mode_enable = jimbox_config_tree.get_node("treadkill_mode_enable").get_bool_value()
        constant_speed_mode_enable = jimbox_config_tree.get_node("constant_speed_mode_enable").get_bool_value()
        constant_speed_mode_setpoint = jimbox_config_tree.get_node("constant_speed_mode_speed").get_float_value()
        homing_mode_enable = jimbox_config_tree.get_node("homing_mode_enable").get_bool_value()
        homing_tick_count = jimbox_config_tree.get_node("homing_tick_count").get_int_value()
        tractor_using_kph = jimbox_config_tree.get_node("tractor_using_kph").get_bool_value()

        if transmission_speed_ratio < 0.5:
            LOG.warning(f"Transmission speed ratio {transmission_speed_ratio} is less than 0.5, setting to 0.5")
            transmission_speed_ratio = 0.5

        if (
            treadkill_mode_enable != self.treadkill_mode_enable
            or tractor_using_kph != self.tractor_using_kph
            or transmission_speed_ratio != self.transmission_speed_ratio
        ):
            LOG.info(f"Setting treadkill_mode_enable: {treadkill_mode_enable}")
            assert self._board is not None
            if treadkill_mode_enable is True:
                cast(JimboxBoardDevice, self._board).set_speed_mapping(
                    TREADKILL_TRANSMISSION_SPEED_MPH, transmission_speed_ratio
                )
            elif tractor_using_kph is True:
                cast(JimboxBoardDevice, self._board).set_speed_mapping(
                    JOHN_DEERE_TRANSMISSION_SPEED_MPH_KPH, transmission_speed_ratio
                )
            else:
                cast(JimboxBoardDevice, self._board).set_speed_mapping(
                    JOHN_DEERE_TRANSMISSION_SPEED_MPH, transmission_speed_ratio
                )

        self.transmission_speed_ratio = transmission_speed_ratio
        self.treadkill_mode_enable = treadkill_mode_enable
        self.constant_speed_mode_enable = constant_speed_mode_enable
        self.constant_speed_mode_setpoint = constant_speed_mode_setpoint
        self.homing_mode_enable = homing_mode_enable
        self.homing_tick_count = homing_tick_count
        self.actual_speed_zero_count = 0
        self.tractor_using_kph = tractor_using_kph

    async def set_speed(self, target_speed: Speed, current_speed: Speed) -> Speed:
        assert self._board is not None

        if await self.get_enabled() is False:
            return ZERO_SPEED()

        if self.homing_mode_enable is True and self._homed is False:
            return ZERO_SPEED()

        if self.constant_speed_mode_enable:
            return Speed.from_mph(self.constant_speed_mode_setpoint)

        if current_speed.mph < 0.009:
            LOG.info(f"Jimbox speed zero {self.actual_speed_zero_count}")
            self.actual_speed_zero_count = self.actual_speed_zero_count + 1
            await self.set_enabled(False)
            if self.actual_speed_zero_count > 5:
                LOG.info("Jimbox disabled zero")
                await self.set_enabled(False)
                self.actual_speed_zero_count = 0
        else:
            self.actual_speed_zero_count = 0

        # Send speed command
        try:
            current_trans_speed = await cast(JimboxBoardDevice, self._board).get_speed()
            # speed_lever = await cast(JimboxBoardDevice, self._board).get_lever_pos()
            # used_ratio = self.transmission_speed_ratio * (0.37 + (speed_lever * 0.63))
            # calculate rolling average of transmission speed ratio
            actual_ratio = current_speed.mph / current_trans_speed
            self.average_transmission_speed_ratio = (self.average_transmission_speed_ratio + actual_ratio) / 2
            # log average transmission speed ratio every 10 minutes
            if maka_control_timestamp_ms() % 600000 < 100:
                LOG.info(f"Average Transmission Ratio {self.average_transmission_speed_ratio}")
            if current_trans_speed == 0.0:
                return ZERO_SPEED()
            new_speed = await cast(JimboxBoardDevice, self._board).set_speed(target_speed.mph)
            # if current_speed != new_speed:
            # LOG.info(f"Jimbox speed setpoint changed from {current_speed} to {new_speed}")
            return Speed.from_mph(new_speed)
        except Exception:
            LOG.exception("Failed to set tractor speed")
            return ZERO_SPEED()

    async def get_speed(self) -> Speed:
        try:
            assert self._board is not None
            return Speed.from_mph(await cast(JimboxBoardDevice, self._board).get_speed())
        except Exception:
            LOG.exception("Failed to get speed from jimbox")
            return ZERO_SPEED()

    async def get_enabled(self) -> bool:
        try:
            assert self._board is not None
            enabled = await cast(JimboxBoardDevice, self._board).get_enabled()
            self._enabled = enabled
            return enabled
        except Exception:
            LOG.exception("Failed to get status from jimbox")
            return False

    async def set_enabled(self, new_enabled: bool) -> bool:
        try:
            assert self._board is not None
            if self._enabled != new_enabled:
                LOG.info(f"Setting jimbox enabled: {new_enabled}")
                self._homed = False
                await cast(JimboxBoardDevice, self._board).set_enabled(new_enabled)
            self._enabled = new_enabled
            return True
        except Exception:
            LOG.exception("Failed to set jimbox status")
            return False

    async def get_allow_enable(self) -> bool:
        try:
            assert self._board is not None
            return await cast(JimboxBoardDevice, self._board).get_allow_enable()
        except Exception:
            LOG.exception("Failed to get allow enable from jimbox")
            return False

    async def _run_forever(self) -> None:
        LOG.info("Jimbox running forever")
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        assert self._board is not None
        try:
            last_iteration = maka_control_timestamp_ms()
            error_count = 0
            while not bot_stop_handler.stopped:
                if maka_control_timestamp_ms() - last_iteration < UPDATE_INTERVAL_MS:
                    await async_sleep_ms(50)
                    continue
                try:
                    await cast(JimboxBoardDevice, self._board).ping()
                    await self._check_jimbox_config()
                    if await cast(JimboxBoardDevice, self._board).get_allow_enable() is False:
                        await cast(JimboxBoardDevice, self._board).set_enabled(False)
                    if self.constant_speed_mode_enable:
                        await cast(JimboxBoardDevice, self._board).set_speed(self.constant_speed_mode_setpoint)
                    error_count = 0
                except Exception:
                    error_count += 1
                    if error_count > 5:
                        self.transmission_speed_ratio = 1.0
                        await self._await_board()
                await asyncio.sleep(0.5)
        finally:
            await cast(JimboxBoardDevice, self._board).set_enabled(False)
            stop_blocker_event.set()

    async def get_status(self) -> CCStatus:
        return CCStatus(
            installed=True,
            allow_enabled=await self.get_allow_enable(),
            enabled=await self.get_enabled(),
            speed=await self.get_speed(),
        )
