import asyncio
import traceback
from copy import deepcopy
from typing import Dict, List, Optional

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.job_metric import JobMetric
from lib.common.tasks.owner import Owner

LOG = get_logger(__name__)


class JobMetricAggregator(Owner):
    def __init__(self, redis: aioredis.Redis):
        self._computed: Dict[str, str] = {}
        self._lock = asyncio.Lock()
        self._metrics: List[JobMetric] = []
        self._task: Optional[asyncio.Task[None]] = None
        self._redis = redis

    async def getMetrics(self) -> Dict[str, str]:
        async with self._lock:
            return deepcopy(self._computed)

    async def start(self) -> None:
        LOG.info("Job metrics aggregator starting")
        try:
            await asyncio.gather(*[metric.start() for metric in self._metrics])
            if self._task is None:
                self._task = asyncio.get_event_loop().create_task(self._loop())
        except Exception:
            LOG.error(traceback.format_exc())

    async def stop(self) -> None:
        await asyncio.gather(*[metric.stop() for metric in self._metrics])
        if self._task is not None:
            self._task.cancel()
            await self._task
            self._task = None

    def add_metric(self, metric: JobMetric) -> None:
        self._metrics.append(metric)

    async def _loop(self) -> None:
        cur_job_id: Optional[str] = None
        count = 0
        with bot_stop_handler.scoped_bot_stop_blocker("job_metrics_aggregator") as bot_stop:
            while not bot_stop.is_stopped():
                try:
                    job_id: Optional[str] = await self._redis.get("jobs/active_job")
                    if job_id != cur_job_id:
                        LOG.info(f"JobMetrics: new job id {job_id}")
                        metrics: Dict[str, str] = await self._redis.hgetall(
                            f"jobs/metrics/{job_id}"
                        ) if job_id is not None else {}
                        LOG.info(f"JobMetrics: for job_id {job_id} read metrics {metrics}")
                        for m in self._metrics:
                            await m.reload(job_id, metrics)
                        cur_job_id = job_id
                    if cur_job_id is not None:
                        key_map: Dict[str, str] = {}
                        for m in self._metrics:
                            await m.add_metrics(key_map)
                        if count % 6 == 0:  # log about once a minute
                            LOG.info(f"JobMetrics: writing for job {cur_job_id} metrics {key_map}")
                        await self._redis.hmset(f"jobs/metrics/{cur_job_id}", key_map)
                        async with self._lock:
                            self._computed = deepcopy(key_map)
                except Exception:
                    LOG.error("JobMetrics: exception: " + traceback.format_exc())
                await asyncio.sleep(10)
                count += 1
