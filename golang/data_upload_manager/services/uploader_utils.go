package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math/rand"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
)

func getUploadedLogPath(filename string) string {
	return fmt.Sprintf("%v/%v", getDataDir(), filename)
}

func SaveUploadedLogs(timestamps []TimestampedFile, filename string) error {
	timestamps_bytes, err := json.Marshal(timestamps)
	if err = ioutil.WriteFile(getUploadedLogPath(filename), timestamps_bytes, 0777); err != nil {
		return err
	}
	return nil
}

func CleanDir(directory string, cleanupDelay time.Duration) error {
	logrus.Infof("Removing directories older than %v from %v", cleanupDelay, directory)
	files, err := ioutil.ReadDir(directory)
	if err != nil {
		return err
	}

	for _, file := range files {
		if time.Now().Sub(file.ModTime()) > cleanupDelay {
			filepath := fmt.Sprintf("%v/%v", directory, file.Name())
			logrus.Infof("Removing file or directory: %v", filepath)
			appFs.RemoveAll(filepath)
		}
	}

	return nil
}

func getRandomMapKey[K comparable, V any](m map[K]V) K {
	keys := make([]K, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys[rand.Intn(len(keys))]
}

func GetCVClientForCamId(rowClient *rows.RowClients, camID string) (*cv_runtime_client.CVRuntimeClient, error) {
	type Result struct {
		client *cv_runtime_client.CVRuntimeClient
		resp   *cv.GetCameraInfoResponse
		err    error
	}
	var retErr error
	wg := sync.WaitGroup{}
	results := make(chan Result, len(rowClient.CVRuntimeClients))
	for _, client := range rowClient.CVRuntimeClients {
		wg.Add(1)
		go func(client *cv_runtime_client.CVRuntimeClient) {
			defer wg.Done()
			resp, err := client.GetCameraInfo()
			res := Result{
				client: client,
				resp:   resp,
				err:    err,
			}
			results <- res
		}(client)
	}
	wg.Wait()
	close(results)
	for res := range results {
		if res.err != nil {
			retErr = errors.Join(retErr, res.err)
			continue
		}
		for _, camInfo := range res.resp.GetCameraInfo() {
			if camInfo.GetCamId() == camID {
				return res.client, nil
			}
		}
	}
	retErr = fmt.Errorf("no camera with ID %v found: %v", camID, retErr)
	return nil, retErr
}
