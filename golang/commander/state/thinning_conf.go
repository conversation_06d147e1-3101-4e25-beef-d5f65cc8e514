package state

import (
	"context"
	"errors"
	"fmt"
	"os/exec"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
	"github.com/carbonrobotics/robot/golang/generated/proto/thinning"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

const thinningConfKey = "/thinning/config"
const activeConfKey = "/thinning/active"

type ThinningConfState struct {
	ManagedStateImpl
	Configs                   map[string]*thinning.ConfigDefinition
	ActiveConfig              string
	redisClient               *redis.Client
	jobsState                 *JobsState
	rows                      map[int]*rows.RowClients
	localToSyncUpdateCallback func(string, frontend.ProfileType, bool) error
	localToSyncDeleteCallback func(string, frontend.ProfileType, bool) error
}

func NewThinningConfState(redisClient *redis.Client, jobsState *JobsState, rowClients map[int]*rows.RowClients) *ThinningConfState {
	UpdateThinningConf()
	state := &ThinningConfState{
		ManagedStateImpl:          ManagedStateImpl{name: "ThinningConfState"},
		redisClient:               redisClient,
		jobsState:                 jobsState,
		rows:                      rowClients,
		localToSyncUpdateCallback: nil,
		localToSyncDeleteCallback: nil,
	}
	state.Configs = make(map[string]*thinning.ConfigDefinition)
	state.initialize()
	state.init()
	return state
}

func (st *ThinningConfState) init() {
	configs, err := st.redisClient.HGetAll(thinningConfKey)
	if err != nil {
		logrus.Warnf("Failed to load from redis err: %v", err)
		return
	}
	active, _ := st.redisClient.ReadString(activeConfKey, "")
	st.WriteOnCurrent(func() {
		for key, val := range configs {
			conf := &thinning.ConfigDefinition{}
			err := proto.Unmarshal([]byte(val), conf)
			if err == nil {
				st.Configs[key] = conf
			} else {
				logrus.Infof("Failed to load config %v err: %v", key, err)
			}
		}
		st.ActiveConfig = active
	})
}

func (st *ThinningConfState) informRows() error {
	var wg sync.WaitGroup
	errs := make([]error, len(st.rows))
	index := 0
	for _, row := range st.rows {
		if row == nil {
			errs[index] = fmt.Errorf("row %v is nil", index+1)
			index++
			continue
		} else if row.AimbotClient == nil {
			errs[index] = fmt.Errorf("row %v aimbot client is nil", index+1)
			index++
			continue
		}

		wg.Add(1)
		go func(idx int, rw *rows.RowClients) {
			defer wg.Done()
			errs[idx] = rw.AimbotClient.ReloadThinningConf()
		}(index, row)
		index++
	}
	wg.Wait()
	combinedErr := errors.Join(errs...)
	if combinedErr != nil {
		return fmt.Errorf("failed to notify rows: %v", combinedErr)
	}
	return nil
}

func (st *ThinningConfState) addConfig(conf *thinning.ConfigDefinition, setActive bool) (string, error) {
	var writeErr error
	st.WriteOnCurrent(func() {
		if conf.GetId() != "" {
			_, ok := st.Configs[conf.GetId()]
			if !ok {
				logrus.Warnf("%v is an unknown configuration. Generating treating as new config.", conf.GetId())
				conf.Id = ""
			}
		}
		if conf.GetId() == "" {
			conf.Id = uuid.New().String()
		}

		data, err := proto.Marshal(conf)
		if err != nil {
			writeErr = fmt.Errorf("failed to marshal protobuf data err: %v", err)
			return
		}

		// TODO(jfroel) should be transactional and use version numbering to avoid race conditions on local state (move out of local state)
		setCtx, setCancel := context.WithTimeout(context.Background(), redisTimeout)
		defer setCancel()
		err = st.redisClient.HSetWithContext(setCtx, thinningConfKey, conf.Id, string(data))
		if err != nil {
			writeErr = fmt.Errorf("failed to write config to redis err: %v", err)
			return
		}
		st.Configs[conf.Id] = conf

		if setActive {
			writeCtx, writeCancel := context.WithTimeout(context.Background(), redisTimeout)
			defer writeCancel()
			err = st.redisClient.WriteStringWithContext(writeCtx, activeConfKey, conf.GetId())
			if err != nil {
				writeErr = fmt.Errorf("failed to write active config to redis err: %v", err)
				return
			}
			st.ActiveConfig = conf.GetId()
		}
	})
	if writeErr != nil {
		return "", writeErr
	}
	if setActive {
		err := st.jobsState.UpdateActiveProfile(conf.Id, conf.Name, frontend.ProfileType_THINNING)
		if err != nil {
			return "", fmt.Errorf("failed to update active profile err: %v", err)
		}

		err = st.informRows()
		if err != nil {
			return "", fmt.Errorf("failed to inform rows err: %v", err)
		}
	}
	return conf.Id, nil
}
func (st *ThinningConfState) AddConfig(conf *thinning.ConfigDefinition, setActive bool) (string, error) {
	id, err := st.addConfig(conf, setActive)
	if err != nil {
		return "", err
	}
	err = st.callUpdateCB(id)
	return id, err
}

func (st *ThinningConfState) deleteConfig(id string, newActiveId string) error {
	if id == st.GetActiveId() && id != newActiveId {
		err := st.SetActiveConfig(newActiveId)
		if err != nil {
			return fmt.Errorf("failed to set new active config %v: %v", newActiveId, err)
		}
	}
	var err error
	st.ConditionalWriteOnCurrent(func() bool {
		if st.ActiveConfig == id {
			err = fmt.Errorf("cannot delete config %v as it is currently active", id)
			return false // no write
		}
		_, ok := st.Configs[id]
		if !ok {
			err = fmt.Errorf("no config with id %v exists", id)
			return false // no write
		}

		delCtx, delCancel := context.WithTimeout(context.Background(), redisTimeout)
		defer delCancel()
		err = st.redisClient.HDelWithContext(delCtx, thinningConfKey, id)
		if err != nil {
			err = fmt.Errorf("failed to delete config from redis err: %v", err)
			return false // no write
		}
		delete(st.Configs, id)
		return true // write
	})
	return err
}
func (st *ThinningConfState) DeleteConfig(id string, newActiveId string) error {
	err := st.deleteConfig(id, newActiveId)
	if err != nil {
		return err
	}
	err = st.callDeleteCB(id)
	return err
}

func (st *ThinningConfState) GetActiveId() string {
	activeId := ""
	st.ReadOnCurrent(func() {
		activeId = st.ActiveConfig
	})
	return activeId
}

func (st *ThinningConfState) GetActiveConfig() *thinning.ConfigDefinition {
	var active *thinning.ConfigDefinition = nil
	st.ReadOnCurrent(func() {
		val, ok := st.Configs[st.ActiveConfig]
		if ok {
			active = val
		}
	})
	return active
}

func (st *ThinningConfState) SetActiveConfig(id string) error {
	var err error
	st.ConditionalWriteOnCurrent(func() bool {
		_, ok := st.Configs[id]
		if !ok {
			err = fmt.Errorf("no config with id %v exists", id)
			return false // no write
		}
		if id == st.ActiveConfig {
			err = nil
			return false // No change but we were successful
		}

		err = st.redisClient.WriteString(activeConfKey, id)
		if err != nil {
			err = fmt.Errorf("failed to write active config to redis err: %v", err)
			return false // no write
		}

		st.ActiveConfig = id
		err = nil
		return true // write
	})
	if err != nil {
		return err
	}
	active := st.GetActiveConfig()
	err = st.jobsState.UpdateActiveProfile(active.Id, active.Name, frontend.ProfileType_THINNING)
	if err != nil {
		return err
	}
	err = st.informRows()
	return err
}
func (st *ThinningConfState) FindIdByName(name string) (string, error) {
	id := ""
	found := false
	st.ReadOnCurrent(func() {
		for key, val := range st.Configs {
			if val.GetName() == name {
				id = key
				found = true
				return
			}
		}
	})
	if found {
		return id, nil
	}
	return "", fmt.Errorf("no config found with name %v", name)
}

func (st *ThinningConfState) FindNameById(id string) string {
	val, ok := st.Configs[id]
	if ok {
		return val.GetName()
	}
	return ""
}
func (st *ThinningConfState) DeleteProfileFromSync(id string) error {
	err := st.deleteConfig(id, id) // Intentionally send new active as same to prevent deleting active id
	if err != nil {
		return fmt.Errorf("failed to delete thinning config %v: %v", id, err)
	}
	return nil
}
func (st *ThinningConfState) SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *portal.GetProfileResponse_Thinning:
		st.addConfig(r.Thinning, st.GetActiveId() == r.Thinning.Id)
		st.WriteOnCurrent(func() {})
		return true, nil
	default:
		return false, nil
	}
}
func (st *ThinningConfState) SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *robot_syncer.GetProfileResponse_Thinning:
		st.addConfig(r.Thinning, st.GetActiveId() == r.Thinning.Id)
		st.WriteOnCurrent(func() {})
		return true, nil
	default:
		return false, nil
	}
}
func (st *ThinningConfState) LoadProfileForSync(id string, req *portal.UploadProfileRequest) error {
	var thinningDef *thinning.ConfigDefinition
	st.ReadOnCurrent(func() {
		thinningDef = st.Configs[id]
	})
	if thinningDef == nil {
		err := fmt.Errorf("failed to load thinning def %v to sync", id)
		logrus.Warn(err.Error())
		return err
	}
	req.Profile = &portal.UploadProfileRequest_Thinning{
		Thinning: thinningDef,
	}
	return nil
}
func (st *ThinningConfState) LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error {
	var thinningDef *thinning.ConfigDefinition
	st.ReadOnCurrent(func() {
		thinningDef = st.Configs[id]
	})
	if thinningDef == nil {
		err := fmt.Errorf("failed to load thinning def %v to sync", id)
		logrus.Warn(err.Error())
		return err
	}
	req.Profile = &robot_syncer.UploadProfileRequest_Thinning{
		Thinning: thinningDef,
	}
	return nil
}
func (s *ThinningConfState) SetActiveProfileFromSync(id string) error {
	err := s.SetActiveConfig(id)
	if err != nil {
		return fmt.Errorf("failed to set %v as active: %v", id, err)
	}
	return nil
}
func (st *ThinningConfState) AddUpdateLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error) {
	st.Lock()
	defer st.Unlock()
	st.localToSyncUpdateCallback = cb
}
func (st *ThinningConfState) AddDeleteLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error) {
	st.Lock()
	defer st.Unlock()
	st.localToSyncDeleteCallback = cb
}
func (st *ThinningConfState) callDeleteCB(id string) error {
	st.RLock()
	cb := st.localToSyncDeleteCallback
	st.RUnlock()
	if cb != nil {
		return cb(id, frontend.ProfileType_THINNING, false)
	}
	return nil
}
func (st *ThinningConfState) callUpdateCB(id string) error {
	st.RLock()
	cb := st.localToSyncUpdateCallback
	st.RUnlock()
	if cb != nil {
		return cb(id, frontend.ProfileType_THINNING, false)
	}
	return nil
}
func (st *ThinningConfState) GetActiveProfileForJob() (string, string, frontend.ProfileType) {
	conf := st.GetActiveConfig()
	id := ""
	name := ""
	if conf != nil {
		id = conf.GetId()
		name = conf.GetName()
	}
	return id, name, frontend.ProfileType_THINNING
}

// Remove this in 1.17
func UpdateThinningConf() {
	cmd := exec.Command("python", "-m", "tools.thinning.thinning_conf_migrator")
	out, err := cmd.Output()
	if err != nil {
		logrus.Warnf("Update failed err: %v, out: %v", err, string(out))
	}
}
