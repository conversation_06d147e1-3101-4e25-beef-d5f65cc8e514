#include "deepweed_infer.h"
#include "cv/runtime/cpp/nodes/distance_buffer.h"
#include <fmt/format.h>
#include <prometheus/family.h>

#include <c10/cuda/CUDAGuard.h>
#include <fmt/format.h>
#include <google/protobuf/util/json_util.h>

#include "cv/runtime/proto/cv_runtime.pb.h"
#include "deeplearning/model_io/cpp/model_utils.h"
#include "lib/common/cpp/resize_util.h"
#include <config/client/cpp/config_subscriber.hpp>

namespace F = torch::nn::functional;

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

const float kBeamRadiusIn = (5.0f / 2.0f) / 25.4f;

constexpr uint32_t kPxMinX = 100;
constexpr uint32_t kPxMaxX = 3996;
constexpr uint32_t kPxMinY = 100;
constexpr uint32_t kPxMaxY = 2900;

DeepweedInfer::DeepweedInfer(
    camera::Camera camera, Input<camera::CameraImage> input,
    std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry, int gpu_id,
    std::shared_ptr<carbon::config::ConfigTree> deepweed_config,
    std::shared_ptr<carbon::config::ConfigTree> simulator_config, std::shared_ptr<prometheus::Registry> registry,
    std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>>
        distance_camera_buffers)
    : CVNodeImpl(fmt::format("/deepweed/{}", camera.get_info().camera_id)), input_(input),
      model_registry_(model_registry), gpu_id_(gpu_id),
      cuda_stream_(c10::cuda::getStreamFromPool(true, c10::DeviceIndex(gpu_id))), deepweed_config_(deepweed_config),
      simulator_config_(simulator_config), deepweed_metrics_(DeepweedMetrics(0.025f, 5.0f, 0.1f, 0.05f, registry)),
      distance_camera_buffers_(distance_camera_buffers) {
  deepweed_config_->register_callback([this]() {
    std::shared_lock<std::shared_mutex> infer_lock(infer_mutex_);
    if (!model_) {
      return;
    }
    std::unique_lock<std::mutex> mask_expression_lock(current_mask_expression_mutex_);
    segmentation_categories_ = get_segmentation_categories();
  });
  current_crop_id_config_ =
      carbon::config::get_global_config_subscriber()->get_config_node("commander", "current_crop_id");
  current_crop_id_config_->register_callback([this]() {
    std::shared_lock<std::shared_mutex> infer_lock(infer_mutex_);
    if (!model_) {
      return;
    }
    model_->set_crop_id(current_crop_id_config_->get_value<std::string>());
  });
  reload_model();
  if (simulator_config_ != nullptr) {
    simulator_wait_time_ = simulator_config_->get_node("deepweed_wait_time")->get_value<uint64_t>();
    simulator_config_->register_callback(
        [this]() { simulator_wait_time_ = simulator_config_->get_node("deepweed_wait_time")->get_value<uint64_t>(); });
  } else {
    simulator_wait_time_ = 220; // shouldn't be used unless in simulator
  }
}

void DeepweedInfer::clear_model() {
  std::unique_lock<std::shared_mutex> lock(infer_mutex_);
  model_.reset();
}

bool DeepweedInfer::ready() {
  bool ready = true;
  {
    std::shared_lock<std::shared_mutex> lck(infer_mutex_);
    ready &= model_ != nullptr;
  }
  return ready;
}

void DeepweedInfer::close_connectors() {
  input_.close();
  output_.close();
}

void DeepweedInfer::reload_model() {
  std::unique_lock<std::shared_mutex> lock(infer_mutex_);
  // Free old model before loading new model
  model_.reset();
  if (!model_registry_->contains(model::ModelUseCase::kPredict)) {
    return;
  }

  spdlog::info("Loading Deepweed model {} to GPU {}",
               model_registry_->get_path_for_use_case(model::ModelUseCase::kPredict), gpu_id_);
  try {
    auto trt_model = model_registry_->get(model::ModelUseCase::kPredict, gpu_id_);
    model_.reset(new deepweed::DeepweedModel(trt_model));
    model_->set_classifier(embeddings_classifier_);
    if (!model_->set_crop_id(current_crop_id_config_->get_value<std::string>())) {
      spdlog::warn("Unable to set deepweed crop id to {}", current_crop_id_config_->get_value<std::string>());
    }

    segmentation_categories_ = get_segmentation_categories();

    spdlog::info("Loaded Deepweed model {}", trt_model.get_model_path());
  } catch (const std::exception &e) {
    spdlog::warn("Unable to load Deepweed model: {}", e.what());
    return;
  }
}

void DeepweedInfer::set_classifier(std::shared_ptr<embeddings::Classifier> embeddings_classifier) {
  embeddings_classifier_ = embeddings_classifier;
  std::unique_lock<std::shared_mutex> lck(infer_mutex_);
  if (model_) {
    model_->set_classifier(embeddings_classifier_);
  }
}

void DeepweedInfer::clear_classifier() {
  embeddings_classifier_.reset();
  std::unique_lock<std::shared_mutex> lck(infer_mutex_);
  if (model_) {
    model_->set_classifier(nullptr);
  }
}

std::vector<std::string> DeepweedInfer::get_supported_segmentation_categories() {
  std::shared_lock<std::shared_mutex> lck(infer_mutex_);
  if (!model_) {
    return {};
  }
  return model_->get_supported_segmentation_categories();
}

std::vector<std::string> DeepweedInfer::get_supported_point_categories() {
  std::shared_lock<std::shared_mutex> lck(infer_mutex_);
  if (!model_) {
    return {};
  }
  return model_->get_supported_point_categories();
}

std::vector<proto::SegmentationDetectionCategory> DeepweedInfer::get_segmentation_categories() {
  std::set<std::string> categories_in_config;
  std::vector<proto::SegmentationDetectionCategory> segmentation_categories;
  std::set<std::string> supported_classes;
  if (model_->get_internal_metadata().segm_classes_size() > 0) {
    for (int i = 0; i < model_->get_internal_metadata().segm_classes_size(); i++) {
      supported_classes.insert(model_->get_internal_metadata().segm_classes(i));
    }
  }
  for (const auto &node : deepweed_config_->get_node("segmentationCategories")->get_children_nodes()) {
    categories_in_config.insert(node->get_name());
    if (!node->get_node("enabled")->get_value<bool>() || supported_classes.count(node->get_name()) == 0) {
      continue;
    }
    proto::SegmentationDetectionCategory segmentation_category;
    segmentation_category.set_category(node->get_name().c_str());
    segmentation_category.set_threshold((float)node->get_node("threshold")->get_value<double>());
    segmentation_category.set_safety_radius_in((float)node->get_node("safety_radius_in")->get_value<double>());
    segmentation_categories.push_back(segmentation_category);
  }
  return segmentation_categories;
}

float DeepweedInfer::get_weed_point_threshold() {
  return std::min((float)deepweed_config_->get_node("weed_point_threshold_diagnostics")->get_value<double>(),
                  std::min((float)deepweed_config_->get_node("weed_point_threshold_image_scoring")->get_value<double>(),
                           (float)deepweed_config_->get_node("weed_point_threshold")->get_value<double>()));
}

float DeepweedInfer::get_crop_point_threshold() {
  return std::min((float)deepweed_config_->get_node("crop_point_threshold_diagnostics")->get_value<double>(),
                  std::min((float)deepweed_config_->get_node("crop_point_threshold_image_scoring")->get_value<double>(),
                           (float)deepweed_config_->get_node("crop_point_threshold")->get_value<double>()));
}

float DeepweedInfer::get_plant_point_threshold() {
  return std::min(
      (float)deepweed_config_->get_node("plant_point_threshold_diagnostics")->get_value<double>(),
      std::min((float)deepweed_config_->get_node("plant_point_threshold_image_scoring")->get_value<double>(),
               (float)deepweed_config_->get_node("plant_point_threshold")->get_value<double>()));
}

std::optional<std::string> DeepweedInfer::get_model_id() {
  std::shared_lock<std::shared_mutex> lock(infer_mutex_);
  if (!model_ || !model_->get_external_metadata().contains("id")) {
    return {};
  }
  return model_->get_external_metadata()["id"].get<std::string>();
}

int64_t DeepweedInfer::process_simulator_data(camera::CameraImage &predict_image, bool only_plant_points) {
  std::this_thread::sleep_for(std::chrono::milliseconds(simulator_wait_time_));
  deepweed::DeepweedOutput output;
  std::vector<std::string> output_embedding_categories, weed_detection_classes;
  std::vector<std::string> mask_intersection_classes;

  uint32_t detection_id = 0;
  std::vector<std::string> embedding_categories;
  if (embeddings_classifier_) {
    embedding_categories = embeddings_classifier_->get_categories();
  }
  for (auto &p : predict_image.simulator_data.value().predictions) {
    if (p.detection_classes().empty() ||
        !((p.x_px() >= kPxMinX) && (p.x_px() < kPxMaxX) && (p.y_px() >= kPxMinY) && (p.y_px() < kPxMaxY))) {
      // using an empty set of detection classes to mean this weed/crop was not detected by model
      // also check it is in the 100px smaller box
      continue;
    }
    deepweed::DeepweedDetection d;
    d.x = (float)p.x_px();
    d.y = (float)p.y_px();

    d.size = (float)p.size_px();
    d.score = p.score();
    d.weed_score = p.weed_score();
    d.crop_score = p.crop_score();
    d.plant_score = p.plant_score();
    d.detection_id = detection_id;
    detection_id += 1;
    if (only_plant_points) {
      d.hit_class = cv::runtime::proto::PLANT;
    } else {
      d.hit_class = p.is_weed() ? cv::runtime::proto::WEED : cv::runtime::proto::CROP;
    }
    if (embeddings_classifier_ &&
        std::find(embedding_categories.begin(), embedding_categories.end(), "CROP") != embedding_categories.end()) {
      if (std::find(output_embedding_categories.begin(), output_embedding_categories.end(), "CROP") ==
          output_embedding_categories.end()) {
        output_embedding_categories.push_back("CROP");
      }
      d.embedding_category_distances.push_back(1.f - p.crop_score());
    }
    for (auto &dc : p.weed_detection_classes()) {
      d.weed_detection_class_scores.push_back(dc.second);
      if (embeddings_classifier_ &&
          std::find(embedding_categories.begin(), embedding_categories.end(), dc.first) != embedding_categories.end()) {
        d.embedding_category_distances.push_back(1.f - dc.second);
        if (std::find(output_embedding_categories.begin(), output_embedding_categories.end(), dc.first) ==
            output_embedding_categories.end()) {
          output_embedding_categories.push_back(dc.first);
        }
      }
      if (std::find(weed_detection_classes.begin(), weed_detection_classes.end(), dc.first) ==
          weed_detection_classes.end()) {
        weed_detection_classes.push_back(dc.first);
      }
    }

    for (float f : p.embedding()) {
      d.reduced_scaled_embedding.push_back(f);
    }
    for (auto &ic : p.mask_intersection_classes()) {
      auto pos = std::find(mask_intersection_classes.begin(), mask_intersection_classes.end(), ic);
      if (pos == mask_intersection_classes.end()) {
        mask_intersection_classes.push_back(ic);
      }
      pos = std::find(mask_intersection_classes.begin(), mask_intersection_classes.end(), ic);
      d.mask_intersections.push_back((int)std::distance(mask_intersection_classes.begin(), pos));
    }

    output.detections.push_back(d);
  }
  output.timestamp_ms = predict_image.timestamp_ms;
  if (embeddings_classifier_) {
    output.embedding_categories.insert(output.embedding_categories.end(), output_embedding_categories.begin(),
                                       output_embedding_categories.end());
  }
  output.mask_channel_classes = mask_intersection_classes;
  output.mask_channels = (int)mask_intersection_classes.size();

  output.weed_detection_classes =
      std::vector<std::string>(weed_detection_classes.begin(), weed_detection_classes.end());
  deepweed_metrics_.log(output);
  bool is_in_distance_buffer = false;
  if (distance_camera_buffers_->find(predict_image.camera_id) != distance_camera_buffers_->end() &&
      distance_camera_buffers_->at(predict_image.camera_id)->contains(predict_image.timestamp_ms)) {
    is_in_distance_buffer = true;
  }
  output.predict_in_distance_buffer = is_in_distance_buffer;
  output.available_for_snapshotting = predict_image.available_for_snapshot;
  set_state(CVNodeState::kPushing);
  output_.push(output);
  return output.timestamp_ms;
}

int64_t DeepweedInfer::tick() {
  set_state(CVNodeState::kPulling);
  camera::CameraImage predict_image = input_.pop();

  bool only_plant_points = true;
  set_state(CVNodeState::kProcessing);
  if (predict_image.simulator_data) {
    return process_simulator_data(predict_image, only_plant_points);
  }

  nvtxRangePushA("Deepweed_pre_processing");
  std::shared_lock<std::shared_mutex> lock(infer_mutex_);
  if (!model_) {
    return 0;
  }

  c10::cuda::CUDAStreamGuard stream_guard(cuda_stream_);

  std::vector<proto::PointDetectionCategory> point_categories_v1;
  std::vector<proto::SegmentationDetectionCategory> segmentation_categories;
  {
    std::unique_lock<std::mutex> expression_lock(current_mask_expression_mutex_);
    segmentation_categories = segmentation_categories_;
  }
  float weed_point_threshold = get_weed_point_threshold();
  float crop_point_threshold = get_crop_point_threshold();
  float plant_point_threshold = get_plant_point_threshold();

  const bool return_embeddings = false;
  auto deepweed_output =
      model_->infer(predict_image, segmentation_categories, weed_point_threshold, crop_point_threshold,
                    plant_point_threshold, return_embeddings, only_plant_points);

  bool is_in_distance_buffer = false;
  if (distance_camera_buffers_->find(predict_image.camera_id) != distance_camera_buffers_->end() &&
      distance_camera_buffers_->at(predict_image.camera_id)->contains(predict_image.timestamp_ms)) {
    is_in_distance_buffer = true;
  }
  deepweed_output.predict_in_distance_buffer = is_in_distance_buffer;
  deepweed_output.available_for_snapshotting = predict_image.available_for_snapshot;

  deepweed_metrics_.log(deepweed_output);
  set_state(CVNodeState::kPushing);
  output_.push(deepweed_output);

  return deepweed_output.timestamp_ms;
}

} // namespace nodes
} // namespace runtime
} // namespace cv
