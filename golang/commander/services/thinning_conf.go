package services

import (
	"context"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ThinningService struct {
	frontend.UnimplementedThinningServiceServer
	stState *state.ThinningConfState

	logger *logrus.Entry
}

func NewThinningService(grpcServer *grpc.Server, stState *state.ThinningConfState) *ThinningService {
	service := &ThinningService{
		stState: stState,
		logger:  logrus.WithField("module", "ThinningService"),
	}
	frontend.RegisterThinningServiceServer(grpcServer, service)
	return service
}

func (s *ThinningService) GetNextConfigurations(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextConfigurationsResponse, error) {
	var ts int64 = req.GetTimestampMs()
	resp := &frontend.GetNextConfigurationsResponse{}
	result := s.stState.ReadOnNext(ctx, ts, func() {
		resp.ActiveId = s.stState.ActiveConfig
		for _, val := range s.stState.Configs {
			if resp.ActiveId == val.Id {
				val.Active = true
			} else {
				val.Active = false
			}
			resp.Definitions = append(resp.Definitions, val)
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.stState.GetTimestampMs(),
		}
	})

	if result {
		return resp, nil
	}
	return nil, status.Error(codes.Aborted, "Context Cancelled before Tasks Retrieved")
}
func (s *ThinningService) GetNextActiveConf(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextActiveConfResponse, error) {
	var ts int64 = req.GetTimestampMs()
	resp := &frontend.GetNextActiveConfResponse{}
	result := s.stState.ReadOnNext(ctx, ts, func() {
		resp.Id = s.stState.ActiveConfig
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.stState.GetTimestampMs(),
		}
		resp.Name = s.stState.FindNameById(resp.Id)
	})

	if result {
		return resp, nil
	}
	return nil, status.Error(codes.Aborted, "Context Cancelled before Tasks Retrieved")
}
func (s *ThinningService) DefineConfiguration(ctx context.Context, req *frontend.DefineConfigurationRequest) (*frontend.DefineConfigurationResponse, error) {
	setActive := req.GetSetActive()
	if req.GetVer() == 0 {
		setActive = req.Definition.GetActive()
		id, err := s.stState.FindIdByName(req.Definition.GetName())
		if err == nil {
			req.Definition.Id = id
		}
	}
	id, err := s.stState.AddConfig(req.Definition, setActive)
	if err != nil {
		s.logger.Errorf("failed to add config %v", err)
		return nil, err
	}
	return &frontend.DefineConfigurationResponse{Id: id}, nil
}
func (s *ThinningService) SetActiveConfig(ctx context.Context, req *frontend.SetActiveConfigRequest) (*frontend.SetActiveConfigResponse, error) {
	if req.GetVer() == 0 {
		id, err := s.stState.FindIdByName(req.GetName())
		if err == nil {
			req.Id = id
		}
	}
	err := s.stState.SetActiveConfig(req.Id)
	if err != nil {
		s.logger.Errorf("Failed to set active config %v", err)
		return nil, err
	}
	return &frontend.SetActiveConfigResponse{}, nil
}
func (s *ThinningService) DeleteConfig(ctx context.Context, req *frontend.DeleteConfigRequest) (*frontend.DeleteConfigResponse, error) {
	if req.GetVer() == 0 {
		id, err := s.stState.FindIdByName(req.GetName())
		if err == nil {
			req.Id = id
		}
	}
	err := s.stState.DeleteConfig(req.Id, req.GetNewActiveId())
	if err != nil {
		s.logger.Errorf("failed to delete config %v", err)
		return nil, err
	}
	return &frontend.DeleteConfigResponse{}, nil
}
