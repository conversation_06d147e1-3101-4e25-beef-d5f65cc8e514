import enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class DeepweedRequestType(str, enum.Enum):
    PREDICTION = "prediction"
    EMBEDDING = "embedding"
    FOCUS_SCORE = "focus_score"
    FEWSHOT_EMBEDDING = "fewshot_embedding"
    COMPARISON_EMBEDDING = "comparison_embedding"
    SELFSUP_EMBEDDING = "selfsup_embedding"


class DeepweedPredictionRequest(BaseModel):
    threshold: float
    model_id: str
    image_id: str
    model_s3_path: str
    image_s3_path: str
    predict_border: bool
    ppi: int
    crop_id: Optional[str] = None
    message_type: str = DeepweedRequestType.PREDICTION
    message_version: str = "v1"
    metadata: Dict[str, Any] = {}


class DeepweedPredictionResponse(BaseModel):
    image_id: str
    image_s3_path: str
    model_id: str
    threshold: float
    predict_border: bool
    ppi: int
    points: List[Any]
    masks: List[Any]
    message_type: str = DeepweedRequestType.PREDICTION
    message_version: str = "v1"
    success: bool
    metadata: Dict[str, Any]


class DeepweedEmbeddingRequest(BaseModel):
    model_id: str
    image_id: str
    model_s3_path: str
    image_s3_path: str
    ppi: int
    crop_id: Optional[str] = None
    message_type: str = DeepweedRequestType.EMBEDDING
    message_version: str = "v1"
    # Will use center if not provided
    x: Optional[int] = None
    y: Optional[int] = None
    metadata: Dict[str, Any] = {}


class DeepweedEmbeddingResponse(BaseModel):
    image_id: str
    image_s3_path: str
    model_id: str
    ppi: int
    success: bool
    embedding: List[float]
    x: int
    y: int
    metadata: Dict[str, Any]
    message_type: str = DeepweedRequestType.EMBEDDING
    message_version: str = "v1"


class DeepweedFocusScoreRequest(BaseModel):
    image_id: str
    image_s3_path: str
    message_type: str = DeepweedRequestType.FOCUS_SCORE
    message_version: str = "v1"
    sample_frequency: float


class DeepweedFocusScoreResponse(BaseModel):
    image_id: str
    image_s3_path: str
    message_type: str = DeepweedRequestType.FOCUS_SCORE
    message_version: str = "v1"
    sample_frequency: float
    success: bool
    score: Optional[float]


class FeatureExtractorEmbeddingRequest(BaseModel):
    model_id: str
    image_id: str
    model_s3_path: str
    image_s3_path: str
    message_type: str = DeepweedRequestType.FEWSHOT_EMBEDDING
    message_version: str = "v1"
    x: int
    y: int
    metadata: Dict[str, Any] = {}


class ComparisonEmbeddingRequest(BaseModel):
    model_id: str
    image_id: str
    model_s3_path: str
    image_s3_path: str
    message_type: str = DeepweedRequestType.COMPARISON_EMBEDDING
    message_version: str = "v1"
    x: int
    y: int
    metadata: Dict[str, Any] = {}


class FeatureExtractorEmbeddingResponse(BaseModel):
    image_id: str
    image_s3_path: str
    model_id: str
    success: bool
    embedding: List[float]
    x: int
    y: int
    metadata: Dict[str, Any]
    message_type: str = DeepweedRequestType.FEWSHOT_EMBEDDING
    message_version: str = "v1"


class ComparisonEmbeddingResponse(BaseModel):
    image_id: str
    image_s3_path: str
    model_id: str
    success: bool
    embedding: List[float]
    x: int
    y: int
    metadata: Dict[str, Any]
    message_type: str = DeepweedRequestType.COMPARISON_EMBEDDING
    message_version: str = "v1"
