#pragma once

#include "cv/deepweed/output.h"
#include "cv/runtime/proto/cv_runtime.grpc.pb.h"
#include <spdlog/spdlog.h>

namespace cv::runtime {
class DeepweedOutputToProtobuf {
public:
  static void to_protobuf(deepweed::DeepweedOutput *output, proto::DeepweedOutput *response) {
    response->mutable_detections()->Reserve((int)output->detections.size());
    for (auto &det : output->detections) {
      auto detection = response->add_detections();
      detection->set_x(det.x);
      detection->set_y(det.y);
      detection->set_size(det.size);
      detection->set_score(det.score);
      detection->set_weed_score(det.weed_score);
      detection->set_crop_score(det.crop_score);
      detection->set_plant_score(det.plant_score);
      detection->set_hit_class(det.hit_class);
      detection->set_detection_id(det.detection_id);

      for (size_t j = 0; j < output->weed_detection_classes.size(); j++) {
        detection->add_weed_detection_class_scores(det.weed_detection_class_scores[j]);
      }

      for (size_t j = 0; j < det.mask_intersections.size(); j++) {
        detection->add_mask_intersections(det.mask_intersections[j]);
      }

      for (float emb : det.reduced_scaled_embedding) {
        detection->add_embedding(emb);
      }

      for (float emb : det.embedding_category_distances) {
        detection->add_embedding_category_distances(emb);
      }
    }

    response->set_mask_width(output->mask_width);
    response->set_mask_height(output->mask_height);
    response->set_mask_channels(output->mask_channels);
    response->set_mask(output->mask.data(), output->mask_width * output->mask_height * output->mask_channels);
    for (size_t i = 0; i < output->mask_channels; i++) {
      response->add_mask_channel_classes(output->mask_channel_classes[i]);
    }
    for (size_t i = 0; i < output->weed_detection_classes.size(); i++) {
      response->add_weed_detection_classes(output->weed_detection_classes[i]);
    }
    for (size_t i = 0; i < output->embedding_categories.size(); i++) {
      response->add_embedding_categories(output->embedding_categories[i]);
    }
    response->set_timestamp_ms(output->timestamp_ms);

    /*
     * Do not change this to response->set_predict_in_distance_buffer(output->predict_in_distance_buffer);
     * For reasons unknown, this causes GRPC to fail the server on some assertion.
     * Similar issue is described in https://github.com/grpc/grpc/issues/24250, there the boolean
     * was uninitialized and when copied to protobuf, caused this error. From what I've seen in our code,
     * the boolean was initialized properly at deepweed_model.cpp:infer(), so I'm not sure how
     * the error happens. Nevertheless, setting true/false directly is a workaround.
     */
    response->set_predict_in_distance_buffer(false);
    if (output->predict_in_distance_buffer) {
      response->set_predict_in_distance_buffer(true);
    }

    response->set_available_for_snapshotting(output->available_for_snapshotting);
  }

  static void from_protobuf(proto::DeepweedOutput *response, deepweed::DeepweedOutput *output) {
    output->detections.clear();
    for (size_t i = 0; i < (size_t)response->detections().size(); i++) {
      auto pDet = response->detections().at((int)i);
      deepweed::DeepweedDetection detection;
      detection.x = pDet.x();
      detection.y = pDet.y();
      detection.size = pDet.size();
      detection.score = pDet.score();
      detection.weed_score = pDet.weed_score();
      detection.crop_score = pDet.crop_score();
      detection.plant_score = pDet.plant_score();
      detection.hit_class = pDet.hit_class();
      detection.detection_id = pDet.detection_id();

      for (const auto &score : pDet.weed_detection_class_scores()) {
        detection.weed_detection_class_scores.push_back(score);
      }

      for (size_t j = 0; j < (size_t)pDet.mask_intersections().size(); j++) {
        detection.mask_intersections.push_back(pDet.mask_intersections()[(int)j]);
      }

      for (float emb : pDet.embedding()) {
        detection.reduced_scaled_embedding.push_back(emb);
      }

      for (float emb : pDet.embedding_category_distances()) {
        detection.embedding_category_distances.push_back(emb);
      }

      output->detections.push_back(detection);
    }

    output->mask_width = response->mask_width();
    output->mask_height = response->mask_height();
    output->weed_detection_classes.clear();
    for (int i = 0; i < response->weed_detection_classes().size(); i++) {
      output->weed_detection_classes.push_back(response->weed_detection_classes()[i]);
    }
    output->embedding_categories.clear();
    for (int i = 0; i < response->embedding_categories().size(); i++) {
      output->embedding_categories.push_back(response->embedding_categories()[i]);
    }
    std::copy(response->mask().begin(), response->mask().end(), output->mask.begin());
    output->mask_channel_classes.clear();
    for (size_t i = 0; i < (size_t)response->mask_channel_classes_size(); i++) {
      output->mask_channel_classes.push_back(response->mask_channel_classes((int)i));
    }

    output->timestamp_ms = response->timestamp_ms();
    output->predict_in_distance_buffer = response->predict_in_distance_buffer();
    output->available_for_snapshotting = response->available_for_snapshotting();
  }
};
} // namespace cv::runtime
