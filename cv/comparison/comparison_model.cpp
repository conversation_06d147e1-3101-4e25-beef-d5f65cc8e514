#include "comparison_model.h"

#include <c10/cuda/CUDAGuard.h>
#include <fmt/format.h>
#include <google/protobuf/util/json_util.h>
#include <spdlog/spdlog.h>

#include "deeplearning/model_io/cpp/model_utils.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/resize_util.h"

namespace F = torch::nn::functional;

namespace cv::comparison {

using namespace lib::common;

ComparisonModel::ComparisonModel(model::AtomicModel model) : model_(model), gpu_id_(model_.get_gpu_id()) {
  if (!model_) {
    throw maka_error("ComparisonModel cannot be constructed with an empty model.");
  }

  if (!model_.get_internal_metadata().means().empty()) {
    std::vector<float> means(model_.get_internal_metadata().means().begin(),
                             model_.get_internal_metadata().means().end());
    means_ = torch::tensor(means).to({torch::kCUDA, gpu_id_});
  }

  if (!model_.get_internal_metadata().stds().empty()) {
    std::vector<float> stds(model_.get_internal_metadata().stds().begin(), model_.get_internal_metadata().stds().end());
    stds_ = torch::tensor(stds).to({torch::kCUDA, gpu_id_});
  }
  batch_size_ = model_.get_internal_metadata().max_batch_size();

  input_size_ = glm::ivec2(model_.get_internal_metadata().input_size().width(),
                           model_.get_internal_metadata().input_size().height());
}

const ModelMetadataProto &ComparisonModel::get_internal_metadata() { return model_.get_internal_metadata(); }
const nlohmann::json &ComparisonModel::get_external_metadata() { return model_.get_external_metadata(); }
std::tuple<int, int> ComparisonModel::get_input_size() { return std::make_tuple(input_size_.x, input_size_.y); }

torch::Tensor ComparisonModel::normalize(torch::Tensor image) {
  torch::Tensor stds;
  torch::Tensor means;
  image = image.permute({0, 2, 3, 1});
  stds = stds_.slice(0, 0, 3);
  means = means_.slice(0, 0, 3);
  image /= 255.0f;
  // normalize to 0..1
  if (!model_.get_internal_metadata().means().empty()) {
    image -= means;
  }
  if (!model_.get_internal_metadata().stds().empty()) {
    image /= stds;
  }
  image = image.permute({0, 3, 1, 2});
  return image;
}

torch::Tensor ComparisonModel::batch_infer(torch::Tensor im_tensor) {
  int64_t in_batch_size = im_tensor.size(0);
  std::vector<torch::Tensor> tensors;
  std::vector<torch::Tensor> outputs;

  if (im_tensor.size(0) < batch_size_) {
    int64_t additional_batch_items = batch_size_ - im_tensor.size(0);
    im_tensor =
        torch::cat({im_tensor, im_tensor.slice(0, 0, 1).expand({additional_batch_items, -1, -1, -1})}, 0).contiguous();
    tensors.push_back(im_tensor);

    auto output = model_(tensors);
    for (size_t output_i = 0; output_i < output.size(); output_i++) {
      output[output_i] = output[output_i].slice(0, 0, in_batch_size);
    }
    auto raw_output = ComparisonRawOutput(output);

    return raw_output.embedding[0].slice(0, 0, in_batch_size, 1);

  } else if (im_tensor.size(0) > batch_size_) {
    int64_t num_batches = (in_batch_size + batch_size_ - 1) / batch_size_;
    int64_t additional_batch_items = num_batches * batch_size_ - in_batch_size;

    im_tensor =
        torch::cat({im_tensor, im_tensor.slice(0, 0, 1).expand({additional_batch_items, -1, -1, -1})}, 0).contiguous();

    for (int i = 0; i < num_batches; i++) {
      tensors.clear();
      tensors.push_back(im_tensor.narrow(0, i * batch_size_, batch_size_));

      auto output = model_(tensors);
      for (size_t output_i = 0; output_i < output.size(); output_i++) {
        output[output_i] = output[output_i].slice(0, 0, in_batch_size);
      }
      auto raw_output = ComparisonRawOutput(output);

      outputs.push_back(raw_output.embedding[0]);
    }

    auto output = torch::cat(outputs, 0);
    output = output.slice(0, 0, in_batch_size, 1);

    return output;
  } else {
    tensors.push_back(im_tensor);

    auto output = model_(tensors);
    for (size_t output_i = 0; output_i < output.size(); output_i++) {
      output[output_i] = output[output_i].slice(0, 0, in_batch_size);
    }
    auto raw_output = ComparisonRawOutput(output);
    return raw_output.embedding[0];
  }
}

ComparisonOutput ComparisonModel::infer(torch::Tensor image_tensor, int x, int y) {
  nvtxRangePushA("Comparison_inference");
  auto torch_type = deeplearning::model_io::dtype_to_torch_type(model_.get_internal_metadata().input_dtype());
  image_tensor = image_tensor.to(torch_type);
  image_tensor = image_tensor.to({torch::kCUDA, gpu_id_});
  image_tensor = normalize(image_tensor);

  auto cropping_height = input_size_.y / 2;
  auto cropping_width = input_size_.x / 2;

  std::vector<int64_t> pad_dimensions = {cropping_width, cropping_width, cropping_height, cropping_height};
  image_tensor = F::pad(image_tensor, F::PadFuncOptions(pad_dimensions));
  auto padded_x = x + cropping_width;
  auto padded_y = y + cropping_height;

  auto original_batch_size = image_tensor.size(0);
  image_tensor = image_tensor.index({torch::indexing::Slice(0, original_batch_size), "...",
                                     torch::indexing::Slice(padded_y - cropping_height, padded_y + cropping_height),
                                     torch::indexing::Slice(padded_x - cropping_width, padded_x + cropping_width)});

  auto output = batch_infer(image_tensor);

  ComparisonOutput comparison_output;
  comparison_output.embedding = {output};
  return comparison_output;
}

torch::Tensor ComparisonModel::infer_chip(torch::Tensor image_tensor) {
  auto torch_type = deeplearning::model_io::dtype_to_torch_type(model_.get_internal_metadata().input_dtype());
  image_tensor = image_tensor.to(torch_type);
  image_tensor = image_tensor.to({torch::kCUDA, gpu_id_});
  image_tensor = normalize(image_tensor);
  return batch_infer(image_tensor);
}

} // namespace cv::comparison
