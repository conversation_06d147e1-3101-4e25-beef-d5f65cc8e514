from typing import List, <PERSON><PERSON>

import torch

from lib.common.model.cpp.model_python import AtomicModel

class ComparisonOutput:
    def __init__(self) -> None: ...
    embedding: List[torch.Tensor]

class ComparisonModel:
    def __init__(self, model: AtomicModel) -> None: ...
    def infer(self, image_tensor: torch.Tensor, x: int, y: int) -> ComparisonOutput: ...
    def infer_chip(self, image_tensor: torch.Tensor) -> torch.Tensor: ...
    def get_input_size(self) -> Tuple[int, int]: ...
