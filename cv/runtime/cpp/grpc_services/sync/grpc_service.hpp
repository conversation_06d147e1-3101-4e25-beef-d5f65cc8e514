/*
 * This implementation is deprecated, use async_grpc_service.hpp instead.
 */

#pragma once

#include "cv/runtime/cpp/grpc_services/impl/cv_runtime_service_impl.hpp"
#include "cv/runtime/cpp/image_service.h"
#include "lib/common/camera/cpp/camera_info.h"
#include "lib/common/camera/cpp/camera_settings.h"
#include "lib/common/cpp/logging_service.hpp"

#include <ctime>
#include <filesystem>
#include <grpcpp/grpcpp.h>
#include <vector>

namespace cv {
namespace runtime {

class CVRuntimeServiceImplSync final : public proto::CVRuntimeService::Service, public CVRuntimeServiceImplCommon {
public:
  CVRuntimeServiceImplSync(
      std::shared_ptr<NodeRegistry> node_registry,
      std::shared_ptr<std::map<std::string, std::map<proto::BufferUseCase, std::string>>> camera_to_buffer,
      std::shared_ptr<std::map<std::string, lib::common::camera::Camera>> camera_id_to_camera,
      std::shared_ptr<std::map<std::string, bool>> camera_id_to_transpose,
      std::shared_ptr<std::map<std::string, std::pair<std::string, lib::common::camera::Camera>>> sim_cameras,
      std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry,
      std::shared_ptr<lib::common::GeoData> geo_data, std::shared_ptr<lib::common::ImplementStatus> implement_status,
      std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues,
      std::shared_ptr<carbon::config::ConfigTree> deepweed_config, std::string kLightweightBurstDirectory,
      std::string kFullBurstDirectory,
      const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues,
      std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>>>>
          deepweed_buffers,
      std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>>
          p2p_buffers,
      std::string role,
      std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
          distance_camera_buffer,
      std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> chip_score_queues,
      std::function<void()> reload_category_collection, std::shared_ptr<cv::embeddings::Manager> embeddings_manager,
      std::shared_ptr<cv::runtime::ErrorState> error_state);
  ~CVRuntimeServiceImplSync();

  void Build(grpc::ServerBuilder &builder) override;
  void Start() override;
  void Shutdown() override;
  void Check() override;
  void ReportMetrics() override;

  grpc::Status GetBooted(grpc::ServerContext *, const proto::GetBootedRequest *request,
                         proto::GetBootedResponse *response);
  grpc::Status GetReady(grpc::ServerContext *, const proto::GetReadyRequest *request,
                        proto::GetReadyResponse *response);
  grpc::Status SetP2PContext(grpc::ServerContext *, const proto::SetP2PContextRequest *request,
                             proto::SetP2PContextResponse *response);
  grpc::Status GetCameraDimensions(grpc::ServerContext *, const proto::GetCameraDimensionsRequest *request,
                                   proto::GetCameraDimensionsResponse *response);
  grpc::Status GetCameraInfo(grpc::ServerContext *, const proto::GetCameraInfoRequest *request,
                             proto::GetCameraInfoResponse *response);

  grpc::Status SetDeepweedDetectionCriteria(grpc::ServerContext *,
                                            const proto::SetDeepweedDetectionCriteriaRequest *request,
                                            proto::SetDeepweedDetectionCriteriaResponse *response);
  grpc::Status GetDeepweedDetectionCriteria(grpc::ServerContext *,
                                            const proto::GetDeepweedDetectionCriteriaRequest *request,
                                            proto::GetDeepweedDetectionCriteriaResponse *response);
  grpc::Status GetDeepweedSupportedCategories(grpc::ServerContext *,
                                              const proto::GetDeepweedSupportedCategoriesRequest *request,
                                              proto::GetDeepweedSupportedCategoriesResponse *response);
  grpc::Status SetCameraSettings(grpc::ServerContext *, const proto::SetCameraSettingsRequest *request,
                                 proto::SetCameraSettingsResponse *response);
  grpc::Status GetCameraSettings(grpc::ServerContext *, const proto::GetCameraSettingsRequest *request,
                                 proto::GetCameraSettingsResponse *response);
  grpc::Status StartBurstRecordFrames(grpc::ServerContext *, const proto::StartBurstRecordFramesRequest *request,
                                      proto::StartBurstRecordFramesResponse *response);
  grpc::Status StopBurstRecordFrames(grpc::ServerContext *, const proto::StopBurstRecordFramesRequest *request,
                                     proto::StopBurstRecordFramesResponse *response);
  grpc::Status GetConnectors(grpc::ServerContext *, const proto::GetConnectorsRequest *request,
                             proto::GetConnectorsResponse *response);
  grpc::Status SetConnectors(grpc::ServerContext *, const proto::SetConnectorsRequest *request,
                             proto::SetConnectorsResponse *response);
  grpc::Status GetTiming(grpc::ServerContext *, const proto::GetTimingRequest *request,
                         proto::GetTimingResponse *response);
  grpc::Status Predict(grpc::ServerContext *, const proto::PredictRequest *request, proto::PredictResponse *response);
  grpc::Status LoadAndQueue(grpc::ServerContext *, const proto::LoadAndQueueRequest *request,
                            proto::LoadAndQueueResponse *response);
  grpc::Status SetImage(grpc::ServerContext *, const proto::SetImageRequest *request,
                        proto::SetImageResponse *response);
  grpc::Status UnsetImage(grpc::ServerContext *, const proto::UnsetImageRequest *request,
                          proto::UnsetImageResponse *response);
  grpc::Status GetModelPaths(grpc::ServerContext *, const proto::GetModelPathsRequest *request,
                             proto::GetModelPathsResponse *response);
  grpc::Status GetCameraTemperatures(grpc::ServerContext *, const proto::GetCameraTemperaturesRequest *request,
                                     proto::GetCameraTemperaturesResponse *response);
  grpc::Status SetImageScore(grpc::ServerContext *, const proto::SetImageScoreRequest *request,
                             proto::SetImageScoreResponse *response);
  grpc::Status GetScoreQueue(grpc::ServerContext *, const proto::GetScoreQueueRequest *request,
                             proto::GetScoreQueueResponse *response);
  grpc::Status ListScoreQueues(grpc::ServerContext *, const proto::Empty *request,
                               proto::ListScoreQueuesResponse *response);
  grpc::Status GetMaxImageScore(grpc::ServerContext *, const proto::GetMaxImageScoreRequest *request,
                                proto::GetMaxImageScoreResponse *response);
  grpc::Status GetMaxScoredImage(grpc::ServerContext *, const proto::GetMaxScoredImageRequest *request,
                                 proto::ImageAndMetadataResponse *response);
  grpc::Status GetChipImage(grpc::ServerContext *, const proto::GetChipImageRequest *request,
                            proto::ChipImageAndMetadataResponse *response);
  grpc::Status GetChipQueueInformation(grpc::ServerContext *, const proto::ChipQueueInformationRequest *request,
                                       proto::ChipQueueInformationResponse *response);
  grpc::Status GetLatestP2PImage(grpc::ServerContext *, const proto::GetLatestP2PImageRequest *request,
                                 proto::P2PImageAndMetadataResponse *response);
  grpc::Status FlushQueues(grpc::ServerContext *, const proto::FlushQueuesRequest *, proto::FlushQueuesResponse *);
  grpc::Status GetLatestImage(grpc::ServerContext *, const proto::GetLatestImageRequest *request,
                              proto::ImageAndMetadataResponse *response);
  grpc::Status GetImageNearTimestamp(grpc::ServerContext *, const proto::GetImageNearTimestampRequest *request,
                                     proto::ImageAndMetadataResponse *response);
  grpc::Status GetLightweightBurstRecord(grpc::ServerContext *, const proto::GetLightweightBurstRecordRequest *request,
                                         proto::GetLightweightBurstRecordResponse *response);
  grpc::Status GetDeepweedOutputByTimestamp(grpc::ServerContext *,
                                            const proto::GetDeepweedOutputByTimestampRequest *request,
                                            proto::DeepweedOutput *response);
  grpc::Status P2PBufferringBurstCapture(grpc::ServerContext *, const proto::P2PBufferringBurstCaptureRequest *request,
                                         proto::P2PBufferringBurstCaptureResponse *response);
  grpc::Status P2PCapture(grpc::ServerContext *, const proto::P2PCaptureRequest *request,
                          proto::P2PCaptureResponse *response);
  grpc::Status SetAutoWhitebalance(grpc::ServerContext *, const proto::SetAutoWhitebalanceRequest *request,
                                   proto::SetAutoWhitebalanceResponse *response);
  grpc::Status GetNextDeepweedOutput(grpc::ServerContext *, const proto::GetNextDeepweedOutputRequest *request,
                                     proto::DeepweedOutput *response);
  grpc::Status GetNextP2POutput(grpc::ServerContext *, const proto::GetNextP2POutputRequest *request,
                                proto::P2POutputProto *response);
  grpc::Status SetTargetingState(grpc::ServerContext *, const proto::SetTargetingStateRequest *request,
                                 proto::SetTargetingStateResponse *response);
  grpc::Status GetRecommendedStrobeSettings(grpc::ServerContext *,
                                            const proto::GetRecommendedStrobeSettingsRequest *request,
                                            proto::GetRecommendedStrobeSettingsResponse *response);
  grpc::Status GetNextFocusMetric(grpc::ServerContext *, const proto::GetNextFocusMetricRequest *request,
                                  proto::GetNextFocusMetricResponse *response);
  grpc::Status RemoveDataDir(grpc::ServerContext *, const proto::RemoveDataDirRequest *request,
                             proto::RemoveDataDirResponse *response);
  grpc::Status GetLastNImages(grpc::ServerContext *, const proto::LastNImageRequest *request,
                              grpc::ServerWriter<proto::ImageAndMetadataResponse> *writer);
  grpc::Status GetComputeCapabilities(grpc::ServerContext *, const proto::Empty *request,
                                      proto::ComputeCapabilitiesResponse *response);
  grpc::Status GetSupportedTensorRTVersions(grpc::ServerContext *, const proto::Empty *request,
                                            proto::SupportedTensorRTVersionsResponse *response);
  grpc::Status ReloadCategoryCollection(grpc::ServerContext *, const proto::Empty *request, proto::Empty *response);
  grpc::Status GetCategoryCollection(grpc::ServerContext *, const proto::Empty *request,
                                     proto::GetCategoryCollectionResponse *response);
  grpc::Status GetErrorState(grpc::ServerContext *, const proto::Empty *request,
                             proto::GetErrorStateResponse *response);
  grpc::Status SnapshotPredictImages(grpc::ServerContext *, const proto::SnapshotPredictImagesRequest *request,
                                     proto::SnapshotPredictImagesResponse *response);
  grpc::Status GetChipForPredictImage(grpc::ServerContext *, const proto::GetChipForPredictImageRequest *request,
                                      proto::GetChipForPredictImageResponse *response);

private:
  // All member variables and helper methods are now inherited from CVRuntimeServiceImplCommon
};

std::unique_ptr<grpc::Server> start_grpc_server(CVRuntimeServiceImplSync *service_impl,
                                                ImageStreamServiceImpl *image_service_impl,
                                                lib::common::logging::LoggingServiceImpl *logging_service_impl,
                                                const std::string &server_address);

} // namespace runtime

} // namespace cv
