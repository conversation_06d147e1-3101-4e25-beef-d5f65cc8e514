import logging
import traceback
from typing import Any, Callable, List, Tuple

import numpy as np
import torch

from .utilities import default_load_fn, default_transform_fn

LOG = logging.getLogger(__name__)


class ChipDataset(torch.utils.data.Dataset[torch.Tensor]):
    def __init__(
        self,
        paths: List[Any],
        load_fn: Callable[[str], Any] = default_load_fn,
        transform_fn: Callable[[Any], torch.Tensor] = default_transform_fn,
    ) -> None:
        self._paths = paths
        self._load_fn = load_fn
        self._transform_fn = transform_fn

    def __len__(self) -> int:
        return len(self._paths)

    def __getitem__(self, index: int) -> torch.Tensor:
        image = self._load_fn(self._paths[index])
        image_tensor = self._transform_fn(image)
        return image_tensor


class FourChipDataset(torch.utils.data.Dataset[Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]]):
    def __init__(
        self,
        paths: List[Any],
        transform_list: List[Callable[[Any], Any]],
        load_fn: Callable[[str], Any] = default_load_fn,
        transform_fn: Callable[[Any], torch.Tensor] = default_transform_fn,
    ) -> None:
        self._paths = paths
        self._load_fn = load_fn
        self._transform_fn = transform_fn
        self._transform_list = transform_list
        assert len(self._transform_list), "FourChipDataset requires transforms"

    def __len__(self) -> int:
        return len(self._paths)

    def __getitem__(self, index: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        np.random.seed(index)
        i1 = np.random.randint(0, len(self._paths))
        i2 = np.random.randint(0, len(self._paths))
        while i1 == i2:
            i2 = np.random.randint(0, len(self._paths))
        im1 = self._load_fn(self._paths[i1])
        im2 = self._load_fn(self._paths[i2])

        try:
            t1 = np.random.randint(0, len(self._transform_list))  # Transforms need to happen before normalization
            t2 = np.random.randint(0, len(self._transform_list))
            im1_t = self._transform_list[t1](im1)
            im2_t = self._transform_list[t2](im2)

            im1 = self._transform_fn(im1)
            im2 = self._transform_fn(im2)

            im1_t = self._transform_fn(im1_t)
            im2_t = self._transform_fn(im2_t)
        except Exception as e:
            LOG.warning(f"i1 metadata: {self._paths[i1]}")
            LOG.warning(f"i2 metadata: {self._paths[i2]}")
            traceback.print_exc()
            raise e

        return im1, im1_t, im2_t, im2
