import asyncio
import time
from typing import Any, Dict, List, Optional, cast

import grpc
from google.protobuf import json_format

import generated.core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2 as aimbot_pb
import generated.frontend.proto.actuation_tasks_pb2 as actuation_pb
import generated.frontend.proto.actuation_tasks_pb2_grpc as actuation_grpc
import generated.frontend.proto.alarm_pb2 as alarm_pb
import generated.frontend.proto.alarm_pb2_grpc as alarm_grpc
import generated.frontend.proto.almanac_pb2 as almanac_pb
import generated.frontend.proto.almanac_pb2_grpc as almanac_pb_grpc
import generated.frontend.proto.banding_pb2 as banding_pb
import generated.frontend.proto.banding_pb2_grpc as banding_grpc
import generated.frontend.proto.calibration_pb2 as calibration_pb
import generated.frontend.proto.calibration_pb2_grpc as calibration_grpc
import generated.frontend.proto.camera_pb2 as camera_pb
import generated.frontend.proto.camera_pb2_grpc as camera_grpc
import generated.frontend.proto.category_collection_pb2 as category_collection_pb
import generated.frontend.proto.category_collection_pb2_grpc as category_collection_grpc
import generated.frontend.proto.category_pb2 as category_pb
import generated.frontend.proto.category_pb2_grpc as category_grpc
import generated.frontend.proto.chip_pb2 as chip_pb
import generated.frontend.proto.chip_pb2_grpc as chip_grpc
import generated.frontend.proto.crosshair_pb2 as crosshair_pb
import generated.frontend.proto.crosshair_pb2_grpc as crosshair_grpc
import generated.frontend.proto.cruise_control_pb2 as cc_pb2
import generated.frontend.proto.cruise_control_pb2_grpc as cc_grpc
import generated.frontend.proto.dashboard_pb2 as dashboard_pb
import generated.frontend.proto.dashboard_pb2_grpc as dashboard_grpc
import generated.frontend.proto.data_capture_pb2 as data_capture_pb
import generated.frontend.proto.data_capture_pb2_grpc as data_capture_grpc
import generated.frontend.proto.debug_pb2 as debug_pb
import generated.frontend.proto.debug_pb2_grpc as debug_grpc
import generated.frontend.proto.features_pb2 as features_pb
import generated.frontend.proto.features_pb2_grpc as features_grpc
import generated.frontend.proto.focus_pb2 as focus_pb
import generated.frontend.proto.focus_pb2_grpc as focus_grpc
import generated.frontend.proto.jobs_pb2 as jobs_pb
import generated.frontend.proto.jobs_pb2_grpc as jobs_pb_grpc
import generated.frontend.proto.laser_pb2 as laser_pb
import generated.frontend.proto.laser_pb2_grpc as laser_grpc
import generated.frontend.proto.messages_pb2 as messages_pb
import generated.frontend.proto.messages_pb2_grpc as messages_grpc
import generated.frontend.proto.model_pb2 as model_pb
import generated.frontend.proto.model_pb2_grpc as model_grpc
import generated.frontend.proto.module_pb2 as module_assignment_pb
import generated.frontend.proto.module_pb2_grpc as module_assignment_grpc
import generated.frontend.proto.plant_captcha_pb2 as plant_captcha_pb
import generated.frontend.proto.plant_captcha_pb2_grpc as plant_captcha_grpc
import generated.frontend.proto.power_pb2 as power_pb
import generated.frontend.proto.power_pb2_grpc as power_grpc
import generated.frontend.proto.reporting_pb2 as reporting_pb
import generated.frontend.proto.reporting_pb2_grpc as reporting_grpc
import generated.frontend.proto.software_pb2 as software_pb
import generated.frontend.proto.software_pb2_grpc as software_grpc
import generated.frontend.proto.startup_task_pb2 as startup_task_pb
import generated.frontend.proto.startup_task_pb2_grpc as startup_task_grpc
import generated.frontend.proto.status_bar_pb2 as status_bar_pb
import generated.frontend.proto.status_bar_pb2_grpc as status_bar_grpc
import generated.frontend.proto.target_velocity_estimator_pb2 as tve_pb
import generated.frontend.proto.target_velocity_estimator_pb2_grpc as tve_grpc
import generated.frontend.proto.thinning_pb2 as thinning_pb
import generated.frontend.proto.thinning_pb2_grpc as thinning_pb_grpc
import generated.frontend.proto.tractor_pb2 as tractor_pb2
import generated.frontend.proto.tractor_pb2_grpc as tractor_grpc
import generated.frontend.proto.util_pb2 as util_pb
import generated.frontend.proto.weeding_diagnostics_pb2 as weeding_diagnostics_pb
import generated.frontend.proto.weeding_diagnostics_pb2_grpc as weeding_diagnostics_grpc
import generated.proto.almanac.almanac_pb2 as almanac_base_pb
import generated.proto.logging.logging_pb2 as logging_pb
import generated.proto.module.orchestrator.orchestrator_pb2 as module_orchestrator_pb
import generated.proto.module.orchestrator.orchestrator_pb2_grpc as module_orchestrator_grpc
import generated.proto.module.types.types_pb2 as module_types_pb
import generated.proto.target_velocity_estimator.target_velocity_estimator_pb2 as tve_base_pb
import generated.proto.thinning.thinning_pb2 as thinning_pb_base
import generated.weed_tracking.proto.weed_tracking_pb2 as weed_tracking_pb
import lib.common.logging
from lib.common.error import MakaException
from lib.common.time.time import maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)

STUB_STATUS_BAR_SERVICE = "StatusBarService"
STUB_DASHBOARD_SERVICE = "DashboardService"
STUB_ACTUATION_SERVICE = "ActuationService"
STUB_LASER_SERVICE = "LaserService"
STUB_FOCUS_SERVICE = "FocusService"
STUB_CROSSHAIR_SERVICE = "CrosshairService"
STUB_DATA_CAPTURE_SERVICE = "DataCaptureService"
STUB_ALARM_SERVICE = "AlarmService"
STUB_CAMERA_SERVICE = "CameraService"
STUB_MODEL_SERVICE = "ModelService"
STUB_SOFTWARE_SERVICE = "SoftwareService"
STUB_BANDING_SERVICE = "BandingService"
STUB_DEBUG_SERVICE = "DebugService"
STUB_WEEDING_DIAGNOSTICS_SERVICE = "WeedingDiagnosticsService"
STUB_CALIBRATION_SERVICE = "CalibrationService"
STUB_MESSAGES_SERVICE = "MessagesService"
STUB_REPORTING_SERVICE = "ReportingService"
STUB_FEATURE_SERVICE = "FeatureService"
STUB_STARTUP_TASK_SERVICE = "StartupTaskService"
STUB_THINNING_SERVICE = "ThinningService"
STUB_JOBS_SERVICE = "JobsService"
STUB_CATEGORY_COLLECTION_SERVICE = "CategoryCollectionService"
STUB_CATEGORY_SERVICE = "CategoryService"
STUB_CHIP_SERVICE = "ChipService"
STUB_ALMANAC_SERVICE = "AlmanacService"
STUB_CAPTCHA_SERVICE = "CaptchaService"
STUB_TVE_SERVICE = "TargetVelocityEstimatorService"
STUB_MODULE_ORCHESTRATOR_SERVICE = "ModuleOrchestratorService"
STUB_MODULE_ASSIGNMENT_SERVICE = "ModuleAssignmentService"
STUB_POWER_SERVICE = "PowerService"
STUB_TRACTOR_SERVICE = "TractorService"
STUB_CRUISE_CONTROL_SERVICE = "CruiseControlService"


STUB_TYPE = {
    STUB_STATUS_BAR_SERVICE: status_bar_grpc.StatusBarServiceStub,
    STUB_DASHBOARD_SERVICE: dashboard_grpc.DashboardServiceStub,
    STUB_ACTUATION_SERVICE: actuation_grpc.ActuationTasksServiceStub,
    STUB_LASER_SERVICE: laser_grpc.LaserServiceStub,
    STUB_FOCUS_SERVICE: focus_grpc.FocusServiceStub,
    STUB_CROSSHAIR_SERVICE: crosshair_grpc.CrosshairServiceStub,
    STUB_DATA_CAPTURE_SERVICE: data_capture_grpc.DataCaptureServiceStub,
    STUB_ALARM_SERVICE: alarm_grpc.AlarmServiceStub,
    STUB_CAMERA_SERVICE: camera_grpc.CameraServiceStub,
    STUB_MODEL_SERVICE: model_grpc.ModelServiceStub,
    STUB_SOFTWARE_SERVICE: software_grpc.SoftwareServiceStub,
    STUB_BANDING_SERVICE: banding_grpc.BandingServiceStub,
    STUB_DEBUG_SERVICE: debug_grpc.DebugServiceStub,
    STUB_WEEDING_DIAGNOSTICS_SERVICE: weeding_diagnostics_grpc.WeedingDiagnosticsServiceStub,
    STUB_CALIBRATION_SERVICE: calibration_grpc.CalibrationServiceStub,
    STUB_MESSAGES_SERVICE: messages_grpc.MessagesServiceStub,
    STUB_REPORTING_SERVICE: reporting_grpc.ReportingServiceStub,
    STUB_FEATURE_SERVICE: features_grpc.FeatureServiceStub,
    STUB_STARTUP_TASK_SERVICE: startup_task_grpc.StartupTaskServiceStub,
    STUB_THINNING_SERVICE: thinning_pb_grpc.ThinningServiceStub,
    STUB_JOBS_SERVICE: jobs_pb_grpc.JobsServiceStub,
    STUB_CATEGORY_COLLECTION_SERVICE: category_collection_grpc.CategoryCollectionServiceStub,
    STUB_CATEGORY_SERVICE: category_grpc.CategoryServiceStub,
    STUB_CHIP_SERVICE: chip_grpc.ChipServiceStub,
    STUB_ALMANAC_SERVICE: almanac_pb_grpc.AlmanacConfigServiceStub,
    STUB_CAPTCHA_SERVICE: plant_captcha_grpc.PlantCaptchaServiceStub,
    STUB_TVE_SERVICE: tve_grpc.TargetVelocityEstimatorServiceStub,
    STUB_MODULE_ORCHESTRATOR_SERVICE: module_orchestrator_grpc.ModuleOrchestratorServiceStub,
    STUB_MODULE_ASSIGNMENT_SERVICE: module_assignment_grpc.ModuleAssignmentServiceStub,
    STUB_POWER_SERVICE: power_grpc.PowerServiceStub,
    STUB_TRACTOR_SERVICE: tractor_grpc.TractorServiceStub,
    STUB_CRUISE_CONTROL_SERVICE: cc_grpc.CruiseControlServiceStub,
}


class CommanderClientException(MakaException):
    pass


class CommanderClient:
    def __init__(self, hostname: str = "127.0.0.1", port: int = 61002) -> None:
        self._hostname = hostname
        self._port = port
        self._channel = None
        self._stubs: Dict[str, Any] = {}

    def _maybe_connect(self, service: str) -> None:
        if self._channel is None:
            self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
        if service not in self._stubs:
            self._stubs[service] = STUB_TYPE[service](self._channel)

    async def close(self) -> None:
        if self._channel is not None:
            await self._channel.close()
            self._channel = None

    def _get_status_bar_stub(self) -> status_bar_grpc.StatusBarServiceStub:
        self._maybe_connect(STUB_STATUS_BAR_SERVICE)
        assert STUB_STATUS_BAR_SERVICE in self._stubs and self._stubs[STUB_STATUS_BAR_SERVICE] is not None
        return cast(status_bar_grpc.StatusBarServiceStub, self._stubs[STUB_STATUS_BAR_SERVICE])

    def _get_dashboard_stub(self) -> dashboard_grpc.DashboardServiceStub:
        self._maybe_connect(STUB_DASHBOARD_SERVICE)
        assert STUB_DASHBOARD_SERVICE in self._stubs and self._stubs[STUB_DASHBOARD_SERVICE] is not None
        return cast(dashboard_grpc.DashboardServiceStub, self._stubs[STUB_DASHBOARD_SERVICE])

    def _get_actuation_stub(self) -> actuation_grpc.ActuationTasksServiceStub:
        self._maybe_connect(STUB_ACTUATION_SERVICE)
        assert STUB_ACTUATION_SERVICE in self._stubs and self._stubs[STUB_ACTUATION_SERVICE] is not None
        return cast(actuation_grpc.ActuationTasksServiceStub, self._stubs[STUB_ACTUATION_SERVICE])

    def _get_laser_stub(self) -> laser_grpc.LaserServiceStub:
        self._maybe_connect(STUB_LASER_SERVICE)
        assert STUB_LASER_SERVICE in self._stubs and self._stubs[STUB_LASER_SERVICE] is not None
        return cast(laser_grpc.LaserServiceStub, self._stubs[STUB_LASER_SERVICE])

    def _get_focus_stub(self) -> focus_grpc.FocusServiceStub:
        self._maybe_connect(STUB_FOCUS_SERVICE)
        assert STUB_FOCUS_SERVICE in self._stubs and self._stubs[STUB_FOCUS_SERVICE] is not None
        return cast(focus_grpc.FocusServiceStub, self._stubs[STUB_FOCUS_SERVICE])

    def _get_camera_stub(self) -> camera_grpc.CameraServiceStub:
        self._maybe_connect(STUB_CAMERA_SERVICE)
        assert STUB_CAMERA_SERVICE in self._stubs and self._stubs[STUB_CAMERA_SERVICE] is not None
        return cast(camera_grpc.CameraServiceStub, self._stubs[STUB_CAMERA_SERVICE])

    def _get_data_capture_stub(self) -> data_capture_grpc.DataCaptureServiceStub:
        self._maybe_connect(STUB_DATA_CAPTURE_SERVICE)
        assert STUB_DATA_CAPTURE_SERVICE in self._stubs and self._stubs[STUB_DATA_CAPTURE_SERVICE] is not None
        return cast(data_capture_grpc.DataCaptureServiceStub, self._stubs[STUB_DATA_CAPTURE_SERVICE])

    def _get_crosshair_stub(self) -> crosshair_grpc.CrosshairServiceStub:
        self._maybe_connect(STUB_CROSSHAIR_SERVICE)
        assert STUB_CROSSHAIR_SERVICE in self._stubs and self._stubs[STUB_CROSSHAIR_SERVICE] is not None
        return cast(crosshair_grpc.CrosshairServiceStub, self._stubs[STUB_CROSSHAIR_SERVICE])

    def _get_calibration_stub(self) -> calibration_grpc.CalibrationServiceStub:
        self._maybe_connect(STUB_CALIBRATION_SERVICE)
        assert STUB_CALIBRATION_SERVICE in self._stubs and self._stubs[STUB_CALIBRATION_SERVICE] is not None
        return cast(calibration_grpc.CalibrationServiceStub, self._stubs[STUB_CALIBRATION_SERVICE])

    def _get_alarm_stub(self) -> alarm_grpc.AlarmServiceStub:
        self._maybe_connect(STUB_ALARM_SERVICE)
        assert STUB_ALARM_SERVICE in self._stubs and self._stubs[STUB_ALARM_SERVICE] is not None
        return cast(alarm_grpc.AlarmServiceStub, self._stubs[STUB_ALARM_SERVICE])

    def _get_model_stub(self) -> model_grpc.ModelServiceStub:
        self._maybe_connect(STUB_MODEL_SERVICE)
        assert STUB_MODEL_SERVICE in self._stubs and self._stubs[STUB_MODEL_SERVICE] is not None
        return cast(model_grpc.ModelServiceStub, self._stubs[STUB_MODEL_SERVICE])

    def _get_software_stub(self) -> software_grpc.SoftwareServiceStub:
        self._maybe_connect(STUB_SOFTWARE_SERVICE)
        assert STUB_SOFTWARE_SERVICE in self._stubs and self._stubs[STUB_SOFTWARE_SERVICE] is not None
        return cast(software_grpc.SoftwareServiceStub, self._stubs[STUB_SOFTWARE_SERVICE])

    def _get_banding_stub(self) -> banding_grpc.BandingServiceStub:
        self._maybe_connect(STUB_BANDING_SERVICE)
        assert STUB_BANDING_SERVICE in self._stubs and self._stubs[STUB_BANDING_SERVICE] is not None
        return cast(banding_grpc.BandingServiceStub, self._stubs[STUB_BANDING_SERVICE])

    def _get_debug_stub(self) -> debug_grpc.DebugServiceStub:
        self._maybe_connect(STUB_DEBUG_SERVICE)
        assert STUB_DEBUG_SERVICE in self._stubs and self._stubs[STUB_DEBUG_SERVICE] is not None
        return cast(debug_grpc.DebugServiceStub, self._stubs[STUB_DEBUG_SERVICE])

    def _get_weeding_diagnostics_stub(self) -> weeding_diagnostics_grpc.WeedingDiagnosticsServiceStub:
        self._maybe_connect(STUB_WEEDING_DIAGNOSTICS_SERVICE)
        assert (
            STUB_WEEDING_DIAGNOSTICS_SERVICE in self._stubs
            and self._stubs[STUB_WEEDING_DIAGNOSTICS_SERVICE] is not None
        )
        return cast(
            weeding_diagnostics_grpc.WeedingDiagnosticsServiceStub, self._stubs[STUB_WEEDING_DIAGNOSTICS_SERVICE]
        )

    def _get_messages_stub(self) -> messages_grpc.MessagesServiceStub:
        self._maybe_connect(STUB_MESSAGES_SERVICE)
        assert STUB_MESSAGES_SERVICE in self._stubs and self._stubs[STUB_MESSAGES_SERVICE] is not None
        return cast(messages_grpc.MessagesServiceStub, self._stubs[STUB_MESSAGES_SERVICE])

    def _get_location_history_stub(self) -> reporting_grpc.ReportingServiceStub:
        self._maybe_connect(STUB_REPORTING_SERVICE)
        assert STUB_REPORTING_SERVICE in self._stubs and self._stubs[STUB_REPORTING_SERVICE] is not None
        return cast(reporting_grpc.ReportingServiceStub, self._stubs[STUB_REPORTING_SERVICE])

    def _get_features_stub(self) -> features_grpc.FeatureServiceStub:
        self._maybe_connect(STUB_FEATURE_SERVICE)
        assert STUB_FEATURE_SERVICE in self._stubs and self._stubs[STUB_FEATURE_SERVICE] is not None
        return cast(features_grpc.FeatureServiceStub, self._stubs[STUB_FEATURE_SERVICE])

    def _get_startup_task_stub(self) -> startup_task_grpc.StartupTaskServiceStub:
        self._maybe_connect(STUB_STARTUP_TASK_SERVICE)
        assert STUB_STARTUP_TASK_SERVICE in self._stubs and self._stubs[STUB_STARTUP_TASK_SERVICE] is not None
        return cast(startup_task_grpc.StartupTaskServiceStub, self._stubs[STUB_STARTUP_TASK_SERVICE])

    def _get_thinning_stub(self) -> thinning_pb_grpc.ThinningServiceStub:
        self._maybe_connect(STUB_THINNING_SERVICE)
        assert STUB_THINNING_SERVICE in self._stubs and self._stubs[STUB_THINNING_SERVICE] is not None
        return cast(thinning_pb_grpc.ThinningServiceStub, self._stubs[STUB_THINNING_SERVICE])

    def _get_jobs_stub(self) -> jobs_pb_grpc.JobsServiceStub:
        self._maybe_connect(STUB_JOBS_SERVICE)
        assert STUB_JOBS_SERVICE in self._stubs and self._stubs[STUB_JOBS_SERVICE] is not None
        return cast(jobs_pb_grpc.JobsServiceStub, self._stubs[STUB_JOBS_SERVICE])

    def _get_category_collection_stub(self) -> category_collection_grpc.CategoryCollectionServiceStub:
        self._maybe_connect(STUB_CATEGORY_COLLECTION_SERVICE)
        assert (
            STUB_CATEGORY_COLLECTION_SERVICE in self._stubs
            and self._stubs[STUB_CATEGORY_COLLECTION_SERVICE] is not None
        )
        return cast(
            category_collection_grpc.CategoryCollectionServiceStub, self._stubs[STUB_CATEGORY_COLLECTION_SERVICE]
        )

    def _get_category_stub(self) -> category_grpc.CategoryServiceStub:
        self._maybe_connect(STUB_CATEGORY_SERVICE)
        assert STUB_CATEGORY_SERVICE in self._stubs and self._stubs[STUB_CATEGORY_SERVICE] is not None
        return cast(category_grpc.CategoryServiceStub, self._stubs[STUB_CATEGORY_SERVICE])

    def _get_chip_stub(self) -> chip_grpc.ChipServiceStub:
        self._maybe_connect(STUB_CHIP_SERVICE)
        assert STUB_CHIP_SERVICE in self._stubs and self._stubs[STUB_CHIP_SERVICE] is not None
        return cast(chip_grpc.ChipServiceStub, self._stubs[STUB_CHIP_SERVICE])

    def _get_almanac_stub(self) -> almanac_pb_grpc.AlmanacConfigServiceStub:
        self._maybe_connect(STUB_ALMANAC_SERVICE)
        assert STUB_ALMANAC_SERVICE in self._stubs and self._stubs[STUB_ALMANAC_SERVICE] is not None
        return cast(almanac_pb_grpc.AlmanacConfigServiceStub, self._stubs[STUB_ALMANAC_SERVICE])

    def _get_captcha_stub(self) -> plant_captcha_grpc.PlantCaptchaServiceStub:
        self._maybe_connect(STUB_CAPTCHA_SERVICE)
        assert STUB_CAPTCHA_SERVICE in self._stubs and self._stubs[STUB_CAPTCHA_SERVICE] is not None
        return cast(plant_captcha_grpc.PlantCaptchaServiceStub, self._stubs[STUB_CAPTCHA_SERVICE])

    def _get_tve_stub(self) -> tve_grpc.TargetVelocityEstimatorServiceStub:
        self._maybe_connect(STUB_TVE_SERVICE)
        assert STUB_TVE_SERVICE in self._stubs and self._stubs[STUB_TVE_SERVICE] is not None
        return cast(tve_grpc.TargetVelocityEstimatorServiceStub, self._stubs[STUB_TVE_SERVICE])

    def _get_module_orchestrator_stub(self) -> module_orchestrator_grpc.ModuleOrchestratorServiceStub:
        self._maybe_connect(STUB_MODULE_ORCHESTRATOR_SERVICE)
        assert (
            STUB_MODULE_ORCHESTRATOR_SERVICE in self._stubs
            and self._stubs[STUB_MODULE_ORCHESTRATOR_SERVICE] is not None
        )
        return cast(
            module_orchestrator_grpc.ModuleOrchestratorServiceStub, self._stubs[STUB_MODULE_ORCHESTRATOR_SERVICE]
        )

    def _get_module_assignment_stub(self) -> module_assignment_grpc.ModuleAssignmentServiceStub:
        self._maybe_connect(STUB_MODULE_ASSIGNMENT_SERVICE)
        assert STUB_MODULE_ASSIGNMENT_SERVICE in self._stubs and self._stubs[STUB_MODULE_ASSIGNMENT_SERVICE] is not None
        return cast(module_assignment_grpc.ModuleAssignmentServiceStub, self._stubs[STUB_MODULE_ASSIGNMENT_SERVICE])

    def _get_power_stub(self) -> power_grpc.PowerServiceStub:
        self._maybe_connect(STUB_POWER_SERVICE)
        assert STUB_POWER_SERVICE in self._stubs and self._stubs[STUB_POWER_SERVICE] is not None
        return cast(power_grpc.PowerServiceStub, self._stubs[STUB_POWER_SERVICE])

    def _get_tractor_stub(self) -> tractor_grpc.TractorServiceStub:
        self._maybe_connect(STUB_TRACTOR_SERVICE)
        assert STUB_TRACTOR_SERVICE in self._stubs and self._stubs[STUB_TRACTOR_SERVICE] is not None
        return cast(tractor_grpc.TractorServiceStub, self._stubs[STUB_TRACTOR_SERVICE])

    def _get_cc_stub(self) -> cc_grpc.CruiseControlServiceStub:
        self._maybe_connect(STUB_CRUISE_CONTROL_SERVICE)
        assert STUB_CRUISE_CONTROL_SERVICE in self._stubs and self._stubs[STUB_CRUISE_CONTROL_SERVICE] is not None
        return cast(cc_grpc.CruiseControlServiceStub, self._stubs[STUB_CRUISE_CONTROL_SERVICE])

    # Status Bar
    async def GetNextStatus(self, timestamp_ms: int = 0) -> status_bar_pb.StatusBarMessage:
        stub = self._get_status_bar_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: status_bar_pb.StatusBarMessage = await stub.GetNextStatus(req)
        return response

    async def ReportIssue(self, description: str, phone: str) -> None:
        stub = self._get_status_bar_stub()
        req = status_bar_pb.ReportIssueRequest(description=description, phone_number=phone)
        await stub.ReportIssue(req)

    async def GetSupportPhone(self, timestamp_ms: int = 0) -> status_bar_pb.SupportPhoneMessage:
        stub = self._get_status_bar_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: status_bar_pb.SupportPhoneMessage = await stub.GetSupportPhone(req)
        return response

    # Dashboard
    async def ToggleRow(self, row_id: int) -> None:
        stub = self._get_dashboard_stub()
        req = dashboard_pb.RowId(row_number=row_id)
        await stub.ToggleRow(req)

    async def ToggleLasers(self) -> None:
        stub = self._get_dashboard_stub()
        req = util_pb.Empty()
        await stub.ToggleLasers(req)

    async def GetNextDashboardState(self, timestamp_ms: int = 0) -> dashboard_pb.DashboardStateMessage:
        stub = self._get_dashboard_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: dashboard_pb.DashboardStateMessage = await stub.GetNextDashboardState(req)
        return response

    async def GetCropModelOptions(self) -> dashboard_pb.CropModelOptions:
        stub = self._get_dashboard_stub()
        req = util_pb.Empty()
        response: dashboard_pb.CropModelOptions = await stub.GetCropModelOptions(req)
        return response

    async def SetCropModel(self, crop: str) -> None:
        stub = self._get_dashboard_stub()
        req = dashboard_pb.CropModel(crop=crop)
        await stub.SetCropModel(req)

    async def GetNextWeedingVelocity(self, timestamp_ms: int = 0) -> dashboard_pb.WeedingVelocity:
        stub = self._get_dashboard_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: dashboard_pb.WeedingVelocity = await stub.GetNextWeedingVelocity(req)
        return response

    async def SetTargetingState(self, weeding: bool, thinning: bool, enabled: Dict[int, bool] = {}) -> None:
        stub = self._get_dashboard_stub()
        req = dashboard_pb.TargetingState(enabled_rows=enabled)
        req.weed_state.enabled = weeding
        req.thinning_state.enabled = thinning
        await stub.SetTargetingState(req)

    async def SetCruiseEnabled(self, enabled: bool) -> None:
        stub = self._get_dashboard_stub()
        req = dashboard_pb.CruiseEnable(enabled=enabled)
        await stub.SetCruiseEnabled(req)

    # Actuation Tasks
    async def GetNextActuationTasks(self, timestamp_ms: int = 0) -> actuation_pb.GlobalActuationTaskState:
        stub = self._get_actuation_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: actuation_pb.GlobalActuationTaskState = await stub.GetNextGlobalActuationTaskState(req)
        return response

    async def StartLaserTest(self, row_id: int, scanner_id: int, duration_ms: int) -> None:
        stub = self._get_actuation_stub()
        req = actuation_pb.GlobalAimbotActuationTaskRequest(
            row_id=row_id,
            task=aimbot_pb.ActuationTaskRequest(
                laser_test=aimbot_pb.LaserTestActuationTask(scanner_id=scanner_id, duration_ms=duration_ms)
            ),
        )
        await stub.StartGlobalAimbotActuationTask(req)

    async def StartImageDraw(self, row_id: int, scanner_id: int, speed_mmps: int) -> None:
        stub = self._get_actuation_stub()
        req = actuation_pb.GlobalAimbotActuationTaskRequest(
            row_id=row_id,
            task=aimbot_pb.ActuationTaskRequest(
                image_draw=aimbot_pb.ImageDrawActuationTask(scanner_id=scanner_id, speed_mmps=speed_mmps,)
            ),
        )
        await stub.StartGlobalAimbotActuationTask(req)

    async def CancelActuationTask(self) -> None:
        stub = self._get_actuation_stub()
        req = util_pb.Empty()
        await stub.CancelGlobalAimbotActuationTask(req)

    # Lasers
    async def FireLaser(self, cam_id: str, duration_ms: int) -> None:
        stub = self._get_laser_stub()
        req = camera_pb.CameraRequest(cam_id=cam_id)
        stream = stub.FireLaser()
        start_time = time.time()
        while time.time() <= start_time + (duration_ms / 1000):
            await stream.write(req)
            sleep_time = min((0.3), (duration_ms / 1000) - (time.time() - start_time))
            if sleep_time <= 0:
                break
            await asyncio.sleep(sleep_time)
        await stream.done_writing()
        await stream

    async def GetNextLaserState(self, timestamp_ms: int = 0) -> laser_pb.LaserStateList:
        stub = self._get_laser_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: laser_pb.LaserStateList = await stub.GetNextLaserState(req)
        return response

    async def ToggleLaserEnabled(self, row_id: int, laser_id: int) -> None:
        stub = self._get_laser_stub()
        req = laser_pb.LaserDescriptor(row_number=row_id, laser_id=laser_id,)
        await stub.ToggleLaserEnabled(req)

    async def EnableRow(self, row_id: int) -> None:
        stub = self._get_laser_stub()
        req = laser_pb.RowRequest(row_number=row_id)
        await stub.EnableRow(req)

    async def DisableRow(self, row_id: int) -> None:
        stub = self._get_laser_stub()
        req = laser_pb.RowRequest(row_number=row_id)
        await stub.DisableRow(req)

    async def ResetLaserMetrics(self, row_id: int, laser_id: int, serial: str) -> None:
        stub = self._get_laser_stub()
        req = laser_pb.LaserDescriptor(row_number=row_id, laser_id=laser_id, serial=serial)
        await stub.ResetLaserMetrics(req)

    async def FixLaserMetrics(
        self, row_id: int, laser_id: int, serial: str, total_fire_count: int, total_fire_time_ms: int, lifetime_sec: int
    ) -> None:
        stub = self._get_laser_stub()
        laser = laser_pb.LaserDescriptor(row_number=row_id, laser_id=laser_id, serial=serial)
        req = laser_pb.FixLaserMetricsRequest(
            laser_descriptor=laser,
            total_fire_count=total_fire_count,
            total_fire_time_ms=total_fire_time_ms,
            lifetime_sec=lifetime_sec,
        )
        await stub.FixLaserMetrics(req)

    async def SetLaserPower(self, row_id: int, laser_id: int, power_level: float) -> None:
        stub = self._get_laser_stub()
        descriptor = laser_pb.LaserDescriptor(row_number=row_id, laser_id=laser_id)
        req = laser_pb.SetLaserPowerRequest(laser_descriptor=descriptor, power_level=power_level)
        await stub.SetLaserPower(req)

    # Focus
    async def TogglePredictGridView(self) -> None:
        stub = self._get_focus_stub()
        req = util_pb.Empty()
        await stub.TogglePredictGridView(req)

    async def GetNextFocusState(self, cam_id: str, timestamp_ms: int = 0) -> focus_pb.FocusState:
        stub = self._get_focus_stub()
        req = focus_pb.FocusStateRequest(cam_id=cam_id, ts=util_pb.Timestamp(timestamp_ms=timestamp_ms))
        response: focus_pb.FocusState = await stub.GetNextFocusState(req)
        return response

    async def StartAutoFocusSpecific(self, cam_id: str) -> None:
        stub = self._get_focus_stub()
        req = camera_pb.CameraRequest(cam_id=cam_id)
        await stub.StartAutoFocusSpecific(req)

    async def StartAutoFocusAll(self) -> None:
        stub = self._get_focus_stub()
        req = util_pb.Empty()
        await stub.StartAutoFocusAll(req)

    async def StopAutoFocus(self) -> None:
        stub = self._get_focus_stub()
        req = util_pb.Empty()
        await stub.StopAutoFocus(req)

    async def SetLensValue(self, cam_id: str, lens_value: int) -> None:
        stub = self._get_focus_stub()
        req = focus_pb.LensSetRequest(cam_id=cam_id, lens_value=lens_value)
        await stub.SetLensValue(req)

    # Cameras
    async def GetCameraList(self, cam_type: str) -> camera_pb.CameraList:
        stub = self._get_camera_stub()
        camera_type = {"predict": camera_pb.PREDICT, "target": camera_pb.TARGET}.get(cam_type, camera_pb.ANY)
        req = camera_pb.CameraListRequest(type=camera_type, include_disconnected=True)
        response: camera_pb.CameraList = await stub.GetCameraList(req)
        return response

    async def GetNextCameraList(self, cam_type: str, timestamp_ms: int = 0) -> camera_pb.CameraList:
        stub = self._get_camera_stub()
        camera_type = {"predict": camera_pb.PREDICT, "target": camera_pb.TARGET}.get(cam_type, camera_pb.ANY)
        req = camera_pb.NextCameraListRequest(
            type=camera_type, ts=util_pb.Timestamp(timestamp_ms=timestamp_ms), include_disconnected=True
        )
        response: camera_pb.CameraList = await stub.GetNextCameraList(req)
        return response

    # Calibration
    async def StartColorCalibration(self, cam_id: str) -> calibration_pb.ColorCalibrationValues:
        stub = self._get_calibration_stub()
        req = camera_pb.CameraRequest(cam_id=cam_id)
        response: calibration_pb.ColorCalibrationValues = await stub.StartColorCalibration(req)
        return response

    async def SaveColorCalibration(self, cam_id: str, red: float, green: float, blue: float) -> None:
        stub = self._get_calibration_stub()
        req = calibration_pb.ColorCalibrationValues(cam_id=cam_id, red=red, green=green, blue=blue)
        await stub.SaveColorCalibration(req)

    # Crosshair
    async def StartAutoCalibrateCrosshair(self, cam_id: str) -> None:
        stub = self._get_crosshair_stub()
        req = camera_pb.CameraRequest(cam_id=cam_id)
        await stub.StartAutoCalibrateCrosshair(req)

    async def StartAutoCalibrateAllCrosshairs(self) -> None:
        stub = self._get_crosshair_stub()
        req = util_pb.Empty()
        await stub.StartAutoCalibrateAllCrosshairs(req)

    async def StopAutoCalibrate(self) -> None:
        stub = self._get_crosshair_stub()
        req = util_pb.Empty()
        await stub.StopAutoCalibrate(req)

    async def GetNextCrosshairState(self, cam_id: str, timestamp_ms: int = 0) -> crosshair_pb.CrosshairPositionState:
        stub = self._get_crosshair_stub()
        req = crosshair_pb.CrosshairPositionRequest(cam_id=cam_id, ts=util_pb.Timestamp(timestamp_ms=timestamp_ms))
        response: crosshair_pb.CrosshairPositionState = await stub.GetNextCrosshairState(req)
        return response

    async def SetCrosshairPosition(self, cam_id: str, x: int, y: int) -> None:
        stub = self._get_crosshair_stub()
        req = crosshair_pb.SetCrosshairPositionRequest(cam_id=cam_id, pos=crosshair_pb.CrosshairPosition(x=x, y=y))
        await stub.SetCrosshairPosition(req)

    async def MoveScanner(self, cam_id: str, x: float, y: float) -> None:
        stub = self._get_crosshair_stub()
        req = crosshair_pb.MoveScannerRequest(cam_id=cam_id, x=x, y=y)
        await stub.MoveScanner(req)

    async def GetNextAutoCrossHairCalState(self, timestamp_ms: int = 0) -> crosshair_pb.AutoCrossHairCalStateResponse:
        stub = self._get_crosshair_stub()
        req = crosshair_pb.AutoCrossHairCalStateRequest(ts=util_pb.Timestamp(timestamp_ms=timestamp_ms))
        response: crosshair_pb.AutoCrossHairCalStateResponse = await stub.GetNextAutoCrossHairCalState(req)
        return response

    # Data Capture
    async def StartDataCapture(self, rate: float, session: str, crop: str, crop_id: str, snap_capture: bool) -> None:
        stub = self._get_data_capture_stub()
        req = data_capture_pb.StartDataCaptureRequest(
            rate=rate, name=session, crop=crop, crop_id=crop_id, snap_capture=snap_capture
        )
        await stub.StartDataCapture(req)

    async def StopDataCapture(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.StopDataCapture(req)

    async def PauseDataCapture(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.PauseDataCapture(req)

    async def ResumeDataCapture(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.ResumeDataCapture(req)

    async def CompleteDataCapture(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.CompleteDataCapture(req)

    async def StartDataCaptureWirelessUpload(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.StartDataCaptureWirelessUpload(req)

    async def StartDataCaptureUSBUpload(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.StartDataCaptureUSBUpload(req)

    async def StopDataCaptureUpload(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.StopDataCaptureUpload(req)

    async def PauseDataCaptureUpload(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.PauseDataCaptureUpload(req)

    async def ResumeDataCaptureUpload(self) -> None:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        await stub.ResumeDataCaptureUpload(req)

    async def StartBackgroundDataCaptureWirelessUpload(self, name: str) -> None:
        stub = self._get_data_capture_stub()
        req = data_capture_pb.SessionName(name=name)
        await stub.StartBackgroundDataCaptureWirelessUpload(req)

    async def StartBackgroundDataCaptureUSBUpload(self, name: str) -> None:
        stub = self._get_data_capture_stub()
        req = data_capture_pb.SessionName(name=name)
        await stub.StartBackgroundDataCaptureUSBUpload(req)

    async def StopBackgroundDataCaptureUpload(self, name: str) -> None:
        stub = self._get_data_capture_stub()
        req = data_capture_pb.SessionName(name=name)
        await stub.StopBackgroundDataCaptureUpload(req)

    async def PauseBackgroundDataCaptureUpload(self, name: str) -> None:
        stub = self._get_data_capture_stub()
        req = data_capture_pb.SessionName(name=name)
        await stub.PauseBackgroundDataCaptureUpload(req)

    async def ResumeBackgroundDataCaptureUpload(self, name: str) -> None:
        stub = self._get_data_capture_stub()
        req = data_capture_pb.SessionName(name=name)
        await stub.ResumeBackgroundDataCaptureUpload(req)

    async def GetNextDataCaptureState(self, timestamp_ms: int = 0) -> data_capture_pb.DataCaptureState:
        stub = self._get_data_capture_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: data_capture_pb.DataCaptureState = await stub.GetNextDataCaptureState(req)
        return response

    async def SnapImages(self, crop: str, crop_id: str, session_name: Optional[str] = None) -> None:
        stub = self._get_data_capture_stub()
        if session_name is None:
            session_name = ""
        req = data_capture_pb.SnapImagesRequest(crop=crop, crop_id=crop_id, session_name=session_name)
        await stub.SnapImages(req)

    async def SnapImage(
        self, crop: str, crop_id: str, cam_id: str, timestamp_ms: Optional[int], session_name: str
    ) -> str:
        stub = self._get_data_capture_stub()
        if timestamp_ms is None:
            timestamp_ms = maka_control_timestamp_ms()
        req = data_capture_pb.SnapImagesRequest(
            crop=crop, crop_id=crop_id, cam_id=cam_id, timestamp_ms=timestamp_ms, session_name=session_name
        )
        session_name = await stub.SnapImages(req)
        return session_name

    async def GetSessions(self) -> data_capture_pb.AvailableSessionResponse:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        response: data_capture_pb.AvailableSessionResponse = await stub.GetSessions(req)
        return response

    async def GetRegularCaptureStatus(self) -> data_capture_pb.RegularCaptureStatus:
        stub = self._get_data_capture_stub()
        req = util_pb.Empty()
        response: data_capture_pb.RegularCaptureStatus = await stub.GetRegularCaptureStatus(req)
        return response

    # Alarms
    async def GetNextAlarmList(self, timestamp_ms: int = 0) -> alarm_pb.AlarmTable:
        stub = self._get_alarm_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: alarm_pb.AlarmTable = await stub.GetNextAlarmList(req)
        return response

    async def GetNextAlarmCount(self, timestamp_ms: int = 0) -> alarm_pb.AlarmCount:
        stub = self._get_alarm_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: alarm_pb.AlarmCount = await stub.GetNextAlarmCount(req)
        return response

    async def GetNextNewAlarmList(self, timestamp_ms: int = 0) -> alarm_pb.AlarmTable:
        stub = self._get_alarm_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        response: alarm_pb.AlarmTable = await stub.GetNextNewAlarmList(req)
        return response

    async def AcknowledgeAlarm(self, identifier: str) -> None:
        stub = self._get_alarm_stub()
        req = alarm_pb.AcknowledgeRequest(identifier=identifier)
        await stub.AcknowledgeAlarm(req)

    async def ResetAlarms(self) -> None:
        stub = self._get_alarm_stub()
        req = util_pb.Empty()
        await stub.ResetAlarms(req)

    async def GetNextAlarmLogCount(self, visible_only: bool) -> alarm_pb.GetNextAlarmLogCountResponse:
        stub = self._get_alarm_stub()
        req = alarm_pb.GetNextAlarmLogCountRequest(ts=util_pb.Timestamp(timestamp_ms=0), visible_only=visible_only)
        resp: alarm_pb.GetNextAlarmLogCountResponse = await stub.GetNextAlarmLogCount(req)
        return resp

    async def GetNextAlarmLog(self, f: int, t: int, visible_only: bool) -> alarm_pb.GetNextAlarmLogResponse:
        stub = self._get_alarm_stub()
        req = alarm_pb.GetNextAlarmLogRequest(
            from_idx=f, to_idx=t, ts=util_pb.Timestamp(timestamp_ms=0), visible_only=visible_only
        )
        resp: alarm_pb.GetNextAlarmLogResponse = await stub.GetNextAlarmLog(req)
        return resp

    async def AutofixAlarm(self, alarmid: str) -> None:
        stub = self._get_alarm_stub()
        req = alarm_pb.AttemptAutofixAlarmRequest(identifier=alarmid)
        await stub.AttemptAutofixAlarm(req)

    async def GetAutofixStatus(self) -> alarm_pb.GetNextAutofixAlarmStatusResponse:
        stub = self._get_alarm_stub()
        req = alarm_pb.GetNextAutofixAlarmStatusRequest(ts=util_pb.Timestamp(timestamp_ms=0))
        resp: alarm_pb.GetNextAutofixAlarmStatusResponse = await stub.GetNextAutofixAlarmStatus(req)
        return resp

    async def PinModel(self, id: str, crop_id: str = "", p2p: bool = False) -> None:
        stub = self._get_model_stub()
        req = model_pb.PinModelRequest(id=id, crop_id=crop_id, p2p=p2p,)
        await stub.PinModel(req)

    async def UnpinModel(self, crop_id: str = "", p2p: bool = False) -> None:
        stub = self._get_model_stub()
        req = model_pb.UnpinModelRequest(crop_id=crop_id, p2p=p2p)
        await stub.UnpinModel(req)

    async def GetNextModelState(
        self, crop: Optional[str], crop_id: Optional[str], ts: int
    ) -> model_pb.GetNextModelStateResponse:
        stub = self._get_model_stub()
        req = model_pb.GetNextModelStateRequest(crop=crop, crop_id=crop_id, ts=util_pb.Timestamp(timestamp_ms=ts))
        resp: model_pb.GetNextModelStateResponse = await stub.GetNextModelState(req)
        return resp

    async def GetNextAllModelState(
        self, crop: Optional[str], crop_id: Optional[str], ts: int
    ) -> model_pb.GetNextModelStateResponse:
        stub = self._get_model_stub()
        req = model_pb.GetNextModelStateRequest(crop=crop, crop_id=crop_id, ts=util_pb.Timestamp(timestamp_ms=ts))
        resp: model_pb.GetNextModelStateResponse = await stub.GetNextAllModelState(req)
        return resp

    async def UpdateModel(self) -> None:
        stub = self._get_model_stub()
        req = util_pb.Empty()
        await stub.UpdateModel(req)

    async def ListEnabledCrops(self, lang: str = "") -> model_pb.EnabledCropList:
        stub = self._get_model_stub()
        req = model_pb.ListCropParameters(lang=lang)
        resp: model_pb.EnabledCropList = await stub.ListEnabledCrops(req)
        return resp

    async def ListCaptureCrops(self, lang: str = "") -> model_pb.EnabledCropList:
        stub = self._get_model_stub()
        req = model_pb.ListCropParameters(lang=lang)
        resp: model_pb.EnabledCropList = await stub.ListCaptureCrops(req)
        return resp

    async def GetNextEnabledCrops(self, timestamp_ms: int = 0, lang: str = "") -> model_pb.GetNextEnabledCropsResponse:
        stub = self._get_model_stub()
        req = model_pb.GetNextEnabledCropsRequest(ts=util_pb.Timestamp(timestamp_ms=timestamp_ms), lang=lang)
        resp: model_pb.GetNextEnabledCropsResponse = await stub.GetNextEnabledCrops(req)
        return resp

    async def GetNextCaptureCrops(self, timestamp_ms: int = 0, lang: str = "") -> model_pb.GetNextCaptureCropsResponse:
        stub = self._get_model_stub()
        req = model_pb.GetNextCaptureCropsRequest(ts=util_pb.Timestamp(timestamp_ms=timestamp_ms), lang=lang)
        resp: model_pb.GetNextCaptureCropsResponse = await stub.GetNextCaptureCrops(req)
        return resp

    async def GetNextSelectedCropID(self, timestamp_ms: int = 0) -> model_pb.GetNextSelectedCropIDResponse:
        stub = self._get_model_stub()
        ts = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        resp: model_pb.GetNextSelectedCropIDResponse = await stub.GetNextSelectedCropID(ts)
        return resp

    async def RefreshDefaultModelParameters(self, crop_id: str, model_id: str) -> None:
        stub = self._get_model_stub()
        pair = model_pb.CropModelPair(crop_id=crop_id, model_id=model_id)
        req = model_pb.RefreshDefaultModelParametersRequest(cropModelPairs=[pair])
        await stub.RefreshDefaultModelParameters(req)

    async def SelectCrop(self, crop_id: str) -> None:
        stub = self._get_model_stub()
        req = model_pb.SelectCropRequest(crop_id=crop_id)
        await stub.SelectCrop(req)

    async def TriggerDownload(self) -> None:
        stub = self._get_model_stub()
        req = util_pb.Empty()
        await stub.TriggerDownload(req)

    async def DownloadModel(self, model_id: str) -> None:
        stub = self._get_model_stub()
        req = model_pb.DownloadModelRequest(model_id=model_id)
        await stub.DownloadModel(req)

    async def SyncCropIDs(self, force_cache_refresh: bool) -> None:
        stub = self._get_model_stub()
        req = model_pb.SyncCropIDsRequest(force_cache_refresh=force_cache_refresh)
        await stub.SyncCropIDs(req)

    async def GetNextModelNicknames(self, model_ids: List[str], ts: int = 0) -> model_pb.GetModelNicknamesResponse:
        stub = self._get_model_stub()
        req = model_pb.GetModelNicknamesRequest(model_ids=model_ids, ts=util_pb.Timestamp(timestamp_ms=ts))
        resp: model_pb.GetModelNicknamesResponse = await stub.GetNextModelNicknames(req)
        return resp

    async def GetModelNicknames(self, model_ids: List[str]) -> model_pb.GetModelNicknamesResponse:
        stub = self._get_model_stub()
        req = model_pb.GetModelNicknamesRequest(model_ids=model_ids)
        resp: model_pb.GetModelNicknamesResponse = await stub.GetModelNicknames(req)
        return resp

    async def SetModelNickname(self, model_id: str, model_nickname: str) -> None:
        stub = self._get_model_stub()
        req = model_pb.SetModelNicknameRequest(model_id=model_id, model_nickname=model_nickname)
        await stub.SetModelNickname(req)

    async def GetNextModelHistory(
        self,
        start_timestamp: int,
        count: int,
        reverse: bool,
        match_filter: Dict[str, Any],
        event_type_matcher: Dict[str, bool],
        ts: int = 0,
    ) -> model_pb.ModelHistoryResponse:
        stub = self._get_model_stub()
        filter = model_pb.ModelEvent()
        json_format.ParseDict(match_filter, filter)
        matcher = model_pb.ModelEventTypeMatcher()
        json_format.ParseDict(event_type_matcher, matcher)
        req = model_pb.ModelHistoryRequest(
            start_timestamp=start_timestamp,
            count=count,
            reverse=reverse,
            match_filter=filter,
            event_type_matcher=matcher,
            ts=util_pb.Timestamp(timestamp_ms=ts),
        )
        resp: model_pb.ModelHistoryResponse = await stub.GetNextModelHistory(req)
        return resp

    async def GetModelHistory(
        self,
        start_timestamp: int,
        count: int,
        reverse: bool,
        match_filter: Dict[str, Any],
        event_type_matcher: Dict[str, bool],
    ) -> model_pb.ModelHistoryResponse:
        stub = self._get_model_stub()
        filter = model_pb.ModelEvent()
        json_format.ParseDict(match_filter, filter)
        matcher = model_pb.ModelEventTypeMatcher()
        json_format.ParseDict(event_type_matcher, matcher)
        req = model_pb.ModelHistoryRequest(
            start_timestamp=start_timestamp,
            count=count,
            reverse=reverse,
            match_filter=filter,
            event_type_matcher=matcher,
        )
        resp: model_pb.ModelHistoryResponse = await stub.GetModelHistory(req)
        return resp

    async def GetNextFeatureFlags(self, timestamp_ms: int = 0) -> util_pb.FeatureFlags:
        stub = self._get_features_stub()
        ts = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        resp: util_pb.FeatureFlags = await stub.GetNextFeatureFlags(ts)
        return resp

    async def GetRobotConfiguration(self) -> features_pb.RobotConfiguration:
        stub = self._get_features_stub()
        resp: features_pb.RobotConfiguration = await stub.GetRobotConfiguration(util_pb.Empty())
        return resp

    async def GetNextSoftwareState(self, timestamp_ms: int, get_host_states: bool) -> software_pb.SoftwareVersionState:
        stub = self._get_software_stub()
        req = software_pb.SoftwareVersionStateRequest(
            ts=util_pb.Timestamp(timestamp_ms=timestamp_ms), get_host_states=get_host_states
        )
        resp: software_pb.SoftwareVersionState = await stub.GetNextSoftwareVersionState(req)
        return resp

    async def UpdateSoftware(self) -> None:
        stub = self._get_software_stub()
        req = util_pb.Empty()
        await stub.Update(req)

    async def RevertSoftware(self) -> None:
        stub = self._get_software_stub()
        req = util_pb.Empty()
        await stub.Revert(req)

    async def FixVersionMismatch(self) -> None:
        stub = self._get_software_stub()
        req = util_pb.Empty()
        await stub.FixVersionMismatch(req)

    async def UpdateHostSoftware(self, host_id: int) -> None:
        stub = self._get_software_stub()
        req = software_pb.UpdateHostRequest(host_id=host_id)
        await stub.UpdateHost(req)

    async def LoadBandingDefs(self) -> banding_pb.LoadBandingDefsResponse:
        stub = self._get_banding_stub()
        req = util_pb.Empty()
        resp: banding_pb.LoadBandingDefsResponse = await stub.LoadBandingDefs(req)
        return resp

    async def SaveBandingDef(self, req: banding_pb.SaveBandingDefRequest) -> None:
        stub = self._get_banding_stub()
        await stub.SaveBandingDef(req)

    async def SetActiveBandingDef(self, req: banding_pb.SetActiveBandingDefRequest) -> None:
        stub = self._get_banding_stub()
        await stub.SetActiveBandingDef(req)

    async def DeleteBandingDef(self, req: banding_pb.DeleteBandingDefRequest) -> None:
        stub = self._get_banding_stub()
        await stub.DeleteBandingDef(req)

    async def GetActiveBandingDef(self) -> banding_pb.GetActiveBandingDefResponse:
        stub = self._get_banding_stub()
        req = util_pb.Empty()
        resp: banding_pb.GetActiveBandingDefResponse = await stub.GetActiveBandingDef(req)
        return resp

    async def GetVisualizationData(
        self, timestamp_ms: int, row_id: int
    ) -> banding_pb.GetNextVisualizationData2Response:
        stub = self._get_banding_stub()
        req = banding_pb.GetNextVisualizationDataRequest(ts=util_pb.Timestamp(timestamp_ms=timestamp_ms), row_id=row_id)
        resp: banding_pb.GetNextVisualizationData2Response = await stub.GetNextVisualizationData2(req)
        return resp

    async def GetVisualizationDataForAllRows(self) -> banding_pb.GetNextVisualizationDataForAllRowsResponse:
        stub = self._get_banding_stub()
        req = banding_pb.GetNextVisualizationDataForAllRowsRequest(ts=util_pb.Timestamp(timestamp_ms=0))
        resp: banding_pb.GetNextVisualizationDataForAllRowsResponse = await stub.GetNextVisualizationDataForAllRows(req)
        return resp

    async def GetVisualizationMetadata(self) -> banding_pb.GetVisualizationMetadataResponse:
        stub = self._get_banding_stub()
        req = util_pb.Empty()
        resp: banding_pb.GetVisualizationMetadataResponse = await stub.GetVisualizationMetadata(req)
        return resp

    async def GetDimensions(self, row_id: int) -> banding_pb.GetActiveBandingDefResponse:
        stub = self._get_banding_stub()
        resp: banding_pb.GetActiveBandingDefResponse = await stub.GetDimensions(
            banding_pb.GetDimensionsRequest(row_id=row_id)
        )
        return resp

    async def SetBandingEnabled(self, enabled: bool) -> banding_pb.SetBandingEnabledResponse:
        stub = self._get_banding_stub()
        resp: banding_pb.SetBandingEnabledResponse = await stub.SetBandingEnabled(
            banding_pb.SetBandingEnabledRequest(enabled=enabled)
        )
        return resp

    async def IsBandingEnabled(self) -> banding_pb.IsBandingEnabledResponse:
        stub = self._get_banding_stub()
        resp: banding_pb.IsBandingEnabledResponse = await stub.IsBandingEnabled(util_pb.Empty())
        return resp

    async def SetDynamicBandingEnabled(self, enabled: bool) -> banding_pb.SetBandingEnabledResponse:
        stub = self._get_banding_stub()
        resp: banding_pb.SetBandingEnabledResponse = await stub.SetDynamicBandingEnabled(
            banding_pb.SetBandingEnabledRequest(enabled=enabled)
        )
        return resp

    async def IsDynamicBandingEnabled(self) -> banding_pb.IsBandingEnabledResponse:
        stub = self._get_banding_stub()
        resp: banding_pb.IsBandingEnabledResponse = await stub.IsDynamicBandingEnabled(util_pb.Empty())
        return resp

    async def GetNextBandingState(self, timestamp_ms: int = 0) -> banding_pb.GetNextBandingStateResponse:
        stub = self._get_banding_stub()
        req = util_pb.Timestamp(timestamp_ms=timestamp_ms)
        resp: banding_pb.GetNextBandingStateResponse = await stub.GetNextBandingState(req)
        return resp

    # debug
    async def SetLogging(self, level: str, component: str, row_id: int) -> None:
        stub = self._get_debug_stub()
        req = debug_pb.SetLogLevelRequest(level=logging_pb.LogLevel.Value(level), component=component, row_num=row_id)
        await stub.SetLogLevel(req)

    async def StartSavingCropLineDetectionReplay(self, filename: str, ttl_ms: int) -> None:
        stub = self._get_debug_stub()
        req = weed_tracking_pb.StartSavingCropLineDetectionReplayRequest(filename=filename, ttl_ms=ttl_ms)
        await stub.StartSavingCropLineDetectionReplay(req)

    async def AddMockSpatialMetricsBlock(self) -> None:
        stub = self._get_debug_stub()
        await stub.AddMockSpatialMetricsBlock(util_pb.Empty())

    async def DeleteProfileSyncData(self) -> None:
        stub = self._get_debug_stub()
        await stub.DeleteProfileSyncData(util_pb.Empty())

    # weeding diagnostics
    async def RecordWeedingDiagnostics(self, name: str, ttl_sec: int, crop_images: float, weed_images: float) -> None:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.RecordWeedingDiagnosticsRequest(
            name=name, ttl_sec=ttl_sec, crop_images_per_sec=crop_images, weed_images_per_sec=weed_images
        )
        await stub.RecordWeedingDiagnostics(req)

    async def GetDiagnosticsRecordingsList(self) -> weeding_diagnostics_pb.GetRecordingsListResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetRecordingsListRequest()
        resp: weeding_diagnostics_pb.GetRecordingsListResponse = await stub.GetRecordingsList(req)
        return resp

    async def OpenDiagnosticsRecording(self, name: str) -> weeding_diagnostics_pb.OpenRecordingResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.OpenRecordingRequest(recording_name=name)
        resp: weeding_diagnostics_pb.OpenRecordingResponse = await stub.OpenRecording(req)
        return resp

    async def StartUpload(self, name: str) -> weeding_diagnostics_pb.OpenRecordingResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.StartUploadRequest(recording_name=name)
        resp: weeding_diagnostics_pb.OpenRecordingResponse = await stub.StartUpload(req)
        return resp

    async def GetUploadState(self, name: str) -> weeding_diagnostics_pb.OpenRecordingResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetNextUploadStateRequest(
            recording_name=name, ts=util_pb.Timestamp(timestamp_ms=0)
        )
        resp: weeding_diagnostics_pb.OpenRecordingResponse = await stub.GetNextUploadState(req)
        return resp

    async def DeleteDiagnosticsRecording(self, name: str) -> util_pb.Empty:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.DeleteRecordingRequest(recording_name=name)
        resp: util_pb.Empty = await stub.DeleteRecording(req)
        return resp

    async def GetRecordedSnapshot(
        self, recording_name: str, row_id: int, snapshot_num: int
    ) -> weeding_diagnostics_pb.GetSnapshotResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetSnapshotRequest(
            recording_name=recording_name, row_id=row_id, snapshot_number=snapshot_num
        )
        resp: weeding_diagnostics_pb.GetSnapshotResponse = await stub.GetSnapshot(req)
        return resp

    async def GetDeepweedPredictionsCount(
        self, recording_name: str, row_id: int, camera_num: int
    ) -> weeding_diagnostics_pb.GetDeepweedPredictionsCountResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetDeepweedPredictionsCountRequest(
            recording_name=recording_name, row=row_id, cam_id=camera_num
        )
        resp: weeding_diagnostics_pb.GetDeepweedPredictionsCountResponse = await stub.GetDeepweedPredictionsCount(req)
        return resp

    async def GetDeepweedPredictions(
        self, recording_name: str, row_id: int, camera_num: int, index: int
    ) -> weeding_diagnostics_pb.GetDeepweedPredictionsResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetDeepweedPredictionsRequest(
            recording_name=recording_name, row=row_id, cam_id=camera_num, idx=index
        )
        resp: weeding_diagnostics_pb.GetDeepweedPredictionsResponse = await stub.GetDeepweedPredictions(req)
        return resp

    async def GetRotaryTicks(self, recording_name: str, row_id: int) -> weeding_diagnostics_pb.GetRotaryTicksResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetRotaryTicksRequest(recording_name=recording_name, row_id=row_id)
        resp: weeding_diagnostics_pb.GetRotaryTicksResponse = await stub.GetRotaryTicks(req)
        return resp

    async def GetRecordedTrajectoryData(
        self, recording_name: str, row_id: int, trajectory_id: int
    ) -> weeding_diagnostics_pb.TrajectoryData:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetTrajectoryDataRequest(
            recording_name=recording_name, row_id=row_id, trajectory_id=trajectory_id,
        )
        resp: weeding_diagnostics_pb.TrajectoryData = await stub.GetTrajectoryData(req)
        return resp

    async def FindTrajectory(
        self, recording_name: str, row_id: int, trajectory_id: int
    ) -> weeding_diagnostics_pb.FindTrajectoryResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.FindTrajectoryRequest(
            recording_name=recording_name, row_id=row_id, trajectory_id=trajectory_id,
        )
        resp: weeding_diagnostics_pb.FindTrajectoryResponse = await stub.FindTrajectory(req)
        return resp

    async def GetRecordedTrajectoryPredictImage(
        self, recording_name: str, row_id: int, trajectory_id: int,
    ) -> List[weeding_diagnostics_pb.ImageChunk]:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetTrajectoryPredictImageRequest(
            recording_name=recording_name, row_id=row_id, trajectory_id=trajectory_id,
        )
        resp: List[weeding_diagnostics_pb.ImageChunk] = [r async for r in stub.GetTrajectoryPredictImage(req)]
        return resp

    async def GetRecordedTrajectoryTargetImage(
        self, recording_name: str, row_id: int, trajectory_id: int, image_name: str,
    ) -> List[weeding_diagnostics_pb.ImageChunk]:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetTrajectoryTargetImageRequest(
            recording_name=recording_name, row_id=row_id, trajectory_id=trajectory_id, image_name=image_name,
        )
        resp: List[weeding_diagnostics_pb.ImageChunk] = [r async for r in stub.GetTrajectoryTargetImage(req)]
        return resp

    async def GetPredictImageData(
        self, recording_name: str, row_id: int, image_name: str
    ) -> weeding_diagnostics_pb.TrajectoryData:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetPredictImageMetadataRequest(
            recording_name=recording_name, row_id=row_id, image_name=image_name,
        )
        resp: weeding_diagnostics_pb.TrajectoryData = await stub.GetPredictImageMetadata(req)
        return resp

    async def GetPredictImage(
        self, recording_name: str, row_id: int, image_name: str,
    ) -> List[weeding_diagnostics_pb.ImageChunk]:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetPredictImageRequest(
            recording_name=recording_name, row_id=row_id, image_name=image_name,
        )
        resp: List[weeding_diagnostics_pb.ImageChunk] = [r async for r in stub.GetPredictImage(req)]
        return resp

    async def GetCurrentTrajectories(self, row_id: int) -> weed_tracking_pb.DiagnosticsSnapshot:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetCurrentTrajectoriesRequest(row_id=row_id)
        resp: weed_tracking_pb.DiagnosticsSnapshot = await stub.GetCurrentTrajectories(req)
        return resp

    async def SnapshotPredictImages(self, row_id: int) -> weeding_diagnostics_pb.SnapshotPredictImagesResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.SnapshotPredictImagesRequest(row_id=row_id)
        resp: weeding_diagnostics_pb.SnapshotPredictImagesResponse = await stub.SnapshotPredictImages(req)
        return resp

    async def GetChipForPredictImage(
        self, row_id: int, pcam_id: str, timestamp_ms: int, center_x_px: int, center_y_px: int
    ) -> weeding_diagnostics_pb.GetChipForPredictImageResponse:
        stub = self._get_weeding_diagnostics_stub()
        req = weeding_diagnostics_pb.GetChipForPredictImageRequest(
            row_id=row_id, pcam_id=pcam_id, timestamp_ms=timestamp_ms, center_x_px=center_x_px, center_y_px=center_y_px
        )
        resp: weeding_diagnostics_pb.GetChipForPredictImageResponse = await stub.GetChipForPredictImage(req)
        return resp

    async def StartRecordingAimbotInputs(
        self, name: str, ttl_ms: int, rotary_ticks: bool = True, deepweed: bool = True, lane_heights: bool = True,
    ) -> None:
        stub = self._get_debug_stub()
        req = weed_tracking_pb.RecordAimbotInputRequest(
            name=name, ttl_ms=ttl_ms, rotary_ticks=rotary_ticks, deepweed=deepweed, lane_heights=lane_heights
        )
        await stub.StartRecordingAimbotInputs(req)

    # messages
    async def SendMessage(self, message: str) -> util_pb.Timestamp:
        stub = self._get_messages_stub()
        req = messages_pb.MessageRequest(message=message)
        resp: util_pb.Timestamp = await stub.SendMessage(req)
        return resp

    async def GetNextMessages(self, ts: int) -> messages_pb.MessagesResponse:
        stub = self._get_messages_stub()
        req = util_pb.Timestamp(timestamp_ms=ts)
        resp: messages_pb.MessagesResponse = await stub.GetNextMessages(req)
        return resp

    # reporting
    async def GetNextLocationHistory(self, ts: int) -> reporting_pb.LocationHistory:
        stub = self._get_location_history_stub()
        req = util_pb.Timestamp(timestamp_ms=ts)
        resp: reporting_pb.LocationHistory = await stub.GetNextLocationHistory(req)
        return resp

    async def GetNextTasks(self, ts: int) -> startup_task_pb.GetNextTasksResponse:
        stub = self._get_startup_task_stub()
        req = util_pb.Timestamp(timestamp_ms=ts)
        resp: startup_task_pb.GetNextTasksResponse = await stub.GetNextTasks(req)
        return resp

    # Thinning
    async def GetNextConfigurations(self, ts: int = 0) -> thinning_pb.GetNextConfigurationsResponse:
        stub = self._get_thinning_stub()
        req = util_pb.Timestamp(timestamp_ms=ts)
        resp: thinning_pb.GetNextConfigurationsResponse = await stub.GetNextConfigurations(req)
        return resp

    async def GetNextActiveConf(self, ts: int) -> thinning_pb.GetNextActiveConfResponse:
        stub = self._get_thinning_stub()
        req = util_pb.Timestamp(timestamp_ms=ts)
        resp: thinning_pb.GetNextActiveConfResponse = await stub.GetNextActiveConf(req)
        return resp

    async def DefineConfiguration(
        self, conf: thinning_pb_base.ConfigDefinition, set_active: bool
    ) -> thinning_pb.DefineConfigurationResponse:
        stub = self._get_thinning_stub()
        req = thinning_pb.DefineConfigurationRequest(
            definition=conf, set_active=set_active, ver=thinning_pb.THIN_CONF_V2
        )
        resp: thinning_pb.DefineConfigurationResponse = await stub.DefineConfiguration(req)
        return resp

    async def SetActiveConfig(self, id: str) -> thinning_pb.SetActiveConfigResponse:
        stub = self._get_thinning_stub()
        req = thinning_pb.SetActiveConfigRequest(id=id, ver=thinning_pb.THIN_CONF_V2)
        resp: thinning_pb.SetActiveConfigResponse = await stub.SetActiveConfig(req)
        return resp

    async def DeleteConfig(self, id: str, new_active_id: Optional[str] = None) -> thinning_pb.DeleteConfigResponse:
        stub = self._get_thinning_stub()
        req = thinning_pb.DeleteConfigRequest(id=id, new_active_id=new_active_id if new_active_id is not None else "")
        resp: thinning_pb.DeleteConfigResponse = await stub.DeleteConfig(req)
        return resp

    # Jobs
    async def CreateJob(self, name: str, active: bool, acreage: float) -> jobs_pb.CreateJobResponse:
        stub = self._get_jobs_stub()
        req = jobs_pb.CreateJobRequest(name=name, active=active, expectedAcreage=acreage)
        resp: jobs_pb.CreateJobResponse = await stub.CreateJob(req)
        return resp

    async def UpdateJob(self, id: str, name: str, ts: int, acreage: float) -> None:
        stub = self._get_jobs_stub()
        job_desc = jobs_pb.JobDescription(jobId=id, name=name, timestampMs=ts)
        req = jobs_pb.UpdateJobRequest(jobDescription=job_desc, expectedAcreage=acreage)
        await stub.UpdateJob(req)

    async def StopActiveJob(self) -> None:
        stub = self._get_jobs_stub()
        await stub.StopActiveJob(util_pb.Empty())

    async def GetNextJobs(self, ts: int) -> jobs_pb.GetNextJobsResponse:
        stub = self._get_jobs_stub()
        resp: jobs_pb.GetNextJobsResponse = await stub.GetNextJobs(
            jobs_pb.GetNextJobsRequest(timestamp=util_pb.Timestamp(timestamp_ms=ts))
        )
        return resp

    async def GetActiveJobId(self) -> jobs_pb.GetNextActiveJobIdResponse:
        stub = self._get_jobs_stub()
        resp: jobs_pb.GetNextActiveJobIdResponse = await stub.GetNextActiveJobId(
            jobs_pb.GetNextActiveJobIdRequest(timestamp=util_pb.Timestamp(timestamp_ms=0))
        )
        return resp

    async def GetJob(self, id: str, ts: int) -> jobs_pb.GetJobResponse:
        stub = self._get_jobs_stub()
        req = jobs_pb.GetNextJobRequest(jobId=id, ts=util_pb.Timestamp(timestamp_ms=ts))
        resp: jobs_pb.GetJobResponse = await stub.GetNextJob(req)
        return resp

    async def DeleteJob(self, id: str) -> util_pb.Empty:
        stub = self._get_jobs_stub()
        req = jobs_pb.DeleteJobRequest(jobId=id)
        resp: util_pb.Empty = await stub.DeleteJob(req)
        return resp

    async def MarkJobCompleted(self, id: str) -> util_pb.Empty:
        stub = self._get_jobs_stub()
        req = jobs_pb.MarkJobCompletedRequest(jobId=id)
        resp: util_pb.Empty = await stub.MarkJobCompleted(req)
        return resp

    async def StartJob(self, id: str) -> None:
        stub = self._get_jobs_stub()
        req = jobs_pb.StartJobRequest(jobId=id)
        await stub.StartJob(req)

    async def GetActiveJobMetrics(self) -> jobs_pb.GetActiveJobMetricsResponse:
        stub = self._get_jobs_stub()
        resp: jobs_pb.GetActiveJobMetricsResponse = await stub.GetActiveJobMetrics(util_pb.Empty())
        return resp

    # Embeddings
    async def GetNextActiveCategoryCollectionId(
        self, ts: int = 0
    ) -> category_collection_pb.GetNextActiveCategoryCollectionIdResponse:
        resp: category_collection_pb.GetNextActiveCategoryCollectionIdResponse = await self._get_category_collection_stub().GetNextActiveCategoryCollectionId(
            util_pb.Timestamp(timestamp_ms=ts)
        )
        return resp

    async def GetNextCategoryCollectionsData(
        self, ts: int = 0
    ) -> category_collection_pb.GetNextCategoryCollectionsDataResponse:
        resp: category_collection_pb.GetNextCategoryCollectionsDataResponse = await self._get_category_collection_stub().GetNextCategoryCollectionsData(
            util_pb.Timestamp(timestamp_ms=ts)
        )
        return resp

    async def GetNextCategoryData(self, ts: int = 0) -> category_pb.GetNextCategoryDataResponse:
        resp: category_pb.GetNextCategoryDataResponse = await self._get_category_stub().GetNextCategoryData(
            util_pb.Timestamp(timestamp_ms=ts)
        )
        return resp

    async def ReloadCategoryCollection(self) -> None:
        await self._get_category_collection_stub().ReloadCategoryCollection(util_pb.Empty())

    async def GetChipMetadata(self, ts: int = 0) -> chip_pb.GetChipMetadataResponse:
        resp: chip_pb.GetChipMetadataResponse = await self._get_chip_stub().GetChipMetadata(util_pb.Empty())
        return resp

    async def GetDownloadedChipIds(self, ts: int = 0) -> chip_pb.ChipIdsResponse:
        resp: chip_pb.ChipIdsResponse = await self._get_chip_stub().GetDownloadedChipIds(util_pb.Empty())
        return resp

    async def GetSyncedChipIds(self, ts: int = 0) -> chip_pb.ChipIdsResponse:
        resp: chip_pb.ChipIdsResponse = await self._get_chip_stub().GetSyncedChipIds(util_pb.Empty())
        return resp

    # Almanac
    async def GetConfigData(self) -> almanac_pb.GetConfigDataResponse:
        resp: almanac_pb.GetConfigDataResponse = await self._get_almanac_stub().GetConfigData(util_pb.Empty())
        return resp

    async def GetNextConfigData(self, ts: int = 0, lang: str = "") -> almanac_pb.GetNextConfigDataResponse:
        stub = self._get_almanac_stub()
        resp: almanac_pb.GetNextConfigDataResponse = await stub.GetNextConfigData(
            almanac_pb.GetNextConfigDataRequest(ts=util_pb.Timestamp(timestamp_ms=ts), lang=lang,)
        )
        return resp

    async def LoadAlmanacConfig(self, id: str) -> almanac_pb.LoadAlmanacConfigResponse:
        resp: almanac_pb.LoadAlmanacConfigResponse = await self._get_almanac_stub().LoadAlmanacConfig(
            almanac_pb.LoadAlmanacConfigRequest(id=id)
        )
        return resp

    async def SaveAlmanacConfig(self, cfg: almanac_base_pb.AlmanacConfig, set_active: bool) -> str:
        stub = self._get_almanac_stub()
        resp = cast(
            almanac_pb.SaveAlmanacConfigResponse,
            await stub.SaveAlmanacConfig(almanac_pb.SaveAlmanacConfigRequest(config=cfg, set_active=set_active)),
        )
        return resp.id

    async def SetActiveAlmanacConfig(self, id: str) -> None:
        await self._get_almanac_stub().SetActiveAlmanacConfig(almanac_pb.SetActiveAlmanacConfigRequest(id=id))

    async def DeleteAlmanacConfig(self, id: str, new_active_id: Optional[str] = None) -> None:
        await self._get_almanac_stub().DeleteAlmanacConfig(
            almanac_pb.DeleteAlmanacConfigRequest(
                id=id, new_active_id=new_active_id if new_active_id is not None else ""
            )
        )

    async def GetNextAlmanacConfig(self, ts: int = 0) -> almanac_pb.GetNextAlmanacConfigResponse:
        resp: almanac_pb.GetNextAlmanacConfigResponse = await self._get_almanac_stub().GetNextAlmanacConfig(
            util_pb.Timestamp(timestamp_ms=ts)
        )
        return resp

    async def LoadDiscriminatorConfig(self, id: str) -> almanac_pb.LoadDiscriminatorConfigResponse:
        resp: almanac_pb.LoadDiscriminatorConfigResponse = await self._get_almanac_stub().LoadDiscriminatorConfig(
            almanac_pb.LoadDiscriminatorConfigRequest(id=id)
        )
        return resp

    async def SaveDiscriminatorConfig(self, cfg: almanac_base_pb.DiscriminatorConfig, set_active: bool) -> str:
        stub = self._get_almanac_stub()
        resp = cast(
            almanac_pb.SaveDiscriminatorConfigResponse,
            await stub.SaveDiscriminatorConfig(
                almanac_pb.SaveDiscriminatorConfigRequest(config=cfg, associate_with_active_crop=set_active)
            ),
        )
        return resp.id

    async def SetActiveDiscriminatorConfig(self, id: str, crop_id: str = "") -> None:
        await self._get_almanac_stub().SetActiveDiscriminatorConfig(
            almanac_pb.SetActiveDiscriminatorConfigRequest(id=id, crop_id=crop_id)
        )

    async def DeleteDiscriminatorConfig(self, id: str) -> None:
        await self._get_almanac_stub().DeleteDiscriminatorConfig(almanac_pb.DeleteDiscriminatorConfigRequest(id=id))

    async def GetNextDiscriminatorConfig(self, ts: int = 0) -> almanac_pb.GetNextDiscriminatorConfigResponse:
        resp: almanac_pb.GetNextDiscriminatorConfigResponse = await self._get_almanac_stub().GetNextDiscriminatorConfig(
            util_pb.Timestamp(timestamp_ms=ts)
        )
        return resp

    async def GetNextModelinatorConfig(self, ts: int = 0) -> almanac_pb.GetNextModelinatorConfigResponse:
        resp: almanac_pb.GetNextModelinatorConfigResponse = await self._get_almanac_stub().GetNextModelinatorConfig(
            util_pb.Timestamp(timestamp_ms=ts)
        )
        return resp

    async def SaveModelinatorConfig(self, cfg: almanac_base_pb.ModelinatorConfig) -> None:
        await self._get_almanac_stub().SaveModelinatorConfig(almanac_pb.SaveModelinatorConfigRequest(config=cfg))

    async def FetchModelinatorConfig(self, model_id: str, crop_id: str) -> almanac_pb.FetchModelinatorConfigResponse:
        resp: almanac_pb.FetchModelinatorConfigResponse = await self._get_almanac_stub().FetchModelinatorConfig(
            almanac_pb.FetchModelinatorConfigRequest(model_id=model_id, crop_id=crop_id)
        )

        return resp

    async def ResetModelinatorConfig(self) -> None:
        await self._get_almanac_stub().ResetModelinatorConfig(almanac_pb.ResetModelinatorConfigRequest())

    async def StartPlantCaptcha(
        self, name: str, model_id: str, crop_id: str, crop_name: str,
    ) -> plant_captcha_pb.StartPlantCaptchaResponse:
        resp: plant_captcha_pb.StartPlantCaptchaResponse = await self._get_captcha_stub().StartPlantCaptcha(
            plant_captcha_pb.StartPlantCaptchaRequest(
                plant_captcha=plant_captcha_pb.PlantCaptcha(
                    model_id=model_id, name=name, crop_id=crop_id, crop_name=crop_name
                )
            )
        )
        return resp

    async def GetPlantCaptchaStatus(self, ts: int = 0) -> plant_captcha_pb.GetNextPlantCaptchaStatusResponse:
        resp: plant_captcha_pb.GetNextPlantCaptchaStatusResponse = await self._get_captcha_stub().GetNextPlantCaptchaStatus(
            plant_captcha_pb.GetNextPlantCaptchaStatusRequest(ts=util_pb.Timestamp(timestamp_ms=ts))
        )
        return resp

    async def GetPlantCaptchasList(self) -> plant_captcha_pb.GetNextPlantCaptchasListResponse:
        resp: plant_captcha_pb.GetNextPlantCaptchasListResponse = await self._get_captcha_stub().GetNextPlantCaptchasList(
            plant_captcha_pb.GetNextPlantCaptchasListRequest(ts=util_pb.Timestamp(timestamp_ms=0))
        )
        return resp

    async def DeletePlantCaptcha(self, name: str) -> None:
        await self._get_captcha_stub().DeletePlantCaptcha(plant_captcha_pb.DeletePlantCaptchaRequest(name=name))

    async def GetPlantCaptcha(self, name: str) -> plant_captcha_pb.GetPlantCaptchaResponse:
        resp: plant_captcha_pb.GetPlantCaptchaResponse = await self._get_captcha_stub().GetPlantCaptcha(
            plant_captcha_pb.GetPlantCaptchaRequest(name=name)
        )
        return resp

    async def CancelPlantCaptcha(self) -> None:
        await self._get_captcha_stub().CancelPlantCaptcha(util_pb.Empty())

    async def UploadPlantCaptcha(self, name: str) -> None:
        await self._get_captcha_stub().StartPlantCaptchaUpload(
            plant_captcha_pb.StartPlantCaptchaUploadRequest(name=name)
        )

    async def GetPlantCaptchaUploadState(self, name: str) -> plant_captcha_pb.GetNextPlantCaptchaUploadStateResponse:
        resp: plant_captcha_pb.GetNextPlantCaptchaUploadStateResponse = await self._get_captcha_stub().GetNextPlantCaptchaUploadState(
            plant_captcha_pb.GetNextPlantCaptchaUploadStateRequest(name=name)
        )
        return resp

    async def SubmitPlantCaptchaResult(self, name: str, item_id: str, result: str) -> None:
        prediction = weed_tracking_pb.IGNORE
        if result == "CROP":
            prediction = weed_tracking_pb.CROP
        elif result == "WEED":
            prediction = weed_tracking_pb.WEED
        elif result == "OTHER":
            prediction = weed_tracking_pb.IGNORE
        req = plant_captcha_pb.SubmitPlantCaptchaResultsRequest(
            name=name, results=[plant_captcha_pb.PlantCaptchaItemResult(id=item_id, user_prediction=prediction),]
        )
        await self._get_captcha_stub().SubmitPlantCaptchaResults(req)

    async def GetPlantCaptchaResult(
        self, name: str, item_id: str
    ) -> plant_captcha_pb.GetPlantCaptchaItemResultsResponse:
        req = plant_captcha_pb.GetPlantCaptchaItemResultsRequest(name=name, id=[item_id])
        resp: plant_captcha_pb.GetPlantCaptchaItemResultsResponse = await self._get_captcha_stub().GetPlantCaptchaItemResults(
            req
        )
        return resp

    async def CalculatePlantCaptcha(self, name: str) -> plant_captcha_pb.CalculatePlantCaptchaResponse:
        resp: plant_captcha_pb.CalculatePlantCaptchaResponse = await self._get_captcha_stub().CalculatePlantCaptcha(
            plant_captcha_pb.CalculatePlantCaptchaRequest(name=name)
        )
        return resp

    async def GetOriginalModelinatorConfig(self, name: str) -> plant_captcha_pb.GetOriginalModelinatorConfigResponse:
        resp: plant_captcha_pb.GetOriginalModelinatorConfigResponse = await self._get_captcha_stub().GetOriginalModelinatorConfig(
            plant_captcha_pb.GetOriginalModelinatorConfigRequest(name=name)
        )
        return resp

    async def GetCaptchaRowStatus(self) -> plant_captcha_pb.GetCaptchaRowStatusResponse:
        resp: plant_captcha_pb.GetCaptchaRowStatusResponse = await self._get_captcha_stub().GetCaptchaRowStatus(
            util_pb.Empty()
        )
        return resp

    async def CancelPlantCaptchaOnRow(self, row_id: int) -> None:
        await self._get_captcha_stub().CancelPlantCaptchaOnRow(
            plant_captcha_pb.CancelPlantCaptchaOnRowRequest(row_id=row_id)
        )

    # Target Velocity Estimator
    async def GetNextAvailableTVEProfiles(self, ts: int = 0) -> tve_pb.GetNextAvailableTVEProfilesResponse:
        resp: tve_pb.GetNextAvailableTVEProfilesResponse = await self._get_tve_stub().GetNextAvailableProfiles(
            util_pb.Timestamp(timestamp_ms=ts)
        )
        return resp

    async def GetNextActiveTVEProfile(self, ts: int = 0) -> tve_pb.GetNextActiveTVEProfileResponse:
        resp: tve_pb.GetNextActiveTVEProfileResponse = await self._get_tve_stub().GetNextActiveProfile(
            util_pb.Timestamp(timestamp_ms=ts)
        )
        return resp

    async def LoadTVEProfile(self, id: str) -> tve_pb.LoadTVEProfileResponse:
        resp: tve_pb.LoadTVEProfileResponse = await self._get_tve_stub().LoadProfile(
            tve_pb.LoadTVEProfileRequest(id=id)
        )
        return resp

    async def SaveTVEProfile(
        self, profile: tve_base_pb.TVEProfile, set_active: bool = False
    ) -> tve_pb.SaveTVEProfileResponse:
        resp: tve_pb.SaveTVEProfileResponse = await self._get_tve_stub().SaveProfile(
            tve_pb.SaveTVEProfileRequest(profile=profile, set_active=set_active)
        )
        return resp

    async def SetActiveTVEProfile(self, id: str) -> tve_pb.SetActiveTVEProfileResponse:
        resp: tve_pb.SetActiveTVEProfileResponse = await self._get_tve_stub().SetActive(
            tve_pb.SetActiveTVEProfileRequest(id=id)
        )
        return resp

    async def DeleteTVEProfile(self, id: str, new_active_id: Optional[str] = None) -> tve_pb.DeleteTVEProfileResponse:
        resp: tve_pb.DeleteTVEProfileResponse = await self._get_tve_stub().DeleteProfile(
            tve_pb.DeleteTVEProfileRequest(id=id, new_active_id=new_active_id)
        )

        return resp

    async def Heartbeat(self, id: int, serial: str, pc_ip: str, ipmi_ip: str, mcb_ip: str) -> None:
        stub = self._get_module_orchestrator_stub()
        req = module_orchestrator_pb.HeartbeatRequest(
            identity=module_types_pb.ModuleIdentity(id=id, serial=serial),
            module_ips=module_types_pb.ModuleIPs(pc_ip=pc_ip, ipmi_ip=ipmi_ip, mcb_ip=mcb_ip),
        )
        await stub.Heartbeat(req, timeout=0.5)

    async def GetNextModulesList(self, ts: int) -> module_assignment_pb.GetNextModulesListResponse:
        stub = self._get_module_assignment_stub()
        req = module_assignment_pb.GetNextModulesListRequest(ts=util_pb.Timestamp(timestamp_ms=ts))
        resp: module_assignment_pb.GetNextModulesListResponse = await stub.GetNextModulesList(req)
        return resp

    async def GetNextActiveModules(self, ts: int) -> module_assignment_pb.GetNextActiveModulesResponse:
        stub = self._get_module_assignment_stub()
        req = module_assignment_pb.GetNextActiveModulesRequest(ts=util_pb.Timestamp(timestamp_ms=ts))
        resp: module_assignment_pb.GetNextActiveModulesResponse = await stub.GetNextActiveModules(req)
        return resp

    async def IdentifyModule(self, serial: str) -> None:
        stub = self._get_module_assignment_stub()
        req = module_assignment_pb.IdentifyModuleRequest(
            module_identity=module_assignment_pb.ModuleIdentity(serial=serial)
        )
        await stub.IdentifyModule(req)

    async def AssignModule(self, id: int, serial: str) -> None:
        stub = self._get_module_assignment_stub()
        req = module_assignment_pb.AssignModuleRequest(
            module_identity=module_assignment_pb.ModuleIdentity(id=id, serial=serial)
        )
        await stub.AssignModule(req)

    async def ClearModuleAssignment(self, serial: str) -> None:
        stub = self._get_module_assignment_stub()
        req = module_assignment_pb.ClearModuleAssignmentRequest(
            module_identity=module_assignment_pb.ModuleIdentity(id=0, serial=serial)
        )
        await stub.ClearModuleAssignment(req)

    async def SetModuleSerial(self, placeholder_serial: str, new_serial: str) -> None:
        stub = self._get_module_assignment_stub()
        req = module_assignment_pb.SetModuleSerialRequest(
            module_identity=module_assignment_pb.ModuleIdentity(id=0, serial=placeholder_serial), new_serial=new_serial,
        )
        await stub.SetModuleSerial(req)

    async def GetPresetsList(self) -> module_assignment_pb.GetPresetsListResponse:
        stub = self._get_module_assignment_stub()
        req = util_pb.Empty()
        resp: module_assignment_pb.GetPresetsListResponse = await stub.GetPresetsList(req)
        return resp

    async def GetCurrentRobotDefinition(self) -> module_assignment_pb.GetCurrentRobotDefinitionResponse:
        stub = self._get_module_assignment_stub()
        req = util_pb.Empty()
        resp: module_assignment_pb.GetCurrentRobotDefinitionResponse = await stub.GetCurrentRobotDefinition(req)
        return resp

    async def SetCurrentRobotDefinition(self, robot_definition: module_assignment_pb.RobotDefinition) -> None:
        stub = self._get_module_assignment_stub()
        req = module_assignment_pb.SetCurrentRobotDefinitionRequest(current_definition=robot_definition)
        await stub.SetCurrentRobotDefinition(req)

    async def GetNextReaperHardwareStatus(
        self, ts: int, module_id: Optional[int] = None
    ) -> power_pb.GetNextReaperHardwareStatusResponse:
        stub = self._get_power_stub()
        req: power_pb.GetNextReaperHardwareStatusRequest
        if not module_id:
            req = power_pb.GetNextReaperHardwareStatusRequest(
                ts=util_pb.Timestamp(timestamp_ms=ts), center_enclosure_status=power_pb.CenterEnclosureStatusRequest()
            )
        else:
            req = power_pb.GetNextReaperHardwareStatusRequest(
                ts=util_pb.Timestamp(timestamp_ms=ts),
                module_status=power_pb.ModuleHardwareStatusRequest(module_id=module_id),
            )
        resp: power_pb.GetNextReaperHardwareStatusResponse = await stub.GetNextReaperHardwareStatus(req)
        return resp

    async def GetNextReaperAllHardwareStatus(self, ts: int) -> power_pb.GetNextReaperAllHardwareStatusResponse:
        stub = self._get_power_stub()
        req = power_pb.GetNextReaperAllHardwareStatusRequest(ts=util_pb.Timestamp(timestamp_ms=ts))
        resp: power_pb.GetNextReaperAllHardwareStatusResponse = await stub.GetNextReaperAllHardwareStatus(req)
        return resp

    async def GetNextTractorIfState(self, ts: int = 0) -> tractor_pb2.GetNextTractorIfStateResponse:
        stub = self._get_tractor_stub()
        req = util_pb.Timestamp(timestamp_ms=ts)
        resp: tractor_pb2.GetNextTractorIfStateResponse = await stub.GetNextTractorIfState(req)
        return resp

    async def GetNextTractorSafetyState(self, ts: int = 0) -> tractor_pb2.GetNextTractorSafetyStateResponse:
        stub = self._get_tractor_stub()
        req = util_pb.Timestamp(timestamp_ms=ts)
        resp: tractor_pb2.GetNextTractorSafetyStateResponse = await stub.GetNextTractorSafetyState(req)
        return resp

    async def SetEnforcementPolicy(self, enforced: bool) -> None:
        stub = self._get_tractor_stub()
        req = tractor_pb2.SetEnforcementPolicyRequest(enforced=enforced)
        await stub.SetEnforcementPolicy(req)

    async def GetNextCruiseControlState(self, ts: int = 0) -> cc_pb2.GetNextCruiseStateResponse:
        stub = self._get_cc_stub()
        req = util_pb.Timestamp(timestamp_ms=ts)
        resp: cc_pb2.GetNextCruiseStateResponse = await stub.GetNextCruiseState(req)
        return resp
