#include "cv/runtime/cpp/image_service_client/image_service_client.hpp"
#include <fmt/format.h>

namespace cv::runtime::image_service_client {
using namespace carbon::frontend::image_stream;

constexpr int64_t kDefaultDeadlineMs = 1000;

ImageServiceClient::ImageServiceClient(const std::string &address) : addr_{address} {}

std::shared_ptr<ImageStreamService::Stub> ImageServiceClient::get_grpc_stub() {
  if (this->channel_ == nullptr) {
    grpc::ChannelArguments args;
    args.SetMaxReceiveMessageSize(32 * 1024 * 1024);
    this->channel_ = grpc::CreateCustomChannel(this->addr_, grpc::InsecureChannelCredentials(), args);
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<ImageStreamService::Stub>(this->channel_);
  }
  return this->stub_;
}

void ImageServiceClient::reset_stub() {
  this->stub_ = nullptr;
  this->channel_ = nullptr;
}

grpc::Status ImageServiceClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) {
    this->reset_stub();
    throw ex;
  }
}

std::shared_ptr<Image> ImageServiceClient::get_next_camera_image(std::string cam_id, int64_t timestamp_ms,
                                                                 bool annotated, bool include_annotations_metadata,
                                                                 bool dont_downsample, bool encode_as_png) {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + std::chrono::milliseconds(kDefaultDeadlineMs));
  CameraImageRequest request;
  std::shared_ptr<Image> response = std::make_shared<Image>();

  auto stub = get_grpc_stub();
  request.set_cam_id(cam_id);
  request.mutable_ts()->set_timestamp_ms(timestamp_ms);
  request.set_annotated(annotated);
  request.set_include_annotations_metadata(include_annotations_metadata);
  request.set_dont_downsample(dont_downsample);
  request.set_encode_as_png(encode_as_png);

  grpc::Status status = this->exec_grpc(
      std::bind(&ImageStreamService::Stub::GetNextCameraImage, stub, &context, request, response.get()));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw std::runtime_error(fmt::format("Failed Get Next Camera Image for {}: {}", cam_id, status.error_message()));
  }

  return response;
}

std::shared_ptr<GetPredictImageByTimestampResponse>
ImageServiceClient::get_predict_image_by_timestamp(std::string cam_id, int64_t timestamp_ms, int crop_around_x,
                                                   int crop_around_y) {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + std::chrono::milliseconds(kDefaultDeadlineMs));
  GetPredictImageByTimestampRequest request;
  std::shared_ptr<GetPredictImageByTimestampResponse> response = std::make_shared<GetPredictImageByTimestampResponse>();

  auto stub = get_grpc_stub();
  request.set_cam_id(cam_id);
  request.mutable_ts()->set_timestamp_ms(timestamp_ms);
  request.set_crop_around_x(crop_around_x);
  request.set_crop_around_y(crop_around_y);

  grpc::Status status = this->exec_grpc(
      std::bind(&ImageStreamService::Stub::GetPredictImageByTimestamp, stub, &context, request, response.get()));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw std::runtime_error(
        fmt::format("Failed GetPredictImageByTimestamp for {}, {}: {}", cam_id, timestamp_ms, status.error_message()));
  }

  return response;
}

std::vector<std::shared_ptr<carbon::frontend::image_stream::CentroidPerspective>>
ImageServiceClient::get_multi_predict_perspectives(std::string cam_id, int requested_perspectves,
                                                   std::vector<std::tuple<int64_t, int, int>> possible_perspectives) {
  grpc::ClientContext context;
  GetMultiPredictPerspectivesRequest request;
  std::shared_ptr<GetMultiPredictPerspectivesResponse> response =
      std::make_shared<GetMultiPredictPerspectivesResponse>();

  auto stub = get_grpc_stub();
  context.set_deadline(std::chrono::system_clock::now() + std::chrono::milliseconds(kDefaultDeadlineMs));
  request.set_cam_id(cam_id);
  request.set_requested_perspectives(requested_perspectves);
  for (auto &perspective : possible_perspectives) {
    auto *p = request.add_perspectives();
    p->mutable_ts()->set_timestamp_ms(std::get<0>(perspective));
    p->set_crop_around_x(std::get<1>(perspective));
    p->set_crop_around_y(std::get<2>(perspective));
  }

  grpc::Status status = this->exec_grpc(
      std::bind(&ImageStreamService::Stub::GetMultiPredictPerspectives, stub, &context, request, response.get()));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw std::runtime_error(fmt::format("Failed GetMultiPredictPerspectives for {}, {}: {}", cam_id,
                                         requested_perspectves, status.error_message()));
  }

  std::vector<std::shared_ptr<CentroidPerspective>> res;
  auto res_perspectives = response->perspectives();
  for (auto &perspective : res_perspectives) {
    res.push_back(std::make_shared<CentroidPerspective>(perspective));
  }

  return res;
}

} // namespace cv::runtime::image_service_client