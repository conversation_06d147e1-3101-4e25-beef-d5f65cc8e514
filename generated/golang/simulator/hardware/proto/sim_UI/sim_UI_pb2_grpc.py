# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.golang.simulator.hardware.proto.sim_UI import sim_UI_pb2 as golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2


class SimulatorUIServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetVelocityStream = channel.unary_stream(
                '/carbon.simulator_UI.SimulatorUIService/GetVelocityStream',
                request_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetVelocityRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetVelocityResponse.FromString,
                )
        self.SetVelocity = channel.unary_unary(
                '/carbon.simulator_UI.SimulatorUIService/SetVelocity',
                request_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetVelocityRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetVelocityResponse.FromString,
                )
        self.GetPresetStream = channel.unary_stream(
                '/carbon.simulator_UI.SimulatorUIService/GetPresetStream',
                request_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetPresetsRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetPresetsResponse.FromString,
                )
        self.SetPreset = channel.unary_unary(
                '/carbon.simulator_UI.SimulatorUIService/SetPreset',
                request_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetPresetRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetPresetResponse.FromString,
                )
        self.GetDiagnosticSettings = channel.unary_unary(
                '/carbon.simulator_UI.SimulatorUIService/GetDiagnosticSettings',
                request_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetDiagnosticSettingsRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetDiagnosticSettingsResponse.FromString,
                )
        self.SetDiagnosticSettings = channel.unary_unary(
                '/carbon.simulator_UI.SimulatorUIService/SetDiagnosticSettings',
                request_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetDiagnosticSettingsRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetDiagnosticSettingsResponse.FromString,
                )


class SimulatorUIServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetVelocityStream(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetVelocity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPresetStream(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetPreset(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDiagnosticSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetDiagnosticSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SimulatorUIServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetVelocityStream': grpc.unary_stream_rpc_method_handler(
                    servicer.GetVelocityStream,
                    request_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetVelocityRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetVelocityResponse.SerializeToString,
            ),
            'SetVelocity': grpc.unary_unary_rpc_method_handler(
                    servicer.SetVelocity,
                    request_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetVelocityRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetVelocityResponse.SerializeToString,
            ),
            'GetPresetStream': grpc.unary_stream_rpc_method_handler(
                    servicer.GetPresetStream,
                    request_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetPresetsRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetPresetsResponse.SerializeToString,
            ),
            'SetPreset': grpc.unary_unary_rpc_method_handler(
                    servicer.SetPreset,
                    request_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetPresetRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetPresetResponse.SerializeToString,
            ),
            'GetDiagnosticSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDiagnosticSettings,
                    request_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetDiagnosticSettingsRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetDiagnosticSettingsResponse.SerializeToString,
            ),
            'SetDiagnosticSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.SetDiagnosticSettings,
                    request_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetDiagnosticSettingsRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetDiagnosticSettingsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.simulator_UI.SimulatorUIService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class SimulatorUIService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetVelocityStream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/carbon.simulator_UI.SimulatorUIService/GetVelocityStream',
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetVelocityRequest.SerializeToString,
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetVelocityResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetVelocity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.simulator_UI.SimulatorUIService/SetVelocity',
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetVelocityRequest.SerializeToString,
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetVelocityResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPresetStream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/carbon.simulator_UI.SimulatorUIService/GetPresetStream',
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetPresetsRequest.SerializeToString,
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetPresetsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetPreset(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.simulator_UI.SimulatorUIService/SetPreset',
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetPresetRequest.SerializeToString,
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetPresetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDiagnosticSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.simulator_UI.SimulatorUIService/GetDiagnosticSettings',
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetDiagnosticSettingsRequest.SerializeToString,
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.GetDiagnosticSettingsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetDiagnosticSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.simulator_UI.SimulatorUIService/SetDiagnosticSettings',
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetDiagnosticSettingsRequest.SerializeToString,
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim__UI_dot_sim__UI__pb2.SetDiagnosticSettingsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
