import { useState, useEffect } from 'react';
import { <PERSON>ton, CircularProgress, Text<PERSON>ield, Typography } from '@mui/material';
import { GetDiagnosticSettingsAPI, SetDiagnosticSettingsAPI } from '../api/client';

const ReplayDiagnostic = () => {
    const [loading, setLoading] = useState(true);
    const [diagnostics, setDiagnostics] = useState<string[]>([]);
    const [currentDiagnostic, setCurrentDiagnostic] = useState('');
    const [row, setRow] = useState(0);
    const [ticket, setTicket] = useState(0);

    const saveDiagnosticSettings = async () => {
        await SetDiagnosticSettingsAPI({
            currentDiagnostic: currentDiagnostic,
            row: BigInt(row),
        });
    };

    const renderReplayDiagnostic = () => {
        return (
            <div className="mx-4 border-solid border-blue border rounded-xl">
                <h2 className="mx-12 mt-4 text-3xl">Replay Diagnostics</h2>
                <div className="text-left m-9 flex flex-col gap-4">
                    <div>
                        <select
                            value={currentDiagnostic}
                            onChange={(e) => setCurrentDiagnostic(e.target.value)}
                            className="bg-transparent text-blue"
                        >
                            {currentDiagnostic ? (
                                <></>
                            ) : (
                                <option key=" " value="">
                                    {' '}
                                </option>
                            )}
                            {diagnostics.map((diagnostic) => (
                                <option key={diagnostic} value={diagnostic}>
                                    {diagnostic}
                                </option>
                            ))}
                            <option key="Custom" value="custom">
                                Custom
                            </option>
                        </select>
                    </div>
                    <div className="flex flex-row justify-between items-center">
                        <Typography className="text-blue">Row:</Typography>
                        <TextField
                            value={row}
                            onChange={(e) => setRow(Number(e.target.value))}
                            className="bg-transparent text-blue"
                            type="number"
                            inputProps={{ min: 0 }}
                        />
                    </div>
                    <div className="flex flex-row justify-between items-center">
                        <Button variant="contained" color="primary" onClick={() => saveDiagnosticSettings()}>
                            Save Settings
                        </Button>
                    </div>
                </div>
            </div>
        );
    };

    useEffect(() => {
        let isMounted = true;
        const getDiagnostics = async () => {
            while (isMounted) {
                const diagnostics = await GetDiagnosticSettingsAPI(BigInt(ticket));

                setDiagnostics(diagnostics.diagnostics);
                setCurrentDiagnostic(diagnostics.currentDiagnostic);
                setRow(Number(diagnostics.row));
                setTicket(Number(diagnostics.ticket));

                setLoading(false);
            }
        };
        getDiagnostics();

        return () => {
            isMounted = false;
        };
    }, [ticket]);

    return <div>{loading ? <CircularProgress /> : renderReplayDiagnostic()}</div>;
};

export default ReplayDiagnostic;
