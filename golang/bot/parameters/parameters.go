package parameters

import (
	"encoding/json"
	"fmt"
)

type BotBuildOpts struct {
	Container             string
	Release               string
	Fast                  bool
	All                   bool
	ID                    string
	Version               string
	BuildArgs             map[string]string
	OptionalTag           []string
	OptionalCacheFrom     []string
	Multiarch             bool
	Platform              string
	Push                  bool
	CiTag                 bool
	CiRevision            string
	NoBuildCmd            bool
	SourceTag             string
	ContainerBuildRetries int
	SkipSemverCheck       bool
}

func (bbo BotBuildOpts) String() string {
	byt, err := json.Marshal(bbo)
	if err != nil {
		return fmt.Sprintf("failed to parse BotBuildOpts: %s", err)
	}
	return string(byt)
}
