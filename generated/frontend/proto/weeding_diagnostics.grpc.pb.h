// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/weeding_diagnostics.proto
#ifndef GRPC_frontend_2fproto_2fweeding_5fdiagnostics_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fweeding_5fdiagnostics_2eproto__INCLUDED

#include "frontend/proto/weeding_diagnostics.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace weeding_diagnostics {

class WeedingDiagnosticsService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncRecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncRecordWeedingDiagnosticsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncRecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncRecordWeedingDiagnosticsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::weed_tracking::DiagnosticsSnapshot* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>> AsyncGetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>>(AsyncGetCurrentTrajectoriesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>> PrepareAsyncGetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>>(PrepareAsyncGetCurrentTrajectoriesRaw(context, request, cq));
    }
    virtual ::grpc::Status GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>> AsyncGetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>>(AsyncGetRecordingsListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>> PrepareAsyncGetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>>(PrepareAsyncGetRecordingsListRaw(context, request, cq));
    }
    virtual ::grpc::Status OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>> AsyncOpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>>(AsyncOpenRecordingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>> PrepareAsyncOpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>>(PrepareAsyncOpenRecordingRaw(context, request, cq));
    }
    virtual ::grpc::Status GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>> AsyncGetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>>(AsyncGetSnapshotRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>> PrepareAsyncGetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>>(PrepareAsyncGetSnapshotRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDeleteRecordingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteRecordingRaw(context, request, cq));
    }
    virtual ::grpc::Status GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::TrajectoryData>> AsyncGetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::TrajectoryData>>(AsyncGetTrajectoryDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::TrajectoryData>> PrepareAsyncGetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::TrajectoryData>>(PrepareAsyncGetTrajectoryDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> GetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(GetTrajectoryPredictImageRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> AsyncGetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(AsyncGetTrajectoryPredictImageRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> PrepareAsyncGetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(PrepareAsyncGetTrajectoryPredictImageRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> GetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(GetTrajectoryTargetImageRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> AsyncGetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(AsyncGetTrajectoryTargetImageRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> PrepareAsyncGetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(PrepareAsyncGetTrajectoryTargetImageRaw(context, request, cq));
    }
    virtual ::grpc::Status GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>> AsyncGetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>>(AsyncGetPredictImageMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>> PrepareAsyncGetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>>(PrepareAsyncGetPredictImageMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> GetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(GetPredictImageRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> AsyncGetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(AsyncGetPredictImageRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>> PrepareAsyncGetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(PrepareAsyncGetPredictImageRaw(context, request, cq));
    }
    virtual ::grpc::Status StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncStartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncStartUploadRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncStartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncStartUploadRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>> AsyncGetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>>(AsyncGetNextUploadStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>> PrepareAsyncGetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>>(PrepareAsyncGetNextUploadStateRaw(context, request, cq));
    }
    virtual ::grpc::Status GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>> AsyncGetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>>(AsyncGetDeepweedPredictionsCountRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>> PrepareAsyncGetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>>(PrepareAsyncGetDeepweedPredictionsCountRaw(context, request, cq));
    }
    virtual ::grpc::Status GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>> AsyncGetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>>(AsyncGetDeepweedPredictionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>> PrepareAsyncGetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>>(PrepareAsyncGetDeepweedPredictionsRaw(context, request, cq));
    }
    virtual ::grpc::Status FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>> AsyncFindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>>(AsyncFindTrajectoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>> PrepareAsyncFindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>>(PrepareAsyncFindTrajectoryRaw(context, request, cq));
    }
    virtual ::grpc::Status GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>> AsyncGetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>>(AsyncGetRotaryTicksRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>> PrepareAsyncGetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>>(PrepareAsyncGetRotaryTicksRaw(context, request, cq));
    }
    virtual ::grpc::Status SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>> AsyncSnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>>(AsyncSnapshotPredictImagesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>> PrepareAsyncSnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>>(PrepareAsyncSnapshotPredictImagesRaw(context, request, cq));
    }
    virtual ::grpc::Status GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>> AsyncGetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>>(AsyncGetChipForPredictImageRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>> PrepareAsyncGetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>>(PrepareAsyncGetChipForPredictImageRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) = 0;
      virtual void GetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) = 0;
      virtual void GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) = 0;
      virtual void StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncRecordWeedingDiagnosticsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncRecordWeedingDiagnosticsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>* AsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>* PrepareAsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* AsyncGetRecordingsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* PrepareAsyncGetRecordingsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* AsyncOpenRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* PrepareAsyncOpenRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* AsyncGetSnapshotRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* PrepareAsyncGetSnapshotRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDeleteRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::TrajectoryData>* AsyncGetTrajectoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::TrajectoryData>* PrepareAsyncGetTrajectoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* AsyncGetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* PrepareAsyncGetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* AsyncGetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* PrepareAsyncGetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* AsyncGetPredictImageMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* PrepareAsyncGetPredictImageMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* AsyncGetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::frontend::weeding_diagnostics::ImageChunk>* PrepareAsyncGetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncStartUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncStartUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* AsyncGetNextUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* PrepareAsyncGetNextUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* AsyncGetDeepweedPredictionsCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* PrepareAsyncGetDeepweedPredictionsCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* AsyncGetDeepweedPredictionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* PrepareAsyncGetDeepweedPredictionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* AsyncFindTrajectoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* PrepareAsyncFindTrajectoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* AsyncGetRotaryTicksRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* PrepareAsyncGetRotaryTicksRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* AsyncSnapshotPredictImagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* PrepareAsyncSnapshotPredictImagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* AsyncGetChipForPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* PrepareAsyncGetChipForPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncRecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncRecordWeedingDiagnosticsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncRecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncRecordWeedingDiagnosticsRaw(context, request, cq));
    }
    ::grpc::Status GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::weed_tracking::DiagnosticsSnapshot* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>> AsyncGetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>>(AsyncGetCurrentTrajectoriesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>> PrepareAsyncGetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>>(PrepareAsyncGetCurrentTrajectoriesRaw(context, request, cq));
    }
    ::grpc::Status GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>> AsyncGetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>>(AsyncGetRecordingsListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>> PrepareAsyncGetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>>(PrepareAsyncGetRecordingsListRaw(context, request, cq));
    }
    ::grpc::Status OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>> AsyncOpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>>(AsyncOpenRecordingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>> PrepareAsyncOpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>>(PrepareAsyncOpenRecordingRaw(context, request, cq));
    }
    ::grpc::Status GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>> AsyncGetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>>(AsyncGetSnapshotRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>> PrepareAsyncGetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>>(PrepareAsyncGetSnapshotRaw(context, request, cq));
    }
    ::grpc::Status DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDeleteRecordingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteRecordingRaw(context, request, cq));
    }
    ::grpc::Status GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::TrajectoryData>> AsyncGetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::TrajectoryData>>(AsyncGetTrajectoryDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::TrajectoryData>> PrepareAsyncGetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::TrajectoryData>>(PrepareAsyncGetTrajectoryDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> GetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(GetTrajectoryPredictImageRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> AsyncGetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(AsyncGetTrajectoryPredictImageRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> PrepareAsyncGetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(PrepareAsyncGetTrajectoryPredictImageRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> GetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(GetTrajectoryTargetImageRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> AsyncGetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(AsyncGetTrajectoryTargetImageRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> PrepareAsyncGetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(PrepareAsyncGetTrajectoryTargetImageRaw(context, request, cq));
    }
    ::grpc::Status GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>> AsyncGetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>>(AsyncGetPredictImageMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>> PrepareAsyncGetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>>(PrepareAsyncGetPredictImageMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> GetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(GetPredictImageRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> AsyncGetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(AsyncGetPredictImageRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>> PrepareAsyncGetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>>(PrepareAsyncGetPredictImageRaw(context, request, cq));
    }
    ::grpc::Status StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncStartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncStartUploadRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncStartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncStartUploadRaw(context, request, cq));
    }
    ::grpc::Status GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>> AsyncGetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>>(AsyncGetNextUploadStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>> PrepareAsyncGetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>>(PrepareAsyncGetNextUploadStateRaw(context, request, cq));
    }
    ::grpc::Status GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>> AsyncGetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>>(AsyncGetDeepweedPredictionsCountRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>> PrepareAsyncGetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>>(PrepareAsyncGetDeepweedPredictionsCountRaw(context, request, cq));
    }
    ::grpc::Status GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>> AsyncGetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>>(AsyncGetDeepweedPredictionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>> PrepareAsyncGetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>>(PrepareAsyncGetDeepweedPredictionsRaw(context, request, cq));
    }
    ::grpc::Status FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>> AsyncFindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>>(AsyncFindTrajectoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>> PrepareAsyncFindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>>(PrepareAsyncFindTrajectoryRaw(context, request, cq));
    }
    ::grpc::Status GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>> AsyncGetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>>(AsyncGetRotaryTicksRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>> PrepareAsyncGetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>>(PrepareAsyncGetRotaryTicksRaw(context, request, cq));
    }
    ::grpc::Status SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>> AsyncSnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>>(AsyncSnapshotPredictImagesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>> PrepareAsyncSnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>>(PrepareAsyncSnapshotPredictImagesRaw(context, request, cq));
    }
    ::grpc::Status GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>> AsyncGetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>>(AsyncGetChipForPredictImageRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>> PrepareAsyncGetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>>(PrepareAsyncGetChipForPredictImageRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void RecordWeedingDiagnostics(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response, std::function<void(::grpc::Status)>) override;
      void GetCurrentTrajectories(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response, std::function<void(::grpc::Status)>) override;
      void GetRecordingsList(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response, std::function<void(::grpc::Status)>) override;
      void OpenRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response, std::function<void(::grpc::Status)>) override;
      void GetSnapshot(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DeleteRecording(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response, std::function<void(::grpc::Status)>) override;
      void GetTrajectoryData(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetTrajectoryPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) override;
      void GetTrajectoryTargetImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) override;
      void GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetPredictImageMetadata(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* request, ::grpc::ClientReadReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* reactor) override;
      void StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartUpload(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextUploadState(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response, std::function<void(::grpc::Status)>) override;
      void GetDeepweedPredictionsCount(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetDeepweedPredictions(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response, std::function<void(::grpc::Status)>) override;
      void FindTrajectory(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response, std::function<void(::grpc::Status)>) override;
      void GetRotaryTicks(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response, std::function<void(::grpc::Status)>) override;
      void SnapshotPredictImages(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response, std::function<void(::grpc::Status)>) override;
      void GetChipForPredictImage(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncRecordWeedingDiagnosticsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncRecordWeedingDiagnosticsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>* AsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>* PrepareAsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* AsyncGetRecordingsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* PrepareAsyncGetRecordingsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* AsyncOpenRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* PrepareAsyncOpenRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* AsyncGetSnapshotRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* PrepareAsyncGetSnapshotRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDeleteRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteRecordingRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::TrajectoryData>* AsyncGetTrajectoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::TrajectoryData>* PrepareAsyncGetTrajectoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request) override;
    ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* AsyncGetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* PrepareAsyncGetTrajectoryPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request) override;
    ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* AsyncGetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* PrepareAsyncGetTrajectoryTargetImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* AsyncGetPredictImageMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* PrepareAsyncGetPredictImageMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request) override;
    ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* AsyncGetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::carbon::frontend::weeding_diagnostics::ImageChunk>* PrepareAsyncGetPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncStartUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncStartUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* AsyncGetNextUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* PrepareAsyncGetNextUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* AsyncGetDeepweedPredictionsCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* PrepareAsyncGetDeepweedPredictionsCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* AsyncGetDeepweedPredictionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* PrepareAsyncGetDeepweedPredictionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* AsyncFindTrajectoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* PrepareAsyncFindTrajectoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* AsyncGetRotaryTicksRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* PrepareAsyncGetRotaryTicksRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* AsyncSnapshotPredictImagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* PrepareAsyncSnapshotPredictImagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* AsyncGetChipForPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* PrepareAsyncGetChipForPredictImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_RecordWeedingDiagnostics_;
    const ::grpc::internal::RpcMethod rpcmethod_GetCurrentTrajectories_;
    const ::grpc::internal::RpcMethod rpcmethod_GetRecordingsList_;
    const ::grpc::internal::RpcMethod rpcmethod_OpenRecording_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSnapshot_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteRecording_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTrajectoryData_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTrajectoryPredictImage_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTrajectoryTargetImage_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPredictImageMetadata_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPredictImage_;
    const ::grpc::internal::RpcMethod rpcmethod_StartUpload_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextUploadState_;
    const ::grpc::internal::RpcMethod rpcmethod_GetDeepweedPredictionsCount_;
    const ::grpc::internal::RpcMethod rpcmethod_GetDeepweedPredictions_;
    const ::grpc::internal::RpcMethod rpcmethod_FindTrajectory_;
    const ::grpc::internal::RpcMethod rpcmethod_GetRotaryTicks_;
    const ::grpc::internal::RpcMethod rpcmethod_SnapshotPredictImages_;
    const ::grpc::internal::RpcMethod rpcmethod_GetChipForPredictImage_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status RecordWeedingDiagnostics(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response);
    virtual ::grpc::Status GetRecordingsList(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response);
    virtual ::grpc::Status OpenRecording(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response);
    virtual ::grpc::Status GetSnapshot(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response);
    virtual ::grpc::Status DeleteRecording(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetTrajectoryData(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response);
    virtual ::grpc::Status GetTrajectoryPredictImage(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* request, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer);
    virtual ::grpc::Status GetTrajectoryTargetImage(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* request, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer);
    virtual ::grpc::Status GetPredictImageMetadata(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response);
    virtual ::grpc::Status GetPredictImage(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* request, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer);
    virtual ::grpc::Status StartUpload(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextUploadState(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response);
    virtual ::grpc::Status GetDeepweedPredictionsCount(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response);
    virtual ::grpc::Status GetDeepweedPredictions(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response);
    virtual ::grpc::Status FindTrajectory(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response);
    virtual ::grpc::Status GetRotaryTicks(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response);
    virtual ::grpc::Status SnapshotPredictImages(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response);
    virtual ::grpc::Status GetChipForPredictImage(::grpc::ServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_RecordWeedingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RecordWeedingDiagnostics() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_RecordWeedingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RecordWeedingDiagnostics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRecordWeedingDiagnostics(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCurrentTrajectories(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::DiagnosticsSnapshot>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetRecordingsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetRecordingsList() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetRecordingsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRecordingsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRecordingsList(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_OpenRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_OpenRecording() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_OpenRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OpenRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestOpenRecording(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSnapshot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetSnapshot() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetSnapshot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSnapshot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSnapshot(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteRecording() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_DeleteRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteRecording(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTrajectoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTrajectoryData() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetTrajectoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::TrajectoryData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTrajectoryData(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::TrajectoryData>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTrajectoryPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTrajectoryPredictImage() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_GetTrajectoryPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTrajectoryPredictImage(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* request, ::grpc::ServerAsyncWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(7, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTrajectoryTargetImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTrajectoryTargetImage() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_GetTrajectoryTargetImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryTargetImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTrajectoryTargetImage(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* request, ::grpc::ServerAsyncWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(8, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPredictImageMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPredictImageMetadata() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_GetPredictImageMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPredictImageMetadata(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPredictImage() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_GetPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPredictImage(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* request, ::grpc::ServerAsyncWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(10, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartUpload() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_StartUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartUpload(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextUploadState() {
      ::grpc::Service::MarkMethodAsync(12);
    }
    ~WithAsyncMethod_GetNextUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextUploadState(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetDeepweedPredictionsCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetDeepweedPredictionsCount() {
      ::grpc::Service::MarkMethodAsync(13);
    }
    ~WithAsyncMethod_GetDeepweedPredictionsCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictionsCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDeepweedPredictionsCount(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetDeepweedPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetDeepweedPredictions() {
      ::grpc::Service::MarkMethodAsync(14);
    }
    ~WithAsyncMethod_GetDeepweedPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDeepweedPredictions(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_FindTrajectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_FindTrajectory() {
      ::grpc::Service::MarkMethodAsync(15);
    }
    ~WithAsyncMethod_FindTrajectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FindTrajectory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFindTrajectory(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetRotaryTicks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetRotaryTicks() {
      ::grpc::Service::MarkMethodAsync(16);
    }
    ~WithAsyncMethod_GetRotaryTicks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRotaryTicks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRotaryTicks(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(16, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SnapshotPredictImages : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SnapshotPredictImages() {
      ::grpc::Service::MarkMethodAsync(17);
    }
    ~WithAsyncMethod_SnapshotPredictImages() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SnapshotPredictImages(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSnapshotPredictImages(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(17, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetChipForPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetChipForPredictImage() {
      ::grpc::Service::MarkMethodAsync(18);
    }
    ~WithAsyncMethod_GetChipForPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipForPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetChipForPredictImage(::grpc::ServerContext* context, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(18, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_RecordWeedingDiagnostics<WithAsyncMethod_GetCurrentTrajectories<WithAsyncMethod_GetRecordingsList<WithAsyncMethod_OpenRecording<WithAsyncMethod_GetSnapshot<WithAsyncMethod_DeleteRecording<WithAsyncMethod_GetTrajectoryData<WithAsyncMethod_GetTrajectoryPredictImage<WithAsyncMethod_GetTrajectoryTargetImage<WithAsyncMethod_GetPredictImageMetadata<WithAsyncMethod_GetPredictImage<WithAsyncMethod_StartUpload<WithAsyncMethod_GetNextUploadState<WithAsyncMethod_GetDeepweedPredictionsCount<WithAsyncMethod_GetDeepweedPredictions<WithAsyncMethod_FindTrajectory<WithAsyncMethod_GetRotaryTicks<WithAsyncMethod_SnapshotPredictImages<WithAsyncMethod_GetChipForPredictImage<Service > > > > > > > > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_RecordWeedingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_RecordWeedingDiagnostics() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* request, ::carbon::frontend::util::Empty* response) { return this->RecordWeedingDiagnostics(context, request, response); }));}
    void SetMessageAllocatorFor_RecordWeedingDiagnostics(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_RecordWeedingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RecordWeedingDiagnostics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RecordWeedingDiagnostics(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::weed_tracking::DiagnosticsSnapshot>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* request, ::weed_tracking::DiagnosticsSnapshot* response) { return this->GetCurrentTrajectories(context, request, response); }));}
    void SetMessageAllocatorFor_GetCurrentTrajectories(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::weed_tracking::DiagnosticsSnapshot>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::weed_tracking::DiagnosticsSnapshot>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCurrentTrajectories(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetRecordingsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetRecordingsList() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* request, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* response) { return this->GetRecordingsList(context, request, response); }));}
    void SetMessageAllocatorFor_GetRecordingsList(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetRecordingsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRecordingsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRecordingsList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_OpenRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_OpenRecording() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* request, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* response) { return this->OpenRecording(context, request, response); }));}
    void SetMessageAllocatorFor_OpenRecording(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_OpenRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OpenRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* OpenRecording(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetSnapshot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetSnapshot() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* request, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* response) { return this->GetSnapshot(context, request, response); }));}
    void SetMessageAllocatorFor_GetSnapshot(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetSnapshot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSnapshot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSnapshot(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteRecording() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* request, ::carbon::frontend::util::Empty* response) { return this->DeleteRecording(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteRecording(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteRecording(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetTrajectoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetTrajectoryData() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::carbon::frontend::weeding_diagnostics::TrajectoryData>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* request, ::carbon::frontend::weeding_diagnostics::TrajectoryData* response) { return this->GetTrajectoryData(context, request, response); }));}
    void SetMessageAllocatorFor_GetTrajectoryData(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::carbon::frontend::weeding_diagnostics::TrajectoryData>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::carbon::frontend::weeding_diagnostics::TrajectoryData>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetTrajectoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::TrajectoryData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTrajectoryData(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::TrajectoryData* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetTrajectoryPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetTrajectoryPredictImage() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackServerStreamingHandler< ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* request) { return this->GetTrajectoryPredictImage(context, request); }));
    }
    ~WithCallbackMethod_GetTrajectoryPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetTrajectoryPredictImage(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetTrajectoryTargetImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetTrajectoryTargetImage() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackServerStreamingHandler< ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* request) { return this->GetTrajectoryTargetImage(context, request); }));
    }
    ~WithCallbackMethod_GetTrajectoryTargetImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryTargetImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetTrajectoryTargetImage(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPredictImageMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPredictImageMetadata() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* request, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* response) { return this->GetPredictImageMetadata(context, request, response); }));}
    void SetMessageAllocatorFor_GetPredictImageMetadata(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetPredictImageMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPredictImageMetadata(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPredictImage() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackServerStreamingHandler< ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* request) { return this->GetPredictImage(context, request); }));
    }
    ~WithCallbackMethod_GetPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::carbon::frontend::weeding_diagnostics::ImageChunk>* GetPredictImage(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartUpload() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* request, ::carbon::frontend::util::Empty* response) { return this->StartUpload(context, request, response); }));}
    void SetMessageAllocatorFor_StartUpload(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartUpload(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextUploadState() {
      ::grpc::Service::MarkMethodCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* request, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* response) { return this->GetNextUploadState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextUploadState(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(12);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextUploadState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetDeepweedPredictionsCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetDeepweedPredictionsCount() {
      ::grpc::Service::MarkMethodCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* response) { return this->GetDeepweedPredictionsCount(context, request, response); }));}
    void SetMessageAllocatorFor_GetDeepweedPredictionsCount(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(13);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetDeepweedPredictionsCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictionsCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDeepweedPredictionsCount(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetDeepweedPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetDeepweedPredictions() {
      ::grpc::Service::MarkMethodCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* request, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* response) { return this->GetDeepweedPredictions(context, request, response); }));}
    void SetMessageAllocatorFor_GetDeepweedPredictions(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(14);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetDeepweedPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDeepweedPredictions(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_FindTrajectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_FindTrajectory() {
      ::grpc::Service::MarkMethodCallback(15,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* request, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* response) { return this->FindTrajectory(context, request, response); }));}
    void SetMessageAllocatorFor_FindTrajectory(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(15);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_FindTrajectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FindTrajectory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FindTrajectory(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetRotaryTicks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetRotaryTicks() {
      ::grpc::Service::MarkMethodCallback(16,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* request, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* response) { return this->GetRotaryTicks(context, request, response); }));}
    void SetMessageAllocatorFor_GetRotaryTicks(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(16);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetRotaryTicks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRotaryTicks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRotaryTicks(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SnapshotPredictImages : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SnapshotPredictImages() {
      ::grpc::Service::MarkMethodCallback(17,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* request, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* response) { return this->SnapshotPredictImages(context, request, response); }));}
    void SetMessageAllocatorFor_SnapshotPredictImages(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(17);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SnapshotPredictImages() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SnapshotPredictImages(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SnapshotPredictImages(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetChipForPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetChipForPredictImage() {
      ::grpc::Service::MarkMethodCallback(18,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* request, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* response) { return this->GetChipForPredictImage(context, request, response); }));}
    void SetMessageAllocatorFor_GetChipForPredictImage(
        ::grpc::MessageAllocator< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(18);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetChipForPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipForPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetChipForPredictImage(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_RecordWeedingDiagnostics<WithCallbackMethod_GetCurrentTrajectories<WithCallbackMethod_GetRecordingsList<WithCallbackMethod_OpenRecording<WithCallbackMethod_GetSnapshot<WithCallbackMethod_DeleteRecording<WithCallbackMethod_GetTrajectoryData<WithCallbackMethod_GetTrajectoryPredictImage<WithCallbackMethod_GetTrajectoryTargetImage<WithCallbackMethod_GetPredictImageMetadata<WithCallbackMethod_GetPredictImage<WithCallbackMethod_StartUpload<WithCallbackMethod_GetNextUploadState<WithCallbackMethod_GetDeepweedPredictionsCount<WithCallbackMethod_GetDeepweedPredictions<WithCallbackMethod_FindTrajectory<WithCallbackMethod_GetRotaryTicks<WithCallbackMethod_SnapshotPredictImages<WithCallbackMethod_GetChipForPredictImage<Service > > > > > > > > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_RecordWeedingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RecordWeedingDiagnostics() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_RecordWeedingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RecordWeedingDiagnostics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetRecordingsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetRecordingsList() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetRecordingsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRecordingsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_OpenRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_OpenRecording() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_OpenRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OpenRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSnapshot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetSnapshot() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetSnapshot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSnapshot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteRecording() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_DeleteRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTrajectoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTrajectoryData() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetTrajectoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::TrajectoryData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTrajectoryPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTrajectoryPredictImage() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_GetTrajectoryPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTrajectoryTargetImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTrajectoryTargetImage() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_GetTrajectoryTargetImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryTargetImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPredictImageMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPredictImageMetadata() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_GetPredictImageMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPredictImage() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_GetPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartUpload() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_StartUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextUploadState() {
      ::grpc::Service::MarkMethodGeneric(12);
    }
    ~WithGenericMethod_GetNextUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetDeepweedPredictionsCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetDeepweedPredictionsCount() {
      ::grpc::Service::MarkMethodGeneric(13);
    }
    ~WithGenericMethod_GetDeepweedPredictionsCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictionsCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetDeepweedPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetDeepweedPredictions() {
      ::grpc::Service::MarkMethodGeneric(14);
    }
    ~WithGenericMethod_GetDeepweedPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_FindTrajectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_FindTrajectory() {
      ::grpc::Service::MarkMethodGeneric(15);
    }
    ~WithGenericMethod_FindTrajectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FindTrajectory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetRotaryTicks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetRotaryTicks() {
      ::grpc::Service::MarkMethodGeneric(16);
    }
    ~WithGenericMethod_GetRotaryTicks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRotaryTicks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SnapshotPredictImages : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SnapshotPredictImages() {
      ::grpc::Service::MarkMethodGeneric(17);
    }
    ~WithGenericMethod_SnapshotPredictImages() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SnapshotPredictImages(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetChipForPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetChipForPredictImage() {
      ::grpc::Service::MarkMethodGeneric(18);
    }
    ~WithGenericMethod_GetChipForPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipForPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_RecordWeedingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RecordWeedingDiagnostics() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_RecordWeedingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RecordWeedingDiagnostics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRecordWeedingDiagnostics(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCurrentTrajectories(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetRecordingsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetRecordingsList() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetRecordingsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRecordingsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRecordingsList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_OpenRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_OpenRecording() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_OpenRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OpenRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestOpenRecording(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSnapshot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetSnapshot() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetSnapshot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSnapshot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSnapshot(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteRecording() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_DeleteRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteRecording(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTrajectoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTrajectoryData() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetTrajectoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::TrajectoryData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTrajectoryData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTrajectoryPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTrajectoryPredictImage() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_GetTrajectoryPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTrajectoryPredictImage(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(7, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTrajectoryTargetImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTrajectoryTargetImage() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_GetTrajectoryTargetImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryTargetImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTrajectoryTargetImage(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(8, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPredictImageMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPredictImageMetadata() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_GetPredictImageMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPredictImageMetadata(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPredictImage() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_GetPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPredictImage(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(10, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartUpload() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_StartUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartUpload(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextUploadState() {
      ::grpc::Service::MarkMethodRaw(12);
    }
    ~WithRawMethod_GetNextUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextUploadState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetDeepweedPredictionsCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetDeepweedPredictionsCount() {
      ::grpc::Service::MarkMethodRaw(13);
    }
    ~WithRawMethod_GetDeepweedPredictionsCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictionsCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDeepweedPredictionsCount(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetDeepweedPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetDeepweedPredictions() {
      ::grpc::Service::MarkMethodRaw(14);
    }
    ~WithRawMethod_GetDeepweedPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDeepweedPredictions(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_FindTrajectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_FindTrajectory() {
      ::grpc::Service::MarkMethodRaw(15);
    }
    ~WithRawMethod_FindTrajectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FindTrajectory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFindTrajectory(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetRotaryTicks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetRotaryTicks() {
      ::grpc::Service::MarkMethodRaw(16);
    }
    ~WithRawMethod_GetRotaryTicks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRotaryTicks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRotaryTicks(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(16, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SnapshotPredictImages : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SnapshotPredictImages() {
      ::grpc::Service::MarkMethodRaw(17);
    }
    ~WithRawMethod_SnapshotPredictImages() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SnapshotPredictImages(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSnapshotPredictImages(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(17, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetChipForPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetChipForPredictImage() {
      ::grpc::Service::MarkMethodRaw(18);
    }
    ~WithRawMethod_GetChipForPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipForPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetChipForPredictImage(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(18, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_RecordWeedingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_RecordWeedingDiagnostics() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->RecordWeedingDiagnostics(context, request, response); }));
    }
    ~WithRawCallbackMethod_RecordWeedingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RecordWeedingDiagnostics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RecordWeedingDiagnostics(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetCurrentTrajectories(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCurrentTrajectories(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetRecordingsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetRecordingsList() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetRecordingsList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetRecordingsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRecordingsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRecordingsList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_OpenRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_OpenRecording() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->OpenRecording(context, request, response); }));
    }
    ~WithRawCallbackMethod_OpenRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OpenRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* OpenRecording(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetSnapshot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetSnapshot() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetSnapshot(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetSnapshot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSnapshot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSnapshot(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteRecording() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteRecording(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteRecording(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetTrajectoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetTrajectoryData() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetTrajectoryData(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetTrajectoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::TrajectoryData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTrajectoryData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetTrajectoryPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetTrajectoryPredictImage() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackServerStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const::grpc::ByteBuffer* request) { return this->GetTrajectoryPredictImage(context, request); }));
    }
    ~WithRawCallbackMethod_GetTrajectoryPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::grpc::ByteBuffer>* GetTrajectoryPredictImage(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetTrajectoryTargetImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetTrajectoryTargetImage() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackServerStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const::grpc::ByteBuffer* request) { return this->GetTrajectoryTargetImage(context, request); }));
    }
    ~WithRawCallbackMethod_GetTrajectoryTargetImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryTargetImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::grpc::ByteBuffer>* GetTrajectoryTargetImage(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPredictImageMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPredictImageMetadata() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetPredictImageMetadata(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetPredictImageMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPredictImageMetadata(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPredictImage() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackServerStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const::grpc::ByteBuffer* request) { return this->GetPredictImage(context, request); }));
    }
    ~WithRawCallbackMethod_GetPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::grpc::ByteBuffer>* GetPredictImage(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartUpload() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartUpload(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartUpload(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextUploadState() {
      ::grpc::Service::MarkMethodRawCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextUploadState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextUploadState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetDeepweedPredictionsCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetDeepweedPredictionsCount() {
      ::grpc::Service::MarkMethodRawCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetDeepweedPredictionsCount(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetDeepweedPredictionsCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictionsCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDeepweedPredictionsCount(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetDeepweedPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetDeepweedPredictions() {
      ::grpc::Service::MarkMethodRawCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetDeepweedPredictions(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetDeepweedPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDeepweedPredictions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDeepweedPredictions(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_FindTrajectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_FindTrajectory() {
      ::grpc::Service::MarkMethodRawCallback(15,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->FindTrajectory(context, request, response); }));
    }
    ~WithRawCallbackMethod_FindTrajectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FindTrajectory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FindTrajectory(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetRotaryTicks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetRotaryTicks() {
      ::grpc::Service::MarkMethodRawCallback(16,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetRotaryTicks(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetRotaryTicks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRotaryTicks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRotaryTicks(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SnapshotPredictImages : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SnapshotPredictImages() {
      ::grpc::Service::MarkMethodRawCallback(17,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SnapshotPredictImages(context, request, response); }));
    }
    ~WithRawCallbackMethod_SnapshotPredictImages() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SnapshotPredictImages(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SnapshotPredictImages(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetChipForPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetChipForPredictImage() {
      ::grpc::Service::MarkMethodRawCallback(18,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetChipForPredictImage(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetChipForPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipForPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetChipForPredictImage(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_RecordWeedingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_RecordWeedingDiagnostics() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedRecordWeedingDiagnostics(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_RecordWeedingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status RecordWeedingDiagnostics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedRecordWeedingDiagnostics(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::RecordWeedingDiagnosticsRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::weed_tracking::DiagnosticsSnapshot>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest, ::weed_tracking::DiagnosticsSnapshot>* streamer) {
                       return this->StreamedGetCurrentTrajectories(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetCurrentTrajectories(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetCurrentTrajectoriesRequest,::weed_tracking::DiagnosticsSnapshot>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetRecordingsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetRecordingsList() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* streamer) {
                       return this->StreamedGetRecordingsList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetRecordingsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetRecordingsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetRecordingsList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetRecordingsListRequest,::carbon::frontend::weeding_diagnostics::GetRecordingsListResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_OpenRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_OpenRecording() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* streamer) {
                       return this->StreamedOpenRecording(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_OpenRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status OpenRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::OpenRecordingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedOpenRecording(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::OpenRecordingRequest,::carbon::frontend::weeding_diagnostics::OpenRecordingResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSnapshot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetSnapshot() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* streamer) {
                       return this->StreamedGetSnapshot(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetSnapshot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSnapshot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetSnapshotResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSnapshot(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetSnapshotRequest,::carbon::frontend::weeding_diagnostics::GetSnapshotResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteRecording : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteRecording() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDeleteRecording(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteRecording() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteRecording(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteRecording(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::DeleteRecordingRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetTrajectoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetTrajectoryData() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::carbon::frontend::weeding_diagnostics::TrajectoryData>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest, ::carbon::frontend::weeding_diagnostics::TrajectoryData>* streamer) {
                       return this->StreamedGetTrajectoryData(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetTrajectoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTrajectoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::TrajectoryData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetTrajectoryData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetTrajectoryDataRequest,::carbon::frontend::weeding_diagnostics::TrajectoryData>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetPredictImageMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetPredictImageMetadata() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* streamer) {
                       return this->StreamedGetPredictImageMetadata(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetPredictImageMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPredictImageMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetPredictImageMetadata(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataRequest,::carbon::frontend::weeding_diagnostics::GetPredictImageMetadataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartUpload() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::StartUploadRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedStartUpload(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::StartUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartUpload(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::StartUploadRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextUploadState() {
      ::grpc::Service::MarkMethodStreamed(12,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* streamer) {
                       return this->StreamedGetNextUploadState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextUploadState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetNextUploadStateRequest,::carbon::frontend::weeding_diagnostics::GetNextUploadStateResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetDeepweedPredictionsCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetDeepweedPredictionsCount() {
      ::grpc::Service::MarkMethodStreamed(13,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* streamer) {
                       return this->StreamedGetDeepweedPredictionsCount(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetDeepweedPredictionsCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetDeepweedPredictionsCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetDeepweedPredictionsCount(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountRequest,::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsCountResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetDeepweedPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetDeepweedPredictions() {
      ::grpc::Service::MarkMethodStreamed(14,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* streamer) {
                       return this->StreamedGetDeepweedPredictions(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetDeepweedPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetDeepweedPredictions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetDeepweedPredictions(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsRequest,::carbon::frontend::weeding_diagnostics::GetDeepweedPredictionsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_FindTrajectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_FindTrajectory() {
      ::grpc::Service::MarkMethodStreamed(15,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* streamer) {
                       return this->StreamedFindTrajectory(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_FindTrajectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status FindTrajectory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedFindTrajectory(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::FindTrajectoryRequest,::carbon::frontend::weeding_diagnostics::FindTrajectoryResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetRotaryTicks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetRotaryTicks() {
      ::grpc::Service::MarkMethodStreamed(16,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* streamer) {
                       return this->StreamedGetRotaryTicks(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetRotaryTicks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetRotaryTicks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetRotaryTicks(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetRotaryTicksRequest,::carbon::frontend::weeding_diagnostics::GetRotaryTicksResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SnapshotPredictImages : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SnapshotPredictImages() {
      ::grpc::Service::MarkMethodStreamed(17,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* streamer) {
                       return this->StreamedSnapshotPredictImages(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SnapshotPredictImages() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SnapshotPredictImages(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSnapshotPredictImages(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesRequest,::carbon::frontend::weeding_diagnostics::SnapshotPredictImagesResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetChipForPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetChipForPredictImage() {
      ::grpc::Service::MarkMethodStreamed(18,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* streamer) {
                       return this->StreamedGetChipForPredictImage(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetChipForPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetChipForPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest* /*request*/, ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetChipForPredictImage(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::weeding_diagnostics::GetChipForPredictImageRequest,::carbon::frontend::weeding_diagnostics::GetChipForPredictImageResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_RecordWeedingDiagnostics<WithStreamedUnaryMethod_GetCurrentTrajectories<WithStreamedUnaryMethod_GetRecordingsList<WithStreamedUnaryMethod_OpenRecording<WithStreamedUnaryMethod_GetSnapshot<WithStreamedUnaryMethod_DeleteRecording<WithStreamedUnaryMethod_GetTrajectoryData<WithStreamedUnaryMethod_GetPredictImageMetadata<WithStreamedUnaryMethod_StartUpload<WithStreamedUnaryMethod_GetNextUploadState<WithStreamedUnaryMethod_GetDeepweedPredictionsCount<WithStreamedUnaryMethod_GetDeepweedPredictions<WithStreamedUnaryMethod_FindTrajectory<WithStreamedUnaryMethod_GetRotaryTicks<WithStreamedUnaryMethod_SnapshotPredictImages<WithStreamedUnaryMethod_GetChipForPredictImage<Service > > > > > > > > > > > > > > > > StreamedUnaryService;
  template <class BaseClass>
  class WithSplitStreamingMethod_GetTrajectoryPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithSplitStreamingMethod_GetTrajectoryPredictImage() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::SplitServerStreamingHandler<
          ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerSplitStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>* streamer) {
                       return this->StreamedGetTrajectoryPredictImage(context,
                         streamer);
                  }));
    }
    ~WithSplitStreamingMethod_GetTrajectoryPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTrajectoryPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedGetTrajectoryPredictImage(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::carbon::frontend::weeding_diagnostics::GetTrajectoryPredictImageRequest,::carbon::frontend::weeding_diagnostics::ImageChunk>* server_split_streamer) = 0;
  };
  template <class BaseClass>
  class WithSplitStreamingMethod_GetTrajectoryTargetImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithSplitStreamingMethod_GetTrajectoryTargetImage() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::SplitServerStreamingHandler<
          ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerSplitStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>* streamer) {
                       return this->StreamedGetTrajectoryTargetImage(context,
                         streamer);
                  }));
    }
    ~WithSplitStreamingMethod_GetTrajectoryTargetImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTrajectoryTargetImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedGetTrajectoryTargetImage(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::carbon::frontend::weeding_diagnostics::GetTrajectoryTargetImageRequest,::carbon::frontend::weeding_diagnostics::ImageChunk>* server_split_streamer) = 0;
  };
  template <class BaseClass>
  class WithSplitStreamingMethod_GetPredictImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithSplitStreamingMethod_GetPredictImage() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::SplitServerStreamingHandler<
          ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerSplitStreamer<
                     ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest, ::carbon::frontend::weeding_diagnostics::ImageChunk>* streamer) {
                       return this->StreamedGetPredictImage(context,
                         streamer);
                  }));
    }
    ~WithSplitStreamingMethod_GetPredictImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPredictImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest* /*request*/, ::grpc::ServerWriter< ::carbon::frontend::weeding_diagnostics::ImageChunk>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedGetPredictImage(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::carbon::frontend::weeding_diagnostics::GetPredictImageRequest,::carbon::frontend::weeding_diagnostics::ImageChunk>* server_split_streamer) = 0;
  };
  typedef WithSplitStreamingMethod_GetTrajectoryPredictImage<WithSplitStreamingMethod_GetTrajectoryTargetImage<WithSplitStreamingMethod_GetPredictImage<Service > > > SplitStreamedService;
  typedef WithStreamedUnaryMethod_RecordWeedingDiagnostics<WithStreamedUnaryMethod_GetCurrentTrajectories<WithStreamedUnaryMethod_GetRecordingsList<WithStreamedUnaryMethod_OpenRecording<WithStreamedUnaryMethod_GetSnapshot<WithStreamedUnaryMethod_DeleteRecording<WithStreamedUnaryMethod_GetTrajectoryData<WithSplitStreamingMethod_GetTrajectoryPredictImage<WithSplitStreamingMethod_GetTrajectoryTargetImage<WithStreamedUnaryMethod_GetPredictImageMetadata<WithSplitStreamingMethod_GetPredictImage<WithStreamedUnaryMethod_StartUpload<WithStreamedUnaryMethod_GetNextUploadState<WithStreamedUnaryMethod_GetDeepweedPredictionsCount<WithStreamedUnaryMethod_GetDeepweedPredictions<WithStreamedUnaryMethod_FindTrajectory<WithStreamedUnaryMethod_GetRotaryTicks<WithStreamedUnaryMethod_SnapshotPredictImages<WithStreamedUnaryMethod_GetChipForPredictImage<Service > > > > > > > > > > > > > > > > > > > StreamedService;
};

}  // namespace weeding_diagnostics
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fweeding_5fdiagnostics_2eproto__INCLUDED
