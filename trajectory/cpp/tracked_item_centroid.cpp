#include "trajectory/cpp/tracked_item_centroid.hpp"

#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <spdlog/spdlog.h>

namespace carbon::trajectory {
// private ctor
TrackedItemCentroid::TrackedItemCentroid(const std::string &pcam_id, uint32_t tracker_id, float x, float y, double x_mm,
                                         double y_mm, double z_mm, float size, float score, float weed_score,
                                         float crop_score, float plant_score, int64_t timestamp_ms,
                                         bool has_perspective, const std::vector<std::string> &intersection_classes,
                                         const std::vector<std::pair<std::string, float>> &detection_classes,
                                         const std::vector<float> &embedding, cv::runtime::proto::HitClass hit_class,
                                         bool perspective_in_dist_buff, float size_mm, bool is_plant_captcha_only,
                                         uint32_t index, const std::vector<float> &embedding_category_distances,
                                         std::shared_ptr<std::vector<std::string>> embedding_categories,
                                         uint32_t detection_id, bool available_for_snapshotting)
    : pcam_id_(pcam_id), tracker_id_(tracker_id), x_(x), y_(y), x_mm_(x_mm), y_mm_(y_mm), z_mm_(z_mm), size_(size),
      score_(score), weed_score_(weed_score), crop_score_(crop_score), plant_score_(plant_score),
      timestamp_ms_(timestamp_ms), has_perspective_(has_perspective), p2p_coord_(std::pair<float, float>(x, y)),
      intersection_classes_(intersection_classes), detection_classes_(detection_classes), embedding_(embedding),
      hit_class_(hit_class), in_dist_buff_(perspective_in_dist_buff), size_mm_(size_mm),
      is_plant_captcha_only_(is_plant_captcha_only), index_(index),
      embedding_category_distances_(embedding_category_distances), embedding_categories_(embedding_categories),
      detection_id_(detection_id), available_for_snapshotting_(available_for_snapshotting) {
  if (timestamp_ms == 0) {
    spdlog::warn("Timestamp for new centroid is 0");
  }
}
// public ctor
TrackedItemCentroid::TrackedItemCentroid(const std::string &pcam_id, uint32_t tracker_id, float x, float y, double x_mm,
                                         double y_mm, double z_mm, float size, float score, float weed_score,
                                         float crop_score, float plant_score, int64_t timestamp_ms,
                                         bool has_perspective, const std::vector<std::string> &intersection_classes,
                                         const std::vector<std::pair<std::string, float>> &detection_class,
                                         const std::vector<float> &embedding, cv::runtime::proto::HitClass hit_class,
                                         bool perspective_in_dist_buff, lib::common::geometric::GeometricCam *geo_cam,
                                         uint32_t index, const std::vector<float> &embedding_category_distances,
                                         std::shared_ptr<std::vector<std::string>> embedding_categories,
                                         uint32_t detection_id)
    : TrackedItemCentroid(pcam_id, tracker_id, x, y, x_mm, y_mm, z_mm, size, score, weed_score, crop_score, plant_score,
                          timestamp_ms, has_perspective, intersection_classes, detection_class, embedding, hit_class,
                          perspective_in_dist_buff, 0.0f, false, index, embedding_category_distances,
                          embedding_categories, detection_id, false) {
  size_mm_ = (float)geo_cam->get_size_mm_from_size_px(size, z_mm);
}
// public ctor
TrackedItemCentroid::TrackedItemCentroid(const TrackedItemCentroid &orig, float x, float y, double x_mm, double y_mm,
                                         double z_mm, int64_t timestamp_ms)
    : TrackedItemCentroid(orig.pcam_id_, orig.tracker_id_, x, y, x_mm, y_mm, z_mm, orig.get_size(), orig.get_score(),
                          orig.get_weed_score(), orig.get_crop_score(), orig.get_plant_score(), timestamp_ms, false,
                          orig.intersection_classes_, orig.detection_classes_, orig.embedding_, orig.hit_class_, false,
                          orig.size_mm_, orig.is_plant_captcha_only_, orig.index_, orig.embedding_category_distances_,
                          orig.embedding_categories_, orig.detection_id(), orig.available_for_snapshotting_) {}
// public ctor
TrackedItemCentroid::TrackedItemCentroid(const TrackedItemCentroid &orig)
    : pcam_id_(orig.pcam_id_), tracker_id_(orig.tracker_id_), x_(orig.x_), y_(orig.y_), x_mm_(orig.x_mm_),
      y_mm_(orig.y_mm_), z_mm_(orig.z_mm_), size_(orig.size_), score_(orig.score_), weed_score_(orig.weed_score_),
      crop_score_(orig.crop_score_), plant_score_(orig.plant_score_), timestamp_ms_(orig.timestamp_ms_),
      has_perspective_(orig.has_perspective_), p2p_coord_(orig.p2p_coord_),
      intersection_classes_(orig.intersection_classes_), detection_classes_(orig.detection_classes_),
      embedding_(orig.embedding_), hit_class_(orig.hit_class_), in_dist_buff_(orig.in_dist_buff_),
      size_mm_(orig.size_mm_), is_plant_captcha_only_(orig.is_plant_captcha_only_.load()), index_(orig.index_),
      embedding_category_distances_(orig.embedding_category_distances_),
      embedding_categories_(orig.embedding_categories_), detection_id_(orig.detection_id()),
      available_for_snapshotting_(orig.available_for_snapshotting_) {}

std::string TrackedItemCentroid::get_pcam_id() const { return pcam_id_; }

std::pair<float, float> TrackedItemCentroid::get_coord() const { return std::pair<float, float>(x_, y_); }

void TrackedItemCentroid::set_coord(float x, float y) {
  x_ = x;
  y_ = y;
}

std::pair<float, float> TrackedItemCentroid::get_p2p_coord() const { return p2p_coord_; }

void TrackedItemCentroid::set_p2p_coord(std::pair<float, float> p2p_coord) { p2p_coord_ = p2p_coord; }

float TrackedItemCentroid::get_x() const { return x_; }

float TrackedItemCentroid::get_y() const { return y_; }

void TrackedItemCentroid::convert_x_y_from_distortion_undistortion_maps(torch::Tensor map_x, torch::Tensor map_y) {
  auto original_x = x_;
  auto original_y = y_;
  if (original_x >= 0 && original_x < (float)map_x.size(1) && original_y >= 0 && original_y < (float)map_y.size(0)) {
    x_ = map_x.index({(int64_t)original_y, (int64_t)original_x}).item<float>();
    y_ = map_y.index({(int64_t)original_y, (int64_t)original_x}).item<float>();
  }
}

double TrackedItemCentroid::get_x_mm() const { return x_mm_; }

double TrackedItemCentroid::get_y_mm() const { return y_mm_; }

double TrackedItemCentroid::get_z_mm() const { return z_mm_; }

float TrackedItemCentroid::get_score() const { return score_; }

float TrackedItemCentroid::get_weed_score() const { return weed_score_; }

float TrackedItemCentroid::get_crop_score() const { return crop_score_; }

float TrackedItemCentroid::get_plant_score() const { return plant_score_; }

void TrackedItemCentroid::set_score(float score) { score_ = score; }

void TrackedItemCentroid::set_weed_score(float score) { weed_score_ = score; }

void TrackedItemCentroid::set_crop_score(float score) { crop_score_ = score; }

void TrackedItemCentroid::set_plant_score(float score) { plant_score_ = score; }

void TrackedItemCentroid::set_mm_coord(double x_mm, double y_mm, double z_mm) {
  x_mm_ = x_mm;
  y_mm_ = y_mm;
  z_mm_ = z_mm;
}

void TrackedItemCentroid::shift_coord(float x_shift, float y_shift) {
  x_ += x_shift;
  y_ += y_shift;
}

float TrackedItemCentroid::get_size() const { return size_; }

void TrackedItemCentroid::set_size(float size) { size_ = size; }

bool TrackedItemCentroid::get_has_perspective() const { return has_perspective_; }

void TrackedItemCentroid::set_has_perspective(bool has_perspective) { has_perspective_ = has_perspective; }

int64_t TrackedItemCentroid::get_timestamp_ms() const { return timestamp_ms_; }

std::vector<std::string> TrackedItemCentroid::get_intersection_classes() const { return intersection_classes_; }

void TrackedItemCentroid::set_intersection_classes(std::vector<std::string> classes) {
  intersection_classes_ = classes;
}

std::vector<std::pair<std::string, float>> TrackedItemCentroid::get_classes() const { return detection_classes_; }

std::vector<float> TrackedItemCentroid::get_embedding() const { return embedding_; }

std::pair<std::string, float> TrackedItemCentroid::get_best_class() const {
  if (detection_classes_.empty()) {
    spdlog::warn("No detection class defined.");
    return std::make_pair<std::string, float>(std::string(UNKNOWN_CLASS), 1.0f);
  }
  auto cur_best = detection_classes_[0];
  for (auto &it : detection_classes_) {
    if (it.second > cur_best.second) {
      cur_best = it;
    }
  }
  return cur_best;
}

void TrackedItemCentroid::set_classes(std::vector<std::pair<std::string, float>> detection_classes) {
  detection_classes_ = detection_classes;
}

void TrackedItemCentroid::set_embedding(std::vector<float> embedding) { embedding_ = embedding; }

cv::runtime::proto::HitClass TrackedItemCentroid::get_hit_class() const { return hit_class_; }

void TrackedItemCentroid::set_hit_class(cv::runtime::proto::HitClass hit_class) { hit_class_ = hit_class; }

void TrackedItemCentroid::copy(const TrackedItemCentroid &orig) {
  pcam_id_ = orig.pcam_id_;
  tracker_id_ = orig.tracker_id_;
  x_ = orig.x_;
  y_ = orig.y_;
  x_mm_ = orig.x_mm_;
  y_mm_ = orig.y_mm_;
  z_mm_ = orig.z_mm_;
  timestamp_ms_ = orig.timestamp_ms_;
  has_perspective_ = orig.has_perspective_;
  p2p_coord_ = orig.p2p_coord_;
  in_dist_buff_ = orig.in_dist_buff_;
  is_plant_captcha_only_ = orig.is_plant_captcha_only_.load();
  index_ = orig.index_;
  available_for_snapshotting_ = orig.available_for_snapshotting_;
  copy_perspective_data(orig);
}

void TrackedItemCentroid::copy_perspective_data(const TrackedItemCentroid &orig) {
  size_ = orig.size_;
  score_ = orig.score_;
  weed_score_ = orig.weed_score_;
  crop_score_ = orig.crop_score_;
  plant_score_ = orig.plant_score_;
  intersection_classes_ = orig.intersection_classes_;
  detection_classes_ = orig.detection_classes_;
  embedding_ = orig.embedding_;
  hit_class_ = orig.hit_class_;
  size_mm_ = orig.size_mm_;
  embedding_category_distances_ = orig.embedding_category_distances_;
  embedding_categories_ = orig.embedding_categories_;
  detection_id_ = orig.detection_id();
}

const std::vector<float> &TrackedItemCentroid::get_embedding_category_distances() const {
  return embedding_category_distances_;
}

const std::shared_ptr<std::vector<std::string>> &TrackedItemCentroid::get_embedding_categories() const {
  return embedding_categories_;
}

} // namespace carbon::trajectory