/*
 * This implementation is deprecated, use async_grpc_service.cpp instead.
 */

#include "cv/runtime/cpp/grpc_services/sync/grpc_service.hpp"

#include <ATen/cuda/CUDAContext.h>
#include <glm/glm.hpp>
#include <random>
#include <spdlog/spdlog.h>
#include <vector>

#include "cv/deepweed/deepweed_utils.h"
#include "cv/runtime/cpp/deepweed_output_protobuf.h"
#include "cv/runtime/cpp/nodes/burst_record.h"
#include "cv/runtime/cpp/nodes/deepweed_infer.h"
#include "cv/runtime/cpp/nodes/deepweed_output_buffer.h"
#include "cv/runtime/cpp/nodes/image_interest_scorer.h"
#include "cv/runtime/cpp/nodes/p2p/burst_record.h"
#include "cv/runtime/cpp/nodes/p2p/capture_buffer.h"
#include "cv/runtime/cpp/nodes/p2p/infer.h"
#include "cv/runtime/cpp/nodes/target_img_buffer.h"
#include "cv/runtime/cpp/nodes/v4l2_sink.h"
#include "generated/cv/runtime/proto/cv_runtime.pb.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/time.h"
#include "lib/common/cpp/utils/environment.hpp"
#include "lib/common/cpp/utils/role.hpp"
#include "lib/common/cpp/utils/scope_guard.hpp"
#include "lib/common/image/cpp/focus_metric.h"
#include "lib/common/model/cpp/model_registry.h"
#include "lib/drivers/thinklucid/cpp/firmware_updater.h"
#include "targeting/cpp/targeting_mode_watcher.hpp"
#include "v4l2_utils/cpp/v4l2_utils.hpp"

namespace cv {
namespace runtime {

using namespace lib::common::camera;
using namespace cv::runtime::proto;
constexpr std::string_view data_dir("/data");

CVRuntimeServiceImplSync::CVRuntimeServiceImplSync(
    std::shared_ptr<NodeRegistry> node_registry,
    std::shared_ptr<std::map<std::string, std::map<proto::BufferUseCase, std::string>>> camera_to_buffer,
    std::shared_ptr<std::map<std::string, lib::common::camera::Camera>> camera_id_to_camera,
    std::shared_ptr<std::map<std::string, bool>> camera_id_to_transpose,
    std::shared_ptr<std::map<std::string, std::pair<std::string, lib::common::camera::Camera>>> sim_cameras,
    std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry,
    std::shared_ptr<lib::common::GeoData> geo_data, std::shared_ptr<lib::common::ImplementStatus> implement_status,
    std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues,
    std::shared_ptr<carbon::config::ConfigTree> deepweed_config, std::string kLightweightBurstDirectory,
    std::string kFullBurstDirectory,
    const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues,
    std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>>>>
        deepweed_buffers,
    std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>>
        p2p_buffers,
    std::string role,
    std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
        distance_camera_buffer,
    std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> chip_score_queues,
    std::function<void()> reload_category_collection, std::shared_ptr<cv::embeddings::Manager> embeddings_manager,
    std::shared_ptr<cv::runtime::ErrorState> error_state)
    : CVRuntimeServiceImplCommon(node_registry, camera_to_buffer, camera_id_to_camera, camera_id_to_transpose,
                                 sim_cameras, model_registry, geo_data, implement_status, score_queues, deepweed_config,
                                 kLightweightBurstDirectory, kFullBurstDirectory, p2p_queues, deepweed_buffers,
                                 p2p_buffers, role, distance_camera_buffer, chip_score_queues,
                                 reload_category_collection, embeddings_manager, error_state) {}

CVRuntimeServiceImplSync::~CVRuntimeServiceImplSync() {}

void CVRuntimeServiceImplSync::Build(grpc::ServerBuilder &builder) { builder.RegisterService(this); }

void CVRuntimeServiceImplSync::Start() {
  // no-op
}

void CVRuntimeServiceImplSync::Shutdown() {
  // no-op
}

void CVRuntimeServiceImplSync::Check() {
  // no-op
}

void CVRuntimeServiceImplSync::ReportMetrics() {
  // no-op
}

grpc::Status CVRuntimeServiceImplSync::GetBooted(grpc::ServerContext *, const proto::GetBootedRequest *request,
                                                 proto::GetBootedResponse *response) {
  return processGetBooted(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetReady(grpc::ServerContext *, const proto::GetReadyRequest *request,
                                                proto::GetReadyResponse *response) {
  return processGetReady(request, response);
}

grpc::Status CVRuntimeServiceImplSync::SetP2PContext(grpc::ServerContext *, const proto::SetP2PContextRequest *request,
                                                     proto::SetP2PContextResponse *response) {
  return processSetP2PContext(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetCameraDimensions(grpc::ServerContext *,
                                                           const proto::GetCameraDimensionsRequest *request,
                                                           proto::GetCameraDimensionsResponse *response) {
  return processGetCameraDimensions(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetCameraInfo(grpc::ServerContext *, const proto::GetCameraInfoRequest *request,
                                                     proto::GetCameraInfoResponse *response) {
  return processGetCameraInfo(request, response);
}

grpc::Status
CVRuntimeServiceImplSync::SetDeepweedDetectionCriteria(grpc::ServerContext *,
                                                       const proto::SetDeepweedDetectionCriteriaRequest *request,
                                                       proto::SetDeepweedDetectionCriteriaResponse *response) {
  return processSetDeepweedDetectionCriteria(request, response);
}

grpc::Status
CVRuntimeServiceImplSync::GetDeepweedDetectionCriteria(grpc::ServerContext *,
                                                       const proto::GetDeepweedDetectionCriteriaRequest *request,
                                                       proto::GetDeepweedDetectionCriteriaResponse *response) {
  return processGetDeepweedDetectionCriteria(request, response);
}

grpc::Status
CVRuntimeServiceImplSync::GetDeepweedSupportedCategories(grpc::ServerContext *,
                                                         const proto::GetDeepweedSupportedCategoriesRequest *request,
                                                         proto::GetDeepweedSupportedCategoriesResponse *response) {
  return processGetDeepweedSupportedCategories(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetCameraTemperatures(grpc::ServerContext *,
                                                             const proto::GetCameraTemperaturesRequest *request,
                                                             proto::GetCameraTemperaturesResponse *response) {
  return processGetCameraTemperatures(request, response);
}

grpc::Status CVRuntimeServiceImplSync::SetCameraSettings(grpc::ServerContext *,
                                                         const proto::SetCameraSettingsRequest *request,
                                                         proto::SetCameraSettingsResponse *response) {
  return processSetCameraSettings(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetCameraSettings(grpc::ServerContext *,
                                                         const proto::GetCameraSettingsRequest *request,
                                                         proto::GetCameraSettingsResponse *response) {
  return processGetCameraSettings(request, response);
}

grpc::Status CVRuntimeServiceImplSync::StartBurstRecordFrames(grpc::ServerContext *,
                                                              const proto::StartBurstRecordFramesRequest *request,
                                                              proto::StartBurstRecordFramesResponse *response) {
  return processStartBurstRecordFrames(request, response);
}

grpc::Status CVRuntimeServiceImplSync::StopBurstRecordFrames(grpc::ServerContext *,
                                                             const proto::StopBurstRecordFramesRequest *request,
                                                             proto::StopBurstRecordFramesResponse *response) {
  return processStopBurstRecordFrames(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetConnectors(grpc::ServerContext *, const proto::GetConnectorsRequest *request,
                                                     proto::GetConnectorsResponse *response) {
  return processGetConnectors(request, response);
}

grpc::Status CVRuntimeServiceImplSync::SetConnectors(grpc::ServerContext *, const proto::SetConnectorsRequest *request,
                                                     proto::SetConnectorsResponse *response) {
  return processSetConnectors(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetTiming(grpc::ServerContext *, const proto::GetTimingRequest *request,
                                                 proto::GetTimingResponse *response) {
  return processGetTiming(request, response);
}

grpc::Status CVRuntimeServiceImplSync::Predict(grpc::ServerContext *, const proto::PredictRequest *request,
                                               proto::PredictResponse *response) {
  return processPredict(request, response);
}

grpc::Status CVRuntimeServiceImplSync::LoadAndQueue(grpc::ServerContext *, const proto::LoadAndQueueRequest *request,
                                                    proto::LoadAndQueueResponse *response) {
  return processLoadAndQueue(request, response);
}

grpc::Status CVRuntimeServiceImplSync::SetImage(grpc::ServerContext *, const proto::SetImageRequest *request,
                                                proto::SetImageResponse *response) {
  return processSetImage(request, response);
}

grpc::Status CVRuntimeServiceImplSync::UnsetImage(grpc::ServerContext *, const proto::UnsetImageRequest *request,
                                                  proto::UnsetImageResponse *response) {
  return processUnsetImage(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetModelPaths(grpc::ServerContext *, const proto::GetModelPathsRequest *request,
                                                     proto::GetModelPathsResponse *response) {
  return processGetModelPaths(request, response);
}

grpc::Status CVRuntimeServiceImplSync::SetImageScore(grpc::ServerContext *, const proto::SetImageScoreRequest *request,
                                                     proto::SetImageScoreResponse *response) {
  return processSetImageScore(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetScoreQueue(grpc::ServerContext *, const proto::GetScoreQueueRequest *request,
                                                     proto::GetScoreQueueResponse *response) {
  return processGetScoreQueue(request, response);
}

grpc::Status CVRuntimeServiceImplSync::ListScoreQueues(grpc::ServerContext *, const proto::Empty *request,
                                                       proto::ListScoreQueuesResponse *response) {
  return processListScoreQueues(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetMaxImageScore(grpc::ServerContext *,
                                                        const proto::GetMaxImageScoreRequest *request,
                                                        proto::GetMaxImageScoreResponse *response) {
  return processGetMaxImageScore(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetMaxScoredImage(grpc::ServerContext *,
                                                         const proto::GetMaxScoredImageRequest *request,
                                                         proto::ImageAndMetadataResponse *response) {
  return processGetMaxScoredImage(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetChipImage(grpc::ServerContext *, const proto::GetChipImageRequest *request,
                                                    proto::ChipImageAndMetadataResponse *response) {
  return processGetChipImage(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetChipQueueInformation(grpc::ServerContext *,
                                                               const proto::ChipQueueInformationRequest *request,
                                                               proto::ChipQueueInformationResponse *response) {
  return processGetChipQueueInformation(request, response);
}

grpc::Status CVRuntimeServiceImplSync::SnapshotPredictImages(grpc::ServerContext *,
                                                             const proto::SnapshotPredictImagesRequest *request,
                                                             proto::SnapshotPredictImagesResponse *response) {
  return processSnapshotPredictImages(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetChipForPredictImage(grpc::ServerContext *,
                                                              const proto::GetChipForPredictImageRequest *request,
                                                              proto::GetChipForPredictImageResponse *response) {
  return processGetChipForPredictImage(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetLatestP2PImage(grpc::ServerContext *,
                                                         const proto::GetLatestP2PImageRequest *request,
                                                         proto::P2PImageAndMetadataResponse *response) {
  return processGetLatestP2PImage(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetImageNearTimestamp(grpc::ServerContext *,
                                                             const proto::GetImageNearTimestampRequest *request,
                                                             proto::ImageAndMetadataResponse *response) {
  return processGetImageNearTimestamp(request, response);
}

grpc::Status CVRuntimeServiceImplSync::FlushQueues(grpc::ServerContext *, const proto::FlushQueuesRequest *request,
                                                   proto::FlushQueuesResponse *response) {
  return processFlushQueues(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetLatestImage(grpc::ServerContext *,
                                                      const proto::GetLatestImageRequest *request,
                                                      proto::ImageAndMetadataResponse *response) {
  return processGetLatestImage(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetLightweightBurstRecord(grpc::ServerContext *,
                                                                 const proto::GetLightweightBurstRecordRequest *request,
                                                                 proto::GetLightweightBurstRecordResponse *response) {
  return processGetLightweightBurstRecord(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetDeepweedOutputByTimestamp(
    grpc::ServerContext *, const proto::GetDeepweedOutputByTimestampRequest *request, proto::DeepweedOutput *response) {
  return processGetDeepweedOutputByTimestamp(request, response);
}

grpc::Status
CVRuntimeServiceImplSync::GetRecommendedStrobeSettings(grpc::ServerContext *,
                                                       const proto::GetRecommendedStrobeSettingsRequest *request,
                                                       proto::GetRecommendedStrobeSettingsResponse *response) {
  return processGetRecommendedStrobeSettings(request, response);
}

grpc::Status CVRuntimeServiceImplSync::P2PCapture(grpc::ServerContext *, const proto::P2PCaptureRequest *request,
                                                  proto::P2PCaptureResponse *response) {
  return processP2PCapture(request, response);
}

grpc::Status CVRuntimeServiceImplSync::SetAutoWhitebalance(grpc::ServerContext *,
                                                           const proto::SetAutoWhitebalanceRequest *request,
                                                           proto::SetAutoWhitebalanceResponse *response) {
  return processSetAutoWhitebalance(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetNextDeepweedOutput(grpc::ServerContext *,
                                                             const proto::GetNextDeepweedOutputRequest *request,
                                                             proto::DeepweedOutput *response) {
  return processGetNextDeepweedOutput(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetNextP2POutput(grpc::ServerContext *,
                                                        const proto::GetNextP2POutputRequest *request,
                                                        proto::P2POutputProto *response) {
  return processGetNextP2POutput(request, response);
}

grpc::Status CVRuntimeServiceImplSync::SetTargetingState(grpc::ServerContext *,
                                                         const proto::SetTargetingStateRequest *request,
                                                         proto::SetTargetingStateResponse *response) {
  return processSetTargetingState(request, response);
}

grpc::Status CVRuntimeServiceImplSync::P2PBufferringBurstCapture(grpc::ServerContext *,
                                                                 const proto::P2PBufferringBurstCaptureRequest *request,
                                                                 proto::P2PBufferringBurstCaptureResponse *response) {
  return processP2PBufferringBurstCapture(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetNextFocusMetric(grpc::ServerContext *,
                                                          const proto::GetNextFocusMetricRequest *request,
                                                          proto::GetNextFocusMetricResponse *response) {
  return processGetNextFocusMetric(request, response);
}

grpc::Status CVRuntimeServiceImplSync::RemoveDataDir(grpc::ServerContext *, const proto::RemoveDataDirRequest *request,
                                                     proto::RemoveDataDirResponse *response) {
  return processRemoveDataDir(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetLastNImages(grpc::ServerContext *, const proto::LastNImageRequest *request,
                                                      grpc::ServerWriter<proto::ImageAndMetadataResponse> *writer) {
  return processGetLastNImages(request, writer);
}

grpc::Status CVRuntimeServiceImplSync::GetComputeCapabilities(grpc::ServerContext *, const proto::Empty *request,
                                                              proto::ComputeCapabilitiesResponse *response) {
  return processGetComputeCapabilities(request, response);
}

grpc::Status
CVRuntimeServiceImplSync::GetSupportedTensorRTVersions(grpc::ServerContext *, const proto::Empty *request,
                                                       proto::SupportedTensorRTVersionsResponse *response) {
  return processGetSupportedTensorRTVersions(request, response);
}

grpc::Status CVRuntimeServiceImplSync::ReloadCategoryCollection(grpc::ServerContext *, const proto::Empty *request,
                                                                proto::Empty *response) {
  return processReloadCategoryCollection(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetCategoryCollection(grpc::ServerContext *, const proto::Empty *request,
                                                             proto::GetCategoryCollectionResponse *response) {
  return processGetCategoryCollection(request, response);
}

grpc::Status CVRuntimeServiceImplSync::GetErrorState(grpc::ServerContext *, const proto::Empty *request,
                                                     proto::GetErrorStateResponse *response) {
  return processGetErrorState(request, response);
}

std::unique_ptr<grpc::Server> start_grpc_server(CVRuntimeServiceImplSync *service_impl,
                                                ImageStreamServiceImpl *image_service_impl,
                                                lib::common::logging::LoggingServiceImpl *logging_service_impl,
                                                const std::string &server_address) {
  grpc::ServerBuilder builder;
  builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
  builder.SetMaxSendMessageSize(50 * 1024 * 1024); // 50 MB for image transfer to data_upload_manager (4 * 3000 * 4096)
  builder.SetResourceQuota(grpc::ResourceQuota().SetMaxThreads(60));
  builder.RegisterService(service_impl);
  builder.RegisterService(image_service_impl);
  builder.RegisterService(logging_service_impl);
  return builder.BuildAndStart();
}

} // namespace runtime

} // namespace cv
