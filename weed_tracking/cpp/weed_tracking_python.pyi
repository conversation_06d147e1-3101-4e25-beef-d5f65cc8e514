from typing import Dict, List, Optional, Set, Tuple

from cv.deepweed.deepweed_python import DeepweedOutput
from ingest.pybind.ingest_python import IngestClient, IngestProducer
from lib.common.geometric.cpp.geometric_python import GeometricCam
from trajectory.pybind.trajectory_python import Tracked<PERSON>temCentroid, TrackedItemPosition, Trajectory
from weed_tracking_libs.arbiter.pybind.arbiter_python import ArbiterClient, ArbiterTrigger
from generated.recorder.proto.recorder_pb2 import TrackedItem, CandidateShift

class WeedTrackerOutput:
    def get_timestamp_ms(self) -> int: ...
    def get_trajectories(self) -> List[Trajectory]: ...
    def get_newly_tracked_ids(self) -> List[int]: ...
    def get_deepweed_flow_offsets_xy(self) -> Dict[int, Tuple[float, float]]: ...
    def get_number_missed_first_instances(self) -> Dict[int, int]: ...
    def get_vertical_offset_first_instances(self) -> Dict[int, float]: ...

class WeedTracker:
    def __init__(
        self,
        pcam_id: str,
        numeric_id: int,
        geo_cam: GeometricCam,
        prune_grace_ms: int,
        width: int,
        height: int,
        max_pos_mm_y: float,
        max_number_of_weeds: int,
    ) -> None: ...
    def add_rotary_ticks(self, timestamp_ms: int, mm_travelled: float) -> int: ...
    def add_deepweed(self, deepweed_output: DeepweedOutput, recorded_tracked_items: List[TrackedItem], model_undistorted_coordinates: Dict[int, Tuple[float, float]], best_candidate_match: CandidateShift, all_candidate_matches: List[CandidateShift], centroids_used_in_dedup: Set[int]
    ) -> Tuple[int, int, int, Dict[int, int]]: ...
    def get_latest_weeds(self) -> WeedTrackerOutput: ...

class AimbotWeedTracker(IngestProducer, ArbiterTrigger):
    def __init__(
        self,
        name: str,
        numeric_id: int,
        width: int,
        height: int,
        log_ticks: bool,
        geo_cam: GeometricCam,
        max_pos_mm_y: float,
        score_images: bool,
        max_number_of_weeds: int,
        cv_address: str,
    ) -> None: ...
    def get_pcam_id(self) -> str: ...
    def get_latest_weeds(self) -> WeedTrackerOutput: ...
    def get_weed_tracker(self) -> WeedTracker: ...
    def get_last_centroid_with_perspective_before_timestamp_from_trajectory(
        self, trajectory: Trajectory, timestamp_ms: int
    ) -> TrackedItemCentroid: ...
    def get_estimated_position_at_timestamp_from_trajectory(
        self, trajectory: Trajectory, ms_from_now: int
    ) -> TrackedItemPosition: ...
    def get_last_item_from_trajectory(self, trajectory: Trajectory) -> TrackedItemCentroid: ...
    def get_pix_per_ticks(self) -> float: ...
    def get_geo_cam(self) -> GeometricCam: ...
    def get_averages_for_all_columns(self, size_mm: Optional[float] = 0) -> List[float]: ...
    def aimbot_weed_tracker_state(self) -> Tuple[int, int, int, bool, bool]: ...

class AimbotTrackerOwner:
    def __init__(self) -> None: ...
    def set(self, scanners: List[AimbotWeedTracker]) -> None: ...
    def get_tracker(self, id: int) -> Optional[AimbotWeedTracker]: ...

class WeedTrackingException(Exception):
    pass

class WeedTrackingTimeoutException(WeedTrackingException):
    pass

class CropLineDetector(ArbiterClient):
    def __init__(self) -> None: ...
    def start(self) -> None: ...

class WeedingDiagnostics(IngestClient):
    @staticmethod
    def get() -> "WeedingDiagnostics": ...
    def is_marked_for_images(self, tid: int) -> bool: ...
    def attempting_to_save_predict(self, tid: int) -> None: ...
    def finished_attempting_to_save_predict(self, tid: int) -> None: ...
