package config

import (
	"errors"
	"path"
	"runtime"
	"runtime/cgo"
	"sync/atomic"

	"github.com/carbonrobotics/robot/golang/swig/config_client"
	"github.com/sirupsen/logrus"
)

const (
	FeatureFlagMessaging                    = "messaging_feature"
	FeatureFlagMapping                      = "mapping_feature"
	FeatureFlagModelPinningDefault          = "pinning_default_feature"
	FeatureFlagCopyModelinator              = "copy_modelinator_feature"
	FeatureFlagEmbeddingBasedClassification = "embedding_based_classification_feature"
)

// Stash feature flags here, this map should stay relatively small...
var featureFlagsNode = make(map[string]*ConfigTree)

func FeatureFlagEnabled(commonNode *ConfigTree, feature string, callback func(bool)) (bool, error) {
	featureNode, ok := featureFlagsNode[feature]
	if !ok {
		featureNode = commonNode.GetNode(path.Join("feature_flags", feature))
		if featureNode == nil {
			return false, errors.New("feature_flags node not found")
		}

		featureFlagsNode[feature] = featureNode
	}

	// subscribe to updates
	if callback != nil {
		featureNode.RegisterCallback(func() {
			value := featureNode.GetBoolValue()
			callback(value)
		})
	}
	// get initial value
	value := featureNode.GetBoolValue()
	return value, nil
}

func GetFeatureFlagNode(commonNode *ConfigTree, feature string) (*ConfigTree, error) {
	featureNode := commonNode.GetNode(path.Join("feature_flags", feature))
	if featureNode == nil {
		return nil, errors.New("feature_flags node not found")
	}

	return featureNode, nil
}

func featureFlagEnabledIfExists(commonNode *ConfigTree, feature string) bool {
	val, err := FeatureFlagEnabled(commonNode, feature, nil)
	if err != nil {
		return false
	}
	return val
}

func GetComputerConfigPrefix() string {
	return config_client.Get_computer_config_prefix()
}

func MakeRobotLocalAddr(port uint16) string {
	return config_client.Make_robot_local_addr(port)
}

func GetRowComputerConfigPrefix(rowId int) string {
	return config_client.Get_row_computer_config_prefix(rowId)
}

type ConfigTree struct {
	sub                            *ConfigSubscriber
	cpp_type                       config_client.Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_
	callbackMap                    map[uint32]cgo.Handle
	deleted                        bool
	garbageCollectorDefenseCounter int32
}

func newConfigTree(cpp_type config_client.Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, sub *ConfigSubscriber) *ConfigTree {
	c := &ConfigTree{
		sub:         sub,
		cpp_type:    cpp_type,
		callbackMap: make(map[uint32]cgo.Handle),
	}
	runtime.SetFinalizer(c, deleteConfigTree)
	return c
}

func preDeleteConfigTree(c *ConfigTree) {
	deleteConfigTree(c)
}

func deleteConfigTree(c *ConfigTree) {
	if c.deleted {
		return
	}
	for id, _ := range c.callbackMap {
		c.UnregisterCallback(id)
	}
	config_client.Delete_config_tree_shared_ptr(c.cpp_type)
	c.cpp_type = nil
	c.deleted = true

	if atomic.LoadInt32(&c.garbageCollectorDefenseCounter) == 123456789 {
		logrus.Info("Wow Lucky, this config got used that many time to trigger this log line. Well at least golang could not optimize out the variable.")
	}
}

func (c *ConfigTree) antiGarbageCollection() {
	atomic.AddInt32(&c.garbageCollectorDefenseCounter, 1)
}

func (c *ConfigTree) GetNode(key string) *ConfigTree {
	defer c.antiGarbageCollection()
	c_ptr := config_client.Get_node(c.cpp_type, key)

	if c_ptr == nil {
		return nil
	}

	return newConfigTree(c_ptr, c.sub)
}

func (c *ConfigTree) HasNode(key string) bool {
	defer c.antiGarbageCollection()
	return config_client.Has_node(c.cpp_type, key)
}

func (c *ConfigTree) GetChild(name string) *ConfigTree {
	defer c.antiGarbageCollection()
	c_ptr := config_client.Get_child(c.cpp_type, name)

	if c_ptr == nil {
		return nil
	}

	return newConfigTree(c_ptr, c.sub)
}

func (c *ConfigTree) GetChildrenNodes() []*ConfigTree {
	defer c.antiGarbageCollection()
	var results []*ConfigTree
	nodes := config_client.Get_children_names(c.cpp_type)
	for i := 0; i < int(nodes.Size()); i++ {
		name := nodes.Get(i)
		node := config_client.Get_node(c.cpp_type, name)
		if node != nil {
			results = append(results, newConfigTree(node, c.sub))
		}
	}
	return results
}

func (c *ConfigTree) GetName() string {
	defer c.antiGarbageCollection()
	return config_client.Get_name(c.cpp_type)
}

func (c *ConfigTree) GetIntValue() int64 {
	defer c.antiGarbageCollection()
	return config_client.Get_int_value(c.cpp_type)
}

func (c *ConfigTree) GetUIntValue() uint64 {
	defer c.antiGarbageCollection()
	return config_client.Get_uint_value(c.cpp_type)
}

func (c *ConfigTree) GetFloatValue() float64 {
	defer c.antiGarbageCollection()
	return config_client.Get_double_value(c.cpp_type)
}

func (c *ConfigTree) GetBoolValue() bool {
	defer c.antiGarbageCollection()
	return config_client.Get_bool_value(c.cpp_type)
}

func (c *ConfigTree) GetStringValue() string {
	defer c.antiGarbageCollection()
	return config_client.Get_string_value(c.cpp_type)
}

func (c *ConfigTree) UnregisterCallback(id uint32) {
	defer c.antiGarbageCollection()
	config_client.Unregister_callback(c.cpp_type, uint(id))
	config_client.DeleteCFunctionHandle(c.callbackMap[id])
	delete(c.callbackMap, id)
}

func (c *ConfigTree) RegisterCallback(callback func()) uint32 {
	defer c.antiGarbageCollection()
	handle := config_client.CreateCFunctionHandle(callback)
	id := config_client.RegisterCallback(c.cpp_type, handle)
	c.callbackMap[id] = handle
	return id
}

type ConfigSubscriber struct {
	cpp_type config_client.ConfigSubscriber
}

func NewConfigSubscriber(addr string) *ConfigSubscriber {
	sub := &ConfigSubscriber{
		cpp_type: config_client.NewConfigSubscriber(addr),
	}
	runtime.SetFinalizer(sub, deleteConfigSubscriber)
	return sub
}

func deleteConfigSubscriber(sub *ConfigSubscriber) {
	config_client.DeleteConfigSubscriber(sub.cpp_type)
	sub.cpp_type = nil
}

func (s *ConfigSubscriber) AddConfigTree(name string, path string, schema string) {
	s.cpp_type.Add_config_tree(name, path, schema)
}

func (s *ConfigSubscriber) Start() {
	s.cpp_type.Start()
}

func (s *ConfigSubscriber) WaitUntilReady() {
	s.cpp_type.Wait_until_ready()
}

func (s *ConfigSubscriber) Refill() bool {
	return s.cpp_type.Refill()
}

func (s *ConfigSubscriber) GetConfigNode(name string, key string) *ConfigTree {
	c_ptr := s.cpp_type.Get_config_node(name, key)

	if c_ptr == nil {
		return nil
	}

	return newConfigTree(c_ptr, s)
}
