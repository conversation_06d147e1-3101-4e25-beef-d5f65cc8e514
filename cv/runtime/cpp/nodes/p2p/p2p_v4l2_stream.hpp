#pragma once

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <filesystem>
#include <mutex>
#include <thread>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "cv/p2p/output.h"
#include "cv/runtime/cpp/cv_http_client.h"
#include "cv/runtime/cpp/image_score_queue.h"
#include "cv/runtime/cpp/metadata_writer.h"
#include "cv/runtime/cpp/node.h"
#include "cv/runtime/cpp/nodes/buffer.h"
#include "cv/runtime/cpp/prediction_writer.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/image/cpp/async_image_io.h"
#include "v4l2_utils/cpp/v4l2_writer.hpp"
#include <config/tree/cpp/config_atomic_accessor.hpp>
#include <config/tree/cpp/config_tree.hpp>

namespace cv {
namespace runtime {
namespace nodes {
namespace p2p {

class P2PV4l2Stream : public CVNodeImpl {
public:
  static std::filesystem::path capture_path();
  using FixedCamBuffer = std::shared_ptr<FixedBuffer<int64_t, lib::common::camera::CameraImage>>;
  using FixedCamBufferMap = std::shared_ptr<std::map<std::string, FixedCamBuffer>>;
  P2PV4l2Stream(lib::common::camera::Camera camera, Input<cv::p2p::P2POutput> input,
                Input<cv::p2p::P2POutputForBurstRecord> p2p_output_for_burst_record,
                FixedCamBufferMap dist_camera_buffers, FixedCamBuffer target_img_buffer,
                std::shared_ptr<carbon::config::ConfigTree> enabled_cfg);
  ~P2PV4l2Stream();

  void close_connectors() override { input_.close(); }
  int64_t tick() override;
  static std::string build_name(const std::string &cam_id) { return fmt::format("/p2p_v4l2_stream/{}", cam_id); }
  inline void set_identifier(uint32_t id) { identifier_ = id; }

private:
  int64_t last_processed_ts_;
  int64_t last_predict_ts_;
  glm::ivec2 last_coords_;
  std::string last_predict_name_;
  std::optional<lib::common::camera::CameraImage> curr_predict_img_;
  Input<cv::p2p::P2POutput> input_;
  Input<cv::p2p::P2POutputForBurstRecord> input_record_data_;
  __u8 *video_buffer_;
  torch::Tensor torch_video_buffer_;
  carbon::v4l2::V4l2Writer v4l2_writer_;
  bool buffer_initialized_;
  FixedCamBufferMap dist_camera_buffers_;
  FixedCamBuffer target_img_buffer_;
  carbon::config::ConfigAtomicAccessor<bool> enabled_;
  std::atomic<uint32_t> identifier_;

  bool load_predict_image(const cv::p2p::P2POutputForBurstRecord &record_data, const cv::p2p::P2POutput &output_data,
                          const glm::vec2 &dims, float target_ppi);
  void init_buffers_if_needed(int width, int height);
  void add_id(int width, int height);
};

} // namespace p2p
} // namespace nodes
} // namespace runtime
} // namespace cv
