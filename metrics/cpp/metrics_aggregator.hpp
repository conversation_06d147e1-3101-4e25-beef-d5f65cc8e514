#pragma once

#include <atomic>
#include <map>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <unordered_map>
#include <vector>

#include <ingest/cpp/ingest_client.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>
#include <metrics/cpp/conclusion_counter.hpp>
#include <metrics/cpp/conclusion_types.hpp>
#include <metrics/cpp/plant_conclusion_counter.hpp>
#include <weed_tracking/cpp/aimbot/aimbot_tracker.hpp>
namespace prometheus {
class Registry;
}
namespace carbon {
namespace weed_tracking {
class Trajectory;
} // namespace weed_tracking
namespace metrics {
class RollingConclusionCounter {
public:
  RollingConclusionCounter(std::function<uint64_t()> rolling_duration_s_func);

  ConclusionCounter get_conclusion_counter();

  void add_conclusion(metrics::ConclusionType type);

private:
  struct TimestampedConclusion {
    metrics::ConclusionType type;
    uint64_t timestamp_ms;
  };

  uint64_t rolling_duration_s_cache_;
  uint64_t rolling_duration_cache_timestamp_ms_;
  std::function<uint64_t()> rolling_duration_s_func_;
  std::queue<TimestampedConclusion> rolling_buffer_;
  ConclusionCounter active_counter_;
  ConclusionCounter cached_counter_;
  uint64_t cached_counter_timestamp_ms_;
  std::mutex cached_counter_mutex_;
};

class MetricsAggregator : public ingest::FilteredIngestClient<>, public ConclusionCounterFetcher {
public:
  ConclusionCounter get_conclusion_counter() override;
  inline void set_scheduler_frame_time(float time) { scheduler_frame_time_ = time; }
  void start();
  virtual ~MetricsAggregator();
  static std::shared_ptr<MetricsAggregator> get();
  inline void increment_scheduler_failure(uint32_t scanner_id, SchedulingFailureType type) {
    ++scheduling_failures_.at(scanner_id)[static_cast<size_t>(type)];
  }
  inline std::shared_ptr<prometheus::Registry> registry() const { return registry_; }

protected:
  virtual void _trajectory_req(const ingest::Request<> &req) override;

private:
  std::shared_ptr<prometheus::Registry> registry_;
  MetricsAggregator(); // Singelton only accessible from friend function
  common::ThreadSafeQueue<std::shared_ptr<trajectory::Trajectory>> intake_;
  std::vector<std::string> plant_classes_;
  std::unordered_map<std::string, size_t> plant_class_map_;
  std::thread metrics_report_thread_;
  std::unique_ptr<RollingConclusionCounter> conclusion_counter_;
  metrics::PlantConclusionCounter plant_conclusion_counter_;
  std::atomic<float> scheduler_frame_time_;
  std::unordered_map<uint32_t, std::vector<std::atomic<uint32_t>>> scheduling_failures_;

  void report_loop();
};
} // namespace metrics
} // namespace carbon