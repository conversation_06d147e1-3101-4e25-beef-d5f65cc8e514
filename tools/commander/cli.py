import argparse
import asyncio
import json
import os
from argparse import Argument<PERSON>arser
from collections import defaultdict
from datetime import datetime
from typing import Dict, Iterable, List

from exitstatus import ExitStatus
from google.protobuf.json_format import MessageToJson, Parse
from prettytable import PrettyTable

import generated.frontend.proto.alarm_pb2 as alarm_pb
import generated.frontend.proto.banding_pb2 as banding_pb
import generated.frontend.proto.camera_pb2 as camera_pb
import generated.frontend.proto.crosshair_pb2 as crosshair_pb
import generated.frontend.proto.dashboard_pb2 as dashboard_pb
import generated.frontend.proto.data_capture_pb2 as data_capture_pb
import generated.frontend.proto.focus_pb2 as focus_pb
import generated.frontend.proto.laser_pb2 as laser_pb
import generated.frontend.proto.model_pb2 as model_pb
import generated.frontend.proto.software_pb2 as software_pb
import generated.frontend.proto.status_bar_pb2 as status_bar_pb
import generated.frontend.proto.weeding_diagnostics_pb2 as weeding_diagnostics_pb
import generated.proto.almanac.almanac_pb2 as almanac_base_pb
import generated.proto.metrics.metrics_pb2 as metrics_pb2
import generated.weed_tracking.proto.weed_tracking_pb2 as weed_tracking_pb
import lib.common.logging
from lib.common.commander.client import CommanderClient
from lib.common.redis_client import RedisClient
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time.time import iso8601_timestamp, maka_control_timestamp_ms
from metrics.python.spatial.spatial_block_generator import BLOCKS_HASH
from tools.robot_definition.builder import Builder as RobotDefinitionBuilder

LOG = lib.common.logging.get_logger(__name__)


def build_service_summary(services: Iterable[status_bar_pb.ServiceStatus]) -> str:
    return ", ".join(
        [service.name + " " + status_bar_pb.StatusLevel.Name(service.status_level) for service in services]
    )


# Status Bar
def print_status(status: status_bar_pb.StatusBarMessage) -> None:
    print("Status: ")
    print(f"ts: {status.ts.timestamp_ms}")
    print(f"LasersEnabled: {status.lasers_enabled}")
    print(f"WeedingEnabled: {status.weeding_enabled}")
    print(f"StatusLevel: {status_bar_pb.Status.Name(status.status_level)}")
    print(f"StatusMessage: {status.status_message}")
    print(f"Translated status message: {MessageToJson(status.translated_status_message)}")
    print(
        f"CommandStatus: {status_bar_pb.StatusLevel.Name(status.command_status.status_level)} ({build_service_summary(status.command_status.service_status)})"
    )
    for row_index in status.row_status:
        row = status.row_status[row_index]
        print(
            f"Row {row_index} Status: {status_bar_pb.StatusLevel.Name(row.status_level)} ({build_service_summary(row.service_status)})"
        )


async def GetNextStatus(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextStatus(args.ts)
    print_status(reply)


async def GetNextStatusSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextStatus(ts)
        print_status(reply)
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


async def ReportIssue(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ReportIssue(args.description, args.phone)


async def GetSupportPhone(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetSupportPhone()
    print(f"Support Phone: {reply.support_phone}")


# Dashboard
async def ToggleRow(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ToggleRow(row_id=args.r)


async def ToggleLasers(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ToggleLasers()


def print_dashboard_state(state: dashboard_pb.DashboardStateMessage) -> None:
    print("Dashboard State")
    print(f"ts: {state.ts}")
    print(f"Debug Mode: {state.debug_mode}")
    print(f"Lasers Enabled: {state.lasers_enabled}")
    print(f"Rows Enabled: {[x for x in state.row_enabled]}")
    print(f"Extra Statuses: {state.extras}")
    print(f"Selected Model: {state.selected_model}")
    print(f"Row width: {state.row_width_in} inches")
    print(f"Implement state: {dashboard_pb.ImplementState.Name(state.implement_state)}")
    print(f"Efficiency enabled: {state.efficiency_enabled}")
    print(f"Efficiency percent: {state.efficiency_percent.percent}")
    print(f"Error rate enabled: {state.error_rate_enabled}")
    print(f"Error percent: {state.error_rate.percent}")
    print(f"Area weeded today: {state.area_weeded_today.acres}")
    print(f"Area weeded total: {state.area_weeded_total.acres}")
    print(f"Time weeded today minutes: {state.time_weeded_today.minutes}")
    print(f"Time weeded total minutes: {state.time_weeded_total.minutes}")
    print(f"Weeds killed today: {state.weeds_killed_today.value}")
    print(f"Weeds killed total: {state.weeds_killed_total.value}")
    print(f"Cruise control enabled: {state.cruise_enabled}")
    print(f"Cruise control allowed: {state.cruise_allow_enable}")
    print(f"Row States: {state.row_states}")


async def GetNextDashboardState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextDashboardState(args.ts)
    print_dashboard_state(reply)


async def GetNextDashboardStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextDashboardState(ts)
        print_dashboard_state(reply)
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


async def GetCropModelOptions(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetCropModelOptions()
    for model in reply.models:
        has_model_str = "" if not model.has_model else " - Model available"
        preferred_str = "" if not model.preferred else " - Preferred"
        print(f"Crop: {model.crop}" + has_model_str + preferred_str)


async def SetCropModel(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetCropModel(crop=args.crop)


async def ListEnabledCrops(client: CommanderClient, args: argparse.Namespace) -> None:
    enabled_crops = await client.ListEnabledCrops(args.lang)
    for crop in enabled_crops.enabledCrops:
        print(f"crop: {crop}")


async def ListCaptureCrops(client: CommanderClient, args: argparse.Namespace) -> None:
    enabled_crops = await client.ListCaptureCrops(args.lang)
    for crop in enabled_crops.enabledCrops:
        print(f"crop: {crop}")


async def GetNextEnabledCrops(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextEnabledCrops(args.ts, args.lang)
    for crop in resp.enabledCrops:
        print(f"crop: {crop}")
    print(f"ts: {resp.ts.timestamp_ms}")


async def GetNextEnabledCropsSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        resp = await client.GetNextEnabledCrops(ts, args.lang)
        for crop in resp.enabledCrops:
            print(f"crop: {crop}")
        print(f"ts: {resp.ts.timestamp_ms}")
        print("--------------------------------")
        ts = resp.ts.timestamp_ms


async def GetNextCaptureCrops(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextCaptureCrops(args.ts, args.lang)
    for crop in resp.enabledCrops:
        print(f"crop: {crop}")
    print(f"ts: {resp.ts.timestamp_ms}")


async def GetNextCaptureCropsSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        resp = await client.GetNextCaptureCrops(ts, args.lang)
        for crop in resp.enabledCrops:
            print(f"crop: {crop}")
        print(f"ts: {resp.ts.timestamp_ms}")
        print("--------------------------------")
        ts = resp.ts.timestamp_ms


async def GetNextSelectedCropID(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextSelectedCropID(args.ts)
    print(f"{resp.crop_id}")


async def RefreshDefaultModelParameters(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.RefreshDefaultModelParameters(crop_id=args.crop_id, model_id=args.model_id)


async def SelectCrop(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SelectCrop(crop_id=args.crop_id)


async def TriggerDownload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.TriggerDownload()


async def DownloadModel(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.DownloadModel(model_id=args.model_id)


async def SyncCropIDs(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SyncCropIDs(args.force_cache_refresh)


async def GetNextModelNicknames(client: CommanderClient, args: argparse.Namespace) -> None:
    model_id_list: List[str] = args.model_ids.split(",")
    response = await client.GetNextModelNicknames(model_ids=model_id_list, ts=args.ts)
    print(f"{response.ts.timestamp_ms} - {response.model_nicknames}")


async def GetModelNicknames(client: CommanderClient, args: argparse.Namespace) -> None:
    model_id_list: List[str] = args.model_ids.split(",")
    response = await client.GetModelNicknames(model_ids=model_id_list)
    print(response.model_nicknames)


async def SetModelNickname(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetModelNickname(model_id=args.model_id, model_nickname=args.model_nickname)


def print_model_history(history: model_pb.ModelHistoryResponse) -> None:
    for event in history.events:
        print(MessageToJson(event))


async def GetNextModelHistory(client: CommanderClient, args: argparse.Namespace) -> None:
    response = await client.GetNextModelHistory(
        start_timestamp=args.start_timestamp,
        count=args.count,
        reverse=args.reverse,
        match_filter=args.match_filter,
        event_type_matcher=args.event_type_matcher,
        ts=args.ts,
    )
    print(f"ts: {response.ts.timestamp_ms}")
    print_model_history(response)


async def GetModelHistory(client: CommanderClient, args: argparse.Namespace) -> None:
    response = await client.GetModelHistory(
        start_timestamp=args.start_timestamp,
        count=args.count,
        reverse=args.reverse,
        match_filter=args.match_filter,
        event_type_matcher=args.event_type_matcher,
    )
    print_model_history(response)


def print_velocity_state(state: dashboard_pb.WeedingVelocity) -> None:
    print("Weeding Velocity State")
    print(f"ts: {state.ts}")
    print(f"Current: {state.current_velocity_mph} mph")
    print(f"Target: {state.target_velocity_mph} mph")
    print(f"Tolerance: {state.tolerance_mph} mph")


async def GetNextWeedingVelocity(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextWeedingVelocity(args.ts)
    print_velocity_state(reply)


async def GetNextWeedingVelocitySub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextWeedingVelocity(ts)
        print_velocity_state(reply)
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


async def SetCruiseEnabled(client: CommanderClient, args: argparse.Namespace) -> None:
    print("Enabling cruise control")
    await client.SetCruiseEnabled(True)


async def SetCruiseDisabled(client: CommanderClient, args: argparse.Namespace) -> None:
    print("Disabling cruise control")
    await client.SetCruiseEnabled(False)


# Actuation
async def GetNextActuationState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextActuationTasks(args.ts)
    print(reply)


async def StartLaserTest(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartLaserTest(args.row, args.scanner, args.duration_ms)


async def StartImageDraw(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartImageDraw(args.row, args.scanner, args.speed_mmps)


async def CancelActuationTask(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.CancelActuationTask()


# Lasers
async def FireLaser(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.FireLaser(cam_id=args.cam, duration_ms=args.duration)


def print_laser_state(state: laser_pb.LaserStateList) -> None:
    re_ordered: Dict[int, Dict[int, laser_pb.LaserState]] = defaultdict(dict)
    for laser in state.lasers:
        re_ordered[laser.laser_descriptor.row_number][laser.laser_descriptor.laser_id] = laser
    t = PrettyTable(
        [
            "Row",
            "Laser",
            "Enabled",
            "Firing",
            "Error",
            "TargetTrajectoryID",
            "TotalFireCount",
            "TotalFireTime (s)",
            "Current",
            "DeltaT",
            "Lifetime (s)",
            "Power Level",
            "Installed At",
            "Serial",
        ]
    )
    print("Laser State")
    print(f"ts: {state.ts}")
    for laser_row in sorted(re_ordered.keys()):
        for laser_id in sorted(re_ordered[laser_row].keys()):
            laser = re_ordered[laser_row][laser_id]
            t.add_row(
                [
                    laser_row,
                    laser_id,
                    laser.enabled,
                    laser.firing,
                    laser.error,
                    laser.target_trajectory_id,
                    laser.total_fire_count,
                    laser.total_fire_time_ms / 1000,
                    laser.current,
                    laser.delta_temp,
                    laser.lifetime_sec,
                    laser.power_level,
                    laser.installed_at,
                    laser.laser_descriptor.serial,
                ]
            )
    t.align = "r"
    print(t)


async def GetNextLaserState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextLaserState(args.ts)
    print_laser_state(reply)


async def GetNextLaserStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextLaserState(ts)
        print_laser_state(reply)
        ts = reply.ts.timestamp_ms


async def ToggleLaserEnabled(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ToggleLaserEnabled(row_id=args.row, laser_id=args.laser)


async def EnableRow(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.EnableRow(row_id=args.row)


async def DisableRow(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.DisableRow(row_id=args.row)


async def ResetLaserMetrics(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ResetLaserMetrics(row_id=args.row, laser_id=args.laser, serial=args.serial)


async def FixLaserMetrics(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.FixLaserMetrics(
        row_id=args.row,
        laser_id=args.laser,
        serial=args.serial,
        total_fire_count=args.fire_count,
        total_fire_time_ms=args.fire_time_ms,
        lifetime_sec=args.lifetime_sec,
    )


# Focus
async def TogglePredictGridView(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.TogglePredictGridView()


def print_focus_state(state: focus_pb.FocusState) -> None:
    print("Focus State")
    print(f"ts: {state.ts}")
    print(f"Grid View: {state.grid_view_enabled}")
    print(f"Global Focus In Progress: {state.focus_in_progress}")
    print(f"Global Focus Progress: {state.global_focus_progress_pct}")
    if state.HasField("target"):
        print(f"Lens Value: {state.target.liquid_lens_value}")
        print(f"Min Lens Value: {state.target.min_lens_value}")
        print(f"Max Lens Value: {state.target.max_lens_value}")
        print(f"Focus In Progress: {state.target.focus_in_progress}")
        print(f"Focus Progress: {state.target.focus_progress_pct}")


async def GetNextFocusState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextFocusState(cam_id=args.cam, timestamp_ms=args.ts)
    print_focus_state(reply)


async def GetNextFocusStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextFocusState(cam_id=args.cam, timestamp_ms=ts)
        print_focus_state(reply)
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


async def StartAutoFocusSpecific(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartAutoFocusSpecific(args.cam)


async def StartAutoFocusAll(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartAutoFocusAll()


async def StopAutoFocus(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StopAutoFocus()


async def SetLensValue(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetLensValue(args.cam, args.val)


# Cameras
async def GetCameraList(client: CommanderClient, args: argparse.Namespace) -> None:
    cameras = await client.GetCameraList(args.type)
    print(cameras)
    for cam in cameras.cameras:
        print(
            f"Cam: {cam.camera_id}, Row: {cam.row_number}, Focusable: {cam.auto_focusable}, Type: {camera_pb.CameraType.Name(cam.type)}, Connected: {cam.connected}"
        )


async def GetNextCameraList(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextCameraList(args.type, ts)
        for cam in reply.cameras:
            print(
                f"Cam: {cam.camera_id}, Row: {cam.row_number}, Focusable: {cam.auto_focusable}, Type: {camera_pb.CameraType.Name(cam.type)}, Connected: {cam.connected}"
            )
        ts = reply.ts.timestamp_ms


# Crosshair
async def StartAutoCalibrateCrosshair(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartAutoCalibrateCrosshair(args.cam)


async def StartAutoCalibrateAllCrosshairs(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartAutoCalibrateAllCrosshairs()


async def StopAutoCalibrate(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StopAutoCalibrate()


def print_crosshair_state(state: crosshair_pb.CrosshairPositionState) -> None:
    print("Crosshair State")
    print(f"ts: {state.ts}")
    print(f"X: {state.pos.x}, Y: {state.pos.y}")
    print(f"Calibrating: {state.calibrating}")


async def GetNextCrosshairState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextCrosshairState(cam_id=args.cam, timestamp_ms=args.ts)
    print_crosshair_state(reply)


async def GetNextAutoCrossHairCalState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextAutoCrossHairCalState(timestamp_ms=args.ts)
    print(reply)


async def GetNextCrosshairStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextCrosshairState(cam_id=args.cam, timestamp_ms=ts)
        print_crosshair_state(reply)
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


async def SetCrosshairPosition(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetCrosshairPosition(args.cam, args.x, args.y)


async def MoveScanner(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.MoveScanner(args.cam, args.x, args.y)


# Data Capture
async def StartDataCapture(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartDataCapture(
        rate=args.rate, session=args.session, crop=args.crop, crop_id=args.crop_id, snap_capture=args.snap_capture
    )


async def PauseDataCapture(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.PauseDataCapture()


async def ResumeDataCapture(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ResumeDataCapture()


async def CompleteDataCapture(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.CompleteDataCapture()


async def StopDataCapture(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StopDataCapture()


async def StartDataCaptureWirelessUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartDataCaptureWirelessUpload()


async def StartDataCaptureUSBUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartDataCaptureUSBUpload()


async def StopDataCaptureUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StopDataCaptureUpload()


async def PauseDataCaptureUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.PauseDataCaptureUpload()


async def ResumeDataCaptureUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ResumeDataCaptureUpload()


async def StartBackgroundDataCaptureWirelessUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartBackgroundDataCaptureWirelessUpload(name=args.name)


async def StartBackgroundDataCaptureUSBUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartBackgroundDataCaptureUSBUpload(name=args.name)


async def StopBackgroundDataCaptureUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StopBackgroundDataCaptureUpload(name=args.name)


async def PauseBackgroundDataCaptureUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.PauseBackgroundDataCaptureUpload(name=args.name)


async def ResumeBackgroundDataCaptureUpload(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ResumeBackgroundDataCaptureUpload(name=args.name)


async def SnapImages(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SnapImages(args.crop, args.crop_id, args.session_name)


async def SnapImage(client: CommanderClient, args: argparse.Namespace) -> None:
    session_name = args.session_name
    if session_name is None:
        session_name = maka_control_timestamp_ms()
    await client.SnapImage(args.crop, args.crop_id, args.cam_id, args.timestamp_ms, session_name)
    print(f"Session name: {session_name}")


async def GetSessions(client: CommanderClient, args: argparse.Namespace) -> None:
    sessions = await client.GetSessions()
    print("Sessions available for upload:\n")
    for session in sessions.sessions:
        print(f"\t{session.name}")
        print(f"\tNumber of images: {session.images_remaining}")
        print(f"\tIs Capturing: {session.is_capturing}")
        print(f"\tIs Uploading: {session.is_uploading}")
        print(f"\tHas Completed: {session.has_completed}\n")
    print("--------------------------------")


async def GetRegularCaptureStatus(client: CommanderClient, args: argparse.Namespace) -> None:
    status = await client.GetRegularCaptureStatus()
    print("Regular Capture Status:")
    print(f"\tUploaded in last hour: {status.uploaded}")
    print(f"\tBudget per hour {status.budget}")
    print(f"\tTimestamp of last upload (utc) {datetime.fromtimestamp(status.last_upload_timestamp / 1000)}")


def print_data_capture_state(state: data_capture_pb.DataCaptureState) -> None:
    print("Data Capture State")
    print(f"ts: {state.ts}")
    print(f"Session: {state.session_name}, Rate: {state.rate}, Step: {data_capture_pb.ProcedureStep.Name(state.step)}")
    print(
        f"Images Taken: {state.images_taken}, Target: {state.target_images_taken}, Ratio: {state.images_taken / state.target_images_taken}, Time Estimate: {state.estimated_capture_remaining_time_ms}"
    )
    print(
        f"Images Uploaded: {state.images_uploaded}, Target: {state.target_images_uploaded}, Ratio: {state.images_uploaded / state.target_images_uploaded}, Time Estimate: {state.estimated_upload_remaining_time_ms}"
    )
    print(f"Availability: Wireless: {state.wireless_upload_available}, USB: {state.usb_storage_connected}")
    print(f"Capture Status: {state.capture_status}, Upload Status: {state.upload_status}")
    print(f"Error Message: {state.error_message}")


async def GetNextDataCaptureState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextDataCaptureState(timestamp_ms=args.ts)
    print_data_capture_state(reply)


async def GetNextDataCaptureStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextDataCaptureState(timestamp_ms=ts)
        print_data_capture_state(reply)
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


# Alarms
def print_alarm_table(state: alarm_pb.AlarmTable) -> None:
    print("Alarm Table")
    print(f"ts: {state.ts}")
    for row in state.alarms:
        print(
            f"{row.timestamp_ms} | {row.alarm_code} | {row.description} | {row.level} | {row.impact} | {row.identifier} | {row.acknowledged}"
        )


async def GetNextAlarmList(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextAlarmList(timestamp_ms=args.ts)
    print_alarm_table(reply)


async def GetNextAlarmListSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextAlarmList(timestamp_ms=ts)
        print_alarm_table(reply)
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


async def GetNextNewAlarmList(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextNewAlarmList(timestamp_ms=args.ts)
    print_alarm_table(reply)


async def GetNextNewAlarmListSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextNewAlarmList(timestamp_ms=ts)
        print_alarm_table(reply)
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


async def GetNextAlarmCount(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextAlarmCount(timestamp_ms=args.ts)
    print(f"Count: {reply.count}")


async def GetNextAlarmCountSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextAlarmCount(timestamp_ms=ts)
        print(f"Count: {reply.count}")
        ts = reply.ts.timestamp_ms


async def AcknowledgeAlarm(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.AcknowledgeAlarm(identifier=args.id)


async def ResetAlarms(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ResetAlarms()


async def GetNextAlarmLogCount(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextAlarmLogCount(args.v)
    print(MessageToJson(resp))


async def GetNextAlarmLog(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextAlarmLog(f=args.f, t=args.t, visible_only=args.v)
    print(MessageToJson(resp))


async def AutofixAlarm(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.AutofixAlarm(alarmid=args.a)


async def GetAutofixStatus(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetAutofixStatus()
    print(MessageToJson(resp))


async def PinModel(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.PinModel(args.id, args.crop_id, args.p2p)


async def UpdateModel(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.UpdateModel()


async def UnpinModel(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.UnpinModel(args.crop_id, args.p2p)


def print_models(model_state: model_pb.GetNextModelStateResponse, unsynced_only: bool = False) -> None:
    model_list = list(model_state.models)
    model_list = sorted(model_list, key=lambda x: (x.type, x.crop, x.ts.timestamp_ms))
    print(f"Current Deepweed Model: {model_state.current_deepweed_model_id}")
    print(f"Current P2P Model: {model_state.current_p2p_model_id}")
    print()
    for model in model_list:
        tags = []
        if unsynced_only and model.synced:
            continue
        if model.active:
            tags.append("Active")
        if model.recommended:
            tags.append("Recommended")
        if model.custom:
            tags.append("Custom")
        if model.pinned:
            tags.append("Pinned")
        if model.synced:
            tags.append("Synced")
        if model.maintained:
            tags.append("Maintained")
        if model.downloading:
            tags.append(
                f"Downloading {model.downloading_progress * 100}% ({model.estimated_downloading_remaining_time_ms} ms)"
            )
        else:
            tags.append("Downloaded")
        if not model.synced and not all(model.synced_to_rows):
            tags.append(
                "Synced to (" + ", ".join([str(i) for i, synced in enumerate(model.synced_to_rows) if synced]) + ")"
            )
        tag_str = ", ".join(tags)
        print(model.id)
        if len(model.nickname) > 0:
            print(f"    Nickname: {model.nickname}")
        print(f"    Type: {model.type}")
        if len(model.crop) > 0:
            print(f"    Crop: {model.crop}")
        print(f"    ViableCropIds: {model.viable_crop_ids}")
        print(f"    Tags: {tag_str}")
        print(f"    Trained At Timestamp: {iso8601_timestamp(seconds=model.ts.timestamp_ms/1000)}")
        print(f"    Downloaded At Timestamp: {iso8601_timestamp(seconds=model.downloaded_timestamp.timestamp_ms/1000)}")
        if model.last_used_timestamp.timestamp_ms != 0:
            print(f"    Last Used Timestamp: {iso8601_timestamp(seconds=model.last_used_timestamp.timestamp_ms/1000)}")
        print()


async def GetNextModelState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextModelState(args.crop, args.crop_id, args.ts)
    print_models(reply)


async def GetNextModelStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextModelState(args.crop, args.crop_id, ts)
        print_models(reply)
        ts = reply.ts.timestamp_ms


async def GetNextAllModelState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextAllModelState(args.crop, args.crop_id, args.ts)
    print_models(reply, args.unsynced_only)


async def GetNextAllModelStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextAllModelState(args.crop, args.crop_id, ts)
        print_models(reply, args.unsynced_only)
        ts = reply.ts.timestamp_ms


def print_software(software_state: software_pb.SoftwareVersionState) -> None:
    print("-----------------------------------------")
    print(
        f"Current: {software_state.current.tag}, Ready: {software_state.current.ready}, Available: {software_state.current.available}"
    )
    print(
        f"Target: {software_state.target.tag}, Ready: {software_state.target.ready}, Available: {software_state.target.available}"
    )
    print(
        f"Previous: {software_state.previous.tag}, Ready: {software_state.previous.ready}, Available: {software_state.previous.available}"
    )
    print(f"Updating: {software_state.updating}")
    print(f"Version mismatch: {software_state.version_mismatch}")
    print("-----------------------------------------")
    for host_state in software_state.host_states:
        print(f"Host: {host_state.host_name}, ID: {host_state.host_id}")
        print(f"Active: {host_state.active}")
        print(f"Updating: {host_state.updating}")
        print(
            f"Current: {host_state.current.tag}, Ready: {host_state.current.ready}, Available: {host_state.current.available}"
        )
        print(
            f"Target: {host_state.target.tag}, Ready: {host_state.target.ready}, Available: {host_state.target.available}"
        )
        print(
            f"Previous: {host_state.previous.tag}, Ready: {host_state.previous.ready}, Available: {host_state.previous.available}"
        )
        print("-----------------------------------------")


async def GetNextSoftwareState(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.GetNextSoftwareState(args.ts, args.verbose)
    print_software(reply)


async def GetNextSoftwareStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextSoftwareState(ts, args.verbose)
        print_software(reply)
        ts = reply.ts.timestamp_ms


async def UpdateSoftware(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.UpdateSoftware()


async def RevertSoftware(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.RevertSoftware()


async def FixVersionMismatch(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.FixVersionMismatch()


async def UpdateHostSoftware(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.UpdateHostSoftware(host_id=args.id)


async def LoadBandingDefs(client: CommanderClient, args: argparse.Namespace) -> None:
    reply = await client.LoadBandingDefs()
    if args.f is None:
        print(MessageToJson(reply))
    else:
        dd = os.getenv("MAKA_DATA_DIR", "/data")
        fname = "/data" + args.f[len(dd) :] if args.f.startswith(dd) else dd + "/" + args.f
        with open(fname, "w") as f:
            f.write(MessageToJson(reply))


async def SaveBandingDef(client: CommanderClient, args: argparse.Namespace) -> None:
    if args.f is None:
        def_ = banding_pb.BandingDef(
            name="Field01",
            rows=[
                banding_pb.BandingRow(
                    row_id=1,
                    bands=[
                        weed_tracking_pb.BandDefinition(offset_mm=10.0, width_mm=5.0),
                        weed_tracking_pb.BandDefinition(offset_mm=70.0, width_mm=5.0),
                    ],
                ),
                banding_pb.BandingRow(
                    row_id=2,
                    bands=[
                        weed_tracking_pb.BandDefinition(offset_mm=10.0, width_mm=5.0),
                        weed_tracking_pb.BandDefinition(offset_mm=70.0, width_mm=5.0),
                    ],
                ),
                banding_pb.BandingRow(
                    row_id=3,
                    bands=[
                        weed_tracking_pb.BandDefinition(offset_mm=10.0, width_mm=5.0),
                        weed_tracking_pb.BandDefinition(offset_mm=70.0, width_mm=5.0),
                    ],
                ),
            ],
        )
        print(
            "ERROR: No filename specified, please create a banding definition JSON file under /data/carbon/data folder and run again."
        )
        print("Example filename content:")
        print(MessageToJson(def_))
        return

    dd = os.getenv("MAKA_DATA_DIR", "/data")
    fname = "/data" + args.f[len(dd) :] if args.f.startswith(dd) else dd + "/" + args.f
    with open(fname, "r") as f:
        s = f.read()
    def_ = Parse(s, banding_pb.BandingDef())
    req = banding_pb.SaveBandingDefRequest(bandingDef=def_, setActive=args.a)
    await client.SaveBandingDef(req)


async def SetActiveBandingDef(client: CommanderClient, args: argparse.Namespace) -> None:
    req = banding_pb.SetActiveBandingDefRequest(name=args.name, uuid=args.uuid)
    await client.SetActiveBandingDef(req)


async def DeleteBandingDef(client: CommanderClient, args: argparse.Namespace) -> None:
    req = banding_pb.DeleteBandingDefRequest(name=args.name, uuid=args.uuid)
    await client.DeleteBandingDef(req)


async def GetActiveBandingDef(client: CommanderClient, args: argparse.Namespace) -> None:
    res = await client.GetActiveBandingDef()
    print(MessageToJson(res))


async def GetVisualizationData(client: CommanderClient, args: argparse.Namespace) -> None:
    res = await client.GetVisualizationData(timestamp_ms=args.ts, row_id=args.r)
    print(MessageToJson(res))


async def GetVisualizationDataForAllRows(client: CommanderClient, args: argparse.Namespace) -> None:
    res = await client.GetVisualizationDataForAllRows()
    print(MessageToJson(res))


async def GetVisualizationMetadata(client: CommanderClient, args: argparse.Namespace) -> None:
    res = await client.GetVisualizationMetadata()
    print(MessageToJson(res))


async def GetDimensions(client: CommanderClient, args: argparse.Namespace) -> None:
    res = await client.GetDimensions(args.r)
    print(MessageToJson(res))


async def SetBandingEnabled(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetBandingEnabled(args.e == 1)


async def IsBandingEnabled(client: CommanderClient, args: argparse.Namespace) -> None:
    res = await client.IsBandingEnabled()
    print(MessageToJson(res))


async def SetDynamicBandingEnabled(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetDynamicBandingEnabled(args.e == 1)


async def IsDynamicBandingEnabled(client: CommanderClient, args: argparse.Namespace) -> None:
    res = await client.IsDynamicBandingEnabled()
    print(MessageToJson(res))


async def GetNextBandingState(client: CommanderClient, args: argparse.Namespace) -> None:
    res = await client.GetNextBandingState(timestamp_ms=args.ts)
    print(MessageToJson(res))


async def GetNextBandingStateSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        reply = await client.GetNextBandingState(ts)
        print(MessageToJson(reply))
        print("--------------------------------")
        ts = reply.ts.timestamp_ms


# debug
async def SetLogging(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetLogging(args.l.upper(), args.c, args.r)


async def StartSavingCropLineDetectionReplay(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartSavingCropLineDetectionReplay(args.f, args.t)


async def RecordWeedingDiagnostics(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.RecordWeedingDiagnostics(args.n, args.t, args.ci, args.wi)


async def SnapshotPredictImages(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.SnapshotPredictImages(args.row_id)
    print(MessageToJson(resp))


async def GetChipForPredictImage(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetChipForPredictImage(
        args.row_id, args.pcam_id, args.timestamp_ms, args.center_x_px, args.center_y_px
    )
    with open(args.output_file, "wb") as f:
        f.write(resp.chip_image)
    print(f"Chip image saved to {args.output_file}")


async def GetNextActiveCategoryCollectionId(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextActiveCategoryCollectionId()
    print(MessageToJson(resp))


async def GetNextCategoryCollectionsData(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextCategoryCollectionsData()
    print(MessageToJson(resp))


async def GetNextCategoryData(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextCategoryData()
    print(MessageToJson(resp))


async def ReloadCategoryCollection(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ReloadCategoryCollection()


async def DisplayActiveCollectionData(client: CommanderClient, args: argparse.Namespace) -> None:
    active_collection_id = await client.GetNextActiveCategoryCollectionId()
    collections = await client.GetNextCategoryCollectionsData()
    active_collection = None
    for collection in collections.category_collections:
        if collection.id == active_collection_id.uuid:
            active_collection = collection
            break

    if active_collection is None:
        print("No active collection")
        return

    categories = await client.GetNextCategoryData()
    active_categories = []
    for category in active_collection.category_ids:
        for category_data in categories.categories:
            if category_data.id == category:
                active_categories.append(category_data)
                break

    print("-----------------------------------------")
    print("Active Category Collection:")
    print(MessageToJson(active_collection))
    print(
        "Last updated timestamp: "
        + datetime.fromtimestamp(active_collection_id.last_updated_timestamp_ms / 1000).strftime("%Y-%m-%d %H:%M:%S")
    )
    print("CV reload required: " + str(active_collection_id.reload_required))
    print("-----------------------------------------")

    downloaded_chips = await client.GetDownloadedChipIds()
    synced_chips = await client.GetSyncedChipIds()

    print("\nAssociated Categories:")
    for category_data in active_categories:
        print("  id:        " + category_data.id)
        print('  name:      "' + category_data.name + '"')
        print("  protected: " + str(category_data.protected))
        print("  chips: (" + str(len(category_data.chip_ids)) + ")")
        for chip_id in category_data.chip_ids:
            chip_display_str = "    " + chip_id
            if chip_id in downloaded_chips.chip_ids:
                chip_display_str += " (downloaded"
                if chip_id in synced_chips.chip_ids:
                    chip_display_str += " and synced"
                else:
                    chip_display_str += " but NOT SYNCED TO ALL ROWS"
                chip_display_str += ")"
            else:
                chip_display_str += " (NOT DOWNLOADED TO COMMANDER)"

            print(chip_display_str)
        print()


async def GetChipMetadata(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetChipMetadata()
    print(MessageToJson(resp))


async def GetDownloadedChipIds(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetDownloadedChipIds()
    print(MessageToJson(resp))


async def GetSyncedChipIds(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetSyncedChipIds()
    print(MessageToJson(resp))


async def SaveAlmanac(client: CommanderClient, args: argparse.Namespace) -> None:
    categories = [
        almanac_base_pb.AlmanacTypeCategory(
            type=almanac_base_pb.TypeCategory(category="DEFAULT"),
            sizes=[1, 2],
            formulas=[
                almanac_base_pb.Formula(exponent=1, multiplier=1, offset=1, fine_tune_multiplier=1, max_time=1),
                almanac_base_pb.Formula(exponent=2, multiplier=2, offset=2, fine_tune_multiplier=2, max_time=2),
                almanac_base_pb.Formula(exponent=3, multiplier=3, offset=3, fine_tune_multiplier=3, max_time=3),
            ],
        )
    ]
    cfg = almanac_base_pb.AlmanacConfig(id=args.i, name=str(args.i), categories=categories,)
    await client.SaveAlmanacConfig(cfg, False)


async def DeleteAlmanac(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.DeleteAlmanacConfig(id=args.i)


async def LoadAlmanac(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.LoadAlmanacConfig(id=args.i)
    print(MessageToJson(resp))


async def GetConfigData(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetConfigData()
    print(MessageToJson(resp))


async def GetNextConfigData(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextConfigData(args.ts, args.lang)
    print(MessageToJson(resp))


async def GetNextConfigDataSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        resp = await client.GetNextConfigData(ts, args.lang)
        print(MessageToJson(resp))
        print("--------------------------------")
        ts = resp.ts.timestamp_ms


async def GetNextAlmanacConfig(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextAlmanacConfig()
    print(MessageToJson(resp))


async def LoadDiscriminator(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.LoadDiscriminatorConfig(id=args.i)
    print(MessageToJson(resp))


async def SaveDiscriminator(client: CommanderClient, args: argparse.Namespace) -> None:
    categories = [
        almanac_base_pb.DiscriminatorTypeCategory(
            type=almanac_base_pb.TypeCategory(category="DEFAULT"),
            trusts=[almanac_base_pb.Trust(), almanac_base_pb.Trust(), almanac_base_pb.Trust()],
        )
    ]
    cfg = almanac_base_pb.DiscriminatorConfig(id=args.i, name=args.i, categories=categories)
    await client.SaveDiscriminatorConfig(cfg, False)


async def LoadModelinator(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextModelinatorConfig()
    print(MessageToJson(resp))


async def SaveModelinator(client: CommanderClient, args: argparse.Namespace) -> None:
    categories = [
        almanac_base_pb.ModelinatorTypeCategory(
            type=almanac_base_pb.TypeCategory(category="PURSLANE"),
            trusts=[almanac_base_pb.ModelTrust(), almanac_base_pb.ModelTrust(), almanac_base_pb.ModelTrust()],
        )
    ]
    cfg = almanac_base_pb.ModelinatorConfig(model_id=args.m, crop_id=args.c, categories=categories)
    await client.SaveModelinatorConfig(cfg)


async def DeleteDiscriminator(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.DeleteDiscriminatorConfig(id=args.i)


async def GetDiagnosticsRecordingsList(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetDiagnosticsRecordingsList()
    print(MessageToJson(resp))


async def AddMockSpatialMetricsBlock(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.AddMockSpatialMetricsBlock()


async def ListSpatialBlocks(client: CommanderClient, args: argparse.Namespace) -> None:
    redis = await RedisClient.build(decode_responses=False)
    blocks_hash = await redis.hgetall(BLOCKS_HASH)
    result = {}
    for (binary_key, raw_proto) in blocks_hash.items():
        key = binary_key.decode("utf-8", errors="replace")
        block = metrics_pb2.SpatialMetricBlock()
        block.ParseFromString(raw_proto)
        result[key] = json.loads(MessageToJson(block))
    print(json.dumps(result, indent=2))


async def DeleteProfileSyncData(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.DeleteProfileSyncData()


async def OpenDiagnosticsRecording(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.OpenDiagnosticsRecording(args.n)
    print(MessageToJson(resp))


async def UploadDiagnosticsRecording(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.StartUpload(args.n)
    print(MessageToJson(resp))


async def GetUploadState(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetUploadState(args.n)
    print(MessageToJson(resp, True))


async def DeleteDiagnosticsRecording(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.DeleteDiagnosticsRecording(args.n)


async def GetRecordedSnapshot(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetRecordedSnapshot(args.n, args.r, args.s)
    printRecordedSnapshot(args, resp)


async def GetDeepweedPredictionsCount(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetDeepweedPredictionsCount(args.n, args.r, args.c)
    print(MessageToJson(resp))


async def GetDeepweedPredictions(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetDeepweedPredictions(args.n, args.r, args.c, args.i)
    print(MessageToJson(resp))


async def GetRotaryTicks(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetRotaryTicks(args.n, args.r)
    print(MessageToJson(resp))


async def GetRecordedTrajectoryData(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetRecordedTrajectoryData(args.n, args.r, args.t)
    print(MessageToJson(resp))


async def FindTrajectory(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.FindTrajectory(args.n, args.r, args.t)
    print(MessageToJson(resp))


async def GetRecordedTrajectoryPredictImage(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetRecordedTrajectoryPredictImage(args.n, args.r, args.t)
    with open(args.d, "wb") as f:
        for r in resp:
            f.write(r.image_chunk)


async def GetRecordedTrajectoryTargetImage(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetRecordedTrajectoryTargetImage(args.n, args.r, args.t, args.i)
    with open(args.d, "wb") as f:
        for r in resp:
            f.write(r.image_chunk)


async def GetPredictImageData(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetPredictImageData(args.n, args.r, args.i)
    print(MessageToJson(resp))


async def GetPredictImage(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetPredictImage(args.n, args.r, args.i)
    with open(args.d, "wb") as f:
        for r in resp:
            f.write(r.image_chunk)


def printRecordedSnapshot(args: argparse.Namespace, resp: weeding_diagnostics_pb.GetSnapshotResponse) -> None:
    if not args.a and resp.snapshot is not None and resp.snapshot.trajectories is not None:
        print(f"Timestamp: {resp.snapshot.timestamp_ms}, {len(resp.snapshot.trajectories)} trajectories")
    else:
        print(MessageToJson(resp))


async def GetCurrentTrajectories(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetCurrentTrajectories(row_id=args.r)
    print(MessageToJson(resp))


async def StartRecordingAimbotInputs(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartRecordingAimbotInputs(args.name, args.t, rotary_ticks=True, deepweed=True, lane_heights=True)


async def StartRecordingRotaryTicksLaneHeights(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartRecordingAimbotInputs(args.name, args.t, rotary_ticks=True, deepweed=False, lane_heights=True)


# Calibration
async def StartColorCalibration(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.StartColorCalibration(cam_id=args.cam_id)
    print(f"Color calibration values: red={resp.red} green={resp.green} blue={resp.blue}")


async def SaveColorCalibration(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SaveColorCalibration(cam_id=args.cam_id, red=args.red, green=args.green, blue=args.blue)


# Messages
async def SendMessage(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.SendMessage(args.message)
    print(resp)


async def GetNextMessages(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextMessages(args.ts)
    print(resp)


# Reporting
async def GetNextLocationHistory(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextLocationHistory(args.ts)
    print(resp)


# Features
async def GetNextFeatureFlags(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextFeatureFlags(timestamp_ms=args.ts)
    print(f"{resp.flags}")


async def GetRobotConfiguration(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetRobotConfiguration()
    print(MessageToJson(resp))


async def GetNextTasks(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextTasks(ts=args.ts)
    print(resp)


# Tasks
async def CreateJob(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.CreateJob(name=args.n, active=args.a, acreage=args.c)
    print(MessageToJson(resp))


async def UpdateJob(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.UpdateJob(id=args.i, name=args.n, ts=args.t, acreage=args.c)


async def GetActiveJobId(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetActiveJobId()
    print(MessageToJson(resp))


async def StopActiveJob(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StopActiveJob()


async def ListJobs(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextJobs(args.ts)
    print(MessageToJson(resp))


async def GetJob(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetJob(id=args.i, ts=args.ts)
    print(MessageToJson(resp))


async def DeleteJob(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.DeleteJob(id=args.i)
    print(MessageToJson(resp))


async def MarkJobCompleted(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.MarkJobCompleted(id=args.i)
    print(MessageToJson(resp))


async def StartJob(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.StartJob(id=args.i)


async def GetActiveJobMetrics(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetActiveJobMetrics()
    print(MessageToJson(resp))


async def StartPlantCaptcha(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.StartPlantCaptcha(args.n, args.m, args.c, args.cn)
    print(MessageToJson(resp))


async def GetPlantCaptchaStatus(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetPlantCaptchaStatus()
    print(MessageToJson(resp))


async def GetPlantCaptchaStatusSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        resp = await client.GetPlantCaptchaStatus(ts)
        print(MessageToJson(resp))
        print("--------------------------------")
        ts = resp.ts.timestamp_ms


async def GetPlantCaptchasList(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetPlantCaptchasList()
    print(MessageToJson(resp))


async def DeletePlantCaptcha(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.DeletePlantCaptcha(args.n)


async def GetPlantCaptcha(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetPlantCaptcha(args.n)
    print(MessageToJson(resp))


async def CancelPlantCaptcha(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.CancelPlantCaptcha()


async def UploadPlantCaptcha(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.UploadPlantCaptcha(args.n)


async def GetPlantCaptchaUploadState(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetPlantCaptchaUploadState(args.n)
    print(MessageToJson(resp))


async def SubmitPlantCaptchaResult(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SubmitPlantCaptchaResult(args.n, args.i, args.r)


async def GetPlantCaptchaResult(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetPlantCaptchaResult(args.n, args.i)
    print(MessageToJson(resp))


async def CalculatePlantCaptcha(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.CalculatePlantCaptcha(args.n)
    print(MessageToJson(resp))


async def GetOriginalModelinatorConfig(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetOriginalModelinatorConfig(args.n)
    print(MessageToJson(resp))


async def GetCaptchaRowStatus(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetCaptchaRowStatus()
    print(MessageToJson(resp, True))


async def CancelPlantCaptchaOnRow(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.CancelPlantCaptchaOnRow(args.r)


# Module Assignment
async def GetNextModuleList(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextModulesList(args.ts)
    print(MessageToJson(resp))


async def GetNextModulesListSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        resp = await client.GetNextModulesList(ts)
        print(MessageToJson(resp))
        print("--------------------------------")
        ts = resp.ts.timestamp_ms


async def GetNextActiveModules(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextActiveModules(args.ts)
    print(MessageToJson(resp))


async def GetNextActiveModulesSub(client: CommanderClient, args: argparse.Namespace) -> None:
    ts = 0
    while True:
        resp = await client.GetNextActiveModules(ts)
        print(MessageToJson(resp))
        print("--------------------------------")
        ts = resp.ts.timestamp_ms


async def IdentifyModule(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.IdentifyModule(args.serial)


async def AssignModule(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.AssignModule(int(args.id), args.serial)


async def ClearModuleAssignment(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.ClearModuleAssignment(args.serial)


async def SetModuleSerial(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetModuleSerial(args.placeholder, args.new_serial)


async def GetPresetsList(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetPresetsList()
    print(MessageToJson(resp))


async def GetCurrentRobotDefinition(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetCurrentRobotDefinition()
    print(MessageToJson(resp))


async def SetCurrentRobotDefinition(client: CommanderClient, args: argparse.Namespace) -> None:
    definition = RobotDefinitionBuilder().build()
    await client.SetCurrentRobotDefinition(definition)


async def GetNextReaperHardwareStatus(client: CommanderClient, args: argparse.Namespace) -> None:
    id = int(args.module_id) if args.module_id else None
    resp = await client.GetNextReaperHardwareStatus(args.ts, id)
    print(MessageToJson(resp))


async def GetNextReaperAllHardwareStatus(client: CommanderClient, args: argparse.Namespace) -> None:
    resp = await client.GetNextReaperAllHardwareStatus(args.ts)
    print(MessageToJson(resp))


async def GetNextTractorIfState(client: CommanderClient, args: argparse.Namespace) -> None:
    if args.watch:
        ts = args.ts
        while True:
            resp = await client.GetNextTractorIfState(ts)
            print(MessageToJson(resp))
            print("--------------------------------")
            ts = resp.ts.timestamp_ms
    else:
        resp = await client.GetNextTractorIfState(args.ts)
        print(MessageToJson(resp))


async def GetNextTractorSafetyState(client: CommanderClient, args: argparse.Namespace) -> None:
    if args.watch:
        ts = args.ts
        while True:
            resp = await client.GetNextTractorSafetyState(ts)
            print(MessageToJson(resp))
            print("--------------------------------")
            ts = resp.ts.timestamp_ms
    else:
        resp = await client.GetNextTractorSafetyState(args.ts)
        print(MessageToJson(resp))


async def SetEnforcementPolicy(client: CommanderClient, args: argparse.Namespace) -> None:
    await client.SetEnforcementPolicy(args.enforced)


async def GetNextCruiseControlState(client: CommanderClient, args: argparse.Namespace) -> None:
    if args.watch:
        ts = args.ts
        while True:
            resp = await client.GetNextCruiseControlState(ts)
            print(MessageToJson(resp))
            print("--------------------------------")
            ts = resp.ts.timestamp_ms
    else:
        resp = await client.GetNextCruiseControlState(args.ts)
        print(MessageToJson(resp))


async def main() -> ExitStatus:
    parser = ArgumentParser("Commander CLI client")
    subparsers = parser.add_subparsers(help="Commander Service", dest="service")
    subparsers.required = True

    # Status Bar
    status_bar_parser = subparsers.add_parser("status_bar", help="Status Bar Service")
    status_bar_subparsers = status_bar_parser.add_subparsers(help="Status Bar Command", dest="cmd", required=True)

    subparser = status_bar_subparsers.add_parser("GetNextStatus")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextStatus)

    subparser = status_bar_subparsers.add_parser("GetNextStatusSub")
    subparser.set_defaults(func=GetNextStatusSub)

    subparser = status_bar_subparsers.add_parser("ReportIssue")
    subparser.add_argument(
        "--description", type=str, help="Description of the Issue", default="Generic Issue Description"
    )
    subparser.add_argument("--phone", type=str, help="Operator phone number", default="")
    subparser.set_defaults(func=ReportIssue)

    subparser = status_bar_subparsers.add_parser("GetSupportPhone")
    subparser.set_defaults(func=GetSupportPhone)

    # Dashboard
    dashboard_parser = subparsers.add_parser("dashboard", help="Dashboard Service")
    dashboard_subparsers = dashboard_parser.add_subparsers(help="Dashboard Command", dest="cmd", required=True)

    subparser = dashboard_subparsers.add_parser("ToggleRow")
    subparser.add_argument("-r", type=int, help="Row Id", default=1)
    subparser.set_defaults(func=ToggleRow)

    subparser = dashboard_subparsers.add_parser("ToggleLasers")
    subparser.set_defaults(func=ToggleLasers)

    subparser = dashboard_subparsers.add_parser("GetNextDashboardState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextDashboardState)

    subparser = dashboard_subparsers.add_parser("GetNextDashboardStateSub")
    subparser.set_defaults(func=GetNextDashboardStateSub)

    subparser = dashboard_subparsers.add_parser("GetCropModelOptions")
    subparser.set_defaults(func=GetCropModelOptions)

    subparser = dashboard_subparsers.add_parser("SetCropModel")
    subparser.add_argument("--crop", type=str, help="Name of Crop", required=True)
    subparser.set_defaults(func=SetCropModel)

    subparser = dashboard_subparsers.add_parser("GetNextWeedingVelocity")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextWeedingVelocity)

    subparser = dashboard_subparsers.add_parser("GetNextWeedingVelocitySub")
    subparser.set_defaults(func=GetNextWeedingVelocitySub)

    subparser = dashboard_subparsers.add_parser("SetCruiseEnabled")
    subparser.set_defaults(func=SetCruiseEnabled)

    subparser = dashboard_subparsers.add_parser("SetCruiseDisabled")
    subparser.set_defaults(func=SetCruiseDisabled)

    # Actuation Tasks
    actuation_parser = subparsers.add_parser("actuation", help="Actuation Tasks Service")
    actuation_subparsers = actuation_parser.add_subparsers(help="Actuation Tasks", dest="cmd", required=True)

    subparser = actuation_subparsers.add_parser("StartLaserTest")
    subparser.add_argument("--row", type=int, help="Row Id", default=0)
    subparser.add_argument("--scanner", type=int, help="Scanner Id", default=0)
    subparser.add_argument("--duration_ms", type=int, help="duration in ms", default=300000)
    subparser.set_defaults(func=StartLaserTest)

    subparser = actuation_subparsers.add_parser("StartImageDraw")
    subparser.add_argument("--row", type=int, help="Row Id", default=0)
    subparser.add_argument("--scanner", type=int, help="Scanner Id", default=0)
    subparser.add_argument("--speed_mmps", type=int, help="Speed in mm per second", default=50)
    subparser.set_defaults(func=StartImageDraw)

    subparser = actuation_subparsers.add_parser("CancelActuationTask")
    subparser.set_defaults(func=CancelActuationTask)

    subparser = actuation_subparsers.add_parser("GetNextActuationState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextActuationState)

    # Lasers
    laser_parser = subparsers.add_parser("laser", help="Laser Service")
    laser_subparsers = laser_parser.add_subparsers(help="Laser Command", dest="cmd", required=True)

    subparser = laser_subparsers.add_parser("GetNextLaserState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextLaserState)

    subparser = laser_subparsers.add_parser("GetNextLaserStateSub")
    subparser.set_defaults(func=GetNextLaserStateSub)

    subparser = laser_subparsers.add_parser("ToggleLaserEnabled")
    subparser.add_argument("--row", type=int, help="Row Id", default=1)
    subparser.add_argument("--laser", type=int, help="Laser Id", default=1)
    subparser.set_defaults(func=ToggleLaserEnabled)

    subparser = laser_subparsers.add_parser("FireLaser")
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.add_argument("--duration", type=int, help="Duration in ms", default=1000)
    subparser.set_defaults(func=FireLaser)

    subparser = laser_subparsers.add_parser("EnableRow")
    subparser.add_argument("--row", type=int, help="Row Id", default=1)
    subparser.set_defaults(func=EnableRow)

    subparser = laser_subparsers.add_parser("DisableRow")
    subparser.add_argument("--row", type=int, help="Row Id", default=1)
    subparser.set_defaults(func=DisableRow)

    subparser = laser_subparsers.add_parser("ResetLaserMetrics")
    subparser.add_argument("--row", type=int, help="Row Id", required=True)
    subparser.add_argument("--laser", type=int, help="Laser Id", required=True)
    subparser.add_argument("--serial", type=str, help="Laser Serial number", required=True)
    subparser.set_defaults(func=ResetLaserMetrics)

    subparser = laser_subparsers.add_parser("FixLaserMetrics")
    subparser.add_argument("--row", type=int, help="Row Id", required=True)
    subparser.add_argument("--laser", type=int, help="Laser Id", required=True)
    subparser.add_argument("--serial", type=str, help="Laser Serial number", required=True)
    subparser.add_argument("--fire_count", type=int, help="Total Fire Count", required=True)
    subparser.add_argument("--fire_time_ms", type=int, help="Total Fire Time (ms)", required=True)
    subparser.add_argument("--lifetime_sec", type=int, help="Lifetime (sec)", required=True)
    subparser.set_defaults(func=FixLaserMetrics)

    # Focus
    focus_parser = subparsers.add_parser("focus", help="Focus Service")
    focus_subparsers = focus_parser.add_subparsers(help="Focus Command", dest="cmd", required=True)

    subparser = focus_subparsers.add_parser("GetNextFocusState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.set_defaults(func=GetNextFocusState)

    subparser = focus_subparsers.add_parser("GetNextFocusStateSub")
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.set_defaults(func=GetNextFocusStateSub)

    subparser = focus_subparsers.add_parser("TogglePredictGridView")
    subparser.set_defaults(func=TogglePredictGridView)

    subparser = focus_subparsers.add_parser("StartAutoFocusSpecific")
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.set_defaults(func=StartAutoFocusSpecific)

    subparser = focus_subparsers.add_parser("StartAutoFocusAll")
    subparser.set_defaults(func=StartAutoFocusAll)

    subparser = focus_subparsers.add_parser("StopAutoFocus")
    subparser.set_defaults(func=StopAutoFocus)

    subparser = focus_subparsers.add_parser("SetLensValue")
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.add_argument("--val", type=int, help="Lens Value", required=True)
    subparser.set_defaults(func=SetLensValue)

    # Cameras
    camera_parser = subparsers.add_parser("camera", help="Camera Service")
    camera_subparsers = camera_parser.add_subparsers(help="Camera Command", dest="cmd", required=True)

    subparser = camera_subparsers.add_parser("GetCameraList")
    subparser.add_argument("--type", type=str, choices=["target", "predict"], help="Camera Type", default=None)
    subparser.set_defaults(func=GetCameraList)

    subparser = camera_subparsers.add_parser("GetNextCameraList")
    subparser.add_argument("--type", type=str, choices=["target", "predict"], help="Camera Type", default=None)
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextCameraList)

    # Crosshair
    crosshair_parser = subparsers.add_parser("crosshair", help="Crosshair Service")
    crosshair_subparsers = crosshair_parser.add_subparsers(help="Crosshair Command", dest="cmd", required=True)

    subparser = crosshair_subparsers.add_parser("GetNextAutoCrosshairState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextAutoCrossHairCalState)

    subparser = crosshair_subparsers.add_parser("StartAutoCalibrateCrosshair")
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.set_defaults(func=StartAutoCalibrateCrosshair)

    subparser = crosshair_subparsers.add_parser("StartAutoCalibrateAllCrosshairs")
    subparser.set_defaults(func=StartAutoCalibrateAllCrosshairs)

    subparser = crosshair_subparsers.add_parser("StopAutoCalibrate")
    subparser.set_defaults(func=StopAutoCalibrate)

    subparser = crosshair_subparsers.add_parser("GetNextCrosshairState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.set_defaults(func=GetNextCrosshairState)

    subparser = crosshair_subparsers.add_parser("GetNextCrosshairStateSub")
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.set_defaults(func=GetNextCrosshairStateSub)

    subparser = crosshair_subparsers.add_parser("SetCrosshairPosition")
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.add_argument("--x", type=int, help="X Coordinate", required=True)
    subparser.add_argument("--y", type=int, help="Y Coordinate", required=True)
    subparser.set_defaults(func=SetCrosshairPosition)

    subparser = crosshair_subparsers.add_parser("MoveScanner")
    subparser.add_argument("--cam", type=str, help="Camera ID", required=True)
    subparser.add_argument("--x", type=float, help="X Coordinate (Normalized)", required=True)
    subparser.add_argument("--y", type=float, help="Y Coordinate (Normalized)", required=True)
    subparser.set_defaults(func=MoveScanner)

    # Data Capture
    data_capture_parser = subparsers.add_parser("data_capture", help="Data Capture Service")
    data_capture_subparsers = data_capture_parser.add_subparsers(help="Data Capture Command", dest="cmd", required=True)

    subparser = data_capture_subparsers.add_parser("StartDataCapture")
    subparser.add_argument("--rate", type=float, help="Data Capture Rate", required=True)
    subparser.add_argument("--session", type=str, help="Data Capture Session Name", required=True)
    subparser.add_argument("--crop", type=str, help="Data Capture Crop", required=False)
    subparser.add_argument("--crop_id", type=str, help="Data Capture Crop ID", required=False)
    subparser.add_argument("--snap-capture", action="store_true")
    subparser.set_defaults(func=StartDataCapture)

    subparser = data_capture_subparsers.add_parser("StopDataCapture")
    subparser.set_defaults(func=StopDataCapture)

    subparser = data_capture_subparsers.add_parser("PauseDataCapture")
    subparser.set_defaults(func=PauseDataCapture)

    subparser = data_capture_subparsers.add_parser("ResumeDataCapture")
    subparser.set_defaults(func=ResumeDataCapture)

    subparser = data_capture_subparsers.add_parser("CompleteDataCapture")
    subparser.set_defaults(func=CompleteDataCapture)

    subparser = data_capture_subparsers.add_parser("StartDataCaptureWirelessUpload")
    subparser.set_defaults(func=StartDataCaptureWirelessUpload)

    subparser = data_capture_subparsers.add_parser("StartDataCaptureUSBUpload")
    subparser.set_defaults(func=StartDataCaptureUSBUpload)

    subparser = data_capture_subparsers.add_parser("StopDataCaptureUpload")
    subparser.set_defaults(func=StopDataCaptureUpload)

    subparser = data_capture_subparsers.add_parser("PauseDataCaptureUpload")
    subparser.set_defaults(func=PauseDataCaptureUpload)

    subparser = data_capture_subparsers.add_parser("ResumeDataCaptureUpload")
    subparser.set_defaults(func=ResumeDataCaptureUpload)

    subparser = data_capture_subparsers.add_parser("StartBackgroundDataCaptureWirelessUpload")
    subparser.add_argument("--name", type=str, help="Session Name", required=True)
    subparser.set_defaults(func=StartBackgroundDataCaptureWirelessUpload)

    subparser = data_capture_subparsers.add_parser("StartBackgroundDataCaptureUSBUpload")
    subparser.add_argument("--name", type=str, help="Session Name", required=True)
    subparser.set_defaults(func=StartBackgroundDataCaptureUSBUpload)

    subparser = data_capture_subparsers.add_parser("StopBackgroundDataCaptureUpload")
    subparser.add_argument("--name", type=str, help="Session Name", required=True)
    subparser.set_defaults(func=StopBackgroundDataCaptureUpload)

    subparser = data_capture_subparsers.add_parser("PauseBackgroundDataCaptureUpload")
    subparser.add_argument("--name", type=str, help="Session Name", required=True)
    subparser.set_defaults(func=PauseBackgroundDataCaptureUpload)

    subparser = data_capture_subparsers.add_parser("ResumeBackgroundDataCaptureUpload")
    subparser.add_argument("--name", type=str, help="Session Name", required=True)
    subparser.set_defaults(func=ResumeBackgroundDataCaptureUpload)

    subparser = data_capture_subparsers.add_parser("SnapImages")
    subparser.add_argument("--crop", type=str, help="Data Capture Crop", required=False)
    subparser.add_argument("--crop-id", type=str, help="Data Capture CropID", required=False)
    subparser.add_argument("--session-name", type=str, help="Session Name", required=False, default=None)
    subparser.set_defaults(func=SnapImages)

    subparser = data_capture_subparsers.add_parser("SnapImage")
    subparser.add_argument("--crop", type=str, help="Data Capture Crop", required=False)
    subparser.add_argument("--crop-id", type=str, help="Data Capture CropID", required=False)
    subparser.add_argument("--cam-id", type=str, help="Camera ID", required=True)
    subparser.add_argument("--timestamp-ms", type=int, help="Timestamp", required=False, default=None)
    subparser.add_argument("--session-name", type=str, help="Session Name", required=False, default=None)
    subparser.set_defaults(func=SnapImage)

    subparser = data_capture_subparsers.add_parser("GetSessions")
    subparser.set_defaults(func=GetSessions)

    subparser = data_capture_subparsers.add_parser("GetRegularCaptureStatus")
    subparser.set_defaults(func=GetRegularCaptureStatus)

    subparser = data_capture_subparsers.add_parser("GetNextDataCaptureState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextDataCaptureState)

    subparser = data_capture_subparsers.add_parser("GetNextDataCaptureStateSub")
    subparser.set_defaults(func=GetNextDataCaptureStateSub)

    # Alarms
    alarm_parser = subparsers.add_parser("alarm", help="Alarm Service")
    alarm_subparsers = alarm_parser.add_subparsers(help="Alarm Command", dest="cmd", required=True)

    subparser = alarm_subparsers.add_parser("GetNextAlarmList")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextAlarmList)

    subparser = alarm_subparsers.add_parser("GetNextAlarmListSub")
    subparser.set_defaults(func=GetNextAlarmListSub)

    subparser = alarm_subparsers.add_parser("GetNextNewAlarmList")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextNewAlarmList)

    subparser = alarm_subparsers.add_parser("GetNextNewAlarmListSub")
    subparser.set_defaults(func=GetNextNewAlarmListSub)

    subparser = alarm_subparsers.add_parser("GetNextAlarmCount")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextAlarmCount)

    subparser = alarm_subparsers.add_parser("GetNextAlarmCountSub")
    subparser.set_defaults(func=GetNextAlarmCountSub)

    subparser = alarm_subparsers.add_parser("AcknowledgeAlarm")
    subparser.add_argument("--id", type=str, help="Alarm Identifier", required=True)
    subparser.set_defaults(func=AcknowledgeAlarm)

    subparser = alarm_subparsers.add_parser("ResetAlarms")
    subparser.set_defaults(func=ResetAlarms)

    subparser = alarm_subparsers.add_parser("GetNextAlarmLogCount")
    subparser.add_argument("-v", action="store_true")
    subparser.set_defaults(func=GetNextAlarmLogCount)

    subparser = alarm_subparsers.add_parser("GetNextAlarmLog")
    subparser.add_argument("-f", type=int, help="From alarm index", default=0)
    subparser.add_argument("-t", type=int, help="To alarm index", default=20)
    subparser.add_argument("-v", action="store_true")
    subparser.set_defaults(func=GetNextAlarmLog)

    subparser = alarm_subparsers.add_parser("AutofixAlarm")
    subparser.add_argument("-a", type=str, help="Alarm Identifier", required=True)
    subparser.set_defaults(func=AutofixAlarm)

    subparser = alarm_subparsers.add_parser("GetAutofixStatus")
    subparser.set_defaults(func=GetAutofixStatus)

    # Models
    model_parser = subparsers.add_parser("model", help="Model Service")
    model_subparsers = model_parser.add_subparsers(help="Model Command", dest="cmd", required=True)

    subparser = model_subparsers.add_parser("PinModel")
    subparser.add_argument("--id", type=str, help="Model ID", required=True)
    subparser.add_argument("--crop_id", type=str, help="Crop ID", required=False)
    subparser.add_argument("--p2p", type=bool, help="Enable to pin a p2p model", required=False, default=False)
    subparser.set_defaults(func=PinModel)

    subparser = model_subparsers.add_parser("UpdateModel")
    subparser.set_defaults(func=UpdateModel)

    subparser = model_subparsers.add_parser("UnpinModel")
    subparser.add_argument("--crop_id", type=str, help="Crop ID", required=False)
    subparser.add_argument("--p2p", type=bool, help="Enable to unpin the p2p model", required=False, default=False)
    subparser.set_defaults(func=UnpinModel)

    subparser = model_subparsers.add_parser("GetNextModelState")
    subparser.add_argument("--crop", type=str, help="Crop Name", default=None)
    subparser.add_argument("--crop_id", type=str, help="Crop ID", default=None)
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextModelState)

    subparser = model_subparsers.add_parser("GetNextModelStateSub")
    subparser.add_argument("--crop", type=str, help="Crop Name", default=None)
    subparser.add_argument("--crop_id", type=str, help="Crop ID", default=None)
    subparser.set_defaults(func=GetNextModelStateSub)

    subparser = model_subparsers.add_parser("GetNextAllModelState")
    subparser.add_argument("--crop", type=str, help="Crop Name", default=None)
    subparser.add_argument("--crop_id", type=str, help="Crop ID", default=None)
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--unsynced-only", action="store_true", help="Only show unsynced models", default=False)
    subparser.set_defaults(func=GetNextAllModelState)

    subparser = model_subparsers.add_parser("GetNextAllModelStateSub")
    subparser.add_argument("--crop", type=str, help="Crop Name", default=None)
    subparser.add_argument("--crop_id", type=str, help="Crop ID", default=None)
    subparser.add_argument("--unsynced-only", action="store_true", help="Only show unsynced models", default=False)
    subparser.set_defaults(func=GetNextAllModelStateSub)

    subparser = model_subparsers.add_parser("GetCropModelOptions")
    subparser.set_defaults(func=GetCropModelOptions)

    subparser = model_subparsers.add_parser("ListEnabledCrops")
    subparser.add_argument("--lang", type=str, help="Language code for translation", default="")
    subparser.set_defaults(func=ListEnabledCrops)

    subparser = model_subparsers.add_parser("ListCaptureCrops")
    subparser.add_argument("--lang", type=str, help="Language code for translation", default="")
    subparser.set_defaults(func=ListCaptureCrops)

    subparser = model_subparsers.add_parser("GetNextEnabledCrops")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--lang", type=str, help="Language code for translation", default="")
    subparser.set_defaults(func=GetNextEnabledCrops)

    subparser = model_subparsers.add_parser("GetNextEnabledCropsSub")
    subparser.add_argument("--lang", type=str, help="Language code for translation", default="")
    subparser.set_defaults(func=GetNextEnabledCropsSub)

    subparser = model_subparsers.add_parser("GetNextCaptureCrops")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--lang", type=str, help="Language code for translation", default="")
    subparser.set_defaults(func=GetNextCaptureCrops)

    subparser = model_subparsers.add_parser("GetNextCaptureCropsSub")
    subparser.add_argument("--lang", type=str, help="Language code for translation", default="")
    subparser.set_defaults(func=GetNextCaptureCropsSub)

    subparser = model_subparsers.add_parser("SelectCrop")
    subparser.add_argument("--crop_id", type=str, help="Crop ID", default=None)
    subparser.set_defaults(func=SelectCrop)

    subparser = model_subparsers.add_parser("TriggerDownload")
    subparser.set_defaults(func=TriggerDownload)

    subparser = model_subparsers.add_parser("DownloadModel")
    subparser.add_argument("--model_id", type=str, help="Model ID", default=None)
    subparser.set_defaults(func=DownloadModel)

    subparser = model_subparsers.add_parser("SyncCropIDs")
    subparser.add_argument("--force_cache_refresh", type=bool, help="force cache refresh", default=False)
    subparser.set_defaults(func=SyncCropIDs)

    subparser = model_subparsers.add_parser("GetNextModelNicknames")
    subparser.add_argument("--model_ids", type=str, help="Comma separated list of Model IDs", default=None)
    subparser.add_argument("--ts", type=int, help="timestamp in millis", default=0)
    subparser.set_defaults(func=GetNextModelNicknames)

    subparser = model_subparsers.add_parser("GetModelNicknames")
    subparser.add_argument("--model_ids", type=str, help="Comma separated list of Model IDs", default=None)
    subparser.set_defaults(func=GetModelNicknames)

    subparser = model_subparsers.add_parser("SetModelNickname")
    subparser.add_argument("--model_id", type=str, help="Model ID", default=None)
    subparser.add_argument("--model_nickname", type=str, help="Model Nickname", default=None)
    subparser.set_defaults(func=SetModelNickname)

    subparser = model_subparsers.add_parser("GetNextModelHistory")
    subparser.add_argument("--start_timestamp", type=int, help="Starting unix millis timestamp", default=0)
    subparser.add_argument("--count", type=int, help="Count of items to return", default=0)
    subparser.add_argument("--reverse", type=bool, help="Order of query & results", default=False)
    subparser.add_argument("--match_filter", type=json.loads, help="Model event fields to match", default={})
    subparser.add_argument(
        "--event_type_matcher", type=json.loads, help="JSON map of event type: bool events to match", default={}
    )
    subparser.add_argument("--ts", type=int, help="timestamp in millis", default=0)
    subparser.set_defaults(func=GetNextModelHistory)

    subparser = model_subparsers.add_parser("GetModelHistory")
    subparser.add_argument("--start_timestamp", type=int, help="Starting unix millis timestamp", default=0)
    subparser.add_argument("--count", type=int, help="Count of items to return", default=0)
    subparser.add_argument("--reverse", type=bool, help="Order of query & results", default=False)
    subparser.add_argument("--match_filter", type=json.loads, help="Model event fields to match", default={})
    subparser.add_argument(
        "--event_type_matcher", type=json.loads, help="JSON map of event type: bool events to match", default={}
    )
    subparser.set_defaults(func=GetModelHistory)

    subparser = model_subparsers.add_parser("GetNextSelectedCropID")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextSelectedCropID)

    subparser = model_subparsers.add_parser("RefreshDefaultModelParameters")
    subparser.add_argument("--model_id", type=str, help="ModelID", required=True)
    subparser.add_argument("--crop_id", type=str, help="CropID", required=True)
    subparser.set_defaults(func=RefreshDefaultModelParameters)

    # Software
    software_parser = subparsers.add_parser("software", help="Software Service")
    software_subparser = software_parser.add_subparsers(help="Software Command", dest="cmd", required=True)

    subparser = software_subparser.add_parser("GetNextVersionState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--verbose", action="store_true", help="get individual host states", default=False)
    subparser.set_defaults(func=GetNextSoftwareState)

    subparser = software_subparser.add_parser("GetNextVersionStateSub")
    subparser.add_argument("--verbose", action="store_true", help="get individual host states", default=False)
    subparser.set_defaults(func=GetNextSoftwareStateSub)

    subparser = software_subparser.add_parser("Update")
    subparser.set_defaults(func=UpdateSoftware)

    subparser = software_subparser.add_parser("Revert")
    subparser.set_defaults(func=RevertSoftware)

    subparser = software_subparser.add_parser("FixVersionMismatch")
    subparser.set_defaults(func=FixVersionMismatch)

    subparser = software_subparser.add_parser("UpdateHost")
    subparser.add_argument("--id", type=int, help="Host ID", required=True)
    subparser.set_defaults(func=UpdateHostSoftware)

    # Banding
    banding_parser = subparsers.add_parser("banding", help="Banding Service")
    banding_subparsers = banding_parser.add_subparsers(help="Banding Command", dest="cmd", required=True)

    subparser = banding_subparsers.add_parser("LoadBandingDefs")
    subparser.add_argument(
        "-f", type=str, help=f"Filename under {os.getenv('MAKA_DATA_DIR')} folder with output JSON", default=None
    )
    subparser.set_defaults(func=LoadBandingDefs)

    subparser = banding_subparsers.add_parser("SaveBandingDef")
    subparser.add_argument(
        "-f", type=str, help=f"Filename under {os.getenv('MAKA_DATA_DIR')} folder with banding area JSON", default=None
    )
    subparser.add_argument("-a", action="store_true", help="Set def as active", default=False)
    subparser.set_defaults(func=SaveBandingDef)

    subparser = banding_subparsers.add_parser("SetActiveBandingDef")
    subparser.add_argument("-name", type=str, help="Banding definition name", default="")
    subparser.add_argument("-uuid", type=str, help="Banding definition UUID", default="")
    subparser.set_defaults(func=SetActiveBandingDef)

    subparser = banding_subparsers.add_parser("GetActiveBandingDef")
    subparser.set_defaults(func=GetActiveBandingDef)

    subparser = banding_subparsers.add_parser("DeleteBandingDef")
    subparser.add_argument("-name", type=str, help="Banding definition name", default="")
    subparser.add_argument("-uuid", type=str, help="Banding definition UUID", default="")
    subparser.set_defaults(func=DeleteBandingDef)

    subparser = banding_subparsers.add_parser("GetVisualizationData")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("-r", type=int, help="Row id", default=1)
    subparser.set_defaults(func=GetVisualizationData)

    subparser = banding_subparsers.add_parser("GetVisualizationDataForAllRows")
    subparser.set_defaults(func=GetVisualizationDataForAllRows)

    subparser = banding_subparsers.add_parser("GetVisualizationMetadata")
    subparser.set_defaults(func=GetVisualizationMetadata)

    subparser = banding_subparsers.add_parser("GetDimensions")
    subparser.add_argument("-r", type=int, help="Row id", default=1)
    subparser.set_defaults(func=GetDimensions)

    subparser = banding_subparsers.add_parser("SetEnabled")
    subparser.add_argument("-e", type=int, help="1 for true, 0 for false", required=True)
    subparser.set_defaults(func=SetBandingEnabled)

    subparser = banding_subparsers.add_parser("IsEnabled")
    subparser.set_defaults(func=IsBandingEnabled)

    subparser = banding_subparsers.add_parser("SetDynamicBandingEnabled")
    subparser.add_argument("-e", type=int, help="1 for true, 0 for false", required=True)
    subparser.set_defaults(func=SetDynamicBandingEnabled)

    subparser = banding_subparsers.add_parser("IsDynamicBandingEnabled")
    subparser.set_defaults(func=IsDynamicBandingEnabled)

    subparser = banding_subparsers.add_parser("GetNextBandingState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextBandingState)

    subparser = banding_subparsers.add_parser("GetNextBandingStateSub")
    subparser.set_defaults(func=GetNextBandingStateSub)

    # Debug
    debug_parser = subparsers.add_parser("debug", help="Debug Service")
    debug_subparser = debug_parser.add_subparsers(help="Debug Command", dest="cmd", required=True)

    subparser = debug_subparser.add_parser("SetLogLevel")
    subparser.add_argument(
        "-l", type=str, help="Log level: TRACE, DEBUG, WARNING, INFO, ERROR, FATAL, PANIC", required=True
    )
    subparser.add_argument(
        "-c",
        type=str,
        help="component: commander, software_manager, hardware_manager, data_upload_manager, host_check, config, calibration, model_receiver, aimbot, cv",
        required=True,
    )
    subparser.add_argument("-r", type=int, help="row number on which the component resides", default=1)
    subparser.set_defaults(func=SetLogging)

    subparser = debug_subparser.add_parser(
        "StartSavingCLDReplay", help="Start saving Crop Line Detection replay files on row machines"
    )
    subparser.add_argument("-f", type=str, help="filename", required=True)
    subparser.add_argument(
        "-t", type=int, help="ttl in millisec, for how long should replay be saved", required=False, default=1000
    )
    subparser.set_defaults(func=StartSavingCropLineDetectionReplay)

    subparser = debug_subparser.add_parser(
        "StartRecordingAimbotInputs", help="Start Recording Aimbot Inputs on all row machines"
    )
    subparser.add_argument("--name", type=str, help="name", required=True)
    subparser.add_argument(
        "-t",
        type=int,
        help="ttl in milliseconds, for how long the recording should go on for",
        required=False,
        default=60000,
    )
    subparser.set_defaults(func=StartRecordingAimbotInputs)

    subparser = debug_subparser.add_parser(
        "StartRecordingRotaryTicksLaneHeights", help="Start Recording Rotary Ticks on all row machines"
    )
    subparser.add_argument("--name", type=str, help="name", required=True)
    subparser.add_argument(
        "-t",
        type=int,
        help="ttl in milliseconds, for how long the recording should go on for",
        required=False,
        default=60000,
    )
    subparser.set_defaults(func=StartRecordingRotaryTicksLaneHeights)

    subparser = debug_subparser.add_parser("SaveAlmanac", help="")
    subparser.add_argument("-i", type=str, help="id", required=True)
    subparser.set_defaults(func=SaveAlmanac)

    subparser = debug_subparser.add_parser("DeleteAlmanac", help="")
    subparser.add_argument("-i", type=str, help="id", required=True)
    subparser.set_defaults(func=DeleteAlmanac)

    subparser = debug_subparser.add_parser("LoadAlmanac", help="")
    subparser.add_argument("-i", type=str, help="id", required=True)
    subparser.set_defaults(func=LoadAlmanac)

    subparser = debug_subparser.add_parser("GetConfigData", help="")
    subparser.set_defaults(func=GetConfigData)

    subparser = debug_subparser.add_parser("GetNextConfigData", help="")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--lang", type=str, help="Language code for translation", default="")
    subparser.set_defaults(func=GetNextConfigData)

    subparser = debug_subparser.add_parser("GetNextConfigDataSub", help="")
    subparser.add_argument("--lang", type=str, help="Language code for translation", default="")
    subparser.set_defaults(func=GetNextConfigDataSub)

    subparser = debug_subparser.add_parser("GetNextAlmanacConfig", help="")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextAlmanacConfig)

    subparser = debug_subparser.add_parser("SaveDiscriminator", help="")
    subparser.add_argument("-i", type=str, help="id", required=True)
    subparser.set_defaults(func=SaveDiscriminator)

    subparser = debug_subparser.add_parser("DeleteDiscriminator", help="")
    subparser.add_argument("-i", type=str, help="id", required=True)
    subparser.set_defaults(func=DeleteDiscriminator)

    subparser = debug_subparser.add_parser("LoadDiscriminator", help="")
    subparser.add_argument("-i", type=str, help="id", required=True)
    subparser.set_defaults(func=LoadDiscriminator)

    subparser = debug_subparser.add_parser("SaveModelinator", help="")
    subparser.add_argument("-m", type=str, help="model id", required=True)
    subparser.add_argument("-c", type=str, help="crop id", default="")
    subparser.set_defaults(func=SaveModelinator)

    subparser = debug_subparser.add_parser("LoadModelinator", help="")
    subparser.set_defaults(func=LoadModelinator)

    subparser = debug_subparser.add_parser("AddSpatial", help="")
    subparser.set_defaults(func=AddMockSpatialMetricsBlock)

    subparser = debug_subparser.add_parser("ListSpatialBlocks", help="Pretty-print spatial blocks queue from Redis")
    subparser.set_defaults(func=ListSpatialBlocks)

    subparser = debug_subparser.add_parser("DeleteProfileSyncData", help="")
    subparser.set_defaults(func=DeleteProfileSyncData)

    # embeddings
    embeddings_parser = subparsers.add_parser("embeddings", help="Embeddings Service")
    embeddings_subparsers = embeddings_parser.add_subparsers(help="Embeddings Command", dest="cmd", required=True)

    subparser = embeddings_subparsers.add_parser("GetNextActiveCategoryCollectionId", help="")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextActiveCategoryCollectionId)

    subparser = embeddings_subparsers.add_parser("GetNextCategoryCollectionsData", help="")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextCategoryCollectionsData)

    subparser = embeddings_subparsers.add_parser("GetNextCategoryData", help="")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextCategoryData)

    subparser = embeddings_subparsers.add_parser("DisplayActiveCollectionData", help="")
    subparser.set_defaults(func=DisplayActiveCollectionData)

    subparser = embeddings_subparsers.add_parser("ReloadCategoryCollection", help="")
    subparser.set_defaults(func=ReloadCategoryCollection)

    subparser = embeddings_subparsers.add_parser("GetChipMetadata", help="")
    subparser.set_defaults(func=GetChipMetadata)

    subparser = embeddings_subparsers.add_parser("GetDownloadedChipIds", help="")
    subparser.set_defaults(func=GetDownloadedChipIds)

    subparser = embeddings_subparsers.add_parser("GetSyncedChipIds", help="")
    subparser.set_defaults(func=GetSyncedChipIds)

    # weeding_diagnostics
    weeding_diagnostics_parser = subparsers.add_parser("diagnostics", help="Weeding Diagnostics Service")
    weeding_diagnostics_subparser = weeding_diagnostics_parser.add_subparsers(
        help="Weeding Diagnostics Command", dest="cmd", required=True
    )

    subparser = weeding_diagnostics_subparser.add_parser("RecordWeedingDiagnostics")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-t", type=int, help="Recording time in sec", required=True)
    subparser.add_argument("-ci", type=float, help="Crop images per sec", required=False, default=1.0)
    subparser.add_argument("-wi", type=float, help="Weed images per sec", required=False, default=1.0)
    subparser.set_defaults(func=RecordWeedingDiagnostics)

    subparser = weeding_diagnostics_subparser.add_parser("GetRecordingsList")
    subparser.set_defaults(func=GetDiagnosticsRecordingsList)

    subparser = weeding_diagnostics_subparser.add_parser("OpenRecording")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.set_defaults(func=OpenDiagnosticsRecording)

    subparser = weeding_diagnostics_subparser.add_parser("UploadRecording")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.set_defaults(func=UploadDiagnosticsRecording)

    subparser = weeding_diagnostics_subparser.add_parser("GetUploadState")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.set_defaults(func=GetUploadState)

    subparser = weeding_diagnostics_subparser.add_parser("GetSnapshot")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-s", type=int, help="Snapshot number", required=True)
    subparser.add_argument("-a", action="store_true", help="Print ALL trajectories", required=False, default=False)
    subparser.set_defaults(func=GetRecordedSnapshot)

    subparser = weeding_diagnostics_subparser.add_parser("GetTrajectoryData")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-t", type=int, help="Trajectory ID", required=True)
    subparser.set_defaults(func=GetRecordedTrajectoryData)

    subparser = weeding_diagnostics_subparser.add_parser("FindTrajectory")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-t", type=int, help="Trajectory ID", required=True)
    subparser.set_defaults(func=FindTrajectory)

    subparser = weeding_diagnostics_subparser.add_parser("GetTrajectoryPredictImage")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-t", type=int, help="Trajectory ID", required=True)
    subparser.add_argument("-d", type=str, help="Destination file name", required=True)
    subparser.set_defaults(func=GetRecordedTrajectoryPredictImage)

    subparser = weeding_diagnostics_subparser.add_parser("GetTrajectoryTargetImage")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-t", type=int, help="Trajectory ID", required=True)
    subparser.add_argument("-i", type=str, help="Source image name", required=True)
    subparser.add_argument("-d", type=str, help="Destination file name", required=True)
    subparser.set_defaults(func=GetRecordedTrajectoryTargetImage)

    subparser = weeding_diagnostics_subparser.add_parser("GetPredictImageData")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-i", type=str, help="Image name", required=True)
    subparser.set_defaults(func=GetPredictImageData)

    subparser = weeding_diagnostics_subparser.add_parser("SnapshotPredictImages")
    subparser.add_argument("--row_id", type=int, help="Row ID to snapshot", required=True)
    subparser.set_defaults(func=SnapshotPredictImages)

    subparser = weeding_diagnostics_subparser.add_parser("GetChipForPredictImage")
    subparser.add_argument("--row_id", type=int, help="Row ID the predict camera is on", required=True)
    subparser.add_argument("--pcam_id", type=str, help="Physical camera ID", required=True)
    subparser.add_argument("--timestamp_ms", type=int, help="Timestamp in milliseconds", required=True)
    subparser.add_argument("--center_x_px", type=int, help="Center X coordinate in pixels", required=True)
    subparser.add_argument("--center_y_px", type=int, help="Center Y coordinate in pixels", required=True)
    subparser.add_argument("--output_file", type=str, help="Output file path for chip image", required=True)
    subparser.set_defaults(func=GetChipForPredictImage)

    subparser = weeding_diagnostics_subparser.add_parser("GetPredictImage")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-i", type=str, help="Source image name", required=True)
    subparser.add_argument("-d", type=str, help="Destination image name", required=True)
    subparser.set_defaults(func=GetPredictImage)

    subparser = weeding_diagnostics_subparser.add_parser("GetCurrentTrajectories")
    subparser.add_argument("-r", type=int, help="Row Id", default=1)
    subparser.set_defaults(func=GetCurrentTrajectories)

    subparser = weeding_diagnostics_subparser.add_parser("DeleteRecording")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.set_defaults(func=DeleteDiagnosticsRecording)

    subparser = weeding_diagnostics_subparser.add_parser("GetDeepweedPredictionsCount")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-c", type=int, help="Camera number", required=True)
    subparser.set_defaults(func=GetDeepweedPredictionsCount)

    subparser = weeding_diagnostics_subparser.add_parser("GetDeepweedPredictions")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.add_argument("-c", type=int, help="Camera number", required=True)
    subparser.add_argument("-i", type=int, help="Prediction index", required=True)
    subparser.set_defaults(func=GetDeepweedPredictions)

    subparser = weeding_diagnostics_subparser.add_parser("GetRotaryTicks")
    subparser.add_argument("-n", type=str, help="Recording name", required=True)
    subparser.add_argument("-r", type=int, help="Row number", required=True)
    subparser.set_defaults(func=GetRotaryTicks)

    # calibration
    calibration_parser = subparsers.add_parser("calibration", help="Calibration")
    messages_subparser = calibration_parser.add_subparsers(help="Calibration Command", dest="cmd", required=True)

    subparser = messages_subparser.add_parser("StartColorCalibration")
    subparser.add_argument("--cam-id", type=str, help="Camera to auto whitebalance", required=True)
    subparser.set_defaults(func=StartColorCalibration)

    subparser = messages_subparser.add_parser("SaveColorCalibration")
    subparser.add_argument("--cam-id", type=str, help="Camera to save values for", required=True)
    subparser.add_argument("--red", type=float, required=True)
    subparser.add_argument("--green", type=float, required=True)
    subparser.add_argument("--blue", type=float, required=True)
    subparser.set_defaults(func=SaveColorCalibration)

    # messages
    messages_parser = subparsers.add_parser("messages", help="Messages")
    messages_subparser = messages_parser.add_subparsers(help="Messages Command", dest="cmd", required=True)

    subparser = messages_subparser.add_parser("SendMessage")
    subparser.add_argument("--message", type=str, help="Message content", required=True)
    subparser.set_defaults(func=SendMessage)

    subparser = messages_subparser.add_parser("GetNextMessages")
    subparser.add_argument("--ts", type=int, help="Timestamp to look from", required=True)
    subparser.set_defaults(func=GetNextMessages)

    # reporting
    reporting_parser = subparsers.add_parser("reporting", help="Reporting")
    reporting_subparser = reporting_parser.add_subparsers(help="Reporting Command", dest="cmd", required=True)

    subparser = reporting_subparser.add_parser("GetNextLocationHistory")
    subparser.add_argument("--ts", type=int, help="Timestamp to look from", required=True)
    subparser.set_defaults(func=GetNextLocationHistory)

    # features
    features_parser = subparsers.add_parser("features", help="Feature Flags")
    features_subparser = features_parser.add_subparsers(help="Features Command", dest="cmd", required=True)

    subparser = features_subparser.add_parser("GetNextFeatureFlags")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextFeatureFlags)

    subparser = features_subparser.add_parser("GetRobotConfiguration")
    subparser.set_defaults(func=GetRobotConfiguration)

    # Tasks
    task_parser = subparsers.add_parser("startup_task", help="Startup Task")
    task_subparser = task_parser.add_subparsers(help="Startup Task Command", dest="cmd", required=True)

    subparser = task_subparser.add_parser("GetNextTasks")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextTasks)

    # Jobs
    jobs_parser = subparsers.add_parser("jobs", help="Jobs")
    jobs_subparser = jobs_parser.add_subparsers(help="Jobs Command", dest="cmd", required=True)

    subparser = jobs_subparser.add_parser("ListJobs")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=ListJobs)

    subparser = jobs_subparser.add_parser("CreateJob")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.add_argument("-a", action="store_true", help="Make job active", default=False)
    subparser.add_argument("-c", type=float, help="Acreage", required=False, default=0)
    subparser.set_defaults(func=CreateJob)

    subparser = jobs_subparser.add_parser("UpdateJob")
    subparser.add_argument("-i", type=str, help="Job ID", required=True)
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.add_argument("-t", type=int, help="Timestamp", required=True)
    subparser.add_argument("-c", type=float, help="Acreage", required=False, default=0)
    subparser.set_defaults(func=UpdateJob)

    subparser = jobs_subparser.add_parser("GetJob")
    subparser.add_argument("-i", type=str, help="Job ID", required=True)
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetJob)

    subparser = jobs_subparser.add_parser("DeleteJob")
    subparser.add_argument("-i", type=str, help="Job ID", required=True)
    subparser.set_defaults(func=DeleteJob)

    subparser = jobs_subparser.add_parser("MarkJobCompleted")
    subparser.add_argument("-i", type=str, help="Job ID", required=True)
    subparser.set_defaults(func=MarkJobCompleted)

    subparser = jobs_subparser.add_parser("GetActiveJobId")
    subparser.set_defaults(func=GetActiveJobId)

    subparser = jobs_subparser.add_parser("StopActiveJob")
    subparser.set_defaults(func=StopActiveJob)

    subparser = jobs_subparser.add_parser("StartJob")
    subparser.add_argument("-i", type=str, help="Job ID", required=True)
    subparser.set_defaults(func=StartJob)

    subparser = jobs_subparser.add_parser("GetActiveJobMetrics")
    subparser.set_defaults(func=GetActiveJobMetrics)

    # Plant Captcha
    captcha_parser = subparsers.add_parser("captcha", help="Plant captcha")
    captcha_subparser = captcha_parser.add_subparsers(help="Plant Captcha Command", dest="cmd", required=True)

    subparser = captcha_subparser.add_parser("Start")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.add_argument("-m", type=str, help="Model ID", required=True)
    subparser.add_argument("-c", type=str, help="Crop ID", required=True)
    subparser.add_argument("-cn", type=str, help="Crop Name", required=True)
    subparser.set_defaults(func=StartPlantCaptcha)

    subparser = captcha_subparser.add_parser("GetStatus")
    subparser.set_defaults(func=GetPlantCaptchaStatus)

    subparser = captcha_subparser.add_parser("GetNextStatusSub")
    subparser.set_defaults(func=GetPlantCaptchaStatusSub)

    subparser = captcha_subparser.add_parser("GetList")
    subparser.set_defaults(func=GetPlantCaptchasList)

    subparser = captcha_subparser.add_parser("Delete")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.set_defaults(func=DeletePlantCaptcha)

    subparser = captcha_subparser.add_parser("Get")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.set_defaults(func=GetPlantCaptcha)

    subparser = captcha_subparser.add_parser("Cancel")
    subparser.set_defaults(func=CancelPlantCaptcha)

    subparser = captcha_subparser.add_parser("Upload")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.set_defaults(func=UploadPlantCaptcha)

    subparser = captcha_subparser.add_parser("GetUploadState")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.set_defaults(func=GetPlantCaptchaUploadState)

    subparser = captcha_subparser.add_parser("Submit")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.add_argument("-i", type=str, help="Item ID", required=True)
    subparser.add_argument("-r", type=str, help="Result: WEED, CROP or UNKNOWN", required=True)
    subparser.set_defaults(func=SubmitPlantCaptchaResult)

    subparser = captcha_subparser.add_parser("GetResult")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.add_argument("-i", type=str, help="Item ID", required=True)
    subparser.set_defaults(func=GetPlantCaptchaResult)

    subparser = captcha_subparser.add_parser("Calculate")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.set_defaults(func=CalculatePlantCaptcha)

    subparser = captcha_subparser.add_parser("GetOriginalModelinatorConfig")
    subparser.add_argument("-n", type=str, help="Name", required=True)
    subparser.set_defaults(func=GetOriginalModelinatorConfig)

    subparser = captcha_subparser.add_parser("GetRowsStatus")
    subparser.set_defaults(func=GetCaptchaRowStatus)

    subparser = captcha_subparser.add_parser("CancelCaptchaOnRow")
    subparser.add_argument("-r", type=int, help="Row ID", required=True)
    subparser.set_defaults(func=CancelPlantCaptchaOnRow)

    # Module Assignment
    module_assignment_parser = subparsers.add_parser("module_assignment", help="Module Assignment")
    module_assignment_subparser = module_assignment_parser.add_subparsers(
        help="Module Assignment Command", dest="cmd", required=True
    )

    subparser = module_assignment_subparser.add_parser("GetNextModulesList")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextModuleList)

    subparser = module_assignment_subparser.add_parser("GetNextModulesListSub")
    subparser.set_defaults(func=GetNextModulesListSub)

    subparser = module_assignment_subparser.add_parser("GetNextActiveModules")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextActiveModules)

    subparser = module_assignment_subparser.add_parser("GetNextActiveModulesSub")
    subparser.set_defaults(func=GetNextActiveModulesSub)

    subparser = module_assignment_subparser.add_parser("IdentifyModule")
    subparser.add_argument("--serial", type=str, help="Module Serial", required=True)
    subparser.set_defaults(func=IdentifyModule)

    subparser = module_assignment_subparser.add_parser("AssignModule")
    subparser.add_argument("--id", type=str, help="Module ID", required=True)
    subparser.add_argument("--serial", type=str, help="Module Serial", required=True)
    subparser.set_defaults(func=AssignModule)

    subparser = module_assignment_subparser.add_parser("ClearModuleAssignment")
    subparser.add_argument("--serial", type=str, help="Module Serial", required=True)
    subparser.set_defaults(func=ClearModuleAssignment)

    subparser = module_assignment_subparser.add_parser("SetModuleSerial")
    subparser.add_argument(
        "--placeholder", type=str, help="Module Serial Placeholder, starts with UNSET_", required=True
    )
    subparser.add_argument("--new-serial", type=str, help="New Module Serial", required=True)
    subparser.set_defaults(func=SetModuleSerial)

    subparser = module_assignment_subparser.add_parser("GetPresetsList")
    subparser.set_defaults(func=GetPresetsList)

    subparser = module_assignment_subparser.add_parser("GetCurrentRobotDefinition")
    subparser.set_defaults(func=GetCurrentRobotDefinition)

    subparser = module_assignment_subparser.add_parser("SetCurrentRobotDefinition")
    subparser.set_defaults(func=SetCurrentRobotDefinition)

    hardware_status_parser = subparsers.add_parser("hardware_status", help="Hardware Status")
    hardware_status_subparser = hardware_status_parser.add_subparsers(
        help="Hardware Status Command", dest="cmd", required=True
    )

    subparser = hardware_status_subparser.add_parser("GetNextReaperHardwareStatus")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--module-id", type=str, help="Module ID, leave empty for center enclosure", default=None)
    subparser.set_defaults(func=GetNextReaperHardwareStatus)

    subparser = hardware_status_subparser.add_parser("GetNextReaperAllHardwareStatus")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.set_defaults(func=GetNextReaperAllHardwareStatus)

    # Tractor
    tractor_if_parser = subparsers.add_parser("tractor_if", help="Tractor Interface")
    tractor_if_subparser = tractor_if_parser.add_subparsers(help="Tractor IF Command", dest="cmd", required=True)

    subparser = tractor_if_subparser.add_parser("GetNextTractorIfState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--watch", action="store_true", help="Continue to watch this state forever", default=False)
    subparser.set_defaults(func=GetNextTractorIfState)

    subparser = tractor_if_subparser.add_parser("GetNextTractorSafetyState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--watch", action="store_true", help="Continue to watch this state forever", default=False)
    subparser.set_defaults(func=GetNextTractorSafetyState)

    subparser = tractor_if_subparser.add_parser("SetEnforcementPolicy")
    subparser.add_argument(
        "--enforced", action="store_true", help="Is the safety state from tractor enforced", default=False
    )
    subparser.set_defaults(func=SetEnforcementPolicy)

    # CruiseControl
    cc_parser = subparsers.add_parser("cruise_control", help="Cruise Control")
    cc_subparser = cc_parser.add_subparsers(help="Cruise Control Command", dest="cmd", required=True)

    subparser = cc_subparser.add_parser("GetNextCruiseControlState")
    subparser.add_argument("--ts", type=int, help="Timestamp in ms", default=0)
    subparser.add_argument("--watch", action="store_true", help="Continue to watch this state forever", default=False)
    subparser.set_defaults(func=GetNextCruiseControlState)

    try:
        args = parser.parse_args()
    except SystemExit as e:
        print(f"Invalid use of the tool: {e}")
        return ExitStatus.failure

    commander_client = CommanderClient()
    await args.func(commander_client, args)
    return ExitStatus.success


if __name__ == "__main__":
    raise SystemExit(asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name()).result())
