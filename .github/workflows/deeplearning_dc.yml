name: deeplearning_dc

on:
  pull_request:
    branches:
      - master
    paths:
      - "lib/**/*"
      - "deeplearning/**/*"
      - "cv/**/*"
      - ".github/workflows/deeplearning_dc.yml"
      - "services/containers/common/Dockerfile"
      - "requirements.txt"
      - "golang/bot/**/*"
      - "veselka/**/*"
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  GITHUB_TOKEN: ${{ secrets.CR_PAT }}

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  deeplearning_dc:
    runs-on: [self-hosted, Linux, dc_gpu]
    timeout-minutes: 160
    permissions:
      actions: write
      contents: write
      packages: write
    env:
      MAKA_BOT_MODE: develop
      REPO_DIR: ${{github.workspace}}
      MAKA_ROBOT_DIR:  ${{github.workspace}}
      DEEPLEARNING_TAG: ci-deepweed-${{ github.run_id }}-${{ github.run_number }}
      MAKA_LOG_DIR: ${{github.workspace}}../logs
      MAKA_CONFIG_DIR: ${{github.workspace}}../config
      GIT_BRANCH: ${{github.head_ref}}
      CARBON_HOST_BASE_DIR: ${{github.workspace}}/..
      AUTH0_TOKEN_CACHE_FILE: /data/ml_auth_token_deeplearning.json
      AUTH0_DOMAIN: ${{ secrets.AUTH0_DOMAIN }}
      AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
      AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
      S3_CACHE_PROXY_SERVICE_HOST: frankenmini01.dc.carbonrobotics.com
      TARGET_LATEST_TAG: latest
      NUM_GPUS: 2
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Load .env file
        uses: xom9ikk/dotenv@v2
        with:
          path: ${{github.workspace}}/../../..
      - name: Set env
        run: |
          echo "MAKA_DATA_DIR=${{env.DATA_DIR}}" >> $GITHUB_ENV
          echo "CUDA_VISIBLE_DEVICES=${{ env.CUDA_VISIBLE_DEVICES }}" >> $GITHUB_ENV
          echo "DEVICES_STR=${{ env.DEVICES_STR }}" >> $GITHUB_ENV
      - name: Set permissions
        run: |
          chown -R $USER:$USER ${{ github.workspace }}
      - name: Cleanup previous runs
        run: |
          echo "Cleaning up previous run"
          rm -rf ${{ github.workspace }}
          mkdir ${{ github.workspace }}
          find ${{ github.workspace }} | xargs ls -al
          docker kill $(docker ps -q) || true
          docker rm $(docker ps -a -q) || true
      - name: Remove the sticky bit on /dev/shm
        run: chmod -t /dev/shm
      - name: Checkout branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
          submodules: recursive
          token: ${{ secrets.CR_PAT }}
      - name: Checkout LFS objects
        run: git lfs checkout
      - name: Set docker tag
        run: |
          if [ -n "${{ github.head_ref }}" ]; then # a PR
            BRANCH_TAG=`echo -n ${GIT_BRANCH} | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            echo "MAKA_DOCKER_TAG=$BRANCH_TAG-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
            if [ "${{github.base_ref}}" != "master" ]; then # pr -> release branch
              echo "TARGET_LATEST_TAG=${${{github.base_ref}}##*/}" >> $GITHUB_ENV  # (release/X.Y -> X.Y)
            fi
          else
            echo "MAKA_DOCKER_TAG=latest-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
          fi
      - name: Setup Bot Context
        run: |
          python3 ${{github.workspace}}/bot/initialize.py ci --repo ${{github.workspace}}
      - name: Pull Gobot Container
        uses: nick-fields/retry@v2
        with:
          max_attempts: 5
          retry_on: error
          timeout_seconds: 120
          retry_wait_seconds: 43
          command: docker pull ghcr.io/carbonrobotics/robot/gobot:latest
      - name: Build Gobot Container if it changed
        run: |
          docker buildx build -t ghcr.io/carbonrobotics/robot/gobot:latest --cache-from ghcr.io/carbonrobotics/robot/gobot:latest-amd64-cache --cache-from ghcr.io/carbonrobotics/robot/gobot:latest-arm64-cache -f services/containers/gobot/Dockerfile .
      - name: Bot Pull Latest Containers
        run: |
          ${{github.workspace}}/bot/bot pull common || true
          ${{github.workspace}}/bot/bot pull deeplearning || true
      - name: Build containers
        run: |
          if [ -n "${{ github.head_ref }}" ]; then
            # skip building unless docker files changed in pr
            ${{github.workspace}}/bot/bot build \
              --all \
              --push \
              --tag $DEEPLEARNING_TAG \
              --tag $BRANCH_TAG \
              --tag ${{github.workflow}}-$BRANCH_TAG \
              --build-arg GITHUB_TOKEN=${GITHUB_TOKEN} \
              --cache-from ${{env.TARGET_LATEST_TAG}} \
              -srt ${{env.TARGET_LATEST_TAG}} \
              --container-build-retries 3 \
              --ci-base-revision "origin/${{github.base_ref}}" \
              deeplearning
          else
            # on master merge, this should build and push latest because there is no head ref
            ${{github.workspace}}/bot/bot build \
              --all \
              --push \
              --tag $DEEPLEARNING_TAG \
              --build-arg GITHUB_TOKEN=${GITHUB_TOKEN} \
              --cache-from ${{env.TARGET_LATEST_TAG}} \
              --container-build-retries 3 \
              deeplearning
          fi
          echo "MAKA_DOCKER_DEEPLEARNING_TAG=ghcr.io/carbonrobotics/robot/deeplearning:$DEEPLEARNING_TAG" >> $GITHUB_ENV
          echo "MAKA_DOCKER_CONTAINER_NAME=$DEEPLEARNING_TAG" >> $GITHUB_ENV
      - name: Setup configs for deepweed
        run: |
          cat >models.yaml <<EOL
          models:
              PREDICT:
                  path: /robot/test/models/fut-20241216-814jypvwjo.trt
              P2P:
                  path: /robot/test/models/p2p.trt
          EOL
          cat >cv.yaml <<EOL
          cameras:
            predict1:
              model: sim-predict
              serial_number: 0
              ppi: 170
              settings:
                sim_fps: 4
                gpu_id: 0
                roi_width: 960
                roi_height: 1280
              transforms:
                transpose: true
              publisher:
                buffer_name: cv_predict1
                stage_on_cpu: true
              buffer:
                max_size: 50
              deepweed_infer:
                gpu_id: 0
                reduction_ratio: 1
              deepweed_publisher:
                buffer_name: cv_deepweed_predict1
              vendor: simulated
            target1:
              model: sim-target
              serial_number: 7
              ppi: 164
              settings:
                sim_fps: 4
                gpu_id: 0
                roi_width: 1280
                roi_height: 800
              transforms:
                transpose: true
              publisher:
                buffer_name: cv_target1
                stage_on_cpu: true
              p2p_publisher:
                buffer_name: cv_p2p_target1
              p2p_infer:
                gpu_id: 0
              vendor: simulated
            front_right:
              model: sim-drive
              ip_address: 1
              settings:
                gpu_id: 0
                sim_fps: 4
                roi_width: 1920
                roi_height: 1080
              transforms:
                transpose: true
              publisher:
                buffer_name: cv_front_right
              vendor: simulated
          EOL
      - name: Start cv runtime
        run: |
          USER=carbon DETACH=true \
          ./deeplearning/bin/run_docker.sh ./bin/cv_runtime -c /robot/cv.yaml -r /robot/models.yaml
          
          docker rename ${MAKA_DOCKER_CONTAINER_NAME} ${MAKA_DOCKER_CONTAINER_NAME}2
      - name: Set docker network env
        run: |
          echo "DOCKER_NETWORK=container:${{env.MAKA_DOCKER_CONTAINER_NAME}}2" >> $GITHUB_ENV
      - name: Run Deepweed prediction
        run: |
          USER=carbon  ./deeplearning/bin/run_docker.sh ./bin/cvrt predict --out-directory ./ --use-case deepweed --src-image /data/labels/ci/deepweed/target0.2020-05-13T23-41-02.376742Z.png --ppi 170
      - name: Clean deepweed_container
        if: ${{ always() }}
        run: |
          docker logs ${MAKA_DOCKER_CONTAINER_NAME} || true
          docker rm -f ${MAKA_DOCKER_CONTAINER_NAME}
      - name: Run GPU required tests
        run: |
            USER=carbon ./deeplearning/bin/run_docker.sh ctest -L GPU_REQUIRED --test-dir ./build/ --output-on-failure
      - name: Clean deepweed_container
        if: ${{ always() }}
        run: |
          docker logs ${MAKA_DOCKER_CONTAINER_NAME} || true
          docker logs ${MAKA_DOCKER_CONTAINER_NAME}2 || true
          docker rm -f ${MAKA_DOCKER_CONTAINER_NAME}
          docker rm -f ${MAKA_DOCKER_CONTAINER_NAME}2
      - name: Veselka CV Tests
        run: |
          docker run --gpus all \
            -e ${CUDA_VISIBLE_DEVICES} \
            -e S3_CACHE_PROXY_SERVICE_HOST=${S3_CACHE_PROXY_SERVICE_HOST} \
            -v ${MAKA_CONFIG_DIR}:/config \
            -v ${MAKA_DATA_DIR}:/data \
            -v ${MAKA_LOG_DIR}:/logs \
            -v ${MAKA_ROBOT_DIR}:/robot \
            --name ${MAKA_DOCKER_CONTAINER_NAME}_veselka_test \
            ghcr.io/carbonrobotics/robot/common:$MAKA_DOCKER_CONTAINER_NAME pytest veselka/cv/tests
      - name: Clean veselka cv server containers
        if: ${{ always() }}
        run: |
          docker logs ${MAKA_DOCKER_CONTAINER_NAME}_veselka_server || true
          docker rm -f ${MAKA_DOCKER_CONTAINER_NAME}_veselka_server || true
          docker rm -f ${MAKA_DOCKER_CONTAINER_NAME}_veselka_test || true
          docker rmi $(docker images | grep ${MAKA_DOCKER_CONTAINER_NAME} | tr -s ' ' | cut -d ' ' -f 3) || true
          docker rmi $(docker images | grep ${BRANCH_TAG} | tr -s ' ' | cut -d ' ' -f 3) || true
          docker rmi $(docker images | grep ${{github.workflow}}-${BRANCH_TAG} | tr -s ' ' | cut -d ' ' -f 3) || true
      - name: Clean image
        if: ${{ always() }}
        run: |
         docker rmi ci-deepweed-${{ github.run_id }}-${{ github.run_number }}/deeplearning || true
         docker rmi $(docker images | grep ci-deepweed-${{ github.run_id }}-${{ github.run_number }} | tr -s ' ' | cut -d ' ' -f 3) || true
         docker image prune -f || true
         docker system prune -f || true
         rm ~/.docker/config.json || true
