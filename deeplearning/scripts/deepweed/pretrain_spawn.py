import argparse
import datetime
import json
import logging
import os
from typing import Optional

import torch
from lightning.pytorch.loggers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from torch.distributed.elastic.multiprocessing.errors import record

from deeplearning.clustering.utils import enable_embedding_clusters_sampling
from deeplearning.constants import CARBON_DATA_DIR, DeepweedTrainingSubtype
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import (
    DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS,
    DATASET_SAMPLING_ALGORITHM_UNIFORM,
)
from deeplearning.deepweed.remote_veselka_dataset.dataset import VeselkaDataset
from deeplearning.dl_metrics.dl_metrics.points_db import EmbeddingType
from deeplearning.scripts.deepweed.spawn_utils import (
    EvaluateComparisonEmbeddingsArguments,
    OptimizeAndTestArguments,
    PumapArguments,
    TrainAndUnoptimizedTestArguments,
    cleanup_spawn_script,
    evaluate_comparison_embeddings,
    evaluate_embeddings_for_new_points,
    get_upload_model_fn,
    load_pumap_head,
    optimize_and_test,
    test_pumap,
    train_and_unoptimized_test,
    train_pumap,
)
from deeplearning.scripts.utils.distributed_launch_server import DistributedLaunchServer
from deeplearning.scripts.utils.training_info import TrainingInfo
from deeplearning.scripts.utils.utils import (
    CONTAINER_VERSION,
    EXCLUDE_CROP_IDS_PRETRAIN,
    EXCLUDE_ROBOTS,
    add_common_arguments,
    add_distributed_launch_arguments,
    add_embedding_training_arguments,
    autodetect_driptape_model,
    generate_model_id,
    get_crop_and_weed_names,
    get_dataset,
    get_dataset_split_image_count,
    get_dataset_v2,
    get_pipeline,
    get_project_name,
    get_segmentation_classes,
    pick_comparison_model_id,
    run_debuggable,
    setup_logging,
    update_dataset_version,
)
from deeplearning.utils.fire_utils import safe_split
from deeplearning.utils.trainer import Environment
from deeplearning.utils.wandb_logger import make_primary_wandb_logger, make_secondary_wandb_logger

assert CARBON_DATA_DIR is not None

setup_logging(level=logging.INFO)
LOG = logging.getLogger(__name__)


PRETRAIN_DATASET_PAGE_SIZE = 2000


@record
def main() -> None:  # noqa: C901
    try:
        logger: Optional[WandbLogger] = None
        veselka_dataset: Optional[VeselkaDataset] = None

        parser = argparse.ArgumentParser()

        parser.add_argument(
            "--balance-positive-classes", action="store_true", help="Balance positive classes in dataset generation"
        )
        parser.add_argument("--autodetect-driptape-model", action="store_true")
        parser.add_argument("--no-autodetect-driptape-model", dest="autodetect_driptape_model", action="store_false")
        parser.add_argument("--segmentation-model", type=str, default=None)
        parser.add_argument("--no-balance-positive-classes", action="store_false", dest="balance_positive_classes")
        parser.add_argument("--geohash-max-precision", type=int, default=4)
        parser.add_argument("--geohash-min-precision", type=int, default=4)
        parser.add_argument(
            "--use-date-groups",
            action="store_true",
            help="Will hierarchically sample dates via date groups, if grouping was used during dataset generation",
        )
        parser.add_argument(
            "--balance-geohash", type=int, default=4, help="Balance geohash at given precision in dataset generation"
        )
        parser.add_argument("--predict-plants-first", action="store_true", default=False)
        parser.add_argument("--core-data-level", type=str, default="unfiltered")
        parser.add_argument("--no-gradient-checkpoint", action="store_false", dest="gradient_checkpoint")
        parser.add_argument("--precision", type=str, default="32-true")

        parser.add_argument("--dataset-start-ms", type=int, default=None)
        parser.add_argument("--dataset-end-ms", type=int, default=None)

        parser.set_defaults(develop=True)
        parser.set_defaults(balance_positive_classes=True)
        parser.set_defaults(gradient_checkpoint=True)
        parser.set_defaults(autodetect_driptape_model=True)

        add_embedding_training_arguments(parser=parser)

        add_common_arguments(parser=parser)

        add_distributed_launch_arguments(parser=parser)

        args = parser.parse_args()

        launch_server = DistributedLaunchServer(
            rdzv_endpoint=args.rdzv_endpoint,
            nodes=args.nodes,
            nproc_per_node=args.nproc_per_node,
            env_var_list=["WANDB_DIR"],
        )
        launch_server_host, launch_server_port = args.server_address.split(":")
        launch_server.run(port=launch_server_port)

        assert not (args.production and args.preview), "Run cannot be both production and preview"

        environment = Environment.DEVELOPMENT
        if args.preview:
            environment = Environment.PREVIEW
        elif args.production:
            environment = Environment.PRODUCTION

        if args.fast_run:
            environment = Environment.DEVELOPMENT

        if (args.production or args.preview) and not CONTAINER_VERSION:
            assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
        elif CONTAINER_VERSION and not args.segmentation_model and args.autodetect_driptape_model:
            args.segmentation_model = autodetect_driptape_model(min_test_oec=0.85, environment=environment)

        if args.segmentation_model is not None:
            segmentation_training_info = TrainingInfo(args.segmentation_model)
            pretrained_segmentation_model = segmentation_training_info.best_model_weights
            test_segmentation_classes = get_segmentation_classes(segmentation_training_info)
        else:
            pretrained_segmentation_model = None
            test_segmentation_classes = None

        tags = ["pretraining"]

        if args.tags is not None:
            tags += args.tags

        if args.job_id is None:
            model_id = generate_model_id()
        else:
            model_id = args.job_id

        comparison_model_id = pick_comparison_model_id(
            comparison_model_id=args.comparison_model_id, parent_comparison_model_id=None
        )
        assert comparison_model_id is not None
        LOG.info(f"Running with comparison model {comparison_model_id}")

        dl_config_dict = {
            "wandb_project": get_project_name(args.fast_run),
            "core_data_level": args.core_data_level,
            "gradient_checkpoint": args.gradient_checkpoint,
            "precision": args.precision,
            "lr_milestones": [10, 20, 30, 40, 50],
            "num_samples": 128 if args.fast_run else 60000,
            "num_epochs": 1 if args.fast_run else 60,
            "fast_run": args.fast_run,
            "comparison_model_id": comparison_model_id,
            "sampling_algorithm": DATASET_SAMPLING_ALGORITHM_UNIFORM,
            "sampling_geohash_precision": 6,
            "evaluate_new_comparison_data": args.evaluate_new_comparison_data,
            "train_set_size_limit": 1000 if args.fast_run else None,
            "validation_set_size_limit": 1000 if args.fast_run else 10000,
            "test_set_size_limit": 1000 if args.fast_run else 100000,
            "model_id": model_id,
            "enable_crop_embeddings": True,
            "train_all_points_every_step": True,
            "check_val_every_n_epoch": 2,
            **args.dl_config,
        }

        if "dataset_version" not in dl_config_dict:
            dl_config_dict["dataset_version"] = 1

        if not args.pipeline_id:
            args.pipeline_id = "ff928c0a-e1d1-4400-ac80-07be02e66cab"
        pipeline = get_pipeline(args.pipeline_id)

        dataset_version = dl_config_dict["dataset_version"]

        LOG.info(f"Model ID = {model_id}")

        LOG.info(f"{json.dumps(vars(args), indent=4)}")

        LOG.info("Starting dataset creation and download")

        dataset_version = update_dataset_version(dataset_id=args.dataset_id, dataset_version=dataset_version)

        dl_config_dict["dataset_version"] = dataset_version
        dl_config = DeepweedConfig.from_dict(dl_config_dict)

        if dataset_version == 1:
            dataset_id, _ = get_dataset(
                dataset_id=args.dataset_id,
                fast_run=args.fast_run,
                balance_positive_classes=args.balance_positive_classes,
                balance_geohash=args.balance_geohash,
                exclude_robots=EXCLUDE_ROBOTS,
                exclude_crop_ids=EXCLUDE_CROP_IDS_PRETRAIN,
                core_data_level=dl_config.core_data_level,
                validation_set_size_limit=dl_config.validation_set_size_limit,
                test_set_size_limit=dl_config.test_set_size_limit,
                train_set_size_limit=dl_config.train_set_size_limit,
                start_timestamp_ms=args.dataset_start_ms,
                end_timestamp_ms=args.dataset_end_ms,
                crop_ids=pipeline.data_source_crop_ids,
                val_test_crop_ids=pipeline.data_source_crop_ids,
            )
        elif dataset_version == 2:
            dataset_id, _ = get_dataset_v2(
                dataset_id=args.dataset_id,
                pipeline_id=args.pipeline_id,
                exclude_robots=EXCLUDE_ROBOTS,
                exclude_crop_ids=EXCLUDE_CROP_IDS_PRETRAIN,
                validation_set_size_limit=dl_config.validation_set_size_limit,
                test_set_size_limit=dl_config.test_set_size_limit,
                train_set_size_limit=dl_config.train_set_size_limit,
                start_timestamp_ms=args.dataset_start_ms,
                end_timestamp_ms=args.dataset_end_ms,
                page_size=PRETRAIN_DATASET_PAGE_SIZE,
            )
        LOG.info(f"dataset downloaded. dataset_id={dataset_id}")

        num_training_images = get_dataset_split_image_count(dataset_id, "train")

        assert dataset_id is not None
        assert num_training_images is not None

        if args.description is None:
            description = f"(pretrain) Development run {datetime.datetime.now()}"
        else:
            description = args.description

        if dl_config.train_embeddings and dl_config.evaluate_new_comparison_data:
            launch_server.run_command(
                evaluate_comparison_embeddings,
                get_upload_model_fn(model_id),
                EvaluateComparisonEmbeddingsArguments(
                    comparison_model_id=comparison_model_id,
                    environment=environment,
                    dataset_id=dataset_id,
                    fast_run=args.fast_run,
                    dataset_version=dataset_version,
                    eval_files=["train.jsonl"] if dataset_version == 2 else ["train.json"],
                ),
            )

        crops, weeds = get_crop_and_weed_names(dataset_id, dataset_version)

        if dl_config.evaluate_embeddings_for_new_points:
            evaluate_embeddings_for_new_points(
                dl_config=dl_config,
                model_id=model_id,
                dataset_id=dataset_id,
                nproc_per_node=args.nproc_per_node,
                crops=crops,
                weeds=weeds,
            )

        dataset_parameters = {
            "config": dl_config,
            "train_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json",
            "validation_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/validation.json",
            "test_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json",
            "num_samples": (
                int(num_training_images * dl_config.training_set_ratio)
                if dl_config.training_set_ratio is not None
                else dl_config.num_samples
            ),
            "crop_classes": tuple(crops),
            "weed_classes": tuple(weeds),
            "train_ppi": 200,
            "geohash_max_precision": args.geohash_max_precision,
            "geohash_min_precision": args.geohash_min_precision,
            "use_date_groups": args.use_date_groups,
            "calibration_dataset_size": 16 if args.fast_run else 1024,
            "embedding_balancing_evaluation_path": (
                f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dl_config.embedding_balancing_model}/image_point_embeddings"
                if dl_config.embedding_balancing_model is not None
                else None
            ),
            "embedding_type": (
                EmbeddingType.FULL if dl_config.evaluate_full_embeddings else EmbeddingType.REDUCED_SCALED
            ),
            "host": launch_server_host,
            "test_segm_classes": tuple(test_segmentation_classes) if test_segmentation_classes is not None else None,
        }

        if dataset_version == 2:
            dataset_parameters["train_filepath"] += "l"  # jsonl
            dataset_parameters["validation_filepath"] += "l"
            dataset_parameters["test_filepath"] += "l"

        if dl_config.sampling_algorithm == DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS:
            enable_embedding_clusters_sampling(
                dl_config=dl_config, dataset_id=dataset_id, dataset_parameters=dataset_parameters
            )

        dw_experiment_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/{model_id}")
        logger = make_primary_wandb_logger(
            name=description, tags=safe_split(tags), project=dl_config.wandb_project, exp_dir=dw_experiment_dir,
        )
        assert logger is not None

        veselka_dataset = VeselkaDataset(**dataset_parameters)
        assert veselka_dataset is not None

        launch_server.run_command(
            train_and_unoptimized_test,
            get_upload_model_fn(model_id),
            TrainAndUnoptimizedTestArguments(
                dl_config=dl_config,
                datasets=veselka_dataset.datasets,
                description=description,
                tags=tags,
                model_id=model_id,
                resume_from=args.resume_from,
                environment=environment,
                dataset_id=dataset_id,
                pipeline_id=args.pipeline_id,
                wandb_config={},
                logger=make_secondary_wandb_logger(logger),
                sub_type=DeepweedTrainingSubtype.PRETRAIN.name.lower(),
                data_source_crop_ids=pipeline.data_source_crop_ids,
                pretrained_segmentation_model=pretrained_segmentation_model,
            ),
        )

        pumap_experiment_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/pumap-{model_id}")
        if dl_config.train_embeddings:
            launch_server.run_command(
                train_pumap,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_experiment_dir=dw_experiment_dir,
                    pumap_experiment_dir=pumap_experiment_dir,
                    dw_config=dl_config,
                    logger=make_secondary_wandb_logger(logger),
                ),
            )
            launch_server.run_command(
                test_pumap,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_experiment_dir=dw_experiment_dir, pumap_experiment_dir=pumap_experiment_dir, dw_config=dl_config,
                ),
            )
            launch_server.run_command(
                load_pumap_head,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_config=dl_config, dw_experiment_dir=dw_experiment_dir, pumap_experiment_dir=pumap_experiment_dir,
                ),
            )

        launch_server.run_command(
            optimize_and_test,
            get_upload_model_fn(model_id),
            OptimizeAndTestArguments(
                dl_config=dl_config,
                datasets=veselka_dataset.datasets,
                description=description,
                tags=tags,
                model_id=model_id,
                wandb_config={},
                dataset_id=dataset_id,
                logger=make_secondary_wandb_logger(logger),
                pretrained_segmentation_model=pretrained_segmentation_model,
            ),
        )

        assert not torch.cuda.is_initialized()
    except Exception as e:
        raise e
    finally:
        cleanup_spawn_script(logger, veselka_dataset)


if __name__ == "__main__":
    run_debuggable(main)
