import logging
import os
from typing import Any, List, Type

import torch
import wandb
from torch.distributed.launcher.api import elastic_launch
from torch.nn.parallel import DistributedDataParallel

from . import utilities
from .config import TrainingConfig
from .models.base import BaseModel
from .samplers import BaseSampler

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


def train(
    model: BaseModel,
    training_dataset: torch.utils.data.Dataset[Any],
    validation_dataset: torch.utils.data.Dataset[Any],
    sampler: Type[BaseSampler] = BaseSampler,
    *args: Any,
    **kwargs: Any,
) -> None:
    config = TrainingConfig(*args, **kwargs)

    LOG.info("Starting training...")
    LOG.info(f"Config: \n{config}")

    launch_config = utilities.get_elastic_launcher_config(num_gpus=config.num_gpus)
    launcher = elastic_launch(launch_config, train_subprocess)
    launcher(model, training_dataset, validation_dataset, sampler, config)


def train_subprocess(
    model: BaseModel,
    training_dataset: torch.utils.data.Dataset[Any],
    validation_dataset: torch.utils.data.Dataset[Any],
    sampler: Type[BaseSampler],
    config: TrainingConfig,
) -> None:
    LOG = logging.getLogger(__name__)
    LOG.setLevel(logging.INFO)

    torch.distributed.init_process_group(backend="nccl")
    rank = int(os.environ["LOCAL_RANK"])

    if rank == 0:
        os.makedirs(config.data_dir, exist_ok=True)

        description = config.description if config.description is not None else config.model_id

        run = wandb.init(
            name=description,
            project=config.wandb_project,
            id=config.model_id,
            dir=config.data_dir,
            config=config.model_dump(),
        )

    model.to(rank)
    distributed_model = DistributedDataParallel(model, device_ids=[rank], find_unused_parameters=True)
    optimizer = torch.optim.Adam(distributed_model.parameters(), lr=config.learning_rate)
    training_dataloader = torch.utils.data.DataLoader(
        training_dataset,
        sampler=sampler(training_dataset, num_samples=config.num_samples, seed=rank,),
        num_workers=config.num_workers,
        prefetch_factor=config.prefetch_factor,
        persistent_workers=True,
        batch_size=config.batch_size,
    )

    validation_dataloader = torch.utils.data.DataLoader(
        validation_dataset,
        sampler=sampler(validation_dataset, num_samples=config.num_validation_samples, seed=rank,),
        num_workers=config.num_workers,
        prefetch_factor=config.prefetch_factor,
        persistent_workers=True,
        batch_size=config.batch_size,
    )

    losses = []
    epoch_metrics: List[float] = []
    global_step = 0
    num_batches = len(training_dataloader)
    num_val_batches = len(validation_dataloader)

    for epoch in range(config.num_epochs):
        distributed_model.train()
        for i, batch in enumerate(training_dataloader):
            optimizer.zero_grad()

            loss = distributed_model(*batch)
            loss.backward()
            optimizer.step()

            torch.distributed.all_reduce(loss, op=torch.distributed.ReduceOp.AVG)

            if rank == 0:
                losses.append(loss.item())
                run.log({"Training loss": loss.item()}, step=global_step)
                global_step += 1
                LOG.info(f"Epoch {epoch} Progress: {i}/{num_batches} Batch Loss: {loss.item():.4f}")

        if rank == 0:
            LOG.info(f"Epoch {epoch} average loss: {sum(losses) / len(losses)}")

        losses = []
        distributed_model.eval()
        for i, batch in enumerate(validation_dataloader):
            with torch.no_grad():
                loss = distributed_model(*batch)
                torch.distributed.all_reduce(loss, op=torch.distributed.ReduceOp.AVG)

            if rank == 0:
                losses.append(loss.item())
                LOG.info(f"Validation Epoch {epoch} Progress: {i}/{num_val_batches} Batch Loss: {loss.item():.4f}")

        if rank == 0:
            val_loss = sum(losses) / len(losses)
            run.log({"Validation loss": val_loss, "Epoch": epoch}, step=global_step)

            LOG.info(f"Validation Epoch {epoch} average loss: {val_loss}")
            utilities.save_model(
                config.data_dir, model, save_best=not len(epoch_metrics) or max(epoch_metrics) > val_loss
            )
            epoch_metrics.append(val_loss)

            global_step += 1
            losses = []

    torch.distributed.barrier()
    if rank == 0:
        run.finish()

    torch.distributed.destroy_process_group()
