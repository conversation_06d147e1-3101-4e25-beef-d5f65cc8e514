"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class DetectionClass(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    score: builtin___float = ...

    def __init__(self,
        *,
        score : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"class",b"class",u"score",b"score"]) -> None: ...
type___DetectionClass = DetectionClass

class DeepweedDetection(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    HitClassValue = typing___NewType('HitClassValue', builtin___int)
    type___HitClassValue = HitClassValue
    HitClass: _HitClass
    class _HitClass(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[DeepweedDetection.HitClassValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        WEED = typing___cast(DeepweedDetection.HitClassValue, 0)
        CROP = typing___cast(DeepweedDetection.HitClassValue, 1)
    WEED = typing___cast(DeepweedDetection.HitClassValue, 0)
    CROP = typing___cast(DeepweedDetection.HitClassValue, 1)

    x: builtin___float = ...
    y: builtin___float = ...
    size: builtin___float = ...
    score: builtin___float = ...
    hit_class: type___DeepweedDetection.HitClassValue = ...
    mask_intersections: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bytes] = ...
    trajectory_id: builtin___int = ...
    weed_score: builtin___float = ...
    crop_score: builtin___float = ...
    plant_score: builtin___float = ...
    embedding_category_distances: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    x_undistorted: builtin___float = ...
    y_undistorted: builtin___float = ...

    @property
    def detection_classes(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___DetectionClass]: ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        size : typing___Optional[builtin___float] = None,
        score : typing___Optional[builtin___float] = None,
        hit_class : typing___Optional[type___DeepweedDetection.HitClassValue] = None,
        detection_classes : typing___Optional[typing___Iterable[type___DetectionClass]] = None,
        mask_intersections : typing___Optional[typing___Iterable[builtin___bytes]] = None,
        trajectory_id : typing___Optional[builtin___int] = None,
        weed_score : typing___Optional[builtin___float] = None,
        crop_score : typing___Optional[builtin___float] = None,
        plant_score : typing___Optional[builtin___float] = None,
        embedding_category_distances : typing___Optional[typing___Iterable[builtin___float]] = None,
        x_undistorted : typing___Optional[builtin___float] = None,
        y_undistorted : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_score",b"crop_score",u"detection_classes",b"detection_classes",u"embedding_category_distances",b"embedding_category_distances",u"hit_class",b"hit_class",u"mask_intersections",b"mask_intersections",u"plant_score",b"plant_score",u"score",b"score",u"size",b"size",u"trajectory_id",b"trajectory_id",u"weed_score",b"weed_score",u"x",b"x",u"x_undistorted",b"x_undistorted",u"y",b"y",u"y_undistorted",b"y_undistorted"]) -> None: ...
type___DeepweedDetection = DeepweedDetection

class TrackedItemCoordinates(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x_mm: builtin___float = ...
    y_mm: builtin___float = ...
    z_mm: builtin___float = ...
    x_px: builtin___float = ...
    y_px: builtin___float = ...

    def __init__(self,
        *,
        x_mm : typing___Optional[builtin___float] = None,
        y_mm : typing___Optional[builtin___float] = None,
        z_mm : typing___Optional[builtin___float] = None,
        x_px : typing___Optional[builtin___float] = None,
        y_px : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x_mm",b"x_mm",u"x_px",b"x_px",u"y_mm",b"y_mm",u"y_px",b"y_px",u"z_mm",b"z_mm"]) -> None: ...
type___TrackedItemCoordinates = TrackedItemCoordinates

class EmbeddingCategory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    category: typing___Text = ...
    distance: builtin___float = ...

    def __init__(self,
        *,
        category : typing___Optional[typing___Text] = None,
        distance : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category",b"category",u"distance",b"distance"]) -> None: ...
type___EmbeddingCategory = EmbeddingCategory

class TrackedItem(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    trajectory_id: builtin___int = ...
    deduplicated: builtin___bool = ...
    in_camera: builtin___bool = ...

    @property
    def coordinates_before(self) -> type___TrackedItemCoordinates: ...

    @property
    def coordinates_after(self) -> type___TrackedItemCoordinates: ...

    def __init__(self,
        *,
        trajectory_id : typing___Optional[builtin___int] = None,
        deduplicated : typing___Optional[builtin___bool] = None,
        coordinates_before : typing___Optional[type___TrackedItemCoordinates] = None,
        coordinates_after : typing___Optional[type___TrackedItemCoordinates] = None,
        in_camera : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"coordinates_after",b"coordinates_after",u"coordinates_before",b"coordinates_before"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"coordinates_after",b"coordinates_after",u"coordinates_before",b"coordinates_before",u"deduplicated",b"deduplicated",u"in_camera",b"in_camera",u"trajectory_id",b"trajectory_id"]) -> None: ...
type___TrackedItem = TrackedItem

class CandidateShift(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x_shift: builtin___float = ...
    y_shift: builtin___float = ...
    centroid_id: builtin___int = ...
    trajectory_id: builtin___int = ...

    def __init__(self,
        *,
        x_shift : typing___Optional[builtin___float] = None,
        y_shift : typing___Optional[builtin___float] = None,
        centroid_id : typing___Optional[builtin___int] = None,
        trajectory_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"centroid_id",b"centroid_id",u"trajectory_id",b"trajectory_id",u"x_shift",b"x_shift",u"y_shift",b"y_shift"]) -> None: ...
type___CandidateShift = CandidateShift

class DeepweedPredictionFrame(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    embedding_categories: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    plant_matcher_valid: builtin___bool = ...

    @property
    def detections(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___DeepweedDetection]: ...

    @property
    def tracked_items(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TrackedItem]: ...

    @property
    def best_shift(self) -> type___CandidateShift: ...

    @property
    def candidate_shifts(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CandidateShift]: ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        detections : typing___Optional[typing___Iterable[type___DeepweedDetection]] = None,
        embedding_categories : typing___Optional[typing___Iterable[typing___Text]] = None,
        tracked_items : typing___Optional[typing___Iterable[type___TrackedItem]] = None,
        best_shift : typing___Optional[type___CandidateShift] = None,
        candidate_shifts : typing___Optional[typing___Iterable[type___CandidateShift]] = None,
        plant_matcher_valid : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"best_shift",b"best_shift"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"best_shift",b"best_shift",u"candidate_shifts",b"candidate_shifts",u"detections",b"detections",u"embedding_categories",b"embedding_categories",u"plant_matcher_valid",b"plant_matcher_valid",u"timestamp_ms",b"timestamp_ms",u"tracked_items",b"tracked_items"]) -> None: ...
type___DeepweedPredictionFrame = DeepweedPredictionFrame

class DeepweedPredictionRecord(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    record_timestamp_ms: builtin___int = ...

    @property
    def frame(self) -> type___DeepweedPredictionFrame: ...

    def __init__(self,
        *,
        record_timestamp_ms : typing___Optional[builtin___int] = None,
        frame : typing___Optional[type___DeepweedPredictionFrame] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"frame",b"frame"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"frame",b"frame",u"record_timestamp_ms",b"record_timestamp_ms"]) -> None: ...
type___DeepweedPredictionRecord = DeepweedPredictionRecord

class LaneHeightSnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weed_height: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    crop_height: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...

    def __init__(self,
        *,
        weed_height : typing___Optional[typing___Iterable[builtin___float]] = None,
        crop_height : typing___Optional[typing___Iterable[builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_height",b"crop_height",u"weed_height",b"weed_height"]) -> None: ...
type___LaneHeightSnapshot = LaneHeightSnapshot

class LaneHeightRecord(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    record_timestamp_ms: builtin___int = ...

    @property
    def snapshot(self) -> type___LaneHeightSnapshot: ...

    def __init__(self,
        *,
        record_timestamp_ms : typing___Optional[builtin___int] = None,
        snapshot : typing___Optional[type___LaneHeightSnapshot] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"snapshot",b"snapshot"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"record_timestamp_ms",b"record_timestamp_ms",u"snapshot",b"snapshot"]) -> None: ...
type___LaneHeightRecord = LaneHeightRecord

class RotaryTicksSnapshot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_us: builtin___int = ...
    fl: builtin___int = ...
    fr: builtin___int = ...
    bl: builtin___int = ...
    br: builtin___int = ...
    fl_enabled: builtin___bool = ...
    fr_enabled: builtin___bool = ...
    bl_enabled: builtin___bool = ...
    br_enabled: builtin___bool = ...

    def __init__(self,
        *,
        timestamp_us : typing___Optional[builtin___int] = None,
        fl : typing___Optional[builtin___int] = None,
        fr : typing___Optional[builtin___int] = None,
        bl : typing___Optional[builtin___int] = None,
        br : typing___Optional[builtin___int] = None,
        fl_enabled : typing___Optional[builtin___bool] = None,
        fr_enabled : typing___Optional[builtin___bool] = None,
        bl_enabled : typing___Optional[builtin___bool] = None,
        br_enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bl",b"bl",u"bl_enabled",b"bl_enabled",u"br",b"br",u"br_enabled",b"br_enabled",u"fl",b"fl",u"fl_enabled",b"fl_enabled",u"fr",b"fr",u"fr_enabled",b"fr_enabled",u"timestamp_us",b"timestamp_us"]) -> None: ...
type___RotaryTicksSnapshot = RotaryTicksSnapshot

class RotaryTicksRecord(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    record_timestamp_ms: builtin___int = ...

    @property
    def snapshot(self) -> type___RotaryTicksSnapshot: ...

    def __init__(self,
        *,
        record_timestamp_ms : typing___Optional[builtin___int] = None,
        snapshot : typing___Optional[type___RotaryTicksSnapshot] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"snapshot",b"snapshot"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"record_timestamp_ms",b"record_timestamp_ms",u"snapshot",b"snapshot"]) -> None: ...
type___RotaryTicksRecord = RotaryTicksRecord
